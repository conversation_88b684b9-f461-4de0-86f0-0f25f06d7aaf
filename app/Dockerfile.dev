FROM node:20.9.0
 
ARG NEXUS_NPM_TOKEN
ARG FONTAWESOME_TOKEN
 
# Set working directory to /app/src (where Dockerfile.dev is located)
WORKDIR /usr/src/app
 
# Create NPMRC
# Vapor + ReactSDK
RUN echo "@vapor:registry=https://nexus.ts-paas.com/repository/ts-one-front-npm-repo/" >> .npmrc
RUN echo "@1f:registry=https://nexus.ts-paas.com/repository/ts-one-front-npm-repo/" >> .npmrc
RUN echo "//nexus.ts-paas.com/repository/ts-one-front-npm-repo/:_authToken=${NEXUS_NPM_TOKEN}" >> .npmrc
# Fontawesome
RUN echo "@fortawesome:registry=https://npm.fontawesome.com/" >> .npmrc
RUN echo "//npm.fontawesome.com/:_authToken=${FONTAWESOME_TOKEN}" >> .npmrc
RUN cat .npmrc
 
# Copy package.json from the root directory
COPY ../package.json /usr/src/app
COPY ../vite.config.ts /usr/src/app
COPY ../tsconfig.json /usr/src/app
COPY ../index.html /usr/src/app

# NPM Install for building
RUN npm install
 
# Set basic entry point
WORKDIR /usr/src/app
ENTRYPOINT [ "npm", "run", "start" ]
 
# Expose the port
EXPOSE 3000