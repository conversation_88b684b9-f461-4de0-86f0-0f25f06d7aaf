import { IGridColumn } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames } from "../common";

export const getItemListGrid = (t: any, type: string): IGridColumn[] => {
    if (type == "voci") {
        const response = BaseGridList.fromJson({
            gridsSettings: {
                column_names: [t("Nome")],
                column_keys: ["descrizione"],
                column_widths: ["100%"],
                cell_templates: [null],
                sortable: [true],
                header_templates: "",
                column_totals: null,
            },
        });

        return mapOtherColumnNames(response);
    } else {
        const response = BaseGridList.fromJson({
            gridsSettings: {
                column_names: [t("Nome")],
                column_keys: ["descrizione"],
                column_widths: ["100%"],
                cell_templates: [null],
                sortable: [true],
                header_templates: "",
                column_totals: null,
            },
        });

        return mapOtherColumnNames(response);
    }
};
