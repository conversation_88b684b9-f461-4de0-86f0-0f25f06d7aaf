import { Typography } from "@vapor/react-extended";
import { TYPE_MAP } from "../../features/utility/Altro/Items/constants";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getItemsGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome"), t("Tipo")],
            column_keys: ["nome", "tipo"],
            column_widths: ["50%", "50%"],
            sortable: [true, true],
            cell_templates: [null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapLogPecNames(response, t);
};

const mapLogPecNames = (response: any, _t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names
        .map((cln: string, index: number) => {
            if (column_widths !== undefined && column_widths[index] !== "0%") {
                const returnColumn: any = {
                    field: column_keys[index],
                    headerName: cln,
                    sortable: sortable[index],
                    flex:
                        Math.round(
                            (parseInt(column_widths[index]) / 100) * 1e2
                        ) / 1e2,
                };
                if (column_keys[index] === "tipo") {
                    returnColumn.renderCell = (row: any) => {
                        // @ts-ignore
                        return <Typography>{TYPE_MAP[row.value]}</Typography>;
                    };
                }
                return returnColumn;
            }
            return undefined;
        })
        .filter((element: any) => element !== undefined);

    return columns as unknown as IGridColumn[];
};
