import { VaporBadge } from "@vapor/react-custom";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getEntriesGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Data"), t("Azione"), t("Utente"), t("Qualifica")],
            column_keys: ["creation_date", "operation", "nome", "qualifica"],
            column_widths: ["25%", "45%", "15%", "15%"],
            sortable: [true, true, true, true],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapWorkflowsColumns(response, t);
};

const mapWorkflowsColumns = (response: any, _t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names
        .map((cln: string, index: number) => {
            if (column_widths !== undefined && column_widths[index] !== "0%") {
                const returnColumn: any = {
                    field: column_keys[index],
                    headerName: cln,
                    sortable: sortable[index],
                    flex:
                        Math.round(
                            (parseInt(column_widths[index]) / 100) * 1e2
                        ) / 1e2,
                };
                if (column_keys[index] === "qualifica") {
                    returnColumn.renderCell = (row: any) => {
                        return (
                            <VaporBadge
                                color="success"
                                variant="outlined"
                                label={row.formattedValue}
                            />
                        );
                    };
                }
                return returnColumn;
            }
            return undefined;
        })
        .filter((element: any) => element !== undefined);

    return columns as unknown as IGridColumn[];
};
