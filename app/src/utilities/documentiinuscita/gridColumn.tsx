import { IGridColumn } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames } from "../common";

export const getDocumentiinuscitaGrid = async (
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Protocollo"),
                t("Titolo"),
                t("Data"),
                t("Oggetto"),
                t("Mittente"),
                t("Rif. Ente"),
                t("Categoria"),
                t("Rif. Pratica"),
            ],
            column_keys: [
                "numeroprotocollo",
                "titolodocumento",
                "data",
                "oggetto",
                "mittente",
                "ente",
                "categoria",
                "pratiche",
            ],
            column_widths: [
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
            ],
            cell_templates: [null, null],
            sortable: [true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};
