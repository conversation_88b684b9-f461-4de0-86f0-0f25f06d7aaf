import { RiepilogoCreateLetterActions } from "../../interfaces/fatturazione.interface";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import {
    actionButtonsRiepilogo,
    fileNameRiepilogo,
    livelloRiepilogo,
    statoRiepilogo,
} from "../helperComponents";

export const getRiepilogoGrid = async (
    t: any,
    actions: RiepilogoCreateLetterActions
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data"),
                t("Clienti"),
                t("File"),
                t("Livello"),
                t("Stato"),
                t(""),
            ],
            column_keys: [
                "creation_date",
                "customer",
                "filename",
                "level",
                "sent",
                "buttons",
            ],
            column_widths: ["15%", "15%", "15%", "15%", "20%", "20%"],
            cell_templates: [null, null, null, null, null, null],
            sortable: [false, false, false, false, false, false],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapRiepilogoColumnNames(response, actions);
};

export const mapRiepilogoColumnNames = (
    response: any,
    actions: RiepilogoCreateLetterActions
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "filename") {
                returnColumn.renderCell = (row: any) => fileNameRiepilogo(row);
            } else if (column_keys[index] === "level") {
                returnColumn.renderCell = (row: any) => livelloRiepilogo(row);
            } else if (column_keys[index] === "sent") {
                returnColumn.renderCell = (row: any) => statoRiepilogo(row);
            } else if (column_keys[index] === "buttons") {
                returnColumn.renderCell = (row: any) =>
                    actionButtonsRiepilogo(row, actions);
            } else {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
