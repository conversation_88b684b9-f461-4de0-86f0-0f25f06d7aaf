import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { defineUserInternalStatus } from "../helperComponents";

export const getInternalUsersGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome Completo"),
                t("Email"),
                t("Referenti"),
                t("Qualifica"),
                t("Ultimo accesso"),
                t("Stato"),
            ],
            column_keys: [
                "nomeutente",
                "email",
                "referente",
                "qualifica",
                "last_login",
                "stato",
            ],
            column_widths: ["23%", "20%", "20%", "12%", "15%", "10%"],
            sortable: [true, true, true, true, false, true],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapInternalUserColumn(response);
};

export const mapInternalUserColumn = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (data: any) => {
                let { row } = data;
                switch (column_keys[index]) {
                    case "stato":
                        return defineUserInternalStatus(data);
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
