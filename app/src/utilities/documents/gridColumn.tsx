import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faDownload,
    faCloud,
    faEye,
    faList,
} from "@fortawesome/pro-regular-svg-icons";

import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { useTranslation } from "@1f/react-sdk";

export const getDocumentGrid = async (
    t: any,
    action: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome pratica"),
                t("Clienti"),
                t("Controparti"),
                t("Documento di"),
                t("Descrizione"),
                t("Categoria"),
                t("Nome file"),
                t("Numero protocollo"),
                t("Mittente"),
                t("Immesso il"),
                t("Data documento"),
                t(""),
                t(""),
                t(""),
                t(""),
                t("Stato Firma"),
            ],
            column_keys: [
                "nome_pratica",
                "listaclienti",
                "listacontroparti",
                "documentodi",
                "titolodocumento",
                "category_id",
                "nomefile",
                "numeroprotocollo",
                "mittente",
                "data",
                "data_documento",
                "uniqueid",
                "visible",
                "is_online",
                "linkedToDeadline",
                "signature_status",
            ],
            column_widths: [
                "0%",
                "15%",
                "15%",
                "0%",
                "13%",
                "10%",
                "15%",
                "0%",
                "0%",
                "8%",
                "8%",
                "7%",
                "3%",
                "7%",
                "3%",
                "0%",
            ],
            sortable: [
                true,
                true,
                true,
                true,
                true,
                true,
                true,
                false,
                false,
                true,
                true,
                false,
                false,
                false,
                false,
                false,
            ],
        },
    });

    return mapDocumentsColumnNames(response, action);
};

export const getDownloadDocument = (row: any, action: any) => {
    return (
        <Stack direction="row" gap={1}>
            <Button
                onClick={(event: any) => {
                    event.preventDefault();
                    event.stopPropagation();
                    action.startDownload(row.uniqueid);
                }}
            >
                <FontAwesomeIcon icon={faDownload} />
                Download
            </Button>
        </Stack>
    );
};

export const getfilemanagerIsOnline = (data: any) => {
    let { row } = data;
    return (
        <>{row.is_online == "1" ? <FontAwesomeIcon icon={faCloud} /> : ""}</>
    );
};

export const getfilemanagerLinkedToDeadline = (data: any) => {
    let { row } = data;
    const { t } = useTranslation();
    return (
        <>
            {row !== undefined && parseInt(row.linkedToDeadline) > 0 ? (
                <Tooltip arrow title={t("Documento collegato a impegno/i")}>
                    <FontAwesomeIcon icon={faList} />
                </Tooltip>
            ) : (
                ""
            )}
        </>
    );
};

export const getfilemanagerVisible = (data: any) => {
    let { row } = data;
    return (
        <>
            {row !== undefined && row.visible === "1" ? (
                <FontAwesomeIcon icon={faEye} />
            ) : (
                ""
            )}
        </>
    );
};

export const mapDocumentsColumnNames = (response: any, action: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_keys[index] === "uniqueid") {
                returnColumn.renderCell = (row: any) =>
                    getDownloadDocument(row, action);
            } else if (column_keys[index] === "is_online") {
                returnColumn.renderCell = (row: any) =>
                    getfilemanagerIsOnline(row);
            } else if (column_keys[index] === "linkedToDeadline") {
                returnColumn.renderCell = (row: any) =>
                    getfilemanagerLinkedToDeadline(row);
            } else if (column_keys[index] === "visible") {
                returnColumn.renderCell = (row: any) =>
                    getfilemanagerVisible(row);
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
