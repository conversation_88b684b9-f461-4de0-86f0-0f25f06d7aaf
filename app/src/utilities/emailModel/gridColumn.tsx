import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { idSezioneField } from "../helperComponents";

export const getEmailModelGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome"), t("Tags"), t("Sezione")],
            column_keys: ["nome", "tags", "id_sezione"],
            column_widths: ["30%", "30%", "30%"],
            sortable: [true, true, false],
            cell_templates: [null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapEmailModelColumnNames(response, t);
};

export const mapEmailModelColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "id_sezione") {
                returnColumn.renderCell = (row: any) => idSezioneField(row, t);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    return columns as unknown as IGridColumn[];
};
