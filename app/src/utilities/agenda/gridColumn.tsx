import BaseGridList from "../../models/BaseGridList";
import { statusUdienzaField } from "../helperComponents";
import { IGridColumn } from "../../interfaces/general.interfaces";

export const getAgendaGrids = async (data: any, t: any) => {
    let { column_names, column_keys, sortable, column_widths, cell_templates } =
        data.gridsSettings;

    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: JSON.parse(column_names),
            column_keys: JSON.parse(column_keys),
            column_widths: JSON.parse(column_widths),
            sortable: JSON.parse(sortable),
            cell_templates: JSON.parse(cell_templates),
        },
    });

    return mapAgendaColumnNames(response, t);
};

export const getImpegniCorrelati = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.from<PERSON>son({
        gridsSettings: {
            column_names: [t("Oggetto"), t("Data ora"), t("Stato")],
            column_keys: ["testo", "data", "stato"],
            column_widths: ["50%", "25%", "25%"],
            sortable: [false, true, true],
            cell_templates: [null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapImpegniCorrelati(response, t);
};

const mapImpegniCorrelati = (response: any, t: any) => {
    const { column_names, column_keys, sortable, column_widths } =
        response.gridsSettings;
    let columns = column_names.map((cln: string, index: number) => {
        if (
            column_widths !== undefined &&
            column_widths[index] !== "0%" &&
            column_keys[index] !== ""
        ) {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_names[index] === "Stato") {
                returnColumn.renderCell = (row: any) =>
                    statusUdienzaField(row?.row.status, t);
                    
            } else if (column_names[index] === "Oggeto") {
                returnColumn.valueGetter = (row: any) => {
                    return row.row["testo"];
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

const mapAgendaColumnNames = (response: any, t: any) => {
    const { column_names, column_keys, sortable, column_widths } =
        response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        if (
            column_widths !== undefined &&
            column_widths[index] !== "0%" &&
            column_keys[index] !== ""
        ) {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_names[index] === "Stato") {
                returnColumn.renderCell = (row: any) =>
                    statusUdienzaField(row?.row?.stato_evasa, t);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });

    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
