import BaseGridList from "../../models/BaseGridList";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import { mapOtherList } from "../common";
import { ImpegniCheckBox } from "../helperComponents";

export const getGestioneGrid = async (
    selectedRows: string[],
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                "",
                t("Tipologia"),
                t("Attivita"),
                t("Intestatari"),
                t("Data"),
            ],
            column_keys: ["id", "tipologia", "attivita", "intestatari", "data"],
            column_widths: ["2%", "15%", "30%", "40%", "13%"],
            sortable: [false, true, true, true, true],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapGestioneColumns(response, selectedRows, t);
};

export const mapGestioneColumns = (
    response: any,
    selectedRows: string[],
    _t?: any,
    _common?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_keys[index] === "id") {
                returnColumn.renderCell = (row: any) => {
                    return (
                        <ImpegniCheckBox
                            row={row}
                            selectedRows={selectedRows}
                        />
                    );
                };
            }

            return returnColumn;
        }
    });

    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });

    return columns as unknown as IGridColumn[];
};
