/**
 * Utility functions for transforming URLs when switching between legacy and non-legacy modes
 */

interface UrlTransform {
  path: string;
  legacy: boolean;
}

// Mirror of PHP OnefrontConstants::PATHS_TO_REDIRECT_TO_ONEFRONT
const PATHS_TO_REDIRECT_TO_ONEFRONT: Record<string, UrlTransform> = {
  'calendar/calendar': {
    path: '/calendar/calendar',
    legacy: false,
  },
  'anagrafiche/update': {
    path: '/anagrafiche/view',
    legacy: true, // Permette il passaggio dei parametri query come ?id=X
  },
};

/**
 * Transform a legacy URL to the corresponding OneFront URL when exiting legacy mode
 * @param currentUrl - The current URL (including pathname and search)
 * @returns The transformed URL or the original URL if no transformation is needed
 */
export function transformLegacyUrl(currentUrl: string): string {
  try {
    
    const url = new URL(currentUrl, window.location.origin);
    const pathname = url.pathname;
    const search = url.search;
    
    // Remove /legacy prefix if present
    const cleanPath = pathname.replace(/^\/legacy/, '');
    
    // Parse the clean path to extract controller/action
    const pathParts = cleanPath.split('/').filter(Boolean);
    if (pathParts.length < 2) {
      console.log('breaked point');
      return cleanPath + search;
    }
    
    const controller = pathParts[0];
    const action = pathParts[1];
    const controllerAction = `${controller}/${action}`;
        
    // Check if this controller/action has a OneFront mapping
    const transform = PATHS_TO_REDIRECT_TO_ONEFRONT[controllerAction];
    if (!transform) {
      return cleanPath + search;
    }
        
    let transformedPath = transform.path;
    
    // Special handling for anagrafiche/update with id parameter
    if (controller === 'anagrafiche' && action === 'update') {
      const urlParams = new URLSearchParams(search);
      const id = urlParams.get('id');
            
      if (id) {
        // Transform /anagrafiche/update?id=74 to /anagrafiche/view?id=74
        // Keep it as query parameter, not path parameter
        const finalUrl = `${transform.path}?id=${id}`;
        
        // Remove the id from the original params and add other params if any
        urlParams.delete('id');
        const remainingSearch = urlParams.toString();
        const fullUrl = finalUrl + (remainingSearch ? `&${remainingSearch}` : '');
        
        return fullUrl;
      }
    }
    
    const finalUrl = transformedPath + search;
    return finalUrl;
  } catch (error) {
    return currentUrl;
  }
}

/**
 * Check if a URL needs transformation when exiting legacy mode
 * @param currentUrl - The current URL
 * @returns True if the URL needs transformation
 */
export function needsLegacyUrlTransform(currentUrl: string): boolean {
  try {    
    const url = new URL(currentUrl, window.location.origin);
    const pathname = url.pathname;
    
    // Remove /legacy prefix if present
    const cleanPath = pathname.replace(/^\/legacy/, '');
    
    // Parse the clean path to extract controller/action
    const pathParts = cleanPath.split('/').filter(Boolean);
    if (pathParts.length < 2) {
      return false;
    }
    
    const controller = pathParts[0];
    const action = pathParts[1];
    const controllerAction = `${controller}/${action}`;
    
    const hasTransform = controllerAction in PATHS_TO_REDIRECT_TO_ONEFRONT;
    
    return hasTransform;
  } catch (error) {
    return false;
  }
}