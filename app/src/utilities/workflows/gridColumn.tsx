import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getWorkflowsGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: ["id", t("Nome"), t("Descrizione"), t("Tags")],
            column_keys: ["id", "nome", "descrizione", "tags"],
            column_widths: ["5%", "25%", "35%", "35%"],
            sortable: [true, true, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapWorkflowsColumns(response, t);
};

const mapWorkflowsColumns = (response: any, _t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names
        .map((cln: string, index: number) => {
            if (column_widths !== undefined && column_widths[index] !== "0%") {
                const returnColumn: any = {
                    field: column_keys[index],
                    headerName: cln,
                    sortable: sortable[index],
                    flex:
                        Math.round(
                            (parseInt(column_widths[index]) / 100) * 1e2
                        ) / 1e2,
                };
                return returnColumn;
            }
            return undefined;
        })
        .filter((element: any) => element !== undefined);

    return columns as unknown as IGridColumn[];
};
