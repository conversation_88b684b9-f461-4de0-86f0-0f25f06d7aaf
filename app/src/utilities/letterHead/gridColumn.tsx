import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { showLogoField } from "../helperComponents";

export const getLetterHeadsGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Descrizione"),
                t("Nome file"),
                t("Espandi in larghezza"),
                t("Allineamento"),
                t("Utilizzato da"),
                t(" "),
            ],
            column_keys: [
                "description",
                "filename",
                "full_width",
                "align",
                "users",
                "use_as_logo",
            ],
            column_widths: ["23%", "20%", "20%", "12%", "15%", "10%"],
            sortable: [true, false, false, false, false, false],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapLetterHeadsColumnNames(response);
};

export const mapLetterHeadsColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "use_as_logo") {
                returnColumn.renderCell = (row: any) => showLogoField(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
