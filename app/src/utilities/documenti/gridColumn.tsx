import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { documentiIconField } from "../helperComponents";

export const getDocumentiGrid = async (t?: any, handlePrintDocumenti?: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome file"), t("Descrizione"), t("Data"), t("")],
            column_keys: ["nomefile", "titolodocumento", "datadoc", "id"],
            column_widths: ["25%", "25%", "25%", "10%"],
            cell_templates: [null, null, null, null],
            sortable: [true, false, true, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDocumentiFieldColumnNames(response, handlePrintDocumenti);
};

export const mapDocumentiFieldColumnNames = (
    response: any,
    handlePrintDocumenti: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "id") {
                returnColumn.renderCell = (row: any) =>
                    documentiIconField({ row, handlePrintDocumenti });
            } else {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
