import {
    changePinCertificate,
    statusCertificate,
} from "../../features/studio/certificates/helper";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames, mapOtherList } from "../common";

export const getCategoriesViewGrid = async (
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Id"), t("Nome"), t("Descrizione"), t("Campi")],
            column_keys: ["id", "nome", "descrizione", "lista"],
            column_widths: ["5%", "15%", "40%", "40%"],
            sortable: [true, true, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapOtherColumnNames(response);
};

export const mapCertificateColumnNames = (
    response: any,
    t: any,
    redirectPin?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "status") {
                returnColumn.renderCell = (row: any) => statusCertificate(row.row);
            } else if (column_keys[index] === "changePin") {
                returnColumn.renderCell = (row: any) =>
                    changePinCertificate(row.row, t, redirectPin);
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const getCertificateGrid = async (
    t: any,
    redirectPin?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data"),
                t("Codice fiscale"),
                t("Numero cellulare"),
                t("Data attivazione"),
                t("Data revoca / scadenza"),
                t("Identificativo"),
                t("Stato"),
                t("Azioni"),
            ],
            column_keys: [
                "date",
                "fiscal_code",
                "mobile",
                "activation_date",
                "revocation_date",
                "certificate_id",
                "status",
                "changePin",
            ],
            column_widths: [
                "15%",
                "15%",
                "15%",
                "15%",
                "15%",
                "15%",
                "10%",
                "20%",
            ],
            sortable: [false, false, false, false, false, false, false, false],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapCertificateColumnNames(response, t, redirectPin);
};
