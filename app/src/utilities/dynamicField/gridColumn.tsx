import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import {
    deletedDynamicField,
    assegnatoDynamicField,
} from "../helperComponents";

export const getDynamicFieldGrid = async (
    t: any,
    deleteAction: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("id"),
                t("Nome"),
                t("Descrizione"),
                t("Tipo"),
                t("Categorie"),
                t("Eliminato"),
                t("Assegnato"),
            ],
            column_keys: [
                "id",
                "nome",
                "descrizione",
                "tipo",
                "categorie",
                "deleted",
                "assegnato",
            ],
            column_widths: ["10%", "10%", "10%", "10%", "10%", "10%", "10%"],
            sortable: [true, true, false, false, false, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapDynamicFieldColumnNames(response, t, deleteAction);
};

export const mapDynamicFieldColumnNames = (
    response: any,
    t: any,
    deleteAction: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "deleted") {
                returnColumn.renderCell = (row: any) =>
                    deletedDynamicField(row, deleteAction);
            } else if (column_keys[index] === "assegnato") {
                returnColumn.renderCell = (row: any) =>
                    assegnatoDynamicField(row, t);
            } else {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
