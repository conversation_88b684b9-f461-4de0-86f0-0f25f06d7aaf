import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { joinWithDash } from "../utils";

export const getLibraryGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Titolo"),
                t("Codice"),
                t("Autore"),
                t("Editore"),
                t("Prestato a"),
                t("Valore"),
                t("Ubicazione"),
            ],
            column_keys: [
                "titolo",
                "b_codice",
                "autore",
                "editore",
                "nome_prestatario",
                "valore",
                "ubicazione",
            ],
            column_widths: ["25%", "20%", "20%", "15%", "15%", "10%", "15%"],
            sortable: [true, true, true, true, true, true, true],
            cell_templates: [null, null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapLibraryColumns(response, t);
};

export const mapLibraryColumns = (response: any, _t?: any, _common?: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (data: any) => {
                let { row } = data;
                switch (column_keys[index]) {
                    case "ubicazione":
                        return joinWithDash([
                            row["nome"],
                            row["ubicazione"],
                            row["posizione"],
                        ]);
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const getLoansGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Prestatario"),
                t("Periodo"),
                t("Data restituzione"),
            ],
            column_keys: ["nome_prestatario", "periodo", "data_restituzione"],
            column_widths: ["30%", "30%", "30%"],
            sortable: [true, false],
            cell_templates: [null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapLoanColumnNames(response, t);
};

export const mapLoanColumnNames = (response: any, _t?: any, _common?: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (data: any) => {
                let { row } = data;
                switch (column_keys[index]) {
                    case "periodo":
                        return `${row["data_inizio"]} - ${row["data_fine"]}`;
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
