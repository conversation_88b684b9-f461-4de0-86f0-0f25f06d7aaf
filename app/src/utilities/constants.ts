export const BASE_CUSTOM_HEADERS = {
  "Netlex-Origin": "Netlex4",
  "Netlex-Client": "OneFront",
  "Content-Type": "application/x-www-form-urlencoded",
};

// in prodo sarà amazon ?
export const DEFAULT_SUBDOMAIN = "test";

export const BASE_URN = "mkt:legal:backend:api";

export const DEFAULT_ROWS_PER_PAGE = 10;

export const GUEST_ROUTES = [
  "/",
  "/login",
  "/register",
  "/forgot-password",
  "/reset-password",
  "/register",
];
export const windowSize = {
  MOBILE: 576,
  MEDIUM: 768,
  LARGE: 992,
  EXTRA_LARGE: 1200,
  EXTRA_EXTRA_LARGE: 1400,
};
