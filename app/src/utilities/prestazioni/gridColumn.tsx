import { IGridColumn, IGridSettings } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { safeRenderCell } from "../safeRender";

export const getPerformancesObjectGrid = async (t?: any): Promise<IGridColumn[]> => {
  const response = BaseGridList.fromJson({
    gridsSettings: {
      column_names: [
        t("Nome"),
        t("Valore"),
        t("Tipologia valore"),
        t("Quantità"),
        t("Data"),
        t("Fattura"),
        t("Immesso il"),
        t("Utente"),
        t("Tipo"),
      ],
      column_keys: [
        "nome",
        "valore",
        "competenza",
        "quantita",
        "data_prestazione",
        "fattura_associata",
        "immesso_il",
        "immesso_da",
        "tipo",
      ],
      column_widths: ["20%", "10%", "10%", "10%", "10%", "10%", "10%", "10%", "10%"],
      sortable: [false, false, false, false, false, false, false, false, false],
      cell_templates: [null, null, null, null, null, null, null, null, null],
      header_templates: "",
      column_totals: null,
    },
  });

  return mapPerformanceColumnNames(response, t);
};

/* -------------------------------------------------------------------------- */
/*                         costruzione dinamica colonne                       */
/* -------------------------------------------------------------------------- */
export function mapPerformanceColumnNames(response: any, t?: any): IGridColumn[] {
  const { column_names, column_keys, sortable, column_widths }: IGridSettings = mapOtherList(response);

  return (
    column_names
      .map((name: string, idx: number) => {
        /* salta colonne nascoste / senza chiave -------------------------- */
/* salta colonne nascoste / senza chiave ---------------------------- */
if (!column_keys[idx] || column_widths?.[idx] === "0%") return undefined;

/* width sempre definita -------------------------------------------- */
const widthStr = column_widths?.[idx] ?? "100%";

const col: any = {
  field:       column_keys[idx],
  headerName:  name,
  flex:        Math.round((parseInt(widthStr, 10) / 100) * 100) / 100,
  sortable:    !!sortable?.[idx],
};

        /* ---------- colonne con formattazione personalizzata ------------ */
        switch (col.field) {
          case "tipo":
            col.renderCell = (params: any) => definePerformanceType(params.row.tipo, t);
            break;

          case "competenza":
            col.renderCell = (params: any) => definePerformanceCompetenza(params.row.competenza, t);
            break;

          case "valore":
            col.renderCell = (params: any) => definePerformanceValore(params.row.valore, t);
            break;

          /* ---------- tutte le altre colonne --------------------------- */
          default:
            col.renderCell = safeRenderCell(col.field);
        }

        return col;
      })
      /* filtra eventuali “buchi”                                           */
      .filter(Boolean) as IGridColumn[]
  );
}

/* -------------------------------------------------------------------------- */
/*                       helper di formattazione custom                       */
/* -------------------------------------------------------------------------- */
export function definePerformanceType(code: string, t: any): string {
  switch (code) {
    case "1":
      return t("Timesheet");
    case "2":
      return t("Movimento");
    case "3":
      return t("Impegno");
    case "4":
      return t("Udienza");
    case "5":
      return t("Voce libera");
    case "6":
      return t("Listino");
    case "7":
      return t("Tariffario");
    default:
      return "";
  }
}

export function definePerformanceCompetenza(code: string, t: any): string {
  switch (code) {
    case "0":
      return t("Non specificata");
    case "1":
      return t("Spesa anticipata");
    case "2":
      return t("Spesa imponibile");
    case "3":
      return t("Spesa esente art. 10");
    case "4":
      return t("Spesa esclusa art. 15");
    case "5":
      return t("Spesa non imponibile");
    case "8":
      return t("Spesa imponibile dlgs 192/05");
    case "10":
      return t("Giroconto");
    default:
      return "";
  }
}

export function definePerformanceValore(value: string | number, t: any): string {
  if (value == null) return "";
  return `${value} ${t("€")}`;
}