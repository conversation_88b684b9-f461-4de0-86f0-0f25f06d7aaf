import { IGridColumn } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames } from "../common";

export const getClauseGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome"), t("Categoria")],
            column_keys: ["nome", "categoria"],
            column_widths: ["80%", "20%"],
            cell_templates: [null, null],
            sortable: [true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};
