import BaseGridList from "../../models/BaseGridList";
import {
  convertMinutesToHours,
  getDurationCell,
  baseTextField,
  visibleTimesheetField,
  calculateTariffaOraria,
  calculateTariffaTotale,
  calculateCostoOrario,
  calculateCostoRisorsa,
  calculateMargin,
  modificaTimesheetRow,
} from "../helperComponents";
import { GridColDef, GridCellParams } from "@mui/x-data-grid-pro";
import { removeLinks } from "../utils";

export const getTimesheetGrid = async (
  data: any,
  t: any,
  navigate: any
): Promise<GridColDef[]> => {
  const { column_names, column_keys, sortable, column_widths, cell_templates } =
    data;

  const response = BaseGridList.fromJson({
    gridsSettings: {
      column_names: JSON.parse(column_names),
      column_keys: [...JSON.parse(column_keys), "fileHeaderString"],
      column_widths: JSON.parse(column_widths),
      sortable: JSON.parse(sortable),
      cell_templates: JSON.parse(cell_templates),
    },
  });

  return mapTimesheetColumnNames(response, t, navigate);
};

const mapTimesheetColumnNames = (
  response: any,
  t: any,
  navigate: any
): GridColDef[] => {
  const { column_names, column_keys, sortable, column_widths } =
    response.gridsSettings;

  return column_keys
    .map((key: string, idx: number) => {
      const width = column_widths?.[idx];
      if (!key || !width || width === "0%") return undefined;

      const field = key === "date" ? "dateDisplay" : key;
      const col: GridColDef = {
        field,
        headerName: column_names[idx],
        flex: parseInt(width, 10) / 100,
        sortable: sortable[idx],
        hideable: true,
      };

      switch (key) {
        case "date":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "file"
              ? removeLinks(params.row.fileHeaderString)
              : params.row?.date ?? "";
          col.renderCell = (params: GridCellParams) => (
+            <span>{String(params.value)}</span>
          );
          col.colSpan = (params: GridCellParams) =>
            params.row?.type === "file" ? 14 : 1;
          break;

        case "started_at":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.started_at)
              : convertMinutesToHours(params.row?.started_at ?? 0);
          col.renderCell = (params: GridCellParams) => (
+            <span>{String(params.value)}</span>
          );
          break;

        case "duration":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.duration)
              : String(getDurationCell(params.row ?? {}));
          col.renderCell = (params: GridCellParams) => (
+            <span>{String(params.value)}</span>
          );
          break;

        case "processed":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.processed)
              : params.row?.processed !== "1"
              ? t("No")
              : t("Sì");
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.processed
              : baseTextField(params.row?.processed !== "1", t);
          break;

        case "visible":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.visible)
              : params.row?.visible !== "1"
              ? t("No")
              : t("Sì");
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.visible
              : visibleTimesheetField(params.row?.visible !== "1", t);
          break;

        case "tariffa_oraria":
          col.valueGetter = (params: GridCellParams) =>
            String(calculateTariffaOraria(params.row ?? {}));
          col.renderCell = (params: GridCellParams) =>
            calculateTariffaOraria(params.row ?? {});
          break;

        case "tariffa_totale":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.tariffa_totale)
              : String(calculateTariffaTotale(params.row ?? {}));
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.tariffa_totale
              : calculateTariffaTotale(params.row ?? {});
          break;

        case "costo_orario":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.costo_orario)
              : String(calculateCostoOrario(params.row ?? {}));
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.costo_orario
              : calculateCostoOrario(params.row ?? {});
          break;

        case "costo_risorsa":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.costo_risorsa)
              : String(calculateCostoRisorsa(params.row ?? {}));
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.costo_risorsa
              : calculateCostoRisorsa(params.row ?? {});
          break;

        case "margin":
          col.valueGetter = (params: GridCellParams) =>
            params.row?.type === "total"
              ? String(params.row.margin)
              : String(calculateMargin(params.row ?? {}));
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.margin
              : calculateMargin(params.row ?? {});
          break;

        case "modifiable":
          col.valueGetter = () => "";
          col.renderCell = (params: GridCellParams) =>
            params.row?.type === "total"
              ? params.row.modifiable
              : modificaTimesheetRow(params.row ?? {}, navigate);
          break;

        default:
          col.valueGetter = (params: GridCellParams) =>
            String(params.row?.[key] ?? "");
      }

      return col;
    })
    .filter((c: GridColDef | undefined): c is GridColDef => !!c);};