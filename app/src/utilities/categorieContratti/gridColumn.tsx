import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getCategorieContrattiGrid = async (
    t: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("id"), t("Nome"), t("Descrizione"), t("Campi")],
            column_keys: ["id", "nome", "descrizione", "campi"],
            column_widths: ["10%", "10%", "10%", "10%"],
            sortable: [true, true, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapCategorieContrattiColumnNames(response);
};

export const mapCategorieContrattiColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            returnColumn.valueGetter = (row: any) => {
                return row["value"];
            };

            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
