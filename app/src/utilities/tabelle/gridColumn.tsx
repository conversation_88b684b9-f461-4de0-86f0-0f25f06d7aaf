import {
    IGridSettings,
    IGridColumn,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";

import { mapOtherColumnNames, mapOtherList } from "../common";
import {
    addDefaultText,
    addasterisk,
    baseTextField,
    cambioCurrencyField,
    changeLabelName,
    deadlinestypesColor,
    deleteAntirecregister,
    downloadAntirecregister,
    expendituresTipo,
    preferitaAuthoritaField,
    sectionalTipo,
    sectionalValore,
} from "../helperComponents";

export const getSimpleGrid = async (
    data: any,
    t: any,
    route: string,
    downloadContent: any
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: data.columnNames,
            column_keys: data.columnKeys,
            column_widths: data.columnWidths,
            sortable: data.sortable,
            cell_templates: data.cellTemplates,
            header_templates: "",
            column_totals: null,
        },
    });
    if (route === "authorities") {
        return mapAuthoritaColumns(response, t);
    } else if (route === "banks") {
        return mapBankColumnNames(response);
    } else if (route === "documentcategories") {
        return mapDocumentcategoriesColumnNames(response, t);
    } else if (route === "contactandaddress") {
        return mapContactAddressColumNames(response, t);
    } else if (route === "descriptions") {
        return mapDescriptionColumnNames(response);
    } else if (route === "additionalnotes") {
        return mapAdditionalnoteColumnNames(response);
    } else if (route === "filesstatus") {
        return mapFilesstatusColumnNames(response);
    } else if (route === "sectional") {
        return mapSectionalColumnNames(response);
    } else if (route === "expenditures") {
        return mapExpendituresColumnNames(response);
    } else if (route === "deadlinestandard") {
        return mapDeadlinestandardColumnNames(response);
    } else if (route === "deadlinestypes") {
        return mapDeadlinestypesColumnNames(response);
    } else if (route === "filestypes") {
        return mapFilestypesColumnNames(response, t);
    } else if (route === "antirecregister") {
        return mapAntirecregisterColumnNames(response, t, downloadContent);
    } else if (route === "currencies") {
        return mapCurrenciesColumnNames(response);
    } else return mapOtherColumnNames(response);
};

export const mapCurrenciesColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "nome") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row["nome"] + " (" + row["simbolo"] + ")";
                };
            } else if (column_keys[index] === "cambio") {
                returnColumn.renderCell = (row: any) =>
                    cambioCurrencyField(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapAuthoritaColumns = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "pwid") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(
                        row["pwid"] === null || row["pwid"] === "",
                        t
                    );
                };
            }
            if (column_keys[index] === "preferita") {
                returnColumn.renderCell = (row: any) =>
                    preferitaAuthoritaField(row);
            }
            if (column_keys[index] === "udienze") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(
                        row["citta"] === null || row["citta"] === "",
                        t
                    );
                };
            } else {
                return {
                    field: column_keys[index],
                    headerName: cln,
                    flex: 1,
                    sortable: sortable[index],
                    valueGetter: (row: any) => {
                        return row["value"];
                    },
                };
            }
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const mapBankColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row[column_keys[index]] === "0" ? "Banca" : "Cassa";
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapDocumentcategoriesColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "downloadable") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(row[column_keys[index]] === "0", t);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const mapContactAddressColumNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "relazione") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return changeLabelName(row["relazione"], t);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapDescriptionColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row[column_keys[index]] === "1"
                        ? "Motivo fattura"
                        : "Desc. forfettario";
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const mapAdditionalnoteColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "is_default") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return addasterisk(row[column_keys[index]]);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapFilesstatusColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "nome") {
                returnColumn.renderCell = (row: any) => addDefaultText(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapSectionalColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (row: any) => sectionalTipo(row);
            } else if (column_keys[index] === "valore") {
                returnColumn.renderCell = (row: any) => sectionalValore(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapExpendituresColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (row: any) => expendituresTipo(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapDeadlinestandardColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (
                column_keys[index] === "importante" ||
                column_keys[index] === "fatturabile"
            ) {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row[column_keys[index]] == 1 ? "Sì" : "No";
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapDeadlinestypesColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "color") {
                returnColumn.renderCell = (row: any) =>
                    deadlinestypesColor(row);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const mapFilestypesColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "pwid") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(
                        row["pwid"] === null || row["pwid"].includes("-"),
                        t
                    );
                };
            } else if (column_keys[index] === "visible") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(row["visible"] == 0, t);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const mapAntirecregisterColumnNames = (
    response: any,
    t: any,
    downloadContent: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_names[index] === "Download") {
                returnColumn.renderCell = (row: any) =>
                    downloadAntirecregister(row, downloadContent, t);
            } else if (column_names[index] === "Delete") {
                returnColumn.renderCell = (row: any) =>
                    deleteAntirecregister(row, downloadContent, t);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
