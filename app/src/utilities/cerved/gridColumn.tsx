import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import {
    cervedBusinessName,
    cervedSubjectType,
    cervedDescription1,
    cervedTaxcode,
    cervedReaNumber,
    cervedDescription2,
    cervedCode,
    cervedSubjectId,
} from "../helperComponents";

export const getCervedGrid = async (
    t: any,
    actions: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome"),
                t("Tipo"),
                t("Stato"),
                t("P IVA / CF"),
                t("Codice rea"),
                t("Indirizzo"),
                t("Tipo attività"),
                t("Dettag<PERSON>"),
            ],
            column_keys: [
                "business_name",
                "subject_type",
                "description",
                "tax_code",
                "rea_number",
                "description2",
                "code",
                "subject_id",
            ],
            column_widths: [
                "20%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
            ],
            cell_templates: [null, null],
            sortable: [false, false, false, false, false, false, false, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapCervedColumnNames(response, t, actions);
};

export const mapCervedColumnNames = (response: any, t: any, actions: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "business_name") {
                returnColumn.renderCell = (row: any) => cervedBusinessName(row);
            } else if (column_keys[index] === "subject_type") {
                returnColumn.renderCell = (row: any) => cervedSubjectType(row);
            } else if (column_keys[index] === "description") {
                returnColumn.renderCell = (row: any) => cervedDescription1(row);
            } else if (column_keys[index] === "tax_code") {
                returnColumn.renderCell = (row: any) => cervedTaxcode(row);
            } else if (column_keys[index] === "rea_number") {
                returnColumn.renderCell = (row: any) => cervedReaNumber(row);
            } else if (column_keys[index] === "description2") {
                returnColumn.renderCell = (row: any) => cervedDescription2(row);
            } else if (column_keys[index] === "code") {
                returnColumn.renderCell = (row: any) => cervedCode(row);
            } else if (column_keys[index] === "subject_id") {
                returnColumn.renderCell = (row: any) =>
                    cervedSubjectId(row, t, actions);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
