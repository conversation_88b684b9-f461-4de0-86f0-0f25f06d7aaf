import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { baseTextLabelField } from "../helperComponents";

export const getPecNotificationsGrid = (t: any): IGridColumn[] => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data creazione"),
                t("Avvocato mittente"),
                t("Destinatari"),
                t("Data invio"),
                t("Stato"),
            ],
            column_keys: [
                "created_at",
                "lawyer_id",
                "recipients",
                "sent_at",
                "status",
            ],
            column_widths: ["20%", "20%", "20%", "20%", "20%"],
            cell_templates: [null, null, null, null, null],
            sortable: [true, true, false, true, true],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapPecNotificationsColumnNames(response);
};

export const mapPecNotificationsColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "status") {
                returnColumn.renderCell = (row: any) =>
                    baseTextLabelField(
                        row.row["status"] === "0",
                        "Bozze",
                        "File pronti"
                    );
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
