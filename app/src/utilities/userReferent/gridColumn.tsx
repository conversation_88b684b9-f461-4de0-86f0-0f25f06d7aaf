import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { defineDelegateDeleteButtons } from "../helperComponents";

export const getUserReferentsGrid = async (
    setOpenConfimModalReferents: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >,
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Delegato"), "", ""],
            column_keys: ["nome", "", "dactions"],
            column_widths: ["75%", "20%", "25%"],
            sortable: [false, false],
            cell_templates: [null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapUserDelegatesColumn(response, setOpenConfimModalReferents, t);
};
export const mapUserDelegatesColumn = (
    response: any,
    setOpenConfimModalReferents: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >,
    t?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (data: any) => {
                let { row } = data;
                switch (column_keys[index]) {
                    case "dactions":
                        return defineDelegateDeleteButtons(
                            data,
                            setOpenConfimModalReferents,
                            t
                        );
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
