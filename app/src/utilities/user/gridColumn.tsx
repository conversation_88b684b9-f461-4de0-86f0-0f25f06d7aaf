import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { qualificaUserField, baseTextField } from "../helperComponents";

export const getUserGrid = async (
    t: any,
    canAccessUserQualifca?: boolean
): Promise<IGridColumn[]> => {
    if (canAccessUserQualifca) {
        const response = BaseGridList.fromJson({
            gridsSettings: {
                column_names: [
                    t("Nome Completo"),
                    t("Email"),
                    t("Referente"),
                    t("Qualifica"),
                    t("Ultimo accesso"),
                    t("Stato"),
                ],
                column_keys: [
                    "nomeutente",
                    "email",
                    "referente",
                    "qualifica",
                    "last_login",
                    "stato",
                ],
                column_widths: ["15%", "15%", "15%", "15%", "15%", "15%"],
                sortable: [true, true, true, true, false, true],
                cell_templates: [null, null, null, null, null, null],
                header_templates: "",
                column_totals: null,
            },
        });
        return mapUserColumnNames(response, t);
    } else {
        const response = BaseGridList.fromJson({
            gridsSettings: {
                column_names: [
                    t("Utente"),
                    t("Email"),
                    t("Referente"),
                    t("Qualifica"),
                    t("Ultimo accesso"),
                    t("Stato"),
                ],
                column_keys: [
                    "nomeutente",
                    "email",
                    "referente",
                    "qualifica",
                    "last_login",
                    "stato",
                ],
                column_widths: ["15%", "15%", "15%", "15%", "15%", "15%"],
                sortable: [true, true, true, true, false, true],
                cell_templates: [null, null, null, null, null, null],
                header_templates: "",
                column_totals: null,
            },
        });
        return mapUserColumnNames(response, t);
    }
};

export const mapUserColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "qualifica") {
                returnColumn.renderCell = (row: any) => qualificaUserField(row);
            } else if (column_keys[index] === "stato") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(row["stato"] !== "1", t, "active");
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
