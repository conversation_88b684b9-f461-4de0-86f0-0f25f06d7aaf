import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { baseTextField, allNetlexTemplateField } from "../helperComponents";

export const getTemplateGrid = async (
    t: any,
    practiceSummary?: boolean
): Promise<IGridColumn[]> => {
    const settings = practiceSummary
        ? {
              columnNames: [t("Titolo"), t("Autore"), t("Categoria")],
              columnKeys: ["title", "author_name", "category"],
              columnWidths: ["20%", "20%", "20%"],
              sortable: [true, true, true],
          }
        : {
              columnNames: [
                  t("Titolo"),
                  t("Autore"),
                  t("Categoria"),
                  t("Visibile agli utenti di questo studio"),
                  t("Visibile agli utenti degli altri studi"),
                  t("Modificabile"),
              ],
              columnKeys: [
                  "title",
                  "author_name",
                  "category",
                  "all_users",
                  "all_netlex",
                  "modifiable",
              ],
              columnWidths: ["20%", "20%", "10%", "10%", "10%", "10%"],
              sortable: [true, true, true, false, false, true],
          };

    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: settings.columnNames,
            column_keys: settings.columnKeys,
            column_widths: settings.columnWidths,
            sortable: settings.sortable,
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapTemplateColumnNames(response, t);
};

export const mapTemplateColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "all_users") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(row["all_users"] === "0", t);
                };
            } else if (column_keys[index] === "all_netlex") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return allNetlexTemplateField(row["all_netlex"], t);
                };
            } else if (column_keys[index] === "modifiable") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(row["modifiable"] === "0", t);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
