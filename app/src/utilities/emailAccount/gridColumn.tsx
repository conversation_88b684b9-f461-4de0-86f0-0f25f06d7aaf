import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { baseTextField } from "../helperComponents";
export const getEmailAccountGrid = (t: any): IGridColumn[] => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Email"),
                t("Utente"),
                t("Gestore PEC"),
                t("Username"),
                t("Predefinito PCT"),
                t("Modificabile"),
                t("Predefinito workflow"),
            ],
            column_keys: [
                "email",
                "nomeutente",
                "pec_provider_id",
                "username",
                "reginde",
                "canModify",
                "workflow",
            ],
            column_widths: ["20%", "20%", "20%", "10%", "10%"],
            sortable: [true, true, false, false, true, false, false],
            cell_templates: [null, null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapEmailColumnNames(response, t);
};

export const mapEmailColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "canModify") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(
                        row["workflow"] === "0",
                        t,
                        "Modificabile"
                    );
                };
            } else if (column_keys[index] === "workflow") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return baseTextField(
                        row["workflow"] === "0",
                        t,
                        "Predefinito workflow"
                    );
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
