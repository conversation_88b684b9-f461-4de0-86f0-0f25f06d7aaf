import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import {
    conservazioneDelegation,
    fatturazioneDelegation,
    visuraDelegation,
} from "../helperComponents";

export const getDelegationGrid = async (
    t: any,
    setSelectedCheckbox: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Utente"),
                t("Referente"),
                t("Visura"),
                t("Conservazione"),
                t("Fatturazione"),
            ],
            column_keys: [
                "nomeutente",
                "referente",
                "visura",
                "conservazione",
                "fatturazione",
            ],
            column_widths: ["20%", "20%", "20%", "20%", "20%"],
            sortable: [true, true, false, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapDelegationColumnNames(response, setSelectedCheckbox);
};

export const mapDelegationColumnNames = (
    response: any,
    setSelectedCheckbox: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "visura") {
                returnColumn.renderCell = (row: any) =>
                    visuraDelegation(row, setSelectedCheckbox);
            } else if (column_keys[index] === "fatturazione") {
                returnColumn.renderCell = (row: any) =>
                    fatturazioneDelegation(row, setSelectedCheckbox);
            } else if (column_keys[index] === "conservazione") {
                returnColumn.renderCell = (row: any) =>
                    conservazioneDelegation(row, setSelectedCheckbox);
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
