import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { scoperto, tipoValidationPaymentReminder } from "../helperComponents";

export const getPaymentReminderGrid = async (
    t: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Progr."),
                t("Data"),
                t("Tipo"),
                t("R.G.(N.R.)"),
                t("Intestatario"),
                t("Importo fatturato"),
                t("IVA"),
                t("Totale"),
                t("Scoperto"),
            ],
            column_keys: [
                "progressivo",
                "data",
                "tipo",
                "rg",
                "intestatario",
                "importofatturato",
                "totale_iva",
                "totale",
                "scoperto",
            ],
            column_widths: [
                "5%",
                "10%",
                "15%",
                "5%",
                "15%",
                "10%",
                "10%",
                "15%",
            ],
            sortable: [false, true, true, false, false, true, true, true],
            cell_templates: [null, null, null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapPaymentReminderColumnNames(response);
};

export const mapPaymentReminderColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (row: any) =>
                    tipoValidationPaymentReminder(row);
            } else if (column_keys[index] === "importofatturato") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row["importofatturato"] + "/" + row["totale"];
                };
            } else if (column_keys[index] === "scoperto") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return scoperto(row["tipo"], row["scoperto"]);
                };
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
