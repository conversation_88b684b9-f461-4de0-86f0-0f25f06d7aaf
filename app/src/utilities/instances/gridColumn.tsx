import { Tag } from "@vapor/react-custom";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const STATUS = {
    "1": "IN PAUSA",
    "2": "IN CORSO",
    "3": "IN CORSO",
    "4": "COMPLETATO",
};

export const getInstancesGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Id"),
                t("Nome"),
                t("Avviato il"),
                t("Terminato il"),
                t("Pratica"),
                t("Stato"),
            ],
            column_keys: [
                "id",
                "nome",
                "immessoil",
                "terminatoil",
                "file_id",
                "stato",
            ],
            column_widths: ["5%", "25%", "20%", "20%", "10%", "20%"],
            sortable: [true, true, true, false, false, true],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapInstancesNames(response, t);
};

const mapInstancesNames = (response: any, _t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names
        .map((cln: string, index: number) => {
            if (column_widths !== undefined && column_widths[index] !== "0%") {
                const returnColumn: any = {
                    field: column_keys[index],
                    headerName: cln,
                    sortable: sortable[index],
                    flex:
                        Math.round(
                            (parseInt(column_widths[index]) / 100) * 1e2
                        ) / 1e2,
                };
                if (column_keys[index] === "stato") {
                    returnColumn.renderCell = (row: any) => {
                        return (
                            <Tag
                                label={STATUS[row.value as keyof typeof STATUS]}
                            />
                        );
                    };
                }
                return returnColumn;
            }
            return undefined;
        })
        .filter((element: any) => element !== undefined);

    return columns as unknown as IGridColumn[];
};
