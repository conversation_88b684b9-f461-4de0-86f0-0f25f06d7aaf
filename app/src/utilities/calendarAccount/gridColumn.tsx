import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import {
    calendarNetlex,
    filterCalendario,
    tipoAccountValidation,
    statoCalendarAccount,
    actionCalendarAccounts,
} from "../helperComponents";

export const getCalendarAccountsGrid = async (
    t: any,
    ScollegaCalendarAccountsDialog?: any,
    CollegaCalendarAccountsDialog?: any,
    CLIENT_MICROSOFT?: any,
    CLIENT_GOOGLE?: any,
    setLoading?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Calendario Netlex"),
                t("Calendario utente"),
                t("Personale/Generale"),
                t("Tipo account"),
                t("Attivato il"),
                t("Stato"),
                t(""),
            ],
            column_keys: [
                "calendario_netlex",
                "nome_calendario_utente",
                "filtro_calendario",
                "tipo_account2",
                "immesso_il",
                "stato",
                "uniqueid",
            ],
            column_widths: ["15%", "15%", "15%", "15%", "15%", "15%"],
            sortable: [false, false, false, false, false, false],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapCalendarAccountsColumnNames(
        response,
        t,
        ScollegaCalendarAccountsDialog,
        CollegaCalendarAccountsDialog,
        CLIENT_MICROSOFT,
        CLIENT_GOOGLE,
        setLoading
    );
};

export const mapCalendarAccountsColumnNames = (
    response: any,
    t: any,
    ScollegaCalendarAccountsDialog?: any,
    CollegaCalendarAccountsDialog?: any,
    CLIENT_MICROSOFT?: any,
    CLIENT_GOOGLE?: any,
    setLoading?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_keys[index] === "calendario_netlex") {
                returnColumn.renderCell = (row: any) => calendarNetlex(row, t);
            } else if (column_keys[index] === "filtro_calendario") {
                returnColumn.renderCell = (row: any) => filterCalendario(row);
            } else if (column_keys[index] === "tipo_account2") {
                returnColumn.renderCell = (row: any) =>
                    tipoAccountValidation(row, CLIENT_GOOGLE, CLIENT_MICROSOFT);
            } else if (column_keys[index] === "stato") {
                returnColumn.renderCell = (row: any) =>
                    statoCalendarAccount(row);
            } else if (column_keys[index] === "uniqueid") {
                returnColumn.renderCell = (row: any) =>
                    actionCalendarAccounts(
                        row,
                        t,
                        ScollegaCalendarAccountsDialog,
                        CollegaCalendarAccountsDialog,
                        setLoading
                    );
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
