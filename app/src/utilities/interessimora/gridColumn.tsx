import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getInterestMoraGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Dal"),
                t("Al"),
                t("Capitale"),
                t("Tasso"),
                t("<PERSON><PERSON><PERSON>"),
                t("Interesse"),
            ],
            column_keys: ["0", "1", "2", "3", "4", "5"],
            column_widths: ["20%", "20%", "20%", "20%", "20%"],
            sortable: [false, false, false, false, false],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNamesFormat(response);
};

export const mapOtherColumnNamesFormat = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    const columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            if (column_keys[index] === "2" || column_keys[index] === "5") {
                return {
                    name: cln,
                    selector: (row: any) => "€" + row[column_keys[index]],
                    sortField: column_keys[index],
                    sortable: sortable[index],
                };
            } else if (column_keys[index] === "3") {
                return {
                    name: cln,
                    selector: (row: any) => row[column_keys[index]] + "%",
                    sortField: column_keys[index],
                    sortable: sortable[index],
                };
            } else {
                return {
                    name: cln,
                    selector: (row: any) => row[column_keys[index]],
                    sortField: column_keys[index],
                    sortable: sortable[index],
                };
            }
        }
    });
    return columns as unknown as IGridColumn[];
};
