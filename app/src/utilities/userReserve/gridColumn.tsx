import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { defineReservesDeleteButtons } from "../helperComponents";

export const getUserReservesGrid = async (
    setOpenConfimModalReserves: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >,
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Clienti"),
                t("Controparti"),
                t("Cod. pratica"),
                t("Altre riserve"),
                "",
            ],
            column_keys: [
                "listaclienti",
                "listacontroparti",
                "codicearchivio",
                "reservations_number",
                "actions",
            ],
            column_widths: ["25%", "25%", "20%", "19%", "11%"],
            sortable: [false, false, true, false, false],
            cell_templates: [null, null, null, null, false],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapUserReservesColumn(response, setOpenConfimModalReserves);
};

export const mapUserReservesColumn = (
    response: any,
    setOpenConfimModalReserves: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (data: any) => {
                let { row } = data;
                switch (column_keys[index]) {
                    case "actions":
                        return defineReservesDeleteButtons(
                            data,
                            setOpenConfimModalReserves
                        );
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
