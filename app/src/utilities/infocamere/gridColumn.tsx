import BaseGridList from "../../models/BaseGridList";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import { mapOtherList } from "../common";
import {
    defineStartParamsColumn,
    defineIsDownloadedInfocamere,
    defineIsPaidInfocamere,
    defineActionButtonsInfocamere,
} from "../helperComponents";

export const getInfocamereDownloadsGrid = async (
    downloadContent: (id: string) => void,
    saveToPractice: (state: boolean, id: string) => void,
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data ora richiesta"),
                t("Parametri ricerca"),
                t("Stato"),
                t("Credito scalato"),
                t("Descrizione"),
                t("R.G."),
                t("Actions"),
                t("Data ora richiesta"),
                t("Parametri ricerca"),
                t("Stato"),
                t("Credito scalato"),
                t("Descrizione"),
                t("R.G."),
                t("Actions"),
            ],
            column_keys: [
                "creation_date",
                "startParams",
                "is_downloaded",
                "is_paid",
                "description",
                "rg",
                "actions",
                "creation_date",
                "startParams",
                "is_downloaded",
                "is_paid",
                "description",
                "rg",
                "actions",
            ],
            column_widths: ["20%", "22%", "20%", "15%", "10%", "8%", "15%"],
            sortable: [true, false, true, true, false, false],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapInfocamereDownloadsColumns(
        response,
        downloadContent,
        saveToPractice,
        t
    );
};

export const mapInfocamereDownloadsColumns = (
    response: any,
    downloadContent: (id: string) => void,
    saveToPractice: (state: boolean, id: string) => void,
    t?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            returnColumn.renderCell = (value: any, data: any) => {
                value;
                let { row } = data;
                switch (column_keys[index]) {
                    case "startParams":
                        return defineStartParamsColumn(data, t);
                    case "is_downloaded":
                        return defineIsDownloadedInfocamere(data, t);
                    case "is_paid":
                        return defineIsPaidInfocamere(data, t);
                    case "actions":
                        return defineActionButtonsInfocamere(
                            data,
                            downloadContent,
                            saveToPractice,
                            t
                        );
                    default:
                        return row[column_keys[index]];
                }
            };
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
