import BaseGridList from "../../models/BaseGridList";

import { AnagraficheActions, AnagrafichePraticheAction } from "../../interfaces/anagrafiche.interface";
import { IGridColumn } from "../../interfaces/general.interfaces";
import {
    codiceArchiveField,
    actionButtonsAnagrafiche,
    actionButtonsAnagrafichePratiche,
} from "../helperComponents";
import { mapOtherColumnNames } from "../common";

export const getAnagraficheNewGrid = async (
    t: any,
    actions?: AnagraficheActions
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("ID"),
                t("Denominazione"),
                t("Tipo di anagrafica"),
                t("Telefono"),
                t("Mail"),
                t("Città"),
                t("Codice fiscale"),
                t("Partita Iva"),
                "Azioni",
            ],
            column_keys: [
                "id",
                "nome",
                "tipo",
                "telefono",
                "email",
                "citta",
                "codicefiscale",
                "partitaiva",
                "Azioni",
            ],
            column_widths: [
                "5%",
                "17%",
                "10%",
                "10%",
                "8%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
            ],
            sortable: [
                true,
                true,
                true,
                true,
                true,
                true,
                true,
                true,
                false,
            ],
            cell_templates: [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
            ],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapAnagraficheColumnNames(response, actions);
};

export const mapAnagraficheColumnNames = (
    response: any,
    actions?: AnagraficheActions
) => {
    const {
        column_names,
        column_keys,
        sortable,
        cell_templates,
        column_widths,
    } = response.gridsSettings;
    let columns = column_names.map((cln: string, index: number) => {
        if (
            column_widths !== undefined &&
            column_widths[index] !== "0%" &&
            column_keys[index] !== ""
        ) {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln === "Azioni" ? "" : cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_names[index] === "Codice") {
                returnColumn.renderCell = (row: any) => codiceArchiveField(row);
            } else if (column_names[index] === "Azioni") {
                returnColumn.renderCell = (row: any) =>
                    actions ? actionButtonsAnagrafiche(row, actions) : null;
            } else if (
    cell_templates !== undefined &&
    cell_templates[index] !== "{{checkbox}}"
) {
    returnColumn.renderCell = (params: any) => {
        const value = params.row[returnColumn.field];
        if (typeof value === "object" && value !== null) {
            return value.label || "";
        }
        return value ?? "";
    };
}
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const getAnagrafichePraticheNewGrid = async (
    t: any,
    action: AnagrafichePraticheAction
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [   
                "Azioni",           
                t("Denominazione"),
                t("Indirizzo"),
                t("Codice fiscale"),
                t("Partita Iva"),
                t("Contatti"),
                t("Tipologia"),
            ],
            column_keys: [    
                "Azioni",          
                "nome",
                "indirizzo",
                "codicefiscale",
                "partitaiva",
                "telefono",
                "tipo",                
            ],
            column_widths: [
                "5%",
                "15%",
                "15%",
                "15%",
                "15%",
                "15%",
                "20%",
               
            ],
            sortable: [
                false,
                true,
                false,
                false,
                false,
                false,
                false,
               
            ],
            cell_templates: [
                null,
                null,
                null,
                null,
                null,
                null,
                null,
              
            ],
            header_templates: "",
            column_totals: null,
        },
    });
    console.log("response", response)
    return mapAnagrafichePraticheColumnNames(response, action);
    
};

export const mapAnagrafichePraticheColumnNames = (
    response: any,
    action: AnagrafichePraticheAction
) => {
    const {
        column_names,
        column_keys,
        sortable,
        cell_templates,
        column_widths,
    } = response.gridsSettings;
    let columns = column_names.map((cln: string, index: number) => {
        if (
            column_widths !== undefined &&
            column_widths[index] !== "0%" &&
            column_keys[index] !== ""
        ) {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln === "Azioni" ? "" : cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
             if (column_names[index] === "Azioni") {
                returnColumn.renderCell = (row: any) =>
                    action ? actionButtonsAnagrafichePratiche(row, action) : null;
            }
            else if (
    cell_templates !== undefined &&
    cell_templates[index] !== "{{checkbox}}"
) {
    returnColumn.renderCell = (params: any) => {
        const value = params.row[returnColumn.field];
        if (typeof value === "object" && value !== null) {
            return value.label || "";
        }
        return value ?? "";
    };
}
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

export const getAnagraficheRelationsGrid = async (
    t: any
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [                     
                t("Denominazione"),
                t("Relazione"), 
            ],
            column_keys: [                       
                "denominazione",
                "relation",                
            ],
            column_widths: [
                "50%",
                "50%",   
            ],
            sortable: [
                true,
                true,   
            ],
            cell_templates: [
                null,
                null,            
            ],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
    
};
