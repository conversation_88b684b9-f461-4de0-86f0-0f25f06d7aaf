import { useState, useEffect } from "react";
import { windowSize as size } from "./constants";

export const useIsMobile = () => {
    const hasWindow = typeof window !== "undefined";
    const getIsMobile = () => {
        const width = hasWindow ? window.screen.width : null;
        return width != null && width < size.MOBILE;
    };

    const [isMobileDimensions, setIsMobileDimensions] = useState(getIsMobile());

    useEffect(() => {
        if (hasWindow) {
            handleResize();
            window.addEventListener("resize", handleResize);
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [hasWindow]);

    const handleResize = () => {
        setIsMobileDimensions(getIsMobile());
    };

    return isMobileDimensions;
};

export const getScreenPixel = () => {
    const hasWindow = typeof window !== "undefined";
    const screenPixel = () => {
        const width = hasWindow ? window.screen.width : null;
        return width;
    };

    const [isMobileDimensions, setIsMobileDimensions] = useState(screenPixel());

    useEffect(() => {
        if (hasWindow) {
            handleResize();
            window.addEventListener("resize", handleResize);
            return () => window.removeEventListener("resize", handleResize);
        }
    }, [hasWindow]);

    const handleResize = () => {
        setIsMobileDimensions(screenPixel());
    };

    return isMobileDimensions;
};
