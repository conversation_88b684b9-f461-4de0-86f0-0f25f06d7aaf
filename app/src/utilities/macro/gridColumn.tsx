import BaseGridList from "../../models/BaseGridList";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import { mapOtherList } from "../common";
import { StatusBadge } from "@vapor/react-custom";

export const getMacroGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome"),
                t("Caratterizzazione"),
                t("Solo per udienza"),
                t("Tipologia"),
                t("Dinamica"),
                t("Visibile"),
            ],
            column_keys: [
                "nome",
                "tipo",
                "data_udienza",
                "tipologia",
                "dinamica",
                "visible",
            ],
            column_widths: ["35%", "15%", "10%", "20%", "10%", "10%"],
            sortable: [false, true, true, true, true, false],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapMacroColumnsName(response, t);
};

export const mapMacroColumnsName = (response: any, t?: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);
    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined) {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            switch (column_keys[index]) {
                case "tipo":
                    returnColumn.renderCell = (data: any) => {
                        let { row } = data;
                        return defineMacroType(row.tipo, t);
                    };
                    break;
                case "data_udienza":
                    returnColumn.renderCell = (data: any) => {
                        let { row } = data;
                        return defineMacroHearings(
                            row.data_udienza,
                            row.data_decorrenza,
                            t
                        );
                    };
                    break;
                case "tipologia":
                    returnColumn.renderCell = (data: any) => {
                        let { row } = data;
                        return row.tipologia === 1
                            ? t("PREDEFINITA")
                            : t("INSERITA DA UTENTE");
                    };
                    break;
                case "dinamica":
                    returnColumn.renderCell = (data: any) => {
                        let { row } = data;
                        return row.dinamica === 1 ? t("SI") : t("NO ");
                    };
                    break;
                case "visible":
                    returnColumn.renderCell = (data: any) => {
                        let { row } = data;
                        return defineMacroVisible(row.visible, t);
                    };
                    break;
                default:
                    returnColumn.valueGetter = (row: any) => {
                        return row["value"];
                    };
                    break;
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
export const defineMacroType = (type: string, t: any) => {
    switch (type) {
        case "1":
            return <StatusBadge bgColor={"green"} label={t("POSITIVA")} />;
        case "2":
            return <StatusBadge bgColor={"orange"} label={t("NEGATIVA")} />;

        case "3":
            return <StatusBadge bgColor={"gray"} label={t("MISTA")} />;

        default:
            return "";
    }
};

export const defineMacroHearings = (
    data_udienza: string,
    data_decorrenza: string,
    t: any
) => {
    if (data_decorrenza === "1" && data_udienza === "0") {
        return t("NO");
    } else if (data_decorrenza === "0" && data_udienza === "1") {
        return t("YES");
    }
};

export const defineMacroVisible = (visible: string, t: any) => {
    switch (visible) {
        case "1":
            return <StatusBadge bgColor={"green"} label={t("SI")} />;

        case "0":
            return <StatusBadge bgColor={"red"} label={t("NO")} />;

        default:
            return "";
    }
};
