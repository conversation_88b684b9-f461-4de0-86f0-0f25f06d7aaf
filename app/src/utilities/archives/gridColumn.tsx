import BaseGridList from "../../models/BaseGridList";
import { codiceArchiveField } from "../helperComponents";
import { IGridColumn } from "../../interfaces/general.interfaces";

export const getArchiveGrids = async (data: any) => {
  const {
    column_names,
    column_keys,
    sortable,
    column_widths,
    cell_templates,
  } = data.gridsSettings;

  const response = BaseGridList.fromJson({
    gridsSettings: {
      column_names: JSON.parse(column_names),
      column_keys: JSON.parse(column_keys),
      column_widths: JSON.parse(column_widths),
      sortable: JSON.parse(sortable),
      cell_templates: JSON.parse(cell_templates),
    },
  });

  return mapArchiveColumnNames(response);
};

const mapArchiveColumnNames = (response: any): IGridColumn[] => {
  const { column_names, column_keys, sortable, column_widths } =
    response.gridsSettings;

  let columns = column_names.map((cln: string, index: number) => {
    if (
      column_widths?.[index] !== "0%" &&
      column_keys?.[index] !== ""
    ) {
      const returnColumn: any = {};

      returnColumn.field = column_keys[index];
      returnColumn.headerName = cln;
      returnColumn.flex =
        Math.round((parseInt(column_widths[index]) / 100) * 100) / 100;
      returnColumn.sortable = sortable?.[index];

      if (cln === "Codice") {
        returnColumn.renderCell = (params: any) => codiceArchiveField(params);
      } else {
        returnColumn.renderCell = (params: any) => {
          const value = params.row[returnColumn.field];
          if (typeof value === "object" && value !== null) {
            return value.label || "";
          }
          return value ?? "";
        };
      }

      return returnColumn;
    }
  });

  return columns.filter(Boolean) as IGridColumn[];
};