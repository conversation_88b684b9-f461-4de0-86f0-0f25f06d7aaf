import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import BaseGridList from "../../../models/BaseGridList";
import { Chip } from "@vapor/react-material";
import { mapOtherList } from "../../common";

export const getDevisioneUtiliGrid = async (t: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Utente"),
                t("Basket"),
                t("Dal"),
                t("Percentuale (del basket)"),
                t("SOB"),
            ],
            column_keys: [
                "nomeutente",
                "basket",
                "date_from",
                "percentuale",
                "sob",
            ],
            column_widths: ["20%", "30%", "20%", "20%", "10%"],
            sortable: [true, true, false, false, false],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDevisioneUtiliGridColumnNames(response);
};

export const basketFunction = (row: any) => {
    return (
        <Chip
            label={`${row.basket} (${row.perc_basket}%)`}
            style={{
                backgroundColor: row.colore,
            }}
        />
    );
};

export const percentageRows = (row: any) => {
    return `${row} %`;
};

export const mapDevisioneUtiliGridColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_keys[index] === "basket") {
                returnColumn.renderCell = (row: any) => basketFunction(row.row);
            } else if (column_keys[index] === "percentuale") {
                returnColumn.renderCell = (row: any) =>
                    percentageRows(row.row.percentuale);
            } else if (column_keys[index] === "sob") {
                returnColumn.renderCell = (row: any) =>
                    percentageRows(row.row.sob);
            }

            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
