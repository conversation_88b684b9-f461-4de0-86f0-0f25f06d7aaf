import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import BaseGridList from "../../../models/BaseGridList";
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import GroupIcon from '@mui/icons-material/Group';
import GroupsIcon from '@mui/icons-material/Groups';
import FaceIcon from '@mui/icons-material/Face';
import { mapOtherList } from "../../common";
import { StatusBadge } from "@vapor/react-custom";
import { Stack } from "@mui/material";

export const getSoggettiGrid = async (t: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Denominazione"),
                t("C.F."),
                t("Partita iva"),
                t("Contatti"),
                t("Relazione"),
                t("Accesso"),
            ],
            column_keys: [
                "name",
                "codicefiscale",
                "partitaiva",
                "telephone",
                "relazione",
                "id_riserva",
            ],
            column_widths: ["20%", "15%", "20%", "15%", "20%","10%"],
            sortable: [false, false, false, false, false,false],
            cell_templates: [null, null, null, null, null,null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapSoggettiGridColumnNames(response, t);
};

export const nameFunction = (row: any,t:any) => {
    let icon = null;
    let additionalLabel: JSX.Element | null = null;
    if (row.tableName === "anagrafiche") {
        icon = row.isExternal == 1 ? <FaceIcon /> : <GroupsIcon />;
    } else if (row.tableName === "utente") {
        icon = <GroupIcon />;
    } else if (row.tableName === "gruppo") {
        icon = <AccountBalanceIcon />;
    }

       if (row.isIntestatario === "1") {
        additionalLabel = (
            <StatusBadge
                bgColor={"#8CC63F"}
                style={{ width: "100%" }} 
                label={t("Intest.")}
            />
               
        );
    }

    return (
        <Stack direction="row" alignItems="center" gap={1}>
            {icon}
            {row.name}
            {additionalLabel}
        </Stack>
    );
};

export const relazioneFunction = (row: any) => {
    const { relazione, roleName, tableName } = row;

    if (roleName && roleName !== relazione && relazione !== "Cointestatario") {
        if (relazione === "Cointestatario con Accesso") {
            return (
                <Stack  style={{ display: "inline" }}>
                    <StatusBadge bgColor="green" label="Cointestatario" />
                    {" / " + roleName}
                </Stack>
            );
        } else if (relazione.includes("Con Accesso")) {
            const relazioneParts = relazione.split("Con Accesso");
            return <>{relazioneParts[0] + " / " + roleName}</>;
        }
        return <>{relazione + " / " + roleName}</>;
    }

    if (relazione === "Accesso Riservato") {
        return tableName !== "gruppo" ? "Utente Interno" : "Gruppo Interno";
    }

    if (relazione === "Cointestatario" && tableName === "utente") {
        return <StatusBadge bgColor="green" label={relazione} />;
    }

    if (relazione === "Responsabile" && tableName === "utente") {
        return <StatusBadge bgColor="yellow" label={relazione} />;
    }

    if (relazione === "Cointestatario" && tableName === "anagrafiche") {
        return roleName && roleName !== relazione ? (
            <Stack  style={{ display: "inline" }}>
                <StatusBadge bgColor="green" label={relazione} />
                {" / " + roleName}
            </Stack>
        ) : (
            <StatusBadge bgColor="green" label={relazione} />
        );
    }

    if (relazione === "Cointestatario con Accesso") {
        return <StatusBadge bgColor="green" label="Cointestatario" />;
    }

    if (relazione.includes("Con Accesso")) {
        const relazioneParts = relazione.split("Con Accesso");
        return <>{relazioneParts[0]}</>;
    }

    if (relazione.includes("Con Riserva")) {
        const relazioneParts = relazione.split("Con Riserva");
        if (relazioneParts[0] === "Cointestatario") {
            return <StatusBadge bgColor="green" label="Cointestatario" />;
        } else if (relazioneParts[0] === "Responsabile") {
            return <StatusBadge bgColor="yellow" label="Responsabile" />;
        }
        return <>{relazioneParts[0]}</>;
    }

    return <>{relazione}</>;
};

export const accessoFunction = (row: any,t:any) => {

    if (row.riserva === 1 || row.riserva === "1") {
        return <StatusBadge bgColor="blue" label={t("Accesso Riservato")} />;
    } else if (row.accessExternal === 1 || row.accessExternal === "1") {
        return <StatusBadge bgColor="green" label={t("Accesso Esterno")} />;
    }
    return null;
};

export const mapSoggettiGridColumnNames = (response: any, t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];

            if (column_keys[index] === "name") {
                returnColumn.renderCell = (row: any) => nameFunction(row.row,t);
            } else if (column_keys[index] === "relazione") {
                returnColumn.renderCell = (row: any) =>
                    relazioneFunction(row.row);
            } else if (column_keys[index] === "id_riserva") {
                returnColumn.renderCell = (row: any) =>
                    accessoFunction(row.row, t);
            }

            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
