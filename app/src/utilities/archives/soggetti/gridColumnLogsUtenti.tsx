import BaseGridList from "../../../models/BaseGridList";
import { mapOtherColumnNames } from "../../common";


export const getLogsUtentiGrid = async (t: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Utente"),
                t("Data Movimento"),
                t("Tipo Movimento"),
              
            ],
            column_keys: [
                "nome_utente",
                "reg_date",
                "tipo_action",
               
            ],
            column_widths: ["35%", "35%", "30%"],
            sortable: [true, true, true],
            cell_templates: [null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};





