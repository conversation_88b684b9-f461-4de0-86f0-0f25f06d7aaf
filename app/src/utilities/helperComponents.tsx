import React, { useState } from "react";
import {
    Box,
    Button,
    Grid,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Checkbox,
    Stack,
    Switch,
    IconButton,
    Menu,
    MenuItem,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { StatusBadge, Tag } from "@vapor/react-custom";
import SendIcon from "@mui/icons-material/Send";
import DeleteIcon from "@mui/icons-material/Delete";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircle } from "@fortawesome/free-solid-svg-icons";
import { faEye } from "@fortawesome/free-regular-svg-icons";
import { faDownload } from "@fortawesome/pro-regular-svg-icons";
import {
    MoneyOff,
    AttachMoney,
    FileDownload,
    InsertDriveFile,
    Info,
} from "@mui/icons-material";
import { Chip } from "@vapor/react-material";
//import icons
import * as icons from "@mui/icons-material";
import { TrashAlt, WindowMaximize } from "@vapor/react-icons";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import FolderOpenIcon from "@mui/icons-material/FolderOpen";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import HomeIcon from "@mui/icons-material/Home";
import CheckIcon from "@mui/icons-material/Check";
import Trash from "@mui/icons-material/Delete";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import { mapOtherList } from "./common";
import { RiepilogoCreateLetterActions } from "../interfaces/fatturazione.interface";
import { AnagraficheActions, AnagrafichePraticheAction } from "../interfaces/anagrafiche.interface";
import { faPenToSquare } from "@fortawesome/free-solid-svg-icons";
import { ImmobiliActions } from "../interfaces/immobili.interface";

export const mapOtherColumnNames = (response: any) => {
    const { column_names, column_keys, sortable, column_widths } =
        mapOtherList(response);
    let columns = column_names.map((cln: any, index: any) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            return {
                name: cln,
                selector: (row: any) => row[column_keys[index]],
                sortField: column_keys[index],
                sortable: sortable[index],
            };
        }
    });
    return columns;
};

export const defineStartParamsColumn = (data: any, t?: any) => {
    let { row } = data;
    return (
        <div className="mt-1 mb-1">
            <div>
                <b>{t("Ambito")}: </b> {row.ambito}
            </div>
            <div>
                <b>{t("Ricerca per")}: </b> {row.dataType}
            </div>
            <div>
                <b>{t("Testo inserito")}: </b> {row.data}
            </div>
        </div>
    );
};

export const defineActionButtonsInfocamere = (
    data: any,
    downloadContent: (id: string) => void,
    saveToPractice: (state: boolean, id: string) => void,
    t?: any
) => {
    let { row } = data;
    return (
        <Box display="flex" flexWrap="wrap" component="form" gap={2}>
            <Grid item>
                <Button
                    onClick={() => downloadContent(row.id)}
                    variant="outlined"
                    size="small"
                    startIcon={<FileDownload />}
                >
                    <Typography> {t("Scarica")} </Typography>
                </Button>
            </Grid>
            <Grid item>
                <Button
                    onClick={() => saveToPractice(true, row.id)}
                    variant="outlined"
                    size="small"
                    startIcon={<InsertDriveFile />}
                >
                    <Typography> {t("Salva nella pratica")} </Typography>
                </Button>
            </Grid>
        </Box>
    );
};

export const defineIsDownloadedInfocamere = (data: any, t?: any) => {
    let { row } = data;
    let bgColor: string = "";
    let label: string = "";
    let dateTime: string = row.download_date
        ? t("il ") + row.download_date + t(" alle ") + row.download_hour
        : "";

    switch (row.is_downloaded) {
        case "0":
            label = t("Da scaricare");
            bgColor = "yellow";
            break;
        case "1":
            label = t("Scaricato");
            bgColor = "green";
            break;
        case "2":
            label = t("Errore");
            bgColor = "red";
            break;
    }
    return (
        <>
            <StatusBadge bgColor={bgColor} label={label} />
            {dateTime}
        </>
    );
};

export const defineIsPaidInfocamere = (data: any, t?: any) => {
    let { row } = data;
    let bgColor: string = "";
    let label: string = "";
    let icon: any;

    switch (row.is_downloaded) {
        case "0":
            label = t("Non pagato");
            bgColor = "red";
            icon = <MoneyOff />;
            break;
        case "1":
            label = t("Pagato");
            bgColor = "green";
            icon = <AttachMoney />;
            break;
    }
    return (
        <>
            <>
                <StatusBadge icon={icon} bgColor={bgColor} label={label} />
            </>
        </>
    );
};

export const defineUserInternalStatus = (data: any) => {
    let { row } = data;
    switch (row.stato) {
        case "1":
            return (
                <Tag label="Attivo" variant="standard" type="islamicGreen" />
            );
        case "2":
            return (
                <Tag label="Disattivato" variant="standard" type="rustyRed" />
            );
    }
};

export const qualificaUserField = (data: any) => {
    let { row } = data;
    return (
        <div style={{ display: "flex", alignItems: "center" }}>
            <div>{row["qualifica"]}</div>
            <div style={{ marginLeft: 5 }}>
                {" "}
                {row["commercialista"] === "1" ? <FolderOpenIcon /> : ""}
            </div>
        </div>
    );
};

export const baseTextField = (condition: any, t: any, label?: string) => {
    return condition ? (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "#cd485c" }} />}
                label={label === "active" ? t("NonActive") : t("No")}
            />
        </>
    ) : (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "green" }} />}
                label={label === "active" ? t("Active") : t("Si")}
            />
        </>
    );
};
export const cambioCurrencyField = (data: any) => {
    let { row } = data;
    return (
        <>
            1 Euro (€) = {row["cambio"]}({row["simbolo"]})
        </>
    );
};

const getMuiIcon = (iconText: string) => {
    const icons: any = {
        "fas fa-file": <InsertDriveFileIcon />,
        "fas fa-home": <HomeIcon />,
        "fas fa-briefcase": <BusinessCenterIcon />,
    };

    return icons[iconText] || "";
};

export const iconFatturazioneFields = (
    condition?: any,
    icon?: string,
    tooltip?: any
) => {
    return condition ? (
        <Tooltip title={tooltip} style={{ fontSize: "inherit" }}>
            {icon ? getMuiIcon(icon) : ""}
        </Tooltip>
    ) : (
        ""
    );
};

export const tipoValidation = (tippo: string, advance: string) => {
    let type = "Fattura";
    let type_short = "F";
    if (parseInt(tippo) === 1) {
        type = "Nota di credito";
        type_short = "NC";
    }
    if (parseInt(tippo) === 2) {
        type = "Preavviso di parcella";
        type_short = "PC";
    }
    if (parseInt(tippo) === 3) {
        type += " elettronica (PA)";
        type_short = "FE (PA)";
    }
    if (parseInt(tippo) === 4) {
        type = "Nota di credito (PA)";
        type_short = "NC";
    }
    if (parseInt(tippo) === 5) {
        type += " d' acquisto";
        type_short = "FA";
    }
    if (parseInt(tippo) === 6) {
        type += " elettronica (B2B)";
        type_short = "FE (B2B)";
    }
    if (parseInt(tippo) === 7) {
        type = "Nota di credito (B2B)";
        type_short = "NC (B2B)";
    }
    if (parseInt(tippo) === 8) {
        type = "Nota di debito";
        type_short = "ND";
    }
    if (advance === "1") {
        type_short += " - Acconto";
        type += " - Acconto";
    }
    return (
        <Tooltip title={type}>
            <span>{type_short}</span>
        </Tooltip>
    );
};

export const scoperto = (tippo: string, scoperto: string) => {
    if (tippo === "1" || tippo === "2" || tippo === "4" || tippo === "7") {
        return <div className="text-right">-</div>;
    } else {
        return (
            <div
                className="text-right"
                style={{ color: parseInt(scoperto) > 0 ? "red" : "" }}
            >
                {scoperto}
            </div>
        );
    }
};

export const showLogoField = (data: any) => {
    let { row } = data;
    return (
        <>
            {row["use_as_logo"] === "1" ? (
                <icons.Image
                    style={{
                        color: "#636363",
                        width: 20,
                        height: 20,
                        marginBottom: -5,
                    }}
                />
            ) : (
                " "
            )}
        </>
    );
};
export const tipoValidationPaymentReminder = (data: any) => {
    let { row } = data;
    if (row["tipo"] === "0" || row["tipo"] === "3" || row["tipo"] === "6") {
        return "Fattura";
    }
    if (row["tipo"] === "1" || row["tipo"] === "4" || row["tipo"] === "7") {
        return "Nota di credito";
    }
    if (row["tipo"] === "2") {
        return "Preavviso di parcella";
    }
    if (row["tipo"] === "5") {
        return "Fattura d'acquisto";
    }

    return "";
};

export const fileNameRiepilogo = (data: any) => {
    let { row } = data;
    return (
        <a
            href={`/legacy/paymentreminder/download-file?id=${row.id}`}
            target="_blank"
        >
            <img src="/pdfIcon.png" alt="pdf" height={30} />
        </a>
    );
};

export const livelloRiepilogo = (data: any) => {
    let { row } = data;
    return `${row.level}°`;
};

export const statoRiepilogo = (data: any) => {
    let { row } = data;
    return row.sent === "1" && row.recipient ? (
        <>
            <StatusBadge color="success" label="Inviata" /> a {row.recipient} il{" "}
            {row.sentDate} alle {row.sentHour}
        </>
    ) : (
        <StatusBadge color="warning" label="Da inviare" />
    );
};

export const actionButtonsRiepilogo = (
    data: any,
    actions: RiepilogoCreateLetterActions
) => {
    let { row } = data;
    const { handleDelete, handleSend } = actions;

    return (
        <Box
            display="flex"
            flexWrap="wrap"
            component="form"
            gap={2}
            style={{ display: "flex", justifyContent: "end" }}
        >
            {row.sent === "0" && (
                <Grid item>
                    <Button
                        onClick={() => handleSend(row)}
                        variant="outlined"
                        size="small"
                        startIcon={<SendIcon />}
                    >
                        <Typography> Invia </Typography>
                    </Button>
                </Grid>
            )}

            <Grid item>
                <Button
                    onClick={() => {
                        handleDelete(row.id);
                    }}
                    variant="outlined"
                    color="error"
                    size="small"
                    startIcon={<DeleteIcon />}
                >
                    <Typography> Elimina </Typography>
                </Button>
            </Grid>
        </Box>
    );
};

export const defineDelegateDeleteButtons = (
    data: any,
    setOpenConfimModalReferents: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >,
    t?: any
) => {
    let { row } = data;
    return (
        <Button
            color="error"
            variant="outlined"
            onClick={() =>
                setOpenConfimModalReferents({ state: true, id: row.id })
            }
        >
            {t("Elimina")}
        </Button>
    );
};

export const defineReservesDeleteButtons = (
    data: any,
    setOpenConfimModalReserves: React.Dispatch<
        React.SetStateAction<{ state: boolean; id: string }>
    >,
    t?: any
) => {
    let { row } = data;
    return (
        <Button
            color="error"
            variant="outlined"
            onClick={() =>
                setOpenConfimModalReserves({ state: true, id: row.id })
            }
        >
            {t("Elimina")}
        </Button>
    );
};

export const actionButtonsAnagrafiche = (
    data: any,
    actions: AnagraficheActions
) => {
    let { row } = data;
    const { handleDelete, handleDrawer } = actions;

    return (
        <Box
            display="flex"
            flexWrap="wrap"
            component="form"
            gap={2}
            style={{
                display: "flex",
                justifyContent: "end",
                marginLeft: "auto",
            }}
        >
            <Grid item style={{ transform: "rotate(90deg)" }}>
                <IconButton
                    name="trash"
                    onClick={(e: any) => {
                        e.stopPropagation();
                        handleDrawer(row);
                    }}
                >
                    <WindowMaximize color="interactive" />
                </IconButton>
            </Grid>
            <Grid item>
                <IconButton
                    name="trash"
                    onClick={(e: any) => {
                        e.stopPropagation();
                        handleDelete(row);
                    }}
                >
                    <TrashAlt color="interactive" />
                </IconButton>
            </Grid>
        </Box>
    );
};

export const actionButtonsAnagrafichePratiche = (
    data: any,
    action: AnagrafichePraticheAction
) => {
    let { row } = data;

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
        event.stopPropagation();
    };

    const handleCloseMenu = (event: React.MouseEvent) => {
        setAnchorEl(null);
        event.stopPropagation();
    };

    const onShowRelations = (event: React.MouseEvent) => {

        const anyAction = action as any;
        if (anyAction.handleShowRelations && typeof anyAction.handleShowRelations === 'function') {
            anyAction.handleShowRelations(row);
        }
        setAnchorEl(null);
        event.stopPropagation();
    };

    return (
        <Box
            display="flex"
            flexWrap="wrap"
            component="form"
            gap={2}
            style={{
                display: "flex",
                justifyContent: "end",
                marginLeft: "auto",
            }}
        >
            <Grid item >
                <IconButton
                    name="relationships"
                    onClick={handleOpenMenu}
                >
                    <ArrowDropDownIcon />
                </IconButton>
                <Menu
                    id="actions-menu"
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={handleCloseMenu}
                >
                    <MenuItem onClick={onShowRelations}>
                        Mostra relazioni
                    </MenuItem>
                </Menu>
            </Grid>
        </Box>
    );
};
export const calendarNetlex = (data: any, t: any) => {
    let { row } = data;
    return row["calendario_netlex"] == "1" ? (
        <b>{t("Udienze")}</b>
    ) : (
        <b>{t("Impegni")}</b>
    );
};

export const statoCalendarAccount = (data: any) => {
    let { row } = data;
    return row["stato"] === "1" ? "Attivo" : "Non attivo";
};

export const filterCalendario = (data: any) => {
    let { row } = data;
    return (
        <div>
            {row["filtro_calendario"] === "1"
                ? "Personale"
                : row["filtro_calendario"] === "2"
                    ? "Generale"
                    : ""}
        </div>
    );
};

export const tipoAccountValidation = (
    data: any,
    CLIENT_GOOGLE?: any,
    CLIENT_MICROSOFT?: any
) => {
    let { row } = data;
    if (parseInt(row["tipo_account"]) === 1) {
        return (
            <>
                <span>
                    <img
                        src={CLIENT_GOOGLE}
                        style={{ width: "75px", height: "25px" }}
                    />
                </span>
            </>
        );
    }
    if (parseInt(row["tipo_account"]) === 2) {
        return (
            <span>
                <img
                    src={CLIENT_MICROSOFT}
                    style={{ width: "75px", height: "25px" }}
                />
            </span>
        );
    }
};

export const cervedBusinessName = (data: any) => {
    let { result } = data;
    if (result.subject_type == "FOREIGN")
        return result.company_info.business_name;
    else if (result.subject_type == "PERSON") return result.person_info.name;
    else return result.company_info.business_name;
};
export const cervedSubjectType = (data: any) => {
    let { result } = data;
    let returnStg = "";
    if (result.subject_type == "COMPANY") returnStg = "Società";
    else if (result.subject_type == "INDIVIDUAL_COMPANY")
        returnStg = "Ditta individuale";
    else if (result.subject_type == "PERSON") returnStg = "Persona fisica";
    else if (result.subject_type == "FOREIGN")
        returnStg = "Società non italiana";
    else returnStg = "Altro";
    return returnStg;
};
export const cervedDescription1 = (data: any) => {
    let { result } = data;
    if (result.company_info?.activity_status?.description)
        return result.company_info?.activity_status?.description;
    else return "-";
};
export const cervedTaxcode = (data: any) => {
    let { result } = data;
    if (result.tax_code) return result.tax_code;
    else return "-";
};
export const cervedReaNumber = (data: any) => {
    let { result } = data;
    if (result.company_info?.rea_code?.rea_number)
        return result.company_info?.rea_code?.rea_number;
    else return "-";
};
export const cervedDescription2 = (data: any) => {
    let { result } = data;
    let returnStrg = "";
    if (result.address?.street?.description)
        returnStrg = result.address.street.description;
    if (result.address?.city?.description)
        returnStrg += "- " + result.address.city.description;
    if (result.address?.province?.code)
        returnStrg +=
            "(" +
            result.address.province.code +
            ") CAP" +
            result.address.postal_code;
    else returnStrg = "-";
    return returnStrg;
};
export const cervedCode = (data: any) => {
    let { result } = data;
    if (result.company_info.economic_activity?.ateco?.description)
        return result.company_info.economic_activity.ateco?.description;
    else return "-";
};
export const cervedSubjectId = (data: any, t: any, actions: any) => {
    let { result } = data;
    return (
        <Box display="flex" flexWrap="wrap" component="form" gap={2}>
            <Grid item>
                <Button
                    type="button"
                    onClick={() => actions(result)}
                    variant="outlined"
                    size="small"
                    startIcon={<Info />}
                >
                    <Typography> {t("Apri")} </Typography>
                </Button>
            </Grid>
        </Box>
    );
};

export const getLoanPeriod = (data: any) => {
    let { row } = data;
    return `${row.data_inizio} - ${row.data_fine}`;
};

export const actionCalendarAccounts = (
    data: any,
    t: any,
    ScollegaCalendarAccountsDialog: any,
    CollegaCalendarAccountsDialog: any,
    setLoading: any
) => {
    let { row } = data;
    return row["stato"] === "1" ? (
        <>
            <ScollegaCalendarAccountsDialog
                label={t("Scollega")}
                id={row["uniqueid"]}
                accountType={row["tipo_account"]}
                setLoading={setLoading}
            />
        </>
    ) : (
        <>
            <CollegaCalendarAccountsDialog
                label={t("Collega")}
                id={row["uniqueid"]}
                accountType={row["tipo_account"]}
                calendarNetlex={row["calendario_netlex"]}
                setLoading={setLoading}
            />
        </>
    );
};
export const preferitaAuthoritaField = (data: any) => {
    let { row } = data;
    return row["preferita"] === "1" ? (
        <CheckIcon style={{ color: "green" }} />
    ) : (
        ""
    );
};

export const changeLabelName = (data: any, t: any) => {
    let { row } = data;
    return (
        <>
            {row === "1"
                ? t("Indirizzo")
                : row === "2"
                    ? t("Contatto")
                    : t("Email")}
        </>
    );
};

export const addasterisk = (key: any) => {
    return key === "0" ? "" : "*";
};
export const addDefaultText = (data: any) => {
    let { row } = data;
    return (
        <>
            {row.nome}
            {row.is_default === "1" ? <Chip label={"Default"} /> : ""}
        </>
    );
};

export const sectionalValore = (data: any) => {
    let { row } = data;
    var str = "";
    switch (parseInt(row.tipo)) {
        // case 1:
        // 	str = "2020"
        // 	break;
        case 2:
            str = "2020-Bis";
            break;
        case 3:
            str = "2020/" + row.valore;
            break;
        case 4:
            str = "20" + row.valore;
            break;
    }
    return str;
};
export const sectionalTipo = (data: any) => {
    let { row } = data;
    var str = "";
    switch (parseInt(row.tipo)) {
        case 1:
            str = "YYYY";
            break;
        case 2:
            str = "YYYY-Bis";
            break;
        case 3:
            str = "YYYY/T";
            break;
        case 4:
            str = "YYT";
            break;
    }
    return str;
};
export const expendituresTipo = (data: any) => {
    let { row } = data;
    var typeStr = "Spesa imponibile";
    if (row.tipo === 3) {
        typeStr = "Spesa esente";
    } else if (row.tipo === 4) {
        typeStr = "Spesa esclusa";
    }
    return typeStr;
};

export const deadlinestypesColor = (data: any) => {
    let { row } = data;
    return (
        <>
            <StatusBadge
                bgColor={row.color}
                style={{ width: "-webkit-fill-available" }}
            />
        </>
    );
};
export const codiceArchiveField = (data: any) => {
    let { row } = data;
    if (!row["codicearchivio"]) {
        return null;
    }

    return (
        <Chip
            label={row["codicearchivio"]}
            style={{
                backgroundColor: row["color"],
                color: row["font_color"],
            }}
        />
    );
};

export const downloadAntirecregister = (
    data: any,
    downloadContent: any,
    t: any
) => {
    let { row } = data;
    return (
        <Button
            onClick={() => downloadContent(row.id)}
            variant="outlined"
            size="small"
            startIcon={<FileDownload />}
        >
            <Typography> {t("Scarica file")} </Typography>
        </Button>
    );
};
export const deleteAntirecregister = (
    data: any,
    deleteContent: any,
    t: any
) => {
    let { row } = data;
    return (
        <Button
            onClick={() => deleteContent(row.id)}
            variant="outlined"
            color="error"
            size="small"
            startIcon={<Trash />}
        >
            <Typography> {t("Elimina")} </Typography>
        </Button>
    );
};
export const allNetlexTemplateField = (rowValue: any, t: any) => {
    if (rowValue === "0") {
        return (
            <>
                <Chip
                    icon={
                        <FiberManualRecordIcon style={{ color: "#cd485c" }} />
                    }
                    label={t("No")}
                />
            </>
        );
    } else if (rowValue === "1") {
        return (
            <>
                <Chip
                    icon={<FiberManualRecordIcon style={{ color: "Gray" }} />}
                    label={t("yes") + " - " + t("in attesa di approvazione")}
                />
            </>
        );
    } else if (rowValue === "2") {
        return (
            <>
                <Chip
                    icon={<FiberManualRecordIcon style={{ color: "Green" }} />}
                    label={t("yes")}
                />
            </>
        );
    }
};

export const colorProfitsDistribuitionField = (data: any) => {
    let { row } = data;
    return (
        <>
            <StatusBadge
                bgColor={row.colore}
                style={{ width: "-webkit-fill-available" }}
            />
        </>
    );
};
//EMAILMODELLI
export const idSezioneField = (data: any, t: any) => {
    let { row } = data;
    return row["id_sezione"] === "1" ? t("Interna alla pratica") : "";
};

export const visuraDelegation = (data: any, setSelectedCheckbox: any) => {
    let { row } = data;
    return (
        <Checkbox
            checked={row.visura === "1"}
            onChange={() =>
                setSelectedCheckbox({
                    masterId: row.masterId,
                    slaveId: row.slaveId,
                    type: "visura",
                    checked: row.visura !== "1",
                })
            }
        ></Checkbox>
    );
};

export const conservazioneDelegation = (
    data: any,
    setSelectedCheckbox: any
) => {
    let { row } = data;
    return (
        <Checkbox
            checked={row.conservazione === "1"}
            onChange={() =>
                setSelectedCheckbox({
                    masterId: row.masterId,
                    slaveId: row.slaveId,
                    type: "conservazione",
                    checked: row.conservazione !== "1",
                })
            }
        ></Checkbox>
    );
};

export const fatturazioneDelegation = (data: any, setSelectedCheckbox: any) => {
    let { row } = data;
    return (
        <Checkbox
            checked={row.fatturazione === "1"}
            onChange={() =>
                setSelectedCheckbox({
                    masterId: row.masterId,
                    slaveId: row.slaveId,
                    type: "fatturazione",
                    checked: row.fatturazione !== "1",
                })
            }
        ></Checkbox>
    );
};

export const deletedDynamicField = (data: any, deleteAction: any) => {
    let { row } = data;
    if (row.deleted === "1")
        return (
            <Stack direction="row" alignItems="center" gap={1}>
                <Switch
                    checked={row.deleted === "1"}
                    onChange={() => deleteAction(row)}
                ></Switch>
            </Stack>
        );
    else return "NO";
};

export const assegnatoDynamicField = (data: any, t: any) => {
    let { row } = data;
    return (
        <Stack direction="row" alignItems="center" gap={1}>
            <FontAwesomeIcon
                icon={faCircle}
                color={row.assegnato ? "#8CC63F" : "#F7931E"}
            ></FontAwesomeIcon>
            <Typography>{row.assegnato ? t("Si") : t("No")}</Typography>
        </Stack>
    );
};

///AGENDA TIMESHEET

//unused function, commented to avoid typescript warnings in pipeline
// function pad(number: number): string {
//     return number < 10 ? "0" + number : number.toString();
// }

export function convertMinutesToHours(value: number | string | null | undefined): string {
    if (typeof value === "string") {
        // se è già “HH:MM”, restituisci così com’è
        if (value.includes(":")) {
            return value;
        }
        // altrimenti prova a interpretarlo come numero
        const parsed = parseInt(value, 10);
        if (isNaN(parsed)) {
            return "";
        }
        value = parsed;
    }
    // a questo punto value è number o null/undefined
    const minutes = typeof value === "number" && isFinite(value) ? value : 0;
    const hours = Math.floor(minutes / 60);
    const remaining = minutes % 60;
    const pad = (n: number) => (n < 10 ? "0" + n : n.toString());
    return `${pad(hours)}:${pad(remaining)}`;
}

export const getDurationCell = (row: any) => {
    if (row.type === "file") {
        return "";
    }
    if (row.status === "1") {
        return (
            <FiberManualRecordIcon
                style={{ color: "red" }}
                className="timer-icon blinking"
            />
        );
    } else {
        return convertMinutesToHours(row.duration);
    }
};

export const visibleTimesheetField = (
    condition: any,
    t: any,
    label?: string
) => {
    return condition ? (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "grey" }} />}
                label={label === "active" ? t("NonActive") : t("No")}
            />
        </>
    ) : (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "orange" }} />}
                label={label === "active" ? t("Active") : t("Yes")}
            />
        </>
    );
};

export const calculateTariffaOraria = (row: any) => {
    if (row.type === "file") {
        return "";
    }
    return row.tariffa_oraria;
};

export const calculateTariffaTotale = (row: any) => {
    if (row.type === "file") {
        return "";
    }

    return `€ ${row.tariffa_totale}`;
};

export const calculateCostoOrario = (row: any) => {
    if (row.type === "file") {
        return "";
    }
    if (row.tariffaEffettivaTotale != null && row.costoRisorsaTotale != null) {
        const result: string = parseFloat(row.CostoOrario).toFixed(2);
        return `€ ${result}`;
    }
};

export const calculateCostoRisorsa = (row: any) => {
    if (row.type === "file") {
        return "";
    }
    return row.costo_risorsa;
};

export const calculateMargin = (row: any) => {
    if (row.type === "file") {
        return "";
    }
    return `€ ${row.margin}`;
};

export const modificaTimesheetRow = (row: any, navigate: any) => {
    return (
        <FontAwesomeIcon
            icon={faPenToSquare}
            style={{ color: "hsl(200, 100%, 42%)" }}
            onClick={() => navigate(`/timesheet/update/${row.id}`)}
        />
    );
};

//AGENDA LEGALE

export const baseTextLabelField = (
    condition: any,
    label1: string,
    label2: string
) => {
    return condition ? (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "#cd485c" }} />}
                label={label1}
            />
        </>
    ) : (
        <>
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "green" }} />}
                label={label2}
            />
        </>
    );
};

export const statusUdienzaField = (row: any, t: any) => {


    if (row === "2" || row === 2) {
        return (<Chip
            icon={<FiberManualRecordIcon style={{ color: "grey" }} />}
            label={t("Non evadere")}
        />);
    } else if (row === "1" || row === 1) {
        return (
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "green" }} />}
                label={t("Evasa")}
            />
        );
    } else if (row === "0" || row === 0) {
        return (
            <Chip
                icon={<FiberManualRecordIcon style={{ color: "#cd485c" }} />}
                label={t("Non evasa")}
            />
        );
    }
}

export const ImpegniCheckBox = ({
    row,
    selectedRows,
}: {
    row: any;
    selectedRows: string[];
}) => {
    return <Checkbox checked={selectedRows.includes(row.id)} />;
};

// Documenti Field
export const documentiIconField = ({
    row,
    handlePrintDocumenti,
}: {
    row: any;
    handlePrintDocumenti: any;
}) => {
    const navigate = useNavigate();
    return (
        <Box
            sx={{ width: 50, display: "flex", justifyContent: "space-between" }}
        >
            <FontAwesomeIcon
                icon={faDownload}
                style={{ color: "hsl(200, 100%, 42%)" }}
                onClick={() =>
                    handlePrintDocumenti(row?.row?.id, row?.row?.nomefile)
                }
            />
            <FontAwesomeIcon
                icon={faEye}
                style={{ color: "hsl(200, 100%, 42%)" }}
                onClick={() =>
                    navigate(
                        `/legacy/anagrafiche/update-file?id=${row?.row?.id}`
                    )
                }
            />
        </Box>
    );
};
//Immobili Anagrafiche
export const actionImmobiliButtons = (data: any, actions: ImmobiliActions) => {
    let { row } = data;
    const { handleDelete, handleEdit } = actions;

    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                gap: 1,
                width: "100%"
            }}
        >
            <FontAwesomeIcon
                icon={faPenToSquare}
                style={{ color: "hsl(200, 100%, 42%)", padding: "10px" }}
                onClick={handleEdit.bind(null, row)}
            />
            <IconButton onClick={handleDelete.bind(null, row)} size="small" style={{ padding: "10px" }}>
                <TrashAlt color="interactive" />
            </IconButton>
        </Box>
    );
};
