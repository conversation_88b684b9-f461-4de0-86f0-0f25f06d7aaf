import { IGridColumn } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames } from "../common";

export const getDashboardTimesheetGrid = async (
    t?: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Pratica"),
                t("Nome Pratica"),
                t("Oggetto"),
                t("Ore Totali"),
                t("Numero"),
                t("Risorse"),
                t("Costo"),
            ],
            column_keys: [
                "pratica",
                "listaclienticontroparti",
                "oggetto",
                "durata",
                "eventi",
                "risorse",
                "costo",
            ],
            column_widths: ["10%", "10%", "10%", "10%", "20%", "20%", "20%"],
            cell_templates: [null, null, null, null, null, null, null],
            sortable: [true, false, true, true, true, true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};
