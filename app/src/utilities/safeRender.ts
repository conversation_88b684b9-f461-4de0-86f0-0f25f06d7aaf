/**
 * Rende sicura la lettura di un campo riga.
 * Se il valore è un oggetto prova a estrarre `label | value`,
 * altrimenti restituisce stringa vuota se nullo/undefined.
 */
export const safeRenderCell =
  (field: string) =>
  (params: any): string => {
    const v = params?.row?.[field];
    if (v == null) return "";
    if (typeof v === "object") return v.label ?? v.value ?? "";
    return v;
  };