import {
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { StatusBadge, Tag } from "@vapor/react-custom";
import { Typography } from "@vapor/react-material";
import { GridColDef } from "@mui/x-data-grid-pro";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationCircle } from "@fortawesome/free-solid-svg-icons";

export const getDeadlinesGrid = async (t: any): Promise<GridColDef[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Oggetto"),
                t("Data ora"),
                t("Tipo"),
                t("Intestatari"),
                t("Stato"),
                // t("Compensi"),
                // t("Imponibili"),
                // t("Esenti"),
                // t("Escluse"),
            ],
            column_keys: [
                "testo",
                "data",
                "tiposcadenzaNome",
                "users",
                "status",
                // "onoraridiritti",
                // "speseimponibili",
                // "speseesenti",
                // "speseescluse",
            ],
            column_widths: [
                "30%",
                "10%",
                "15%",
                "14%",
                "7%",
                "7%",
                "7%",
                "7%",
                "7%",
            ],
            sortable: [
                true,
                true,
                true,
                true,
                true,
                true,
                true,
                true,
                true,
            ],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDeadlinesColumnNames(response, t);
};

export const mapDeadlinesColumnNames = (
  response: any,
  t?: any
): GridColDef[] => {
  const {
    column_names,
    column_keys,
    sortable,
    column_widths,
  }: IGridSettings = mapOtherList(response);

  return column_names
    .map((cln, index) => {
      if (!column_widths?.[index] || column_widths[index] === "0%") {
        return undefined;

      }

      const key = column_keys[index];
      const widthPercent = parseInt(column_widths[index], 10);
      const col: GridColDef = {
        field: key,
        headerName: cln,
        flex: Math.round((widthPercent / 100) * 100) / 100,
        sortable: sortable[index],
        hideable: true,
      };

      if (column_keys[index] === "testo") {
        col.valueGetter = (params: any) =>
          params.row && typeof params.row.testo === "string" ? params.row.testo : "";
      
        col.renderCell = (params: any) => {
          const row = params.row || {};
          return (
            <div style={{ display: "flex", alignItems: "center" }}>
              {row.important === "1" ? (
                <FontAwesomeIcon
                  icon={faExclamationCircle}
                  style={{ color: "#d32f2f", marginRight: 8 }}
                />
              ) : (
                <div style={{ width: 16, marginRight: 8 }} />
              )}
              <span>{params.value}</span>
            </div>
          );
        };
      }
      else if (key === "status") {
        col.renderCell = params => {
          switch (params.value) {
            case "0":
              return <StatusBadge bgColor="red" label={t("Non evaso")} />;
            case "1":
              return <StatusBadge bgColor="green" label={t("Evaso")} />;
            case "2":
              return <StatusBadge bgColor="gray" label={t("Non evadere")} />;
            case "3":
              return <StatusBadge bgColor="orange" label={t("Vicino")} />;
            case "4":
              return params.row?.evasa === "1" ? (
                <Tag
                  label={t("Polisweb")}
                  variant="standard"
                  type="islamicGreen"
                />
              ) : (
                <StatusBadge bgColor="lightgray" label={t("Polisweb")} />
              );
            default:
              return null;
          }
        };
      }
      else if (key === "data") {
        col.valueGetter = (params: any) => {
          const date = params.row?.data;
          if (date) {
            try {
              // Parse DD/MM/YYYY HH:MM format
              const dateTimeMatch = date.match(/^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/);
              if (dateTimeMatch) {
                const [, day, month, year, hour, minute] = dateTimeMatch;
                const parsedDate = new Date(
                  parseInt(year, 10),
                  parseInt(month, 10) - 1, // Month is 0-indexed
                  parseInt(day, 10),
                  parseInt(hour, 10),
                  parseInt(minute, 10)
                );
                return parsedDate.toLocaleDateString("it-IT", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                  hour: "2-digit",
                  minute: "2-digit"
                });
              }
              // Fallback to original date string if parsing fails
              return date;
            } catch {
              return date;
            }
          }
          return "";
        };
      }
      else if (
        ["onoraridiritti","speseimponibili","speseesenti","speseescluse"].includes(
          key
        )
      ) {
        col.type = "number";
        col.align = "right";
        col.headerAlign = "right";
        col.renderCell = params => {
          const v = parseFloat(params.value) || 0;
          return (
            <Typography align="right" variant="body2">
              €{number_format(v, 2, ",", ".")}
            </Typography>
          );
        };
      }

      return col;
    })
    .filter((c): c is GridColDef => !!c);
};

const number_format = (
    number: any,
    decimals: number,
    dec_point: string,
    thousands_sep: string
) => {
    const n = !isFinite(+number) ? 0 : +number;
    const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
    const sep = typeof thousands_sep === "undefined" ? "," : thousands_sep;
    const dec = typeof dec_point === "undefined" ? "." : dec_point;

    const toFixedFix = function (n: number, prec: number) {
        const k = Math.pow(10, prec);
        return "" + Math.round(n * k) / k;
    };

    let s: any = (prec ? toFixedFix(n, prec) : "" + Math.round(n)).split(".");
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || "").length < prec) {
        s[1] = s[1] || "";
        s[1] += new Array(prec - s[1].length + 1).join("0");
    }
    return s.join(dec);
};
