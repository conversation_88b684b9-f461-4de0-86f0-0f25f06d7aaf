import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";
import { colorProfitsDistribuitionField } from "../helperComponents";

export const getProfitDistribuitionGrid = async (
    t: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome"),
                t("Descrizione"),
                t("Percentuale sugli utili"),
                t("Colore"),
            ],
            column_keys: ["nome", "descrizione", "percentuale", "color"],
            column_widths: ["20%", "20%", "20%", "30%"],
            sortable: [true, true, true, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapProfitsDistribuitionColumnNames(response);
};

export const mapProfitsDistribuitionColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "color") {
                returnColumn.renderCell = (row: any) =>
                    colorProfitsDistribuitionField(row);
            } else {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
