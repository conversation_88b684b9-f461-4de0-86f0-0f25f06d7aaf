import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherList } from "../common";

export const getLogPecGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data"),
                t("Inviata da"),
                t("Oggetto"),
                t("Mittente"),
                t("Destinatario"),
                t("Tipologia"),
            ],
            column_keys: [
                "data",
                "inviata_da",
                "oggetto",
                "mittente",
                "destinatario",
                "tipo",
            ],
            column_widths: ["10%", "15%", "20%", "15%", "20%", "20%"],
            sortable: [true, true, true, true, true, true],
            cell_templates: [null, null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapLogPecNames(response, t);
};

const mapLogPecNames = (response: any, _t: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_names
        .map((cln: string, index: number) => {
            if (column_widths !== undefined && column_widths[index] !== "0%") {
                const returnColumn: any = {
                    field: column_keys[index],
                    headerName: cln,
                    sortable: sortable[index],
                    flex:
                        Math.round(
                            (parseInt(column_widths[index]) / 100) * 1e2
                        ) / 1e2,
                };
                return returnColumn;
            }
            return undefined;
        })
        .filter((element: any) => element !== undefined);

    return columns as unknown as IGridColumn[];
};
