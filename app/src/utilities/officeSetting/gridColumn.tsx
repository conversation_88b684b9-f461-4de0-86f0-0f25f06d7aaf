import {
    qualificaUtenteField,
    idStudioDefault,
    activeListUtentiField,
} from "../../features/studio/officeSettings/helper";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames, mapOtherList } from "../common";

export const getOfficeSettingsGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Nome"),
                t("C.F."),
                t("P.IVA"),
                t("Disponibilità fatture"),
            ],
            column_keys: [
                "nome",
                "codice_fiscale",
                "partita_iva",
                "disponibilita",
            ],
            column_widths: ["20%", "20%", "20%", "10%"],
            sortable: [false, false, false, false],
            cell_templates: [null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapOtherColumnNames(response);
};

export const getListUtentiGrid = async (
    t: any,
    handleChange: any,
    isActive: any
): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Active"),
                t("Nome Utente"),
                t("Ruolo"),
                t("Referente"),
                t("Default Studio"),
            ],
            column_keys: [
                "active",
                "nome",
                "qualificautente",
                "referente",
                "id_studio_default",
            ],
            column_widths: ["20%", "20%", "20%", "10%"],
            sortable: [false, true, false, false, false],
            cell_templates: [null, null, null, null, null],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapListUtentiColumnNames(response, handleChange, isActive);
};

export const mapListUtentiColumnNames = (
    response: any,
    handleChange: any,
    isActive: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "qualificautente") {
                returnColumn.renderCell = (row: any) =>
                    qualificaUtenteField(row);
            } else if (column_keys[index] === "id_studio_default") {
                returnColumn.renderCell = (row: any) => idStudioDefault(row);
            } else if (column_keys[index] === "active") {
                returnColumn.renderCell = (row: any) =>
                    activeListUtentiField(row, handleChange, isActive);
            } else {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
