import BaseGridList from "../../models/BaseGridList";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import {
    iconFatturazioneFields,
    scoperto,
    tipoValidation,
} from "../helperComponents";

export const getFatturazioneGrids = async (
    data: any
): Promise<IGridColumn[]> => {
    let { column_names, column_keys, sortable, column_widths, cell_templates } =
        data.gridsSettings;

    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: JSON.parse(column_names),
            column_keys: JSON.parse(column_keys),
            column_widths: JSON.parse(column_widths),
            sortable: JSON.parse(sortable),
            cell_templates: JSON.parse(cell_templates),
        },
    });

    return mapFatturazioneColumnNames(response);
};

export const mapFatturazioneColumnNames = (response: any) => {
    const {
        column_names,
        column_keys,
        sortable,
        cell_templates,
        column_widths,
    }: IGridSettings = response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "rg") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return iconFatturazioneFields(
                        row["listaclienti"] != null,
                        "fas fa-file",
                        `Associato alla pratica con Codice: ${row["codicepratica"]} R.G.: ${row["rg"]}`
                    );
                };
            } else if (column_keys[index] === "office") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return iconFatturazioneFields(
                        row["office"] === "1",
                        "fas fa-home",
                        "Studio Associato"
                    );
                };
            } else if (column_keys[index] === "advance") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return iconFatturazioneFields(
                        row["advance"] === "1",
                        "fas fa-briefcase",
                        "Acconto"
                    );
                };
            } else if (column_keys[index] === "tipo") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return tipoValidation(row["tipo"], row["advance"]);
                };
            } else if (column_keys[index] === "scoperto") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return scoperto(row["tipo"], row["scoperto"]);
                };
            } else if (column_keys[index] === "importofatturato") {
                returnColumn.renderCell = (data: any) => {
                    let { row } = data;
                    return row["importofatturato"] + "/" + row["totale"];
                };
            } else if (
    cell_templates !== undefined &&
    cell_templates[index] !== "{{checkbox}}"
) {
    returnColumn.renderCell = (params: any) => {
        const value = params.row[returnColumn.field];
        if (typeof value === "object" && value !== null) {
            return value.label || "";
        }
        return value ?? "";
    };
}
            return returnColumn;
        }
    });
    console.log("columns11a", columns);
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
