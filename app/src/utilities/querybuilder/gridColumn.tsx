import { IGridColumn } from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";
import { mapOtherColumnNames } from "../common";

export const getQuerybuilderGrid = async (t?: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome"), t("Descrizione")],
            column_keys: ["nome", "descrizione"],
            column_widths: ["50%", "50%"],
            cell_templates: [null, null],
            sortable: [false, false],
            header_templates: "",
            column_totals: null
        }
    });
    return mapOtherColumnNames(response);
};

export const getQuerybuilderExecuteGrid = async (data: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: data,
            column_keys: data,
            column_widths: splitByWidth(data),
            cell_templates: [null],
            sortable: [false],
            header_templates: "",
            column_totals: null
        }
    });
    return mapOtherColumnNames(response);
};

const splitByWidth = (data: any) => {
    const totalPercentage = 100;
    const percentagePerElement = totalPercentage / data.length;

    const newArray = Array(data.length).fill(percentagePerElement);
    return newArray;
};
