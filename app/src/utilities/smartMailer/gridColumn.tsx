import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import BaseGridList from "../../models/BaseGridList";

export const getSmartMailerGrid = (t: any): IGridColumn[] => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome"), t("Indirizzo")],
            column_keys: ["nome", "indirizzo"],
            column_widths: ["50%", "50%"],
            cell_templates: [null, null],
            sortable: [true, true],
            header_templates: "",
            column_totals: null,
        },
    });

    return mapSmartMailerColumnNames(response);
};

export const mapSmartMailerColumnNames = (response: any) => {
    const { column_names, column_keys, sortable }: IGridSettings =
        response.gridsSettings;

    let columns = column_names.map((cln: string, index: number) => {
        return {
            field: column_keys[index],
            headerName: cln,
            valueGetter: (row: any) => {
                return row["value"];
            },
            flex: 1,
            sortable: sortable[index],
        };
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
