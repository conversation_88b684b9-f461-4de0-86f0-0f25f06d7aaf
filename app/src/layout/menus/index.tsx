import {
  FactCheck,
  Home,
  Groups,
  LibraryBooks,
  Euro,
  Email,
  AccountBalance,
  PeopleAlt,
  Build,
  AddShoppingCart,
  Newspaper,
  Article,
  NotificationsActive,
  Payment,
  LocalPostOffice,
  Handshake,
  FolderCopy
} from "@mui/icons-material";
import { ReactElement } from "react";

interface MenuItemTypes {
  key: string;
  label: string;
  sectionId?: number;
  isTitle?: boolean;
  icon?: ReactElement;
  url?: string;
  badge?: {
    variant: string;
    text: string;
  };
  parentKey?: string;
  target?: string;
  children?: MenuItemTypes[];
  onClickFunction?: () => void;
  closePopoverAfterClick?: boolean;
}

const mappedMenuIcons: {
  [key: string]: ReactElement;
} = {
  Dashboard: <Home />,
  Anagrafiche: <Groups />,
  Pratiche: <LibraryBooks />,
  Contratti: <FolderCopy />,
  Agenda: <FactCheck />,
  Fatturazione: <Euro />,
  Mailbox: <Email />,
  PCT: <AccountBalance />,
  Studio: <PeopleAlt />,
  Utilità: <Build />,
  Shop: <AddShoppingCart />,
  News: <Newspaper />,
  Documenti: <Article />,
  Notifiche: <NotificationsActive />,
  TsPay: <Payment />,
  "Poste Italiane": <LocalPostOffice />,
  AI: <Handshake />,
};

const createMenuItems = (menuObject: any, navigate: any): MenuItemTypes[] => {
  return Object.keys(menuObject).map((key) => {
    const value = menuObject[key];
    let menuItem: MenuItemTypes = {
      key,
      label: key,
      isTitle: false,
    };

    if (value.hasOwnProperty("root") && value.root) {
      menuItem.isTitle = true;
      menuItem.icon = mappedMenuIcons[key] || <Home />;
    }

    if (value.hasOwnProperty("href")) {
      menuItem.url = value.href;
      menuItem.onClickFunction = () => {
        if (value.target && value.target === "_blank") {
          window.open(value.href);
          return;
        }

        navigate(value.href)
      };
      menuItem.closePopoverAfterClick = true;
    }

    if (
      value.hasOwnProperty("subMenu") &&
      Object.keys(value.subMenu).length > 0
    ) {
      menuItem.children = createMenuItems(value.subMenu, navigate);
    }

    return menuItem;
  });
};

export const MenuConstructor = (menuConditions: any, navigate: any) => {
  const MENU_ITEMS: any[] = [];

  if (!Object.keys(menuConditions).length) {
    return [];
  }

  MENU_ITEMS.push(...createMenuItems(menuConditions, navigate));

  return MENU_ITEMS;
};
