import * as React from "react";
import Paper from "@mui/material/Paper";
import InputBase from "@mui/material/InputBase";
import IconButton from "@mui/material/IconButton";
import SearchIcon from "@mui/icons-material/Search";
import { useNavigate } from "react-router-dom";

export default function CustomizedInputBase() {
    const [value, setValue] = React.useState("");
    const navigate = useNavigate();

    // Commented because of the release with only agenda
    // React.useEffect(() => {
    //   const searchParams = new URLSearchParams(location.search);
    //   const fromSearch = searchParams.get("fromSearch");
    //   const nome = searchParams.get("nome");
    //   const uid = searchParams.get("uid");

    //   if (location.pathname.includes("anagrafiche") && fromSearch && nome) {
    //     setValue("");
    //     return navigate(`/anagrafiche?nome=${value}`);
    //   }

    //   if (location.pathname.includes("archive") && uid) {
    //     return navigate(`/legacy/archive/summary?uid=${uid}`);
    //   }
    // }, [location.search]);

    const handleSearch = () => {
        if (!value) {
            return;
        }
        navigate(`/legacy/search?querySearch=${value}`);
    };

    return (
        <Paper sx={{ display: "flex", alignItems: "center" }}>
            <InputBase
                sx={{ ml: 1, flex: 1 }}
                placeholder="Search"
                size="small"
                inputProps={{ "aria-label": "search" }}
                onChange={(event: any) => setValue(event.target.value)}
                value={value}
                onKeyDown={(event) => {
                    if (event.key === "Enter") {
                        handleSearch();
                    }
                }}
            />
            <IconButton
                type="button"
                size="small"
                sx={{
                    backgroundColor: "lightblue",
                    "&&&&&&:hover": {
                        minHeight: "inherit",
                        height: "inherit",
                    },
                }}
                aria-label="search"
                onClick={handleSearch}
            >
                <SearchIcon />
            </IconButton>
        </Paper>
    );
}
