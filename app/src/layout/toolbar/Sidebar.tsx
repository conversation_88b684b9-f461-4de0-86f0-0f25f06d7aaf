import { createElement } from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useLayout, useAuth } from "@1f/react-sdk";

import { UIShell } from "./UIShell";
import CustomError404 from "../../features/error404/CustomError404";

export const NavigationMenu = () => {
    const { routes, baseName } = useLayout();
    const { isPublic } = useAuth();

    if (isPublic) {
        return null;
    }


    return (
        <BrowserRouter basename={baseName}>
            <UIShell>
                {
                    routes.before.map((item: any, key: number) => {
                        return createElement(item.component, {
                            ...item.props,
                            key
                        });
                    })
                }
                <Routes>
                    {routes.items.map((item: any, idx: number) => {
                        const { component, element, props = {}, ...routeConfig } = item;
                        const useElement = element || createElement(component, props);
                        return <Route key={idx} element={useElement} {...routeConfig} />;
                    })}
                    <Route path="*" element={<CustomError404 />} />
                </Routes>
                {
                    routes.after.map((item: any, key: number) => {
                        return createElement(item.component, {
                            ...item.props,
                            key
                        })
                    })
                }
            </UIShell>
        </BrowserRouter>
    );
};
