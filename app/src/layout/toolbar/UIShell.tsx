import React, { useEffect, useState } from "react";
import { VaporUI<PERSON>hellNav, VaporAppBar } from "@vapor/v3-components";
import Button from "@mui/material/Button";
import IconButton from "@vapor/react-material/IconButton";
import { styled } from "@mui/material/styles";
import SearchTopbar from "./SearchTopbar";
import CreateIcon from "@mui/icons-material/Create";
import LogoutIcon from "@mui/icons-material/Logout";
import { Stack, Switch, FormControlLabel } from "@vapor/react-material";
import { useAuth, useMenu } from "@1f/react-sdk";
import AccountCircle from "@mui/icons-material/AccountCircle";
import VisibleWhenLogged from "../../hoc-components/VisibleWhenLogged";
import { MenuConstructor } from "../menus";
import { useConfigs } from "../../store/ConfigStore";
import useGetCustom from "../../hooks/useGetCustom";
import { useNavigate, Link } from "react-router-dom";
import { useUser } from "../../store/UserStore";
import { GUEST_ROUTES } from "../../utilities/constants";
import { useApi } from "../../store/ApiStore";
import { Typography } from "@vapor/react-material";
import { useLegacySwitch } from "../../store/LegacySwitchStore";
import useCustomNavigate from "../../hooks/useCustomNavigate";
import NotificationContent from "./notifications/NotificationContent";
import usePostCustom from "../../hooks/usePostCustom";
import UserProfileMenu from "./UserProfileMenu";
import "./ui-shell.scss";
import TimerTimesheet from "./TimerTimesheet";
import { transformLegacyUrl, needsLegacyUrlTransform } from "../../utilities/legacyUrlTransforms";
import tsLogo from "../../../public/logo-ts-legal-192.png";


interface UIShellProps {
    children: React.ReactNode;
}

const VisuallyHiddenInput = styled("input")({
    clip: "rect(0 0 0 0)",
    clipPath: "inset(50%)",
    height: 1,
    overflow: "hidden",
    position: "absolute",
    bottom: 0,
    left: 0,
    whiteSpace: "nowrap",
    width: 1,
});

const leftContent = (
    <Stack direction="row" alignItems="center" gap={3} sx={{ ml: 1.5 }}>
        <img src={tsLogo} alt="TS legal logo" height={30} />
        <span>TeamSystem Legal</span>
    </Stack>
);

export const UIShell = ({ children }: UIShellProps) => {
    const { isVisible, setMenuTree, isOpen, toggleIsOpen } = useMenu();
    const [menuLevels, setMenuLevels] = useState<any>(null);
    const [timerData, setTimerData] = useState(false);
    const [emailNotifications, setEmailNotifications] = useState<
        number | string
    >("");

    const navigate = useNavigate();
    const { customNavigate } = useCustomNavigate();
    const userDataRequest = useGetCustom("login/getloggeduser", {});
    const { user, modules, menuConditions, getUser }: any = useUser();

    React.useEffect(() => {
        if (user.emailNotifications) {
            setEmailNotifications(user.emailNotifications);
        }
    }, []);

    const { dropToken, isPublic } = useAuth();
    const { reset: resetConfig }: any = useConfigs();
    const { reset: resetUser, user: loggedUser }: any = useUser();
    const { reset: resetApi }: any = useApi();
    const {
        legacySwitch,
        setLegacySwitch,
        reset: resetLegacySwitch,
    }: any = useLegacySwitch();

    useEffect(() => {
        setMenuLevels(null);
    }, [legacySwitch]);

    const [anchorEl, setAnchorEl] = useState(null);

    const logoutRequest = useGetCustom("login/logout");

    const isTimerSet = localStorage.getItem("isTimerSet");

    React.useEffect(() => {
        if (isTimerSet) {
            getUser(userDataRequest);
            setTimerData(true);
        } else {
            setTimerData(false);
        }
    }, [isTimerSet]);

    const canInjectMenu =
        menuConditions &&
        user &&
        !isPublic &&
        !GUEST_ROUTES.includes(window.location.pathname) &&
        !menuLevels;

    React.useEffect(() => {
        if (canInjectMenu) {
            const children = MenuConstructor(menuConditions, customNavigate);

            setMenuTree(children);

            setMenuLevels({
                children,
            });
        }
    }, [canInjectMenu]);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setLegacySwitch(event.target.checked);
        if (event.target.checked) {
            if (!window.location.pathname.includes("/legacy")) {
                navigate(
                    `/legacy${window.location.pathname}${window.location.search}`
                );
            }
            return;
        }

        const currentFullUrl = window.location.pathname + window.location.search;
        const cleanPath = window.location.pathname.replace(new RegExp("/legacy", "g"), "");
        
        if (needsLegacyUrlTransform(currentFullUrl)) {
            const transformedUrl = transformLegacyUrl(currentFullUrl);
            navigate(transformedUrl);
        } else {
            navigate(cleanPath + window.location.search);
        }
    };

    const handleMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleLogout = () => {
        dropToken();
        logoutRequest.doFetch();
        resetUser();
        resetApi();
        resetConfig();
        resetLegacySwitch();
    };

    const updateEmailNotifications = usePostCustom(
        "userssettings/toggleemailnotifications?noTemplateVars=true"
    );

    const handleOnClickEvent = async () => {
        const response: any = await updateEmailNotifications.doFetch(true);
        setEmailNotifications(response.data);
    };

    const rightContent = () => {
        if (!isPublic && GUEST_ROUTES.includes(window.location.pathname)) {
            return (
                <IconButton
                    size="large"
                    onClick={dropToken}
                    sx={{ mr: "1rem" }}
                >
                    <LogoutIcon fontSize="large" sx={{ ml: 1 }} />
                    <Typography sx={{ color: "white", mx: 1, mr: "1rem" }}>
                        Logout
                    </Typography>
                </IconButton>
            );
        }

        return (
            <VisibleWhenLogged>
                <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ width: "100%" }}>
                    {/* Left side content */}
                    <Stack direction="row" alignItems="center" gap={2}>
                        {!user.isExternal && !user.locked_menu && <SearchTopbar />}
                        {user && user.locked_menu && (
                            <Link to="/legacy/agyo/contract">
                                <Button
                                    component="label"
                                    role={undefined}
                                    variant="contained"
                                    tabIndex={-1}
                                    size="medium"
                                >
                                    Accedi al contrattos
                                    <VisuallyHiddenInput type="file" />
                                </Button>
                            </Link>
                        )}
                        {timerData && (
                            <TimerTimesheet
                                timerData={
                                    modules && modules.getTimertaskdata()
                                        ? JSON.parse(modules.getTimertaskdata())
                                        : null
                                }
                            />
                        )}
                        {modules && modules.canAccessSupportoTopbar(user) && (
                            <Link to="/legacy/helpdesk">
                                <Button
                                    component="label"
                                    role={undefined}
                                    variant="contained"
                                    tabIndex={-1}
                                    size="medium"
                                    startIcon={<CreateIcon />}
                                >
                                    Supporto
                                    <VisuallyHiddenInput type="file" />
                                </Button>
                            </Link>
                        )}
                        <NotificationContent />
                    </Stack>

                    
                    <Stack direction="row" alignItems="center" gap={3}>
                        
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={legacySwitch}
                                    onChange={handleChange}
                                    name="legacySwitch"
                                />
                            }
                            labelPlacement="start"
                            label={
                                <Typography sx={{ color: "white" }}>
                                    Legacy Mode
                                </Typography>
                            }
                            sx={{ mr: 1 }}
                        />
                        
                     
                        <IconButton
                            size="large"
                            onClick={handleMenu}
                            sx={{ mr: "3rem" }}
                        >
                            <AccountCircle fontSize="large" sx={{ ml: 1 }} />
                            <Typography 
                                sx={{ 
                                    color: "white", 
                                    mx: 1, 
                                    mr: "1rem",
                                    whiteSpace: "nowrap"
                                }}
                            >
                                {loggedUser?.nomeutente} 
                            </Typography>
                        </IconButton>
                    </Stack>

                    <UserProfileMenu
                        anchorEl={anchorEl}
                        handleClose={handleClose}
                        handleOnClickEvent={handleOnClickEvent}
                        emailNotifications={emailNotifications}
                        handleLogout={handleLogout}
                    />
                </Stack>
            </VisibleWhenLogged>
        );
    };

    const renderToolbar = (
        <VaporAppBar
            isDrawerOpen={isOpen}
            toggleDrawer={toggleIsOpen}
            rightContent={rightContent()}
            leftContent={leftContent}
            disableDrawer={!isVisible}
        />
    );

    return (
        <VaporUIShellNav
            drawerOpen={isOpen}
            setDrawerOpen={toggleIsOpen}
            renderToolbar={renderToolbar}
            hideDrawer={!isVisible}
            menuLevels={menuLevels}
        >
            {children}
        </VaporUIShellNav>
    );
};
