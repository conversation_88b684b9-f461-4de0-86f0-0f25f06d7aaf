import PersonIcon from "@mui/icons-material/Person";
import CheckIcon from "@mui/icons-material/Check";
import QrCode2Icon from "@mui/icons-material/QrCode2";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import InboxIcon from "@mui/icons-material/Inbox";
import BuildIcon from "@mui/icons-material/Build";
import { Link } from "react-router-dom";
import {
    Menu,
    ListItemText,
    ListItemIcon,
    ListItemButton,
} from "@vapor/react-material";
import { ExtendedTypography } from "@vapor/react-extended";
import LogoutIcon from "@mui/icons-material/Logout";
import { useUser } from "../../store/UserStore";
import { useApi } from "../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../utilities/constants";

const UserProfileMenu = (props: any) => {
    const {
        anchorEl,
        handleClose,
        handleOnClickEvent,
        emailNotifications,
        handleLogout,
    } = props;

    const { user, modules }: any = useUser();
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    return (
        <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
                vertical: "bottom",
                horizontal: "left",
            }}
            keepMounted
            transformOrigin={{
                vertical: "top",
                horizontal: "left",
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
        >
            {!user.isAccounting && (
                // <ListItem>
                <ListItemButton component={Link} to="/legacy/users/profile">
                    <ListItemIcon>
                        <PersonIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Profilo" />
                </ListItemButton>
                // </ListItem>
            )}
            {user &&
                (user.getIsLawyerUser() ||
                    user.getIsPracticeSecretaryAndOther()) && (
                    <ListItemButton onClick={handleOnClickEvent}>
                        <ListItemText primary="Notifica impegni ed udienze via email" />
                        <ListItemIcon>
                            {emailNotifications && (
                                <CheckIcon fontSize="small" />
                            )}
                        </ListItemIcon>
                    </ListItemButton>
                )}

            <ListItemButton component={Link} to="/legacy/twofa">
                <ListItemIcon>
                    <QrCode2Icon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Autenticazione a due fattori" />
            </ListItemButton>

            {modules && modules.canAccessGestioneAbbonamento(user) && (
                <ListItemButton component={Link} to="/legacy/account/account">
                    <ListItemIcon>
                        <BuildIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Gest. abbonamento" />
                </ListItemButton>
            )}

            {modules && modules.canAccessGestioneCreditoTopbar(user) && (
                <ListItemButton component={Link} to="/legacy/credit/">
                    <ListItemIcon>
                        <BusinessCenterIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary="Gestione credito" />
                </ListItemButton>
            )}

            {user.tipoutente === "1" &&
                !user.isNetlexpdabasicUser &&
                !user.isNetlexpdafreeUser &&
                !user.isNetlexeasynotaUser && (
                    <ListItemButton component={Link} to="/legacy/agyo/">
                        <ListItemIcon>
                            <InboxIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary="Gestione fatture" />
                    </ListItemButton>
                )}

            {modules.lastLoginDate && (
                <ListItemButton>
                    <ListItemText
                        primary={
                            <ExtendedTypography ml={1} fontSize={14}>
                                Ultimo accesso: <i>{modules.lastLoginDate}</i>
                            </ExtendedTypography>
                        }
                    />
                </ListItemButton>
            )}
            {modules.gitVersion && (
                <ListItemButton>
                    <ListItemText
                        primary={
                            <ExtendedTypography
                                variant="body1"
                                fontWeight="bold"
                            >
                                {`Versione: ${modules.gitVersion}`}
                            </ExtendedTypography>
                        }
                    />
                </ListItemButton>
            )}
            {usedSubdomain && (
                <ListItemButton component={Link} to="/">
                    <ListItemText
                        primary={
                            <ExtendedTypography fontSize={14}>
                                Nome copia: <b>{usedSubdomain}</b>
                            </ExtendedTypography>
                        }
                    />
                </ListItemButton>
            )}

            <ListItemButton onClick={handleLogout}>
                <ListItemIcon>
                    <LogoutIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Logout" />
            </ListItemButton>
        </Menu>
    );
};

export default UserProfileMenu;
