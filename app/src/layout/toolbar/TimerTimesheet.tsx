import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Box,
  Grid,
  DialogContent,
  Dialog,
  DialogTitle,
  DialogActions,
  DialogContentText,
  Divider,
} from "@vapor/react-material";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import usePostCustom from "../../hooks/usePostCustom";
import { FiberManualRecord } from "@mui/icons-material";

import { Close } from "@mui/icons-material";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { useNavigate } from "react-router-dom";

interface TimerProps {
  initHours: number;
  initMinutes: number;
}

const Timer: React.FC<TimerProps> = ({ initHours, initMinutes }) => {
  const [hours, setHours] = useState("");
  const [minutes, setMinutes] = useState("");

  useEffect(() => {
    if (initHours === 0 || initHours < 10) {
      setHours(`0${initHours}`);
    } else if (initHours > 9) {
      setHours(initHours.toString());
    }

    if (initMinutes === 0 || initMinutes < 10) {
      setMinutes(`0${initMinutes}`);
    } else if (initMinutes > 9) {
      setMinutes(initMinutes.toString());
    }
  }, []);

  return (
    <div>
      <div className="timer">
        <span>{hours}:</span>
        <span>{minutes}</span>
      </div>
    </div>
  );
};

const TimerTimesheet = ({ timerData }: any) => {
  const [openModal, setOpenModal] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const iniziaAttivitaPostRequest = usePostCustom(
    "timesheet/timer?noTemplateVars=true"
  );

  if (!timerData) {
    return null;
  }

  const stopAttivita = async () => {
    const params = {
      id: timerData.timerTaskId,
      file_id: timerData.timerTaskFile_id,
      stopTimer: 1,
    };

    try {
      const response: any = await iniziaAttivitaPostRequest.doFetch(
        true,
        params
      );
      if (response.data === 1) {
        setOpenModal(!openModal);
        navigate("/timesheet/timesheet", {
          state: { reload: true },
        });

        localStorage.removeItem("isTimerSet");
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  return (
    <>
      <Stack direction="row" alignItems="center" gap={3} sx={{ ml: 1.5 }}>
        <IconButton
          size="small"
          style={{
            backgroundColor: "white",
            color: "red",
            borderRadius: "10%",
            minHeight: "inherit",
            height: "inherit",
          }}
          onClick={() => setOpenModal(!openModal)}
        >
          <FiberManualRecord className="blinking" />
        </IconButton>
      </Stack>
      <Dialog
        open={openModal}
        onClose={() => setOpenModal(!openModal)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>
          <Box display="flex" alignItems="right">
            <IconButton
              color="primary"
              onClick={() => setOpenModal(!openModal)}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <Divider className="MuiDivider-VaporLight" />
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Box
              component="section"
              sx={{
                p: 2,
                border: "1px solid #e3e3e3",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
                textAlign: "center",
                fontSize: "25px",
              }}
            >
              <Timer
                initHours={timerData?.timer?.h}
                initMinutes={timerData?.timer?.i}
              />
            </Box>
            <Box style={{ width: 350 }} sx={{ mt: 3 }}>
              <Grid container spacing={2}>
                <Grid
                  item
                  xs={4}
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {t("Inizio")}
                </Grid>
                <Grid
                  item
                  xs={8}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {timerData?.taskDate} {timerData?.clock}
                </Grid>
                <Grid
                  item
                  xs={4}
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {t("Utente")}
                </Grid>
                <Grid
                  item
                  xs={8}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {timerData?.user}
                </Grid>
                <Grid
                  item
                  xs={4}
                  sx={{ display: "flex", justifyContent: "flex-end" }}
                >
                  {t("Attività")}
                </Grid>
                <Grid
                  item
                  xs={8}
                  sx={{ display: "flex", justifyContent: "flex-start" }}
                >
                  {timerData?.taskActivity}
                </Grid>
              </Grid>
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={() => setOpenModal(!openModal)}>
            {t("Annulla")}
          </Button>
          <Button
            variant="contained"
            startIcon={<AccessTimeIcon />}
            onClick={() => stopAttivita()}
          >
            {t("Ferma cronometro")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TimerTimesheet;
