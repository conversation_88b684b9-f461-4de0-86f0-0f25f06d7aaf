import { Box } from "@mui/material";
const NoticationWrapper = ({ children }: any) => {
  return (
    <Box
      sx={{
        p: 2,
        marginTop: "15px",
        border: "1px solid #c9c9c9",
        bgcolor: "background.paper",
        position: "relative",
        maxHeight: "500px",
        overflow: "auto",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: "-10px",
          left: "50%",
          transform: "translateX(-50%)",
          width: "0",
          height: "0",
          borderLeft: "10px solid transparent",
          borderRight: "10px solid transparent",
          borderBottom: "10px solid #c9c9c9",
        }}
      />
      {children}
    </Box>
  );
};
export default NoticationWrapper;
