import CloudQueueIcon from "@mui/icons-material/CloudQueue";
import React, { useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { IconButton, Badge } from "@mui/material";
import Popper from "@mui/material/Popper";
import { Typography } from "@vapor/react-material";
import { useUser } from "../../../store/UserStore";
import NoticationWrapper from "./NotificationWrapper";
import { useTranslation } from "react-i18next";

const CloudNotification = ({ anchor, id, open, handleClick }: any) => {
  const { user, modules }: any = useUser();

  const formatted_disk_space_usage =
    modules?.provisioningRow?.formatted_disk_space_usage || "";

  const spaceNumber = modules?.provisioningRow?.formatted_disk_usage || "";

  const popperRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popperRef.current &&
        !popperRef.current.contains(event.target as Node)
      ) {
        handleClick(event, "outside");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleClick]);

  return (
    <div>
      <IconButton
        onClick={(event: React.MouseEvent<HTMLElement>) =>
          handleClick(event, "cloud")
        }
      >
        {formatted_disk_space_usage ? (
          <Badge
            badgeContent={`${formatted_disk_space_usage}%`}
            color="secondary"
          >
            <CloudQueueIcon />
          </Badge>
        ) : (
          <CloudQueueIcon />
        )}
      </IconButton>
      <Popper
        id={id}
        open={open}
        anchorEl={anchor}
        ref={popperRef}
        disablePortal
      >
        <NoticationWrapper>
          <Typography variant="bodySmall" gutterBottom component="div">
            {`${t("Spazio cloud utilizzato")}: ${formatted_disk_space_usage}%`}{" "}
            {t("di")} {spaceNumber} {t("GB")}
          </Typography>
          {!user.isNetlexpdaUser && (
            <Typography variant="bodySmall" gutterBottom component="div">
              <Link to="/legacy/account/account">
                {t("Acquista altro spazio")}
              </Link>
            </Typography>
          )}
        </NoticationWrapper>
      </Popper>
    </div>
  );
};
export default CloudNotification;
