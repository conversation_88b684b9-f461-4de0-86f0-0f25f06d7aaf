import MessageIcon from "@mui/icons-material/Message";
import React, { useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Grid from "@mui/material/Grid";
import { styled } from "@mui/material/styles";
import {
  IconButton,
  Badge,
  List,
  ListItem,
  Divider,
  ListItemText,
  ListItemSecondaryAction,
  Button,
} from "@mui/material";
import { useState } from "react";
import Popper from "@mui/material/Popper";
import { Typography } from "@vapor/react-material";
import NoticationWrapper from "./NotificationWrapper";
import { NewMessageModal } from "./NewMessageModal";

const StyledLink = styled(Link)({
  textDecoration: "none", // Remove underline
  color: "inherit", // Inherit color from parent
});

const EmailNotification = ({
  notification,
  anchor,
  id,
  open,
  handleClick,
}: any) => {
  const popperRef = useRef<HTMLDivElement>(null);
  const [openModal, setOpenModal] = useState(false);

  const handleDialog = () => {
    setOpenModal(!openModal);
    handleClick({}, "outside");
  };
  const { t } = useTranslation();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popperRef.current &&
        !popperRef.current.contains(event.target as Node)
      ) {
        handleClick(event, "outside");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleClick]);

  return (
    <>
      {" "}
      <NewMessageModal open={openModal} handleClose={handleDialog} />
      <div>
        <IconButton
          onClick={(event: React.MouseEvent<HTMLElement>) =>
            handleClick(event, "message")
          }
        >
          {notification?.emailsCounter &&
          parseInt(notification.emailsCounter) > 0 ? (
            <Badge badgeContent={notification.emailsCounter} color="secondary">
              <MessageIcon />
            </Badge>
          ) : (
            <MessageIcon />
          )}
        </IconButton>
        <Popper
          id={id}
          open={open}
          anchorEl={anchor}
          ref={popperRef}
          disablePortal
        >
          <NoticationWrapper>
            <Grid container spacing={1} style={{ width: "320px" }}>
              <Grid item xs={7}>
                <Typography variant="bodySmall" gutterBottom component="div">
                  {notification.emailsCounter &&
                  parseInt(notification.emailsCounter) === 0
                    ? t("Nessun nuovo messaggio")
                    : ""}
                </Typography>
              </Grid>
              <Grid item xs={5}>
                <Typography variant="bodySmall" gutterBottom component="div">
                  <Button variant="text" size="small" onClick={handleDialog}>
                    {t("Nuovo messaggio")}
                  </Button>
                </Typography>
              </Grid>
            </Grid>
            <Typography>
              <List
                sx={{
                  width: "100%",
                  maxWidth: 360,
                  bgcolor: "background.paper",
                }}
              >
                {(notification?.emailsList || []).map(
                  (item: any, i: number) => {
                    return (
                      <>
                        <StyledLink
                          to="/legacy/messages/messages"
                          className="dropdown-item notify-item notify-all"
                          key={i}
                        >
                          <ListItem alignItems="flex-start">
                            <ListItemText
                              secondary={
                                <React.Fragment>{item.message}</React.Fragment>
                              }
                            />
                            <ListItemSecondaryAction>
                              <Typography variant="body2" color="textSecondary">
                                {item.date}
                              </Typography>
                            </ListItemSecondaryAction>
                          </ListItem>
                        </StyledLink>
                        <Divider component="li" />
                      </>
                    );
                  }
                )}
              </List>
            </Typography>
            <Typography variant="bodySmall" gutterBottom component="div">
              {" "}
              <Link to="/legacy/messages/messages">
                {t("Tutti i messaggi")}
              </Link>
            </Typography>
          </NoticationWrapper>
        </Popper>
      </div>
    </>
  );
};
export default EmailNotification;
