import FlagIcon from "@mui/icons-material/Flag";
import React, { Fragment, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import {
    IconButton,
    Badge,
    List,
    ListItem,
    Divider,
    ListItemText,
    ListItemSecondaryAction,
} from "@mui/material";
import Popper from "@mui/material/Popper";
import { Typography } from "@vapor/react-material";

import NoticationWrapper from "./NotificationWrapper";
const StyledLink = styled(Link)({
    textDecoration: "none",
    color: "inherit",
});
const FlagNotification = ({
    notification,
    anchor,
    id,
    open,
    handleClick,
}: any) => {
    const popperRef = useRef<HTMLDivElement>(null);
    const { t } = useTranslation();
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                popperRef.current &&
                !popperRef.current.contains(event.target as Node)
            ) {
                handleClick(event, "outside");
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [handleClick]);
    return (
        <div>
            <IconButton
                onClick={(event: React.MouseEvent<HTMLElement>) =>
                    handleClick(event, "flag")
                }
            >
                {notification?.notificationsCounter &&
                parseInt(notification.notificationsCounter) > 0 ? (
                    <Badge
                        badgeContent={notification?.notificationsCounter}
                        color="secondary"
                    >
                        <FlagIcon />
                    </Badge>
                ) : (
                    <FlagIcon />
                )}
            </IconButton>
            <Popper
                id={id}
                open={open}
                anchorEl={anchor}
                ref={popperRef}
                disablePortal
            >
                <NoticationWrapper
                    sx={{
                        maxHeight: "400px",
                        overflowY: "auto",
                        position: "relative",
                        paddingBottom: "50px",
                    }}
                >
                    <Typography
                        variant="bodySmall"
                        gutterBottom
                        component="div"
                        key="nessuna-notifica"
                    >
                        {notification?.notificationsCounter &&
                        parseInt(notification.notificationsCounter) === 0
                            ? t("Nessuna nuova notifica")
                            : ""}
                    </Typography>
                    <List
                        sx={{
                            minWidth: "350px",
                            bgcolor: "background.paper",
                        }}
                    >
                        {notification.notificationsCounter > 0 && (
                            <Typography
                                variant="body500"
                                gutterBottom
                                component="div"
                                key="nuova-notifica"
                            >
                                {t("Nuova notifica")}
                            </Typography>
                        )}

                        {(notification?.notificationsList || []).map(
                            (item: any, i: number) => {
                                return (
                                    <Fragment key={i}>
                                        <StyledLink
                                            to="/legacy/deadlines/deadlines"
                                            className="dropdown-item notify-item notify-all"
                                        >
                                            <ListItem
                                                alignItems="flex-start"
                                            >
                                                <ListItemText
                                                    secondary={
                                                        <React.Fragment>
                                                            {item.text_message.slice(
                                                                0,
                                                                25
                                                            ) + "..."}
                                                        </React.Fragment>
                                                    }
                                                />
                                                <ListItemSecondaryAction>
                                                    <Typography
                                                        variant="body2"
                                                        color="textSecondary"
                                                    >
                                                        {item.date}
                                                    </Typography>
                                                </ListItemSecondaryAction>
                                            </ListItem>
                                        </StyledLink>
                                        <Divider component="li" />
                                    </Fragment>
                                );
                            }
                        )}
                    </List>

                    <Typography
                        variant="bodySmall"
                        sx={{
                            position: "sticky",
                            bottom: "-18px",
                            backgroundColor: "white",
                            padding: "10px",
                        }}
                        gutterBottom
                        component="div"
                    >
                        {" "}
                        <Link to="/legacy/notifications/notifications">
                            {t("Tutte le notifiche")}
                        </Link>
                    </Typography>
                </NoticationWrapper>
            </Popper>
        </div>
    );
};
export default FlagNotification;
