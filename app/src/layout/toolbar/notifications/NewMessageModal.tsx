import {
    Box,
    <PERSON>ton,
    <PERSON>alog,
    DialogTitle,
    Divider,
    DialogActions,
    IconButton,
    DialogContent,
    TextField,
    Stack,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import ToastNotification from "../../../custom-components/ToastNotification";
import FormInput from "../../../custom-components/FormInput";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import CloseIcon from "@mui/icons-material/Close";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useTranslation } from "react-i18next";
import { useUser } from "../../../store/UserStore";
import { SelectMultiple } from "../../../custom-components/SelectMultiple";
import ConfirmModal from "../../../custom-components/ConfirmModal";

const schema = yup.object().shape({
    messageFileUniqueid: yup.string(),
    receiverUser: yup.array(),
    messageGroups: yup.string(),
    messageSubject: yup.string().required("Oggetto obbligatorio"),
    messageText: yup.string().required("Messaggio obbligatorio"),
    fileNumber: yup.string(),
});

export const NewMessageModal = (props: {
    open: boolean;
    handleClose: () => void;
}) => {
    const { open, handleClose } = props;
    const [openConfirm, setOpenConfirm] = useState(false);
    const [show, setShow] = useState(false);
    const { t } = useTranslation();
    const { user }: any = useUser();
    const [selectedValues, setSelectedValues] = useState<string[]>([]);
    const [selectedTempValues, setSelectedTempValues] = useState<string[]>([]);
    const [groupId, setGroupId] = useState<string>("");

    const senByEmailRequest = usePostCustom(
        "messages/sendmessage?noTemplateVars=true"
    );

    const getUserGroupById = useGetCustom(
        `messages/get-users-group-by-id?noTemplateVars=true&idGroup=${groupId}`
    );

    const {
        register,
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm({
        resolver: yupResolver(schema),
        defaultValues: {
            messageFileUniqueid: "",
            receiverUser: [],
            messageSubject: "",
            fileNumber: "",
            messageText: "",
            messageGroups: "",
        },
    });

    const multiSelectValues = user
        ? user?.getPeople().map((people: any) => ({
              value: people.id,
              label: people.nome,
          }))
        : [];

    useEffect(() => {
        if (!open) {
            setSelectedValues([]);
            setSelectedTempValues([]);
            setGroupId("");
        }
    }, [open]);

    useEffect(() => {
        if (groupId) {
            getSelectedData();
        }
    }, [groupId]);

    const getSelectedData = async () => {
        const response: any = await getUserGroupById.doFetch(true);
        const selectedValuesByGroup = multiSelectValues
            ?.filter((item: any) => response.data.includes(item.value))
            .map(({ label }: any) => label);

        setSelectedValues(selectedValuesByGroup);
        setValue("receiverUser", selectedValuesByGroup);
    };

    const handleSelect = (e: any) => {
        const { name, value } = e.target;
        setValue(name, value);
        setGroupId(value);
    };

    const handleChangeMultiSelect = (
        event: React.ChangeEvent<{ value: unknown }>
    ) => {
        if (groupId) {
            setOpenConfirm(!openConfirm);
            setSelectedTempValues(event.target.value as string[]);
        } else {
            setSelectedValues(event.target.value as string[]);
        }
    };

    const onSubmit = async (values: any) => {
        const recieverUserIds = multiSelectValues
            .filter((item: any) => selectedValues?.includes(item.label))
            .map(({ value }: any) => value);
        values["receiverUser"] = recieverUserIds;

        if (!values.messageGroups) {
            values.messageGroups = "-1";
        }

        if (!values.messageFileUniqueid) {
            values.messageFileUniqueid = "";
        }

        if (!values.fileNumber) {
            values.fileNumber = "";
        }

        const formData = new FormData();
        Object.keys(values).forEach((key: string) => {
            if (key === "receiverUser") {
                values[key].forEach((element: any) => {
                    formData.append("receiverUser[]", element);
                });
            } else {
                formData.append(key, values[key]);
            }
        });

        const response: any = await senByEmailRequest.doFetch(true, values);

        if (!response?.data) {
            setShow(true);
        }
        handleClose();
        setSelectedValues([]);
        setSelectedTempValues([]);
        reset();
    };

    const handleDecline = () => {
        setOpenConfirm(false);
    };

    const handleAgree = () => {
        setOpenConfirm(false);

        const lastSelectedItem =
            selectedTempValues[selectedTempValues.length - 1];

        setSelectedValues([...selectedValues, lastSelectedItem]);
        setValue("receiverUser", [...selectedValues, lastSelectedItem]);
        setSelectedTempValues([]);
        setGroupId("");
        reset({ messageGroups: "" });
    };

    return (
        <>
            <ToastNotification
                showNotification={show}
                setShowNotification={setShow}
                severity="error"
                text={t(
                    "Si è verificato un errore durante l'invio del messaggio, si prega di riprovare più tardi. Grazie"
                )}
            />
            <ConfirmModal
                open={openConfirm}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={t("Annulla")}
                agree={t("OK")}
                confirmText={t(
                    "Stai associando manualmente le persone al gruppo. L'associazione di gruppo verra tolta. Vuoi continuare?"
                )}
                title={t("Conferma")}
            />

            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Invia messaggio")}
                    <IconButton color="primary" onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                "& .MuiTextField-root": {
                                    mt: 1,
                                    width: 350,
                                },
                            }}
                        >
                            <div>
                                <Stack>
                                    <FormInput
                                        sx={{
                                            width: "100%",
                                            mb: "5px",
                                        }}
                                        control={control}
                                        {...register("messageGroups")}
                                        name="messageGroups"
                                        label={t("Gruppo Utenti")}
                                        onChange={(data: any) =>
                                            handleSelect(data)
                                        }
                                        type="select"
                                        variant="outlined"
                                        value={groupId}
                                        options={
                                            user &&
                                            user
                                                ?.getGroups()
                                                .map((item: any) => ({
                                                    label: item.name,
                                                    value: item.id,
                                                }))
                                        }
                                    />

                                    <SelectMultiple
                                        values={selectedValues}
                                        label={`${t("Intestatari")}:`}
                                        options={multiSelectValues}
                                        onChange={handleChangeMultiSelect}
                                        width={"95%"}
                                        margin={"8px"}
                                        maxWidth="350px"
                                    />

                                    <FormInput
                                        control={control}
                                        name="messageSubject"
                                        label={t("Oggetto*")}
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["messageSubject"] &&
                                            errors["messageSubject"][
                                                "message"
                                            ] != ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["messageSubject"] &&
                                            errors["messageSubject"]["message"]
                                        }
                                    />

                                    <Controller
                                        name="messageText"
                                        control={control}
                                        render={({ field }: any) => (
                                            <TextField
                                                {...field} // Connects react-hook-form to TextField
                                                id="outlined-multiline-static"
                                                label="Messaggio"
                                                multiline
                                                rows={8}
                                                error={
                                                    !!errors?.messageText
                                                        ?.message
                                                }
                                                helperText={
                                                    errors?.messageText
                                                        ?.message || ""
                                                }
                                            />
                                        )}
                                    />
                                </Stack>
                            </div>
                        </Box>
                    </DialogContent>

                    <DialogActions>
                        <Button
                            type="button"
                            variant="outlined"
                            onClick={handleClose}
                            sx={{
                                mr: 1,
                            }}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button
                            type="submit"
                            variant="contained"
                            sx={{
                                mr: 1,
                            }}
                        >
                            {t("Invia")}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </>
    );
};
