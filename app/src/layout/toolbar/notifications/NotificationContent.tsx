import React, { useEffect, useState } from "react";
import { useUser } from "../../../store/UserStore";
import usePostCustom from "../../../hooks/usePostCustom";
import CloudNotification from "./CloudNotification";
import EmailNotification from "./EmailNotification";
import FlagNotification from "./FlagNotification";

const NotificationContent = () => {
    const [notification, setNotification] = useState<any>({
        emailsCounter: "",
        notificationsCounter: "",
        emailsList: [],
        notificationsList: [],
    });
    const [anchorCloudEl, setAnchorCloudEl] =
        React.useState<null | HTMLElement>(null);
    const [anchorMessageEl, setAnchorMessageEl] =
        React.useState<null | HTMLElement>(null);

    const [anchorFlagEl, setAnchorFlagEl] = React.useState<null | HTMLElement>(
        null
    );

    useEffect(() => {
        getMessages();
    }, []);

    const notificationMessages = usePostCustom(
        "notifications/getmessages?noTemplateVars=true"
    );

    const getMessages = async () => {
        const { data }: any = await notificationMessages.doFetch(true);
        setNotification((prevNotification: any) => ({
            ...prevNotification,
            emailsCounter: data.emailsCounter,
            notificationsCounter: data.notificationsCounter,
            emailsList: data.emailsList || [],
            notificationsList: data.notificationsList || [],
        }));
    };

    const handleClick = (
        event: React.MouseEvent<HTMLElement>,
        type: string
    ) => {
        if (type === "cloud") {
            setAnchorCloudEl(anchorCloudEl ? null : event.currentTarget);
            setAnchorMessageEl(null);
            setAnchorFlagEl(null);
        }
        if (type === "message") {
            setAnchorMessageEl(anchorMessageEl ? null : event.currentTarget);
            setAnchorCloudEl(null);
            setAnchorFlagEl(null);
        }
        if (type === "flag") {
            setAnchorFlagEl(anchorFlagEl ? null : event.currentTarget);
            setAnchorCloudEl(null);
            setAnchorMessageEl(null);
        }

        if (type === "outside") {
            setAnchorFlagEl(null);
            setAnchorCloudEl(null);
            setAnchorMessageEl(null);
        }
        getMessages();
    };

    const openCloud = Boolean(anchorCloudEl);
    const cloudId = openCloud ? "simple-popper" : undefined;

    const openMessage = Boolean(anchorMessageEl);
    const messageId = openMessage ? "simple-popper" : undefined;

    const openFlag = Boolean(anchorFlagEl);
    const flagId = openFlag ? "simple-popper" : undefined;
    const { user }: any = useUser();

    return (
        <>
            {!user.isAccounting && (
                <CloudNotification
                    anchor={anchorCloudEl}
                    open={openCloud}
                    id={cloudId}
                    handleClick={handleClick}
                />
            )}
            {!user.isNetlexpdaUser &&
                !user.isNetlexpdabasicUser &&
                !user.isNetlexpdafreeUser &&
                !user.isNetlexpdaulofUser &&
                !user.isNetlexeasynotaUser && (
                    <EmailNotification
                        notification={notification}
                        anchor={anchorMessageEl}
                        open={openMessage}
                        id={messageId}
                        handleClick={handleClick}
                    />
                )}
            {user.isLawyerUser && (
                <FlagNotification
                    notification={notification}
                    anchor={anchorFlagEl}
                    open={openFlag}
                    id={flagId}
                    handleClick={handleClick}
                />
            )}
        </>
    );
};

export default NotificationContent;
