import { Dispatch, SetStateAction } from "react";

export interface Response<T> {
    data: T;
    loading: boolean;
    hasLoaded: boolean;
    error: any;
}

export interface DeleteCustomRateProps {
    uid: string;
    remove: boolean;
}

export interface IDeleteCustomRateProps {
    (props: DeleteCustomRateProps): Response<any>;
}

export interface IAuthorityProps {
    id: string;
    setSelectedAuthority: (value: string) => void;
    selectedAuthority: string;
}

export interface ITariffarioData {
    id: string;
    nome: string;
    descrizione: string;
    immessoil: string;
    riservato: string;
    uniqueid: string;
    external_code: string;
    autorita: IAutorita[];
}

export interface IAutorita {
    id: string;
    nome: string;
}

export interface ISaveCustomRateProps {
    (props: {
        uid: string | undefined;
        nome: string;
        descrizione: string;
        riservato: string;
        save: boolean;
    }): Response<any>;
}

export interface IRemoveAuthorityProps {
    (props: { uid: string; id: string; removeAll: boolean }): Response<any>;
}

export interface IRemoveThresholdProps {
    (props: { uid: string; id: string; removeAll: boolean }): Response<any>;
}

export interface ModifyFields {
    thresholds: IThresholdData[];
    fieldType: string;
    value: string;
    idThreshold: string | null;
    idPhase: string | null;
    idAuthority: string | null;
    index: number | null;
}

export interface IModifyFieldProps {
    (props: ModifyFields): Response<any>;
}

export interface IThresholdsProps {
    (props: {
        uid: string;
        authorityId: string;
        reloadThreshold: boolean;
    }): Response<IThresholdData[] | undefined>;
}

export interface IThresholdData {
    id: string;
    nome: string;
    min: string;
    max: string;
}

export interface ITariffsProps {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    searchField: string;
}

export interface IPhaseData {
    id: string;
    descrizione: string;
    min: string;
    max: string;
}

export interface IGetPhasesProps {
    (props: { thresholdId: string; authorityId: string }): Response<
        IPhaseData[]
    >;
}

export interface IAddAuthorityProps {
    (props: { uid: string; authorityId: string; add: boolean }): Response<any>;
}

export interface IAddThresholdProps {
    (props: {
        uid: string;
        authorityId: string;
        addThreshold: boolean;
    }): Response<any>;
}

export interface IPhaseRowProps {
    value: IPhaseData;
    index: number;
    setSelectedFieldModify: Dispatch<SetStateAction<ModifyFields>>;
    selectedFieldModify: ModifyFields;
}

export interface IPhasesProps {
    selectedThreshold: string;
    selectedAuthority: string;
    setSelectedFieldModify: Dispatch<SetStateAction<ModifyFields>>;
    selectedFieldModify: ModifyFields;
}

export interface IThresholdRowProps {
    selectedThreshold: string;
    setSelectedThreshold: Dispatch<SetStateAction<string>>;
    setSelectedThresholdDelete: Dispatch<SetStateAction<string>>;
    selectedThresholdDelete: string;
    setSelectedFieldModify: Dispatch<SetStateAction<ModifyFields>>;
    selectedFieldModify: ModifyFields;
    value: IThresholdData;
    index: number;
}

export interface IThresholdProps {
    id: string;
    selectedAuthority: string;
    selectedThreshold: string;
    setSelectedThreshold: Dispatch<SetStateAction<string>>;
    selectedFieldModify: ModifyFields;
    thresholds: IThresholdData[];
    setThresholds: Dispatch<SetStateAction<IThresholdData[]>>;
    setSelectedFieldModify: Dispatch<SetStateAction<ModifyFields>>;
}

export interface ITariffProps {
    id: string;
    saveCustomRate: boolean;
    deleteCustomRate: boolean;
    deleteCustomRateModal: boolean;
    setDeleteCustomRateModal: Dispatch<SetStateAction<boolean>>;
    setDeleteCustomRate: Dispatch<SetStateAction<boolean>>;
    authorityData: ITariffarioData;
    setAuthorityData: Dispatch<SetStateAction<ITariffarioData>>;
}

export interface IAuthorityRow {
    selectedAuthority: string;
    selectedAuthorityDelete: string;
    value: IAutorita;
    setSelectedAuthorityDelete: Dispatch<SetStateAction<string>>;
    setSelectedAuthority: (value: string) => void;
}
