import { Dispatch, SetStateAction } from "react";

export interface IGestioneParams {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    startDate: Date | string | null;
    endDate: Date | string | null;
    person: number;
    tipologia: number;
}

export type fieldName = "startDate" | "endDate" | "person" | "tipologia";

export interface IimpegniFilters {
    handleSearchChange: (field: fieldName) => (value: SearchValue) => void;
    params: IGestioneParams;
    setParams: Dispatch<SetStateAction<IGestioneParams>>;
    users: User[];
}

export type SearchValue = Date | null | unknown;

export interface User {
    id: string;
    nomeutente: string;
    nome: string;
}

export interface IGestioneResponse {
    data: {
        currentPage: any;
        totalRows: string;
    };
}
