import { Dispatch, SetStateAction } from "react";

export interface Document {
    Email: string;
    accepted_banner_id: string;
    aggiunta_attestazione: string;
    alert_email: string;
    alert_gestionale: string;
    alert_telegram: string;
    annotazioni: string;
    api_personal_token: string;
    api_token_nonce: string;
    archived_id: string;
    attivo: string;
    avvocato: string;
    calendarInterval: string;
    campi_agenda: string;
    category_id: string;
    cct_password: string;
    cct_username: string;
    centro_profitto: string;
    codicearchivio: string;
    codiceavvocato: string;
    codicepratica: string;
    cognomepersonale: string;
    color: string;
    containsAttachments: string;
    containsFile: string;
    content: string;
    costo_risorsa: string;
    creation_date: string;
    creation_user: string;
    currency_id: string;
    da_associare: string;
    daily_workload: string;
    data: string;
    data_archivio: string;
    data_chiusura: string;
    data_decreto: string;
    data_della_sentenza: string;
    data_documento: string;
    data_macero: string;
    data_sentenza: string;
    dataprescrizione: string;
    datareato: string;
    default_calendar: string;
    default_page_size: string;
    deleted_at: string;
    descrizione: string;
    document_hash: string;
    document_id: string;
    documentale_data: string;
    documentale_id: string;
    documentodi: string;
    dominus: string;
    downloadable: string;
    duplicate_id: string;
    edit_propri_timesheet: string;
    emailNotifications: string;
    emissione_di: string;
    emittente: string;
    emittente_office: string;
    enable_timesheet: string;
    ente: string;
    esito: string;
    ext_users_visible: string;
    extended_days_before: string;
    extended_days_before_commitments: string;
    external_can_upload: string;
    external_sw_id: string;
    external_token: string;
    external_token_date: string;
    external_upload_notification: string;
    faldone: string;
    folder_id: string;
    font_color: string;
    formula_esecutiva: string;
    formula_protesto: string;
    fourclegal_contest: string;
    fourclegal_id: string;
    from_external_user: string;
    general_protocol: string;
    gestisce_riserva: string;
    giudice: string;
    gruppo: string;
    holderUuid: string;
    hourly_rate: string;
    id: string;
    idDfa: string;
    id_categoria: string;
    id_studio_default: string;
    identificatore_codice: string;
    immessoda: string;
    immessoil: string;
    importo_forfait: string;
    in_out: string;
    is_archived: string;
    is_assignee_pa: string;
    is_online: string;
    last_changelog_date: string;
    last_mobile_access_at: string;
    lastemail_from: string;
    lastemail_to: string;
    lastmodify_date: string;
    lastmodify_user: string;
    lastmodifypassword_date: string;
    lastmodifypassword_user: string;
    legacy_mode: string;
    legal_storage: string;
    legal_storage_path: string;
    letterhead_id: string;
    linkedToDeadline: string;
    listaclienti: string;
    listacontroparti: string;
    listino: string;
    listino_orario: string;
    login_attempts: string;
    max_hourly_rate: string;
    min_hourly_rate: string;
    mittente: string;
    modifica_propri_timesheet: string;
    modificatoda: string;
    modificatoil: string;
    mostra_codice_fiscale_studio: string;
    nascondi_codice_fiscale_studio: string;
    natoa: string;
    natoil: string;
    nome: string;
    nome_pratica: string;
    nomefile: string;
    nomepersonale: string;
    nomeutente: string;
    note: string;
    notifica_di: string;
    notifica_pignoramento: string;
    notifica_precetto: string;
    notify_ged: string;
    notify_new_proceed: string;
    numero_sentenza: string;
    numero_sentenza_anno: string;
    numerodecretoingiuntivo: string;
    numerodecretoingiuntivoanno: string;
    numeroprotocollo: string;
    object_id: string;
    office: string;
    oggetto: string;
    one_drive_email: string;
    one_drive_folder_id: string;
    one_drive_last_editor_id: string;
    one_drive_last_editor_name: string;
    one_drive_modified_at: string;
    one_drive_new_upload: string;
    one_drive_uniqueid: string;
    one_drive_url: string;
    opentext_download_url: string;
    opentext_ticket_owner: string;
    opentext_ticketid: string;
    opentext_uniqueid: string;
    ordineAcquisto_CIG: string;
    ordineAcquisto_CUP: string;
    ordineAcquisto_codiceCommessaConvenzione: string;
    ordineAcquisto_data: string;
    ordineAcquisto_id: string;
    ordineAcquisto_numItem: string;
    orefatturabili: string;
    pa_cap_studio: string;
    pa_citta_studio: string;
    pa_civico_studio: string;
    pa_codice_fiscale_da: string;
    pa_codice_fiscale_studio: string;
    pa_iban_studio: string;
    pa_id_codice: string;
    pa_id_codice_da: string;
    pa_indirizzo_studio: string;
    pa_nome_studio: string;
    pa_partita_iva_studio: string;
    pa_provincia_studio: string;
    pa_regime_fiscale_studio: string;
    pa_tipo_cassa_studio: string;
    pa_tipo_ritenuta: string;
    palchetto: string;
    password: string;
    pec_address_for_pa_invoice: string;
    person_id: string;
    pin_change_password: string;
    pin_creation_date: string;
    polisweb_doc_id: string;
    polisweb_doc_superiore: string;
    polisweb_doc_tipo: string;
    procuratore: string;
    provvedimento: string;
    pubblico_ministero: string;
    purge_polisweb_events: string;
    quadro_d_code_id: string;
    qualificautente: string;
    reato: string;
    referent_id: string;
    rgapp: string;
    rgappanno: string;
    rgappdescr: string;
    rgcass: string;
    rgcassanno: string;
    rgcassdescr: string;
    rggip: string;
    rggipanno: string;
    rggipdescr: string;
    rggup: string;
    rggupanno: string;
    rggupdescr: string;
    rgnr: string;
    rgnranno: string;
    rgriesame: string;
    rgriesameanno: string;
    rgriesamedescr: string;
    rgsiep: string;
    rgsiepanno: string;
    rgsiepdescr: string;
    rgsius: string;
    rgsiusanno: string;
    rgsiusdescr: string;
    rgtrib: string;
    rgtribanno: string;
    rgtribdescr: string;
    rgtribtipo: string;
    riforma_cartabia: string;
    riservata: string;
    ruolo_avvocato: string;
    ruologeneraleanno: string;
    ruologeneralenumero: string;
    scaffale: string;
    scatolone: string;
    sede: string;
    seniority: string;
    sezione: string;
    sigla: string;
    signature_details: string;
    signature_id: string;
    signature_points: string;
    signature_status: string;
    signer_profile: string;
    signers: string;
    situazione: string;
    situazione_contabile: string;
    size: string;
    smartmailer_address: string;
    smartmailer_both: string;
    smartmailer_upload: string;
    soglia_budget: string;
    soglia_ore: string;
    soglia_scoperto: string;
    special: string;
    spese_generali: string;
    spese_generali_check: string;
    spesefatturabili: string;
    stanza: string;
    stato: string;
    status_id: string;
    stima_soccombenza: string;
    struttura_repertoriante: string;
    submitterUuid: string;
    subprocedimento: string;
    terms_of_use_id: string;
    theme: string;
    throttle: string;
    tipo_valore: string;
    tipologia: string;
    tipologiaFE: string;
    tipologiapratica: string;
    tipoutente: string;
    tipoutentenome: string;
    titolodocumento: string;
    token_app: string;
    twofa_enabled: string;
    twofa_key: string;
    twofa_time: string;
    ufficio_giudiziario: string;
    ui_settings: string;
    uniqueid: string;
    valore: string;
    valori_dinamici: string;
    valori_extra_giud_dinamici: string;
    vede_soci: string;
    visible: string;
    voispeed_ext: string;
}

export interface DocumentListData {
    currentPage: Document[];
    totalRows: string;
}

export interface DocumentListFilters {
    searchField: "";
    titolodocumento: string;
    note: string;
    searchCategories: string;
    fileUniqueid: string;
    fileSearch: string;
    contractUuid: string;
    contractSearch: string;
    protocollo: string;
    documentoDi: string;
    visibility: string;
    signatureStatus: string;
    archiveSubject: string;
    archiveSubjectRelation: string;
    startDate: string;
    endDate: string;
    noupdate: boolean;
    search: boolean;
    afterload?: boolean;
}

export interface DocumentFiltersProps {
    searchParams: DocumentListFilters;
    setSearchParams: Dispatch<SetStateAction<DocumentListFilters>>;
    onTextFieldChange: any;
    onSelectChange: any;
    handleDateChange: any;
    resetSearchParams: () => void;
    data: any;
    hasLoaded: any;
}

export interface DocumentData {
    loggedUser: any;
    poliswebEventsData: any;
    documentgroupcategories: any;
    documentcategories: any;
    users: any;
    fileId: any;
    documentstatus: any;
    fromEmails: any;
    toEmails: any;
    documentsinout: any;
    lawyers: any;
    fileLawyers: any;
    fileCustomers: any;
    customerEmails: any;
    fileUsers: any;
    result?: any;
    startDate?: any;
    endDate?: any;
}

export interface Practice {
    id: string;
    codicearchivio: string;
    uniqueid: string;
    ruologeneralenumero: string;
    ruologeneraleanno: string;
    descrizione: string;
    nome_pratica: string;
    is_archived: string;
    oggetto: null;
    listaclienti: string;
    listacontroparti: string;
    rgnRga: string;
    hourly_rate: string;
    tipologiapratica: string;
    headerArchive: string;
}

export interface SearchContractsProps {
    query: string;
    from: string;
}

export interface Certificate {
    username: string;
    mobile: string;
    uniqueid: string;
}

export interface RemoteSignature {
    remoteSignON: number | null;
    certificates: Certificate[];
    folderName: string;
}

export interface ModifyDocumentDetails {
    documentoData: Date | "";
    numeroprotocollo: string;
    documentodi: number;
    visibile: number;
    category_id: number;
    note: string;
    folders: {
        id: string;
        name: string;
        level: number;
    }[];
    folder_id: string;
    in_out: string;
    documentFileUniqueid: string;
    titolodocumento: string;
    nomefileForBtn: string;
    nomefileForEmail: string;
    document1: string;
    uniqueid: string | undefined | null;
    oneDriveUniqueid: string;
    docSelectionCallbackUrl: string;
    nuovonome: string;
    nomeFile: string;
    searchOggettoDocumento: string;
    object_id: string;
    externalUser: string[];
    status_id: number | string;
    mittente: string;
    data: string;
    lastEditor?: any;
}

export interface Folder {
    id: string;
    name: string;
    level: number;
}

export interface Events {
    error: boolean;
    events: any[];
}

export interface Subject {
    id: string;
    denominazione: string;
}

export interface User {
    id: string;
    denominazione: string;
}

export interface Form {
    repertorioOggetto: string;
    protocolOggetto: string;
    id: number;
    uniqueid: string;
    category_id: number;
    relation_type: string;
    documentodi: number;
    documentale_data: string;
    numeroprotocollo: string;
    progressivoRepertorio: string;
    mittente: string;
    rifEnte: string;
    note: string;
    in_out: string;
    titolodocumento: string;
    object_id: number;
    searchOggettoDocumento: string;
    myDocument: boolean;
    modificatoda: string;
    modificatoil: string;
    status: number;
    documentalSubjects: string;
    documentalRecipients: string;
    documentalAttachments: any[];
    id_documento: string;
    documentale_document_type: string;
    codeType: string;
    containsFile: string;
    containsAttachment: string;
    nomefileForBtn: string;
    documentoData: string;
    poliswebDocType: string;
    polisweb_doc_id: string;
    searchOggettoPratica: string;
    subjects: Subject[];
    users: User[];
    external_user_name: string;
    numeroprotocolloged: string;
    documentFileUniqueid: string;
    onedriveFileUniqueid: string;
    externalCode: string;
    isExternal: number;
    visible: number;
    nomefileForEmail: string;
    document1: string;
    isConvertible: boolean;
    saveInMessages: number;
    canModify: number;
    isEML: number;
    cSDocumentEnabled: boolean;
    documentale_id: string;
    externalUsers: any[];
    ext_users_visible: string;
}

export interface DocumentDetails {
    folder_id: string;
    legal_storage: string;
    fileNotFound: boolean;
    folders: Folder[];
    events: Events;
    CSEnabled: boolean;
    form: Form;
    lastEditor: any;
}

export interface Anagraficha {
    id: string;
    nome: string;
    cognome: string;
    sesso: string;
    datanascita: string;
    luogonascita: string;
    contatti: string;
    denominazione: string;
    codicefiscale: string | null;
    uniqueid: string;
    partitaiva: string | null;
    signer_profile: string | null;
}

export interface DeleteDocumentsProps {
    documentIds: string[];
    remove: boolean;
}
