
export interface SignatureListProps {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    lawyerSearch: number;
}

export interface SignaturesResponse {
    currentPage: Signature[];
    totalRows: string;
}

export interface Signature {
    username: string;
    certificate_id: string | null;
    mobile: string;
    provider: string;
    active: string;
    uniqueid: string;
    lawyer: string;
    modifiable: string;
}

export interface IGetSignatureProps {
    uniqueId?: string;
}

export interface IGetSignatureResponse {
    extras: any[];
    form: ISignature;
}

export interface ISignature {
    mobile: string;
    uname: string;
    certificate: string;
    provider: string;
    active: string;
    uniqueid: string;
}

export interface ISignatureData {
    uniqueid?: string;
    provider?: string;
    mobile?: string;
    uname?: string;
    certificate?: string;
    passwd: string;
    passwdConfirm: string;
    active?: string;
}

export interface ISignatureForm {
    signature: ISignatureData;
    handleInputChange: any;
    certificates: any[];
    validations: any;
    showErrors: boolean;
}
