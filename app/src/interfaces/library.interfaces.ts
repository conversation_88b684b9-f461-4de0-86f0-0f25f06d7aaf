import { Dispatch, SetStateAction } from "react";

export interface LoanDetailsQuery {
    uniqueid: string | null;
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
}

export interface BookDetails {
    form: {
        id: string;
        titolo: string;
        autore: string;
        editore: string | null;
        descrizione: string | null;
        data_pubblicazione: string | null;
        tipo: string;
        collana: string | null;
        codice: string | null;
        b_codice: string;
        id_sede: string;
        ubicazione: string;
        posizione: string;
        valore: string | null;
        uniqueid: string;
    };
    sede: string;
    last_loan_uid: string;
    borrowed: number;
}

interface LoanRecord {
    nome_prestatario: string;
    data_inizio: string;
    data_fine: string;
    data_restituzione: string | null;
    uniqueid: string;
}

export interface LoanData {
    currentPage: LoanRecord[];
    totalRows: number;
}

export interface LibraryQueryParams {
    titoloSearch: string;
    autoreSearch: string;
    editoreSearch: string;
    codiceSearch: string;
    b_codiceSearch: string;
    tipoSearch: number;
    collanaSearch: string;
    sedeSearch: number;
    ubicazioneSearch: string;
    posizioneSearch: string;
    startDate: string;
    endDate: string;
    prestatoSearch: string;
    startLoanDate: string;
    endLoanDate: string;
    startValue: string;
    endValue: string;
}

export interface BookData {
    uniqueid: string | null;
    titolo: string;
    autore: string;
    editore: string | null;
    b_codice: string;
    descrizione: string | null;
    data_pubblicazione: string | Date | null;
    tipo: string;
    collana: string | null;
    codice: string | null;
    id_sede: string;
    ubicazione: string;
    posizione: string;
    valore: string | null;
}

export interface BookUpdateExtraDataProps {
    handleInputChange: (
        field: keyof BookData
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleDateChange: any;

    bookData: BookData;
}

export interface BookUpdateDetailsProps {
    errors: Partial<BookData>;
    handleInputChange: (
        field: keyof BookData
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    bookData: BookData;
    handleSelectChange: (field: keyof BookData) => (e: any) => void;
}

export interface LoanForm {
    form: {
        nome_prestatario: string;
        data_inizio: string;
        data_fine: string;
        data_restituzione: string;
        loanUid: string;
    };
}

export interface LoanParams {
    loanUid: string | undefined | null;
    id_prestatario: string;
    data_inizio: string;
    data_fine: string;
    data_restituzione: string;
    tipo_prestatario: string;
    uid: string | null | undefined;
}

export interface User {
    id: string;
    nome: string;
    type: string;
}

export interface BorrowersProps {
    selectedUser: User | null;
    setSelectedUser: Dispatch<SetStateAction<User | null>>;
}
