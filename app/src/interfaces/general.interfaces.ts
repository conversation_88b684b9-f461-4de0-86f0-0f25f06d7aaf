export interface IColumn {
    Header: string;
    accessor: string;
    sort?: boolean;
    Cell?: any;
    className?: string;
}

export interface IList<T> {
    rows: T[];
    columns: T[];
    totalRows: number;
    pageIndex: number;
    pageSize: number;
    loading?: boolean;
    page?: number;
}

export interface IGridSettings {
    column_names: string[];
    column_keys: string[];
    column_widths?: string[];
    cell_templates?: string[];
    sortable: boolean[];
}

export interface IGridColumn {
    name: string;
    selector: (row: any) => void;
    sortField: string;
    sortable: boolean;
    wrap?: boolean;
    width?: string;
}

export interface ITableColumn<T> {
    name: string;
    selector: (row: T) => void;
    sortField: string;
    sortable: boolean;
}

export interface ITableTotalRow {
    totalValues: string[];
    redColumnIndex: number;
    validValues: number;
    skipColumns: number;
    startColumn: number;
    displayValues: boolean;
    colSpan: number;
}

export interface ICustomDataTable<T> {
    columns: ITableColumn<T[]>[];
    data: T[];
    totalRows: number;
    selectableRows?: boolean;
    setSelectedRows?: (array: any[]) => any;
    loading?: boolean;
    onPageChangeCallback?: (event: any, page: number) => void;
    onSortChangeCallback?: (property: string, columnId: string) => void;
    onClickCallback?: (uniqueid: any) => void;
    onClickKey?: string;
    onClickCheckboxKey?: string;
    page?: number | undefined;
    pageSize?: number | undefined;
    handleChangeRowsPerPage?: (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => void;
    pagination?: any;
    totalColumn?: ITableTotalRow;
}

export interface ICustomBasicTable<T> {
    columns: any[];
    data: T[];
    totalRows: number;
    selectableRows?: boolean;
    setSelectedRows?: (array: any[]) => any;
    loading?: boolean;
}

export interface IBasicDataTable<T> {
    columns: any[];
    data: T[];
    totalRows: number;
    setSelectedRows?: (array: any[]) => any;
    loading?: boolean;
    onClickCallback?: (uniqueid: any) => void;
    onClickKey?: string;
}
