import { Stack, But<PERSON> } from "@vapor/react-material";
import { ExtendedTypography } from "@vapor/react-extended";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import { useLegacySwitch } from "../../store/LegacySwitchStore";

const CustomError404 = () => {
    const navigate = useNavigate();
    const { legacySwitch }: any = useLegacySwitch();
    const { t } = useTranslation();

    const handleClick = () => {
        const currentPath = window.location.pathname + window.location.search;
        if (!currentPath.includes("/legacy") && !legacySwitch) {
            navigate("/legacy" + currentPath);
        }
    };

    return (
        <Stack
            direction="column"
            justifyContent="center"
            alignItems="center"
            sx={{ p: "32px", gap: "16px", flex: "1" }}
        >
            <ExtendedTypography
                variant="display"
                gutterBottom
                component="div"
                color="primary.textTitleColor"
            >
                Ops!
            </ExtendedTypography>
            <ExtendedTypography
                variant="titleLarge"
                gutterBottom
                color="primary.textTitleColor"
            >
                {t("Questa area non è presente nella nuova modalità")}
            </ExtendedTypography>
            <ExtendedTypography
                variant="titleMedium"
                gutterBottom
                color="primary.textTitleColor"
            >
                {t("Prova a visualizzarla in modalità legacy")}
            </ExtendedTypography>
            <Button variant="contained" onClick={handleClick}>
                {t("Vai")}
            </Button>
        </Stack>
    );
};

export default CustomError404;
