import { useLocation, useParams } from "react-router-dom";

interface AgendaParams {
  id?: string;
  uniqueId?: string;
}

interface LocationState {
  items?: any[];
  prevPath?: string;
  rowDataUrl?: string;
  defaultParams?: any
  origin?: string;
  fileUniqueid?: string;
  uniqueId?: string;
  form?: any;
  fileHeaderData?: any;
  rowData?: any;
}

interface AgendaParamsReturn {
  paramId?: string;
  fileUniqueid?: string;
  rowDataUrl: string;
  items: any[];
  prevPath: string;
  origin: string;
  defaultParams: any,
  locationUniqueId: string,
  rowData: any,
  location: any
  fileHeaderData?: any;
}


export const useAgendaParams = (): AgendaParamsReturn => {
    const params = useParams();
    const { id, uniqueId } = params as AgendaParams;
    
    const location = useLocation();
    const state = location.state as LocationState | null;
    const items = state?.items || [];
    const prevPath = state?.prevPath || "/agenda/agenda";

    const rowDataUrl = state?.rowDataUrl || "agendaDeadline";

    const defaultParams = state?.defaultParams || {};

    const origin = state?.origin || "agenda";

    const idToSend = uniqueId || id;

    const fileUniqueid = state?.fileUniqueid;

    const fileHeaderData = state?.fileHeaderData;

    return { 
        paramId: idToSend, 
        fileUniqueid, 
        rowDataUrl, 
        items, 
        prevPath, 
        origin, 
        defaultParams,
        locationUniqueId: location.state?.uniqueId,
        rowData: state?.rowData,
        location,
        fileHeaderData
     };
}       