import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    IconButton,
    Typography,
    Box,
    Divider,
} from '@vapor/react-material';
import { Close } from '@mui/icons-material';
import { format } from 'date-fns';
// import { it } from 'date-fns/locale';
import { renderCalendarText } from '../helpers/calendarHelper';

interface IMoreEventsModalProps {
    open: boolean;
    onClose: () => void;
    events: any[];
    date: Date | null;
    query?: any;
    loggedUserCampiAgenda?: any;
    impegniMultiutente?: any;
    netlexSettingsFileId?: any;
}

export default function MoreEventsModal(props: IMoreEventsModalProps) {
    const {
        open,
        onClose,
        events,
        //TODO date, todo
        query,
        loggedUserCampiAgenda,
        impegniMultiutente,
        netlexSettingsFileId
    } = props;

    //TODO const formatDate = (date: Date) => {
    //TODO     return format(date, 'd MMMM yyyy', { locale: it });
    //TODO };

    const formatEventTime = (event: any) => {
        if (event.allDay) {
            return '';
        }
        if (event.start) {
            return format(new Date(event.start), 'HH:mm');
        }
        return '';
    };

    const getEventTypeColor = (type: string): { box: string, text: string, background: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: '#AD3A00',
                    text: '#521B00',
                    background: '#FFE0D1'
                };
            case "deadline":
                return {
                    box: '#53832D',
                    text: '#263D14',
                    background: '#E7F4DD'
                };
            case "polisweb":
                return {
                    box: '#2B8BA1',
                    text: '#0A3C47',
                    background: '#D7F3F9'
                };
            default:
                return {
                    box: '#757575',
                    text: '#424242',
                    background: '#F5F5F5'
                };
        }
    };

    const getEventDisplayData = (event: any) => {
        console.log('Event in modal:', event);

        let backgroundColor = event.backgroundColor || event.color;

        if (!backgroundColor) {
            const eventType = event.type || event.extendedProps?.type || 'default';
            const colors = getEventTypeColor(eventType);
            backgroundColor = colors.background;
        }

        if (query && loggedUserCampiAgenda !== undefined && impegniMultiutente !== undefined && netlexSettingsFileId !== undefined) {
            try {
                const eventInfo = {
                    event: {
                        _def: {
                            title: event.title,
                            extendedProps: event.extendedProps || event,
                            allDay: event.allDay
                        }
                    }
                };
                const renderData = renderCalendarText(
                    eventInfo,
                    query.calendarReferent,
                    loggedUserCampiAgenda,
                    impegniMultiutente,
                    netlexSettingsFileId
                );

                console.log('Background color used:', backgroundColor);

                return {
                    title: event.title,
                    time: formatEventTime(event),
                    backgroundColor: backgroundColor,
                    textColor: 'white',
                    icons: renderData.icons || '',
                    type: event.type || event.extendedProps?.type || 'default'
                };
            } catch (error) {
                console.warn('Error using centralized rendering:', error);
            }
        }

        return {
            title: event.title,
            time: formatEventTime(event),
            backgroundColor: backgroundColor,
            textColor: 'white',
            icons: '',
            type: event.type || event.extendedProps?.type || 'default'
        };
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="sm"
            aria-labelledby="more-events-dialog-title"
            PaperProps={{
                sx: {
                    width: '30vw',
                    maxWidth: '90vw',
                    maxHeight:"50vh"
                }
            }}
        >
            <DialogTitle id="more-events-dialog-title" sx={{ py: 1.5, px: 2 }}>
                    <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                        Eventi
                    </Typography>
                    <IconButton
                        aria-label="close"
                        onClick={onClose}
                        size="medium"
                    >
                        <Close fontSize="small" />
                    </IconButton>
            </DialogTitle>

            <Divider />

            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ width: '100%' }}>
                    {events.map((event, index) => {
                        const eventData = getEventDisplayData(event);
                        return (
                            <Box
                                key={event.id || index}
                                sx={{
                                    backgroundColor: eventData.backgroundColor || '#f5f5f5',
                                    color: 'black',
                                    p: 1.5,
                                    mb: index < events.length - 1 ? 0.5 : 0,
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5,
                                    cursor: 'pointer',
                                    minHeight: '40px',
                                    '&:hover': {
                                        opacity: 0.8,
                                    },
                                }}
                            >
                                {eventData.icons && (
                                    <Box
                                        sx={{ mr: 0.5 }}
                                        dangerouslySetInnerHTML={{ __html: eventData.icons }}
                                    />
                                )}



                                <Typography
                                    variant="body2"
                                    sx={{
                                        fontFamily: 'Roboto',
                                        fontWeight: 500,
                                        fontSize: '16px',
                                        lineHeight: '16px',
                                        letterSpacing: '0%',
                                        verticalAlign: 'middle',
                                        flex: 1
                                    }}
                                >
                                    {eventData.title}
                                </Typography>
                            </Box>
                        );
                    })}
                </Box>
            </DialogContent>

            <DialogActions sx={{ p: 1.5 }}>
                <Button onClick={onClose} variant="outlined" size="small">
                    Chiudi
                </Button>
            </DialogActions>
        </Dialog>
    );
}
