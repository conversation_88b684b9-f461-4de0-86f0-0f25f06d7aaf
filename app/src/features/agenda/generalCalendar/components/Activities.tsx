import { <PERSON>, Grid, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r, Tabs, Tab, TextField, Select, Button, MenuItem, FormControl, InputLabel } from "@vapor/react-material";
import ClearRoundedIcon from "@mui/icons-material/ClearRounded";
import React, { useEffect, useMemo, useState } from "react";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import { GridActionsCellItem, GridRowOrderChangeParams } from "@mui/x-data-grid-pro";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { CURRENT_DATE_FORMATTED } from "../addCalendar/impegno/constants/constant";
import { debounce } from "lodash";
interface IActivitiesProps {
    setRightPanelOpen: (open: boolean) => void;
    t: (key: string) => string;
    setShowAddForm: React.Dispatch<React.SetStateAction<boolean>>;
    showAddForm?: boolean;
    setOpenQuickImpegno?: any;
    setSavedFormData?: any;
    onActivityDeleted?: () => void; // Callback to refresh the activity list after deletion
}

export default function Activities(props: IActivitiesProps) {
    const { setRightPanelOpen, t, showAddForm, setShowAddForm, setOpenQuickImpegno, setSavedFormData, onActivityDeleted } = props;
    const [tab, setTab] = React.useState(0);
    const [activityTitle, setActivityTitle] = useState("");
    const [priority, setPriority] = useState<number>(3);
    const [list, setList] = useState<any>({
        rows: [],
        totalRows: 0,
        page: 1,
        pageSize: 5
    });

    const [completedList, setCompletedList] = useState<any>({
        rows: [],
        totalRows: 0,
        page: 1,
        pageSize: 5
    });
    const [loading, setLoading] = useState<boolean>(false);
    const [loadingCompleted, setLoadingCompleted] = useState<boolean>(false);
    const [query, setQuery] = useState<any>({
        page: 1,
        pageSize: 20,
        str: ""
    });

    const [queryCompleted, setQueryCompleted] = useState<any>({
        page: 1,
        pageSize: 20,
        str: ""
    });

    const [selectedRows, setSelectedRows] = useState<string[]>([]);

    const [showDeleteActivity, setShowDeleteActivity] = useState(false);
    const [activityToDelete, setActivityToDelete] = useState<string | null>(null);
    const [moveToTodoTaskId, setMoveToTodoTaskId] = useState<string | null>(null);
    const [editMode, setEditMode] = useState(false);
    const [editActivityId, setEditActivityId] = useState<string | null>(null);
    const [reorderActivityId, setReorderActivityId] = useState<string | null>(null);
    const [reorderActivityParams, setReorderActivityParams] = useState<GridRowOrderChangeParams | null>(null);

    // api's

    const addTodoRequest = usePostCustom("todos/create-todos?noTemplateVars=true");
    const todoListRequest = useGetCustom("todos/open?noTemplateVars=true");
    const todoSearchRequest = useGetCustom("todos/search?noTemplateVars=true");
    const todoSearchCompletedRequest = useGetCustom("todos/search-completed?noTemplateVars=true");
    const todoCompleteListRequest = useGetCustom("todos/list-completed?noTemplateVars=true");
    const todoCompleteRequest = usePostCustom("todos/complete-todo?noTemplateVars=true&task_id=" + selectedRows.join(","));
    const todoReopenRequest = usePostCustom("todos/reopen?noTemplateVars=true&task_id=" + moveToTodoTaskId);
    const todoEditRequest = usePostCustom("todos/edit-todo?noTemplateVars=true&task_id=" + editActivityId);
    const todoDeleteRequest = usePostCustom("todos/delete-todo?noTemplateVars=true");

    const todoOrderRequest = usePostCustom("todos/order?noTemplateVars=true&task_id=" + reorderActivityId);

    const columns: any = useMemo(
        () => [
            { field: "description", headerName: "Activity", width: 200, sortable: false },
            {
                field: "priority",
                headerName: "",
                renderHeader: () => (
                    <div>
                        <OutlinedFlagIcon sx={{ color: "#0076AD", fontSize: 22 }} />
                    </div>
                ),
                width: 60,
                sortable: false,
                renderCell: (params: any) => <FlagIcon sx={{ color: priorityFlagColor(params.row.priority), fontSize: 22 }} />
            },
            {
                field: "actions",
                type: "actions",
                width: 80,
                getActions: (params: any) => [
                    <GridActionsCellItem label="Modifica" onClick={() => openEditForm(params.row)} showInMenu />,
                    <GridActionsCellItem label="Trasforma in impegno" onClick={() => transformToImpegno(params.row)} showInMenu />,
                    <GridActionsCellItem label="Elimina" onClick={() => deleteActivity(params.row.id)} showInMenu />
                ]
            }
        ],
        [t]
    );

    const completedColumns: any = useMemo(
        () => [
            { field: "description", headerName: "Attività completate", width: 250, sortable: false },
            {
                field: "actions",
                type: "actions",
                width: 80,
                getActions: (params: any) => [
                    <GridActionsCellItem label="Sposta in Da fare" onClick={() => spostaInDaFare(params.row.id)} showInMenu />,
                    <GridActionsCellItem label="Trasforma in impegno" onClick={() => transformToImpegno(params.row)} showInMenu />,
                    <GridActionsCellItem label="Elimina" onClick={() => deleteActivity(params.row.id)} showInMenu />
                ]
            }
        ],
        [t]
    );

    const openEditForm = (activity: any) => {
        setActivityTitle(activity.description);
        setPriority(activity.priority);
        setEditActivityId(activity.id);
        setEditMode(true);
        setShowAddForm(true);
    };
    const fetchTodoList = async () => {
        try {
            setLoading(true);
            const { data }: any = await todoListRequest.doFetch(true, query);
            setList((prevList: any) => ({
                ...prevList,
                rows: data.todos || [],
                totalRows: data.total || 0,
                page: data.page || 0,
                pageSize: data.per_page || 5
            }));

            setLoading(false);
        } catch (error) {
            console.error("Error fetching todo list:", error);
        }
    };

    const fetchCompletedTodoList = async () => {
        try {
            setLoadingCompleted(true);
            const { data }: any = await todoCompleteListRequest.doFetch(true, queryCompleted);
            setCompletedList((prevList: any) => ({
                ...prevList,
                rows: data.todos || [],
                totalRows: data.total || 0,
                page: data.page || 0,
                pageSize: data.per_page || 5
            }));

            setLoadingCompleted(false);
        } catch (error) {
            console.error("Error fetching todo list:", error);
        }
    };
    useEffect(() => {
        fetchTodoList();
        fetchCompletedTodoList();
    }, []);

    const handleClose = () => {
        setRightPanelOpen(false);
    };

    const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
        setTab(newValue);
    };

    const priorityFlagColor = (priority: string) => {
        if (priority === "1") return "#f44336"; // Red
        if (priority === "3") return "#ffb300"; // Yellow
        return "#43a047"; // Green
    };

    const spostaInDaFare = (id: string) => {
        setMoveToTodoTaskId(id);
    };

    useEffect(() => {
        if (moveToTodoTaskId) {
            todoReopenRequest
                .doFetch(true)
                .then(() => {
                    setMoveToTodoTaskId(null);
                    fetchTodoList();
                    fetchCompletedTodoList();
                })
                .catch((error) => {
                    console.error("Error moving activity to todo:", error);
                });
        }
    }, [moveToTodoTaskId]);
    const deleteActivity = (id: string) => {
        setActivityToDelete(id);
        setShowDeleteActivity(true);
    };

    const handleConfirmDelete = async () => {
        if (activityToDelete) {
            await todoDeleteRequest.doFetch(true, { task_id: activityToDelete });
            setShowDeleteActivity(false);
            setActivityToDelete(null);
            fetchTodoList();
            fetchCompletedTodoList();
        }
    };

    // Function to silently delete an activity (used after successful engagement creation)
    const deleteActivitySilently = async (activityId: string) => {
        try {
            await todoDeleteRequest.doFetch(true, { task_id: activityId });
            fetchTodoList();
            fetchCompletedTodoList();
            if (onActivityDeleted) {
                onActivityDeleted();
            }
            return true;
        } catch (error) {
            console.error("Error deleting activity:", error);
            return false;
        }
    };

    // Make the delete function available in savedFormData
    const transformToImpegno = (row: any) => {
        if (setOpenQuickImpegno) {
            const now = new Date();

            setSavedFormData({
                deadlineText: row.description,
                deadlineDate: CURRENT_DATE_FORMATTED(),
                deadlineHours: String(now.getHours()).padStart(2, "0"),
                deadlineMinutes: String(now.getMinutes()).padStart(2, "0"),
                deadlinePeriod: "30",
                activityId: row.id, // Add the activity ID to delete after successful engagement creation
                deleteActivityFunction: deleteActivitySilently // Pass the delete function
            });
            setOpenQuickImpegno({ state: true, view: "" });
        }
    };

    const completeActivity = (selectedRows: any) => {
        setSelectedRows(selectedRows);
    };

    useEffect(() => {
        if (selectedRows.length > 0) {
            todoCompleteRequest
                .doFetch(true, { task_id: selectedRows.join(",") }, "post", "json", true)
                .then(() => {
                    setSelectedRows([]);
                    fetchTodoList();
                    fetchCompletedTodoList();
                })
                .catch((error) => {
                    console.error("Error completing activities:", error);
                });
        }
    }, [selectedRows]);

    const saveActivity = async () => {
        const data = {
            description: activityTitle,
            priority: priority
        };

        if (editMode && editActivityId) {
            await todoEditRequest.doFetch(true, { ...data }, "post", "json", true);
        } else {
            await addTodoRequest.doFetch(true, data, "post", "json", true);
        }
        setActivityTitle("");
        setPriority(3);
        setShowAddForm(false);
        setEditMode(false);
        setEditActivityId(null);
        fetchTodoList();
        fetchCompletedTodoList();
    };

    const handleRowOrderChange = async (params: GridRowOrderChangeParams) => {
        setReorderActivityId(params.row.id);
        setReorderActivityParams(params);
    };

    useEffect(() => {
        if (reorderActivityId && reorderActivityParams) {
            handleReorderActivity();
        }
    }, [reorderActivityId, reorderActivityParams]);

    const handleReorderActivity = async () => {
        if (reorderActivityParams === null) return;
        const currentRows = [...list.rows];

        const movedItem = currentRows.splice(reorderActivityParams.oldIndex, 1)[0];

        currentRows.splice(reorderActivityParams.targetIndex, 0, movedItem);

        const beforeId = reorderActivityParams.targetIndex > 0 ? currentRows[reorderActivityParams.targetIndex - 1]?.id : null;

        const afterId = reorderActivityParams.targetIndex < currentRows.length - 1 ? currentRows[reorderActivityParams.targetIndex + 1]?.id : null;

        await todoOrderRequest.doFetch(
            true,
            {
                before_id: beforeId,
                after_id: afterId,
                task_id: reorderActivityParams.row.id
            },
            "post",
            "json",
            true
        );
        setReorderActivityId(null);
        setReorderActivityParams(null);

        fetchTodoList();
    };
    const rowSelectableCustom = () => {
        return true;
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="activitysList"
                columns={columns}
                data={list.rows || []}
                totalRows={list.totalRows}
                loading={loading}
                selectableRows={true}
                onRowOrderChange={handleRowOrderChange}
                onClickKey="id"
                onRowSelectionModelChange={(selectedRows: any) => completeActivity(selectedRows)}
                hasAdditionaStyles={false}
                page={0}
                pageSize={0}
                rowReordering={true}
                additionStyles={{ border: "none", width: "100%" }}
                hideFooter={true}
                isRowSelectableCustom={rowSelectableCustom}
            />
        );
    };

    const renderCompletedDataTable = () => {
        return (
            <CustomDataGrid
                name="activitysListCompleted"
                columns={completedColumns}
                data={completedList.rows || []}
                totalRows={completedList.totalRows}
                loading={loadingCompleted}
                page={0}
                pageSize={0}
                onClickKey="id"
                hasAdditionaStyles={false}
                additionStyles={{ border: "none", width: "100%" }}
                hideFooter={true}
            />
        );
    };

    const resetForm = () => {
        setActivityTitle("");
        setPriority(3);
        setShowAddForm(false);
        setEditMode(false);
        setEditActivityId(null);
    };

    const searchTodoList = async () => {
        try {
            setLoading(true);
            const { data }: any = await todoSearchRequest.doFetch(true, query);
            setList((prevList: any) => ({
                ...prevList,
                rows: data.todos || [],
                totalRows: data.total || 0,
                page: data.page || 0,
                pageSize: data.per_page || 5
            }));
            setLoading(false);
        } catch (error) {
            console.error("Error searching todo list:", error);
        }
    };

    const searchCompletedTodoList = async () => {
        try {
            setLoading(true);
            const { data }: any = await todoSearchCompletedRequest.doFetch(true, queryCompleted);
            setCompletedList((prevList: any) => ({
                ...prevList,
                rows: data.todos || [],
                totalRows: data.total || 0,
                page: data.page || 0,
                pageSize: data.per_page || 5
            }));
            setLoading(false);
        } catch (error) {
            console.error("Error searching todo list:", error);
        }
    };

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            searchTodoList();
        }, 500);
        query.str && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [query.str]);

    useEffect(() => {
        const debouncedSearchCompleted = debounce(() => {
            searchCompletedTodoList();
        }, 500);
        queryCompleted.str && debouncedSearchCompleted();
        return () => {
            debouncedSearchCompleted.cancel();
        };
    }, [queryCompleted.str]);
    return (
        <Box sx={{ p: 0.5 }}>
            <ConfirmModal
                open={showDeleteActivity}
                agree={t("Conferma")}
                decline={t("Annulla")}
                title={t("Elimina attività")}
                handleAgree={handleConfirmDelete}
                handleDecline={() => {
                    setShowDeleteActivity(false);
                    setActivityToDelete(null);
                }}
                confirmText={t("Stai per eliminare un’attività. Vuoi procedere con l’eliminazione?")}
            />
            <Grid container spacing={2}>
                <Grid item xs={12} sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Typography sx={{ fontSize: "22px", fontWeight: "bold" }}>{t("Attività")}</Typography>
                    <ClearRoundedIcon onClick={() => handleClose()} fontSize="medium" sx={{ color: "#008fd6" }} />
                </Grid>

                <Grid item xs={12}>
                    <Divider />
                </Grid>

                <Grid item xs={12}>
                    <Tabs
                        value={tab}
                        onChange={handleTabChange}
                        sx={{
                            "& .MuiTabs-indicator": {
                                display: "none"
                            },
                            "& .MuiTab-root": {
                                color: "#0076AD",
                                backgroundColor: "transparent",
                                borderRadius: "4px 4px 0 0",
                                marginRight: "4px",
                                minHeight: "40px",
                                "&.Mui-selected": {
                                    color: "#fff",
                                    backgroundColor: "#0076AD"
                                },
                                "&:hover": {
                                    backgroundColor: "#f5f5f5",
                                    "&.Mui-selected": {
                                        backgroundColor: "#0076AD"
                                    }
                                }
                            }
                        }}
                    >
                        <Tab label={t("Da fare")} />
                        <Tab label={t("Completate")} />
                    </Tabs>
                </Grid>

                <Grid item xs={12}>
                    {tab === 0 && (
                        <>
                            {!showAddForm && (
                                <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
                                    <TextField
                                        variant="outlined"
                                        placeholder={t("Cerca attività")}
                                        value={query.str}
                                        onChange={(e) =>
                                            setQuery((query: any) => {
                                                return {
                                                    ...query,
                                                    str: e.target.value,
                                                    page: 1 // Reset to first page on search
                                                };
                                            })
                                        }
                                        sx={{ flexGrow: 1 }}
                                    />
                                    {!showAddForm && (
                                        <Typography
                                            onClick={() => setShowAddForm(true)}
                                            sx={{
                                                color: "#1976d2",
                                                textDecoration: "underline",
                                                cursor: "pointer",
                                                whiteSpace: "nowrap",
                                                "&:hover": {
                                                    textDecoration: "underline",
                                                    opacity: 0.8
                                                }
                                            }}
                                        >
                                            {t("Aggiungi attività")}
                                        </Typography>
                                    )}
                                </Box>
                            )}
                            {showAddForm && (
                                <>
                                    <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2, p: 2, border: "1px solid #e0e0e0", borderRadius: 2, background: "#fafbfc" }}>
                                        <TextField variant="outlined" label={t("Titolo nuova attività")} value={activityTitle} onChange={(e) => setActivityTitle(e.target.value)} size="small" />
                                        <FormControl variant="outlined" sx={{ minWidth: 120 }}>
                                            <InputLabel id="priority-label">{t("Priorità")}</InputLabel>
                                            <Select labelId="priority-label" variant="outlined" label={t("Priorità")} value={priority} onChange={(e: any) => setPriority(e.target.value)} size="small">
                                                <MenuItem value={1}>{t("Alta")}</MenuItem>
                                                <MenuItem value={3}>{t("Media")}</MenuItem>
                                                <MenuItem value={5}>{t("Bassa")}</MenuItem>
                                            </Select>
                                        </FormControl>
                                    </Box>
                                    <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mb: 2 }}>
                                        <Button variant="outlined" onClick={() => resetForm()}>
                                            {t("Annulla")}
                                        </Button>
                                        <Button variant="contained" disabled={activityTitle == ""} onClick={() => saveActivity()}>
                                            {editMode ? t("Salva modifiche") : t("Salva")}
                                        </Button>
                                    </Box>
                                </>
                            )}
                            <Box
                                sx={{
                                    height: "calc(100vh - 400px)",
                                    overflowY: "auto",
                                    "&::-webkit-scrollbar": {
                                        display: "none"
                                    },
                                    "-ms-overflow-style": "none",
                                    "scrollbar-width": "none"
                                }}
                            >
                                {renderDataTable()}
                            </Box>
                        </>
                    )}
                    {tab === 1 && (
                        <>
                            <TextField
                                fullWidth
                                variant="outlined"
                                placeholder={t("Cerca attività")}
                                value={queryCompleted.str}
                                onChange={(e) =>
                                    setQueryCompleted((query: any) => {
                                        return {
                                            ...query,
                                            str: e.target.value,
                                            page: 1 // Reset to first page on search
                                        };
                                    })
                                }
                                sx={{ mb: 2 }}
                            />
                            <Box
                                sx={{
                                    height: "calc(100vh - 400px)",
                                    overflowY: "auto",
                                    "&::-webkit-scrollbar": {
                                        display: "none"
                                    },
                                    "-ms-overflow-style": "none",
                                    "scrollbar-width": "none"
                                }}
                            >
                                {renderCompletedDataTable()}
                            </Box>
                        </>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
}
