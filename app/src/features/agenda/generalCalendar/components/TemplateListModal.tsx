import { useState } from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
    Button,
    EmptyState,
    Divider,
    IconButton,
    Radio
} from "@vapor/v3-components";
import { CircularProgress } from "@vapor/v3-components";
import { useTranslation } from "@1f/react-sdk";
import type { IPrintTemplatesRespose } from "../typings/generalCalendar.interface";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";

interface TemplateListModalProps {
    open: boolean;
    onClose: () => void;
    templates: IPrintTemplatesRespose[];
    loading: boolean;
    onConfirm: (selectedTemplateId: string) => void;
}

export const TemplateListModal = ({
    open,
    onClose,
    templates,
    loading,
    onConfirm,
}: TemplateListModalProps) => {
    const { t } = useTranslation();
    const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

    const handleClose = () => {
        setSelectedTemplate(null);
        onClose();
    };

    const handleConfirm = () => {
        if (selectedTemplate) {
            onConfirm(selectedTemplate);
            setSelectedTemplate(null);
        }
    };

    return (
        <Dialog open={open} maxWidth="md" fullWidth>
            <DialogTitle
                sx={{
                    fontFamily: 'Cairo',
                    fontWeight: 700,
                    fontStyle: 'normal',
                    fontSize: '24px',
                    lineHeight: '150%',
                    letterSpacing: '0%',
                    verticalAlign: 'middle'
                }}
            >
                {t("Scegli il template da utilizzare")}
            <IconButton onClick={handleClose}><FontAwesomeIcon icon={faTimes} /></IconButton>
            </DialogTitle>
            <Divider variant="fullWidth"></Divider>
            <DialogContent style={{minHeight:400}}>
                {loading ? (
                    <CircularProgress />
                ) : templates && templates.length > 0 ? (
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell></TableCell>
                                <TableCell>{t("Nome")}</TableCell>
                                <TableCell>{t("Descrizione")}</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {templates.map((template) => (
                                <TableRow key={template.id}>
                                    <TableCell>
                                        <Radio
                                            checked={selectedTemplate === template.id}
                                            onChange={() => setSelectedTemplate(template.id)}
                                        />
                                    </TableCell>
                                    <TableCell>{template.filename}</TableCell>
                                    <TableCell>{template.title}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                ) : (
                    <EmptyState
                        title={t("Nessun template disponibile")}
                        description={t("Non ci sono template disponibili al momento")}
                    />
                )}
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClose} variant="outlined">
                    {t("Annulla")}
                </Button>
                <Button 
                    variant="contained" 
                    onClick={handleConfirm}
                    disabled={!selectedTemplate}
                >
                    {t("Conferma")}
                </Button>
            </DialogActions>
        </Dialog>
    );
};
