import {
    Box,
    Grid,
    Typography,
    Divider
} from "@vapor/react-material";
import { Divider as <PERSON><PERSON><PERSON><PERSON><PERSON>, Too<PERSON><PERSON> } from "@mui/material";
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faRepeat } from "@fortawesome/free-solid-svg-icons";
// import { faLock } from "@fortawesome/pro-regular-svg-icons";
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';

interface IDayEventsProps {
    selectedDate: Date | null;
    events: any[];
    setRightPanelOpen: (open: boolean) => void;
    t: (key: string) => string;
}

export default function DayEvents(props: IDayEventsProps) {
    const { selectedDate, events, setRightPanelOpen, t } = props;

    const handleClose = () => {
        setRightPanelOpen(false);
    };

    const formatEventTime = (date: Date) => {
        return format(date, 'HH:mm');
    };

    const formatDateWithCapital = (date: Date) => {
        const formatted = format(date, 'EEE d', { locale: it });
        return formatted.charAt(0).toUpperCase() + formatted.slice(1);
    };

    const getEventTypeColor = (type: string): { box: string, text: string } => {
        switch (type) {
            case "hearing":
                return {
                    box: '#AD3A00',
                    text: '#521B00'
                };
            case "deadline":
                return {
                    box: '#51822B',
                    text: '#263D14'
                };
            case "polisweb":
                return {
                    box: '#2196F3',
                    text: '#0A3C47'
                };
            default:
                return {
                    box: '#757575',
                    text: '#424242'
                };
        }
    };

    const truncateText = (text: string, maxLength: number) => {
        if (text && text.length > maxLength) {
            return text.slice(0, maxLength) + '...';
        }
        return text;
    };

    const formatDuration = (durata: string) => {
        const minutes = parseInt(durata);
        if (minutes >= 60) {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return remainingMinutes > 0 ? `${hours} ore ${remainingMinutes} minuti` : `${hours} ore`;
        }
        return `${durata} min`;
    };

    return (
        <Box sx={{ p: 0.5, height: '80vh' }}>
            <Grid container spacing={2}>
                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography sx={{ fontSize: '22px', fontWeight: 'bold' }}>
                        {selectedDate && formatDateWithCapital(selectedDate)}
                    </Typography>
                    <ClearRoundedIcon onClick={handleClose} fontSize="medium" sx={{ color: '#008fd6' }} />
                </Grid>

                <Grid item xs={12}>
                    <Divider />
                </Grid>

                <Grid item xs={12} sx={{
                    maxHeight: 'calc(80vh - 80px)',
                    overflowY: 'auto',
                    pr: 1,
                    '&::-webkit-scrollbar': {
                        width: '8px',
                    },
                    '&::-webkit-scrollbar-track': {
                        background: '#f1f1f1',
                        borderRadius: '4px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                        background: '#888',
                        borderRadius: '4px',
                        '&:hover': {
                            background: '#555',
                        },
                    },
                }}>
                    {events.map((event, index) => (
                        <>
                            <Box
                                key={event.id || index}
                                sx={{
                                    position: 'relative',
                                    mb: 0.5,
                                    p: 1.5,
                                    height: '100px',
                                    borderRadius: 1,
                                    backgroundColor: 'white',
                                    borderLeft: `4px solid ${getEventTypeColor(event.type).box}`,
                                    borderTopLeftRadius: 4,
                                    borderBottomLeftRadius: 4,
                                    '&:hover': {
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                    },
                                    ...(event.important == "1" && {
                                        '&::after': {
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            width: 0,
                                            height: 0,
                                            borderStyle: 'solid',
                                            borderWidth: '0 15px 15px 0',
                                            borderColor: 'transparent #D10000 transparent transparent',
                                        }
                                    })
                                }}
                            >
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 8,
                                        right: 20,
                                        display: 'flex',
                                        gap: '4px',
                                        zIndex: 1
                                    }}
                                >
                                    {/* <FontAwesomeIcon icon={faRepeat} style={{ fontSize: 18, color: getEventTypeColor(event.type).text }} />
                                    <FontAwesomeIcon icon={faLock} style={{ fontSize: 18, color: getEventTypeColor(event.type).text }} /> */}
                                </Box>

                                <Grid container>
                                    <Grid item xs={2.5}>
                                        <Typography color="textSecondary" sx={{ fontSize: '13px', color: getEventTypeColor(event.type).text }}>
                                            {formatEventTime(new Date(event.start))}
                                        </Typography>
                                        <Typography color="textSecondary" sx={{ fontSize: '13px', color: getEventTypeColor(event.type).text, pt: 0.2, opacity: 0.5 }}>
                                            {(() => {
                                                const formattedDuration = formatDuration(event.durata);
                                                return formattedDuration.length > 6 ? (
                                                    <Tooltip title={formattedDuration} arrow>
                                                        <span>{formattedDuration.slice(0, 6)} ...</span>
                                                    </Tooltip>
                                                ) : formattedDuration;
                                            })()}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={9.5}>
                                        <Typography sx={{ fontWeight: 'bold', fontSize: '15px', color: getEventTypeColor(event.type).text }}>
                                            {truncateText(event.title, 20)}
                                        </Typography>
                                        {event.autorita && (
                                            <Typography color="textSecondary" sx={{ fontSize: '13px', color: getEventTypeColor(event.type).text }}>
                                                {truncateText(event.autorita, 30)}
                                            </Typography>
                                        )}
                                    </Grid>
                                    <Grid item xs={12}>
                                        <Box sx={{ display: 'flex', justifyContent: 'flex-start', pt: 1 }}>
                                            <Typography color="textSecondary" sx={{
                                                bgcolor: getEventTypeColor(event.type).box,
                                                fontSize: '15px',
                                                padding: '2px 8px',
                                                borderRadius: '4px',
                                                color: 'white',
                                            }}>
                                                {event.type === "deadline" ? event.tipologia :
                                                    event.type === "hearing" ? "udienza" :
                                                        event.type === "polisweb" ? "generico" :
                                                            event.type}
                                            </Typography>
                                        </Box>
                                    </Grid>
                                </Grid>
                            </Box>
                            <MuiDivider sx={{
                                my: 1,
                            }} />
                        </>
                    ))}

                    {events.length === 0 && (
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '100%',
                            pt: 4
                        }}>
                            <FormatListBulletedIcon
                                sx={{
                                    fontSize: 45,
                                    mb: 3,
                                    mt: 2
                                }}
                            />
                            <Typography
                                variant="body1"
                                sx={{
                                    fontWeight: "bold",
                                    textAlign: 'center',
                                    fontSize: '18px'
                                }}
                            >
                                {t("Nessun evento da visualizzare")}
                            </Typography>
                        </Box>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
} 