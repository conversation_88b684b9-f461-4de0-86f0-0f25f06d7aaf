import useGetCustom from "../../../../../../hooks/useGetCustom";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useEffect, useState, useCallback } from "react";
import { getAnagraficheGrid } from "../customGrids/grids";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { debounce } from "lodash";
import { DEADLINE_UNIQUE_RANDOM } from "../constants/constant";
import { useCalendarData } from "../context/CalendarDataContext";

// DEAFULT FOR CREATE
const INITIAL_QUERY = {
    nuovoImpegno: "1", // true for creation form
    deadlineUniqueid: DEADLINE_UNIQUE_RANDOM,
    anagraficeAllegateFilter: "1",
    page: 0,
    pageSize: 9,
    sortColumn: "data",
};

export default function useAnagrafiche() {
    const { t } = useTranslation();
    const { isUserEditing, deadlineSaveParams } = useCalendarData();
    const getAnagraficheRequest = useGetCustom(
        "anagrafiche/list?noTemplateVars=true"
    );
    const searchAnagraficheRequest = useGetCustom(
        "default/anagrafiche/search?noTemplateVars=true"
    );
    const insertAnagraficheImpegniRequest = usePostCustom(
        "anagrafiche/insert-anagrafiche-impegni?noTemplateVars=true"
    );
    const deleteAnagraficheImpegniRequest = usePostCustom(
        "anagrafiche/delete-anagrafiche-impegni?noTemplateVars=true"
    );

    const [listAnagrafiche, setListAnagrafiche] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [anagraficheQuery, setAnagraficheQuery] =
        useState<any>(INITIAL_QUERY);
    const [anagraficheResult, setAnagraficheResult] = useState<any>([]);
    const [selectedAnagrafica, setSelectedAnagrafica] = useState<string | null>(
        ""
    );
    const [insertAnagraficheParams, setInsertAnagraficheParams] = useState<any>(
        []
    );

    useEffect(() => {
        if (isUserEditing) {
            setAnagraficheQuery((prev: any) => ({
                ...prev,
                nuovoImpegno: "0", //means is on update
                deadlineUniqueid: deadlineSaveParams.deadlineUniqueid,
            }));
        }
    }, [isUserEditing, deadlineSaveParams.deadlineUniqueid]);

    const fetchAnagraficheData = async () => {
        const [columns, response]: any = await Promise.all([
            getAnagraficheGrid(
                t,
                !isUserEditing
                    ? handleRemoveAnagraficheOnCreate
                    : handleRemoveAnagraficheOnUpdate,
                isUserEditing
            ),
            getAnagraficheRequest.doFetch(true, anagraficheQuery),
        ]);
        const { currentPage, totalRows } = response.data;

        setListAnagrafiche({
            ...listAnagrafiche,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: anagraficheQuery?.page,
            pageSize: anagraficheQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchAnagraficheData();
    }, [anagraficheQuery, t]);

    //search functions
    const debounceSearchImpegno = debounce(async (value: string) => {
        if (value === "") return;
        let params = {
            q: value,
        };
        const response: any = await searchAnagraficheRequest.doFetch(
            true,
            params
        );
        setAnagraficheResult(response.data);
    }, 500);

    const memoizedSearchPractica = useCallback(debounceSearchImpegno, [
        debounceSearchImpegno,
    ]);

    const handleAnagraficheSearch = (value: any) => {
        memoizedSearchPractica(value);
    };

    const handleAddAnagraficheOnCreate = () => {
        if (selectedAnagrafica) {
            const updatedRows = [...listAnagrafiche.rows, selectedAnagrafica];
            setListAnagrafiche({
                ...listAnagrafiche,
                rows: updatedRows,
                totalRows: listAnagrafiche.totalRows + 1,
            });
            setSelectedAnagrafica(null);
        }
    };

    const handleAddAnagraficheOnUpdate = async () => {
        const params = {
            anagrafica: insertAnagraficheParams[0].uniqueid,
            deadlineFileUniqueid: deadlineSaveParams.deadlineFileUniqueid,
            deadlineUniqueid: deadlineSaveParams.deadlineUniqueid,
            nuovoImpegno: "0",
        };

        const response: any = await insertAnagraficheImpegniRequest.doFetch(
            true,
            params
        );
        if (response) {
            setInsertAnagraficheParams([]); //to default
            setSelectedAnagrafica("");
            fetchAnagraficheData();
        }
    };

    const handleRemoveAnagraficheOnCreate = (id: any) => {
        // Ensure working with the latest state by using the functional form to prevent stale state issues
        setListAnagrafiche((prevList: any) => {
            console.log(prevList)
            const updatedRows = prevList.rows.filter(
                (row: any) => row.id !== id
            );
            return {
                ...prevList,
                rows: updatedRows,
                totalRows: updatedRows.length,
            };
        });

        setSelectedAnagrafica(null);
    };

    const handleRemoveAnagraficheOnUpdate = async (anagraficheId: string) => {
        const params = {
            anagrafichaUniqueid: anagraficheId,
            nuovoImpegno: "0",
            deadlineUniqueid: deadlineSaveParams.deadlineUniqueid,
        };

        const response = await deleteAnagraficheImpegniRequest.doFetch(
            true,
            params
        );
        if (response) {
            fetchAnagraficheData();
        }
    };

    const insertAnagrafiche = async (idScadenzario: string) => {
        const formData = new FormData();
        listAnagrafiche.rows.forEach((row: any) => {
            if (row.uniqueid) {
                formData.append("uids[]", row.uniqueid);
            }
        });
        formData.append("idScadenzario", idScadenzario);
        const response: any = await insertAnagraficheImpegniRequest.doFetch(
            true,
            formData
        );

        return response.data;
    };

    return {
        INITIAL_QUERY,
        anagraficheQuery,
        setAnagraficheQuery,
        listAnagrafiche,
        setListAnagrafiche,
        anagraficheListLoading: getAnagraficheRequest.loading,
        fetchAnagraficheData,
        handleAnagraficheSearch,
        handleAddAnagraficheOnUpdate,
        anagraficheResult,
        searchAnagraficheLoading: searchAnagraficheRequest.loading,
        selectedAnagrafica,
        setSelectedAnagrafica,
        handleAddAnagraficheOnCreate,
        insertAnagrafiche,
        insertAnagraficheParams,
        setInsertAnagraficheParams,
    };
}
