import usePostCustom from "../../../../../../hooks/usePostCustom";

export default function useCreateFromDeadline() {
    const createDeadlineRequest = usePostCustom(
        "timesheet/create-from-deadline?noTemplateVars=true"
    );

    const createFromDeadline = async (
        deadlineUniqueid: string,
        connect: boolean
    ) => {
        await createDeadlineRequest.doFetch(true, {
            deadlineUniqueid,
            connect,
        });
    };
    return { createFromDeadline };
}
