import { useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON>,
    Icon<PERSON>utton,
    Divider,
    <PERSON>,
    Breadcrumbs,
    Hyperlink,
    Stack,
    Typography,
    AnchorMenu,
    AnchorMenuItem,
} from "@vapor/v3-components";
import { Dialog, DialogContent, DialogActions } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faCalendar,
    faClose,
    faArrowAltLeft,
    faArrowLeftFromLine,
} from "@fortawesome/pro-regular-svg-icons";
import NewSchedaImpegno from "./sections/newSchedaImpegno";
import FullCalendarDayView from "./components/FullCalendarDayView";
// import Documenti from "./sections/documenti";
// import DatiAggiuntivi from "./sections/datiAggiuntivi";
import Modelli from "./sections/modelli";
import ImpegniCollegati from "./sections/impegniCollegati";
import useSearchPractica from "./hooks/useSearchPractica";
import useSearchImpegno from "./hooks/useSearchImpegno";
import useSaveDeadlines from "./hooks/useSaveDeadlines";
import useCreateFromDeadline from "./hooks/useCreateFromDeadline";
import useSessionStorageState from "./hooks/useSessionStorageState";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import SpinnerButton from "../../../../../custom-components/SpinnerButton";
import { useCalendarData } from "./context/CalendarDataContext";
// import useFetchDocuments from "./hooks/useFetchDocuments";
// import useFetchDatiAggiuntivi from "./hooks/useFetchDatiAggiuntivi";
import { useTranslation } from "@1f/react-sdk";

interface IProps {
    open: boolean;
    onClose: () => void;
    fetchEventData: (query?: any) => Promise<void>;
    query: any;
}

export default function NewImpegnoIndexModal(props: IProps) {
    const { open, onClose, fetchEventData, query } = props;
    const [tabId, setTabId] = useState<any>("generali");
    const [calendarOpen, setCalendarOpen] = useState(false);
    const [giornataSwitch, setGiornataSwitch] = useState(false);
    const handleToggleCalendar = () => {
        setCalendarOpen((prev) => !prev);
    };
    const { t } = useTranslation();
    const [showModal, setShowModal] = useState<any>({
        open: false,
        title: "",
        confirmText: "",
        deleteFunc: () => "",
    });
    const [showSaveFormModal, setShowSaveFormModal] = useState<boolean>(false);
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [saveFromDeadlineLoading, seFromDeadlineLoading] =
        useState<boolean>(false);
    const {
        data, //its needed on index
        fetchUserGroupById,
        loggedUserName,
        fetchCalendarData,
        deadlineSaveParams,
        setDeadlineSaveParams,
        selectedPraticaName,
        setSelectedPraticaName,
        isExternal,
    } = useCalendarData();
    const {
        practicaSearchResult,
        handlePracticaSearch,
        setPracticaSearchResult,
        searchPracticaLoading,
        setIsArchiveChecked,
    } = useSearchPractica();
    const { impegnoSearchResult, handleImpegnoSearch, searchImpegnoLoading } =
        useSearchImpegno();
    const { saveDeadline, requiredFields, setRequiredFields } =
        useSaveDeadlines();
    const { createFromDeadline } = useCreateFromDeadline();
    const { clearSessionStorage } = useSessionStorageState();
    // const {
    //     documentsQuery,
    //     setDocumentsQuery,
    //     listDocuments,
    //     loadingDocuments,
    //     LINK_DOCUMENT,
    // } = useFetchDocuments();
    // const {
    //     fetchDatiAggiuntivi,
    //     connect,
    //     selectedItem,
    //     setSelectedItem,
    //     localParentItemId,
    //     setLocalParentItemId,
    //     toggleItemConnection,
    //     loadingDatiAggiuntivi,
    // } = useFetchDatiAggiuntivi();

    const [showInstrumentsMenu, setShowInstrumentsMenu] =
        useState<boolean>(false);

    const [showModalOverlapping, setShowModalOverlapping] =
        useState<boolean>(false);
    const [confirmText, setConfirmText] = useState<any>([]);

    const handleModalCloseState = () => {
        console.log("is this called?");
        onClose();
    };

    const handleSaveAction = async (controlOverlapping: boolean = true) => {
        try {
            setSaveLoading(true);
            const result = await saveDeadline(
                deadlineSaveParams,
                false,
                controlOverlapping
            );

            if (result && result.overlappingDeadlines) {
                const confirmTextArray = [
                    ...result.data,
                    "",
                    "Vuoi continuare comunque?",
                ];
                setConfirmText(confirmTextArray);
                setShowModalOverlapping(true);
                return;
            }
            const idScadenzario: any = result;

            if (idScadenzario) {
                clearSessionStorage();
                await fetchEventData(query);
                handleModalCloseState();
            }
        } catch (error: any) {
            console.error("Error during save action:", error);
        } finally {
            setSaveLoading(false);
        }
    };

    const handleCreateFromDeadline = async (connect: boolean) => {
        seFromDeadlineLoading(true);
        try {
            const deadlineUniqueid = await saveDeadline(
                deadlineSaveParams,
                true
            );
            if (deadlineUniqueid) {
                const finalId =
                    deadlineUniqueid === true
                        ? deadlineSaveParams.deadlineUniqueid
                        : deadlineUniqueid;

                await createFromDeadline(finalId, connect);
                clearSessionStorage();
                // Add the ID as a query parameter when navigating back to the impegni section
                handleModalCloseState(); //close modal full impegno
            }
        } catch (error: any) {
            console.error("Error during save action", error);
        } finally {
            seFromDeadlineLoading(false);
        }
    };

    const handleModalConfirm = () => {
        handleSaveAction(false);
    };

    const handleDecline = () => {
        setShowModalOverlapping(false);
    };

    return (
        <Dialog
            open={open}
            onClose={() => handleModalCloseState()}
            fullScreen
            PaperProps={{
                sx: { borderRadius: "0 !important" },
            }}
        >
            {/* <DialogTitle> */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mt: 1,
                    ml: 6,
                }}
            >
                <Breadcrumbs aria-label="breadcrumb">
                    <Hyperlink
                        onClick={() => handleModalCloseState()}
                        underline="none"
                    >
                        <FontAwesomeIcon icon={faCalendar} />
                        &nbsp;Agenda
                    </Hyperlink>
                    <Hyperlink underline="none">Calendario</Hyperlink>
                    <Hyperlink underline="none">Nuovo Impegno</Hyperlink>
                </Breadcrumbs>
            </Box>

            <Title
                leftItems={[
                    <IconButton
                        color="primary"
                        size="small"
                        onClick={() => handleModalCloseState()}
                    >
                        <FontAwesomeIcon icon={faArrowAltLeft} />
                    </IconButton>,
                ]}
                rightItems={[
                    <IconButton
                        key="2"
                        size="small"
                        onClick={() => handleModalCloseState()}
                    >
                        <FontAwesomeIcon icon={faClose} />
                    </IconButton>,
                ]}
                size="small"
                title={t("Nuovo impegno")}
            />
            {/* </DialogTitle> */}
            <DialogContent>
                <Box display="flex">
                    {/* ─── LEFT NAVIGATION ─── */}
                    <Box minWidth={182}>
                        <AnchorMenu
                            aria-label="Impegno menu"
                            selectedItems={tabId}
                        >
                            <AnchorMenuItem
                                itemId="generali"
                                label={t("Generali")}
                                onClick={() => setTabId("generali")}
                            />
                            {/* <AnchorMenuItem
                                itemId="documenti"
                                label={t("Documenti")}
                                onClick={() => setTabId("documenti")}
                            />
                            <AnchorMenuItem
                                itemId="dati-aggiuntivi"
                                label={t("Dati aggiuntivi")}
                                onClick={() => setTabId("dati-aggiuntivi")}
                            /> */}
                            <AnchorMenuItem
                                itemId="modelli"
                                label={t("Modelli")}
                                onClick={() => setTabId("modelli")}
                            />
                            <AnchorMenuItem
                                itemId="impegni-collegati"
                                label={t("Impegni collegati")}
                                onClick={() => setTabId("impegni-collegati")}
                            />
                        </AnchorMenu>
                    </Box>

                    {/* ─── RIGHT CONTENT ─── */}
                    <Box flexGrow={1} pl={3} pr={5}>
                        {tabId === "generali" && (
                            <NewSchedaImpegno
                                data={data}
                                practicaSearchResult={practicaSearchResult}
                                setPracticaSearchResult={
                                    setPracticaSearchResult
                                }
                                handlePracticaSearch={handlePracticaSearch}
                                searchPracticaLoading={searchPracticaLoading}
                                impegnoSearchResult={impegnoSearchResult}
                                handleImpegnoSearch={handleImpegnoSearch}
                                searchImpegnoLoading={searchImpegnoLoading}
                                deadlineSaveParams={deadlineSaveParams}
                                setDeadlineSaveParams={setDeadlineSaveParams}
                                fetchUserGroupById={fetchUserGroupById}
                                loggedUserName={loggedUserName}
                                fetchCalendarData={fetchCalendarData}
                                setIsArchiveChecked={setIsArchiveChecked}
                                requiredFields={requiredFields}
                                setRequiredFields={setRequiredFields}
                                selectedPraticaName={selectedPraticaName}
                                setSelectedPraticaName={setSelectedPraticaName}
                                handleCreateFromDeadline={
                                    handleCreateFromDeadline
                                }
                                isExternal={isExternal}
                                giornataSwitch={giornataSwitch}
                                setGiornataSwitch={setGiornataSwitch}
                            />
                        )}
                        {/* {tabId === "documenti" && (
                            <Documenti
                                documentsQuery={documentsQuery}
                                setDocumentsQuery={setDocumentsQuery}
                                listDocuments={listDocuments}
                                loadingDocuments={loadingDocuments}
                                LINK_DOCUMENT={LINK_DOCUMENT}
                            />
                        )} */}
                        {/* {tabId === "dati-aggiuntivi" && (
                            <DatiAggiuntivi
                                fetchDatiAggiuntivi={fetchDatiAggiuntivi}
                                connect={connect}
                                selectedItem={selectedItem}
                                setSelectedItem={setSelectedItem}
                                localParentItemId={localParentItemId}
                                setLocalParentItemId={setLocalParentItemId}
                                toggleItemConnection={toggleItemConnection}
                                loadingDatiAggiuntivi={loadingDatiAggiuntivi}
                            />
                        )} */}
                        {tabId === "modelli" && <Modelli />}
                        {tabId === "impegni-collegati" && <ImpegniCollegati />}
                    </Box>

                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "flex-end",
                            mb: 1,
                        }}
                    >
                        {!calendarOpen && tabId === "generali" && (
                            <IconButton onClick={handleToggleCalendar}>
                                <FontAwesomeIcon icon={faArrowLeftFromLine} />
                            </IconButton>
                        )}
                    </Box>

                    {/* ─── OPTIONAL CALENDAR ─── */}
                    <Box>
                        {calendarOpen && tabId === "generali" && (
                            <FullCalendarDayView
                                setCalendarOpen={() => setCalendarOpen(false)}
                            />
                        )}
                    </Box>
                </Box>
            </DialogContent>

            <Divider />
            <DialogActions>
                <Box
                    sx={{
                        width: "100%",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                    }}
                >
                    <Button
                        variant="outlined"
                        onClick={() => handleModalCloseState()}
                    >
                        {t("Annulla")}
                    </Button>
                    <Stack direction="row" gap={1} sx={{ mr: 7 }}>
                        <Button
                            onClick={
                                showInstrumentsMenu
                                    ? () => setShowInstrumentsMenu(false)
                                    : () => {
                                          handleModalCloseState();
                                          clearSessionStorage();
                                      }
                            }
                            variant="outlined"
                        >
                            {t("Assistente pianificazione")}
                        </Button>
                        <SpinnerButton
                            variant="contained"
                            onClick={() => handleSaveAction()}
                            label={t("Salva")}
                            isLoading={saveLoading}
                        />
                    </Stack>
                </Box>
            </DialogActions>
            <ConfirmModal
                open={showModal.open}
                handleDecline={() =>
                    setShowModal({ ...showModal, open: false })
                }
                handleAgree={showModal.deleteFunc}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t(showModal.confirmText)}
                title={t(showModal.title)}
                colorConfirmButton="error"
            />
            <ConfirmModal
                open={showModalOverlapping}
                handleDecline={handleDecline}
                handleAgree={handleModalConfirm}
                decline={t("Annulla")}
                agree={t("Conferma")}
                title={t("Conflitti Attività:")}
            >
                {confirmText.map((txt: string) => (
                    <Typography>{t(txt)}</Typography>
                ))}
            </ConfirmModal>
            <ConfirmModal
                open={showSaveFormModal}
                handleDecline={() => setShowSaveFormModal(false)}
                handleAgree={() => handleSaveAction()}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t("Vuoi salvare il nuovo evento?")}
                title={t("Salva nuovo evento")}
                loading={saveFromDeadlineLoading}
            />
        </Dialog>
    );
}
