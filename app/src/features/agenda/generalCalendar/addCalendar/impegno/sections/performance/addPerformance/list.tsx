import { useEffect } from "react";
import useGetVicino from "../../../hooks/useGetVicino";
import {
    Box,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from "@vapor/react-material";
import Spinner from "../../../../../../../../custom-components/Spinner";
import { TYPES } from "../../../hooks/useSavePerformance";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../../../../../custom-components/CustomDataGrid";
import {
    GridCallbackDetails,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";

interface IListProps {
    savePerformanceParams: any;
    setSavePerformanceParams: any;
}

export default function List(props: IListProps) {
    const { t } = useTranslation();
    const { savePerformanceParams, setSavePerformanceParams } = props;
    const {
        LIST_OPTIONS,
        vicinoSelected,
        setVicinoSelected,
        vicinoListData,
        loading,
    } = useGetVicino();

    const handleRowSelection = (
        uniqueid: GridRowSelectionModel,
        _: GridCallbackDetails<any>
    ) => {
        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            selectedVoices: [uniqueid],
        }));
    };

    const handleInputChanges = (event: any) => {
        const { value } = event.target;
        setVicinoSelected(value);
    };

    //setting the type of the performance
    useEffect(() => {
        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            tipo: TYPES.list,
        }));
    }, [TYPES, savePerformanceParams.tipo]);

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="impegniListino"
                columns={vicinoListData.columns}
                data={vicinoListData.rows || []}
                page={vicinoListData.page}
                totalRows={vicinoListData.totalRows}
                pageSize={vicinoListData.pageSize}
                loading={loading}
                selectableRows
                onClickCheckboxKey="id_voce_listino"
                onClickKey="id_voce_listino"
                onRowSelectionModelChange={handleRowSelection}
            />
        );
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
            }}
        >
            <FormControl sx={{ width: 500, mt: 2, ml: 1 }}>
                <InputLabel>{t("Listino")}</InputLabel>
                <Select value={vicinoSelected} onChange={handleInputChanges}>
                    {(LIST_OPTIONS || [])?.map((list: any, index: number) => (
                        <MenuItem key={index} value={list.id_listino}>
                            {list.descrizione}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Box sx={{ mt: 3, ml: 1 }}>{renderDataTable()}</Box>
        </Box>
    );
}
