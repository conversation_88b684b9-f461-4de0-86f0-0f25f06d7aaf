import {
    Autocomplete,
    Box,
    Button,
    Checkbox,
    Divider,
    FormControl,
    FormControlLabel,
    FormGroup,
    IconButton,
    InputLabel,
    MenuItem,
    Popover,
    Select,
    TextareaAutosize,
    TextField,
    TimePicker,
    Toggle,
    Typography,
    VaporTag,
    AdapterDateFns,
    LocalizationProvider,
} from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faAlarmClock,
    faBell,
    faBusinessTime,
    faRepeat,
} from "@fortawesome/pro-regular-svg-icons";
import { useEffect, useState } from "react";
import CustomAutocomplete from "../../../../../../custom-components/CustomAutocomplete";
import { removeLinks } from "../../../../../../utilities/utils";
import { useTranslation } from "@1f/react-sdk";
import useDeadlineSelection from "../hooks/useDeadlineSelection";
import useSaveDeadlineTypes from "../hooks/useSaveDeadlineTypes";
import useSessionStorageState from "../hooks/useSessionStorageState";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { parseDate } from "../../../../../../helpers/parseDataFormat";
import ConfirmModal from "../../../../../../custom-components/ConfirmModal";
import DatePicker from "../../../../../../components/ui-kit/DatePicker";

const filter = createFilterOptions<any>();

interface IProps {
    data: any;
    practicaSearchResult: any;
    setPracticaSearchResult: React.Dispatch<React.SetStateAction<any>>;
    handlePracticaSearch: (value: any) => void;
    searchPracticaLoading: boolean;
    impegnoSearchResult: any;
    handleImpegnoSearch: (value: any) => void;
    searchImpegnoLoading: boolean;
    deadlineSaveParams: any;
    setDeadlineSaveParams: React.Dispatch<React.SetStateAction<any>>;
    fetchUserGroupById: (id: string) => Promise<void>;
    loggedUserName: string;
    fetchCalendarData: () => void;
    setIsArchiveChecked: React.Dispatch<React.SetStateAction<boolean>>;
    requiredFields: any;
    setRequiredFields: React.Dispatch<React.SetStateAction<any>>;
    selectedPraticaName: any | null;
    setSelectedPraticaName: React.Dispatch<React.SetStateAction<any | null>>;
    handleCreateFromDeadline: (connect: boolean) => void;
    isExternal: boolean;
    giornataSwitch: boolean;
    setGiornataSwitch: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function xnewSchedaImpegno(props: IProps) {
    const {
        data,
        practicaSearchResult,
        setPracticaSearchResult,
        handlePracticaSearch,
        searchPracticaLoading,
        deadlineSaveParams,
        setDeadlineSaveParams,
        fetchUserGroupById,
        fetchCalendarData,
        requiredFields,
        setRequiredFields,
        selectedPraticaName,
        setSelectedPraticaName,
        handleCreateFromDeadline,
        isExternal,
        giornataSwitch,
        setGiornataSwitch,
    } = props;
    // console.log('deadlineSaveParams', deadlineSaveParams);

    const [collegaAValue, setCollegaAValue] = useState<string>("");
    const [showModal, setShowModal] = useState<boolean>(false);
    const [showSaveFormModal, setShowSaveFormModal] = useState<boolean>(false);
    const [tempUsersSelected, setTempUsersSelected] = useState<any>([]);
    const [anchorGruppoUtenit, setAnchorGruppoUtenit] = useState(null);
    const openGruppoUtentiPopover = Boolean(anchorGruppoUtenit);
    const [anchorElIntestatari, setAnchorElIntestatari] = useState(null);
    const openIntestatariPopover = Boolean(anchorElIntestatari);
    const [anchorElPartecipanti, setAnchorElPartecipanti] = useState(null);
    const openPartecipantiPopover = Boolean(anchorElPartecipanti);

    const { t } = useTranslation();
    const { saveDeadlineType, saveDeadlineCategoria, saveDeadlineStato } =
        useSaveDeadlineTypes();
    const {
        selectedTipologia,
        setSelectedTipologia,
        selectedCategoria,
        setSelectedCategoria,
        selectedStato,
        setSelectedStato,
        clearSessionStorage,
    } = useSessionStorageState();

    const minutesToHour = (totalMinutes: number) => {
        // create a Date at "today at midnight", then add the minutes
        const d = new Date();
        d.setHours(0, totalMinutes, 0, 0);
        return d;
    };

    const dateToMinutes = (d: Date | null) => {
        if (!d) return 0;
        return d.getHours() * 60 + d.getMinutes();
    };

    const handlePraticaInputChanges = (_: any, newInputValue: any) => {
        setSelectedPraticaName(newInputValue);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineFileUniqueid: newInputValue.uniqueid,
        });
    };

    const handleCategoriaChange = async (_event: any, value: any) => {
        setSelectedCategoria(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineCategory: value.id,
        });
        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineCategoria(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const handleTipologiaChange = async (_event: any, value: any) => {
        setSelectedTipologia(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineType: value === null ? "" : value.id,
        });

        if (value?.id && requiredFields.deadlineType) {
            setRequiredFields({
                ...requiredFields,
                deadlineType: false,
            });
        }

        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineType(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const handleStatoChange = async (_event: any, value: any) => {
        setSelectedStato(value);
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            deadlineStatus: value.id,
        });
        if (value?.nome?.startsWith(t('Aggiungi "'))) {
            const newInputText = value.inputValue;
            const res = await saveDeadlineStato(newInputText);
            if (res) {
                fetchCalendarData();
            }
        }
    };

    const filterOptionsFunction = (options: any, params: any) => {
        const filtered = filter(options, params);
        const { inputValue } = params;
        const isExisting = options.some(
            (option: any) => inputValue === option.nome
        );
        if (inputValue !== "" && !isExisting) {
            filtered.push({
                nome: `Aggiungi "${inputValue}"`,
                inputValue: inputValue, // Keep the raw input value
            });
        }
        return filtered;
    };

    const renderOptionFucntion = (props: any, option: any) => {
        if (option.nome && option.nome.startsWith('Aggiungi "')) {
            return (
                <li {...props}>
                    <Typography>{option.nome}</Typography>
                </li>
            );
        }
        return <li {...props}>{option.nome}</li>;
    };

    const getOptionLabelFunc = (option: any) => {
        if (typeof option === "string") {
            return option;
        }
        // Ensure the input box shows only the actual text, not "Aggiungi ..."
        if (option.nome.startsWith(t('Aggiungi "'))) {
            return option.inputValue;
        }
        return option.nome;
    };

    // Function to set checked users based on the selected group
    const setCheckedUsersFromGroup = (groupData: any) => {
        const checkedUsers = groupData.map((checkedUser: any) =>
            data.usersData.find((user: any) => user.id === checkedUser.userId)
        );
        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: checkedUsers,
        }));
    };

    const handleUsersGroup = async (_: any, newValue: any) => {
        if (!newValue) {
            setDeadlineSaveParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: undefined,
                deadlineUser: [],
            }));
            return;
        }
        const { id } = newValue;
        try {
            const groups = await fetchUserGroupById(id);
            if (
                Array.isArray(groups) &&
                groups.length > 0 &&
                requiredFields.deadlineUser
            ) {
                setRequiredFields({
                    ...requiredFields,
                    deadlineUser: false,
                });
            }
            setDeadlineSaveParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: newValue,
                deadlineUser: Array.isArray(groups) ? groups : [],
            }));
            if (Array.isArray(groups)) {
                setCheckedUsersFromGroup(groups);
            }
        } catch (error) {
            console.error("Error fetching user group:", error);
        }
    };

    const updateDeadlineUsers = (users: any) => {
        const uniqueUsers = Array.from(
            new Map(users.map((user: any) => [user.id, user])).values()
        );

        // Directly update the `deadlineUser` with the unique list
        setDeadlineSaveParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: uniqueUsers,
        }));
    };

    const handleUserData = (_: any, newValue: any) => {
        if (newValue && newValue.length > 0 && requiredFields.deadlineUser) {
            setRequiredFields({
                ...requiredFields,
                deadlineUser: false,
            });
        }
        if (!deadlineSaveParams.deadLinesGroups) {
            updateDeadlineUsers(newValue);
        } else {
            setTempUsersSelected(newValue);
            setShowModal(true);
        }
    };

    const handleModalConfirm = (confirm: boolean) => {
        if (confirm) {
            setDeadlineSaveParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: undefined,
            }));
            if (tempUsersSelected.length > 0) {
                updateDeadlineUsers(tempUsersSelected);
            }
        }
        setShowModal(false);
    };

    const handleInputCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: checked });
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        if (value !== "") {
            setRequiredFields({
                ...requiredFields,
                deadlineText: false,
            });
        }
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: value });
    };

    const handleGiornataSwitch = (event: any) => {
        const { checked } = event.target;
        setGiornataSwitch(checked);
        if (checked) {
            setDeadlineSaveParams((prev: any) => ({
                ...prev,
                deadlinePeriod: "1435", //23:55 in minutes
                deadlineHours: "00",
                deadlineMinutes: "00",
            }));
        }
    };

    const handleTimeChange = (newValue: any | null) => {
        if (!newValue) {
            setDeadlineSaveParams((prev: any) => ({
                ...prev,
                deadlineHours: "",
                deadlineMinutes: "",
            }));
            return;
        }
        const hh = String(newValue.getHours()).padStart(2, "0");
        const mm = String(newValue.getMinutes()).padStart(2, "0");

        setDeadlineSaveParams((prev: any) => ({
            ...prev,
            deadlineHours: hh,
            deadlineMinutes: mm,
        }));
    };

    const onDateChange = (value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        setDeadlineSaveParams((prevValue: any) => ({
            ...prevValue,
            deadlineDate: formattedDate,
        }));
    };

    function parseTimeStringToDate(timeString: string) {
        if (!timeString) return null;
        const [hh, mm] = timeString.split(":").map((n) => parseInt(n, 10));
        const d = new Date();
        d.setHours(hh);
        d.setMinutes(mm);
        d.setSeconds(0);
        d.setMilliseconds(0);
        return d;
    }

    useDeadlineSelection({
        deadlineSaveParams,
        data,
        setSelectedTipologia,
        setSelectedCategoria,
        setSelectedStato,
        selectedTipologia,
        selectedCategoria,
        selectedStato,
        clearSessionStorage,
        setDeadlineSaveParams,
    });

    useEffect(() => {
        // On mount, check sessionStorage for fullImpegnoInitialData
        const storedData = sessionStorage.getItem("fullImpegnoInitialData");
        const storedGiornataSwitch = sessionStorage.getItem(
            "giornataSwitchImpegno"
        );
        console.log("storedGiornataSwitch", storedGiornataSwitch);
        if (storedData) {
            setDeadlineSaveParams((prev: any) => ({
                ...prev,
                ...JSON.parse(storedData),
            }));
            sessionStorage.removeItem("fullImpegnoInitialData");
        }
        if (storedGiornataSwitch) {
            setGiornataSwitch(JSON.parse(storedGiornataSwitch));
            if (JSON.parse(storedGiornataSwitch)) {
                setDeadlineSaveParams((prev: any) => ({
                    ...prev,
                    deadlinePeriod: "1435", //23:55 in minutes
                    deadlineHours: "00",
                    deadlineMinutes: "00",
                }));
            }
            sessionStorage.removeItem("giornataSwitchImpegno");
        }
    }, []);

    return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Box
                sx={{
                    width: "100%",
                    px: 4,
                    display: "flex",
                    flexDirection: "column",
                    gap: 0,
                }}
            >
                {/* Titolo & Tipologia */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <TextField
                            label={t("Titolo*")}
                            name="deadlineText"
                            value={deadlineSaveParams.deadlineText}
                            onChange={handleInputChanges}
                            error={!!requiredFields.deadlineText}
                            helperText={
                                requiredFields.deadlineText
                                    ? t("Titolo obbligatorio")
                                    : ""
                            }
                        />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                        <FormControl fullWidth>
                            <Autocomplete
                                options={data?.tipologiaData || []}
                                selectOnFocus
                                clearOnBlur
                                limitTags={1}
                                value={selectedTipologia}
                                onChange={handleTipologiaChange}
                                filterOptions={filterOptionsFunction}
                                renderOption={renderOptionFucntion}
                                getOptionLabel={getOptionLabelFunc}
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        label={t("Tipologia*")}
                                        placeholder={t(
                                            "Seleziona una tipologia"
                                        )}
                                        error={!!requiredFields.deadlineType}
                                        helperText={
                                            requiredFields.deadlineType
                                                ? t("Tipologia obbligatoria")
                                                : ""
                                        }
                                    />
                                )}
                            />
                        </FormControl>
                    </Box>
                </Box>

                {/* Impegno standard, Stato impegno, Categoria */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    {/* Left 50%: Impegno standard & Stato impegno */}
                    <Box sx={{ flex: 1, display: "flex", gap: 2 }}>
                        <Box sx={{ flex: 1 }}>
                            <FormControl fullWidth>
                                <InputLabel>Impegno standard</InputLabel>
                                <Select label="Impegno standard">
                                    <MenuItem value="">None</MenuItem>
                                </Select>
                            </FormControl>
                        </Box>
                        <Box sx={{ flex: 1 }}>
                            <FormControl fullWidth>
                                <CustomAutocomplete
                                    options={data?.statoData || []}
                                    selectOnFocus
                                    noOptionsText={t("Nessuna opzione")}
                                    clearOnBlur
                                    limitTags={1}
                                    value={selectedStato}
                                    onChange={handleStatoChange}
                                    filterOptions={filterOptionsFunction}
                                    renderOption={renderOptionFucntion}
                                    getOptionLabel={getOptionLabelFunc}
                                    renderInput={(params: any) => (
                                        <TextField
                                            {...params}
                                            label={t("Stato")}
                                            placeholder={t(
                                                "Seleziona uno stato"
                                            )}
                                        />
                                    )}
                                />
                            </FormControl>
                        </Box>
                    </Box>
                    {/* Right 50%: Categoria */}
                    <Box sx={{ flex: 1 }}>
                        <Autocomplete
                            options={data?.categoriaData || []}
                            selectOnFocus
                            clearOnBlur
                            limitTags={1}
                            value={selectedCategoria}
                            loadingText={t("Caricamento...")}
                            noOptionsText={t("Nessuna opzione")}
                            onChange={handleCategoriaChange}
                            filterOptions={filterOptionsFunction}
                            renderOption={renderOptionFucntion}
                            getOptionLabel={getOptionLabelFunc}
                            renderInput={(params: any) => (
                                <TextField
                                    {...params}
                                    label={t("Categoria")}
                                    placeholder={t("Seleziona una categoria")}
                                />
                            )}
                        />
                    </Box>
                </Box>

                {/* Collega a & Seleziona pratica */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl fullWidth>
                            <InputLabel>{t("Collega a")}</InputLabel>
                            <Select
                                label="Collega a"
                                value={collegaAValue}
                                onChange={(e: any) =>
                                    setCollegaAValue(e.target.value)
                                }
                            >
                                <MenuItem value="pratica">Pratica</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                    {collegaAValue === "pratica" && (
                        <Box sx={{ flex: 1 }}>
                            <FormControl fullWidth>
                                <CustomAutocomplete
                                    options={practicaSearchResult}
                                    selectOnFocus
                                    clearOnBlur
                                    limitTags={1}
                                    loading={searchPracticaLoading}
                                    value={selectedPraticaName}
                                    loadingText={t("Caricamento...")}
                                    noOptionsText={t("Nessuna opzione")}
                                    getOptionLabel={(option: any) => {
                                        if (
                                            !option.listaclienti &&
                                            !option.listacontroparti
                                        ) {
                                            return option.codicearchivio
                                                ? `${option.codicearchivio}: Nessun cliente / Nessuna controparte`
                                                : "";
                                        }
                                        return removeLinks(
                                            option.headerArchive
                                                ? option.headerArchive
                                                : `${
                                                      option.listaclienti || ""
                                                  } contro ${
                                                      option.listacontroparti ||
                                                      ""
                                                  }`,
                                            " "
                                        );
                                    }}
                                    onChange={handlePraticaInputChanges}
                                    onInputChange={(
                                        _: any,
                                        newInputValue: any
                                    ) => {
                                        if (newInputValue === "") {
                                            setPracticaSearchResult([]);
                                            return;
                                        }
                                        handlePracticaSearch(newInputValue);
                                    }}
                                    renderOption={(props: any, option: any) => (
                                        <div {...props}>
                                            <span>
                                                {removeLinks(
                                                    option.headerArchive,
                                                    " "
                                                )}
                                            </span>
                                        </div>
                                    )}
                                    renderInput={(params: any) => (
                                        <TextField
                                            label={t("Pratica")}
                                            {...params}
                                            placeholder={t(
                                                "Cerca pratica per codice, descrizione, nominativi, RG…"
                                            )}
                                            InputProps={{
                                                ...params.InputProps,
                                                endAdornment: (
                                                    <>
                                                        {
                                                            params.InputProps
                                                                .endAdornment
                                                        }
                                                    </>
                                                ),
                                            }}
                                        />
                                    )}
                                />
                            </FormControl>
                        </Box>
                    )}
                </Box>

                <Divider light sx={{ my: 2 }} />

                {/* Data, Inizio, Fine, Giornata intera */}
                <Box
                    sx={{
                        display: "flex",
                        gap: 2,
                        alignItems: "center",
                        mb: 2,
                    }}
                >
                    <Box sx={{ width: 250 }}>
                        <DatePicker
                            label={t("Data")}
                            name="deadlineDate"
                            value={parseDate(deadlineSaveParams?.deadlineDate)}
                            onChange={onDateChange}
                        />
                    </Box>
                    <Box sx={{ width: 125 }}>
                        <TimePicker
                            label="Inizio"
                            disabled={giornataSwitch}
                            onChange={handleTimeChange}
                            ampm={false}
                            value={parseTimeStringToDate(
                                `${deadlineSaveParams.deadlineHours}:${deadlineSaveParams.deadlineMinutes}`
                            )}
                        />
                    </Box>
                    <Box sx={{ width: 125 }}>
                        <TimePicker
                            label="Fine"
                            disabled={giornataSwitch}
                            onChange={handleTimeChange}
                            ampm={false}
                        />
                    </Box>
                    <FormControlLabel
                        control={
                            <Toggle
                                size="medium"
                                checked={giornataSwitch}
                                onChange={handleGiornataSwitch}
                            />
                        }
                        label={t("Giornata intera")}
                        sx={{ ml: 5, mt: 2.5 }}
                    />
                </Box>

                {/* Durata, Promemoria, Rendi ricorrente */}
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        gap: 1,
                        mb: 2,
                    }}
                >
                    <Box>
                        <TimePicker
                            value={minutesToHour(
                                deadlineSaveParams.deadlinePeriod
                            )}
                            disabled={giornataSwitch}
                            name="deadlinePeriod"
                            label={t("Durata")}
                            ampm={false}
                            views={["hours", "minutes"]}
                            sx={{ width: 250 }}
                            onChange={(newValue: any) => {
                                const mins = dateToMinutes(newValue);
                                setDeadlineSaveParams((old: any) => ({
                                    ...old,
                                    deadlinePeriod: mins,
                                }));
                            }}
                        />
                    </Box>
                    <Button
                        sx={{ ml: 1, mt: 2.5 }}
                        startIcon={<FontAwesomeIcon icon={faAlarmClock} />}
                    >
                        {t("Promemoria")}
                    </Button>
                    <Button
                        sx={{ mt: 2.5 }}
                        startIcon={<FontAwesomeIcon icon={faRepeat} />}
                    >
                        {t("Rendi ricorrente")}
                    </Button>
                </Box>

                <Divider light sx={{ my: 2 }} />

                {/* Gruppi utenti */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl fullWidth>
                            <Autocomplete
                                options={[
                                    {
                                        id: "-1",
                                        name: t(
                                            "Seleziona il gruppo di utenti..."
                                        ),
                                    },
                                    ...(data?.groupsData || []),
                                ]}
                                value={
                                    deadlineSaveParams.deadLinesGroups === "-1"
                                        ? {
                                              id: "-1",
                                              name: t(
                                                  "Seleziona il gruppo di utenti..."
                                              ),
                                          }
                                        : deadlineSaveParams.deadLinesGroups
                                }
                                isOptionEqualToValue={(
                                    option: any,
                                    value: any
                                ) => option.id === value.id}
                                limitTags={1}
                                onChange={handleUsersGroup}
                                getOptionLabel={(option: any) => option?.name}
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        size="medium"
                                        label={t("Gruppo Utenti")}
                                    />
                                )}
                            />
                        </FormControl>
                    </Box>
                    <IconButton
                        variant="outlined"
                        sx={{ alignSelf: "center", mt: 2.7 }}
                        onClick={(e: any) =>
                            setAnchorGruppoUtenit(e.currentTarget)
                        }
                    >
                        <FontAwesomeIcon icon={faBell} />
                    </IconButton>
                    <Popover
                        open={openGruppoUtentiPopover}
                        anchorEl={anchorGruppoUtenit}
                        onClose={() => setAnchorGruppoUtenit(null)}
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "left",
                        }}
                        PaperProps={{ sx: { p: 2, borderRadius: 2 } }}
                    >
                        <FormGroup sx={{ p: 1 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        onChange={() => ""}
                                        name="email"
                                        color="primary"
                                    />
                                }
                                label={t("‘Invia notifica")}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        onChange={() => ""}
                                        name="email"
                                        color="primary"
                                    />
                                }
                                label={t("Invia email")}
                            />
                        </FormGroup>
                    </Popover>
                </Box>

                {/* Intestatari utenti interni */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl fullWidth>
                            <Autocomplete
                                disableCloseOnSelect
                                multiple
                                options={data.usersData || []}
                                value={deadlineSaveParams.deadlineUser}
                                onChange={handleUserData}
                                isOptionEqualToValue={(
                                    option: any,
                                    value: any
                                ) => option.id === value.id}
                                getOptionLabel={(option: any) =>
                                    option.nomeutente
                                }
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        label={t("Intestatari")}
                                    />
                                )}
                                renderOption={(props, option, { selected }) => (
                                    <li {...props}>
                                        <Checkbox
                                            size="small"
                                            style={{ marginRight: 8 }}
                                            checked={selected}
                                        />
                                        {option.nomeutente}
                                    </li>
                                )}
                                noOptionsText={t("Nessuna opzione")}
                                renderTags={(value: any[], getTagProps: any) =>
                                    value.map((option, index) => {
                                        const { key, ...tagProps } =
                                            getTagProps({ index });
                                        return (
                                            <div
                                                key={key}
                                                style={{
                                                    marginRight: "2px",
                                                    marginBottom: "2px",
                                                    marginTop: "2px",
                                                }}
                                            >
                                                <VaporTag
                                                    key={key}
                                                    label={option.nomeutente}
                                                    variant="filter"
                                                    {...tagProps}
                                                />
                                            </div>
                                        );
                                    })
                                }
                            />
                        </FormControl>
                    </Box>
                    <IconButton
                        variant="outlined"
                        sx={{ alignSelf: "center", mt: 2.7 }}
                        onClick={(e: any) =>
                            setAnchorElIntestatari(e.currentTarget)
                        }
                    >
                        <FontAwesomeIcon icon={faBell} />
                    </IconButton>
                    <Popover
                        open={openIntestatariPopover}
                        anchorEl={anchorElIntestatari}
                        onClose={() => setAnchorElIntestatari(null)}
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "left",
                        }}
                        PaperProps={{ sx: { p: 2, borderRadius: 2 } }}
                    >
                        <FormGroup sx={{ p: 1 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        onChange={() => ""}
                                        name="email"
                                        color="primary"
                                    />
                                }
                                label={t("Invia notifica")}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        onChange={() => ""}
                                        name="email"
                                        color="primary"
                                    />
                                }
                                label={t("Invia email")}
                            />
                        </FormGroup>
                    </Popover>
                </Box>

                {/* Partecipanti */}
                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                        <FormControl fullWidth>
                            <InputLabel>{t("Partecipanti")}</InputLabel>
                            <Select label="Partecipanti" />
                        </FormControl>
                    </Box>
                    <IconButton
                        variant="outlined"
                        sx={{ alignSelf: "center", mt: 2.7 }}
                        onClick={(e: any) =>
                            setAnchorElPartecipanti(e.currentTarget)
                        }
                    >
                        <FontAwesomeIcon icon={faBell} />
                    </IconButton>
                    <Popover
                        open={openPartecipantiPopover}
                        anchorEl={anchorElPartecipanti}
                        onClose={() => setAnchorElPartecipanti(null)}
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "left",
                        }}
                        PaperProps={{ sx: { p: 2, borderRadius: 2 } }}
                    >
                        <FormGroup sx={{ p: 1 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        onChange={() => ""}
                                        name="email"
                                        color="primary"
                                    />
                                }
                                label={t("Invia email")}
                            />
                        </FormGroup>
                    </Popover>
                </Box>

                {/* Posizione */}
                <Box sx={{ mb: 2 }}>
                    <TextField
                        fullWidth
                        name="location"
                        label={t("Posizione")}
                        value={deadlineSaveParams.location}
                        onChange={handleInputChanges}
                    />
                </Box>

                {/* Annotazioni */}
                <Box sx={{ mb: 2 }}>
                    <FormControl fullWidth>
                        <InputLabel>{t("Annotazioni")}</InputLabel>
                        <TextareaAutosize
                            name="deadlineAnnotation"
                            value={deadlineSaveParams.deadlineAnnotation}
                            onChange={handleInputChanges}
                            minRows={6}
                            sx={{
                                width: "25%",
                            }}
                        />
                    </FormControl>
                </Box>

                <Divider light sx={{ my: 2 }} />

                {/* Checkboxes and Crea Timesheet */}
                <Box
                    display="flex"
                    width="100%"
                    justifyContent="space-between"
                    alignItems="flex-end"
                    sx={{ mt: 2, ml: 1 }}
                >
                    <Box display="flex" flexDirection="column" gap={2}>
                        <FormGroup row sx={{ mb: 1 }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="deadlineEvasa"
                                        size="small"
                                        checked={
                                            deadlineSaveParams?.deadlineEvasa
                                        }
                                        disabled={isExternal}
                                        onChange={handleInputCheckboxChanges}
                                    />
                                }
                                label={t("Evaso")}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="deadlineAddebitabile"
                                        disabled={isExternal}
                                        onChange={handleInputCheckboxChanges}
                                        size="small"
                                    />
                                }
                                label={t("Addebitabile")}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="deadlineBillable"
                                        checked={
                                            deadlineSaveParams?.deadlineBillable
                                        }
                                        onChange={handleInputCheckboxChanges}
                                        size="small"
                                    />
                                }
                                label={t("Fatturabile")}
                            />
                        </FormGroup>
                        <FormGroup row>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="deadlineImportant"
                                        checked={
                                            deadlineSaveParams?.deadlineImportant
                                        }
                                        onChange={handleInputCheckboxChanges}
                                        size="small"
                                    />
                                }
                                label={t("Importante")}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="privato"
                                        checked={deadlineSaveParams?.privato}
                                        onChange={handleInputCheckboxChanges}
                                        size="small"
                                    />
                                }
                                label={t("Privato")}
                            />
                        </FormGroup>
                    </Box>
                    <Box ml="auto" alignSelf="flex-end">
                        <Button
                            disabled={!deadlineSaveParams?.deadlineEvasa}
                            onClick={() => setShowSaveFormModal(true)}
                            startIcon={
                                <FontAwesomeIcon icon={faBusinessTime} />
                            }
                        >
                            {t("Crea Timesheet")}
                        </Button>
                    </Box>
                </Box>
            </Box>

            <ConfirmModal
                open={showModal}
                handleDecline={() => handleModalConfirm(false)}
                handleAgree={() => handleModalConfirm(true)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t(
                    "Stai associando manualmente le persone al gruppo. L'associazione di gruppo verra tolta."
                )}
                title={t("Vuoi continuare?")}
            />
            <ConfirmModal
                open={showSaveFormModal}
                handleDecline={() => setShowSaveFormModal(false)}
                handleAgree={() => handleCreateFromDeadline(false)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t("Vuoi salvare il nuovo evento?")}
                title={t("Salva nuovo evento")}
            />
        </LocalizationProvider>
    );
}
