import { useState } from "react";
import { Box, Button, Menu, MenuItem } from "@vapor/v3-components";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import NewPerformanceForm from "./addPerformance/newPerformanceForm";
import { CustomDataGrid } from "../../../../../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { useTranslation } from "@1f/react-sdk";

interface IProps {
    showPerformanceForm: any;
    setShowPerformanceForm: any;
    savePerformanceParams: any;
    setSavePerformanceParams: any;
    closeForm: () => void;
    performanceQuery: any;
    setPerformanceQuery: any;
    listPerformance: any;
    loading: boolean;
}

export default function Performance(props: IProps) {
    const {
        loading,
        showPerformanceForm,
        setShowPerformanceForm,
        savePerformanceParams,
        setSavePerformanceParams,
        closeForm,
        performanceQuery,
        setPerformanceQuery,
        listPerformance,
    } = props;
    const { t } = useTranslation();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const openPerformanceForm = (type: string) => {
        setShowPerformanceForm({
            list: type === "list",
            tariff: type === "tariff",
            expense: type === "expense",
            timesheet: type === "timesheet",
        });
        handleClose();
    };

    const onPageChange = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setPerformanceQuery({
            ...performanceQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="impegniPerformance"
                columns={listPerformance.columns}
                data={listPerformance.rows || []}
                page={listPerformance.page}
                totalRows={listPerformance.totalRows}
                pageSize={listPerformance.pageSize}
                loading={loading}
                onPageChangeCallback={onPageChange}
                query={performanceQuery}
                setQuery={setPerformanceQuery}
                // onClickKey=""
            />
        );
    };

    return (
        <>
            {Object.values(showPerformanceForm).some(Boolean) ? (
                <NewPerformanceForm
                    showPerformanceForm={showPerformanceForm}
                    setShowPerformanceForm={setShowPerformanceForm}
                    savePerformanceParams={savePerformanceParams}
                    setSavePerformanceParams={setSavePerformanceParams}
                    closeForm={closeForm}
                />
            ) : (
                <Box>
                    <Box sx={{ display: "flex", justifyContent: "end" }}>
                        <div>
                            <Button
                                aria-haspopup="true"
                                variant="outlined"
                                startIcon={<AddCircleOutlineIcon />}
                                endIcon={<ArrowDropDownIcon />}
                                onClick={handleClick}
                                aria-controls={open ? "basic-menu" : undefined}
                                aria-expanded={open ? "true" : undefined}
                            >
                                {t("Aggiungi")}
                            </Button>
                            <Menu
                                MenuListProps={{
                                    "aria-labelledby": "basic-button",
                                }}
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                transformOrigin={{
                                    vertical: "bottom",
                                    horizontal: "center",
                                }}
                                anchorOrigin={{
                                    vertical: "top",
                                    horizontal: "center",
                                }}
                            >
                                <MenuItem
                                    onClick={() => openPerformanceForm("list")}
                                >
                                    {t("Listino")}
                                </MenuItem>
                                <MenuItem
                                    onClick={() =>
                                        openPerformanceForm("tariff")
                                    }
                                >
                                    {t("Tariffario")}
                                </MenuItem>
                                <MenuItem
                                    onClick={() =>
                                        openPerformanceForm("expense")
                                    }
                                >
                                    {t("Spesa")}
                                </MenuItem>
                                <MenuItem
                                    onClick={() =>
                                        openPerformanceForm("timesheet")
                                    }
                                >
                                    {t("Timesheet")}
                                </MenuItem>
                            </Menu>
                        </div>
                    </Box>
                    <Box sx={{ ml: 2 }}>{renderDataTable()}</Box>
                </Box>
            )}
        </>
    );
}
