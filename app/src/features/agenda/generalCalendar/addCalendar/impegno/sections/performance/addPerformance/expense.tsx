import {
    Box,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Button,
    TextField,
    Menu,
    InputAdornment,
    Link,
    ListSubheader,
} from "@vapor/react-material";
import { useState, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { TYPES } from "../../../hooks/useSavePerformance";
import { EXPENSE_TYPE } from "../../../constants/constant";
import { useCalendarData } from "../../../context/CalendarDataContext";

interface IExpenseProps {
    savePerformanceParams: any;
    setSavePerformanceParams: any;
}

export default function Expense(props: IExpenseProps) {
    const { savePerformanceParams, setSavePerformanceParams } = props;
    const { t } = useTranslation();
    const { data }: any = useCalendarData();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setSavePerformanceParams({ ...savePerformanceParams, [name]: value });
    };

    const handleExpenseChanges = (expense: any) => {
        const { deducibilita_iva, iva, nome, tipo, importo } = expense;
        setSavePerformanceParams({
            ...savePerformanceParams,
            deducibilita_iva,
            iva,
            nome,
            type: tipo,
            valore: importo,
        });
        if (expense) {
            handleClose();
        }
    };

    useEffect(() => {
        if (data?.expenseMethods) {
            const DEFATUL_EXPENSE = data?.expenseMethods.banca[0].id;
            setSavePerformanceParams({
                ...savePerformanceParams,
                paymentMethod: DEFATUL_EXPENSE,
            });
        }
    }, [data?.expenseMethods]);

    useEffect(() => {
        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            tipo: TYPES.expense,
        }));
    }, [savePerformanceParams.tipo, TYPES]);

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                ml: 1,
            }}
        >
            <div style={{ marginTop: "20px", marginBottom: "20px" }}>
                <Button
                    aria-haspopup="true"
                    variant="outlined"
                    endIcon={<ArrowDropDownIcon />}
                    onClick={handleClick}
                    aria-controls={open ? "basic-menu" : undefined}
                    aria-expanded={open ? "true" : undefined}
                >
                    {t("Seleziona spesa fissa")}
                </Button>
                <Menu
                    MenuListProps={{
                        "aria-labelledby": "basic-button",
                    }}
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    transformOrigin={{
                        vertical: "bottom",
                        horizontal: "center",
                    }}
                    anchorOrigin={{
                        vertical: "top",
                        horizontal: "center",
                    }}
                >
                    {(data?.expenseSpeseFisse || [])?.map(
                        (item: any, index: number) => (
                            <MenuItem
                                key={index}
                                onClick={() => handleExpenseChanges(item)}
                            >
                                {item.nome}
                            </MenuItem>
                        )
                    )}
                </Menu>
            </div>

            <Box sx={{ ml: -1 }}>
                <TextField
                    label="Costo Effettivo"
                    name="costo"
                    value={savePerformanceParams.costo}
                    onChange={handleInputChanges}
                    type="number"
                />
            </Box>
            <FormControl sx={{ width: 500 }}>
                <InputLabel>{"Tipo"}</InputLabel>
                <Select
                    name="type"
                    value={savePerformanceParams.type}
                    onChange={handleInputChanges}
                >
                    {(EXPENSE_TYPE || [])?.map(
                        (version: any, index: number) => (
                            <MenuItem key={index} value={version.value}>
                                {version.label}
                            </MenuItem>
                        )
                    )}
                </Select>
            </FormControl>
            <Box sx={{ ml: -1, width: 250 }}>
                <TextField
                    label="IVA"
                    name="iva"
                    value={savePerformanceParams.iva}
                    onChange={handleInputChanges}
                    sx={{
                        ml: -1,
                        width: "250px !important",
                    }}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">%</InputAdornment>
                        ),
                    }}
                />
            </Box>
            <Box sx={{ ml: -1 }}>
                <TextField
                    label="Deducibilità IVA"
                    name="deducibilita_iva"
                    value={savePerformanceParams.deducibilita_iva}
                    onChange={handleInputChanges}
                    sx={{
                        ml: -1,
                        width: "250px !important",
                    }}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">%</InputAdornment>
                        ),
                    }}
                />
            </Box>
            <FormControl
                sx={{
                    width: 500,
                }}
            >
                <InputLabel htmlFor="grouped-select">
                    {t("Modo pagamento")}
                </InputLabel>
                <Select
                    label="Modo pagamento"
                    name="paymentMethod"
                    value={savePerformanceParams.paymentMethod}
                    onChange={handleInputChanges}
                >
                    <ListSubheader>{t("Banca")}</ListSubheader>
                    {(data?.expenseMethods?.banca || [])?.map(
                        (banca: any, index: number) => (
                            <MenuItem
                                value={banca.id}
                                key={index}
                            >{`${banca.bancacassaNome} - ${banca.modopagamentoNome}`}</MenuItem>
                        )
                    )}
                    <ListSubheader>{t("Cassa")}</ListSubheader>
                    {(data?.expenseMethods?.cassa || [])?.map(
                        (banca: any, index: number) => (
                            <MenuItem
                                value={banca.id}
                                key={index}
                            >{`${banca.bancacassaNome} - ${banca.modopagamentoNome}`}</MenuItem>
                        )
                    )}
                </Select>
            </FormControl>

            <Box>
                <Link
                    href="/paymenttypes" // Replace with the actual URL
                    underline="hover"
                    sx={{
                        mt: 1,
                        fontSize: 14,
                        color: "gray",
                        display: "block",
                        textAlign: "center",
                    }}
                >
                    {t("Vai a gestione e modifica Dei modi di pagamento")}
                </Link>
            </Box>
        </Box>
    );
}
