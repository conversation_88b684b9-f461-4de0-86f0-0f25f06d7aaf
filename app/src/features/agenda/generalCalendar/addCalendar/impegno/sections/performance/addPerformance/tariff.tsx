import React, { useEffect } from "react";
import {
    Box,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Button,
    TextField,
    FormGroup,
    FormControlLabel,
    Checkbox,
    IconButton,
    InputAdornment,
} from "@vapor/react-material";
import useTariffs from "../../../hooks/useTariffs";
import { VERSIONS } from "../../../constants/constant";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faRight, faLeft } from "@fortawesome/pro-regular-svg-icons";
import { TYPES } from "../../../hooks/useSavePerformance";
import { useTranslation } from "@1f/react-sdk";

interface ITariffProps {
    savePerformanceParams: any;
    setSavePerformanceParams: any;
}

export default function Tariff(props: ITariffProps) {
    const { t } = useTranslation();
    const { savePerformanceParams, setSavePerformanceParams } = props;
    const { authorityData, rangeData, phaseData } = useTariffs(
        savePerformanceParams,
        setSavePerformanceParams
    );

    function parseDisplayValue(value: string) {
        if (!value) return "";
        const formatValue = parseFloat(value.split("-")[0]);
        return formatValue.toFixed(2);
    }

    const returnMaxValue = (value: string) => {
        if (!value) return 0;
        const formatMaxValue = parseFloat(value.split("-")[1]);
        return Math.round(formatMaxValue * 100) / 100; // Round to 2 decimal places without converting to string
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setSavePerformanceParams({ ...savePerformanceParams, [name]: value });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;

        setSavePerformanceParams((prev: any) => {
            if (checked && name === "contractLimits") {
                const updatedParams = { ...prev, contractLimits: checked };

                phaseData.values.forEach((phaseValue: any, index: number) => {
                    const maxLimit = parseFloat(phaseValue.value.split("-")[1]);
                    const faseKey = `fase${index + 1}`;
                    if (parseFloat(prev[faseKey]) > maxLimit) {
                        updatedParams[faseKey] = maxLimit.toFixed(2);
                    }
                });

                return updatedParams;
            }

            // Unchecked state: only update the contractLimits flag
            return { ...prev, [name]: checked };
        });
    };

    const handlePhaseValueChanges = (
        event: any,
        index: number,
        isOnBlur: boolean = false
    ) => {
        const { name, value } = event.target;

        if (isOnBlur && savePerformanceParams.contractLimits === true) {
            const phaseRange = phaseData.values[index].value;

            const enteredValue = parseFloat(value === "" ? 0 : value);
            const maxAllowedValue = returnMaxValue(phaseRange);

            const newValue =
                enteredValue > maxAllowedValue ? maxAllowedValue : enteredValue;

            setSavePerformanceParams((prev: any) => ({
                ...prev,
                [name]: newValue.toFixed(2),
            }));
        } else {
            setSavePerformanceParams((prev: any) => ({
                ...prev,
                [name]: value,
            }));
        }
    };

    const addValueByPercent = (percentage: number, phaseValue: number) => {
        const newValue = phaseValue + (phaseValue * percentage) / 100;
        return newValue.toFixed(2);
    };

    const handleIncrementDecrement = (
        delta: number,
        index: number,
        phaseValue: number,
        minPercentage: number, // Negative percentage allowed
        maxPercentage: number
    ) => {
        setSavePerformanceParams((prevParams: any) => {
            const currentPercentage = prevParams[`fase${index}_perc`] || 0;
            let updatedPercentage = currentPercentage + delta;

            // Clamp updatedPercentage within min and max limits
            if (updatedPercentage > maxPercentage) {
                updatedPercentage = maxPercentage; // Don't exceed max percentage
            } else if (updatedPercentage < minPercentage) {
                updatedPercentage = minPercentage; // Allow negative percentages
            }

            return {
                ...prevParams,
                [`fase${index}_perc`]: updatedPercentage,
                [`fase${index}`]: addValueByPercent(
                    updatedPercentage,
                    phaseValue
                ),
            };
        });
    };

    const handleInputChangesPercentage = (
        event: any,
        index: number,
        phaseValue: number,
        minPercentage: number = 0,
        maxPercentage: number = 0,
        isBlur: boolean = false // Add a parameter to indicate if it's a blur event
    ) => {
        const { value } = event.target;

        // Handle empty input
        const parsedValue =
            value === "" ? NaN : parseFloat(value.replace(/^0+(?=\d)/, ""));
        let newPercentage = isBlur && isNaN(parsedValue) ? 0 : parsedValue;

        setSavePerformanceParams((prevParams: any) => {
            if (isBlur) {
                if (newPercentage < minPercentage) {
                    newPercentage = minPercentage;
                } else if (newPercentage > maxPercentage) {
                    newPercentage = maxPercentage;
                } else if (isNaN(newPercentage)) {
                    newPercentage = 0;
                }
            }

            return {
                ...prevParams,
                [`fase${index}_perc`]: newPercentage,
                [`fase${index}`]: addValueByPercent(
                    isNaN(newPercentage) ? 0 : newPercentage,
                    phaseValue
                ),
            };
        });
    };

    //cleaning phase values when changing versions....
    useEffect(() => {
        if (phaseData.phases) {
            const updatedPhases = Object.fromEntries(
                phaseData.values.map((phase: any, index: number) => [
                    `fase${index + 1}`,
                    parseDisplayValue(phase.value),
                ])
            );

            setSavePerformanceParams((prevParams: any) => {
                const filteredParams = Object.keys(prevParams)
                    .filter((key) => !key.startsWith("fase"))
                    .reduce((acc, key) => {
                        acc[key] = prevParams[key];
                        return acc;
                    }, {} as any);

                return {
                    ...filteredParams,
                    ...updatedPhases,
                };
            });
        }
    }, [phaseData.phases, savePerformanceParams.version]);

    useEffect(() => {
        if (savePerformanceParams.version === "0" && phaseData.phases) {
            const DEFAULT_PERC_VALUE = Object.fromEntries(
                phaseData.values.map((_phase: any, index: number) => [
                    `fase${index + 1}_perc`,
                    0,
                ])
            );

            setSavePerformanceParams((prevParams: any) => ({
                ...prevParams,
                ...DEFAULT_PERC_VALUE,
            }));
        }
    }, [phaseData.phases, savePerformanceParams.version]);

    const importPhaseValues = (phaseDescription: any, phaseValue: any) => {
        setSavePerformanceParams((prevParams: any) => {
            const { importedPhases } = prevParams;

            // Check if the phase already exists in the array
            const phaseExists = importedPhases.some(
                (phase: any) =>
                    phase.descrizione === phaseDescription &&
                    phase.valore === phaseValue
            );

            let updatedPhases;
            if (phaseExists) {
                // If phase exists, remove it from the array
                updatedPhases = importedPhases.filter(
                    (phase: any) =>
                        phase.descrizione !== phaseDescription ||
                        phase.valore !== phaseValue
                );
            } else {
                // If phase doesn't exist, add it to the array
                updatedPhases = [
                    ...importedPhases,
                    {
                        descrizione: phaseDescription,
                        valore: phaseValue,
                    },
                ];
            }
            return {
                ...prevParams,
                importedPhases: updatedPhases,
            };
        });
    };

    //setting the type of the performance
    useEffect(() => {
        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            tipo: TYPES.tariff,
        }));
    }, [TYPES, savePerformanceParams.tipo]);

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                ml: 1,
            }}
        >
            <FormControl sx={{ width: 500, mt: 2 }}>
                <InputLabel>{"Tipo tariffario"}</InputLabel>
                <Select
                    name="version"
                    value={savePerformanceParams.version}
                    onChange={handleInputChanges}
                >
                    {(VERSIONS || [])?.map((version: any, index: number) => (
                        <MenuItem key={index} value={version.value}>
                            {version.label}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {savePerformanceParams.version === "-1" && (
                <TextField
                    label={t("Valore")}
                    variant="outlined"
                    name="contractValue"
                    type="number"
                    sx={{
                        "&.MuiTextField-root": {
                            width: "500px !important",
                            ml: 0,
                        },
                    }}
                    value={savePerformanceParams.contractValue}
                    onChange={handleInputChanges}
                />
            )}

            {savePerformanceParams.version !== "-1" && (
                <>
                    <FormControl sx={{ width: 500, mt: 1 }}>
                        <InputLabel>{t("Autorità")}</InputLabel>
                        <Select
                            name="contractAuthorities"
                            value={savePerformanceParams.contractAuthorities}
                            onChange={handleInputChanges}
                        >
                            {(authorityData || [])?.map(
                                (version: any, index: number) => (
                                    <MenuItem key={index} value={version.id}>
                                        {version.nome}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>

                    {rangeData.length > 0 && (
                        <FormControl sx={{ width: 500, mt: 1 }}>
                            <InputLabel>{t("Fascia")}</InputLabel>
                            <Select
                                name="contractRange"
                                value={savePerformanceParams.contractRange}
                                onChange={handleInputChanges}
                            >
                                {(rangeData || [])?.map(
                                    (range: any, index: number) => (
                                        <MenuItem key={index} value={range.id}>
                                            {range.nome}
                                        </MenuItem>
                                    )
                                )}
                            </Select>
                        </FormControl>
                    )}
                    <FormControl sx={{ mt: 1 }}>
                        <FormGroup row>
                            <FormControlLabel
                                value="right"
                                control={
                                    <Checkbox
                                        name="contractLimits"
                                        checked={
                                            savePerformanceParams.contractLimits
                                        }
                                        onChange={handleCheckboxChanges}
                                    />
                                }
                                label={t("Applica limiti da decreto")}
                                labelPlacement="end"
                            />
                        </FormGroup>
                    </FormControl>
                    {phaseData.phases?.length > 0 &&
                        phaseData.phases.map((phase: any, index: number) => {
                            const isImported = (
                                savePerformanceParams.importedPhases || []
                            ).some(
                                (selectedPhase: any) =>
                                    selectedPhase.descrizione ===
                                        phase.descrizione &&
                                    selectedPhase.valore ===
                                        savePerformanceParams[
                                            `fase${index + 1}`
                                        ]
                            );
                            return (
                                <React.Fragment key={index + 1}>
                                    {["2022", "2018", "2014"].includes(
                                        savePerformanceParams.version
                                    ) ? (
                                        <Box
                                            display="flex"
                                            alignItems="center"
                                            justifyContent="space-between"
                                            sx={{
                                                "& > .MuiTextField-root": {
                                                    width: "250px",
                                                    ml: 0,
                                                },
                                                mt: 2,
                                            }}
                                        >
                                            <TextField
                                                label={t(phase.descrizione)}
                                                variant="outlined"
                                                name={`fase${index + 1}`}
                                                type="number"
                                                value={
                                                    savePerformanceParams[
                                                        `fase${index + 1}`
                                                    ]
                                                }
                                                onChange={(event: any) =>
                                                    handlePhaseValueChanges(
                                                        event,
                                                        index
                                                    )
                                                }
                                                onBlur={(event: any) =>
                                                    handlePhaseValueChanges(
                                                        event,
                                                        index,
                                                        true
                                                    )
                                                }
                                            />
                                            <Button
                                                variant="outlined"
                                                sx={{ mt: 3.5 }}
                                                onClick={() =>
                                                    importPhaseValues(
                                                        phase.descrizione,
                                                        savePerformanceParams[
                                                            `fase${index + 1}`
                                                        ]
                                                    )
                                                }
                                            >
                                                {isImported
                                                    ? t("Rimuovi")
                                                    : t("Importa")}
                                            </Button>
                                        </Box>
                                    ) : (
                                        <Box
                                            display="flex"
                                            alignItems="center"
                                            justifyContent="space-between"
                                            sx={{
                                                "& > .MuiTextField-root": {
                                                    width: "180px",
                                                    ml: 0,
                                                },
                                                mt: 2,
                                            }}
                                        >
                                            <TextField
                                                label={t(
                                                    `${phase.descrizione}`
                                                )}
                                                disabled
                                                variant="outlined"
                                                name={`fase${index + 1}`}
                                                type="number"
                                                value={
                                                    savePerformanceParams[
                                                        `fase${index + 1}`
                                                    ]
                                                }
                                                onChange={handleInputChanges}
                                            />

                                            <Box
                                                display="flex"
                                                flexDirection="row"
                                                alignItems="center"
                                                sx={{ mr: 2 }}
                                            >
                                                <IconButton
                                                    color="primary"
                                                    onClick={() =>
                                                        handleIncrementDecrement(
                                                            -1,
                                                            index + 1,
                                                            parseFloat(
                                                                phaseData
                                                                    .values[
                                                                    index
                                                                ]?.value || "0"
                                                            ),
                                                            -parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].minorazione
                                                            ),
                                                            parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].maggiorazione
                                                            )
                                                        )
                                                    }
                                                    sx={{ mt: 3.5 }}
                                                >
                                                    <FontAwesomeIcon
                                                        icon={faLeft}
                                                        size="lg"
                                                    />
                                                </IconButton>
                                                <TextField
                                                    sx={{
                                                        width: "100px !important",
                                                    }}
                                                    label="Test"
                                                    name={`fase${
                                                        index + 1
                                                    }_perc`}
                                                    value={
                                                        savePerformanceParams[
                                                            `fase${
                                                                index + 1
                                                            }_perc`
                                                        ]
                                                    }
                                                    onChange={(e) =>
                                                        handleInputChangesPercentage(
                                                            e,
                                                            index + 1,
                                                            parseFloat(
                                                                phaseData
                                                                    .values[
                                                                    index
                                                                ]?.value || "0"
                                                            )
                                                        )
                                                    }
                                                    onBlur={(e) =>
                                                        handleInputChangesPercentage(
                                                            e,
                                                            index + 1,
                                                            parseFloat(
                                                                phaseData
                                                                    .values[
                                                                    index
                                                                ]?.value || "0"
                                                            ),
                                                            -parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].minorazione
                                                            ),
                                                            parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].maggiorazione
                                                            ),
                                                            true
                                                        )
                                                    }
                                                    type="number"
                                                    InputLabelProps={{
                                                        shrink: true,
                                                        style: {
                                                            visibility:
                                                                "hidden",
                                                        },
                                                    }}
                                                    InputProps={{
                                                        endAdornment: (
                                                            <InputAdornment position="end">
                                                                %
                                                            </InputAdornment>
                                                        ),
                                                    }}
                                                />
                                                <IconButton
                                                    color="primary"
                                                    onClick={() =>
                                                        handleIncrementDecrement(
                                                            1,
                                                            index + 1,
                                                            parseFloat(
                                                                phaseData
                                                                    .values[
                                                                    index
                                                                ]?.value || "0"
                                                            ),
                                                            -parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].minorazione
                                                            ),
                                                            parseFloat(
                                                                phaseData
                                                                    .phases[
                                                                    index
                                                                ].maggiorazione
                                                            )
                                                        )
                                                    }
                                                    sx={{ mt: 3.5 }}
                                                >
                                                    <FontAwesomeIcon
                                                        icon={faRight}
                                                        size="lg"
                                                    />
                                                </IconButton>
                                            </Box>

                                            <Button
                                                variant="outlined"
                                                sx={{ mt: 3.5 }}
                                                onClick={() =>
                                                    importPhaseValues(
                                                        phase.descrizione,
                                                        savePerformanceParams[
                                                            `fase${index + 1}`
                                                        ]
                                                    )
                                                }
                                            >
                                                {isImported
                                                    ? t("Rimuovi")
                                                    : t("Importa")}
                                            </Button>
                                        </Box>
                                    )}
                                </React.Fragment>
                            );
                        })}
                </>
            )}
        </Box>
    );
}
