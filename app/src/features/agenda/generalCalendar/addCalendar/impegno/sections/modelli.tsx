import { Box } from "@vapor/v3-components";
import Spinner from "../../../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import useFetchModelli from "../hooks/useFetchModelli";
import { useCalendarData } from "../context/CalendarDataContext";

export default function Modelli() {
    const { deadlineSaveParams } = useCalendarData();
    const {
        loadingModelli,
        listModelli,
        modelsQuery,
        setModelsQuery,
        navigateToModelli,
    } = useFetchModelli(deadlineSaveParams);

    const onPageChange = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setModelsQuery({
            ...modelsQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (loadingModelli) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="impegniModelli"
                columns={listModelli.columns}
                data={listModelli.rows || []}
                page={listModelli.page}
                totalRows={listModelli.totalRows}
                pageSize={listModelli.pageSize}
                onPageChangeCallback={onPageChange}
                onClickCallback={(uniqueid: string) =>
                    navigateToModelli(uniqueid)
                }
                onClickKey="uniqueid"
            />
        );
    };

    return <Box sx={{ ml: 2 }}>{renderDataTable()}</Box>;
}
