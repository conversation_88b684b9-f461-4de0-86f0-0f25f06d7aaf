import { useState } from "react";
import { Box, Button, TextField } from "@vapor/v3-components";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { IAnagrafiche } from "../interfaces/impegno.interface";
import Spinner from "../../../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { useTranslation } from "@1f/react-sdk";
import CustomAutocomplete from "../../../../../../custom-components/CustomAutocomplete";

export default function Anagrafiche(props: IAnagrafiche) {
    const { setInsertAnagraficheParams, anagraficheQuery, setAnagraficheQuery, listAnagrafiche, loading, handleAnagraficheSearch, anagraficheResult, searchAnagraficheLoading, selectedAnagrafica, setSelectedAnagrafica, handleAddAnagraficheOnCreate, handleAddAnagraficheOnUpdate, isUserEditing } = props;
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);

    const onPageChange = (model: GridPaginationModel, _: GridCallbackDetails<any>) => {
        setAnagraficheQuery({
            ...anagraficheQuery,
            page: model.page,
            pageSize: model.pageSize
        });
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return <CustomDataGrid name="impegniAnagrafiche" columns={listAnagrafiche.columns} data={listAnagrafiche.rows || []} page={listAnagrafiche.page} totalRows={listAnagrafiche.totalRows} pageSize={listAnagrafiche.pageSize} selectableRows={false} onPageChangeCallback={onPageChange} query={anagraficheQuery} setQuery={setAnagraficheQuery} />;
    };

    return (
        <Box>
            <Box sx={{ display: "flex", justifyContent: "end", gap: 2 }}>
                <CustomAutocomplete
                    sx={{ width: 400 }}
                    options={anagraficheResult || []}
                    selectOnFocus
                    clearOnBlur
                    limitTags={1}
                    value={selectedAnagrafica || null}
                    loading={searchAnagraficheLoading}
                    loadingText={t("Caricamento...")}
                    noOptionsText={t("Nessuna opzione")}
                    open={isOpen}
                    onOpen={() => setIsOpen(true)}
                    onClose={() => setIsOpen(false)}
                    componentsProps={{
                        popupIndicator: {
                            title: isOpen ? t("Chiudi") : t("Apri")
                        }
                    }}
                    getOptionLabel={(option: any) => {
                        return `${option.denominazione} (C.F: ${option.codicefiscale}) (PIVA: ${option.partitaiva})`;
                    }}
                    onInputChange={(_: any, newInputValue: any) => {
                        handleAnagraficheSearch(newInputValue);
                    }}
                    isOptionEqualToValue={(option: any, value: any) => option.id === value?.id}
                    onChange={(_: any, newValue: any) => {
                        setSelectedAnagrafica(newValue);
                        setInsertAnagraficheParams((prevParams: any[]) => {
                            if (newValue) {
                                return [...prevParams, newValue];
                            }
                            return prevParams;
                        });
                    }}
                    renderOption={(props: any, option: any) => (
                        <li {...props} key={option.id}>
                            {`${option.denominazione}${option.codicefiscale ? ` (C.F: ${option.codicefiscale})` : ""}${option.partitaiva ? ` (PIVA: ${option.partitaiva})` : ""}`}
                        </li>
                    )}
                    renderInput={(params: any) => <TextField {...params} placeholder={t("Cerca anagrafica per codice, nome, cognome, codice fiscale...")} />}
                />
                <Button startIcon={<AddCircleOutlineIcon />} onClick={!isUserEditing ? handleAddAnagraficheOnCreate : handleAddAnagraficheOnUpdate} variant="outlined">
                    {t("Collega")}
                </Button>
            </Box>
            <Box sx={{ ml: 2 }}>{renderDataTable()}</Box>
        </Box>
    );
}
