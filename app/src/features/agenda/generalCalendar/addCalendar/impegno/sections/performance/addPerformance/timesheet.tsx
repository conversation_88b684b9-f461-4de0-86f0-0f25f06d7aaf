import { Typography } from "@vapor/react-extended";
import {
    Box,
    FormControl,
    TextField,
    FormGroup,
    FormControlLabel,
    Checkbox,
    InputAdornment,
} from "@vapor/react-material";
import { useState, useEffect } from "react";
import { TYPES } from "../../../hooks/useSavePerformance";
import { useTranslation } from "@1f/react-sdk";

interface ITimesheetProps {
    savePerformanceParams: any;
    setSavePerformanceParams: any;
}

export default function Timesheet(props: ITimesheetProps) {
    const { t } = useTranslation();
    const { savePerformanceParams, setSavePerformanceParams } = props;
    const [errors, setErrors] = useState<Record<number, string>>({});

    const handleInputChange = (event: any, index: number) => {
        const { name, value } = event.target;
        const hourlyRate = parseFloat(value);

        setSavePerformanceParams((prevParams: any) => {
            const updatedUsers = prevParams.prestazioneUser.map(
                (user: any, i: number) => {
                    if (i === index) {
                        const minRate = parseFloat(user.min_hourly_rate);
                        const maxRate = parseFloat(user.max_hourly_rate);

                        let errorMessage = "";
                        if (hourlyRate < minRate) {
                            errorMessage = `Tariffa minima ${minRate}`;
                        } else if (hourlyRate > maxRate) {
                            errorMessage = `Tariffa massima ${maxRate}`;
                        }

                        // Set or clear the error message
                        setErrors((prevErrors: any) => ({
                            ...prevErrors,
                            [index]: errorMessage,
                        }));

                        return { ...user, [name]: value };
                    }
                    return user;
                }
            );

            return { ...prevParams, prestazioneUser: updatedUsers };
        });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setSavePerformanceParams((prevValues: any) => ({
            ...prevValues,
            [name]: checked,
        }));
    };

    useEffect(() => {
        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            tipo: TYPES.timesheet,
        }));
    }, [TYPES, savePerformanceParams.tipo]);

    return (
        <Box sx={{ ml: 1 }}>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    maxHeight: 200, // Maximum height to trigger scroll
                    overflowY: "auto", // Only vertical scroll
                    mt: 2,
                }}
            >
                <Typography
                    variant="bodyLarge500"
                    gutterBottom
                    component="div"
                    sx={{ mt: 2 }}
                >
                    {t("Dati timesheet")}
                </Typography>
                {(savePerformanceParams.prestazioneUser || []).map(
                    (user: any, index: number) => (
                        <Box
                            key={index}
                            display="flex"
                            alignItems="center"
                            sx={{
                                "& > .MuiTextField-root": {
                                    width: "150px",
                                    ml: 0,
                                    mr: 2, // Spacing to the right of TextField
                                },
                                mt: 2,
                            }}
                        >
                            <TextField
                                label=""
                                name="hourly_rate"
                                value={user.hourly_rate}
                                onChange={(event: any) =>
                                    handleInputChange(event, index)
                                }
                                error={Boolean(errors[index])}
                                helperText={errors[index] || ""}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            €
                                        </InputAdornment>
                                    ),
                                }}
                            />
                            <Typography sx={{ ml: 20 }}>
                                {user.nomeutente}
                            </Typography>
                        </Box>
                    )
                )}
            </Box>
            <FormControl sx={{ mt: 1 }} component="fieldset">
                <FormGroup>
                    {/* Evasa Checkbox */}
                    <Box display="flex" alignItems="center">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="processed"
                                    checked={
                                        savePerformanceParams?.processed ===
                                        undefined
                                            ? true
                                            : savePerformanceParams?.processed
                                    }
                                    onChange={handleCheckboxChanges}
                                />
                            }
                            label={t("Evasa")}
                            labelPlacement="start"
                            sx={{ ml: 1 }} // Adjust left alignment
                        />
                    </Box>

                    {/* Visibile Checkbox with additional text */}
                    <Box display="flex" alignItems="center">
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="visible"
                                    checked={savePerformanceParams.visible}
                                    onChange={handleCheckboxChanges}
                                />
                            }
                            label={t("Visibile")}
                            labelPlacement="start"
                            sx={{ ml: 0 }} // Adjust left alignment
                        />
                        <Typography variant="bodySmall" sx={{ ml: 1 }}>
                            {t("Rendi l'attività visibile agli utenti esterni")}
                        </Typography>
                    </Box>
                </FormGroup>
            </FormControl>
        </Box>
    );
}
