import { WEEK_OPTIONS } from "../../../../constants/constant";

function formatToRfc5545Date(date: any) {
    return date.replace(/-/g, "");
}

export function saveToRfc5545(deadlineSaveParams: any) {
    const data: any = {}; // Object to store RFC 5545 formatted rule
    const { rirtemplate, endType } = deadlineSaveParams;

    // Define mapping for frequencies
    const frequencyMapping: any = {
        daily: "DAILY",
        weekly: "WEEKLY",
        monthly: "MONTHLY",
        yearly: "YEARLY",
    };

    // Set FREQ based on rirtemplate
    data["FREQ"] = frequencyMapping[rirtemplate];

    // Set INTERVAL based on template
    if (rirtemplate === "daily") {
        const interval = deadlineSaveParams.ridailyinterval || 1;
        if (interval !== "1") data["INTERVAL"] = interval;
    } else if (rirtemplate === "weekly") {
        const interval = deadlineSaveParams.riweeklyinterval || 1;
        if (interval !== "1") data["INTERVAL"] = interval;

        // Add BYDAY rule if weekdays are selected
        const weekdays = WEEK_OPTIONS.filter(
            (day) => deadlineSaveParams[day.name] === day.value
        )
            .map((day) => day.value)
            .join(",");
        if (weekdays) data["BYDAY"] = weekdays;
    } else if (rirtemplate === "monthly") {
        const interval = deadlineSaveParams.rimonthlyinterval || 1;
        if (interval !== "1") data["INTERVAL"] = interval;

        // Add BYMONTHDAY rule for day of the month
        const dayOfMonth = deadlineSaveParams.rimonthlydayofmonthday;
        if (dayOfMonth) data["BYMONTHDAY"] = dayOfMonth;
    } else if (rirtemplate === "yearly") {
        const interval = deadlineSaveParams.riyearlyinterval || 1;
        if (interval !== "1") data["INTERVAL"] = interval;

        // Add BYMONTH for specific month
        const monthOfYear = deadlineSaveParams.riyearlydayofmonthmonth;
        if (monthOfYear) data["BYMONTH"] = monthOfYear;
    }

    // Handle end type: either by occurrences or specific end date
    if (endType === "BYOCCURRENCES") {
        const occurrences = deadlineSaveParams.occurrencesN || 1;
        if (occurrences !== "1") data["UNTIL"] = occurrences;
    } else if (endType === "BYENDDATE") {
        const endDate = deadlineSaveParams.rirangebyenddatecalendar;
        if (endDate) data["UNTIL"] = formatToRfc5545Date(endDate);
    }

    return data;
}
