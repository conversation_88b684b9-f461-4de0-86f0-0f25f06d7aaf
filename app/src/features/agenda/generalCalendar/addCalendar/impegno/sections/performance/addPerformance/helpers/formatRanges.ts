
// Prepare rangeData with formatted labels
export const formatRangeData = (ranges: any) => {
  const formatNumber = (id: any) =>
      id.toLocaleString("it-IT", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
      });

  return ranges.map((range: any) => {
      if (range.id && range.nome) {
          return range;
      }

      const { Field } = range;
      if (Field !== undefined) {
          let nome = Field;
          let id = Field;
          let maxValueReturn = Field;

          // Check for numeric ranges
          if (Field.startsWith("fascia_") && !Field.includes("indeterminato")) {
              const rangeParts = Field.replace("fascia_", "").split("_");
              if (
                  rangeParts.length === 2 &&
                  !isNaN(rangeParts[0]) &&
                  !isNaN(rangeParts[1])
              ) {
                  const minValue = parseFloat(rangeParts[0]) + 0.01;
                  const maxValue = parseFloat(rangeParts[1]);
                  nome = `${formatNumber(minValue)} - ${formatNumber(
                      maxValue
                  )}`;
                  maxValueReturn = formatNumber(maxValue)
              }
          }
          // Handle non-numeric ranges
          else {
              nome = Field.replace("fascia_", "").replace("_", " ");
          }

          return { id, nome, maxValueReturn };
      }
  });
};
