import React from "react";
import {
    Box,
    ListItem,
    List,
    Divider,
    ListItemText,
    Button,
    Typography,
    Grid,
    FormControl,
    MenuItem,
    Select,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useParams } from "react-router-dom";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useAgendaProvider } from "../providers/AgendaUpdateProvider";

const DateAggiuntivi = (props: any) => {

 
    const { t } = useTranslation();
    const [collega, setCollega] = React.useState(false);
    const [value, setValue] = React.useState("");

    const { datiValues, setDatiValues } = useAgendaProvider();
    
   
    const [parentData, setParentData] = React.useState({
        data: "",
        externalUid: "",
        nome: "",
        tipo: "",
        tipologia: "",
        referente: "",
    });
    const { items, rowData } = props;
    const { agenda } = useAgendaProvider();
    const { childTypes } = agenda as any;

    const { uniqueId } = useParams<{ uniqueId: string }>();

    const getId = () => {
        if (datiValues.value) {
            return datiValues.value;
        }
        if (value) {
            return value;
        }

        if (rowData?.form?.parentItemId) {
            return rowData?.form?.parentItemId;
        }

        return items[0]?.id;
    };

    const linkItem = usePostCustom(`item/link-item?noTemplateVars=true`);
    const unlinkItem = usePostCustom(`item/unlink-item?noTemplateVars=true`);

    const getParentDataReq = useGetCustom(
        `item/get-parent-data?itemId=${getId()}&noTemplateVars=true`
    );

    React.useEffect(() => {
        getParentData();
    }, [value]);

    React.useEffect(() => {
        if (rowData?.form?.parentItemId) {
            setCollega(true);
            setDatiValues({ value: getId(), collega });
        } else {
            setCollega(false);
        }
    }, []);

    React.useEffect(() => {
        setCollega(datiValues.collega);
    }, [datiValues.collega]);

    const getParentData = async () => {
        const response: any = await getParentDataReq.doFetch(true);
        setParentData(response.data);
        setValue(getId());
        setDatiValues({ ...datiValues, value: getId() });
    };

    const handleChange = (event: any) => {
        setValue(event.target.value as string);
        setDatiValues({ value: event.target.value, collega });
    };

    const changeCollega = async () => {
        const formData = new FormData();
        formData.append("parentId", getId());
        formData.append("childUid", uniqueId ?? "");

        if (!collega) {
            await linkItem.doFetch(true, formData);
            setCollega(true);
            setDatiValues({ ...datiValues, collega: true });
        } else {
            await unlinkItem.doFetch(true, formData);
            setCollega(false);
            setDatiValues({ ...datiValues, collega: false });
        }
    };

    return (
        <>
            {childTypes && (
                <>
                    {" "}
                    <Typography variant="body700" gutterBottom component="div">
                        {t("N.B.")}
                    </Typography>
                    {childTypes === 1 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Timesheet può essere collegato solo a oggetti di tipo"
                                )}
                            </Typography>
                            <List>
                                <ListItem>
                                    <ListItemText primary={t("Impegno")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Udienza")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Libero")} />
                                </ListItem>
                            </List>
                        </>
                    )}
                    {childTypes === 2 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Movimento può essere collegato solo a oggetti di tipo"
                                )}
                            </Typography>
                            <List>
                                <ListItem>
                                    <ListItemText primary={t("Timesheet")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Impegno")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Udienza")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Libero")} />
                                </ListItem>
                            </List>
                        </>
                    )}
                    {childTypes === 3 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Impegno può essere collegato solo a oggetti di tipo 'Libero'"
                                )}
                            </Typography>
                        </>
                    )}
                    {childTypes === 4 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un'Udienza può essere collegata solo a oggetti di tipo 'Libero'"
                                )}
                            </Typography>
                        </>
                    )}
                    {!items && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Non sono presenti oggetti a cui poter collegare questa voce"
                                )}
                            </Typography>
                        </>
                    )}
                </>
            )}

            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                {collega ? (
                    <Typography>
                        {t("Questo Udienza È collegato a ")}
                        <b>
                            {" "}
                            {`${parentData.tipologia} - ${parentData.nome}`}{" "}
                        </b>
                    </Typography>
                ) : (
                    <FormControl
                        variant="outlined"
                        sx={{ width: 1 / 4, mt: 4 }}
                    >
                        <Select
                            labelId="select-label"
                            value={value}
                            label="Oggetto"
                            onChange={handleChange}
                            name="importaItemsSelect"
                        >
                            {items?.map((item: any) => (
                                <MenuItem key={item.id} value={item.id}>
                                    {item.tipo} -{item.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                )}
                <Button
                    size="small"
                    variant="contained"
                    color={`${collega ? "error" : "primary"}`}
                    sx={{ mt: collega ? 0 : 4 }}
                    onClick={() => changeCollega()}
                >
                    {collega ? t("Scollega") : t("Collega")}
                </Button>
            </Box>

            <Divider className="MuiDivider-VaporLight" sx={{ mt: 2 }} />
            <Box style={{ width: 350 }} sx={{ mt: 3 }}>
                <Grid container spacing={2}>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-end" }}
                    >
                        {t("Nome")}
                    </Grid>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-start" }}
                    >
                        {parentData.nome}
                    </Grid>
                </Grid>

                <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-end" }}
                    >
                        {t("Tipologia")}
                    </Grid>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-start" }}
                    >
                        {parentData.tipologia}
                    </Grid>
                </Grid>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-end" }}
                    >
                        {t("Data")}
                    </Grid>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-start" }}
                    >
                        {parentData.data}
                    </Grid>
                </Grid>

                <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-end" }}
                    >
                        {t("Referente")}
                    </Grid>
                    <Grid
                        item
                        xs={6}
                        sx={{ display: "flex", justifyContent: "flex-start" }}
                    >
                        {parentData.referente || "-"}
                    </Grid>
                </Grid>
            </Box>
        </>
    );
};

export default DateAggiuntivi;
