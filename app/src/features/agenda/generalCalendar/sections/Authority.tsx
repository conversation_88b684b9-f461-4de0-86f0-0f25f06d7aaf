import React, { useState, useEffect, useCallback } from "react";
import { Box, Stack, Button, Typography, TextField } from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import AddIcon from "@mui/icons-material/Add";
import { useTranslation } from "@1f/react-sdk";
import { useAgendaHooks } from "../hooks/useAgendaHooks";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useNavigate, useLocation } from "react-router-dom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { debounce } from "lodash";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const getSchemaValidation = (t: any) => {
    return yup.object().shape({
        officeSearch: yup.string().required(t("Autorità obbligatorio")),
        insertAuthority: yup.string(),
        insertAuthorityCity: yup.string()
    });
};

export const Authority = (props: any) => {
    const { selectedFile, hideAuthority } = props;
    const { t } = useTranslation();
    const { control, handleSubmit, setValue, watch, reset } = useForm({
        resolver: yupResolver(getSchemaValidation(t)),
        defaultValues: {
            officeSearch: "",
            insertAuthority: "",
            insertAuthorityCity: ""
        }
    });

    const [autoritySearch, setAutoritySearch] = React.useState("");
    const [increment, setIncrement] = useState(0);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [isAutocompleteDisabled, setIsAutocompleteDisabled] = useState(false);
    const [autority, setAutority] = React.useState([]);
    const [autorityNewField, setAuthorityNewField] = useState(false);
    const [selectedAutority, setSelectedAutority] = React.useState({
        value: "",
        label: ""
    });

    const { cities } = useAgendaHooks();

    const getAuthorityRequest = useGetCustom(`archive/searchoffices?noTemplateVars=true`);

    const saveAuthorityRequest = usePostCustom("archiveagenda/savefileauthority?noTemplateVars=true");

    const saveNewAuthorityRequest = usePostCustom("authorities/remotesave?noTemplateVars=true");

    const debouncedFetch = useCallback(
        debounce(async (search) => getData(search), 500),
        []
    );

    useEffect(() => {
        const initialFetch = async () => getData("");
        initialFetch();

        return () => debouncedFetch.cancel();
    }, [debouncedFetch]);

    useEffect(() => {
        if (autoritySearch) {
            debouncedFetch(autoritySearch);
        }
    }, [autoritySearch, debouncedFetch]);

    const getData = async (search: string) => {
        const response: any = await getAuthorityRequest.doFetch(true, {
            q: search,
            increment: increment
        });
        setAutority(
            response.data.map((item: any) => ({
                value: item.id,
                label: `${item.nome}`,
                increment: item.increment
            }))
        );
    };

    const navigate = useNavigate();
    const location = useLocation();

    const values = watch();

    const saveNewAuthority = async () => {
        const query = {
            authorityName: values.insertAuthority,
            authorityCity: values.insertAuthorityCity
        };

        const response: any = await saveNewAuthorityRequest.doFetch(true, query);

        if (response.data === false) {
            setShowErrorMessage(true);
        } else {
            setAutoritySearch(response.data.descripion as string);
            setSelectedAutority({
                value: response.data.id,
                label: response.data.description
            });
            setValue("officeSearch", response.data.id);
            setAuthorityNewField(!autorityNewField);
            setIsAutocompleteDisabled(!isAutocompleteDisabled);
        }
    };

    const addAutorityField = () => {
        setAuthorityNewField(!autorityNewField);
    };

    const onSubmit = async () => {
        const body = {
            ufficio_giudiziario: selectedAutority.value,
            fileUniqueid: selectedFile.fileUniqueid ? selectedFile.fileUniqueid : selectedFile.value
        };

        const response: any = await saveAuthorityRequest.doFetch(true, body);
        if (response.data) {
            reset();
            hideAuthority(selectedFile.fileUniqueid ? selectedFile.fileUniqueid : selectedFile.value);
        }
    };

    const handleShowMore = () => {
        setIncrement((prev) => prev + 1);
        getData(autoritySearch);
    };

    return (
        <>
            <>
                <ToastNotification showNotification={showErrorMessage} setShowNotification={setShowErrorMessage} severity="error" text={t("Autorità già presente")} />{" "}
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box display="flex" gap={1} sx={{ mt: 2 }}>
                        <Stack alignItems="end" autoComplete="off" component="form" direction="row" gap={1}>
                            {" "}
                            <>
                                {" "}
                                <Typography variant="bodyLarge700" component="div" color="primary.main" gutterBottom>
                                    {t("Assegna autorità alla pratica")}
                                </Typography>
                            </>
                        </Stack>
                    </Box>
                    {autorityNewField ? (
                        <Stack direction="row" spacing={2} alignItems="flex-end" sx={{ mt: 2 }}>
                            <FormInput control={control} name="insertAuthority" type="text" variant="outlined" sx={{ width: 200 }} />

                            <FormInput
                                control={control}
                                name="insertAuthorityCity"
                                type="select"
                                options={cities.map((city: any) => ({
                                    value: city.id,
                                    label: city.nome
                                }))}
                                sx={{ width: 200 }}
                            />

                            <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center" sx={{ paddingTop: "20px" }}>
                                <Button
                                    size="small"
                                    variant="contained"
                                    sx={{
                                        minWidth: "auto",
                                        marginTop: "30px"
                                    }}
                                    onClick={() => saveNewAuthority()}
                                >
                                    {t("Salva")}
                                </Button>
                                <Button type="button" size="small" variant="outlined" onClick={() => addAutorityField()}>
                                    {t("Annulla")}
                                </Button>
                            </Stack>
                        </Stack>
                    ) : (
                        <Box display="flex" gap={1} sx={{ mt: 2 }}>
                            <CustomAutocomplete
                                options={autority || []}
                                disabled={isAutocompleteDisabled}
                                isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                                value={selectedAutority}
                                renderInput={(params: any) => <TextField {...params} placeholder={t("Cerca autorità per nome…")} />}
                                loadingText={t("Caricamento...")}
                                noOptionsText={t("Nessuna opzione")}
                                renderOption={(props: any, option: any) => {
                                    if (option.increment != undefined) {
                                        return (
                                            <li {...props} onClick={handleShowMore}>
                                                <span
                                                    style={{
                                                        color: "blue",
                                                        cursor: "pointer"
                                                    }}
                                                >
                                                    {t("Mostra altre")}
                                                </span>
                                            </li>
                                        );
                                    }
                                    return (
                                        <li {...props}>
                                            <span
                                                dangerouslySetInnerHTML={{
                                                    __html: option.label
                                                }}
                                            />
                                        </li>
                                    );
                                }}
                                onInputChange={(_event: any, value: any, reason: string) => {
                                    if (reason === "input") {
                                        setAutoritySearch(value);
                                    } else {
                                        setAutoritySearch("");
                                    }
                                }}
                                onChange={(_event: any, item: any) => {
                                    setSelectedAutority(item);
                                    setIsAutocompleteDisabled(true);
                                    setValue("officeSearch", item.value);
                                }}
                            />
                            <Button
                                size="small"
                                variant="contained"
                                sx={{
                                    minWidth: "auto"
                                }}
                                onClick={() => addAutorityField()}
                            >
                                <AddIcon />
                            </Button>

                            <Button
                                size="small"
                                variant="outlined"
                                sx={{
                                    minWidth: "auto"
                                }}
                                disabled={!isAutocompleteDisabled}
                                onClick={() => {
                                    setIsAutocompleteDisabled(false);
                                    setSelectedAutority({
                                        value: "",
                                        label: ""
                                    });
                                }}
                            >
                                {t("Cambia Autorità")}
                            </Button>
                        </Box>
                    )}
                    <Box
                        component={"div"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 350
                            }
                        }}
                    >
                        <Stack alignItems="end" autoComplete="off" component="form" direction="row" gap={1}>
                            {" "}
                            <>
                                {" "}
                                <Typography variant="bodySmall" component="div" color="primary.main" gutterBottom>
                                    <i>
                                        <b>{t("NOTA")}:</b>

                                        {t(" Al fine di inserire la prima udienza è necessario assegnare un'autorità alla pratica.")}
                                    </i>
                                </Typography>
                            </>
                        </Stack>
                    </Box>
                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 150
                            }
                        }}
                    >
                        <Button type="button" variant="outlined" onClick={() => navigate(location?.state?.prevPath)} sx={{ mr: 1 }}>
                            {t("Annulla")}
                        </Button>
                        <Button type="submit" variant="contained" sx={{ mr: 1 }}>
                            {t("Conferma")}
                        </Button>
                    </Box>
                </form>
            </>
        </>
    );
};
