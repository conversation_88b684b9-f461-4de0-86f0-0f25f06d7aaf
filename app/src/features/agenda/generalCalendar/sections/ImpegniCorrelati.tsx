import { Box, Button, Typography } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import AddIcon from "@mui/icons-material/Add";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { useAgendaUpdateHooks } from "../hooks/useAgendaUpdateHooks";
import Spinner from "../../../../custom-components/Spinner";

interface IImpegniCorrelatiProps {
    showSchedaImpegnio: (uniqueId?: string) => void;
}

export const ImpegniCorrelati = (props: IImpegniCorrelatiProps) => {
    const { t } = useTranslation();

    const { showSchedaImpegnio } = props;

    const { state, onPageChange, defaultLAParams, setDefaultLAParams } =
        useAgendaUpdateHooks();

    const onClickCallback = (id: any) => {
        const uniqueId = state.listAgenda.currentPage.find(
            (item: any) => item.id === id
        )?.uniqueid;
        showSchedaImpegnio(uniqueId);
    };


    const newImpegno = () => {
        // console.log(uniqueId)
        showSchedaImpegnio();
    };
    const renderDataTable = () => {
        if (!state.listAgenda) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="impegniCorrelati"
                columns={state.columns}
                data={state.listAgenda.currentPage}
                page={defaultLAParams.page}
                pageSize={defaultLAParams.pageSize}
                totalRows={state.listAgenda.totalRows}
                query={defaultLAParams}
                setQuery={setDefaultLAParams}
                loading={state.listAgenda.loading || state.listAgenda.loading}
                onPageChangeCallback={onPageChange}
                onClickCallback={onClickCallback}
                onClickKey="id"
            />
        );
    };

    return (
        <>
            {" "}
            <Box
                display="flex"
                alignItems="end"
                justifyContent="flex-end"
                gap={1}
                sx={{ paddingBottom: "1rem" }}
            >
                <Button
                    startIcon={<AddIcon />}
                    size="small"
                    variant="contained"
                    onClick={newImpegno}
                >
                    {" "}
                    {t("Nuovo impegno")}
                </Button>
            </Box>
            <Box display="flex" alignItems="end">
                {" "}
                <Typography
                    variant="bodyLarge500"
                    gutterBottom
                    component="div"
                    color="primary.error"
                >
                    !{" "}
                </Typography>
                <Typography variant="body500" gutterBottom component="div">
                    {t("  = Impegni importanti")}{" "}
                </Typography>
            </Box>
            <Box sx={{ overflow: "auto", paddingRight: "16px" }}>
                {" "}
                {renderDataTable()}
            </Box>
        </>
    );
};
