import React, { useEffect, useState } from "react";
import { Ta<PERSON>, Tab } from "@vapor/react-extended";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { SchedaUdienzaTab } from "./SchedaUdienzaTab";
import { Box, Button,  TableBody,TableContainer, TableRow } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useAgendaUpdateHooks } from "../hooks/useAgendaUpdateHooks";
import { useNavigate, useSearchParams } from "react-router-dom";
import DateAggiuntivi from "./DatiAggiuntivi";
import { ImpegniCorrelati } from "./ImpegniCorrelati";
import { useCalendarData } from "../addCalendar/impegno/context/CalendarDataContext";
import { Macro } from "./MacroTab";
import SchedaImpegnoUpdate from "../addCalendar/impegno/sections/schedaImpegno";
import useSearchPractica from "../addCalendar/impegno/hooks/useSearchPractica";
import useSearchImpegno from "../addCalendar/impegno/hooks/useSearchImpegno";
import useSaveDeadlines from "../addCalendar/impegno/hooks/useSaveDeadlines";
import { useAgendaFormHook } from "../hooks/useAgendaFormHook";
import { AgendaUpdateProvider } from "../providers/AgendaUpdateProvider";
import moment from "moment";
import { removeLinks } from "../../../../utilities/utils";
import { useAgendaParams } from "../../hooks/useAgendaParams.hook";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { DEFAULT_DEADLINE_PARAMS } from "../addCalendar/impegno/constants/saveParams";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

type View = "scheda" | "macro" | "dati";
type Validation = "default" | "timesheet";

export const Update = () => {
    const [value, setValue] = React.useState(0);
    const [overlappingModal, setOverlappingModal] = useState(false);
    const [displaySchedaImpegnio, setDisplaySchedaImpegnio] = useState(false);
    const [validation, setValidation] = useState<Validation>("default");
    const [pendingFormData, setPendingFormData] = useState<any>(null);
    const [conflictsMessage, setConflictsMessage] = useState<string>("");

    const { rowDataUrl, items, prevPath, defaultParams, fileHeaderData } =
        useAgendaParams();


    const {
        data,
        fetchUserGroupById,
        loggedUserName,
        fetchCalendarData,
        deadlineSaveParams,
        setDeadlineSaveParams,
        selectedPraticaName,
        setSelectedPraticaName,
        fetchAgain: fetchCalendarDataAgain
    } = useCalendarData();


    const {
        practicaSearchResult,
        handlePracticaSearch,
        setPracticaSearchResult,
        searchPracticaLoading,
        setIsArchiveChecked,
    } = useSearchPractica();

    const { impegnoSearchResult, handleImpegnoSearch, searchImpegnoLoading } =
        useSearchImpegno();

    useEffect(() => {
        if (value === 0 || value === 1) {
            setDisplaySchedaImpegnio(false);
        }
    }, [value]);

    const [view, setView] = useState<View>("scheda");

    const usePostRequest = usePostCustom(`agenda/save?noTemplateVars=true`);

    const overlappingDeadlinesCheck = usePostCustom(`deadlines/overlapping-deadlines?noTemplateVars=true`);

    const { t } = useTranslation();

    const { state, rowData, deadlineLinkuId, fetchAgain } =
        useAgendaUpdateHooks();


    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        if (newValue === 0 || newValue === 1) {
            setDisplaySchedaImpegnio(false);
        }
        if (view === "macro") {
            if (newValue < 1) {
                setValue(0);
            } else setValue(2);
        } else setValue(newValue);
    };

    const method: any = useAgendaFormHook(validation);
    const { watch } = method;
    const values = watch();

    const changeView = (view: View) => {
        if (view === "macro") {
            onSubmit(values, view, false, true);
        }
        setView(view);
    };
 
    const setSchedaImpegnio = async (uniqueId?: string) => {
        if(uniqueId){
            fetchCalendarDataAgain(uniqueId);
        } else {    
            const checkedUsers: any = rowData?.form?.referente.map((checkedUser: any) =>
            data.usersData.find((user: any) => user.id === checkedUser.id)
        );

      
            setDeadlineSaveParams({
                ...DEFAULT_DEADLINE_PARAMS,
                deadlineFileUniqueid: rowData?.form?.agendaArchiveuid || "",
                deadlineLinkuid: deadlineLinkuId || "",
                deadlineUser: checkedUsers
            });
            setSelectedPraticaName(null);
        }
        setDisplaySchedaImpegnio(!displaySchedaImpegnio);
        setValue(2);
    };

    const { saveDeadline, requiredFields, setRequiredFields } =
        useSaveDeadlines('agendadeadlines');

    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    const handleOverlapingModalConfirm = () => {
        localStorage.setItem("overlappingDeadlinesRound", "bypass");
        setOverlappingModal(false);

        if (pendingFormData) {
            onSubmit(pendingFormData.data, pendingFormData.type, pendingFormData.isReinvia, pendingFormData.isFromMacro);
            setPendingFormData(null);
        }
    };

    const handleOverlapingModalCancel = () => {
        localStorage.removeItem("overlappingDeadlinesRound");
        setOverlappingModal(false);
        setPendingFormData(null);
    };

    const onSubmit = async (
        data: any,
        type: string,
        isReinvia?: boolean,
        isFromMacro?: boolean,
        isCancelEvasa?: boolean
    ) => {
        const dataCopy = JSON.parse(JSON.stringify(data));

        const isReinviaSet = localStorage.getItem("isReinviaSet");

        const formatDate = (isoString: string) => {
            const date = new Date(isoString);
            return date.toLocaleDateString("it-IT");
        };

        const formData = new FormData();
        const hourAndMinutes = data.ora
            ? moment(data.ora).format("HH:mm").split(":")
            : ["09", "00"];

        formData.append("uniqueid", isReinviaSet ? "" : data.agendaUniqueid);
        if(isReinviaSet || isReinvia === true){
             formData.append("fkUniqueIdUdienzaReinvio", data.agendaUniqueid);
        }
        if (isReinvia === true) {
            if(!isReinviaSet && !isCancelEvasa) {
                formData.append("agendaEvasa", "on");
            }
        } else {
            formData.append("fkUniqueIdUdienzaReinvio", "");
        }

        formData.append("archiveuid", data.agendaArchiveuid);
        formData.append("data", formatDate(data.date));
        formData.append("ora", hourAndMinutes[0]);
        formData.append("minuti", hourAndMinutes[1]);
        formData.append("agendaDaysBefore", data.agendaDaysBefore);
        formData.append("agendaPeriod", data.agendaPeriod);


        data.referente.forEach((ref: any) =>
            formData.append(
                "referente[]",
                ref.hasOwnProperty("id") ? ref.id : ref.value
            )
        );

        formData.append("attivita", data.attivita);
        formData.append("sezione", data.agendaSezione);
        formData.append("autorita", data.agendaAutorita);
        formData.append("citta", data.agendaCitta);
        formData.append("istruttore", data.agendaIstruttore);
        formData.append("annotazioni", data.agendaAnnotazioni);
        if (data.agendaBillable === "true")
            formData.append("agendaBillable", data.agendaBillable);
        if (data.agendaEvasa === "true")
            formData.append("agendaEvasa", data.agendaEvasa);
        formData.append("statopratica", data.agendaStatopratica);
        formData.append("parentItemId", data.parentItemId || "");
        if (data?.agendaNonEvadere === "true") {
            formData.append("agendaNonEvadere", "on");
        }
        if (data.referente) {
        formData.append(
            "referenteData",
            data?.referente
                .map((ref: any) =>
                    ref.hasOwnProperty("id") ? ref.id : ref.value
                )
                .join(",")
        );
        }


        const overlappingDeadlinesData = {
            deadlineDate: formatDate(data.date),
            deadlineHours: hourAndMinutes[0],
            deadlineMinutes: hourAndMinutes[1],
            deadlinePeriod: data.agendaPeriod,
            deadlineUniqueid: data.agendaUniqueid || "",
            deadlineUser: data.referente.map((ref: any) => {
                const id = ref?.id ?? ref?.value;
                return id !== undefined ? String(id) : "";
            }).filter((id: string) => id !== ""),
        };
        const overlappingDeadlines: any = await overlappingDeadlinesCheck.doFetch(true,
            overlappingDeadlinesData,
            "post",
            "json",
            true
        )
        if(overlappingDeadlines.data.length > 0){
            setConflictsMessage(`${overlappingDeadlines.data.join("\n")}\n\n${t("Vuoi continuare comunque?")}`);
        }

        const overlappingDeadlinesRoundChecked = localStorage.getItem("overlappingDeadlinesRound");

        if(overlappingDeadlines.data.length > 0 && overlappingDeadlinesRoundChecked !== "bypass"){
            setPendingFormData({
                data: dataCopy,
                type,
                isReinvia,
                isFromMacro
            });

            setOverlappingModal(true);
            setConflictsMessage(`${overlappingDeadlines.data.join("\n")}\n\n${t("Vuoi continuare comunque?")}`);
            localStorage.setItem("overlappingDeadlinesRound", "checked");
            return;
        }
        localStorage.removeItem("overlappingDeadlinesRound");


        await usePostRequest.doFetch(true, formData);
        localStorage.removeItem("isReinviaSet");

        if (typeof isReinvia === "boolean" && isReinvia) {
            fetchAgain(true, data.agendaArchiveuid);
            localStorage.setItem("isReinviaSet", "true");
            return;
        }

        if (typeof isFromMacro === "boolean" && isFromMacro) {
            fetchAgain(true, data.agendaArchiveuid);
            return;
        }

        if (type != "timesheet") {
            navigate(prevPath, {
                state: {
                    defaultParams: defaultParams,
                },
            });
        }

    };

    const handleSaveAction = async () => {
        try {
            deadlineSaveParams.deadlineLinkuid = deadlineLinkuId;
            const idScadenzario = await saveDeadline(deadlineSaveParams);
            if (idScadenzario) {
                navigate(prevPath || "/calendar/calendar");
                // setValue(1);
            }
        } catch (error: any) {
            console.error("Error during save action:", error);
        }
    };

    const isOnUpdate = !!deadlineSaveParams.deadlineFileUniqueid;
   
    return (
        <>
            <VaporPage>
                <PageTitle
                    title={removeLinks(state?.rowData?.form?.agendaHeader, " ")}
                    showBackButton={true}
                    pathToPrevPage={prevPath}
                    stateOptions={{
                        defaultParams: defaultParams,
                        fileHeaderData,
                    }}
                />

                <ConfirmModal
                    open={overlappingModal}
                    handleDecline={() => handleOverlapingModalCancel()}
                    handleAgree={() => handleOverlapingModalConfirm()}
                    decline={t("Annulla")}
                    agree={t("Conferma")}
                    title={t("Conflitti Attività:")}>
                    <TableContainer>
                        <TableBody>
                            {conflictsMessage.split("\n").map((row: string, index: number) => (
                                <TableRow key={index}>
                                    {row}
                                </TableRow>
                            ))}
                        </TableBody>
                    </TableContainer>
                </ConfirmModal>

                <VaporPage.Section>
                    <>
                        <AgendaUpdateProvider>
                            <Box>
                                <Tabs
                                    value={value}
                                    onChange={handleChange}
                                    size="extraSmall"
                                    variant="standard"
                                >
                                    <Tab
                                        label={
                                            view === "scheda"
                                                ? t("Scheda Udienza")
                                                : t("Macro")
                                        }
                                        {...a11yProps(0)}
                                    />
                                    {deadlineLinkuId && view === "scheda" && (
                                        <Tab
                                            label={t("Impegni correlati")}
                                            {...a11yProps(1)}
                                        />
                                    )}

                                    {displaySchedaImpegnio && (
                                        <Tab
                                            label={t("Scheda impegno")}
                                            {...a11yProps(2)}
                                        />
                                    )}
                                    <Tab
                                        label={t("Dati aggiuntivi")}
                                        {...a11yProps(
                                            displaySchedaImpegnio ? 3 : 2
                                        )}
                                    />
                                </Tabs>
                            </Box>
                            <CustomTabPanel value={value} index={0}>
                                <Box sx={{ p: 1 }}>
                                    {view === "scheda" && (
                                        <SchedaUdienzaTab
                                            rowData={rowData}
                                            changeView={changeView}
                                            method={method}
                                            onSubmit={onSubmit}
                                            saveLoading={usePostRequest.loading}
                                            setValidation={setValidation}
                                            prevPath={prevPath}
                                            rowDataUrl={rowDataUrl}
                                            items={items}
                                            defaultParams={defaultParams}
                                            fileHeaderData={fileHeaderData}

                                            //tabKey={value}
                                        />
                                    )}
                                    {view === "macro" && (
                                        <Macro
                                            rowData={values}
                                            changeView={changeView}
                                        />
                                    )}
                                </Box>
                            </CustomTabPanel>
                            {deadlineLinkuId && view === "scheda" && (
                                <CustomTabPanel value={value} index={1}>
                                    <ImpegniCorrelati
                                        showSchedaImpegnio={setSchedaImpegnio}
                                    />
                                </CustomTabPanel>
                            )}
                            {displaySchedaImpegnio && (
                                <CustomTabPanel value={value} index={2}>
                                    <SchedaImpegnoUpdate
                                        data={data}
                                        practicaSearchResult={
                                            practicaSearchResult
                                        }
                                        setPracticaSearchResult={
                                            setPracticaSearchResult
                                        }
                                        handlePracticaSearch={
                                            handlePracticaSearch
                                        }
                                        searchPracticaLoading={
                                            searchPracticaLoading
                                        }
                                        impegnoSearchResult={
                                            impegnoSearchResult
                                        }
                                        handleImpegnoSearch={
                                            handleImpegnoSearch
                                        }
                                        searchImpegnoLoading={
                                            searchImpegnoLoading
                                        }
                                        deadlineSaveParams={deadlineSaveParams}
                                        setDeadlineSaveParams={
                                            setDeadlineSaveParams
                                        }
                                        fetchUserGroupById={fetchUserGroupById}
                                        loggedUserName={loggedUserName}
                                        fetchCalendarData={fetchCalendarData}
                                        setIsArchiveChecked={
                                            setIsArchiveChecked
                                        }
                                        requiredFields={requiredFields}
                                        setRequiredFields={setRequiredFields}
                                        selectedPraticaName={
                                            selectedPraticaName
                                        }
                                        setSelectedPraticaName={
                                            setSelectedPraticaName
                                        }
                                        hideFileSelect={(location.pathname.includes("archivedeadlines/deadlines") || searchParams?.get("from") === "archivedeadlines" || prevPath?.includes("archiveagenda/agenda"))}
                                    />
                                    <Box
                                        sx={{
                                            "& > :not(style)": {
                                                m: 1,
                                                width: 150,
                                            },
                                        }}
                                    >
                                        <Button
                                            type="button"
                                            variant="outlined"
                                            onClick={() =>
                                                setDisplaySchedaImpegnio(
                                                    !displaySchedaImpegnio
                                                )
                                            }
                                            sx={{ mr: 1 }}
                                        >
                                            {t("Annulla")}
                                        </Button>

                                        {isOnUpdate && (
                                            <Button
                                                type="button"
                                                variant="outlined"
                                                onClick={() =>
                                                    navigate(
                                                        `/legacy/archive/summary?uid=${state?.rowData?.form?.agendaArchiveuid}`
                                                    )
                                                }
                                                sx={{ mr: 1 }}
                                            >
                                                {t("Vai alla pratica")}
                                            </Button>
                                        )}

                                        <Button
                                            type="submit"
                                            variant="contained"
                                            onClick={handleSaveAction}
                                            sx={{ mr: 1 }}
                                        >
                                            {t("Conferma")}
                                        </Button>
                                    </Box>
                                </CustomTabPanel>
                            )}
                            <CustomTabPanel
                                value={value}
                                index={displaySchedaImpegnio ? 3 : 2}
                            >
                                <Box sx={{ p: 1 }}>
                                    <DateAggiuntivi
                                        items={items}
                                        rowData={rowData}
                                    />
                                </Box>
                            </CustomTabPanel>
                        </AgendaUpdateProvider>
                    </>
                </VaporPage.Section>
            </VaporPage>
        </>
    );
};
