import React, {
    ReactNode,
    useState,
    useCallback,
    useContext,
    useReducer,
} from "react";
import { useLocation, useParams } from "react-router-dom";
import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

const INITIALIZE = "INITIALIZE";
const SET_AGENDA = "SET_AGENDA";
const SET_VALUES = "SET_VALUES";
const SET_LOCATION = "SET_LOCATION";
const SET_ROW_VALUES = "SET_ROW_VALUES";

export type ActionMap<M extends { [index: string]: any }> = {
    [Key in keyof M]: M[Key] extends undefined
        ? { type: Key }
        : { type: Key; payload: M[Key] };
};

type AgendaActionTypes = {
    [INITIALIZE]: {
        [x: string]: any;
        isInitialized: boolean;
        agenda: any;
        datiValues: any;
        locationState: any;
    };
    [SET_AGENDA]: {
        agenda: any;
    };
    [SET_VALUES]: {
        datiValues: any;
    };
    [SET_LOCATION]: {
        locationState: any;
    };
    [SET_ROW_VALUES]: {
        rowValues: any;
    };
};

export type AgendaState = {
    isInitialized: boolean;
    agenda: any;
    datiValues: any;
    rowValues: any;
    locationState: any;
};

const initialState: AgendaState = {
    isInitialized: false,
    agenda: {},
    datiValues: {},
    rowValues: {},
    locationState: {},
};

const JWTReducer = (
    state: AgendaState,
    action: ActionMap<AgendaActionTypes>[keyof ActionMap<AgendaActionTypes>]
) => {
    switch (action.type) {
        case INITIALIZE:
            return {
                isInitialized: true,
                agenda: action.payload.agenda,
                datiValues: action.payload.datiValues,
                rowValues: action.payload.rowValues,
                locationState: action.payload.locationState,
            };
        case SET_AGENDA:
            return {
                ...state,
                isInitialized: true,
                agenda: action.payload.agenda,
            };
        case SET_VALUES:
            return {
                ...state,
                datiValues: action.payload.datiValues,
            };

        case SET_ROW_VALUES:
            return {
                ...state,
                rowValues: action.payload.rowValues,
            };
        default:
            return state;
    }
};

export interface AgendaContextType {
    isInitialized: boolean;
    agenda: any;
    datiValues: any;
    rowValues: any;
    locationState: any;
    fetchAgenda: () => void;
    setDatiValues: (values: any) => void;
    setRowValues: (values: any) => void;
}

export const AgendaContext = React.createContext<AgendaContextType>({
    isInitialized: false,
    agenda: null,
    datiValues: null,
    rowValues: null,
    locationState: null,
    fetchAgenda: () => {},
    setDatiValues: (_values: any) => {},
    setRowValues: (_values: any) => {},
});

export function useAgendaProvider() {
    return useContext(AgendaContext);
}

export function AgendaUpdateProvider({ children }: { children: ReactNode }) {
    const [state, dispatch] = useReducer(JWTReducer, initialState);
    const [error, setError] = useState<any>(null);

    const params = useParams<{ uniqueId: string }>();
    const location = useLocation();

    const agendaRequest = useGetCustom(
        `archiveagenda/agenda?fileUniqueid=${params.uniqueId}`
    );

    useEffect(() => {
        fetchAgenda();
        setLocationValues();
    }, [params.uniqueId]);

    const fetchAgenda = useCallback(async () => {
        return await agendaRequest
            .doFetch(true)
            .then((response: any) => {
                dispatch({
                    type: SET_AGENDA,
                    payload: {
                        agenda: response.data,
                    },
                });
            })
            .catch((error) => {
                console.log({ error });
                setError(error);
            });
    }, [params.uniqueId]);

    const setDatiValues = (values: any) => {
        dispatch({
            type: SET_VALUES,
            payload: {
                datiValues: values,
            },
        });
    };

    const setLocationValues = () => {
        dispatch({
            type: SET_LOCATION,
            payload: {
                locationState: location.state,
            },
        });
    };

    const setRowValues = (values: any) => {
        dispatch({
            type: SET_ROW_VALUES,
            payload: {
                rowValues: values,
            },
        });
    };

    return (
        <AgendaContext.Provider
            value={{ ...state, fetchAgenda, setDatiValues, setRowValues }}
        >
            {error ? Error.toString() : children}
        </AgendaContext.Provider>
    );
}
