import dayjs from "dayjs";

export const parseDate = (value: string) => {
    if (value && value?.includes("/")) {
        const dateParts = value.split("/");
        const parsedDate = dayjs(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);

        return parsedDate.toDate();
    }
    return value;
};

export const parseTime = (value: string, minuti: string) => {
    if (!value) return;
    const parsedTime = dayjs().hour(Number(value)).minute(Number(minuti));
    return parsedTime.toDate();
};

export const mapKeys = (key: string): string => {
    const keys: any = {
        agendaData: "date",
        agendaOra: "ora",
        nomeAvvocato: "agendaAvvocato",
        agendaAttivita: "attivita",
    };

    return keys[key] || key;
};

export const mapValues = (key: string, value: any, helperValue?: any) => {
    const data: any = {
        referente: value,
        agendaData: parseDate(value),
        agendaOra: parseTime(value, helperValue),
        agendaBillable: value === "1",
    };
    return data[key] || value;
};

const calculateNoticeDays = (
    originalStart: Date,
    noticeStart: Date
): number => {
    return (
        Math.floor(
            (originalStart.getTime() - noticeStart.getTime()) / (1000 * 60 * 60 * 24)
        ) - 1
    );
};

const generateNotices = (
    originalEvent: any,
    noticeEvent: any,
    daysRemaining: number
): any[] => {
    const newNotices: Event[] = [];
    for (let i = daysRemaining; i >= 1; i--) {
        const newNotice = { ...noticeEvent };
        const newDate = new Date(originalEvent.start);
        newDate.setDate(newDate.getDate() - i);
        newNotice.start = newDate;
        newNotices.push(newNotice);
    }
    return newNotices;
};

export const manageDeadlinesNotice = (events: any[]): any[] => {
    if (!events || !Array.isArray(events)) {
        return [];
    }

    const deadlines = events.filter((event) => event.type === "deadline");
    const deadlinesGroup: any = deadlines.reduce(
        (group: any, event) => {
            group[event.id] = group[event.id] || [];
            group[event.id].push(event);
            return group;
        },
        {}
    );

    Object.keys(deadlinesGroup).forEach((id) => {
        if (id.includes("closeDeadline-")) return;

        const closeId = `closeDeadline-${id}`;
        const originalEvent = deadlinesGroup[id]?.[0];
        const noticeEvent = deadlinesGroup[closeId]?.[0];

        if (originalEvent && noticeEvent) {
            const originalStart = new Date(originalEvent.start);
            const noticeStart = new Date(noticeEvent.start);
            const daysRemaining = calculateNoticeDays(originalStart, noticeStart);

            if (daysRemaining >= (noticeEvent.giorni_preavviso || 0)) {
                const noticeIdx = deadlines.findIndex(
                    (event) =>
                        event.id === noticeEvent.id && event.status === noticeEvent.status
                );
                if (noticeIdx > -1) deadlines.splice(noticeIdx, 1);
            }

            if (daysRemaining > 0) {
                const newNotices = generateNotices(
                    originalEvent,
                    noticeEvent,
                    daysRemaining
                );
                deadlines.push(...newNotices);
            }
        }
    });

    return events.filter((event) => event.type !== "deadline").concat(deadlines);
};

export const manageHearingsNotice = (events: Event[]): Event[] => {
    // Add null/undefined check and ensure events is an array
    if (!events || !Array.isArray(events)) {
        console.warn('manageHearingsNotice: events is not a valid array', events);
        return [];
    }

    const hearings = events.filter((event) => event.type === "hearing");
    const hearingGroup: any = hearings.reduce(
        (group: any, event: any) => {
            group[event.id] = group[event.id] || [];
            group[event.id].push(event);
            return group;
        },
        {}
    );

    Object.keys(hearingGroup).forEach((id) => {
        if (id.includes("closeHearing-")) return;

        const closeId = `closeHearing-${id}`;
        const originalEvent = hearingGroup[id]?.[0];
        const noticeEvent = hearingGroup[closeId]?.[0];

        if (originalEvent && noticeEvent) {
            const originalStart = new Date(originalEvent.start);
            const noticeStart = new Date(noticeEvent.start);
            const daysRemaining = calculateNoticeDays(originalStart, noticeStart);

            if (daysRemaining >= (noticeEvent.giorni_preavviso || 0)) {
                const noticeIdx = hearings.findIndex(
                    (event: any) =>
                        event.id === noticeEvent.id && event.status === noticeEvent.status
                );
                if (noticeIdx > -1) hearings.splice(noticeIdx, 1);
            }

            if (daysRemaining > 0) {
                const newNotices = generateNotices(
                    originalEvent,
                    noticeEvent,
                    daysRemaining
                );
                hearings.push(...newNotices);
            }
        }
    });

    return events.filter((event) => event.type !== "hearing").concat(hearings);
};
