import AgendaIndex from "../generalCalendar/index";
import ImpegnoIndex from "./addCalendar/impegno/index";
import CreateUpdateUdienza from "./sections/CreateUdienza";
import { Update } from "./sections/UpdateUdienza";
import { CalendarDataProvider } from "./addCalendar/impegno/context/CalendarDataContext";

const ImpegnoWithContext = () => {
    return (
        <CalendarDataProvider>
            <ImpegnoIndex />
        </CalendarDataProvider>
    );
};

const UdienzaWithContext = () => {
    return (
        <CalendarDataProvider type="agendaDeadline">
            <Update />
        </CalendarDataProvider>
    );
};

export const agenda = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/calendar/calendar",
            element: <AgendaIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/impegno/update",
            element: <ImpegnoWithContext />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/impegno/update/:id",
            element: <ImpegnoWithContext />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/agenda/agenda/update/:uniqueId?",
            element: <UdienzaWithContext />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/archiveagenda/agenda/create/:id",
            element: <CreateUpdateUdienza />,
        },
    },
];
