import { useState, useMemo, useEffect } from "react";
import { Button, Stack, Menu, MenuItem, SplitButton } from "@vapor/v3-components";
import { useNavigate } from "react-router-dom";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { Chip, Typography, CircularProgress } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faListCheck, faCalendar, faEllipsisVertical, faChevronDown } from "@fortawesome/pro-regular-svg-icons";
import { faCircleExclamation } from "@fortawesome/pro-solid-svg-icons";
import moment from "moment";
import { ICalendarButtonsProps } from "../typings/generalCalendar.interface";
import { updateCalendar } from "./calendarHelper";
import { gettingCalendarView } from "./gettingCalendarViewName";
import usePostCustom from "../../../../hooks/usePostCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useFilterTemplates } from "../hooks/useFilterTemplates";
import { usePrintCalendarWithTemplate } from "../hooks/usePrintCalendarWithTemplate";
import { TemplateListModal } from "../components/TemplateListModal";
import { useUser } from "../../../../store/UserStore";

export default function CalendarCustomButtons(props: ICalendarButtonsProps) {
    const { query, setQuery, calendarRef, fetchEventData, setMonthTitle, t, calendarData, eventData, eventResponse, openImpegnoModal, setRightPanelType, setRightPanelOpen } = props;
    const { user }: any = useUser();
    const [openDatePicker, setOpenDatePicker] = useState(false);
    const [openStampaTemplateModal, setOpenStampaTemplateModal] = useState(false);
    const [shouldFetchTemplates, setShouldFetchTemplates] = useState(false);
    const [selectedTemplateId, setSelectedTemplateId] = useState<string>("");
    const [shouldPrintWithTemplate, setShouldPrintWithTemplate] = useState(false);

    const templatesResponse = useFilterTemplates({
        fetch: shouldFetchTemplates,
        category: 1
    });

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const [anchorElMore, setAnchorElMore] = useState<null | HTMLElement>(null);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [isSyncLoading, setIsSyncLoading] = useState(false);
    const openMore = Boolean(anchorElMore);

    const sincronizeRequest = usePostCustom("calendar/manual-sync-calendars?noTemplateVars=true");

    const printRequest = useGetCustom("calendar/print", {}, null, true);

    const createTemplateQuery = (templateId: string) => {
        return {
            sectionid: "",
            category: 1,
            docid: templateId,
            calendarCommitments: query.calendarCommitments || "-1",
            deadlineType: query.calendarDeadlineType || "-1",
            deadlineCategory: query.calendarDeadlineCategory || "-1",
            calendarPerson: Array.isArray(query.calendarPersons) && query.calendarPersons.length > 0 ? query.calendarPersons[0] : "1",
            calendarGroup: query.calendarGroup || "-1",
            calendarReferent: query.calendarReferent || "-1",
            praticaSelectUniqueid: query.praticaSelectUniqueid || "",
            authority: query.authority || "",
            authoritySearch: query.authoritySearchId || "",
            calendarEvasa: query.calendarEvasa || "-1",
            calendarNonevadere: query.calendarNonevadere || "-1",
            viewName: query.viewName || "basicWeek",
            visStart: query.visStart || query.start || 0,
            visEnd: query.visEnd || query.end || 0
        };
    };

    const defaultTemplateQuery = useMemo(
        () => ({
            sectionid: "",
            category: 1,
            calendarCommitments: "-1",
            deadlineType: "-1",
            deadlineCategory: "-1",
            calendarPerson: "1",
            calendarGroup: "-1",
            calendarReferent: "-1",
            praticaSelectUniqueid: "",
            authority: "",
            authoritySearch: "",
            calendarEvasa: "-1",
            calendarNonevadere: "-1",
            viewName: "basicWeek",
            visStart: 0,
            visEnd: 0
        }),
        []
    );

    const templateQuery = useMemo(() => {
        return selectedTemplateId ? createTemplateQuery(selectedTemplateId) : defaultTemplateQuery;
    }, [
        selectedTemplateId,
        query.calendarCommitments,
        query.deadlineType,
        query.deadlineCategory,
        query.calendarPersons,
        query.calendarGroup,
        query.calendarReferent,
        query.praticaSelectUniqueid,
        query.authority,
        query.authoritySearchId,
        query.calendarEvasa,
        query.calendarNonevadere,
        query.viewName,
        query.start,
        query.end,
        defaultTemplateQuery
    ]);

    const printWithTemplateResponse = usePrintCalendarWithTemplate({
        query: templateQuery,
        templateId: selectedTemplateId,
        print: shouldPrintWithTemplate
    });

    useEffect(() => {
        if (printWithTemplateResponse.hasLoaded && !printWithTemplateResponse.isDownloading) {
            setShouldPrintWithTemplate(false);
            setSelectedTemplateId("");
        }
    }, [printWithTemplateResponse.hasLoaded, printWithTemplateResponse.isDownloading]);

    const currentCalendarDate = useMemo(() => {
        if (query.date && query.date !== 0) {
            if (query.date > 1000000000000) {
                return moment(query.date).toDate();
            } else {
                return moment.unix(query.date).toDate();
            }
        }

        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            const date = calendarAPI.getDate();
            return date ? new Date(date) : new Date();
        }

        return new Date();
    }, [query.date, calendarRef]);

    const goToDate = (date: any) => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            const isWeekend = moment(date).isoWeekday() >= 6;
            calendarAPI.setOption("weekends", isWeekend);

            const currentView = gettingCalendarView(query.viewName || "month");
            calendarAPI.changeView(currentView, new Date(date));

            const selectedDate = moment(date);
            const startOfDay = selectedDate.clone().startOf("day");
            const endOfDay = selectedDate.clone().endOf("day");

            const updatedQuery = {
                ...query,
                start: startOfDay.unix(),
                end: endOfDay.unix(),
                date: selectedDate.unix(),
                calendarWeekends: isWeekend
            };

            updateCalendar(calendarAPI, updatedQuery, setQuery, setMonthTitle, t, query.viewName || "month", true);
        }
    };

    // useEffect(() => {
    //     const calendarApi = calendarRef.current?.getApi();
    //     if (calendarApi) {
    //         calendarApi.gotoDate(currentDate);
    //     }

    //     const calendarAPI = calendarRef?.current?.getApi();
    //     if (calendarAPI) {
    //         const date = calendarAPI.getDate();
    //         return date ? new Date(date) : new Date();
    //     }

    //     return new Date();
    // }, [query.date, calendarRef]);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleClickMore = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElMore(event.currentTarget);
    };

    const handleCloseMore = () => {
        setAnchorElMore(null);
    };

    const stampaButton = async (closeDeadline: boolean) => {
        if (eventData.length > 0) {
            setAnchorElMore(null);
            const queryToPrint: any = {
                calendarCommitments: query.calendarCommitments,
                deadlineType: query.deadlineType,
                deadlineCategory: query.deadlineCategory,
                calendarPerson: query.calendarPersons,
                calendarGroup: query.calendarGroup,
                calendarReferent: query.calendarReferent,
                praticaSelectUniqueid: query.praticaSelectUniqueid,
                authority: query.authority,
                authoritySearch: query.authoritySearchId,
                calendarEvasa: query.calendarEvasa,
                calendarNonevadere: query.calendarNonevadere,
                viewName: query.viewName,
                visStart: query.start * 1000,
                visEnd: query.end * 1000,
                closeDeadline: closeDeadline
            };

            if (query.viewName === "basicWeek") {
                queryToPrint.calendarWeekends = "on";
            }

            const printResponse: any = await printRequest.doFetch(true, queryToPrint);
            const blob = new Blob([printResponse.data], { type: "application/pdf" });
            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = "Calendario_generale.pdf";
            document.body.appendChild(link);
            link.click();

            if (link.parentNode) link.parentNode.removeChild(link);
        } else {
            setShowErrorMessage(true);
            setAnchorElMore(null);
        }
    };

    const navigate = useNavigate();

    const handleChipClick = () => {
        const convertStartDate = (dateString: string) => dateString.split("/").reverse().join("/");
        const convertEndDate = (dateString: string) => dateString.split("-").reverse().join("/");

        const startDate = convertStartDate(calendarData.startButtonDate);
        const endDate = convertEndDate(calendarData.endButtonDate);
        const currentUserId = user?.getId() || "";
        const navigateQuery = `/deadlines/deadlines?oldDeadlines=true&startDate=${startDate}&endDate=${endDate}&intestatario=${currentUserId}`;

        navigate(navigateQuery);
    };

    const sincronizeButton = async () => {
        if (calendarData?.showSyncButton) {
            setIsSyncLoading(true);
            try {
                const response: any = await sincronizeRequest.doFetch(true);
                if (response.data) {
                    setShowSuccessMessage(true);
                    setAnchorElMore(null);
                    fetchEventData && fetchEventData(query);
                }
            } catch (error) {
                // Handle error if needed
                console.error("Sync error:", error);
            } finally {
                setIsSyncLoading(false);
            }
        } else {
            navigate("/legacy/calendaraccounts/index");
        }
    };

    const handleStampaTemplate = () => {
        if (eventData.length > 0) {
            setOpenStampaTemplateModal(true);
            setShouldFetchTemplates(true);
            setAnchorElMore(null);
        } else {
            setShowErrorMessage(true);
            setAnchorElMore(null);
        }
    };

    const handleTemplateConfirm = (templateId: string) => {
        setSelectedTemplateId(templateId);
        setShouldPrintWithTemplate(true);
        setOpenStampaTemplateModal(false);
        setShouldFetchTemplates(false);
    };

    const handleTemplateModalClose = () => {
        setOpenStampaTemplateModal(false);
        setShouldFetchTemplates(false);
    };

    return (
        <Stack direction="row" alignItems="center" spacing={1}>
            <ToastNotification showNotification={showSuccessMessage} setShowNotification={setShowSuccessMessage} severity="success" text={t("Sincronizzazione calendario completata")} />
            <ToastNotification showNotification={showErrorMessage} setShowNotification={setShowErrorMessage} severity="error" text={t("Non è presente nessun evento da stampare")} />

            {/* Chip - first element on the right */}
            {(eventResponse?.missedEvents || calendarData?.missedEvents) && parseInt(eventResponse?.missedEvents || calendarData?.missedEvents || "0") > 0 && (
                <Chip
                    icon={<FontAwesomeIcon icon={faCircleExclamation} />}
                    label={eventResponse?.missedEvents || calendarData?.missedEvents}
                    size="medium"
                    onClick={handleChipClick}
                    clickable
                    sx={{
                        backgroundColor: "#d32f2f !important",
                        color: "white !important",
                        border: "1px solid #d32f2f !important",
                        px: 1,
                        minWidth: "60px !important",
                        whiteSpace: "nowrap",
                        "&:hover": {
                            backgroundColor: "#b71c1c !important",
                            border: "1px solid #b71c1c !important",
                            color: "white !important"
                        },
                        "&.MuiChip-root:hover": {
                            backgroundColor: "#b71c1c !important",
                            border: "1px solid #b71c1c !important",
                            color: "white !important"
                        },
                        "&.MuiChip-clickable:hover": {
                            backgroundColor: "#b71c1c !important",
                            border: "1px solid #b71c1c !important",
                            color: "white !important"
                        },
                        "&.MuiChip-filled:hover": {
                            backgroundColor: "#b71c1c !important",
                            border: "1px solid #b71c1c !important",
                            color: "white !important"
                        },
                        "&:focus": {
                            backgroundColor: "#d32f2f !important",
                            border: "1px solid #d32f2f !important"
                        },
                        "&:active": {
                            backgroundColor: "#b71c1c !important",
                            border: "1px solid #b71c1c !important",
                            color: "white !important"
                        },
                        "& .MuiChip-icon": {
                            color: "white !important"
                        },
                        "& .MuiChip-label": {
                            fontSize: "0.875rem",
                            fontWeight: 500,
                            color: "white !important"
                        }
                    }}
                />
            )}

            {/* Create button group */}
           <Stack direction="row" spacing={0}>  
            <SplitButton variant="contained" aria-label="split button group">
                <Button onClick={function Hi(){}}>
                         {t("Crea")}
                </Button>
                <Button  onClick={handleClick}>
                    <FontAwesomeIcon icon={faChevronDown} />
                
                </Button>
                   <Menu
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: 'bottom',
                        horizontal: 'right',
                    }}
                    transformOrigin={{
                        vertical: 'top',
                        horizontal: 'right',
                    }}
                >
                    <MenuItem
                        onClick={openImpegnoModal}
                    >{t("Nuovo impegno")}</MenuItem>
                    <MenuItem
                        onClick={handleClose}

                    >Opzione 2</MenuItem>
                    <MenuItem
                        onClick={handleClose}

                    >Opzione 3</MenuItem>
                </Menu>
                </SplitButton>
                </Stack>

            {/* Action buttons - larger size */}
            <Button onClick={() => setOpenDatePicker(true)} disabled={false}>
                <FontAwesomeIcon icon={faCalendar} size="lg" />
            </Button>

            <Button
                onClick={() => {
                    if (setRightPanelType) {
                        setRightPanelType("activities");
                        setRightPanelOpen && setRightPanelOpen(true);
                    }
                }}
            >
                <FontAwesomeIcon icon={faListCheck} size="lg" />
            </Button>

            <Button onClick={handleClickMore}>
                <FontAwesomeIcon icon={faEllipsisVertical} size="lg" />
            </Button>

            <Menu
                anchorEl={anchorElMore}
                open={openMore}
                onClose={handleCloseMore}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right"
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right"
                }}
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: "8px",
                            minWidth: "200px"
                        }
                    }
                }}
            >
                <MenuItem onClick={() => stampaButton(true)}>
                    <Typography variant="body" component="div" sx={{color: '#0076ad'}} gutterBottom>
                        {t("Stampa")}
                    </Typography>
                </MenuItem>
                <MenuItem onClick={() => stampaButton(false)}>
                    <Typography variant="body" component="div" sx={{color: '#0076ad'}} gutterBottom>
                        {t("Stampa sensa avvisi")}
                    </Typography>
                </MenuItem>
                <MenuItem
                    onClick={handleStampaTemplate}
                    sx={{
                        "&:hover": {
                            backgroundColor: "#e3f2fd"
                        }
                    }}
                >
                    <Typography variant="body" component="div" sx={{color: '#0076ad'}} gutterBottom>
                        {t("Stampa template")}
                    </Typography>
                </MenuItem>
                <MenuItem
                    onClick={sincronizeButton}
                    disabled={isSyncLoading}
                    sx={{
                        "&:hover": {
                            backgroundColor: "#e3f2fd"
                        }
                    }}
                >
                    <Stack direction="row" alignItems="center" spacing={1}>
                        {isSyncLoading && <CircularProgress size={16} sx={{ color: "primary.textTitleColor" }} />}
                        <Typography variant="body" component="div" sx={{color: '#0076ad'}} gutterBottom>
                            {t("Sincronizza calendario")}
                        </Typography>
                    </Stack>
                </MenuItem>
            </Menu>

            {openDatePicker && (
                <DatePicker
                    open={openDatePicker}
                    onClose={() => setOpenDatePicker(false)}
                    value={currentCalendarDate}
                    onChange={(date: any) => goToDate(date)}
                    format="dd/MM/yyyy"
                    slotProps={{
                        textField: {
                            error: false,
                            sx: { visibility: "hidden", width: 0 }
                        },
                        day: {
                            sx: {
                                "&.MuiPickersDay-today": {
                                    backgroundColor: "#005075 !important",
                                    color: "#ffffff !important",
                                    fontWeight: "bold !important",
                                    borderRadius: "50% !important",
                                    border: "none !important"
                                },
                                "&.MuiPickersDay-today:hover": {
                                    backgroundColor: "#005075",
                                    color: "#ffffff"
                                },
                                "&.Mui-selected": {
                                    backgroundColor: "#005075 !important",
                                    color: "#ffffff !important",
                                    fontWeight: "bold !important",
                                    borderRadius: "50% !important"
                                },
                                "&.Mui-selected:hover": {
                                    backgroundColor: "#005075",
                                    color: "#ffffff"
                                }
                            }
                        }
                    }}
                />
            )}

            <TemplateListModal
                open={openStampaTemplateModal}
                onClose={handleTemplateModalClose}
                templates={templatesResponse.data || []}
                loading={templatesResponse.loading}
                onConfirm={handleTemplateConfirm}
            />
        </Stack>
    );
}
