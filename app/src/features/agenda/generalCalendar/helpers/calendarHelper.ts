import { EventInput } from "@fullcalendar/core";
import moment from "moment";
import bulletRed from "./../../../../assets/images/bulletRed.png";
import bulletGreen from "./../../../../assets/images/bulletGreen.png";
import bulletOrange from "./../../../../assets/images/bulletOrange.png";
import bulletGray from "./../../../../assets/images/bulletGray.png";
import bulletWhite from "./../../../../assets/images/bulletWhite.png";
import bulletGoogle from "./../../../../assets/images/bulletGoogle.png";
import bulletOutlook from "./../../../../assets/images/bulletOutlook.png";
import bulletPoliswebGreen from "./../../../../assets/images/bulletPoliswebGreen.png";
import iconPoliswebWhite from "./../../../../assets/images/icon-polisweb-white.png";
import { IQuery } from "../typings/generalCalendar.interface";

export const updateCalendar = (
    calendarAPI: any,
    query: IQuery,
    setQuery: React.Dispatch<React.SetStateAction<IQuery>>,
    setMonthTitle: React.Dispatch<React.SetStateAction<string | undefined>>,
    t: any,
    calendarViewName: string,
    preserveSelectedDate: boolean = false
) => {
    try {
        if (!calendarAPI || !calendarAPI.view || !calendarAPI.currentDataManager) {
            console.warn('Calendar API is not properly initialized');
            return;
        }

        const currentMonthTitle = calendarAPI.view.title;
        const formattedTitle =
            currentMonthTitle?.charAt(0).toUpperCase() +
            currentMonthTitle?.slice(1);

        setMonthTitle(t(formattedTitle));

        const currentDateRaw = calendarAPI.getDate();
        const activeRange = calendarAPI.currentDataManager.state?.dateProfile?.activeRange;

        if (!currentDateRaw || !activeRange || !activeRange.start || !activeRange.end) {
            console.warn('Calendar date range is not properly initialized');
            return;
        }

        const currentDate = moment(currentDateRaw);
        const rangeStart = moment(activeRange.start);
        const rangeEnd = moment(activeRange.end);

        if (!currentDate.isValid() || !rangeStart.isValid() || !rangeEnd.isValid()) {
            console.warn('Invalid dates detected in calendar update');
            return;
        }

        const newStart = rangeStart.unix();
        const newEnd = rangeEnd.unix();

        // Preserve the user's selected date only when explicitly requested (e.g., view changes)
        // For navigation actions (prev/next/today), allow the date to be updated
        let newDate = currentDate.unix();
        if (preserveSelectedDate && query.date && query.date !== 0) {
            const existingDate = query.date > 1000000000000 ?
                moment(query.date) : moment.unix(query.date);
            if (existingDate.isValid()) {
                newDate = query.date > 1000000000000 ?
                    Math.floor(query.date / 1000) : query.date;
            }
        }

        const updatedQuery = {
            ...query,
            start: newStart,
            end: newEnd,
            date: newDate,
            viewName: calendarViewName,
        };

        setQuery(updatedQuery); // Ensure query state is updated for filters
        localStorage.setItem("agendaQuery", JSON.stringify(updatedQuery));
    } catch (error) {
        console.error('Error updating calendar:', error);
        setQuery(query);
    }
};

const externalCalendar = [bulletOutlook, bulletGoogle];
const renderCalendarText = (
  eventInfo: EventInput,
  calendarReferent: string | undefined,
  loggedUserCampiAgenda: any | null,
  impegniMultiutente: any | null,
  netlexSettingsFileId: any | null
) => {
  const data = {
    iconNotice: "",
    icons: "",
    tooltip: "",
    annotations: "",
    event: eventInfo.event._def.extendedProps,
  };
  let dataToRender;
  if (eventInfo.event._def.extendedProps.type === "deadline") {
    dataToRender = renderDeadlineText(
      data,
      loggedUserCampiAgenda,
      impegniMultiutente
    );
  } else if (eventInfo.event._def.extendedProps.type === "hearing") {
    dataToRender = renderHearingText(data, loggedUserCampiAgenda);
  } else if (eventInfo.event._def.extendedProps.type === "polisweb") {
    dataToRender = renderPoliswebEventText(
      data,
      loggedUserCampiAgenda,
      impegniMultiutente
    );
  }
  dataToRender.title = eventInfo.event._def.title;
  dataToRender = tooltipEventRender(
    dataToRender,
    calendarReferent,
    loggedUserCampiAgenda,
    netlexSettingsFileId
  );

  return dataToRender;
};

const renderDeadlineText = (
  data: any,
  loggedUserCampiAgenda: any | null,
  impegniMultiutente: any | null
) => {
  let event = data.event;
  if (data.event.editable === false) {
    //data.event.editable = false;  //TODO: !ma anche no!
    data.iconNotice = '<i class="icon-tags"></i> ';
    data.tooltip = "Avviso impegno del: " + event.data;
  }
  if (data.event.polisweb === "1") {
    data.iconNotice += `<img src=${iconPoliswebWhite} class="poliswebIcon" />`;
  } else if (event.evasa === "1") {
    data.iconNotice += '<i class="icon-ok"></i> ';
  }
  if (+data.event.important === 1) {
    data.icons += ' <i class="fa fa-exclamation-circle"></i>';
  }

  data.annotations = prepareDeadlineAnnotations(
    data.event,
    loggedUserCampiAgenda,
    impegniMultiutente
  );
  switch (+data.event.status) {
    case 0:
      data.bulletIcon = bulletRed; //test
      break;
    case 1:
      data.bulletIcon = bulletGreen;
      break;
    case 2:
      data.bulletIcon = bulletGray;
      break;
    case 3:
      data.bulletIcon = bulletOrange;
      break;
    case 4:
      if (1 === +data.event.polisweb && 1 === +data.event.evasa) {
        data.bulletIcon = bulletPoliswebGreen;
      } else {
        data.bulletIcon = bulletWhite;
      }
      break;
  }
  if (
    typeof data.event.calendarioEsternoType !== "undefined" &&
    data.event.calendarioEsternoType !== "" &&
    data.event.calendarioEsternoType !== null
  ) {
    data.bulletIcon =
      '<img width="13px !important" height="13px !important" class="calendarLegendIcon" src="' +
      externalCalendar[data.event.calendarioEsternoType] +
      '">&nbsp;<img src="' +
      data.bulletIcon +
      '">';
  } else {
    data.bulletIcon = '<img src="' + data.bulletIcon + '">';
  }

  return data;
};

const renderHearingText = (data: any, loggedUserCampiAgenda: any | null) => {
  if (data.event.editable === false) {
    data.tooltip = "Avviso udienza del: " + data.event.data;
  }

  data.annotations = prepareHearingAnnotations(
    data.event,
    loggedUserCampiAgenda
  );
  if (1 === +data.event.avviso_udienza) {
    data.bulletIcon = bulletOrange;
  } else if (0 === +data.event.segnalaritardo) {
    data.bulletIcon = bulletGreen;
  } else {
    if (1 === +data.event.stato_evasa) {
      data.bulletIcon = bulletGreen;
    } else if (2 === +data.event.stato_evasa) {
      data.bulletIcon = bulletGray;
    } else {
      data.bulletIcon = bulletRed;
    }
  }
  if (
    typeof data.event.calendarioEsternoType !== "undefined" &&
    data.event.calendarioEsternoType !== "" &&
    data.event.calendarioEsternoType !== null
  ) {
    data.bulletIcon =
      '<img width="13px !important" height="13px !important" class="calendarLegendIcon" src="' +
      externalCalendar[data.event.calendarioEsternoType] +
      '">&nbsp;<img src="' +
      data.bulletIcon +
      '">';
  } else {
    data.bulletIcon = '<img src="' + data.bulletIcon + '">';
  }

  return data;
};

const renderPoliswebEventText = (
  data: any,
  loggedUserCampiAgenda: any | null,
  impegniMultiutente: any | null
) => {
  data.iconNotice += `<img src=${iconPoliswebWhite} class="poliswebIcon" />`;

  data.bulletIcon = bulletPoliswebGreen;
  data.bulletIcon = '<img src="' + data.bulletIcon + '">';
  data.annotations = preparePoliswebAnnotations(
    data.event,
    loggedUserCampiAgenda,
    impegniMultiutente
  );

  return data;
};

const tooltipEventRender = (
  data: any,
  calendarReferent: string | undefined,
  loggedUserCampiAgenda: any | null,
  netlexSettingsFileId: any | null
) => {
  var hadMultiintestatario = calendarReferent;
  var status = "";
  /** NETLEX SETTINGS BEGIN **/
  var netlex_settings_none = "none";
  var netlex_settings_file_code = "fileCode";
  var netlex_settings_file_archive_code = "fileArchiveCode";
  /** NETLEX SETTINGS END **/

  if (data.event.pratica) {
    if (
      data.event.ruologeneralenumero &&
      data.event.ruologeneraleanno &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.rg) ||
        (data.event.type === "hearing" && loggedUserCampiAgenda?.udienza?.rg))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>R.G.:</b> " +
        data.event.ruologeneralenumero +
        "/" +
        data.event.ruologeneraleanno +
        (data.event.subprocedimento != null && data.event.subprocedimento !== 0
          ? "-" + data.event.subprocedimento
          : "");
    } else if (
      data.event.rgnr &&
      data.event.rgnranno &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.rgnr) ||
        (data.event.type === "hearing" && loggedUserCampiAgenda?.udienza?.rgnr))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>R.G.N.R.:</b> " +
        data.event.rgnr +
        "/" +
        data.event.rgnranno;
    }

    if (netlexSettingsFileId !== netlex_settings_none) {
      var codice = "";
      var codicearchivio = "";
      if (netlexSettingsFileId === netlex_settings_file_code) {
        codice = data.event.pratica;
        codicearchivio = data.event.codicearchivio;
      } else if (netlexSettingsFileId === netlex_settings_file_archive_code) {
        codice = data.event.codicearchivio;
      }
      if (
        codice !== "" &&
        codice !== "0" &&
        (!loggedUserCampiAgenda ||
          (data.event.type === "deadline" &&
            loggedUserCampiAgenda?.pratica?.codice) ||
          (data.event.type === "hearing" &&
            loggedUserCampiAgenda?.udienza?.codice) ||
          data.event.type === "polisweb")
      ) {
        if (codice !== "") {
          data.tooltip =
            addNewLine(data.tooltip) + "<b>Codice Pratica:</b> " + codice;
        }
        if (codicearchivio !== "") {
          data.tooltip =
            addNewLine(data.tooltip) +
            "<b>Codice Archivio:</b> " +
            codicearchivio;
        }
      }
    }
    if (
      !loggedUserCampiAgenda ||
      (data.event.type === "deadline" &&
        loggedUserCampiAgenda?.pratica?.stato_pratica) ||
      (data.event.type === "hearing" &&
        loggedUserCampiAgenda?.udienza?.stato_pratica)
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>Stato pratica:</b> " +
        data.event.nomeStatoPratica;
    }
    if (
      data.event.listaclienti &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.clienti) ||
        (data.event.type === "hearing" &&
          loggedUserCampiAgenda?.udienza?.clienti))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>Clienti:</b> " +
        (data.event.listaclienti.length > 55
          ? data.event.listaclienti.substring(0, 55) + "&hellip;"
          : data.event.listaclienti);
    }
    if (
      data.event.listacontroparti != null &&
      data.event.listacontroparti !== "" &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.controparti) ||
        (data.event.type === "hearing" &&
          loggedUserCampiAgenda?.udienza?.controparti))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>Controparti:</b> " +
        (data.event.listacontroparti.length > 55
          ? data.event.listacontroparti.substring(0, 55) + "&hellip;"
          : data.event.listacontroparti);
    }
    if (
      data.event.avvocato &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.avvocato) ||
        (data.event.type === "hearing" &&
          loggedUserCampiAgenda?.udienza?.avvocato))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) + "<b>Avvocato:</b> " + data.event.avvocato;
    }
    if (
      data.event.descrizionepratica &&
      data.event.avvocato &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.descrizione) ||
        (data.event.type === "hearing" &&
          loggedUserCampiAgenda?.udienza?.descrizione))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) +
        "<b>Descrizione:</b> " +
        data.event.descrizionepratica;
    }

    if (
      hadMultiintestatario &&
      data.event.referente &&
      data.event.referente !== data.event.avvocato &&
      (!loggedUserCampiAgenda ||
        (data.event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.referente) ||
        (data.event.type === "hearings" &&
          loggedUserCampiAgenda?.udienza?.referente))
    ) {
      data.tooltip =
        addNewLine(data.tooltip) + "<b>Referente:</b> " + data.event.referente;
    }
  } else {
    if (data.tooltip === "") {
      data.tooltip = "Impegno non collegato a pratica";
    }
  }
  if (
    data.event.annotazioni &&
    (!loggedUserCampiAgenda ||
      (data.event.type === "deadline" &&
        data.event.pratica &&
        loggedUserCampiAgenda?.pratica?.annotazioni) ||
      (data.event.type === "deadline" &&
        data.event.pratica &&
        loggedUserCampiAgenda?.semplice?.annotazioni) ||
      (data.event.type === "hearing" &&
        loggedUserCampiAgenda?.udienza?.annotazioni))
  ) {
    var notes =
      data.event.annotazioni.length <= 445
        ? data.event.annotazioni
        : data.event.annotazioni.substring(0, 445) + "...";
    data.annotations =
      addNewLine(data.annotations) + "<b>Annotazioni:</b> " + notes;
  }
  if (data.annotations != null && data.annotations !== "") {
    data.tooltip = addNewLine(data.tooltip) + data.annotations;
  }
  if (
    !loggedUserCampiAgenda ||
    (data.event.type === "deadline" &&
      data.event.pratica &&
      loggedUserCampiAgenda?.pratica?.oggetto) ||
    (data.event.type === "deadline" &&
      data.event.pratica &&
      loggedUserCampiAgenda?.semplice?.oggetto) ||
    (data.event.type === "hearing" && loggedUserCampiAgenda?.udienza?.oggetto)
  ) {
    data.tooltip = addNewLine(data.tooltip) + "<b>Oggetto:</b> " + data.title;
  }

  if (data.event.type === "deadline") {
    switch (+data.event.status) {
      case 0:
        status = "Non evaso";
        break;
      case 1:
        status = "Evaso";
        break;
      case 2:
        status = "Non evadere";
        break;
      case 3:
        status = "Vicino";
        break;
      case 4:
        status = "Polisweb";
        break;
    }
  } else if ("hearing" === data.event.type) {
    if (1 === data.event.avviso_udienza) {
      status = "Vicina";
    }
    if (0 === data.event.segnalaritardo) {
      status = data.event.nomeStatoPratica;
    } else {
      switch (+data.event.stato_evasa) {
        case 0:
          status = "Non evasa";
          break;
        case 1:
          status = "Evasa";
          break;
        case 2:
          status = "Da non evadere";
          break;
      }
    }
  }
  if (data.event.type !== "polisweb") {
    data.tooltip =
      addNewLine(data.tooltip) +
      "<b>Stato:</b> " +
      data.bulletIcon +
      " " +
      status;
  } else {
    data.tooltip = addNewLine(data.tooltip);
  }

  if (
    data.event.pratica == null &&
    data.event.durata &&
    data.event.type === "deadline" &&
    loggedUserCampiAgenda?.semplice?.durata
  ) {
    data.tooltip +=
      "<br><strong>Durata:</strong> " + data.event.durata + " minuti"; //
  }
  if (
    data.event.pratica &&
    data.event.durata &&
    data.event.type === "deadline" &&
    loggedUserCampiAgenda?.pratica?.durata
  ) {
    data.tooltip +=
      "<br><strong>Durata:</strong> " + data.event.durata + " minuti"; //FIX fields durata tooltip agenda
  }
  if (
    data.event.durata &&
    data.event.type === "hearing" &&
    loggedUserCampiAgenda?.udienza?.durata
  ) {
    data.tooltip +=
      "<br><strong>Durata:</strong> " + data.event.durata + " minuti"; //FIX fields durata tooltip agenda
  }
  return data;
};

const addNewLine = (str: string) => {
  var suffix = "<br>";
  if (str !== "" && !(str.indexOf(suffix, str.length - suffix.length) !== -1)) {
    str += suffix;
  }
  return str;
};

// const normalizeNumber = (value: number | string) => {
//   if (value < 10) {
//     value = "0" + value;
//   }
//   return value;
// };

const prepareHearingAnnotations = (
  event: any,
  loggedUserCampiAgenda: any | null
) => {
  var annotations = "";

  if (
    event.dataUltimaUdienza &&
    (!loggedUserCampiAgenda ||
      (event.type === "deadline" &&
        loggedUserCampiAgenda?.pratica?.ultimaUdienza))
  ) {
    annotations += "<b>Data ultima udienza:</b> " + event.dataUltimaUdienza;
  }

  if (
    event.dataUdienza &&
    (!loggedUserCampiAgenda ||
      (event.type === "deadline" &&
        loggedUserCampiAgenda?.pratica?.dataUdienza))
  ) {
    annotations =
      addNewLine(annotations) + "<b>Data udienza:</b> " + event.dataUdienza;
  }

  if (
    event.sezione &&
    (!loggedUserCampiAgenda ||
      (event.type === "deadline" && loggedUserCampiAgenda?.pratica?.sezione) ||
      (event.type === "hearing" && loggedUserCampiAgenda?.udienza?.sezione))
  ) {
    annotations = addNewLine(annotations) + "<b>Sezione:</b> " + event.sezione;
  }

  if (
    event.autorita &&
    (!loggedUserCampiAgenda ||
      (event.type === "deadline" && loggedUserCampiAgenda?.pratica?.autorita) ||
      (event.type === "hearing" && loggedUserCampiAgenda?.udienza?.autorita))
  ) {
    annotations =
      addNewLine(annotations) + "<b>Autorità:</b> " + event.autorita;
  }

  if (
    event.citta &&
    (!loggedUserCampiAgenda ||
      (event.type === "deadline" && loggedUserCampiAgenda?.pratica?.citta) ||
      (event.type === "hearing" && loggedUserCampiAgenda?.udienza?.citta))
  ) {
    annotations = addNewLine(annotations) + "<b>Città:</b> " + event.citta;
  }

  if (event.istruttore && event.istruttore !== "") {
    var index = event.istruttore.indexOf("(PM)");
    if (index !== -1) {
      if (
        !loggedUserCampiAgenda ||
        (event.type === "deadline" && loggedUserCampiAgenda?.pratica?.pm) ||
        (event.type === "hearing" && loggedUserCampiAgenda?.udienza?.pm)
      ) {
        annotations =
          addNewLine(annotations) +
          "<b>PM:</b> " +
          event.istruttore.substring(0, index);
      }
    } else {
      if (
        !loggedUserCampiAgenda ||
        (event.type === "deadline" &&
          loggedUserCampiAgenda?.pratica?.istruttore) ||
        (event.type === "hearing" && loggedUserCampiAgenda?.udienza?.istruttore)
      ) {
        annotations =
          addNewLine(annotations) + "<b>Giudice:</b> " + event.istruttore;
      }
    }
  }
  return annotations;
};

const prepareDeadlineAnnotations = (
  event: any,
  loggedUserCampiAgenda: any | null,
  impegniMultiutente: any | null
) => {
  var annotations = prepareHearingAnnotations(event, loggedUserCampiAgenda);

  if (
    (event.nomeutente && !loggedUserCampiAgenda) ||
    (event.type === "deadline" &&
      event.pratica &&
      loggedUserCampiAgenda?.pratica?.intestatari) ||
    (event.type === "deadline" &&
      event.pratica &&
      loggedUserCampiAgenda?.semplice?.intestatari)
  ) {
    annotations = addNewLine(annotations);
    if (impegniMultiutente === 0) {
      annotations += "<b>Intestatario:</b> ";
    } else if (impegniMultiutente === 1) {
      annotations += "<b>Intestatari:</b> ";
    }
    annotations += event.nomeutente;
  }

  return annotations;
};

const preparePoliswebAnnotations = (
  event: any,
  loggedUserCampiAgenda: any | null,
  impegniMultiutente: any | null
) => {
  var annotations = prepareDeadlineAnnotations(
    event,
    loggedUserCampiAgenda,
    impegniMultiutente
  );
  if (
    event.registrationDate != null &&
    event.registrationDate !== "" &&
    event.type === "polisweb"
  ) {
    annotations = `${addNewLine(annotations)}<b>Data registrazione:</b> ${
      event.registrationDate
    }`;
  }
  if (
    event.nomeStatoPratica != null &&
    event.nomeStatoPratica !== "" &&
    event.type === "polisweb"
  ) {
    annotations = `${addNewLine(annotations)} <b>Stato Pratica:</b> ${
      event.nomeStatoPratica
    }`;
  }
  if (
    event.poliswebTypeComunication != null &&
    event.poliswebTypeComunication !== "" &&
    event.type === "polisweb"
  ) {
    annotations = `${addNewLine(annotations)} <b>Tipo:</b> ${
      +event.poliswebTypeComunication === 1
        ? "comunicazione di cancelleria"
        : "evento Polisweb"
    } ${event.bulletIcon}`;
  }
  return annotations;
};

export { renderCalendarText };
