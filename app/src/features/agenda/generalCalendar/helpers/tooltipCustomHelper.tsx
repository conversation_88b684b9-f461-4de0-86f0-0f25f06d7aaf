import { styled } from "@mui/material/styles";
import { TooltipProps, tooltipClasses } from "@mui/material/Tooltip";
import { Tooltip } from "@vapor/react-material";

export const EventTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({}: any) => ({
    [`& .${tooltipClasses?.tooltip}`]: {
        backgroundColor: "#f5f5f9",
        color: "rgba(0, 0, 0, 0.87)",
        border: "1px solid #dadde9",
        whiteSpace: "normal",
    },
}));

export const LegendTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
))(({}: any) => ({
    [`& .${tooltipClasses?.tooltip}`]: {
        backgroundColor: "#f5f5f9",
        color: "rgba(0, 0, 0, 0.87)",
        border: "1px solid #dadde9",
        whiteSpace: "normal",
    },
}));
