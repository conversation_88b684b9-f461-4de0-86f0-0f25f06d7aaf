export const formatTitle = (title: any) => {
    const titleParts = title.split(" ");

    if (/^\d{1,2} [a-z]+ \d{4}$/.test(title)) {
        return title.replace(
            /(\d+ )([a-z]+)( \d{4})/,
            (_match: any, day: any, month: string, year: any) => {
                return (
                    day + month.charAt(0).toUpperCase() + month.slice(1) + year
                );
            }
        );
    }

    if (title.includes("–")) {
        return title.replace(
            /(\d+ – \d+ )([a-z]+)/,
            (_match: any, prefix: any, month: string) => {
                return prefix + month.charAt(0).toUpperCase() + month.slice(1);
            }
        );
    }

    // Case 2: "Dicembre 2024" (Single word month)
    if (titleParts.length === 2 && titleParts[0].length > 2) {
        return title.charAt(0).toUpperCase() + title.slice(1);
    }

    // Default case: return title as is
    return title;
};
