export function gettingCalendarViewName(view?: string): string {
    switch (view) {
        case "dayGridMonth":
            return "month";
        case "timeGridWeek":
            return "basicWeek";
        case "timeGridWorkWeek":
            return "timeGridWorkWeek";
        case "timeGridDay":
            return "basicDay";
        default:
            return view || "";
    }
}

export function gettingCalendarView(view: string): string {
    switch (view) {
        case "month":
            return "dayGridMonth";
        case "basicWeek":
            return "timeGridWeek";
        case "timeGridWorkWeek":
            return "timeGridWorkWeek";
        case "basicDay":
            return "timeGridDay";
        default:
            return view;
    }
}