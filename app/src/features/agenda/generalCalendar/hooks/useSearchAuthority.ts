import useGetCustom from "../../../../hooks/useGetCustom";
import { useState, useCallback } from "react";
import { debounce } from "lodash";

export default function SearchAuthority() {
  const searchOfficeArchiveRequest = useGetCustom(
    "archive/searchoffices?noTemplateVars=true"
  );
  const [searchResult, setSearchResult] = useState<any[]>([]);

  //search functions
  const debounceSearchArchive = debounce(async (value: string) => {
    let params = {
      q: value,
      increment: "0",
    };
    const response: any = await searchOfficeArchiveRequest.doFetch(
      true,
      params
    );
    setSearchResult(response.data);
  }, 500);

  const memoizedSearchArchive = useCallback(debounceSearchArchive, [
    debounceSearchArchive,
  ]);

  const handleArchiveSearch = (value: any) => {
    memoizedSearchArchive(value);
  };

  return {
    handleArchiveSearch,
    searchResult,
    authoriyLoading: searchOfficeArchiveRequest.loading,
  };
}
