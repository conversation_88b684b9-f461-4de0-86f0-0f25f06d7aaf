import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const usePrintCalendarTemplate = ({
    query,
    print,
}: {
    query: any;
    print: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom(
        "/calendar/print-model?noTemplateVars=true"
    );

    useEffect(() => {
        if (print) {
            doFetch(true, query);
        }
    }, [print]);

    useEffect(() => {
        if (hasLoaded && !loading) {
            const blob = new Blob([data], {
                type: "application/pdf",
            });

            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Calendario_generale.pdf");
            link.setAttribute("target", "_blank");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }, [hasLoaded, loading]);

    return { hasLoaded, loading };
};
