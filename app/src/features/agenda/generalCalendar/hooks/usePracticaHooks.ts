export const defaultQuery = {
  page: 0,
  pageSize: 100,
  sortColumn: "codicearchivio",
  sortOrder: "desc",
  codeType: "-1",
  code: "",
  identificatore: "-1",
  dateType: "0",
  startDate: "",
  endDate: "",
  relazioneType: "0",
  relazioneName: "",
  relazioneNameUTN: "",
  peopleCategory: "0",
  customer: "",
  counterpart: "",
  referent: "",
  descrizione: "",
  rgn: "",
  rga: "",
  subprocedimento: "",
  outcome: "-1",
  dataArchivio: "",
  authority: "-1",
  authoritySearch: "",
  tipology: "-1",
  status: "-1",
  sectorStudy: "-1",
  sezioneFascicolo: "",
  subjects_group: "-1",
  object: "",
  searchObject: "",
  archiveDominus: "-1",
  archivePublicProsecutors: " -1",
  archiveCrimes: "-1",
  archiveTipiSpesa: "0",
  istruttore: "",
  sede: "",
  ns: "",
  nsa: "",
  officeSearch: "-1",
  searchTags: "",
  tagsList: "",
  generalProtocol: "",
  situazione: "-1",
  situazioneContabile: " -1",
  centroProfitto: "-1",
  annotazioni: "",
  stanza: "",
  palchetto: "",
  scaffale: "",
  faldone: "",
  scatolone: "",
  categoryId: "-1",
  dynamicSearchFields: "",
  movimentate_have: "-1",
  movimentate_type: "prestazioni",
  movimentate_startDate: "",
  movimentate_endDate: "",
};
