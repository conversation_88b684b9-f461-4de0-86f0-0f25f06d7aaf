import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import type { IPrintTemplatesRespose } from "../typings/generalCalendar.interface";

export const useFilterTemplates = ({
    fetch,
    category = 1,
}: {
    fetch: boolean;
    category?: number;
}): {
    data: IPrintTemplatesRespose[];
    loading: boolean;
    hasLoaded: boolean;
} => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "prints/filter-templates?noTemplateVars=true"
    );

    useEffect(() => {
        if (fetch) {
            doFetch(true, { category });
        }
    }, [fetch, category]);

    return { data, loading, hasLoaded };
};
