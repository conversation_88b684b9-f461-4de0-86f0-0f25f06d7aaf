import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

export const useAgendaHooks = () => {
    const [loggedUser, setLoggedUser] = useState<any>({});
    const [fileStatus, setFileStatus] = useState<any[]>([]);
    const [searchInstructors, setSearchInstructors] = useState<any[]>([]);
    const [cities, setCities] = useState<any[]>([]);
    const [childTypes, setChildTypes] = useState<any>();
    const [macro, setMacro] = useState<any>();
    const [macroCategories, setMacroCategories] = useState<any>();
    const [items, setItems] = useState<any[]>([]);
    const [users, setUsers] = useState<any>();
    const [request, setRequest] = useState<any>();
    const [people, setPeople] = useState<any>();
    const [currentUser, setCurrentUser] = useState<any>();
    const params = useParams<{ id: string }>();

    const agendaRequest = useGetCustom(
        `archiveagenda/agenda?fileUniqueid=${params.id}`
    );

    useEffect(() => {
        if (!fileStatus.length) initAgenda();
    }, []);

    async function initAgenda() {
        try {
            const response: any = await agendaRequest.doFetch();
            setChildTypes(response.data.childType);
            setItems(response.data.items);
            setFileStatus(response.data.fileStatus);
            setLoggedUser(response.data.loggedUser);
            setSearchInstructors(response.data.searchInstructors);
            setMacro(response.data.macro);
            setMacroCategories(response.data.macroCategories);
            setUsers(response.data.users);
            setRequest(response.data.request);
            setPeople(response.data.people);
            setCurrentUser(response.data.currentUser);
            setCities(response.data.cities);
        } catch (error) {
            console.log("Agenda error", error);
            return;
        }
    }

    return {
        fileStatus,
        loggedUser,
        childTypes,
        items,
        searchInstructors,
        macro,
        macroCategories,
        users,
        request,
        people,
        currentUser,
        cities,
        fetchAgain: initAgenda,
    };
};
