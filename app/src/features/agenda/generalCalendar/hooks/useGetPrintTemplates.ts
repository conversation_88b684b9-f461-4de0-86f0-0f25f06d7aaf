import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import type { IPrintTemplatesRespose } from "../typings/generalCalendar.interface";

export const useGetPrintTemplates = ({
    fetch,
}: {
    fetch: boolean;
}): {
    data: IPrintTemplatesRespose[];
    loading: boolean;
    hasLoaded: boolean;
} => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "prints/filter-templates?noTemplateVars=true"
    );

    useEffect(() => {
        if (fetch) {
            doFetch(true, { category: 1 });
        }
    }, [fetch]);

    return { data, loading, hasLoaded };
};
