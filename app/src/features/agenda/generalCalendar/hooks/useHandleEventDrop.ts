import { useState } from "react";
import moment from "moment";
import usePostCustom from "../../../../hooks/usePostCustom";

const normalizeNumber = (num: any) => {
    return num < 10 ? "0" + num : num;
};

export function useHandleEventDrop(fetchEventData: any, query: any) {
    const updateDeadlinesRequest = usePostCustom("/calendar/updatedeadline");
    const updateHearingRequest = usePostCustom("/calendar/updatehearing");
    const changeCloseDeadlineRequest = usePostCustom(
        "/calendar/change-close-deadline"
    );
    const changeCloseHearingRequest = usePostCustom(
        "/calendar/change-close-hearing"
    );
    const [showErrorModal, setShowErrorModal] = useState({
        open: false,
        title: "",
        confirmText: "",
    });

    const updateDeadlineHearing = async (
        typeOfEvent: string,
        uniqueid: string,
        date: any,
        hours: any,
        dynamic: any,
        duration: number
    ) => {
        const params = {
            uniqueid,
            date,
            hours,
            dynamic,
            duration,
        };
        const response: any =
            typeOfEvent === "deadline"
                ? updateDeadlinesRequest.doFetch(true, params)
                : updateHearingRequest.doFetch(true, params);
        return response;
    };

    const handleEventDrop = async (info: any) => {
        const { event, revert } = info;

        if (
            event.extendedProps.isClosedDeadline === "true" ||
            event.extendedProps.isClosedHearing === "true"
        ) {
            const idImpegno = event.extendedProps.idImpegno;
            const newDate = moment(event.start);
            const dateImpegno = moment(event.extendedProps.data, "DD/MM/YYYY");

            if (dateImpegno.diff(newDate) < 0) {
                setShowErrorModal({
                    open: true,
                    title: "Attenzoine!",
                    confirmText: `Non è possibile spostare un avviso in una data successiva alla data dell'impegno"`,
                });
                revert(); //check this later
                return false;
            }

            if (event.extendedProps.isCloseDeadline === "true") {
                const response: any = await changeCloseDeadlineRequest.doFetch(
                    true,
                    {
                        impegno: idImpegno,
                        newDate:
                            newDate.format("YYYY-MM-DD") +
                            " " +
                            event.extendedProps.ora,
                    }
                );
                if (response) {
                    fetchEventData(query);
                }
            } else if (event.extendedProps.isCloseHearing === "true") {
                const response: any = await changeCloseHearingRequest.doFetch(
                    true,
                    {
                        impegno: idImpegno,
                        newDate: newDate.format("YYYY-MM-DD"),
                        dateImpegno: dateImpegno.format("YYYY-MM-DD"),
                    }
                );
                if (response.status === 200) {
                    fetchEventData(query);
                }
            }

            return true;
        }

        const eventDate = event.start;
        const date =
            eventDate.getFullYear() +
            "-" +
            (eventDate.getMonth() + 1) +
            "-" +
            eventDate.getDate();
        const hours =
            normalizeNumber(eventDate.getHours()) +
            ":" +
            normalizeNumber(eventDate.getMinutes());

        const start = event.start;
        const end = event.end;

        const durationMinutes = (end - start) / (1000 * 60);

        const response: any = await updateDeadlineHearing(
            event.extendedProps.type,
            event.extendedProps.uniqueid,
            date,
            hours,
            event.extendedProps.dinamica,
            durationMinutes
        );

        if (response.status === 200) {
            fetchEventData(query);
        }
    };

    return { handleEventDrop, showErrorModal, setShowErrorModal };
}
