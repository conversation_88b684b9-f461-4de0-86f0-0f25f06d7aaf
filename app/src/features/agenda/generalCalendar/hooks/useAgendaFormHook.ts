import { useTranslation } from "@1f/react-sdk";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const getSchemaValidation = (t: any, validaton?: Validation) => {
    return yup.object().shape({
        uniqueid: yup.string(),
        fkUniqueIdUdienzaReinvio: yup.string(),
        archiveuid: yup.string(),
        data: yup.date().nullable(),
        ora: yup.date().nullable(),
        minuti: yup.string(),
        agendaStatopratica: yup.string(),
        agendaAvvocato: yup.string(),
        agendaDaysBefore: yup.number(),
        agendaPeriod:
            validaton === "timesheet"
                ? yup.number().min(1, t("Durata obbligatoria"))
                : yup.number().min(0, t("Il valore deve essere maggiore di 0")),
        referente: yup.array().min(1, t("Il campo Referente deve contenere almeno un elemento")).required(t("Il campo Referente non puo' essere vuoto")),
        attivita: yup.string().required(t("Attività obbligatoria")),
        agendaIstruttore: yup
            .string()
            .required(t("Il campo Giudice non puo' essere vuoto")),
        agendaSezione: yup.string().nullable(),
        autorita: yup.string(),
        citta: yup.string(),
        istruttore: yup.string(),
        annotazioni: yup.string(),
        statopratica: yup.string(),
        parentItemId: yup.string().nullable(),
        referenteData: yup.string(),
        agendaAutoritaDescrizione: yup.string(),
        agendaCittaDescrizione: yup.string(),
        agendaAnnotazioni: yup.string(),
        agendaEvasa: yup.string(),
        agendaBillable: yup.string(),
        agendaNonEvadere: yup.string(),
        newAgendaInstructor: yup.string(),
        insertAgendaAttivita: yup.string(),
    });
};

const defaultValues = {
    uniqueid: "",
    fkUniqueIdUdienzaReinvio: "",
    archiveuid: "",
    data: null,
    ora: null,
    minuti: "",
    agendaStatopratica: "",
    agendaAvvocato: "",
    agendaDaysBefore: 0,
    agendaPeriod: 0,
    referente: [],
    attivita: "",
    agendaIstruttore: "",
    agendaSezione: "",
    autorita: "",
    citta: "",
    istruttore: "",
    annotazioni: "",
    statopratica: "",
    parentItemId: null,
    referenteData: "",
    agendaAutoritaDescrizione: "",
    agendaCittaDescrizione: "",
    agendaAnnotazioni: "",
    agendaEvasa: "",
    agendaBillable: "",
    agendaNonEvadere: "",
    newAgendaInstructor: "",
    insertAgendaAttivita: "",
    importaItemsSelect: "",
};

type Validation = "default" | "timesheet";

export const useAgendaFormHook = (validaton?: Validation) => {
    const { t } = useTranslation();
    const method = useForm({
        resolver: yupResolver(getSchemaValidation(t, validaton)),
        defaultValues,
    });

    return method;
};
