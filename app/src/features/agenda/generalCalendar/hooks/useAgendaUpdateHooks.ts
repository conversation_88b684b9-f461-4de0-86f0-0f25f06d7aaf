import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useParams } from "react-router-dom";
import React from "react";
import { useTranslation } from "@1f/react-sdk";
import { getImpegniCorrelati } from "../../../../utilities/agenda/gridColumn";
import { useLocation } from "react-router-dom";

const LIST_AGENDA_PARAMS: any = {
    linkuid: "",
    noTemplateVars: true,
    page: 0,
    pageSize: 8,
    sortColumn: "data",
    sortOrder: "desc",
};

export const useAgendaUpdateHooks = () => {
    const location = useLocation();

    const { id, uniqueId }: any = useParams();  

    const uniqueIdToSend = uniqueId || id;
    const fileUniqueid = location.state?.fileUniqueid;
    const origin = location.state?.origin;


    const rowDataUrl = location.state?.rowDataUrl || "agendaDeadline";


    const [deadlineLinkuId, setDeadlineLinkuId] = React.useState("");
    const [state, setState] = React.useState({
        columns: [],
        rowData: null,
        parentData: null,
        listAgenda: {
            currentPage: [],
            totalRows: 0,
        },
    });

    const [rowData, setRowData] = React.useState({
        form: {},
        users: [],
        isLastHearing: false,
    });

    const { t } = useTranslation();
    const [defaultLAParams, setDefaultLAParams] =
        React.useState(LIST_AGENDA_PARAMS);

    const params = useParams<{ fileUniqueid: string }>();

    const getRowDataRequest = usePostCustom(
        origin && origin === "agenda"
            ? `agenda/getrowdata?noTemplateVars=true`
            : `${rowDataUrl}/getrowdata?noTemplateVars=true`
    );

    const getListAgendaRq = useGetCustom(
        "agendadeadlines/list",
        defaultLAParams
    );

    React.useEffect(() => {
        getData();
    }, [id, uniqueId]);


    React.useEffect(() => {
        if (
            defaultLAParams.linkuid &&
            state.listAgenda?.currentPage?.length === 0
        ) {
            getListAgenda();
        }
    }, [defaultLAParams.linkuid,]);

    const getData = async (reInvia?: boolean, fileUniqueIdFromRinvia?: string) => {
        if (!uniqueIdToSend) {
            return;
        }
        const params: any = {
            fileUniqueid: fileUniqueIdFromRinvia ? fileUniqueIdFromRinvia : fileUniqueid,
            uniqueId: uniqueIdToSend,
        };
        if (reInvia) {
            params.uniqueId = "";
            params.postpone = true;
        }
        const dataRow: any = await getRowDataRequest.doFetch(true, {
            ...params,
        });
        setRowData(dataRow.data);
        setDefaultLAParams({
            ...defaultLAParams,
            linkuid: dataRow.data.form.deadlineLinkuid,
        });
        setDeadlineLinkuId(dataRow.data.form.deadlineLinkuid);
        setState((prevState: any) => ({
            ...prevState,
            rowData: dataRow.data,
        }));
    };

    const getListAgenda = async () => {
        const response: any = await getListAgendaRq.doFetch(true);

        const columns: any[] = await getImpegniCorrelati(t);

        setState((prevState: any) => ({
            ...prevState,
            listAgenda: response.data,
            columns: columns,
        }));
    };

    const onPageChange = (_: any, page: number) => {
        setDefaultLAParams({
            ...defaultLAParams,
            page,
        });
    };

    const onSortChange = (column: any, direction: any) => {
        setDefaultLAParams({
            ...defaultLAParams,
            sortColumn: column,
            sortOrder: direction,
        });
    };

    return {
        state,
        rowData,
        deadlineLinkuId,
        id: params.fileUniqueid,
        listAgendaRq: getListAgendaRq,
        onPageChange,
        onSortChange,
        defaultLAParams,
        setDefaultLAParams,
        fetchAgain: getData,
    } as any;
};
