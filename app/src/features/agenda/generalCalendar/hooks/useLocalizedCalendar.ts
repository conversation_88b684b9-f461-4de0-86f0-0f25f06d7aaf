import { LocaleInput } from "@fullcalendar/core";

export const useLocalizedCalendar = (baseLocale: any): LocaleInput => {
    return {
        ...baseLocale,
        week: {
            dow: 1, // Monday is the first day of the week
            doy: 4, // The week that contains Jan 4th is the first week of the year
        },
        buttonText: {
            today: "Oggi",
            month: "Me<PERSON>",
            week: "Settimana",
            day: "Giorno",
        },
        weekText: "Sm",
        allDayText: "Tutto il giorno",
        noEventsText: "Non ci sono eventi da visualizzare",
        monthNames: [
            "Gennaio",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "April<PERSON>",
            "Ma<PERSON>",
            "<PERSON>iu<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "A<PERSON><PERSON>",
            "Settembre",
            "Ottobre",
            "Novembre",
            "Dicembre",
        ],
        monthNamesShort: [
            "Gen",
            "Feb",
            "Mar",
            "Apr",
            "Mag",
            "Giu",
            "Lug",
            "Ago",
            "Set",
            "<PERSON>tt",
            "Nov",
            "Dic",
        ],
        dayNames: [
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
        ],
        dayNamesShort: ["Dom", "Lun", "Mar", "Mer", "Gio", "Ven", "Sab"],
    };
};
