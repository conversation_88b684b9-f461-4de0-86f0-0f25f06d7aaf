import VaporPage from "@vapor/react-custom/VaporPage";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Filters from "./sections/Filters";
import React, { useEffect, useRef, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { DEFAULT_QUERY, useTimesheetHook } from "./hooks/useTimesheetHook";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import PageTitle from "../../../custom-components/PageTitle";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { debounce } from "lodash";
import { mapDataForCustomTable } from "./utils";
import PersonalizaExport from "./sections/PersonalizaExport";
import PdfExport from "./sections/PdfExport";
import "./utils/index.css";
import moment from "moment";

export const updateParams = (params: any, dateRange: any) => {
    if (dateRange.date) {
        params.date = moment(dateRange.date).format("DD/MM/YYYY");
    }

    return params;
};

const convertStringToDate = (date: any) => {
    if (!date) {
        return null;
    }
    const [day, month, year] = date.split("/");
    return new Date(year, month - 1, day);
};

const TimesheetIndex = () => {
    const [data, setData] = useState<any[]>([]);
    const [openModalPersonaliza, setOpenModalPersonaliza] = useState(false);
    const [openModalPdf, setOpenModalPDF] = useState(false);

    const [dateRange, setDateRange] = useState({
        date: new Date(),
        endDate: DEFAULT_QUERY.endDate,
    });

    const resetClicked = useRef(false);
    const [defaultParams, setDefaultParams] = useState(DEFAULT_QUERY);
    const [open, setOpen] = React.useState(false);
    const [, setTotalRows] = useState<number>(0);
    const { columns, users, feeTypes, fields, search } = useTimesheetHook();
    const [items, setItems] = useState([]);
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();

    const timesheetGridRequest = useGetCustom(
        "timesheet/gettimesheet",
        updateParams(defaultParams, dateRange)
    );

    const defaulttimesheetGridRequest = useGetCustom(
        "timesheet/gettimesheet",
        updateParams(DEFAULT_QUERY, dateRange)
    );

    useEffect(() => {
        if (search && (search.date || search.endDate)) {
            setDefaultParams({ ...search, noTemplateVars: true });
            const startDate = convertStringToDate(search.date);
            const endDate = convertStringToDate(search.endDate);

            setDateRange((prevDate: any) => ({
                ...prevDate,
                date: startDate,
                endDate: endDate,
            }));
        }
    }, [search]);

    useEffect(() => {
        if (!resetClicked.current) {
            startSearchList();
        }
    }, [
        defaultParams.date,
        defaultParams.endDate,
        defaultParams.user,
        defaultParams.codeType,
        defaultParams.order,
        defaultParams.processed,
        defaultParams.fattura,
        defaultParams.visible,
        defaultParams.feeType,
        defaultParams.billable,
        defaultParams.parcella,
    ]);

    useEffect(() => {
        if (location?.state?.reload) {
            startSearchList();
        }
    }, [location]);

    const getTimeeSheetData = React.useCallback(async () => {
        startSearchList();
    }, [defaultParams.file_id, defaultParams.client, defaultParams.task]);

    const debouncedSearch = React.useCallback(
        debounce(getTimeeSheetData, 500),
        [getTimeeSheetData]
    );

    React.useEffect(() => {
        if (
            defaultParams.file_id ||
            defaultParams.client ||
            defaultParams.task
        ) {
            debouncedSearch();
        }

        return () => {
            debouncedSearch.cancel();
        };
    }, [
        defaultParams.file_id,
        defaultParams.client,
        defaultParams.task,
        debouncedSearch,
    ]);

    const timesheetRequest = useGetCustom("timesheet/timesheet");

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaulttimesheetGridRequest.doFetch(true)
            : timesheetGridRequest.doFetch(true));
        const responseee: any = await timesheetRequest.doFetch();
        setItems(responseee.data.items);

        if (reset) {
            resetClicked.current = false;
        }
        const mappedData = mapDataForCustomTable(response.data);
        setData(mappedData);
        setTotalRows(response.data.tasks.length);
    };
    const clearAll = () => {
        resetClicked.current = true;
        setDefaultParams(DEFAULT_QUERY);
        startSearchList(true);
        setDateRange((prevDateRange: any) => ({
            ...prevDateRange,
            date: new Date(),
            endDate: "",
        }));
    };

    const handleDialog = () => {
        setOpen(!open);
    };

    const onSubmit = () => {
        startSearchList();
    };

    const downloadTimesheetRequest = useGetCustom("default/timesheet/export");
    const exportTimesheetCSV = async (option: string) => {
        if (data.length > 0) {
            const response: any = await downloadTimesheetRequest.doFetch(true, {
                option: option,
            });
            const blob = new Blob([response.data], { type: "text/csv" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "timesheet.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const personalizaExport = () => {
        setOpenModalPersonaliza(!openModalPersonaliza);
    };

    const pdfExport = () => {
        if (data.length > 0) {
            setOpenModalPDF(!openModalPdf);
        }
    };

    const handleClickCallback = (id: string) => {
        navigate(`/timesheet/update/${id}`, { state: { items } });
    };
    const handleClickCreate = () => {
        navigate(`/timesheet/create/`, { state: { items } });
    };
    return (
        <VaporPage>
            <PersonalizaExport
                openModalPersonaliza={openModalPersonaliza}
                setOpenModalPersonaliza={setOpenModalPersonaliza}
                fields={fields}
            />
            <PdfExport
                openModalPdf={openModalPdf}
                setOpenModalPDF={setOpenModalPDF}
                fields={fields}
            />
            <PageTitle
                title={t("Timesheet")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: "Esporta",
                        onclick: () => handleDialog(),
                        dropdown: true,
                        options: [
                            { label: "PDF", handleClick: pdfExport },
                            {
                                label: "Excel - Lista",
                                handleClick: exportTimesheetCSV,
                            },
                            {
                                label: "Excel - Ore in centesimi",
                                handleClick: exportTimesheetCSV,
                            },
                            {
                                label: "Personalizza esportazione",
                                handleClick: personalizaExport,
                            },
                        ],
                    },
                    {
                        label: t("Nuova attività"),
                        onclick: () => handleClickCreate(),
                        variant: "contained",
                        startIcon: <AddCircleOutlineIcon />,
                    },
                ]}
            ></PageTitle>

            <VaporPage.Section>
                <Filters
                    params={defaultParams}
                    setDefaultParams={setDefaultParams}
                    users={users}
                    feeTypes={feeTypes}
                    clearAll={clearAll}
                    dateRange={dateRange}
                    setDateRange={setDateRange}
                    onSubmit={onSubmit}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <CustomDataGrid
                    name="timesheet"
                    columns={columns}
                    data={data}
                    page={0}
                    totalRows={data.length}
                    pageSize={data.length}
                    loading={
                        timesheetGridRequest.loading ||
                        defaulttimesheetGridRequest.loading
                    }
                    query={defaultParams}
                    setQuery={setDefaultParams}
                    onClickKey="id"
                    onClickCheckboxKey="id"
                    onClickCallback={handleClickCallback}
                    paginationMode="client"
                    // getRowClassName={(params: any) => {
                    //     if (params.row.type === "total") {
                    //         return `row-type-total`;
                    //     }
                    //     return `row-type-${
                    //         params.row.type === "file" ? "file" : ""
                    //     }`;
                    // }}
                />
            </VaporPage.Section>
        </VaporPage>
    );
};
export default TimesheetIndex;
