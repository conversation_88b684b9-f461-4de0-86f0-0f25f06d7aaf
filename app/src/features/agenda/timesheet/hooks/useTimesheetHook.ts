import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useTranslation } from "@1f/react-sdk";
import { getTimesheetGrid } from "../../../../utilities/timesheet/gridColumn";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useNavigate } from "react-router-dom";

export const DEFAULT_QUERY = {
    noTemplateVars: true,
    date: new Date(),
    endDate: "",
    user: "0",
    client: "",
    categoria: "0",
    task: "",
    codeType: "-1",
    file_id: "",
    order: "data",
    processed: "-1",
    fattura: "-1",
    visible: "-1",
    feeType: "-1",
    billable: "-1",
    parcella: "",
};

let timesheetCache: any = null;

export const useTimesheetHook = () => {
    const { t } = useTranslation();

    const [data, setData] = useState<{
        users: any[];
        feeTypes: any[];
        fields: any;
        loggedUser: any;
        columns: any[];
        items: any[];
        childTypes: any;
        search: any;
    }>({
        users: [],
        feeTypes: [],
        fields: null,
        loggedUser: null,
        columns: [],
        items: [],
        childTypes: null,
        search: null,
    });

    const timesheetRequest = useGetCustom("timesheet/timesheet");
    const timesheetGrid = usePostCustom(
        "timesheet/getgrid?noTemplateVars=true"
    );

    const navigate = useNavigate();

    useEffect(() => {
        initTimesheet();
    }, []);

    async function initTimesheet() {
        try {
            if (timesheetCache) {
                setData(timesheetCache);
                return;
            }

            const [response, timeSheedData]: any = await Promise.all([
                timesheetRequest.doFetch(),
                timesheetGrid.doFetch(true),
            ]);

            const finalColumns: any = await getTimesheetGrid(
                timeSheedData.data,
                t,
                navigate
            );

            const fetchedData = {
                users: response.data.users,
                feeTypes: response.data.feeTypes,
                fields: response.data.fields,
                loggedUser: response.data.loggedUser,
                items: response.data.items,
                search: response.data.search,
                childTypes: response.data.childType,
                columns: finalColumns,
            };

            setData((prevData: any) => ({
                ...prevData,
                ...fetchedData,
            }));

            timesheetCache = fetchedData;
        } catch (error) {
            console.log("Timesheet Request error", error);
            return;
        }
    }

    const {
        columns,
        users,
        feeTypes,
        childTypes,
        items,
        loggedUser,
        fields,
        search,
    } = data;

    return {
        t,
        columns,
        users,
        feeTypes,
        childTypes,
        items,
        loggedUser,
        fields,
        search,
    };
};
