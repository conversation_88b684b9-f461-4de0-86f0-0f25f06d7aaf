import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import moment from "moment";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useNavigate } from "react-router-dom";

const schema = yup.object().shape({
    id: yup.string(),
    file_id: yup.string(),
    taskCategory: yup.string(),
    date: yup.date(),
    started_at: yup.string(),
    ended_at: yup.string(),
    duration: yup.string(),
    user: yup.string(),
    timesheetRate: yup.number(),
    task: yup.string(),
    addebitabile: yup.boolean(),
    billable: yup.boolean(),
    visible: yup.boolean(),
    processed: yup.boolean(),
    importaItemsSelect: yup.string(),
    annotations: yup.string(),
    parentItemId: yup.string(),
});

export const useTimesheetFormHook = () => {
    const method = useForm({
        defaultValues: {
            id: "",
            file_id: "",
            taskCategory: "-1",
            date: new Date(),
            started_at: "",
            ended_at: "",
            duration: "",
            user: "",
            timesheetRate: 0,
            task: "",
            importaItemsSelect: "",
            annotations: "",
            visible: false,
            processed: false,
            addebitabile: false,
            billable: false,
            parentItemId: "",
        },
        resolver: yupResolver(schema),
    });

    const usePostRequest = usePostCustom(
        `default/timesheet/save?noTemplateVars=true`
    );
    const iniziaAttivitaPostRequest = usePostCustom("timesheet/timer");
    const formatDate = (date: Date): string => {
        const day = String(date.getDate()).padStart(2, "0");
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };

    const formatTime = (date: Date): string => {
        const formattedTime = moment(date).format("HH:mm");
        return formattedTime;
    };

    const calculateTimeDifference = (started_at: any, ended_at: any) => {
        const [startHours, startMinutes] = started_at.split(":").map(Number);
        const [endHours, endMinutes] = ended_at.split(":").map(Number);
        const totalStartMinutes = startHours * 60 + startMinutes;
        const totalEndMinutes = endHours * 60 + endMinutes;
        let difference = totalEndMinutes - totalStartMinutes;

        if (difference < 0) {
            difference = 0;
        }

        const hours = Math.floor(difference / 60)
            .toString()
            .padStart(2, "0");
        const minutes = (difference % 60).toString().padStart(2, "0");

        return `${hours}:${minutes}`;
    };
    const navigate = useNavigate();
    const onSubmit = async (values: any) => {
        const zeroKeys: string[] = ["billable", "addebitabile"];
        Object.keys(values).forEach((key: string) => {
            if (typeof values[key] === "boolean") {
                if (values[key]) {
                    values[key] = "on";
                } else {
                    delete values[key];
                }
                if (zeroKeys.includes(key) && !values[key]) {
                    values[key] = "0";
                }
            }
            if (key === "date") {
                const formattedDate = formatDate(values[key]);
                values[key] = formattedDate;
            }
            if (key === "started_at") {
                const formattedTime = formatTime(values[key]);
                values[key] = formattedTime;
            }

            if (key === "ended_at") {
                const formattedTime = formatTime(values[key]);
                values[key] = formattedTime;
            }
            if (key === "duration") {
                values[key] = calculateTimeDifference(
                    formatTime(values["started_at"]),
                    formatTime(values["ended_at"])
                );
            }
        });

        await usePostRequest.doFetch(true, values);
        navigate("/timesheet/timesheet");
    };

    const iniziaAttivita = async (values: any) => {
        Object.keys(values).forEach((key: string) => {
            if (typeof values[key] === "boolean") {
                values[key] = values[key] ? "on" : "";
            }
            if (key === "date") {
                const formattedDate = formatDate(values[key]);
                values[key] = formattedDate;
            }
            if (key === "started_at") {
                const formattedTime = formatTime(values[key]);
                values[key] = formattedTime;
            }

            if (key === "ended_at") {
                const formattedTime = formatTime(values[key]);
                values[key] = formattedTime;
            }
        });

        await iniziaAttivitaPostRequest.doFetch(true, values);

        navigate("/timesheet/timesheet");
        localStorage.setItem("isTimerSet", "true");
    };
    const { control } = method;

    return { method, control, onSubmit, iniziaAttivita };
};
