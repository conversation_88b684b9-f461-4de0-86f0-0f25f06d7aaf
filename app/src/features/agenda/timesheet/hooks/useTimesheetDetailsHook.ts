import useGetCustom from "../../../../hooks/useGetCustom";
import { useParams } from "react-router-dom";
import React from "react";
import moment from "moment";

export const useTimesheetDetailHook = () => {
  const [state, setState] = React.useState({});
  const params = useParams<{ id: string }>();

  const getAgendaTimesheet = useGetCustom(
    params.id
      ? `default/timesheet/gettaskdata?id=${params.id}&noTemplateVars=true`
      : `default/timesheet/gettaskdata?date=${moment().format(
          "DD/MM/YYYY"
        )}&noTemplateVars=true`
  );
  const getAttivita = useGetCustom("attivita");

  React.useEffect(() => {
    if (Object.keys(state).length === 0) getData();
  }, []);

  const getData = async () => {
    const [data, activityData]: any = await Promise.all([
      getAgendaTimesheet.doFetch(true),
      getAttivita.doFetch(true),
    ]);

    setState((prevState: any) => ({
      ...prevState,
      taskData: data.data,
      activityData: activityData.data,
    }));
  };

  return { state, id: params.id, getData } as any;
};
