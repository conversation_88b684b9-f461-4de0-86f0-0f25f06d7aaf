import React from "react";
import {
    Box,
    ListItem,
    List,
    Divider,
    ListItemText,
    Button,
    Typography,
    Grid,
} from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { useTimesheetHook } from "../hooks/useTimesheetHook";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

const CreateDatiAggiuntivi = (props: any) => {
    const { t } = useTranslation();
    const { method, id, state, items } = props;
    const [collega, setCollega] = React.useState(false);
    const [parentData, setParentData] = React.useState({
        data: "",
        externalUid: "",
        nome: "",
        tipo: "",
        tipologia: "",
        referente: "",
    });
    const { childTypes } = useTimesheetHook();
    const { watch, control, setValue } = method;

    const importaItemsSelect = watch("importaItemsSelect");

    const getId = (id: any) => {
        if (id) {
            return state.taskData.parentId;
        }
        if (importaItemsSelect) {
            return importaItemsSelect;
        }

        return items[0]?.id;
    };

    const getParentDataReq = useGetCustom(
        `item/get-parent-data?itemId=${getId(
            state.taskData.parentId
        )}&noTemplateVars=true`
    );

    const linkItem = usePostCustom(`item/link-item?noTemplateVars=true`);
    const unlinkItem = usePostCustom(`item/unlink-item?noTemplateVars=true`);

    React.useEffect(() => {
        if (items.length) {
            getParentData();
            setValue("importaItemsSelect", items[0]?.id);
        }
    }, [items.length, items]);

    React.useEffect(() => {
        if (state?.taskData?.parentId) {
            setCollega(true);
        }
    }, []);

    React.useEffect(() => {
        if (importaItemsSelect) {
            getParentData();
            setValue("importaItemsSelect", importaItemsSelect);
        }
    }, [importaItemsSelect]);

    const getParentData = async () => {
        if (importaItemsSelect) {
            const response: any = await getParentDataReq.doFetch(
                true,
                importaItemsSelect
            );
            setParentData(response.data);
        } else {
            const response: any = await getParentDataReq.doFetch(
                true,
                getId(state.taskData.parentId)
            );
            setParentData(response.data);
        }
    };

    const changeCollega = async () => {
        const formData = new FormData();
        formData.append("parentId", getId(state.taskData.parentId));
        formData.append("childUid", id);

        if (!collega) {
            await linkItem.doFetch(true, formData);
            setCollega(true);
        } else {
            await unlinkItem.doFetch(true, formData);
            setCollega(false);
        }
        props.getData();
    };

    return (
        <>
            {childTypes && (
                <>
                    {" "}
                    <Typography variant="body700" gutterBottom component="div">
                        {t("N.B.")}
                    </Typography>
                    {childTypes === 1 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Timesheet può essere collegato solo a oggetti di tipo"
                                )}
                            </Typography>
                            <List>
                                <ul>
                                    <li>{t("Impegno")}</li>
                                    <li>{t("Udienza")}</li>
                                    <li>{t("Libero")}</li>
                                </ul>
                            </List>
                        </>
                    )}
                    {childTypes === 2 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Movimento può essere collegato solo a oggetti di tipo"
                                )}
                            </Typography>
                            <List>
                                <ListItem>
                                    <ListItemText primary={t("Timesheet")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Impegno")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Udienza")} />
                                </ListItem>
                                <ListItem>
                                    <ListItemText primary={t("Libero")} />
                                </ListItem>
                            </List>
                        </>
                    )}
                    {childTypes === 3 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un Impegno può essere collegato solo a oggetti di tipo 'Libero'"
                                )}
                            </Typography>
                        </>
                    )}
                    {childTypes === 4 && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Un'Udienza può essere collegata solo a oggetti di tipo 'Libero'"
                                )}
                            </Typography>
                        </>
                    )}
                    {!items && (
                        <>
                            <Typography
                                variant="bodySmall500"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Non sono presenti oggetti a cui poter collegare questa voce"
                                )}
                            </Typography>
                        </>
                    )}
                </>
            )}

            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                {collega ? (
                    <Typography>
                        {t("Questo Timesheet È collegato a ")}
                        <b> {`${parentData.tipologia} - ${parentData.nome}`}</b>
                    </Typography>
                ) : (
                    <FormInput
                        control={control}
                        name="importaItemsSelect"
                        type="select"
                        label={t("Oggetto")}
                        disabled={items.length > 0 ? false : true}
                        style={{ width: 350 }}
                        options={items.map((item: any) => ({
                            value: item.id,
                            label: `${item.tipo} - ${item.nome}`,
                        }))}
                    />
                )}
                <Button
                    size="small"
                    variant="contained"
                    color={`${collega ? "error" : "primary"}`}
                    sx={{ mt: collega ? 0 : 4 }}
                    onClick={() => changeCollega()}
                    disabled={items.length > 0 ? false : true}
                >
                    {collega ? t("Scollega") : t("Collega")}
                </Button>
            </Box>

            <Divider className="MuiDivider-VaporLight" sx={{ mt: 2 }} />
            {items.length > 0 && (
                <Box style={{ width: 350 }} sx={{ mt: 3 }}>
                    <Grid container spacing={2}>
                        <Grid
                            item
                            xs={6}
                            sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                            {t("Nome")}
                        </Grid>
                        <Grid
                            item
                            xs={6}
                            sx={{
                                display: "flex",
                                justifyContent: "flex-start",
                            }}
                        >
                            {parentData.nome}
                        </Grid>
                    </Grid>

                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid
                            item
                            xs={6}
                            sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                            {t("Tipologia")}
                        </Grid>
                        <Grid
                            item
                            xs={6}
                            sx={{
                                display: "flex",
                                justifyContent: "flex-start",
                            }}
                        >
                            {parentData.tipologia}
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid
                            item
                            xs={6}
                            sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                            {t("Data")}
                        </Grid>
                        <Grid
                            item
                            xs={6}
                            sx={{
                                display: "flex",
                                justifyContent: "flex-start",
                            }}
                        >
                            {parentData.data}
                        </Grid>
                    </Grid>

                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid
                            item
                            xs={6}
                            sx={{ display: "flex", justifyContent: "flex-end" }}
                        >
                            {t("Referente")}
                        </Grid>
                        <Grid
                            item
                            xs={6}
                            sx={{
                                display: "flex",
                                justifyContent: "flex-start",
                            }}
                        >
                            {parentData.referente || "-"}
                        </Grid>
                    </Grid>
                </Box>
            )}
        </>
    );
};

export default CreateDatiAggiuntivi;
