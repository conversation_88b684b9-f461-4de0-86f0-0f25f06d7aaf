import { useState } from "react";
import {
  Box,
  Chip,
  DialogContent,
  Dialog,
  DialogTitle,
  DialogActions,
  DialogContentText,
  Divider,
  Grid,
} from "@vapor/react-material";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import usePostCustom from "../../../../hooks/usePostCustom";

const PersonalizaExport = (props: any) => {
  const { openModalPersonaliza, setOpenModalPersonaliza, fields } = props;
  const [campiSelected, setCampiSelected] = useState<any[]>([]);
  const { t } = useTranslation();

  const confirmPostRequest = usePostCustom(
    "timesheet/setcustomfields?noTemplateVars=true"
  );

  const handleChipClick = (campi: any) => {
    if (!campiSelected.some((c: any) => c === campi)) {
      setCampiSelected([...campiSelected, campi]);
    } else {
      setCampiSelected(campiSelected.filter((c: any) => c !== campi));
    }
  };

  const confirmCampiSelected = async () => {
    const formData = new FormData();
    campiSelected.forEach((element: any) => {
      formData.append("fields[]", element);
    });

    try {
      const response: any = await confirmPostRequest.doFetch(true, formData);
      if (response.data.status === "ok") {
        setOpenModalPersonaliza(!openModalPersonaliza);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const ripristinaCampi = async () => {
    setCampiSelected([]);
    try {
      const response: any = await confirmPostRequest.doFetch(true);
      if (response.data.status === "ok") {
        setOpenModalPersonaliza(!openModalPersonaliza);
      }
    } catch (error) {
      console.log("error", error);
    }
  };
  return (
    <>
      <Dialog
        open={openModalPersonaliza}
        onClose={() => setOpenModalPersonaliza(!openModalPersonaliza)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>
          {t("Personalizza lista")}

          <Box display="flex" alignItems="right">
            <IconButton
              color="primary"
              onClick={() => setOpenModalPersonaliza(!openModalPersonaliza)}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <Divider className="MuiDivider-VaporLight" />
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid
                item
                xs={3}
                sx={{ display: "flex", justifyContent: "flex-end" }}
              >
                {t("Campi")}
              </Grid>
              <Grid
                item
                xs={9}
                sx={{ display: "flex", justifyContent: "flex-start" }}
              >
                <Grid container spacing={2} gap={1}>
                  {fields &&
                    Object.entries(fields).map(([key, value]: any) => (
                      <Chip
                        onClick={() => handleChipClick(key)}
                        style={{ cursor: "pointer" }}
                        size="small"
                        label={value.name}
                        variant={
                          campiSelected.some((c: any) => c === key)
                            ? "outlined"
                            : "filled"
                        }
                      ></Chip>
                    ))}
                </Grid>
              </Grid>
            </Grid>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            type="button"
            variant="outlined"
            onClick={ripristinaCampi}
            color="error"
            sx={{ mr: 1 }}
          >
            {t("Ripristina colonne")}
          </Button>
          <Button
            variant="outlined"
            onClick={() => setOpenModalPersonaliza(!openModalPersonaliza)}
          >
            {t("Annulla")}
          </Button>
          <Button variant="contained" onClick={confirmCampiSelected}>
            {t("Conferma")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PersonalizaExport;
