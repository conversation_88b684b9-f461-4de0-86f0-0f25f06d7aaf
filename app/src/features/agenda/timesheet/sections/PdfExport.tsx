import { useState } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    FormControl,
    Select,
    MenuItem,
} from "@vapor/react-material";

import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import usePostCustom from "../../../../hooks/usePostCustom";

const PdfExport = (props: any) => {
    const { openModalPdf, setOpenModalPDF } = props;
    const [printValue, setPrintValue] = useState<string>("0");
    const { t } = useTranslation();

    const exportPdfPostRequest = usePostCustom(
        "default/timesheet/print?noTemplateVars=true&printmode=" + printValue
    );

    const handleSelectChange = (event: any) => {
        setPrintValue(event.target.value);
    };
    const printButton = async () => {
        const response: any = await exportPdfPostRequest.doFetch(
            true,
            {},
            "",
            "blob"
        );

        const blob = new Blob([response.data], { type: "application/pdf" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "Timesheet.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <>
            <Dialog
                open={openModalPdf}
                onClose={() => setOpenModalPDF(!openModalPdf)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Opzioni di stampa")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={() => setOpenModalPDF(!openModalPdf)}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        {" "}
                        <Box display="flex" gap={1} sx={{ mt: 2 }}>
                            <FormControl
                                sx={{
                                    width: 400,
                                }}
                            >
                                <Select
                                    id="printMode"
                                    name="printMode"
                                    value={printValue}
                                    labelId="select-label"
                                    onChange={handleSelectChange}
                                >
                                    <MenuItem key={0} value="0">
                                        {t("Dettaglio e totali generali")}
                                    </MenuItem>
                                    <MenuItem key={1} value="1">
                                        {t(
                                            "Totali per utente per pratica con dettaglio e totali generali"
                                        )}
                                    </MenuItem>
                                    <MenuItem key={2} value="2">
                                        {t(
                                            "Totali per utente per pratica senza dettaglio e totali generali"
                                        )}
                                    </MenuItem>
                                    <MenuItem key={3} value="3">
                                        {t(
                                            "Solo totali senza divisione per pratica con totali generali"
                                        )}
                                    </MenuItem>
                                </Select>
                            </FormControl>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        onClick={() => setOpenModalPDF(!openModalPdf)}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" onClick={printButton}>
                        {t("Stampa")}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default PdfExport;
