import { Box, Button } from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { useTimesheetHook } from "../hooks/useTimesheetHook";
import moment from "moment";

const CreateAnnotazioni = (props: any) => {
  const { method } = props;
  const { t } = useTranslation();

  const { loggedUser } = useTimesheetHook();

  const { control, setValue, watch } = method;

  const annotationsValues = watch("annotations");

  const addSiggla = () => {
    const currentDate = moment();
    const formattedDateTime = currentDate.format("DD.MM.YYYY HH:mm:ss");
    if (!annotationsValues) {
      setValue(
        "annotations",
        loggedUser.nomepersonale + " " + formattedDateTime
      );
    } else {
      const val = annotationsValues + "\n";
      setValue(
        "annotations",
        val + loggedUser.nomepersonale + " " + formattedDateTime
      );
    }
  };

  return (
    <>
      <Box display="flex" gap={1} sx={{ mt: 2 }}>
        <FormInput
          control={control}
          name="annotations"
          type="textarea"
          label={t("Annotazioni")}
          row="5"
          style={{ width: 350, resize: "none" }}
        />
        <Button
          size="small"
          variant="contained"
          sx={{ mt: 4 }}
          onClick={() => addSiggla()}
        >
          {t("Sigla")}
        </Button>
      </Box>
    </>
  );
};

export default CreateAnnotazioni;
