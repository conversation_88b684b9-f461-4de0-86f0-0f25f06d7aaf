export const convertMinutesToDate = (minutes: string): Date => {
    const totalMinutes = parseInt(minutes, 10);
    const hours = Math.floor(totalMinutes / 60);
    const mins = totalMinutes % 60;
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(mins);
    date.setSeconds(0);
    date.setMilliseconds(0);

    return date;
};

function pad(number: number): string {
    return number < 10 ? "0" + number : number.toString();
}

// Function to convert minutes to "HH:mm" format
export const convertMinutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    // Pad hours and minutes with leading zeros if needed
    const paddedHours = String(hours).padStart(2, "0");
    const paddedMinutes = String(mins).padStart(2, "0");
    return `${paddedHours}:${paddedMinutes}`;
};
export const a11yProps = (index: number) => {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
};

// function combineFilesAndTasks(files: any, tasks: any[]) {
//   let combinedData = [];
//   let uniqueIdCounter = 0;

//   for (let i = 0; i < tasks.length; i++) {
//     const task: any = tasks[i];
//     const file = files[task.file_id];

//     if (file) {
//     }
//   }

//   for (const fileId in files) {
//     const file = files[fileId];
//     const relatedTasks = tasks
//       .reverse()
//       .filter((task) => task.file_id === fileId);

//     combinedData.push({ ...file, type: "file" });
//     relatedTasks.forEach((task) => {
//       combinedData.push({ ...task, type: "task" });
//     });
//   }

//   return combinedData.map((item, index) => ({
//     ...item,
//     id: `${item.id}_${index}`,
//   }));
// }

function createTimesheetData(timesheet: any) {
    if (!timesheet || !timesheet.tasks || !timesheet.tasks.length) {
        return [];
    }

    let tariffa_oraria = "0.0";
    let tariffa_totale = "0.0";
    let costo_risorsa_fixed;
    let margin;

    const tasks = timesheet.tasks;
    const files = timesheet.files;
    let resultArray: any[] = [];
    let currentFile: any = null;
    let pointer = 0;
    tasks.forEach((t: any) => {
        let taskData = {};
        let hourly_rate = t.hourly_rate;

        if (currentFile !== t.file_id) {
            currentFile = t.file_id;
            let fileData = {
                type: "file",
                id: t.file_id + `${pointer++}`,
                file_id: t.file_id,
                codicearchivio: t.codicearchivio || "-",
                nomeutente: t.nomeutente || "-",
                fileHeaderString:
                    files[t.file_id]?.fileHeaderString ||
                    "Attività non associate a pratiche",
                listaclienti:
                    files[t.file_id]?.listaclienti ||
                    "Nessun Cliente associato",
                listacontroparti:
                    files[t.file_id]?.listacontroparti ||
                    "Nessuna Controparte associata",
                date: t.date || null,
                order: t.order || "file",
            };
            resultArray.push(fileData);
        }

        if (currentFile > 0 && files[currentFile].hourly_rate > 0) {
            hourly_rate = files[currentFile].hourly_rate;
        }

        if (
            typeof t.tariffaEffettivaTotale !== "undefined" &&
            typeof t.costoRisorsaTotale !== "undefined" &&
            t.tariffaEffettivaTotale !== null &&
            t.costoRisorsaTotale !== null
        ) {
            tariffa_oraria = parseFloat(t.TariffaOraria).toFixed(2);
            tariffa_totale = parseFloat(t.tariffaEffettivaTotale).toFixed(2);
            costo_risorsa_fixed = parseFloat(t.costoRisorsaTotale);
            margin = Number(tariffa_totale) - costo_risorsa_fixed;
        } else {
            tariffa_oraria = ((hourly_rate * t.duration) / 60).toFixed(2);
            costo_risorsa_fixed = (t.costo_risorsa * t.duration) / 60;
            margin = Number(tariffa_totale) - costo_risorsa_fixed;
        }

        taskData = {
            type: "task",
            id: t.id,
            task_id: t.id,
            date: t.date,
            started_at: t.started_at,
            duration: t.duration,
            nomeutente: t.nomeutente,
            task: t.task,
            tariffa_oraria: `€ ${tariffa_oraria}` || "-",
            tariffa_totale: tariffa_totale || "-",
            costo_orario: t.CostoOrario || "-",
            costo_risorsa: costo_risorsa_fixed
                ? `€ ${costo_risorsa_fixed}`
                : "-",
            margin: margin.toFixed(2) || "-",
            progressivo: t.progressivo || "-",
            processed: t.processed || false,
            visible: t.visible || false,
            modifiable: t.modifiable || false,
            hourly_rate,
            status: t.status,
        };

        resultArray.push(taskData);
    });

    return resultArray;
}

export const mapDataForCustomTable = (response: any) => {
    const combinedData = createTimesheetData(response);
    const totalData = mapTotalValues(response);
    const totalRow = {
        type: "total",
        id: "total",
        date: "Totale",
        started_at: "",
        duration: totalData.totalDuration,
        nomeutente: "",
        task: "",
        tariffa_oraria: `€${totalData.total_tariffa_oraria.toFixed(2)}`,
        tariffa_totale: `€${totalData.total_tariffa_totale.toFixed(2)}` || "-",
        costo_orario: `€${totalData.total_costo_orario.toFixed(2)}` || "-",
        costo_risorsa: `€${totalData.total_costo_risorsa.toFixed(2)}` || "-",
        margin: `€${totalData.total_margin.toFixed(2)}` || "-" || "-",
        progressivo: "",
        processed: "",
        visible: "",
        modifiable: "",
    };

    if (combinedData.length > 0) {
        combinedData.push(totalRow);
    }

    return combinedData;
};

function formatNumber(numberString: string): string {
    const number = parseFloat(numberString.replace(",", "."));
    if (isNaN(number)) {
        return "";
    }

    const [integerPart, decimalPart] = number.toFixed(2).split(".");
    const formattedIntegerPart = integerPart.replace(
        /\B(?=(\d{3})+(?!\d))/g,
        "."
    );

    return `${formattedIntegerPart},${decimalPart}`;
}

export const totalValuesArray = [
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
];

export const convertMinutesToHours = (row: any) => {
    if (row.includes(":")) {
        return row;
    }
    const hours = Math.floor(row / 60);
    const remainingMinutes = row % 60;
    return `${pad(hours)}:${pad(remainingMinutes)}`;
};

export const mapTotalValueArray = (selectedValues: any[], totals: any) => {
    const newValues =
        selectedValues.length === 1
            ? [
                  "Totali (€)",
                  "",
                  formatNumber(totals.totale),
                  formatNumber(totals.scoperto),
                  formatNumber(totals.importofatturato),
              ]
            : [
                  "Per visualizzare i Totali filtra per Tipologia",
                  "",
                  "",
                  "",
                  "",
              ];

    const lastIndex = totalValuesArray.length - 1;

    const valuesWithTotalFields = [
        ...totalValuesArray.slice(0, lastIndex - newValues.length + 1),
        ...newValues,
    ];

    return valuesWithTotalFields;
};

interface Task {
    file_id: string;
    codicearchivio?: string;
    setting: number;
    hourly_rate: number;
    duration: string;
    tariffaEffettivaTotale?: string;
    costoRisorsaTotale?: string;
    TariffaOraria: string;
    CostoOrario: string;
    task: string;
    costo_risorsa: number;
}

interface File {
    hourly_rate: number;
}

interface Data {
    tasks: Task[];
    files: { [key: string]: File };
}

export const mapTotalValues = (data: Data): any => {
    const { tasks, files } = data;

    let file: string | null = null;
    let show_codice: number | null = null;
    let totalDuration = 0;
    let total_hourly_rate = 0;
    let total_costo_orario = 0;
    let total_costo_risorsa = 0;
    let total_tariffa_oraria = 0.0;
    let total_tariffa_totale = 0.0;
    let total_margin = 0;

    for (let i = 0; i < tasks.length; i++) {
        const t = tasks[i];

        let hourly_rate = t.hourly_rate;

        if (file !== t.file_id) {
            file = t.file_id;
            show_codice = t.setting;
        }

        if (file && files[file] && files[file].hourly_rate > 0) {
            hourly_rate = files[file].hourly_rate;
        }

        let tariffa_oraria = 0.0;
        let tariffa_totale = 0.0;
        let costo_orario = 0.0;
        let costo_risorsa_fixed = 0;
        let margin = 0;

        if (
            t.tariffaEffettivaTotale &&
            t.costoRisorsaTotale &&
            t.tariffaEffettivaTotale !== null &&
            t.costoRisorsaTotale !== null
        ) {
            tariffa_oraria = parseFloat(t.TariffaOraria);
            tariffa_totale = parseFloat(t.tariffaEffettivaTotale);
            costo_orario = parseFloat(t.CostoOrario);
            costo_risorsa_fixed = parseFloat(t.costoRisorsaTotale);
            margin = tariffa_totale - costo_risorsa_fixed;
        } else {
            // Retrocompatibilità
            tariffa_oraria = (hourly_rate * parseInt(t.duration, 10)) / 60;
            costo_risorsa_fixed =
                (t.costo_risorsa * parseInt(t.duration, 10)) / 60;
            margin = tariffa_oraria - costo_risorsa_fixed;
        }

        totalDuration += parseInt(t.duration, 10);
        if (show_codice) {
            total_tariffa_oraria += tariffa_oraria;
            total_tariffa_totale += tariffa_totale;
            total_costo_risorsa += costo_risorsa_fixed;
            total_costo_orario += costo_orario;
            total_margin += margin;
        }
    }

    if (show_codice) {
        total_hourly_rate = parseFloat(total_hourly_rate.toFixed(2));
        total_tariffa_oraria = parseFloat(total_tariffa_oraria.toFixed(2));
        total_tariffa_totale = parseFloat(total_tariffa_totale.toFixed(2));
        total_costo_orario = parseFloat(total_costo_orario.toFixed(2));
        total_costo_risorsa = parseFloat(total_costo_risorsa.toFixed(2));
        total_margin = parseFloat(total_margin.toFixed(2));
    } else {
        total_hourly_rate = 0;
        total_tariffa_oraria = 0;
        total_tariffa_totale = 0;
        total_costo_orario = 0;
        total_costo_risorsa = 0;
        total_margin = 0;
    }

    const hours = Math.floor(totalDuration / 60);
    const remainingMinutes = totalDuration % 60;
    const totalData = {
        totalDuration: `${hours}:${
            remainingMinutes > 10 ? remainingMinutes : "0" + remainingMinutes
        }`,
        total_hourly_rate,
        total_tariffa_oraria,
        total_tariffa_totale,
        total_costo_orario,
        total_costo_risorsa,
        total_margin,
    };

    return totalData;
};
