import React, { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useNavigate } from "react-router-dom";
import PageTitle from "../../../custom-components/PageTitle";
import { Tab, Tabs } from "@vapor/react-extended";
import Create<PERSON>ttivita from "./sections/CreateAttivita";
import { Box, Button } from "@vapor/react-material";
import CreateAnnotazioni from "./sections/CreateAnnotazioni";
import { useTranslation } from "@1f/react-sdk";
import { useTimesheetFormHook } from "./hooks/useTimesheetFormHook";
import { useTimesheetDetailHook } from "./hooks/useTimesheetDetailsHook";
import CreateDatiAggiuntivi from "./sections/CreateDatiAggiuntivi";
import { useParams } from "react-router-dom";
import usePostCustom from "../../../hooks/usePostCustom";
import useGetCustom from "../../../hooks/useGetCustom";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { useUser } from "../../../store/UserStore";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { a11yProps } from "./utils";
import { useLocation } from "react-router-dom";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );
}

const TimesheetDetails = () => {
    const [value, setValue] = React.useState(0);
    const [disableStartTimer, setDisableStartTimer] = React.useState(false);
    const { t } = useTranslation();
    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
    const navigate = useNavigate();
    const params = useParams<{ id: string }>();
    const { method, onSubmit, iniziaAttivita } = useTimesheetFormHook();
    const deleteTimeSheetReq = usePostCustom(
        "default/timesheet/delete?noTemplateVars=true"
    );

    const location = useLocation();
    const { items } = location.state || {};

    const { user, modules, getUser }: any = useUser();
    const userDataRequest = useGetCustom("login/getloggeduser", {});
    const { state, getData } = useTimesheetDetailHook();
    const { taskData } = state || {};

    React.useEffect(() => {
        if (modules && modules.getActivetimesheet() === 0) {
            setDisableStartTimer(false);
        } else if (modules && modules.getActivetimesheet() !== 0) {
            setDisableStartTimer(true);
        }
        getUser(userDataRequest);
    }, []);

    const isTimerSet = localStorage.getItem("isTimerSet");

    React.useEffect(() => {
        if (isTimerSet) {
            getUser(userDataRequest);
            setDisableStartTimer(true);
        } else {
            setDisableStartTimer(false);
        }
    }, [isTimerSet]);

    const { handleSubmit, getValues } = method;

    const deleteTimeSheet = async () => {
        if (params.id) {
            const formData = new FormData();
            formData.append("id", params.id);
            const response: any = await deleteTimeSheetReq.doFetch(
                true,
                formData
            );
            if (response.data === 1) {
                return navigate("/timesheet/timesheet");
            }
        }
    };

    const toggleDeleteRow = async () => {
        setOpenDeleteConfirmation(true);
    };

    const closeDeleteConfirmation = async () => {
        setOpenDeleteConfirmation(false);
    };

    const values = getValues();
    return (
        <VaporPage>
            {openDeleteConfirmation && (
                <ConfirmModal
                    open={openDeleteConfirmation}
                    handleDecline={closeDeleteConfirmation}
                    handleAgree={deleteTimeSheet}
                    decline={t("Annulla")}
                    agree={t("Elimina")}
                    confirmText={t("Eliminare definitivamente l'attività?")}
                    title={t("Conferma eliminazione")}
                    dividerVariant="alert"
                    colorConfirmButton="error"
                />
            )}
            <PageTitle
                title={
                    params.id ? t("Timesheet Update") : t("Timesheet Create")
                }
                showBackButton={true}
                pathToPrevPage={`/timesheet/timesheet/`}
            ></PageTitle>
            <VaporPage.Section>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label={t("Attività")} {...a11yProps(0)} />
                    {user && user.getConfigs() && (
                        <Tab label={t("Dati aggiuntivi")} {...a11yProps(1)} />
                    )}

                    <Tab label={t("Annotazioni")} {...a11yProps(2)} />
                </Tabs>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <CustomTabPanel value={value} index={0}>
                        <CreateAttivita method={method} state={state} />
                    </CustomTabPanel>
                    <CustomTabPanel value={value} index={1}>
                        <CreateDatiAggiuntivi
                            method={method}
                            id={params.id}
                            state={state}
                            items={items}
                            getData={getData}
                        />
                    </CustomTabPanel>
                    <CustomTabPanel value={value} index={2}>
                        <CreateAnnotazioni method={method} />
                    </CustomTabPanel>

                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 200,
                            },
                        }}
                    >
                        <Button
                            type="button"
                            variant="outlined"
                            onClick={() => navigate("/timesheet/timesheet")}
                            sx={{ mr: 1 }}
                        >
                            {t("Annulla")}
                        </Button>
                        {modules &&
                            modules.canAccesPracticeButtonTimesheet(user) && (
                                <Button
                                    type="button"
                                    variant="outlined"
                                    onClick={() =>
                                        navigate(
                                            `/legacy/archive/summary?uid=${taskData?.file?.uniqueid}`
                                        )
                                    }
                                    sx={{ mr: 1 }}
                                >
                                    {t("Vai alla pratica")}
                                </Button>
                            )}
                        {params.id && (
                            <Button
                                type="button"
                                variant="outlined"
                                color="error"
                                onClick={() => toggleDeleteRow()}
                                sx={{ mr: 1 }}
                            >
                                {t("Elimina")}
                            </Button>
                        )}

                        <Button
                            type="button"
                            variant="contained"
                            sx={{ mr: 1 }}
                            startIcon={<AccessTimeIcon />}
                            disabled={disableStartTimer}
                            onClick={() => iniziaAttivita(values)}
                        >
                            {params.id
                                ? t(" Riprendi attività")
                                : t("Inizia Attivitta")}
                        </Button>
                        <Button
                            type="submit"
                            variant="contained"
                            sx={{ mr: 1 }}
                        >
                            {t("Conferma")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default TimesheetDetails;
