export interface IDeadlineQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    oldDeadlines: string;
    startDate: string;
    endDate: string;
    person: string;
    deadlinesTypeSearch: string;
    deadlinesCategorySearch: string;
    object: string;
    UdienzeRitardo: string;
    processed: string;
    notProcessed: string;
    close: boolean;
    isArchived: boolean;
    poliswebFilter: boolean;
    importantOnly: boolean;
}


export interface IDefaultUpdateDeadlineObj {
  deadline_uid: string | string[];
  oggetto: string;
  data: string;
  ora: string;
  minuti: string;
}

