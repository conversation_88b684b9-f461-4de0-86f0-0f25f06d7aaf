import {
  IGridColumn,
  IGridSettings,
} from "../../../../interfaces/general.interfaces";
import { mapOtherList } from "../../../../utilities/common";
import bulletGreen from "../../../../assets/images/bulletGreen.png";
import bulletRed from "../../../../assets/images/bulletRed.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationCircle } from "@fortawesome/free-solid-svg-icons";

const displayingStatusDeadlines = (params: any, t?: any) => {
  const status = params.row?.status;
  if (status === "1") {
    return (
      <>
        {t("Evaso")}
        <img
          src={bulletGreen}
          alt="Evaso"
          style={{ width: 16, height: 16, marginLeft: 5 }}
        />
      </>
    );
  }
  return (
    <>
      {t("Non evaso")}
      <img
        src={bulletRed}
        alt="Non evaso"
        style={{ width: 16, height: 16, marginLeft: 5 }}
      />
    </>
  );
};

export const mapDeadlinesColumnNames = (
  response: any,
  t?: any
): IGridColumn[] => {
  const {
    column_names,
    column_keys,
    sortable,
    column_widths,
  }: IGridSettings = mapOtherList(response);

  const columns = column_keys
    .map((key, idx) => {
      const width = column_widths?.[idx];
      if (!key || !width || width === "0%") return undefined;

      const col: any = {
        field: key,
        headerName: column_names[idx],
        flex: parseInt(width, 10) / 100,
        sortable: sortable[idx],
        hideable: true,
      };

      if (key === "testo") {
        col.valueGetter = (params: any) => params.row?.testo ?? "";
        col.renderCell = (params: any) => (
          <div style={{ display: "flex", alignItems: "center" }}>
            {params.row?.important === "1" && (
              <FontAwesomeIcon
                icon={faExclamationCircle}
                style={{ color: "#d32f2f", marginRight: 8 }}
              />
            )}
            <span>{params.value}</span>
          </div>
        );
      } else if (key === "status") {
        col.renderCell = (params: any) => displayingStatusDeadlines(params, t);
      }

      return col;
    })
    .filter((c): c is IGridColumn => !!c);

  return columns;
};