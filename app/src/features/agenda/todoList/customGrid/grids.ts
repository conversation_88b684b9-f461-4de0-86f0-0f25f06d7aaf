import BaseGridList from "../../../../models/BaseGridList";
import { IGridColumn } from "../../../../interfaces/general.interfaces";
import { mapDeadlinesColumnNames } from "./mapColumns";

export const getDeadlinesGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Clienti"),
                t("Controparti"),
                t("Oggetto"),
                t("Data ora"),
                t("Categoria"),
                t("Autorita"),
                t("Intestatari"),
                t("Stato"),
            ],
            column_keys: [
                "listaclienti",
                "listacontroparti",
                "testo",
                "data",
                "categoria",
                "autorita",
                "users",
                "status",
            ],
            column_widths: [
                "10%",
                "10%",
                "20%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
            ],
            // cell_templates: [null, null],
            sortable: [true, true, true, true, true, true, true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDeadlinesColumnNames(response, t);
};