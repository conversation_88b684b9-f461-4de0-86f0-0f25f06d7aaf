import useGetCustom from "../../../../hooks/useGetCustom";

export default function usePrintImpegno() {
    const printRequest = useGetCustom(
        "deadlines/printbysearch",
        {},
        null,
        true
    );
    const exportCSVRequest = useGetCustom(
        "deadlines/exportcsv?noTemplateVars=true",
        {},
        null,
        true
    );

    const cleanQuery = (query: any) => {
        const defaultsToRemove: any = {
            page: 0,
            pageSize: 10,
            sortColumn: "data",
            sortOrder: "desc",
        };

        return Object.fromEntries(
            Object.entries(query).filter(
                ([key, value]) =>
                    value !== false && defaultsToRemove[key] !== value
            )
        );
    };

    const handlePrintRequest = async (params: any) => {
        const cleanedQuery = cleanQuery(params);
        if (cleanedQuery) {
            const response: any = await printRequest.doFetch(
                true,
                cleanedQuery
            );
            const blob = new Blob([response.data], { type: "text/pdf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Impegni.pdf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            return true;
        }
    };

    const handleExportCSVRequest = async (params: any) => {
        const cleanedQuery = cleanQuery(params);
        if (cleanedQuery) {
            const response: any = await exportCSVRequest.doFetch(
                true,
                cleanedQuery
            );
            const blob = new Blob([response.data], { type: "text/csv" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Gestione_impegni.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    return { handlePrintRequest, handleExportCSVRequest };
}
