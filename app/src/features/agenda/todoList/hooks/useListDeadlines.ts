import useGetCustom from "../../../../hooks/useGetCustom";
import { getDeadlinesGrid } from "../customGrid/grids";
import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { IList } from "../../../../interfaces/general.interfaces";
import { LAST_YEAR_START_DATE_FORMATTED, CURRENT_DATE_END_FORMATTED } from "../../generalCalendar/addCalendar/impegno/constants/constant";

import { IDeadlineQuery } from "../interfaces/interfaces";

const INITIAL_QUERY: IDeadlineQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "data",
    sortOrder: "desc",
    oldDeadlines: "1",
    startDate: LAST_YEAR_START_DATE_FORMATTED(),
    endDate: CURRENT_DATE_END_FORMATTED(),
    person: "1",
    deadlinesTypeSearch: "-1",
    deadlinesCategorySearch: "-1",
    object: "",
    UdienzeRitardo: "1",
    processed: "-1",
    notProcessed: "0",
    close: true,
    isArchived: false,
    poliswebFilter: false,
    importantOnly: false,
};

export default function useListDeadlines() {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const listDeadlinesRequest = useGetCustom(
        "deadlines/list?noTemplateVars=true"
    );

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [columnsLoaded, setColumnsLoaded] = useState(false);

    // Initialize query with URL parameters if available
    const getInitialQuery = (): IDeadlineQuery => {
        const urlOldDeadlines = searchParams.get('oldDeadlines');
        const urlStartDate = searchParams.get('startDate');
        const urlEndDate = searchParams.get('endDate');
        const urlIntestratario = searchParams.get('intestatario');

        return {
            ...INITIAL_QUERY,
            oldDeadlines: urlOldDeadlines || INITIAL_QUERY.oldDeadlines,
            startDate: urlStartDate || INITIAL_QUERY.startDate,
            endDate: urlEndDate || INITIAL_QUERY.endDate,
            person: urlIntestratario || INITIAL_QUERY.person,
            // Set processed to "0" (Non evaso) when oldDeadlines=true
            processed: urlOldDeadlines === 'true' ? "0" : INITIAL_QUERY.processed,
        };
    };

    const [deadlineQuery, setDeadlineQuery] = useState<IDeadlineQuery>(getInitialQuery());

    const cleanQuery = (query: any) =>
        Object.fromEntries(
            Object.entries(query).filter(([_, value]) => value !== false)
        );

    const fetchData = async () => {
        const cleanedQuery = cleanQuery(deadlineQuery);

        const response: any = await listDeadlinesRequest.doFetch(true, cleanedQuery);
        const { currentPage, totalRows } = response.data;

        setList(prev => ({
            ...prev, // Keep existing columns
            rows: currentPage,
            totalRows: parseInt(totalRows),
            page: deadlineQuery?.page,
            pageSize: deadlineQuery?.pageSize,
        }));
    };

    useEffect(() => {
        const loadColumns = async () => {
            try {
                const columns = await getDeadlinesGrid(t);
                setList(prev => ({
                    ...prev,
                    columns: columns
                }));
                setColumnsLoaded(true);
            } catch (error) {
                console.error("Error loading columns:", error);
                setColumnsLoaded(true);
            }
        };

        if (!columnsLoaded) {
            loadColumns();
        }
    }, [t, columnsLoaded]);

    useEffect(() => {
        fetchData();
    }, [deadlineQuery, t]);

    return {
        deadlineQuery,
        setDeadlineQuery,
        list,
        loading: listDeadlinesRequest.loading,
        INITIAL_QUERY,
        fetchData,
    };
}
