import usePostCustom from "../../../../hooks/usePostCustom";
import { useState } from "react";
import { CURRENT_DATE_FORMATTED } from "../../generalCalendar/addCalendar/impegno/constants/constant";
import { IDefaultUpdateDeadlineObj } from "../interfaces/interfaces";

const DEFAULT_PAYLOAD: IDefaultUpdateDeadlineObj = {
    deadline_uid: "",
    oggetto: "",
    data: CURRENT_DATE_FORMATTED(),
    ora: "09",
    minuti: "00",
};

export default function useUpdateDeadlineObj() {
    const [updateDeadlineParams, setUpdateDeadlineParams] =
        useState<IDefaultUpdateDeadlineObj>(DEFAULT_PAYLOAD);
    const [disableModifyButton, setDisableModifyButton] =
        useState<boolean>(true);

    const UPDATE_DEADLINE_OBJECT_REQUEST = usePostCustom(
        "deadlines/updatedeadlineobj?noTemplateVars=true"
    );

    const handleUpdateDeadlineObject = async (params: any) => {
        const response: any = await UPDATE_DEADLINE_OBJECT_REQUEST.doFetch(
            true,
            params
        );
        if (response.data) {
            setDisableModifyButton(true);
            setUpdateDeadlineParams(DEFAULT_PAYLOAD);
        }
    };

    return {
        updateDeadlineParams,
        setUpdateDeadlineParams,
        handleUpdateDeadlineObject,
        disableModifyButton,
        setDisableModifyButton,
    };
}
