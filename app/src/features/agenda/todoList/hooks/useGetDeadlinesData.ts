import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export default function useGetDeadlinesData() {
    const getDeadlinesRequest = useGetCustom("deadlines/deadlines");
    const [deadlineData, setDeadlineData] = useState<any>([]);

    const fetchData = async () => {
        const response: any = await getDeadlinesRequest.doFetch(true);
        setDeadlineData(response.data);
    };

    useEffect(() => {
        fetchData();
    }, []);

    return { deadlineData };
}