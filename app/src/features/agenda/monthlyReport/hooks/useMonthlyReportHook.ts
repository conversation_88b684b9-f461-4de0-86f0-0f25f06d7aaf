import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";

export const useMonthlyReportHook = () => {
  const { t } = useTranslation();
  const [people, setPeople] = useState<any[]>([]);

  const monthlyReportRequest = useGetCustom("calendar/monthlyreport/");

  useEffect(() => {
    async function initMonthlyReport() {
      try {
        const response: any = await monthlyReportRequest.doFetch();
        setPeople(response.data.people);
      } catch (error) {
        console.log("Report Mensile Timesheet error", error);
        return;
      }
    }

    initMonthlyReport();
  }, []);

  return {
    t,
    people,
  };
};
