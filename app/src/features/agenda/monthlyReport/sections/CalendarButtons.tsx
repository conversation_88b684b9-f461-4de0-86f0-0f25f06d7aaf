import React from "react";
import { <PERSON>, Typo<PERSON>, Button } from "@vapor/react-material";
import TodayIcon from "@mui/icons-material/Today";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import {
  ICalendarButtonsProps,
  IQuery,
} from "../../generalCalendar/typings/generalCalendar.interface";
import { gettingCalendarViewName } from "../../generalCalendar/helpers/gettingCalendarViewName";

const updateCalendar = (
  calendarAPI: any,
  query: IQuery,
  setQuery: React.Dispatch<React.SetStateAction<IQuery>>,
  setMonthTitle: React.Dispatch<React.SetStateAction<string | undefined>>,
  t: any,

  calendarViewName: string
) => {
  const currentMonthTitle = calendarAPI.view.title;
  setMonthTitle(t(currentMonthTitle));
  const newStart =
    calendarAPI.currentDataManager.state.dateProfile.activeRange.start.getTime() /
    1000;
  const newEnd =
    calendarAPI.currentDataManager.state.dateProfile.activeRange.end.getTime() /
    1000;
  const newDate = calendarAPI.getDate().getTime() / 1000;
  const updatedQuery = {
    ...query,
    start: newStart,
    end: newEnd,
    date: newDate,
    viewName: calendarViewName,
  };
  setQuery(updatedQuery); // Ensure query state is updated for filters
};

export default function CalendarButtons(props: ICalendarButtonsProps) {
  const { query, setQuery, calendarRef, monthTitle, setMonthTitle, t } = props;

  const handleNavigationClick = (action: any) => {
    const calendarAPI: any = calendarRef?.current?.getApi();
    const calendarViewName = gettingCalendarViewName(
      calendarAPI.currentData.currentViewType
    );
    if (calendarAPI) {
      action(calendarAPI);
      updateCalendar(
        calendarAPI,
        query,
        setQuery,
        setMonthTitle,
        t,
        calendarViewName
      );
    }
  };

  return (
    <Box sx={{ p: 2, display: "flex", alignItems: "center" }}>
      {/* Left buttons */}
      <Box sx={{ flexGrow: 1 }}>
        <Button
          variant="outlined"
          onClick={() => handleNavigationClick((api: any) => api.prevYear())}
          sx={{ mr: 1 }}
        >
          <KeyboardDoubleArrowLeftIcon />
        </Button>
        <Button
          variant="outlined"
          onClick={() => handleNavigationClick((api: any) => api.prev())}
          sx={{ mr: 1 }}
        >
          <KeyboardArrowLeftIcon />
        </Button>
        <Button
          variant="outlined"
          onClick={() => handleNavigationClick((api: any) => api.next())}
          sx={{ mr: 1 }}
        >
          <KeyboardArrowRightIcon />
        </Button>
        <Button
          variant="outlined"
          onClick={() => handleNavigationClick((api: any) => api.nextYear())}
          sx={{ mr: 1 }}
        >
          <KeyboardDoubleArrowRightIcon />
        </Button>
        <Button
          variant="outlined"
          endIcon={<TodayIcon />}
          onClick={() => handleNavigationClick((api: any) => api.today())}
        >
          {t("Oggi")}
        </Button>
      </Box>

      {/* Center title */}
      <Typography
        variant="titleMedium"
        component="div"
        color="primary.textTitleColor"
        sx={{ flexGrow: 1, textAlign: "center" }}
      >
        {monthTitle}
      </Typography>

      <Box sx={{ flexGrow: 1, textAlign: "right" }}></Box>
    </Box>
  );
}
