import VaporPage from "@vapor/react-custom/VaporPage";
import { useEffect, useState, useRef } from "react";
import { useTranslation } from "@1f/react-sdk";
import {
    Box,
    Checkbox,
    FormControl,
    FormControlLabel,
    MenuItem,
    Select,
    Grid,
    Typography,
    LinearProgress,
    Card,
    CardContent,
} from "@vapor/react-material";
import dayGridPlugin from "@fullcalendar/daygrid"; // a plugin!
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import PageTitle from "../../../custom-components/PageTitle";
import FullCalendar from "@fullcalendar/react";
import { useMonthlyReportHook } from "./hooks/useMonthlyReportHook";
import useGetCustom from "../../../hooks/useGetCustom";
import CalendarCustomButtons from "../../../custom-components/CalendarCustomButtons";
import { IQueryMonthlyReport } from "../generalCalendar/typings/generalCalendar.interface";

const DEFAULT_QUERY: IQueryMonthlyReport = {
    currentDate: "0",
    id: "",
    viewName: "month",
    dailyWorkload: "",
    noTemplateVars: true,
};

const MonthlyReportIndex = () => {
    const { t } = useTranslation();
    const [calendarPersonValue, setCalendarPersonValue] =
        useState<string>("13");
    const [calendarWeekendsValue, setCalendarWeekendsValue] =
        useState<boolean>(false);

    const { people } = useMonthlyReportHook();
    const [monthTitle, setMonthTitle] = useState<string | undefined>("");
    const [dailyWorkload, setDailyWorkload] = useState("");

    const [currentDate, setCurrentDate] = useState<number>(1609023600000);

    const [calendarData, setCalendarData] = useState([]);
    const [query, setQuery] = useState<any>(DEFAULT_QUERY);
    const [totalHours, setTotalHours] = useState<{
        totalMonthWorkload: number;
        sumWeekWorkload: any;
    }>({
        totalMonthWorkload: 0,
        sumWeekWorkload: {},
    });

    const calendarRef = useRef<FullCalendar>(null);

    const getDailyWorkload = useGetCustom(
        `users/get-daily-workload?id=${calendarPersonValue}&noTemplateVars=true`
    );
    const getMonthWorkload = useGetCustom(
        `/timesheet/get-month-workload?currentDate=${currentDate}&id=${calendarPersonValue}&dailyWorkload=${dailyWorkload}&noTemplateVars=true`
    );

    useEffect(() => {
        getCalendarData();
    }, [currentDate, dailyWorkload, calendarPersonValue]);

    const onChangePerson = (e: any) => {
        const { value } = e.target;
        setCalendarPersonValue(value);
    };

    useEffect(() => {
        getDailyData();
    }, []);

    const getDailyData = async () => {
        const dailyData: any = await getDailyWorkload.doFetch(true);
        setDailyWorkload(dailyData.data);
    };

    const getCalendarData = async () => {
        if (dailyWorkload) {
            const monthlyData: any = await getMonthWorkload.doFetch(true);

            const transformedEvents = monthlyData.data.map((event: any) => {
                const [year, month, day] = event.date.split("-");
                const formattedDate = `${year}-${month.padStart(
                    2,
                    "0"
                )}-${day.padStart(2, "0")}`;

                return {
                    title: event.title || `Work Done: ${event.workDone} mins`,
                    start: formattedDate,
                    extendedProps: {
                        workDone: event.workDone,
                        percentage: event.percentage,
                        weekOfMonth: event.weekOfMonth,
                        dayOfWeek: event.dayOfWeek,
                    },
                };
            });

            setTotalHours(calculateSums(monthlyData.data, currentDate, false));
            setCalendarData(transformedEvents);
        }
    };

    const onChangeCheckbox = (e: any) => {
        const { checked } = e.target;
        setCalendarWeekendsValue(checked || "");
    };

    const convertMinutesToHours = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours} h ${mins.toString().padStart(2, "0")} m`;
    };

    const handleDatesSet = (arg: any) => {
        const { start } = arg;

        const firstDayOfMonth = new Date(
            Date.UTC(start.getFullYear(), start.getMonth() + 1, 1)
        );

        const startMillis = firstDayOfMonth.getTime() - 1 * 60 * 60 * 1000;
        setCurrentDate(startMillis);

        const calendarAPI: any = calendarRef?.current?.getApi();
        const currentMonthTitle = calendarAPI?.view.title;

        const formattedTitle =
            currentMonthTitle?.charAt(0).toUpperCase() +
            currentMonthTitle?.slice(1);

        setMonthTitle(
            `${formattedTitle} (${convertMinutesToHours(
                totalHours.totalMonthWorkload
            )})`
        );
    };

    const calculateSums = (
        events: any[],
        calendarDate: number,
        weekends: boolean = false
    ) => {
        let weeks = 0;
        let sumWeekWorkload: any = {};
        sumWeekWorkload[weeks] = 0;

        let currentMonth = new Date(calendarDate).getMonth();
        let currentYear = new Date(calendarDate).getFullYear();

        let firstDay = new Date(currentYear, currentMonth, 1);
        let lastDay = new Date(firstDay.setMonth(firstDay.getMonth() + 1));
        lastDay = new Date(lastDay.setDate(firstDay.getDate() - 1));

        let totalMonthWorkload = events.reduce(function (acc, item) {
            let currentDay = new Date(item.date).getDay();
            if ((currentDay !== 0 && currentDay !== 6) || weekends) {
                acc += parseInt(item.workDone);
            }
            return acc;
        }, 0);

        for (var i = 1; i <= lastDay.getDate(); i++) {
            let currentDay = new Date(
                currentYear + "-" + (currentMonth + 1) + "-" + i
            ).getDay();

            let eventFound = events.filter(function (item) {
                if (
                    item.date ===
                    currentYear + "-" + (currentMonth + 1) + "-" + i
                ) {
                    return true;
                }
            });

            if (currentDay === 0) {
                weeks++;
                sumWeekWorkload[weeks] = 0;
            } else {
                if ((currentDay !== 0 && currentDay !== 6) || weekends) {
                    sumWeekWorkload[weeks] +=
                        eventFound.length > 0
                            ? parseInt(eventFound[0].workDone)
                            : 0;
                }
            }
        }
        const calendarAPI: any = calendarRef?.current?.getApi();
        const currentDate = calendarAPI?.getDate();
        const fullMonthName = currentDate?.toLocaleDateString('it-IT', {
            month: 'long',
            year: 'numeric'
        });
        const capitalizedMonthName = fullMonthName?.charAt(0).toUpperCase() + fullMonthName?.slice(1);
        setMonthTitle(
            `${capitalizedMonthName} (${convertMinutesToHours(
                totalMonthWorkload
            )})`
        );

        return { totalMonthWorkload, sumWeekWorkload };
    };

    const renderEventContent = (eventInfo: any) => {
        const { _def } = eventInfo.event;
        const { extendedProps } = _def;

        return (
            <Grid
                container
                spacing={2}
                sx={{
                    "&&&&&&.fc-h-event": {
                        backgroundColor: "lighblue",
                        border: "none",
                    },
                }}
            >
                <Grid item xs={12}>
                    <LinearProgress
                        variant="determinate"
                        color="error"
                        value={
                            Number(extendedProps.percentage) > 100
                                ? 100
                                : Number(extendedProps.percentage)
                        }
                        sx={{ mb: 2 }}
                    />
                    <Typography variant="caption" sx={{ mr: 0.5 }}>
                        {convertMinutesToHours(Number(extendedProps.workDone))}
                    </Typography>
                    <Typography variant="caption">{t("Totali")}</Typography>
                </Grid>
            </Grid>
        );
    };

    const customItalianLocale = {
        code: "it",
        week: {
            dow: 1,
        },
        buttonText: {
            today: "Oggi",
            month: "Mese",
            week: "Settimana",
            day: "Giorno",
        },
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("REPORT MENSILE TIMESHEET")}
                showBackButton={false}
            ></PageTitle>

            <VaporPage.Section>
                <Box
                    display="flex"
                    alignItems="end"
                    gap={2}
                    style={{ marginTop: "7px" }}
                >
                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <Select
                            labelId="select-label"
                            value={calendarPersonValue}
                            onChange={onChangePerson}
                            name="calendarPerson"
                        >
                            {people?.map((people: any) => (
                                <MenuItem key={people.id} value={people.id}>
                                    {people.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControlLabel
                        sx={{ width: 1 / 4 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="calendarWeekends"
                                checked={!!calendarWeekendsValue}
                            />
                        }
                        label={t("Mostra eventi weekend")}
                    />
                </Box>
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container>
                    <Grid item xs={9}>
                        <CalendarCustomButtons
                            query={query}
                            setQuery={setQuery}
                            calendarRef={calendarRef}
                            DEFAULT_QUERY={DEFAULT_QUERY}
                            monthTitle={monthTitle}
                            setMonthTitle={setMonthTitle}
                            t={t}
                            selectedView="dayGridMonth"
                            onViewChange={() => {}}
                            leftPanelOpen={false}
                            setLeftPanelOpen={() => {}}
                            rightPanelOpen={false}
                            setRightPanelOpen={() => {}}
                        />
                        <FullCalendar
                            initialView="dayGridMonth"
                            plugins={[
                                dayGridPlugin,
                                timeGridPlugin,
                                interactionPlugin,
                                listPlugin,
                            ]}
                            height="auto"
                            handleWindowResize={true}
                            allDaySlot={false}
                            ref={calendarRef}
                            events={calendarData}
                            eventContent={renderEventContent}
                            headerToolbar={false}
                            eventBackgroundColor="#ffffff"
                            datesSet={handleDatesSet}
                            locales={[customItalianLocale]}
                            locale="it"
                            // Fix calendar layout issues
                            fixedWeekCount={false}
                            showNonCurrentDates={true}
                            aspectRatio={1.35}
                            dayMaxEvents={false}
                            moreLinkClick="popover"
                            views={{
                                dayGridMonth: {
                                    dayHeaderFormat: { weekday: 'long' }
                                }
                            }}
                        />
                    </Grid>
                    <Grid
                        item
                        xs={3}
                        sx={{
                            mt: "85px",
                            justifyContent: "center",
                            textAlign: "center",
                        }}
                    >
                        {Object.keys(totalHours.sumWeekWorkload).map(
                            (key: string) => {
                                return (
                                    <Grid key={key} sx={{ margin: "15px" }}>
                                        <Card sx={{ p: 2, cursor: "pointer" }}>
                                            <CardContent
                                                sx={{ color: `#5676D4` }}
                                            >
                                                <Typography
                                                    variant="titleSmall"
                                                    color="primary.error"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {convertMinutesToHours(
                                                        totalHours
                                                            .sumWeekWorkload[
                                                            key
                                                        ]
                                                    )}{" "}
                                                    <b>{t("Totali")}</b>
                                                </Typography>
                                            </CardContent>
                                        </Card>
                                    </Grid>
                                );
                            }
                        )}
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};
export default MonthlyReportIndex;
