import * as React from "react";
import { Box } from "@mui/material";
import { Tabs, Tab } from "@vapor/react-extended";
import { a11yProps, CustomTabPanel } from "../../helpers/customTabPanel";

import Tab1 from "./tabs/Tab1";
import Tab2 from "./tabs/Tab2";
import Tab3 from "./tabs/Tab3";
import Tab4 from "./tabs/Tab4";
import Tab5 from "./tabs/Tab5";
import Tab6 from "./tabs/Tab6";
import Tab7 from "./tabs/Tab7";
import Tab8 from "./tabs/Tab8";

export default function FilterTabs(props: any) {
    const [value, setValue] = React.useState(0);

    const {
        defaultQuery,
        query,
        setQuery,
        archiveData,
        filterArchiveData,
        onChangeFilterInputs,
        onChangeCheckbox,
        onDateChange,
        handleChangeMultiSelect,
        selectedValues,
        setSelectedValues,
    } = props;

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    return (
        <Box sx={{ width: "100%" }}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    aria-label="basic tabs example"
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label="Ricerca" {...a11yProps(0)} />
                    <Tab label="Ricerca avanzata (1)" {...a11yProps(1)} />
                    <Tab label="Ricerca avanzata (2)" {...a11yProps(2)} />
                    <Tab label="Ricerca per sede" {...a11yProps(3)} />
                    <Tab label="Ricerca per tag" {...a11yProps(4)} />
                    <Tab label="Ricerca per altri dati" {...a11yProps(5)} />
                    <Tab label="Ricerca per campi dinamici" {...a11yProps(6)} />
                    <Tab label="Ricerca per movimentazioni" {...a11yProps(7)} />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
                <Tab1
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Tab2
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                    handleChangeMultiSelect={handleChangeMultiSelect}
                    selectedValues={selectedValues}
                    setSelectedValues={setSelectedValues}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={2}>
                <Tab3
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={3}>
                <Tab4
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={4}>
                <Tab5
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={5}>
                <Tab6
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={6}>
                <Tab7
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={7}>
                <Tab8
                    defaultQuery={defaultQuery}
                    query={query}
                    setQuery={setQuery}
                    archiveData={archiveData}
                    filterArchiveData={filterArchiveData}
                    onChangeFilterInputs={onChangeFilterInputs}
                    onDateChange={onDateChange}
                    onChangeCheckbox={onChangeCheckbox}
                />
            </CustomTabPanel>
        </Box>
    );
}
