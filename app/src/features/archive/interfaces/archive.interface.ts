export interface IDefaultQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    codeType: number;
    code: string;
    identificatore: number;
    relazioneType: number;
    relazioneName: string;
    customer: string;
    counterpart: string;
    referent: string;
    otherSubject: string;
    descrizione: string;
    rgn: string;
    c: string;
    rga: string;
    subprocedimento: string;
    categoryId: number;
    outcome: number;
    searchTagsLike: string;
    dataArchivio: string;
    authority: number;
    tipology: number;
    status1: number;
    status: number;
    sectorStudy: number;
    lawyer: number;
    object: string;
    searchObject: string;
    archiveDominus: number;
    archivePublicProsecutors: number;
    archiveCrimes: number;
    istruttore: string;
    sede: string;
    officeSearch: number;
    searchTags: string;
    dateType: number;
    startDate: string;
    endDate: string;
    generalProtocol: string;
    situazione: number;
    situazioneContabile: number;
    centroProfitto: number;
    showAdvanceDynamic: string;
    isArchived: string;
    isWithoutLink: string;
    ns: string;
    nsa: string;
    movimentate_startDate: string;
    movimentate_endDate: string;
    contractId: string;
    relazioneTypeUTN: string;
    relazioneNameUTN: string;
    peopleCategory: string;
    sezioneFascicolo: string;
    archiveTipiSpesa: string;
    annotazioni: string;
    stanza: string;
    palchetto: string;
    scaffale: string;
    faldone: string;
    scatolone: string;
    dynamicSearchFields: string;
    movimentate_have: string;
    movimentate_type: string;
    subjects_group: string;
    campodinamicoId: string;
    valorecampo: string;
    valorecampo_to: string;
    tagsList: string | string[];
}

export interface IFilterProps {
    defaultQuery: IDefaultQuery;
    query: IDefaultQuery;
    archiveData: any;
    setQuery: React.Dispatch<React.SetStateAction<IDefaultQuery>>;
    filterArchiveData: (query: IDefaultQuery) => void;
    onChangeFilterInputs: any;
    onDateChange: any;
    onChangeCheckbox: any;
    handleChangeMultiSelect?: any;
    selectedValues?: any;
    setSelectedValues?: any;
}
