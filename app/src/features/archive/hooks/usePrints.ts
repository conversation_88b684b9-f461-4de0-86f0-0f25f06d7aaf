import useGetCustom from "../../../hooks/useGetCustom";
import { useState } from 'react';

export const usePrints = (list: any, query: any) => {
    const [showNotification, setShowNotification] = useState(false);
    const [notificationText, setNotificationText] = useState('');
    const [notificationSeverity, setNotificationSeverity] = useState<'success' | 'error' | 'warning' | 'info'>('info');

    const exportEtichettaPdfRequest = useGetCustom(
        "archive/printetichetta?noTemplateVars=true",
        {},
        null,
        true
    );

    const exportPratichePdfRequest = useGetCustom(
        "archive/print-pdf?noTemplateVars=true",
        query,
        null,
        true
    );

    const exportArchivePdfRequest = useGetCustom(
        "archive/exportcsv?noTemplateVars=true",
        query,
        null,
        true
    );

    const showToast = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
        setNotificationText(message);
        setNotificationSeverity(severity);
        setShowNotification(true);
    };

    const handleExportEtichettaPdfRequest = async (selectedGridRows: any) => {
        try {
            const response: any = await exportEtichettaPdfRequest.doFetch(
                true,
                { uniqueid: selectedGridRows[0] }
            );
            const blob = new Blob([response.data], { type: "text/pdf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Etichetta.pdf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showToast('Etichetta esportata con successo', 'success');
        } catch (e) {
            showToast('Errore durante l\'esportazione dell\'etichetta', 'error');
            console.error(e);
        }
    };

    const handleExportPratichePdfRequest = async () => {
        try {
            if (list?.rows?.length > 0) {
                var totalRows = list?.totalRows;
                if (totalRows > 700) {
                    showToast(`L'export di ${totalRows} pratiche risulterebbe troppo grande, ridurre il numero di pratiche tramite gli appositi filtri`, 'warning');
                } else {
                    const response: any = await exportPratichePdfRequest.doFetch(
                        true,
                        ""
                    );
                    const blob = new Blob([response.data], { type: "text/pdf" });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.setAttribute("download", "Gestione_Pratiche.pdf");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    showToast('Pratiche esportate con successo', 'success');
                }
            } else {
                showToast('Non è presente nessuna pratica da stampare', 'info');
            }
        } catch (e) {
            showToast('Errore durante l\'esportazione delle pratiche', 'error');
            console.error(e);
        }
    };

    const handleExportArchivePdfRequest = async (campiDinamici: any, impegniEUdienze = false, rischio = false, kpi = false, riepilogoGenerale = false) => {
        try {
            if (list?.rows?.length > 0) {
                var totalRows = list?.totalRows;
                if (totalRows > 3000 && impegniEUdienze) {
                    showToast(`L'export di ${totalRows} pratiche risulterebbe troppo grande, ridurre il numero di pratiche tramite gli appositi filtri`, 'warning');
                } else {
                    const response: any = await exportArchivePdfRequest.doFetch(
                        true,
                        {
                            campiDinamici: campiDinamici,
                            impegniEUdienze: impegniEUdienze,
                            rischio: rischio,
                            kpi: kpi,
                            riepilogoGenerale: riepilogoGenerale,
                        }
                    );
                    const blob = new Blob([response.data], { type: "text/csv" });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.href = url;
                    link.setAttribute("download", "Gestione_pratiche.csv");
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    showToast('Archivio esportato con successo', 'success');
                }
            } else {
                showToast('Non è presente nessuna pratica da stampare', 'info');
            }
        } catch (e) {
            showToast('Errore durante l\'esportazione dell\'archivio', 'error');
            console.error(e);
        }
    };

    return {
        handleExportEtichettaPdfRequest,
        handleExportPratichePdfRequest,
        handleExportArchivePdfRequest,
        showNotification,
        setShowNotification,
        notificationText,
        notificationSeverity
    };
};