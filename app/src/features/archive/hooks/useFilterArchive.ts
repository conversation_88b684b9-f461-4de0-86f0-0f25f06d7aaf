import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { getArchiveGrids } from "../../../utilities/archives/gridColumn";

const defaultQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "codicearchivio",
    sortOrder: "desc",
    codeType: -1,
    code: "",
    identificatore: -1,
    dateType: 0,
    startDate: "",
    endDate: "",
    relazioneType: 0,
    relazioneName: "",
    relazioneTypeUTN: 1,
    relazioneNameUTN: "",
    peopleCategory: 0,
    customer: "",
    counterpart: "",
    referent: "",
    descrizione: "",
    rgn: "",
    rga: "",
    subprocedimento: "",
    outcome: -1,
    dataArchivio: "",
    authority: "",
    authoritySearch: "",
    tipology: -1,
    status: -1,
    sectorStudy: -1,
    sezioneFascicolo: "",
    subjects_group: -1,
    object: "",
    searchObject: "",
    archiveDominus: -1,
    archivePublicProsecutors: -1,
    archiveCrimes: -1,
    archiveTipiSpesa: 0,
    istruttore: "",
    sede: "",
    ns: "",
    nsa: "",
    officeSearch: -1,
    searchTags: "",
    tagsList: "",
    generalProtocol: "",
    situazione: -1,
    situazioneContabile: -1,
    centroProfitto: -1,
    annotazioni: "",
    stanza: "",
    palchetto: "",
    scaffale: "",
    faldone: "",
    scatolone: "",
    categoryId: -1,
    dynamicSearchFields: "",
    movimentate_have: -1,
    movimentate_type: "prestazioni",
    movimentate_startDate: "",
    movimentate_endDate: "",
    campodinamicoId: -1,
    valorecampo: "",
    valorecampo_to: "",
};

export default function useFilterArchive(archiveData: any) {
    const archiveFilterRequest = useGetCustom(
        "archive/list?noTemplateVars=true"
    );
    const [query, setQuery] = useState<any>(defaultQuery);
    const [list, setList] = useState<any>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    useEffect(() => {
        if (archiveData.length > 0 || Object.keys(archiveData).length > 0)
            filterArchiveData(query);
    }, [query, archiveData]);

    const filterArchiveData = async (query: any) => {
        let customQuery: any = { ...query };

        if ('tagsList[]' in customQuery && customQuery['tagsList[]']) {
            customQuery.tagsList = customQuery['tagsList[]'];
            delete customQuery['tagsList[]'];
        }

        if (customQuery.tagsList && Array.isArray(customQuery.tagsList)) {
            customQuery.tagsList = customQuery.tagsList.join(',');
        }

        const [columns, response]: any = await Promise.all([
            getArchiveGrids(archiveData),
            archiveFilterRequest.doFetch(true, customQuery),
        ]);
        const { currentPage, totalRows } = response.data;

        // Preserve any existing list state and only update the necessary fields
        setList((prevList:any) => ({
            ...prevList,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: query?.page,
            pageSize: query?.pageSize,
        }));
    };

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterArchiveData,
        loading: archiveFilterRequest.loading,
    };
}
