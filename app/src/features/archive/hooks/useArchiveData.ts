import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

export default function useInternalUserData() {
    const archiveRequest = useGetCustom("archive/archive");
    const [archiveData, setArchiveData] = useState<any>([]);

    useEffect(() => {
        async function fetchData() {
            const { data }: any = await archiveRequest.doFetch(true);
            setArchiveData(data);
        }

        fetchData();
    }, []);

    return {
        archiveData,
    };
}

export function useGetAllTags(open: any) {
    const archiveRequest = useGetCustom(
        "archive/getalltags?noTemplateVars=true"
    );
    const [archiveTags, setArchiveTags] = useState<any>([]);

    const fetchTags = async () => {
        const { data }: any = await archiveRequest.doFetch(true);
        setArchiveTags(data);
    };

    useEffect(() => {
        fetchTags();
    }, [open]);

    return {
        archiveTags,
        fetchTags,
    };
}

export function useGetWizards() {
    const archiveRequest = useGetCustom("wizards/get-list?noTemplateVars=true");
    const [wizardsData, setWizardsData] = useState<any>([]);

    const defaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "title",
        sortOrder: "asc",
    };

    const fetchWizards = async () => {
        const { data }: any = await archiveRequest.doFetch(true, defaultQuery);
        const { currentPage } = data;
        setWizardsData(currentPage);
    };

    useEffect(() => {
        fetchWizards();
    }, []);

    return {
        wizardsData,
    };
}
