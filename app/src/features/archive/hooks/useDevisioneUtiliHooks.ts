import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";

export const useDevisioneUtiliHooks = () => {
    const { t } = useTranslation();
    const [list, setList] = useState<any>();
    const [baskets, setBaskets] = useState<any[]>([]);

    const devisioneUtiliRequest = useGetCustom(
        "archive-profits-distribution?fileUniqueid=EED39C7C241-8182556461D2C8E711A18"
    );

    useEffect(() => {
        async function initDevisioneUtili() {
            try {
                const response: any = await devisioneUtiliRequest.doFetch();
                const { data } = response;
                setList(data.list);
                setBaskets(data.baskets);
            } catch (error) {
                console.error("Devisione Utili error", error);
                return;
            }
        }

        initDevisioneUtili();
    }, []);

    return {
        t,
        list,
        baskets,
    };
};
