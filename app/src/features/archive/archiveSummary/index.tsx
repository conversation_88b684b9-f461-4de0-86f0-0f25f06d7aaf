import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Sidebar from "./sidebar";
import { Box, Button, Divider } from "@vapor/react-material";
import <PERSON>tiPratica from "./pages/DatiPratica";
import <PERSON>atistiche from "./pages/Statistiche";
import DevisionedegliUtili from "./pages/DevisionedegliUtili";
import Soggetti from "./pages/soggetti/Soggetti";
import Prestazioni from "./pages/prestazioni/Index";
import Udienze from "./pages/udienze/Udienze";
import {Impegni} from "./pages/Impegni";
import Documenti from "./pages/Documenti";
import EmailFascicolate from "./pages/EmailFascicolate";
import Modelli from "./pages/Modelli";
import Polisweb from "./pages/Polisweb";
import DepositoTelematico from "./pages/DepositoTelematico";
import PagamentoTelematico from "./pages/PagamentoTelematico";
import GeneraCartacea from "./pages/GeneraCartacea";
import NotificheInProprio from "./pages/NotificheInProprio";
import NotaSpeseGuidiziale from "./pages/NotaSpeseGuidiziale";
import FattureNotePreavisi from "./pages/FattureNotePreavisi";
import Movimenti from "./pages/Movimenti";
import Contratto from "./pages/Contratto";
import RecuperoCrediti from "./pages/RecuperoCrediti";
import Antiriciclaggio from "./pages/Antiriciclaggio";
import VisureBilanci from "./pages/VisureBilanci";
import Timesheet from "./pages/Timesheet";
import Messaggi from "./pages/Messaggi";
import Workflows from "./pages/Workflows";
import DepositoUNEP from "./pages/DepositoUNEP";
import Spinner from "../../../custom-components/Spinner";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { useTranslation } from "@1f/react-sdk";
import { AgendaItem, AltriDati, Campo, DeadlineItem, DocumentItem, fileHeader, PolisWebData } from "./interface/archivedetail.interface";
import useGetCustom from "../../../hooks/useGetCustom";

export interface PraticaData {
    hasLoaded: boolean;
    uiSettings: any;
    fileHeader: fileHeader;
    altriDati: AltriDati;
    poliswebData: PolisWebData;
    agendaList: AgendaItem[];
    deadlinesList: DeadlineItem[];
    documentList: DocumentItem[];
    preview_campi: Campo[];
    netlexSettings: any;
}
import { useUser } from "../../../store/UserStore";
const SELECTED_ITEM_STORAGE_KEY = "archiveSummary_selectedItem";

export default function ArchiveSummary() {
    const navigate = useNavigate();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const fileUniqueid = queryParams.get("fileUniqueid");
    const uid = queryParams.get("uid");

    const { t } = useTranslation();
    const { modules }: any = useUser();

    const divisioneUtiliPermissions = () => {
        return modules && !modules.isExternal && modules.canAccessDivisioneDegliUtili();
    };

    // Initialize selectedItem from localStorage or default to "1"
    const [selectedItem, setSelectedItem] = useState<string>(() => {
        const savedItem = localStorage.getItem(SELECTED_ITEM_STORAGE_KEY);
        return savedItem || "1";
    });
    const [fileHeaderData, setFileHeaderData] = useState<any>(null);
    useEffect(() => {
        localStorage.setItem(SELECTED_ITEM_STORAGE_KEY, selectedItem);
    }, [selectedItem]);
    useEffect(() => {
        return () => {
            localStorage.removeItem(SELECTED_ITEM_STORAGE_KEY);
        };
    }, []);

    const COMPONENT_MAP: Record<string, React.FC<any>> = {
        "1": (props) => <DatiPratica praticaData={props.fileHeaderData} />,
        "2": (_props) => <Statistiche />,
        ...(divisioneUtiliPermissions() && {
            "3": (_props) => <DevisionedegliUtili />
        }),
        "4": (_props) => <Soggetti />, // Soggetti
        "5": (props) => <Prestazioni fuid={fileUniqueid} fileHeaderData={props.fileHeaderData} />,
        "6": (props) => <Udienze id={fileUniqueid} fileHeaderData={props.fileHeaderData} />, // Udienze
        "7": (props) => <Impegni praticaData={props.fileHeaderData} />, // Impegni
        "8": (_props) => <Documenti />, // Documenti
        "9": (_props) => <EmailFascicolate />, // Email fascicolate
        "10": (_props) => <Modelli />, // Modelli
        "11": (_props) => <Polisweb />, // Polisweb
        "12": (_props) => <DepositoTelematico />, // Deposito telematico
        "13": (_props) => <PagamentoTelematico />, // Pagamento telematico
        "14": (_props) => <GeneraCartacea />, // Genera NIR cartacea
        "15": (_props) => <NotificheInProprio />, // Notifiche in proprio
        "16": (_props) => <NotaSpeseGuidiziale />, // Nota Spese guidiziale
        "17": (_props) => <FattureNotePreavisi />, // Fatture, Note e Preavvisi
        "18": (_props) => <Movimenti />, // Movimenti
        "19": (_props) => <Contratto />, // Contratto
        "20": (_props) => <RecuperoCrediti />, // Recupero crediti
        "21": (_props) => <Antiriciclaggio />, // Antiriciclaggio
        "22": (_props) => <VisureBilanci />, // Visure e bilanci
        "23": (_props) => <Timesheet />, // TimeSheet
        "24": (_props) => <Messaggi />, // Messaggi
        "25": (_props) => <Workflows />, // Workflows
        "26": (_props) => <DepositoUNEP /> // Deposito UNEP
    };

    const { doFetch, hasLoaded, data, loading } = useGetCustom("archive/summary");

    useEffect(() => {
        doFetch(true, { uid });
    }, [uid]);

    useEffect(() => {
        if (data && hasLoaded) {
            setFileHeaderData({
                hasLoaded: hasLoaded,
                uiSettings: data.uiSettings,
                fileHeader: data.fileHeader,
                altriDati: data.altriDati,
                agendaList: data.agendaList,
                deadlinesList: data.deadlinesList,
                documentList: data.documentsList,
                netlexSettings: data.netlexSettings,
                poliswebData: data.poliswebData,
                preview_campi: data.preview_campi
            });

            window.scrollTo({ top: 0, behavior: "smooth" });
        }


    }, [data, hasLoaded]);

    function handlePrevNextNavigation(uid: string) {
        if (uid) {
            navigate(`/archive/summary?uid=${uid}`);
        }
    }
    return (
        <>
            {loading ? (
                <Spinner fullPage />
            ) : (
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        height: "100vh"
                    }}
                >
                    {/* Header Row for Buttons */}
                    {fileHeaderData && fileHeaderData.fileHeader && (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                padding: 2,
                                backgroundColor: "#fff"
                            }}
                        >
                            {fileHeaderData && fileHeaderData.fileHeader?.neighbors?.previousUniqueid && (
                                <Button variant="outlined" size="small" startIcon={<ArrowBackIcon />} onClick={() => handlePrevNextNavigation(fileHeaderData.fileHeader.neighbors.previousUniqueid)}>
                                    {t("Vai alla pratica precedente")}
                                </Button>
                            )}
                            {fileHeaderData && fileHeaderData.fileHeader?.neighbors?.previousUniqueid && (
                                <Button variant="outlined" size="small" endIcon={<ArrowForwardIcon />} onClick={() => handlePrevNextNavigation(fileHeaderData.fileHeader.neighbors.nextUniqueid)}>
                                    {t("Vai alla pratica successiva")}
                                </Button>
                            )}
                        </Box>
                    )}

                    <Divider />

                    {/* Main Content */}
                    <Box sx={{ display: "flex", flexGrow: 1 }}>
                        <Sidebar selectedItem={selectedItem} setSelectedItem={setSelectedItem} />
                        <Divider orientation="vertical" flexItem />
                        <Box sx={{ flexGrow: 1, padding: 3, overflowY: "auto" }}>{COMPONENT_MAP[selectedItem] && COMPONENT_MAP[selectedItem]({ fileHeaderData })}</Box>
                    </Box>
                </Box>
            )}
        </>
    );
}
