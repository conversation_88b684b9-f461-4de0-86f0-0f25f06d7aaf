import { Box } from "@vapor/react-material";
import {
    List,
    ListItemIcon,
    ListItemButton,
    ListItemText,
} from "@vapor/react-material";
import "./style.css";
import { getListItems } from "./constant/sidebar.constant";
import { useUser } from "../../../store/UserStore";
import { useLocation, useNavigate } from "react-router-dom";
import React from "react";

interface ISidebarProps {
    selectedItem: any;
    setSelectedItem: any;
}

const Sidebar = (props: ISidebarProps) => {
    const { selectedItem, setSelectedItem } = props;

    const location = useLocation();
    const navigate = useNavigate();
    const queryParams = new URLSearchParams(location.search);

    React.useEffect(() => {
        const findItem = LIST_ITEM.find((item: any) => item.url === location.pathname);
        if(findItem){
            setSelectedItem(findItem.id);
        }
    }, [location.pathname]);

    const uid = queryParams.get("uid");
    const fileUniqueid = queryParams.get("fileUniqueid");
    const { modules }: any = useUser();
    const LIST_ITEM = getListItems(modules);

    const paramId = uid || fileUniqueid;

    const isSelected = (item: any):boolean => {
        return selectedItem === item.id || item.url === location.pathname;
    }

    return (
        <Box
            sx={{
                width: 240,
                display: "flex",
                flexDirection: "column",
            }}
        >
            <List
                sx={{
                    m: 2,
                }}
            >
                {LIST_ITEM.map((item: any, index: number) => (
                    <ListItemButton
                        key={index}
                        selected={isSelected(item)}
                        focusVisibleClassName="custom-focus-visible"
                        onClick={() => {
                            setSelectedItem(item.id);
                            navigate(`${item.url}?${item.queryType}=${paramId}`);
                        }}
                    >
                        <ListItemIcon>
                            <item.icon sx={{ color: " rgb(0, 139, 204)" }} />
                        </ListItemIcon>
                        <ListItemText primary={item.name} />
                    </ListItemButton>
                ))}
            </List>
        </Box>
    );
};

export default Sidebar;
