import { useState, useEffect } from "react";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import useGetCustom from "../../../../hooks/useGetCustom";
import Spinner from "../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import { Grid } from "@vapor/react-material";
import Header from "../archiveHeader";
import { Box, Button, Checkbox, FormControlLabel } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { getDevisioneUtiliGrid } from "../../../../utilities/archives/devisioneUtili/gridColumn";
import CreateUpdatePercentualiModal from "./CreateUpdatePercentualiModal";

export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    fileUniqueid: "",
    page: 0,
    pageSize: 10,
    sortColumn: "basket",
    sortOrder: "desc",
    activeBaskets: "",
};

export default function DevisionedegliUtili() {
    const queryParams = new URLSearchParams(location.search);
    const fileUniqueid =
        queryParams.get("fileUniqueid") || queryParams.get("uid");
    const navigate = useNavigate();
    const [defaultParams, setDefaultParams] = useState<any>({
        noTemplateVars: true,
        fileUniqueid: fileUniqueid || "",
        page: 0,
        pageSize: 10,
        sortColumn: "basket",
        sortOrder: "desc",
        activeBaskets: "on",
    });

    const [data, setData] = useState<any[]>([]);
     const [fileHeaderData, setFileHeaderData] = useState<any>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [columns, setColumns] = useState<any[]>([]);
    const [, setList] = useState<any>();
    const [baskets, setBaskets] = useState<any[]>([]);
    const [typeOfModal, setTypeOfModal] = useState<string>("create");
    const [
        openCreateUpdatePercentualiModal,
        setOpenCreateUpdatePercentualiModal,
    ] = useState(false);

    const devisioneUtiliRequest = useGetCustom(
        "archive-profits-distribution?noTempleteVars=true"
    );

    const listArchiveDivisionRequest = useGetCustom(
        "archive-profits-distribution/list",
        defaultParams
    );

    useEffect(() => {
        const getColumns = async () => {
            const gridColumns: any[] = await getDevisioneUtiliGrid(t);
            setColumns(gridColumns);
        };

        if (!columns.length) {
            getColumns();
        }
        startSearchList();
    }, [
        defaultParams.searchField,
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.activeBaskets,
    ]);

    useEffect(() => {
        async function initDevisioneUtili() {
            try {
                const response: any = await devisioneUtiliRequest.doFetch(
                    true,
                    {
                        fileUniqueid: fileUniqueid,
                    }
                );

                const { data } = response;
                setList(data.list);
                setBaskets(data.baskets);
                setFileHeaderData(data.fileHeader);
            } catch (error) {
                console.error("Devisione Utili error", error);
                return;
            }
        }

        initDevisioneUtili();
    }, []);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? listArchiveDivisionRequest.doFetch(true)
            : listArchiveDivisionRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;

        setData(currentPage);
        setTotalRows(totalRows);
    };

    const handleClear = () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
    };

    const onChangeCheckbox = (e: any) => {
        const { name, checked } = e.target;

        setDefaultParams({
            ...defaultParams,
            [name]: checked ? "on" : "",
            fileUniqueid: fileUniqueid || "",
        });
    };

    const { t } = useTranslation();

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="archiveDivision"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    listArchiveDivisionRequest.loading ||
                    listArchiveDivisionRequest.loading
                }
                onPageChangeCallback={onPageChange}
                query={defaultParams}
            />
        );
    };

    const openCreateUpdate = (type: string) => {
        setTypeOfModal(type);
        setOpenCreateUpdatePercentualiModal(!openCreateUpdatePercentualiModal);
    };
    return (
        <>
            <Box>
                {openCreateUpdatePercentualiModal && (
                    <CreateUpdatePercentualiModal
                        fileUniqueid={fileUniqueid}
                        openModal={openCreateUpdatePercentualiModal}
                        setOpenModal={setOpenCreateUpdatePercentualiModal}
                        params={defaultParams}
                        typeOfModal={typeOfModal}
                        users={data}
                        fetchAllData={() => startSearchList()}
                        baskets={baskets}
                    />
                )}
                <Header
                    title={t("Divisione degli utili della pratica")}
                    fileHeaderData={fileHeaderData}
                    buttons={[
                        <Button
                            variant="outlined"
                            color="primary"
                            onClick={() => navigate(`/archive/archive`)}
                        >
                            {t("Esci dalla pratica")}
                        </Button>,
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => openCreateUpdate("update")}
                        >
                            {t("Modifica percentuali")}
                        </Button>,
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => openCreateUpdate("create")}
                        >
                            {t("Aggiungi percentuali")}
                        </Button>,
                    ]}
                />
                <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                    sx={{ paddingBottom: "1rem", pt: 2 }}
                >
                    <Grid item md={6} sm={6} lg={6}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    onChange={onChangeCheckbox}
                                    color="primary"
                                    name="activeBaskets"
                                    checked={!!defaultParams.activeBaskets}
                                />
                            }
                            label={t("Solo basket attivi")}
                        />
                    </Grid>
                    <Grid item>
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleClear}
                        >
                            {t("Mostra tutte")}
                        </Button>
                    </Grid>
                </Box>
                {renderDataTable()}
            </Box>
        </>
    );
}
