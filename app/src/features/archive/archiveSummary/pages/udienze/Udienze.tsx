import { useState,useEffect } from "react";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { Button, Box } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import { useUdienzeHook, usePrintUdienze } from "./hooks/useUdienzeHook";
import { useNavigate } from "react-router-dom";
import ArchiveHeader from "../../archiveHeader";
import useGetCustom from "../../../../../hooks/useGetCustom";

interface QueryParams {
  fileUniqueid: string;
  page: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: 'asc' | 'desc';
}

const Udienze = ({ id }: any) => {
    const [headerData, setHeaderData] = useState<any>([]);
    const navigate = useNavigate();
    const [query, setQuery] = useState<QueryParams>({
        fileUniqueid: id,
        page: 0,
        pageSize: 10,
        sortColumn: "data_ora",
        sortOrder: "desc",
    });

  const [items, setItems] = useState<any[]>([]);

  const { t } = useTranslation();
  const { columns, list, loading } = useUdienzeHook(query);

  const onPageChangeCallback = (model: GridPaginationModel) => {
    setQuery(prev => ({ ...prev, page: model.page, pageSize: model.pageSize }));
  };

  const udienzeItemsRequest = useGetCustom("archiveagenda/agenda");

  const getItems = async () => {
    const response: any = await udienzeItemsRequest.doFetch(true, {
        fileUniqueid: id,  
    });
    setItems(response.data.items)
    setHeaderData(response.data.fileHeader)
  };

   const { handlePrintUdienze } = usePrintUdienze(id, headerData?.uniqueid);
  useEffect(() => {
    getItems()
  }, []);

  const onClickCallback = (rowId: string) => {
    navigate(`/agenda/agenda/update/${rowId}`, { 
      state: {
        origin: "archive",
        type: "update",
        prevPath: '/archiveagenda/agenda?fileUniqueid='+id,
        rowDataUrl: 'archiveagenda',
        fileUniqueid: id,
        items: items,
        fileHeaderData: headerData
      },
    });
  };

  const renderDataTable = () => (
    <CustomDataGrid
      name="archiveagenda"
      columns={columns}
      data={list?.currentPage}
      page={query.page}
      totalRows={list?.totalRows}
      pageSize={query.pageSize}
      loading={loading}
      query={query}
      setQuery={setQuery}
      onClickCallback={onClickCallback}
      onPageChangeCallback={onPageChangeCallback}
      onClickKey="uniqueid"
    />
  );


  return (
    <Box>
      <ArchiveHeader
        fileHeaderData={headerData}
        buttons={[
          <Button variant="outlined" onClick={() => navigate("/archive/archive")}>
            {t("Esci dalla pratica")}
          </Button>,
          <Button variant="outlined" onClick={() => handlePrintUdienze()}>{t("Stampa")}</Button>,
          <Button variant="contained" onClick={() => navigate(
            "/archiveagenda/agenda/create/new",
            {
              state: {
                origin: "archive",
                type: "create",
                prevPath: '/archiveagenda/agenda?fileUniqueid='+id,
                rowDataUrl: 'archiveagenda',
                fileUniqueid: id,
                items: items,
                fileHeaderData: headerData
              },
            }
          )}>
            {t(" Nuova udienza")}
          </Button>,
        ]}
        title={t("Udienze della pratica")}
      />
      <Box sx={{ pt: 10 }}>{renderDataTable()}</Box>
    </Box>
  );
}

export default Udienze;