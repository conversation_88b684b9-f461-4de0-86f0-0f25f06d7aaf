import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { getUdienzeGrid } from "../helper/udienzeGridColumn";

interface QueryParams {
    [key: string]: any;
}

export const useUdienzeHook = (query: QueryParams) => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<any>({
        totalRows: 0,
        currentPage: [],
    });

    const udienzeRequest = useGetCustom("archiveagenda/list?noTemplateVars=true", query);

    const initUdienze = async () => {
        try {
            const finalColumns = await getUdienzeGrid(t);
            setColumns(finalColumns);

            setLoading(true);
            const { data }: any = await udienzeRequest.doFetch(true);

            setList({
                totalRows: data?.totalRows,
                currentPage: data?.currentPage,
            });
        } catch (error) {
            console.error("Error loading documenti grid:", error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        initUdienze();
    }, [query]);

    return {
        initUdienze,
        t,
        columns,
        list,
        loading,
    };
};

export const usePrintUdienze = (uniqueId: any, id: any) => {
    const printUdienze = useGetCustom(`archiveagenda/printlist?fileUniqueid=${uniqueId}`, {}, null, true);

    const handlePrintUdienze = async () => {
        const response: any = await printUdienze.doFetch(true, {});

        const blob = new Blob([response.data], { type: `application/pdf` });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `Lista_udienze_${id}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return { handlePrintUdienze }
};  