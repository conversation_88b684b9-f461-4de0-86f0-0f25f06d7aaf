import {
    IGridColumn,
    IGridSettings,
} from "../../../../../../interfaces/general.interfaces";
import BaseGridList from "../../../../../../models/BaseGridList";
import { mapOtherList } from "../../../../../../utilities/common";
import { statusUdienzaField } from "../../../../../../utilities/helperComponents";

export const getUdienzeGrid = async (t: any) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Data Ora"),
                t("Data provenienza"),
                t("Data rinvio"),
                t("Attività"),
                t("Autorità"),
                t("Città"),
                t("Giudice"),
                t("Stato"),
            ],
            column_keys: [
                "data_ora",
                "data_provenienza",
                "data_rinvio",
                "attivita",
                "autorita",
                "citta",
                "istruttore",
                "stato_evasa",
            ],
            column_widths: [
                "12%",
                "14%",
                "12%",
                "16%",
                "16%",
                "10%",
                "10%",
                "10%",
            ],
            cell_templates: [null, null, null, null],
            sortable: [true, false, false, true, true, true, true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapUdienzeFieldColumnNames(response, t);
};

export const mapUdienzeFieldColumnNames = (response: any, t: any): IGridColumn[] => {
  const {
    column_names,
    column_keys,
    sortable,
    column_widths,
  }: IGridSettings = mapOtherList(response);

  return column_names
    .map((name: string, idx: number) => {
      /* salta colonne nascoste o senza chiave ---------------------------- */
      if (!column_keys[idx] || column_widths?.[idx] === "0%") return undefined;

      /* calcola la flex -------------------------------------------------- */
      const flex =
        Math.round((parseInt(column_widths?.[idx] ?? "100", 10) / 100) * 100) /
        100;

      const col: any = {
        field:      column_keys[idx],
        headerName: name,
        flex,
        sortable:   !!sortable?.[idx],
      };

      /* ---- colonna “Stato” con badge ---------------------------------- */
      if (column_keys[idx] === "stato_evasa") {
        col.renderCell = (params: any) =>
          statusUdienzaField(params?.row?.stato_evasa, t);
        return col;
      }

      /* ---- tutte le altre colonne ------------------------------------- */
      col.renderCell = (params: any) => {
        const raw = params?.row?.[col.field];
        if (raw == null) return "";                // null / undefined → stringa vuota
        if (typeof raw === "object") {
          return raw.label ?? raw.value ?? "";     // oggetto → label/value
        }
        return raw;                                // primitivo
      };

      return col;
    })
    .filter(Boolean) as IGridColumn[];
};
