import { Too<PERSON><PERSON>, VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../custom-components/PageTitle";
import {
    Box,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Grid,
    Button,
    Menu,
    Stack,
} from "@vapor/react-material";
import { Ta<PERSON>, <PERSON>b, Typography } from "@vapor/react-extended";
import { useState, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import { a11yProps, CustomTabPanel } from "../../../../helpers/customTabPanel";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useSearchParams } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCloudArrowUp } from "@fortawesome/pro-regular-svg-icons";
import { faCaretDown } from "@fortawesome/pro-solid-svg-icons";
import { SelectChangeEvent } from "@mui/material";

interface Clause {
    id: string;
    nome: string;
}

interface Category {
    id: string;
    nome: string;
}

interface State {
    clauses: Clause[];
    template: {
        fileName: string;
        author: string;
        tUid: string;
    };
    selections: {
        contract: string;
        contractUser: string;
        contractRegistry: string;
        customer: string;
        lawyer: string;
        counterpart: string;
        external: string;
        collaborator: string;
        coowner: string;
        responsible: string;
        opponent: string;
        creditRecovery: string;
        other: string;
    };
}

export const TemplateUpdate = () => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const fileUniqueid =
        searchParams.get("fileUniqueid") || searchParams.get("uid");
    const [activeTab, setActiveTab] = useState(0);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const getRowData = usePostCustom(
        `archivetemplates/getrowdata?uniqueId=${fileUniqueid}&noTemplateVars=true`
    );

    const getClausesRequest = usePostCustom(
        "archivetemplates/get-clauses-by-category-id?noTemplateVars=true"
    );

    const [state, setState] = useState<State>({
        clauses: [],
        template: {
            fileName: "",
            author: "",
            tUid: "",
        },
        selections: {
            contract: "",
            contractUser: "",
            contractRegistry: "",
            customer: "",
            lawyer: "",
            counterpart: "",
            external: "",
            collaborator: "",
            coowner: "",
            responsible: "",
            opponent: "",
            creditRecovery: "",
            other: "",
        },
    });

    const [categories, setCategories] = useState<Category[]>([]);
    const [insertedClauses] = useState<Clause[]>([]);

    const engineParams = {
        fUid: fileUniqueid,
        tUid: state.template.tUid,
        customers: state.selections.customer,
        counterparts: state.selections.counterpart,
        externals: state.selections.external,
        collaborators: state.selections.collaborator,
        coowners: state.selections.coowner,
        others: state.selections.other,
        responsables: state.selections.responsible,
        documents: null,
        opponents: state.selections.opponent,
        creditrecoveries: state.selections.creditRecovery,
        claims: "",
        liquidations: undefined,
        clauses: insertedClauses.join(","),
        saveInDoc: false,
        external: state.selections.external,
        extension: null,
        iframe: 1,
    };

    const engineRequest = usePostCustom(
        "archivetemplates/engine?noTemplateVars=true"
    );

    const handleCompile = () => {
        engineRequest.doFetch(true, engineParams);
    };

    useEffect(() => {
        getRowData.doFetch(true);
    }, []);

    useEffect(() => {
        if (getRowData.hasLoaded && getRowData.data) {
            setState(prev => ({
                ...prev,
                template: {
                    fileName: getRowData.data.form.fileName,
                    author: getRowData.data.form.author,
                    tUid: getRowData.data.form.tUid,
                },
            }));

            setCategories(getRowData.data.form.categories);

            getClausesRequest.doFetch(true, {
                categoryId: getRowData.data.form.categoryId,
            });
        }
    }, [getRowData.hasLoaded]);

    useEffect(() => {
        if (getClausesRequest.hasLoaded && getClausesRequest.data) {
            setState(prev => ({
                ...prev,
                clauses: getClausesRequest.data.clauses || [],
            }));
        }
    }, [getClausesRequest.hasLoaded]);

    const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
        setActiveTab(newValue);
    };

    const createSelectHandler = (field: keyof typeof state.selections) => {
        return (event: SelectChangeEvent<unknown>) => {
            setState(prev => ({
                ...prev,
                selections: {
                    ...prev.selections,
                    [field]: event.target.value,
                },
            }));
        };
    };

    const handleCompileAndSaveClick = (
        event: React.MouseEvent<HTMLButtonElement>
    ) => {
        setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
    };

    const handleSaveToSystem = async () => {
        const saveParams = {
            ...engineParams,
            saveInDoc: true,
        };

        await engineRequest.doFetch(true, saveParams);
        handleMenuClose();
    };

    const handleSaveToOneDrive = async () => {
        const saveParams = {
            ...engineParams,
            saveInDoc: true,
            external: 1,
        };

        await engineRequest.doFetch(true, saveParams);
        handleMenuClose();
    };

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack
                            direction="row"
                            gap={1}>
                            <Button variant="outlined">{t("Anulla")}</Button>
                            <Button
                                variant="outlined"
                                onClick={handleCompileAndSaveClick}
                                aria-controls={
                                    open ? "compile-save-menu" : undefined
                                }
                                aria-haspopup="true"
                                aria-expanded={open ? "true" : undefined}
                                startIcon={
                                    <FontAwesomeIcon icon={faCloudArrowUp} />
                                }
                                endIcon={
                                    <FontAwesomeIcon icon={faCaretDown} />
                                }>
                                {t("Compila e Salva")}
                            </Button>
                            <Menu
                                id="compile-save-menu"
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleMenuClose}
                                anchorOrigin={{
                                    vertical: "top",
                                    horizontal: "center",
                                }}
                                transformOrigin={{
                                    vertical: "bottom",
                                    horizontal: "center",
                                }}
                                style={{ marginTop: "-8px" }}>
                                <MenuItem onClick={handleSaveToSystem}>
                                    {t("Salva nel gestionale")}
                                </MenuItem>
                                <MenuItem onClick={handleSaveToOneDrive}>
                                    {t("Salva su OneDrive")}
                                </MenuItem>
                            </Menu>
                            <Button
                                variant="outlined"
                                onClick={handleCompile}>
                                {t("Compila")}
                            </Button>
                        </Stack>
                    }></Toolbar>
            }>
            <PageTitle 
                title={t("Modifica modello")} 
                pathToPrevPage={`/archiveTemplates/templates?fileUniqueid=${fileUniqueid}`}
            />
            <VaporPage.Section>
                <Box>
                    <Tabs
                        value={activeTab}
                        onChange={handleTabChange}
                        size="extraSmall"
                        variant="standard">
                        <Tab
                            label={t("Compila modello")}
                            {...a11yProps(0)}
                        />
                        <Tab
                            label={t("Clausole")}
                            {...a11yProps(1)}
                        />
                    </Tabs>
                </Box>
                <CustomTabPanel
                    value={activeTab}
                    index={0}>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            gap: 2,
                            mt: 2,
                            width: "300px",
                        }}>
                        <Typography>{`${t("Nome modello")}: ${
                            state.template.fileName
                        }`}</Typography>
                        <Typography>{`${t("Autore")}: ${
                            state.template.author
                        }`}</Typography>
                        <FormControl>
                            <InputLabel>{t("Contratto")}</InputLabel>
                            <Select
                                value={state.selections.contract}
                                onChange={createSelectHandler("contract")}
                                name="contracts">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Contratto Utente")}</InputLabel>
                            <Select
                                value={state.selections.contractUser}
                                onChange={createSelectHandler("contractUser")}
                                name="contractUsers">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Contratto Anagrafica")}</InputLabel>
                            <Select
                                value={state.selections.contractRegistry}
                                onChange={createSelectHandler(
                                    "contractRegistry"
                                )}
                                name="contractRegistry">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Cliente")}</InputLabel>
                            <Select
                                value={state.selections.customer}
                                onChange={createSelectHandler("customer")}
                                name="customers">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Avvocato")}</InputLabel>
                            <Select
                                value={state.selections.lawyer}
                                onChange={createSelectHandler("lawyer")}
                                name="lawyers">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Controparte")}</InputLabel>
                            <Select
                                value={state.selections.counterpart}
                                onChange={createSelectHandler("counterpart")}
                                name="counterparts">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Esterno")}</InputLabel>
                            <Select
                                value={state.selections.external}
                                onChange={createSelectHandler("external")}
                                name="externals">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Collaboratore")}</InputLabel>
                            <Select
                                value={state.selections.collaborator}
                                onChange={createSelectHandler("collaborator")}
                                name="collaborators">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Cointestatario")}</InputLabel>
                            <Select
                                value={state.selections.coowner}
                                onChange={createSelectHandler("coowner")}
                                name="coowners">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Responsabile")}</InputLabel>
                            <Select
                                value={state.selections.responsible}
                                onChange={createSelectHandler("responsible")}
                                name="responsables">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Avvocato avversario")}</InputLabel>
                            <Select
                                value={state.selections.opponent}
                                onChange={createSelectHandler("opponent")}
                                name="opponents">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Altro")}</InputLabel>
                            <Select
                                value={state.selections.other}
                                onChange={createSelectHandler("other")}
                                name="others">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>

                        <FormControl>
                            <InputLabel>{t("Recupero crediti")}</InputLabel>
                            <Select
                                value={state.selections.creditRecovery}
                                onChange={createSelectHandler("creditRecovery")}
                                name="creditrecoveries">
                                <MenuItem value="">-</MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                </CustomTabPanel>
                <CustomTabPanel
                    value={activeTab}
                    index={1}>
                    <Box sx={{ mt: 2, width: 300 }}>
                        <Grid
                            container
                            spacing={3}>
                            <Grid
                                item
                                xs={12}>
                                <FormControl fullWidth>
                                    <InputLabel id="category-label">
                                        {t("Categoria")}
                                    </InputLabel>
                                    <Select
                                        labelId="category-label"
                                        id="category"
                                        label={t("Categoria")}>
                                        {(categories || []).map(category => (
                                            <MenuItem
                                                key={category.id}
                                                value={category.id}>
                                                {category.nome}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid
                                item
                                xs={12}>
                                <FormControl fullWidth>
                                    <InputLabel id="clauses-label">
                                        {t("Clausole")}
                                    </InputLabel>
                                    <Select
                                        labelId="clauses-label"
                                        id="clauses"
                                        multiple
                                        label={t("Clausole")}
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 224,
                                                },
                                            },
                                        }}>
                                        {(state.clauses || []).map(
                                            (clause: Clause) => (
                                                <MenuItem
                                                    key={clause.id}
                                                    value={clause.id}>
                                                    {clause.nome}
                                                </MenuItem>
                                            )
                                        )}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid
                                item
                                xs={12}>
                                <FormControl fullWidth>
                                    <InputLabel id="inserted-clauses-label">
                                        {t("Clausole inserite")}
                                    </InputLabel>
                                    <Select
                                        labelId="inserted-clauses-label"
                                        id="insertedClauses"
                                        multiple
                                        label={t("Clausole inserite")}
                                        MenuProps={{
                                            PaperProps: {
                                                style: {
                                                    maxHeight: 224,
                                                },
                                            },
                                        }}>
                                        {(insertedClauses || []).map(
                                            (clause: Clause) => (
                                                <MenuItem
                                                    key={clause.id}
                                                    value={clause.id}>
                                                    {clause.nome}
                                                </MenuItem>
                                            )
                                        )}
                                    </Select>
                                </FormControl>
                            </Grid>
                        </Grid>
                    </Box>
                </CustomTabPanel>
            </VaporPage.Section>
        </VaporPage>
    );
};
