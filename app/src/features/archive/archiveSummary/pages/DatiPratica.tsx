import { useTranslation } from "@1f/react-sdk";
import { VaporPage } from "@vapor/react-custom";
import { Box, Button } from "@vapor/react-material";
import { useEffect, useState } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useNavigate, useSearchParams } from "react-router-dom";
import { DatiPraticaCard } from "../components/DatiPraticaCards";
import { DeleteArchiveButton } from "../components/DeleteArchive";
import { DuplicateArchive } from "../components/DuplicateArchive";
import { PrintMenu } from "../components/PrintMenu";
import { ShowCardsMenu } from "../components/ShowCArdsMenu";
import { ToggleArchiveSummary } from "../components/ToggleArchiveSummary";
import type { CardType, Columns } from "../interface/archivedetail.interface";
import { useUpdateSummaryPositions } from "../hooks/useUpdateSummaryPositions";
import { PraticaData } from "..";
import { defaultOptions } from "../constant/sidebar.constant";
import ArchiveHeader from "../archiveHeader";
import { defaultColumns } from "../constant/detail.constant";
export default function DatiPratica({
    praticaData,
}: {
    praticaData: PraticaData | undefined;
}) {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");

    const [showArchiveModal, setShowArchiveModal] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showDuplicateModal, setShowDuplicateModal] = useState(false);

    const [columns, setColumns] = useState<Columns>(defaultColumns);

    useEffect(() => {
        if (praticaData?.uiSettings) {
            const { left, right } = praticaData.uiSettings;

            const leftCards = left
                ? left
                      .filter(
                          (item: { hidden: string }) => item.hidden === "false"
                      )
                      .filter((item: { name: string }) =>
                          defaultOptions.includes(item.name as CardType)
                      )
                      .map((item: { name: string }) => item.name)
                : [];

            const rightCards = right
                ? right
                      .filter(
                          (item: { hidden: string }) => item.hidden === "false"
                      )
                      .filter((item: { name: string }) =>
                          defaultOptions.includes(item.name as CardType)
                      )
                      .map((item: { name: string }) => item.name)
                : [];

            // Only update columns if we have valid data from the server
            if (leftCards.length > 0 || rightCards.length > 0) {
                setColumns({
                    left:
                        leftCards.filter(Boolean).length > 0
                            ? leftCards.filter(Boolean)
                            : null,
                    right:
                        rightCards.filter(Boolean).length > 0
                            ? rightCards.filter(Boolean)
                            : null,
                });
            } else {
                // If no valid data, use default columns
                setColumns(defaultColumns);
            }
        }
    }, [praticaData?.uiSettings]);

    useUpdateSummaryPositions({
        columns: praticaData?.uiSettings
            ? [columns.left || [], columns.right || []].map(
                  (column: CardType[]) =>
                      column.map(id => ({ id: id, hidden: false }))
              )
            : [[], []],
        hasLoaded: !!praticaData?.hasLoaded,
    });

    const handleNavigateToFunds = () => {
        praticaData &&
            navigate(
                `/archive/summary/funds?uid=${uid}&codicearchivio=${praticaData.fileHeader?.codicearchivio}`
            );
    };

    const handleGoBack = () => {
        navigate("/archive/archive");
    };

    const handleArchive = () => {
        setShowArchiveModal(true);
    };

    const handleShowDeleteModal = () => {
        setShowDeleteModal(true);
    };

    const handleShowDuplicateModal = () => {
        setShowDuplicateModal(true);
    };

    return (
        <Box>
            <ToggleArchiveSummary
                setShowArchiveModal={setShowArchiveModal}
                showArchiveModal={showArchiveModal}
                is_archived={praticaData?.fileHeader?.is_archived}
            />
            <DeleteArchiveButton
                showDeleteModal={showDeleteModal}
                setShowDeleteModal={setShowDeleteModal}
            />
            <DuplicateArchive
                setShowDuplicateModal={setShowDuplicateModal}
                showDuplicateModal={showDuplicateModal}
            />
            {praticaData?.fileHeader && (
                <ArchiveHeader
                    fileHeaderData={praticaData.fileHeader}
                    title={t("")}
                    buttons={[
                        <Button
                            variant="outlined"
                            onClick={handleGoBack}>
                            {t("Esci dalla pratica")}
                        </Button>,
                        <Button
                            variant="outlined"
                            onClick={handleArchive}>
                            {praticaData.fileHeader.is_archived === "1"
                                ? t("Ripristina")
                                : t("Archivia")}
                        </Button>,
                        <Button
                            variant="outlined"
                            color="error"
                            onClick={handleShowDeleteModal}>
                            {t("Elimina")}
                        </Button>,
                        <Button
                            variant="outlined"
                            onClick={handleNavigateToFunds}>
                            {t("Fondi pratica")}
                        </Button>,
                        <Button
                            variant="outlined"
                            onClick={handleShowDuplicateModal}>
                            {t("Duplica pratica")}
                        </Button>,
                        <PrintMenu fileHeader={praticaData.fileHeader} />,
                        <ShowCardsMenu
                            columns={columns}
                            setColumns={setColumns}
                        />,
                    ]}
                />
            )}
            <VaporPage.Section>
                <DndProvider backend={HTML5Backend}>
                    <DatiPraticaCard
                        hasLoaded={praticaData?.hasLoaded}
                        columns={[columns.left || [], columns.right || []].map(
                            column =>
                                column.map(cardId => ({
                                    id: cardId,
                                    hidden: false,
                                }))
                        )}
                        setColumns={(newColumns: any) => {
                            setColumns(newColumns as Columns);
                        }}
                        praticaData={{
                            fileHeader: praticaData?.fileHeader,
                            altriDati: praticaData?.altriDati,
                            preview_campi: praticaData?.preview_campi,
                            poliswebData: praticaData?.poliswebData,
                            agendaList: praticaData?.agendaList,
                            deadlinesList: praticaData?.deadlinesList,
                            documentList: praticaData?.documentList,
                        }}
                    />
                </DndProvider>
            </VaporPage.Section>
        </Box>
    );
}
