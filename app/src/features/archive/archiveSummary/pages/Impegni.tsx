import { useTranslation } from "@1f/react-sdk";
import { Box, Button } from "@vapor/react-material";
import { PraticaData } from "..";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { useImpegniData } from "../hooks/useImpegniData";
import { useNavigate, useSearchParams } from "react-router-dom";
import ArchiveHeader from "../archiveHeader";
import { useState, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { saveFile } from "../../../../utilities/utils";
import ToastNotification from "../../../../custom-components/ToastNotification";

export const Impegni = ({
    praticaData,
}: {
    praticaData: PraticaData | undefined;
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const fileUniqueid = searchParams.get("fileUniqueid") || praticaData?.fileHeader?.uniqueid || "";
    const [headerData, setHeaderData] = useState<any>([]);

    const headerDataRequest = useGetCustom("archivedeadlines/deadlines");
    const printRequest = useGetCustom("archivedeadlines/printlist", {}, null, true);
    const [showSaveError, setShowSaveError] = useState(false);

    useEffect(() => {
        if (!praticaData?.fileHeader && fileUniqueid) {
            const fetchHeaderData = async () => {
                const response: any = await headerDataRequest.doFetch(true, {
                    fileUniqueid: fileUniqueid
                });
                if (response?.data?.fileHeader) {
                    setHeaderData(response.data.fileHeader);
                }
            };
            fetchHeaderData();
        } else if (praticaData?.fileHeader) {
            setHeaderData(praticaData.fileHeader);
        }
    }, [praticaData, fileUniqueid]);

    useEffect(() => {
        if (printRequest.hasLoaded) {
            saveFile({
                data: printRequest.data,
                fileName: "Lista_impegni",
                setSaveFileError: setShowSaveError,
                type: "pdf",
            });
        }
    }, [printRequest.hasLoaded, printRequest.loading]);

    const {
        list,
        query,
        setQuery,
        loading,
        gridData,
        handlePageChange,
        handleDeadlineClick,
    } = useImpegniData(praticaData);

    const handlePrintImpegni = () => {
        if (!fileUniqueid) return;
        printRequest.doFetch(true, { fileUniqueid: fileUniqueid });
    };

    const handleNavigateToMacroPage = () => {
        navigate(`/archivedeadlines/deadlines/macro?fileUniqueid=${fileUniqueid}`);
    };

    const renderDataGrid = () => (
        <CustomDataGrid
            columns={list.columns}
            data={gridData}
            totalRows={list.totalRows}
            loading={loading}
            page={query.page}
            pageSize={query.pageSize}
            query={query}
            setQuery={setQuery}
            onPageChangeCallback={handlePageChange}
            onClickCallback={handleDeadlineClick}
            selectableRows={false}
            name="impegniGrid"
            onClickKey="uniqueid"
        />
    );

    return (
        <Box>
            <ArchiveHeader
                fileHeaderData={headerData}
                title={t("Impegni")}
                buttons={[
                    <Button
                        variant="outlined"
                        onClick={() => navigate("/archive/archive")}
                        sx={{ borderRadius: 0 }}
                    >
                        {t("Esci dalla pratica")}
                    </Button>,
                    <Button
                        variant="outlined"
                        onClick={handlePrintImpegni}
                        sx={{ borderRadius: 0 }}
                        disabled={printRequest.loading}
                    >
                        {t("Stampa")}
                    </Button>,
                    <Button
                        variant="outlined"
                        onClick={handleNavigateToMacroPage}
                    >
                        {t("Macro")}
                    </Button>,
                    <Button
                        variant="contained"
                        color="warning"
                        sx={{ borderRadius: 0, bgcolor: "#f0ad4e" }}
                        onClick={() =>
                            navigate(
                                `/impegno/update?fileUniqueid=${fileUniqueid}&from=archivedeadlines`,
                                {
                                    state: {
                                        fileUniqueid: fileUniqueid,
                                        isFromArchive: true
                                    }
                                }
                            )
                        }>
                        {t("Nuovo impegno")}
                    </Button>
                ]}
            />
            <Box sx={{ pt: 10 }}>{renderDataGrid()}</Box>

            <ToastNotification
                showNotification={showSaveError}
                setShowNotification={setShowSaveError}
                severity="error"
                text={t("Errore durante il salvataggio del file")}
            />
        </Box>
    );
};
