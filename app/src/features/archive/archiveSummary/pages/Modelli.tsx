import {
    Box,
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Select,
    TextField,
    CircularProgress,
    Stack,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import useTemplatesFilter from "../hooks/useTemplatesFilter";
import ArchiveHeader from "../archiveHeader";
import { useNavigate, useSearchParams } from "react-router-dom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useState, useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

const Modelli = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const fileUniqueid = searchParams.get("fileUniqueid");

    const {
        query,
        setQuery,
        list,
        defaultQuery,
        loading,
        filterData,
        fileHeaderData,
        authors,
        modelCategories
    } = useTemplatesFilter();

    const [showLoadingNotification, setShowLoadingNotification] =
        useState(false);
    const [showSuccessNotification, setShowSuccessNotification] =
        useState(false);
    const [showErrorNotification, setShowErrorNotification] = useState(false);

    const saveDocumentRequest = usePostCustom(
        "archivetemplates/save-document?noTemplateVars=true"
    );

    const handleClickCallback = async (uniqueid: string) => {
        const selectedRow = list?.rows?.find(row => row.uniqueid === uniqueid);
        const fileExtension = selectedRow.filename.split(".").pop();

        if (fileExtension === "docx") {
            navigate(
                `/archiveTemplates/templates/update?fileUniqueid=${uniqueid}`
            );
        } else {
            await saveDocumentRequest.doFetch(true, {
                tUid: selectedRow.uniqueid,
                fUid: fileUniqueid,
            });
        }
    };

    useEffect(() => {
        if (saveDocumentRequest.loading) {
            setShowLoadingNotification(true);
            setShowSuccessNotification(false);
            setShowErrorNotification(false);
        }

        if (saveDocumentRequest.hasLoaded) {
            setShowLoadingNotification(false);
            if (saveDocumentRequest.data) {
                setShowSuccessNotification(true);
                setShowErrorNotification(false);
            } else {
                setShowErrorNotification(true);
                setShowSuccessNotification(false);
            }
        }
    }, [saveDocumentRequest.loading, saveDocumentRequest.hasLoaded]);

    const onCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { checked } = event.target;
        setQuery({
            ...query,
            mySearch: checked ? "on" : "",
        });
    };

    const handleChange = (event: any) => {
        const { name, value } = event.target;
        if (name) {
            setQuery({
                ...query,
                [name]: value,
            });
        }
    };

    const handleSearch = () => {
        filterData();
    };

    const handleShowAll = () => {
        const resetQuery = {
            ...defaultQuery,
            fileUniqueid: query.fileUniqueid,
        };

        setQuery(resetQuery);
    };

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    return (
        <Box>
            <ToastNotification
                showNotification={showLoadingNotification}
                setShowNotification={setShowLoadingNotification}
                severity="info"
                text={t("Caricando il file nei documenti del fascicolo...")}
            />
            <ToastNotification
                showNotification={showSuccessNotification}
                setShowNotification={setShowSuccessNotification}
                severity="success"
                text={t("Documento salvato con successo!")}
            />
            <ToastNotification
                showNotification={showErrorNotification}
                setShowNotification={setShowErrorNotification}
                severity="error"
                text={t("Il salvataggio non è andato a buon fine riprovare")}
            />

            <ArchiveHeader
                title={t("Modelli")}
                fileHeaderData={fileHeaderData}
                buttons={[]}
            />

            <Box sx={{ padding: 3 }}>
                <Stack
                    direction="row"
                    pb={5}
                    gap={2}
                    alignItems="flex-end">
                    <TextField
                        label={t("Nome modello")}
                        variant="outlined"
                        name="titleSearch"
                        value={query?.titleSearch || ""}
                        onChange={handleChange}
                        sx={{ width: 1 / 4 }}
                    />

                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="authorSearch-label">{t("Autori")}</InputLabel>
                        <Select
                            labelId="authorSearch-label"
                            id="authorSearch"
                            name="authorSearch"
                            value={query?.authorSearch || ""}
                            onChange={handleChange}
                            label={t("Autori")}
                            displayEmpty
                            renderValue={(selected: any) => {
                                if (!selected) return t("Tutti gli autori");
                                return selected as React.ReactNode;
                            }}>
                            <MenuItem value="">{t("Tutti gli autori")}</MenuItem>
                            {authors && authors.map((author: any, index: number) => (
                                <MenuItem
                                    key={`author-${index}`}
                                    value={author.authorName || author.nome}
                                >
                                    {author.authorName || author.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="categorySearch-label">{t("Categorie")}</InputLabel>
                        <Select
                            labelId="categorySearch-label"
                            id="categorySearch"
                            name="categorySearch"
                            value={query?.categorySearch || ""}
                            onChange={handleChange}
                            label={t("Categorie")}
                            displayEmpty
                            renderValue={(selected: any) => {
                                if (!selected) return t("Tutte le categorie");
                                const selectedCategory = modelCategories?.find(cat => cat.id === selected);
                                return selectedCategory ? selectedCategory.nome : selected;
                            }}>
                            <MenuItem value="">{t("Tutte le categorie")}</MenuItem>
                            {modelCategories && modelCategories.map((category: any) => (
                                <MenuItem
                                    key={`category-${category.id}`}
                                    value={category.id}
                                >
                                    {category.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="extensionSearch-label">{t("Estensioni")}</InputLabel>
                        <Select
                            labelId="extensionSearch-label"
                            id="extensionSearch"
                            name="extensionSearch"
                            value={query?.extensionSearch || ""}
                            onChange={handleChange}
                            label={t("Estensioni")}
                            displayEmpty
                            renderValue={(selected: any) => {
                                if (!selected) return t("Tutte le estensioni");
                                return selected as React.ReactNode;
                            }}>
                            <MenuItem value="">
                                {t("Tutte le estensioni")}
                            </MenuItem>
                            <MenuItem value="docx">{t(".docx")}</MenuItem>
                            <MenuItem value="odt">{t(".odt")}</MenuItem>
                            <MenuItem value="pdf">{t(".pdf")}</MenuItem>
                        </Select>
                    </FormControl>

                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={query?.mySearch === "on"}
                                onChange={onCheckboxChange}
                                name="mySearch"
                            />
                        }
                        label={t("Solo i miei modelli")}
                    />

                    <Button
                        variant="outlined"
                        onClick={handleSearch}>
                        {t("Cerca")}
                    </Button>

                    <Button
                        variant="outlined"
                        onClick={handleShowAll}
                        sx={{ whiteSpace: "nowrap" }}>
                        {t("Mostra tutti")}
                    </Button>
                </Stack>

                {loading ? (
                    <Box
                        display="flex"
                        justifyContent="center">
                        <CircularProgress />
                    </Box>
                ) : (
                    <CustomDataGrid
                        name="archivetemplates"
                        data={list?.rows || []}
                        columns={list?.columns || []}
                        totalRows={list?.totalRows || 0}
                        page={list?.page || 0}
                        pageSize={list?.pageSize || 10}
                        loading={loading}
                        onClickCallback={handleClickCallback}
                        query={query}
                        setQuery={setQuery}
                        onPageChangeCallback={onPageChange}
                    />
                )}
            </Box>
        </Box>
    );
};

export default Modelli;
