import React, { useState } from "react";
import { Box, DialogContent, Dialog, DialogTitle, DialogActions, DialogContentText, Divider, FormGroup } from "@vapor/react-material";
import { ExtendedInputAdornment } from "@vapor/react-extended";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useForm } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import FormInput from "../../../../../../custom-components/FormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import AutocompleteWithAdd from "../../../../../../custom-components/AutocompleteWithAdd";

const getSchemaValidation = () => {
    return yup.object().shape({
        billing_perc: yup.number(),
        nome: yup.string(),
        relazione: yup.string(),
        role: yup.string(),
        username: yup.string()
    });
};

const UpdateSoggettiModal = (props: any) => {
    const { fileUniqueid, openModal, setOpenModal, fetchAllData, rowToUpdate, initialRoles } = props;

    const { t } = useTranslation();
    const addToFileRequest = usePostCustom("anagrafiche/addtofile?noTemplateVars=true");
    const [roles, setRoles] = useState(initialRoles || []);

    const relationOptions = [
        { value: "1", label: t("Cliente") },
        { value: "2", label: t("Controparte") },
        { value: "3", label: t("Avversario") },
        { value: "4", label: t("Altro") },
        { value: "5", label: t("Dominus") },
        { value: "6", label: t("Procuratore") },
        { value: "7", label: t("Esterno") },
        { value: "8", label: t("Professionista esterno") },
        { value: "20", label: t("Cointestatario") },
        { value: "21", label: t("Domiciliatario") },
        { value: "22", label: t("Corrispondente") },
        { value: "23", label: t("Cliente Fatturazione") }
    ];

    const filledUpdatePartsRequest = usePostCustom("archiveanagrafiche/fillupdateparts?noTemplateVars=true");
    const cleanedUniqueId = rowToUpdate?.uniqueid.replace(/-\d+$/, "");

    const checkRelationsRequest = useGetCustom("archiveanagrafiche/check-relation-conflicts?noTemplateVars=true");
    const checkValutaRequest = useGetCustom("archiveanagrafiche/check-valuta-conflicts?noTemplateVars=true");
    const getFileHeaderRequest = usePostCustom("archive/getfileheader?noTemplateVars=true");
    const removeRequest = usePostCustom("archiveanagrafiche/removeparts?noTemplateVars=true");

    const remotePersonRoleRequest = usePostCustom(`proceduralroles/remotesave?noTemplateVars=true`);

    React.useEffect(() => {
        const fillRowData = async () => {
            const formData = new FormData();
            formData.append("people_uid", cleanedUniqueId);
            formData.append("file_uid", fileUniqueid);
            formData.append("user_uid", rowToUpdate.user_uid);
            const response: any = await filledUpdatePartsRequest.doFetch(true, formData);
            if (response.data.billing_perc) {
                setValue("billing_perc", response.data.billing_perc);
            }

            setValue("nome", response.data.nome);
            setValue("relazione", response.data.relazione);
            if (response.data.role) {
                setValue("role", String(response.data.role));
            }

            setValue("username", response.data.username);
        };
        fillRowData();
    }, []);

    const { control, handleSubmit, watch, setValue } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            billing_perc: 0.0,
            nome: "",
            relazione: "1",
            role: "-1",
            username: ""
        }
    });

    const values: any = watch();

    const relazione = watch("relazione");
    const navigate = useNavigate();

    const handleRoleChange = async (_: React.SyntheticEvent, value: any) => {
        if (value && value.inputValue) {
            const sendParams = {
                insertItem: value.inputValue
            };
            const response: any = await remotePersonRoleRequest.doFetch(true, sendParams);
            if (response?.data) {
                const newRole = response.data;
                setRoles((prevRoles: any) => [
                    ...prevRoles,
                    {
                        id: newRole.id,
                        nome: newRole.description
                    }
                ]);
                setValue("role", newRole.id);
            }
        } else if (value) {
            setValue("role", value.id);
        } else {
            setValue("role", "-1");
        }
    };

    const onSubmit = async (data: any) => {
        let params = {
            customerUid: cleanedUniqueId,
            fileUniqueId: fileUniqueid,
            relationId: data.relazione
        };

        const response: any = await checkRelationsRequest.doFetch(true, params);
        if (response.data === false) {
            await checkValutaRequest.doFetch(true, params);
            const formData = new FormData();

            formData.append("personUniqueid", cleanedUniqueId);
            formData.append("externalaccessuid", rowToUpdate.access ?? "");
            formData.append("anagraficaUniqueid", cleanedUniqueId);
            formData.append("personId", rowToUpdate.id);
            formData.append("anagraficaUserUid", rowToUpdate.user_uid ?? "");
            formData.append("fileUniqueid", fileUniqueid);
            formData.append("personRole", data.role);
            formData.append("relazione", data.relazione);
            formData.append("billing_perc", data.billing_perc);
            const addToFileResponse: any = await addToFileRequest.doFetch(true, formData);

            if (addToFileResponse.data === "1" || addToFileResponse.data === 1) {
                const formData2 = new FormData();
                formData2.append("fileUniqueid", fileUniqueid);
                await getFileHeaderRequest.doFetch(true, formData2);
                fetchAllData();
                setOpenModal(false);
            }
        }
    };

    const removeParts = async () => {
        const formData = new FormData();
        formData.append("file_uid", fileUniqueid);
        formData.append("people_id", rowToUpdate.id);
        const removeResponse: any = await removeRequest.doFetch(true, formData);
        if (removeResponse.data === "1" || removeResponse.data === 1) {
            fetchAllData();
            setOpenModal(false);
        }
    };

    const clientiRelazione = relazione === "1" || relazione === "23";

    return (
        <>
            <Dialog open={openModal} onClose={() => setOpenModal(!openModal)} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description" maxWidth="lg">
                <DialogTitle>
                    {t("Modifica soggetto")}

                    <Box display="flex" alignItems="right">
                        <IconButton color="primary" onClick={() => setOpenModal(!openModal)}>
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>
                        <DialogContentText id="alert-dialog-description">
                            <Box
                                display="flex"
                                gap={1}
                                sx={{
                                    mt: 2,
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 450
                                    }
                                }}
                            >
                                <FormGroup>
                                    <FormInput control={control} name="nome" type="text" disabled label={t("Nome")} sx={{ width: 1 }} />
                                </FormGroup>
                            </Box>
                            <Box display="flex" alignItems="flex-start" gap={1} sx={{ m: 1 }}>
                                <Box sx={{ width: 450 }}>
                                    <FormGroup>
                                        <AutocompleteWithAdd
                                            options={[
                                                { id: "-1", nome: t("Nessun ruolo selezionato") },
                                                ...roles.map((role: any) => ({
                                                    id: role.id,
                                                    nome: role.nome
                                                }))
                                            ]}
                                            value={roles.find((role: any) => role.id === watch("role")) || (watch("role") === "-1" ? { id: "-1", nome: t("Nessun ruolo selezionato") } : null)}
                                            onChange={handleRoleChange}
                                            label={t("Ruolo")}
                                            width="100%"
                                            valueField="id"
                                            labelField="nome"
                                        />
                                    </FormGroup>
                                </Box>
                            </Box>
                            <Box
                                display="flex"
                                gap={1}
                                sx={{
                                    mt: 2,
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 450
                                    }
                                }}
                            >
                                <FormGroup>
                                    <FormInput control={control} name="relazione" type="select" label={t("Relazione")} options={relationOptions} sx={{ width: 1 }} />
                                </FormGroup>
                            </Box>

                            {clientiRelazione && (
                                <Box
                                    display="flex"
                                    gap={1}
                                    sx={{
                                        mt: 2,
                                        "& > :not(style)": {
                                            m: 1,
                                            width: 450
                                        }
                                    }}
                                >
                                    <FormGroup>
                                        <FormInput
                                            sx={{ width: 300 }}
                                            control={control}
                                            label={t("Percentuale di fatturazione")}
                                            name="billing_perc"
                                            value={values.billing_perc}
                                            placeholder="0.00"
                                            type="number"
                                            InputProps={{
                                                endAdornment: (
                                                    <ExtendedInputAdornment position="end" adornmentBg>
                                                        %
                                                    </ExtendedInputAdornment>
                                                )
                                            }}
                                            inputProps={{
                                                min: 0,
                                                step: "0.01"
                                            }}
                                            onInput={(e: any) => {
                                                const inputValue = e.target.value;
                                                if (inputValue > 100) {
                                                    e.target.value = 100;
                                                }
                                            }}
                                        />
                                    </FormGroup>
                                </Box>
                            )}
                        </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                        <Button variant="contained" onClick={() => navigate(`/anagrafiche/view?id=${rowToUpdate.id}`)}>
                            {t("Vai all'anagrafica")}
                        </Button>

                        <Button variant="outlined" onClick={() => setOpenModal(!openModal)}>
                            {t("Annulla")}
                        </Button>

                        <Button
                            variant="outlined"
                            onClick={() =>
                                navigate("/externalusers/create", {
                                    state: {
                                        createFromAnagrafica: location.pathname,
                                        id: rowToUpdate.id
                                    }
                                })
                            }
                        >
                            {t("Crea Utenza")}
                        </Button>

                        <Button color="error" variant="outlined" onClick={removeParts}>
                            {t("Rimuovi")}
                        </Button>

                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </>
    );
};

export default UpdateSoggettiModal;
