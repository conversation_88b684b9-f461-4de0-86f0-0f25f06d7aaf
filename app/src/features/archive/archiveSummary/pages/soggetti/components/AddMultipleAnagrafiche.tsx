import { useState } from "react";
import { Box, DialogContent, Dialog, DialogTitle, DialogActions, DialogContentText, Divider, FormGroup, Stack, Typography } from "@vapor/react-material";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useForm } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import FormInput from "../../../../../../custom-components/FormInput";
import AutocompleteWithAdd from "../../../../../../custom-components/AutocompleteWithAdd";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const getSchemaValidation = () => {
    return yup.object().shape({
        fileUniqueid: yup.string(),
        selectedAnagrafiche: yup.string(),
        personRole: yup.string(),
        relazione: yup.string()
    });
};

const AddMultipleAnagrafiche = (props: any) => {
    const { fileUniqueid, openModal, setOpenModal, fetchAllData, initialRoles, setOpenMainModal, selectedMultipleRowData, fetchSoggettiData } = props;

    const { t } = useTranslation();
    const [roles, setRoles] = useState(initialRoles || []);

    const relationOptions = [
        { value: "1", label: t("Cliente") },
        { value: "2", label: t("Controparte") },
        { value: "3", label: t("Avversario") },
        { value: "4", label: t("Altro") },
        { value: "5", label: t("Dominus") },
        { value: "6", label: t("Procuratore") },
        { value: "7", label: t("Esterno") },
        { value: "8", label: t("Professionista esterno") },
        { value: "20", label: t("Cointestatario") },
        { value: "21", label: t("Domiciliatario") },
        { value: "22", label: t("Corrispondente") },
        { value: "23", label: t("Cliente Fatturazione") }
    ];

    const addManyToFileRequest = usePostCustom("anagrafiche/addmanytofile?noTemplateVars=true");

    const remotePersonRoleRequest = usePostCustom(`proceduralroles/remotesave?noTemplateVars=true`);

    const denominazioni = (selectedMultipleRowData || [])
        .map((item: any) => item?.denominazione || "")
        .filter(Boolean)
        .join(", ");

    const { control, handleSubmit, watch, setValue } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            fileUniqueid: fileUniqueid,
            selectedAnagrafiche: "",
            personRole: "-1",
            relazione: "1"
        }
    });

    const onSubmit = async (data: any) => {
        const formData = new FormData();
        const anagraficheIds = (selectedMultipleRowData || [])
            .map((item: any) => item?.uniqueid || "")
            .filter(Boolean)
            .join(",");
        formData.append("fileUniqueid", fileUniqueid);
        formData.append("personRole", data.personRole);
        formData.append("relazione", data.relazione);
        formData.append("selectedAnagrafiche", anagraficheIds);
        const addToFileResponse: any = await addManyToFileRequest.doFetch(true, formData);
        if (addToFileResponse.data === "2" || addToFileResponse.data === 2) {
            setOpenModal(false);
            setOpenMainModal(false);
            fetchAllData();
            fetchSoggettiData();
        }
    };

    const handleRoleChange = async (_: React.SyntheticEvent, value: any) => {
        if (value && value.inputValue) {
            const sendParams = {
                insertItem: value.inputValue
            };
            const response: any = await remotePersonRoleRequest.doFetch(true, sendParams);
            if (response?.data) {
                const newRole = response.data;
                setRoles((prevRoles: any) => [
                    ...prevRoles,
                    {
                        id: newRole.id,
                        nome: newRole.description
                    }
                ]);
                setValue("personRole", newRole.id);
            }
        } else if (value) {
            setValue("personRole", value.id);
        } else {
            setValue("personRole", "-1");
        }
    };

    return (
        <>
            <Dialog open={openModal} onClose={() => setOpenModal(!openModal)} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description" maxWidth="lg">
                <DialogTitle>
                    {t("Aggiungi anagrafiche")}

                    <Box display="flex" alignItems="right">
                        <IconButton color="primary" onClick={() => setOpenModal(!openModal)}>
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>
                        <DialogContentText id="alert-dialog-description">
                            <Box
                                sx={{
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 350
                                    }
                                }}
                            >
                                <Stack
                                    alignItems="end"
                                    autoComplete="off"
                                    component="form"
                                    direction="row"
                                    gap={2}
                                    sx={{
                                        width: 350
                                    }}
                                >
                                    {" "}
                                    <>
                                        {" "}
                                        <Typography variant="body500" component="div" color="primary.main" gutterBottom>
                                            {t("Selezionati")}
                                        </Typography>
                                        <Typography variant="bodySmall" component="div" color="primary.main" gutterBottom>
                                            {denominazioni}
                                        </Typography>
                                    </>
                                </Stack>
                            </Box>

                            <Box display="flex" alignItems="flex-start" gap={1} sx={{ m: 1 }}>
                                <Box sx={{ width: 450 }}>
                                    <FormGroup>
                                        <AutocompleteWithAdd
                                            options={[
                                                { id: "-1", nome: t("Nessun ruolo selezionato") },
                                                ...roles.map((role: any) => ({
                                                    id: role.id,
                                                    nome: role.nome
                                                }))
                                            ]}
                                            value={watch("personRole") === "-1" ? { id: "-1", nome: t("Nessun ruolo selezionato") } : roles.find((role: any) => role.id === watch("personRole")) || null}
                                            onChange={handleRoleChange}
                                            label={t("Ruolo")}
                                            width="100%"
                                            valueField="id"
                                            labelField="nome"
                                        />
                                    </FormGroup>
                                </Box>
                            </Box>

                            <Box
                                display="flex"
                                gap={1}
                                sx={{
                                    mt: 2,
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 450
                                    }
                                }}
                            >
                                <FormGroup>
                                    <FormInput control={control} name="relazione" type="select" label={t("Relazione")} options={relationOptions} sx={{ width: 1 }} />
                                </FormGroup>
                            </Box>
                        </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                        <Button variant="outlined" onClick={() => setOpenModal(!openModal)}>
                            {t("Annulla")}
                        </Button>

                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </>
    );
};

export default AddMultipleAnagrafiche;
