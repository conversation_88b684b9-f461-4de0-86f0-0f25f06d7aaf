import React, { useState } from "react";
import { Box, DialogContent, Dialog, DialogTitle, DialogActions, DialogContentText, Divider, FormGroup, Typography } from "@vapor/react-material";
import { ExtendedInputAdornment } from "@vapor/react-extended";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useForm } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import FormInput from "../../../../../../custom-components/FormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import AutocompleteWithAdd from "../../../../../../custom-components/AutocompleteWithAdd";
const getSchemaValidation = () => {
    return yup.object().shape({
        hourlyRateNew: yup.number().min(0),
        hourlyRateOld: yup.number().min(0),
        personName: yup.string(),
        relazione: yup.string(),
        personRole: yup.string(),
        username: yup.string(),
        insertPersonRole: yup.string()
    });
};

const AddSingleAnagraficaModal = (props: any) => {
    const { fileUniqueid, openModal, setOpenModal, fetchAllData, rowToUpdate, initialRoles, fetchSoggettiData, setOpenMainModal } = props;

    const { t } = useTranslation();
    const addToFileRequest = usePostCustom("anagrafiche/addtofile?noTemplateVars=true");
    const [roles, setRoles] = useState(initialRoles || []);
    const relationOptions = [
        { value: "1", label: t("Cliente") },
        { value: "2", label: t("Controparte") },
        { value: "3", label: t("Avversario") },
        { value: "4", label: t("Altro") },
        { value: "5", label: t("Dominus") },
        { value: "6", label: t("Procuratore") },
        { value: "7", label: t("Esterno") },
        { value: "8", label: t("Professionista esterno") },
        { value: "20", label: t("Cointestatario") },
        { value: "21", label: t("Domiciliatario") },
        { value: "22", label: t("Corrispondente") },
        { value: "23", label: t("Cliente Fatturazione") }
    ];

    const checkRelationsRequest = useGetCustom("archiveanagrafiche/check-relation-conflicts?noTemplateVars=true");
    const checkValutaRequest = useGetCustom("archiveanagrafiche/check-valuta-conflicts?noTemplateVars=true");
    const getFileHeaderRequest = usePostCustom("archive/getfileheader?noTemplateVars=true");
    const remotePersonRoleRequest = usePostCustom(`proceduralroles/remotesave?noTemplateVars=true`);

    React.useEffect(() => {
        const fillRowData = async () => {
            setValue("hourlyRateNew", rowToUpdate.hourlyRateNew);
            setValue("hourlyRateOld", rowToUpdate.hourlyRateOld);
            setValue("personName", rowToUpdate.personName);
            setValue("relazione", rowToUpdate.relazione);

            if (rowToUpdate.personRole) {
                setValue("personRole", String(rowToUpdate.personRole));
            }
            setValue("username", rowToUpdate.username);
            setValue("insertPersonRole", rowToUpdate.insertPersonRole);
        };
        fillRowData();
    }, []);

    const { control, handleSubmit, watch, setValue } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            hourlyRateNew: 0.0,
            hourlyRateOld: 0.0,
            personName: "",
            personRole: "-1",
            relazione: "1",
            username: "",
            insertPersonRole: ""
        }
    });

    const values: any = watch();

    const relazione = watch("relazione");

    const onSubmit = async (data: any) => {
        let params = {
            customerUid: rowToUpdate.personUniqueid,
            fileUniqueId: fileUniqueid,
            relationId: data.relazione
        };
        const response: any = await checkRelationsRequest.doFetch(true, params);
        if (response.data === false) {
            await checkValutaRequest.doFetch(true, params);
            const formData = new FormData();
            formData.append("personUniqueid", rowToUpdate.personUniqueid);
            formData.append("fileUniqueid", fileUniqueid);
            formData.append("personRole", data.personRole);
            formData.append("relazione", data.relazione);
            formData.append("billing_perc", data.billing_perc);
            const addToFileResponse: any = await addToFileRequest.doFetch(true, formData);

            if (addToFileResponse.data === "1" || addToFileResponse.data === 1) {
                const formData2 = new FormData();
                formData2.append("fileUniqueid", fileUniqueid);
                await getFileHeaderRequest.doFetch(true, formData2);
                fetchAllData();
                setOpenModal(false);
                fetchSoggettiData();
                setOpenMainModal(false);
            }
        }
    };

    const handleRoleChange = async (_: React.SyntheticEvent, value: any) => {
        if (value && value.inputValue) {
            const sendParams = {
                insertItem: value.inputValue
            };
            const response: any = await remotePersonRoleRequest.doFetch(true, sendParams);
            if (response?.data) {
                const newRole = response.data;
                setRoles((prevRoles: any) => [
                    ...prevRoles,
                    {
                        id: newRole.id,
                        nome: newRole.description
                    }
                ]);
                setValue("personRole", newRole.id);
            }
        } else if (value) {
            setValue("personRole", value.id);
        } else {
            setValue("personRole", "-1");
        }
    };

    const clientiRelazione = relazione === "1" || relazione === "23";

    return (
        <>
            <Dialog open={openModal} onClose={() => setOpenModal(!openModal)} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description" maxWidth="lg">
                <DialogTitle>
                    {t("Aggiungi Anagrafica")}

                    <Box display="flex" alignItems="right">
                        <IconButton color="primary" onClick={() => setOpenModal(!openModal)}>
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>
                        <DialogContentText id="alert-dialog-description">
                            <Box
                                display="flex"
                                gap={1}
                                sx={{
                                    mt: 2,
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 450
                                    }
                                }}
                            >
                                <FormGroup>
                                    <FormInput control={control} name="personName" type="text" disabled label={t("Nome")} sx={{ width: 1 }} />
                                </FormGroup>
                            </Box>
                            <Box display="flex" alignItems="flex-start" gap={1} sx={{ m: 1 }}>
                                <Box sx={{ width: 450 }}>
                                    <FormGroup>
                                        <AutocompleteWithAdd
                                            options={[
                                                { id: "-1", nome: t("Nessun ruolo selezionato") },
                                                ...roles.map((role: any) => ({
                                                    id: role.id,
                                                    nome: role.nome
                                                }))
                                            ]}
                                            value={watch("personRole") === "-1" ? { id: "-1", nome: t("Nessun ruolo selezionato") } : roles.find((role: any) => role.id === watch("personRole")) || null}
                                            onChange={handleRoleChange}
                                            label={t("Ruolo")}
                                            width="100%"
                                            valueField="id"
                                            labelField="nome"
                                        />
                                    </FormGroup>
                                </Box>
                            </Box>

                            <Box
                                display="flex"
                                gap={1}
                                sx={{
                                    mt: 2,
                                    "& > :not(style)": {
                                        m: 1,
                                        width: 450
                                    }
                                }}
                            >
                                <FormGroup>
                                    <FormInput control={control} name="relazione" type="select" label={t("Relazione")} options={relationOptions} sx={{ width: 1 }} />
                                </FormGroup>
                            </Box>

                            {clientiRelazione && (
                                <Box
                                    display="flex"
                                    gap={1}
                                    sx={{
                                        mt: 2,
                                        "& > :not(style)": {
                                            m: 1,
                                            width: 450
                                        }
                                    }}
                                >
                                    <FormGroup>
                                        <FormInput
                                            sx={{ width: 300 }}
                                            control={control}
                                            label={t("Percentuale di fatturazione")}
                                            name="billing_perc"
                                            value={values.billing_perc}
                                            placeholder="0.00"
                                            type="number"
                                            InputProps={{
                                                endAdornment: (
                                                    <ExtendedInputAdornment position="end" adornmentBg>
                                                        %
                                                    </ExtendedInputAdornment>
                                                )
                                            }}
                                            inputProps={{
                                                min: 0,
                                                step: "0.01"
                                            }}
                                            onInput={(e: any) => {
                                                const inputValue = e.target.value;
                                                if (inputValue > 100) {
                                                    e.target.value = 100;
                                                }
                                            }}
                                        />
                                    </FormGroup>
                                </Box>
                            )}
                            {parseFloat(String(values?.hourlyRateNew).replace(",", ".")) > 0 && (
                                <Box
                                    display="flex"
                                    gap={1}
                                    sx={{
                                        "& > :not(style)": {
                                            m: 1,
                                            width: 450
                                        }
                                    }}
                                >
                                    <FormGroup>
                                        <FormInput
                                            control={control}
                                            name={`applicaTariffa`}
                                            type="checkbox"
                                            options={[
                                                {
                                                    label: t("Applica tariffa oraria"),
                                                    value: "0"
                                                }
                                            ]}
                                        />
                                        <Typography variant="body" gutterBottom sx={{ color: "#566B76" }}>
                                            {t("Sovrascrive la tariffa oraria della pratica con quella del cliente.")}
                                        </Typography>
                                        <Box display="flex" alignItems="center" gap={1}>
                                            <Typography color="error" sx={{ textDecoration: "line-through", fontWeight: "bold" }}>
                                                {values?.hourlyRateOld}
                                            </Typography>
                                            <ArrowForwardIcon fontSize="small" />
                                            <Typography color="success.main" fontWeight="bold">
                                                {values?.hourlyRateNew}
                                            </Typography>
                                        </Box>
                                    </FormGroup>
                                </Box>
                            )}
                        </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                        <Button variant="outlined" onClick={() => setOpenModal(!openModal)}>
                            {t("Annulla")}
                        </Button>

                        <Button variant="contained" type="submit">
                            {t("Aggiungi")}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </>
    );
};

export default AddSingleAnagraficaModal;
