
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    FormGroup,
    Typography
} from "@vapor/react-material";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useForm } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import  FormInput  from "../../../../../../custom-components/FormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

const getSchemaValidation = () => {
    return yup.object().shape({
        relazione: yup.string(),
    });
};

const GroupSoggettiModal = (props: any) => {
    const {
        fileUniqueid,
        openModal,
        setOpenModal,
        groups,
        fetchAllData
    } = props;

    const { t } = useTranslation();
    const addGroupRequest = usePostCustom(
        "archiveanagrafiche/addgrouppratica?noTemplateVars=true"
    );

    const { control, handleSubmit } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            relazione: "-1",
        },
    });


    const onSubmit = async (data: any) => {
        const formData = new FormData();
        formData.append("id", data.relazione);
        formData.append("file_uid",fileUniqueid);
        const response: any = await addGroupRequest.doFetch(true,formData)
        if(response.data === "1" || response.data === 1){
            fetchAllData()          
            setOpenModal(false);
        }
     
    };
    return (
        <>
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(!openModal)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Aggiungi Gruppo")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={() => setOpenModal(!openModal)}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                       
                                <FormGroup>
                                    <FormInput
                                        control={control}
                                        name="relazione"
                                        type="select"
                                        label={t("Gruppo soggetto")}
                                        options={[
                                            { value: -1, label: t("Seleziona un gruppo") }, 
                                            ...groups.map((group: any) => ({
                                                value: group.id,
                                                label: group.name,
                                            })),
                                        ]}
                                        sx={{ width: 1 }}
                                    />
                                </FormGroup>

                           
                        </Box>
                        <Box display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}>
                            <Typography variant="body" gutterBottom component="div">
                                <b>{t("N.B.")}</b>
                                {t("Confermando tutti gli utenti del gruppo verranno aggiunti tra le riserve della pratica. Se una volta aggiunto si toglierà la riserva ad un utente appartenente al gruppo esso non sarà più considerato tra le riserve. In presenza di riserva l'intestatario e l'utente che effettua l'operazione saranno sempre presenti.")}
                            </Typography>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        onClick={() => setOpenModal(!openModal)}
                    >
                        {t("Annulla")}
                    </Button>

                    <Button
                        variant="contained"
                        type="submit"
                        
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
                 </form>
            </Dialog>
        </>
    );
};

export default GroupSoggettiModal;
