import {useState, useEffect} from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    MenuItem,
    Select,
    FormControl,
    Divider,
    InputLabel
} from "@vapor/react-material";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import Spinner from "../../../../../../custom-components/Spinner";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { getLogsUtentiGrid } from "../../../../../../utilities/archives/soggetti/gridColumnLogsUtenti";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";


const LogsUtentiModal = (props: any) => {
    const {
        fileUniqueid,
        openModal,
        setOpenModal,
        relazioniutenti
    } = props;

    const { t } = useTranslation();
    const [defaultParams, setDefaultParams] = useState<any>({
        noTemplateVars: true,
        file_uid: fileUniqueid || "",
        page: 0,
        pageSize: 10,
        sortColumn: "reg_date",
        sortOrder: "desc",
        relazioneUtenteLogs: 2
    });
    const [columns, setColumns] = useState<any[]>([]);
    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);


    const logsUtentiRequest = useGetCustom(
        "archiveanagrafiche/get-saved-logs",
        defaultParams
    );


    useEffect(() => {
        const getColumns = async () => {
            const gridColumns: any[] = await getLogsUtentiGrid(t);
            setColumns(gridColumns);
        };

        if (!columns.length) {
            getColumns();
        }
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.relazioneUtenteLogs
    ]);


    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? logsUtentiRequest.doFetch(true)
            : logsUtentiRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;
        setData(currentPage);
        setTotalRows(totalRows);
    };

    const onPageChange = (
        model: GridPaginationModel
    ) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="soggetti"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    logsUtentiRequest.loading ||
                    logsUtentiRequest.loading
                }
                onPageChangeCallback={onPageChange}
                query={defaultParams}
            />
        );
    };
   
  return (
        <>
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(!openModal)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                fullWidth
                maxWidth="md"
            >
                <DialogTitle>
                    {t("Logs utenti")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={() => setOpenModal(!openModal)}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <DialogContent sx={{
                    height: 500, 
                    display: "flex",
                    flexDirection: "column",
                }}>
                    <Box display="flex" alignItems="end" gap={2} sx={{ marginBottom: 2 }}>
                        <FormControl variant="outlined" sx={{ width: 1 / 2 }}>
                         <InputLabel>{t("Relazione")}</InputLabel>
                            <Select
                                labelId="select-label"
                                value={defaultParams.relazioneUtenteLogs}
                                label="Relazione"
                                onChange={onChangeInput}
                                name="relazioneUtenteLogs"
                            >
                                {relazioniutenti?.map((utenti: any) => (
                                <MenuItem key={utenti.id} value={utenti.id}>
                                    {utenti.nome}
                                </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Box >
                    <Box
                        sx={{
                            flexGrow: 1,
                            overflowY: "auto",
                        }}
                    >
                        {renderDataTable()}
                    </Box>
                </DialogContent>
               
            </Dialog>
        </>
    );
};

export default  LogsUtentiModal;
