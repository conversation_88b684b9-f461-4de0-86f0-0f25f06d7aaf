import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { useLocation } from "react-router-dom";

export const useSoggettiHooks = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const fileUniqueid = queryParams.get("fileUniqueid") || queryParams.get("uid");
    const [roles, setRoles] = useState<any[]>([]);
    const [groups, setGroups] = useState<any[]>([]);
    const [internals, setInternals] = useState<any[]>([]);
    const [relazioniutenti, setRelazioniutenti] = useState<any[]>([]);
    const [fileHeaderData, setFileHeaderData] = useState<any[]>([]);

    const soggettiRequest = useGetCustom(
        "archiveanagrafiche/archiveindex?noTempleteVars=true"
    );

      useEffect(() => {
        const initSoggetti = async () => {
        try {
            const response: any = await soggettiRequest.doFetch(
                true,
                { fileUniqueid: fileUniqueid }
            );
            if (response?.data) {
                setRoles(response.data.roles);
                setFileHeaderData(response.data.fileHeader);
                setGroups(response.data.groups);
                setInternals(response.data.internals);
                setRelazioniutenti(response.data.relazioniutenti);
      
            } 
        } catch (error) {
            console.error("Soggetti error", error);          
        }
    };
     initSoggetti();     
    }, [fileUniqueid]);

    return {
        t,
        roles,
        groups,
        internals,
        relazioniutenti,
        fileHeaderData,
       
    };
};