import { useState, useEffect } from "react";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import useGetCustom from "../../../../../hooks/useGetCustom";
import usePostCustom from "../../../../../hooks/usePostCustom";
import Spinner from "../../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import Header from "../../archiveHeader";
import {
    Box,
    Button,
    Menu,
    MenuItem
} from "@vapor/react-material";
import { useNavigate, useLocation } from "react-router-dom";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { getSoggettiGrid } from "../../../../../utilities/archives/soggetti/gridColumn";
import GroupSoggettiModal from "./components/GroupSoggettiModal";
import CreateUtenteModal from "./components/CreateUtenteModal";
import LogsUtentiModal from "./components/LogsUtentiModal";
import AnagraficheModal from "./components/AnagraficheModal";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import UpdateSoggettiModal from "./components/UpdateSoggettiModal";
import UpdateUtenteModal from "./components/UpdateUtenteModal";
import { useSoggettiHooks } from "./hooks/useSoggettiHooks";

export default function Soggetti() {
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const fileUniqueid = queryParams.get("fileUniqueid") || queryParams.get("uid");
    const navigate = useNavigate();
    const [defaultParams, setDefaultParams] = useState<any>({
        noTemplateVars: true,
        fileUniqueid: fileUniqueid || "",
        page: 0,
        pageSize: 10,
        sortColumn: "id",
        sortOrder: "asc"
    });

    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [removeGroupId, setRemoveGroupId] = useState<string>("");
    const [rowToUpdate, setRowToUpdate] = useState<any>(null);
    const [columns, setColumns] = useState<any[]>([]);

    const [
        openSelectGroup,
        setOpenSelectGroup,
    ] = useState(false);
    const [openSelectUser, setOpenSelectUser] = useState(false);
    const [openLogsUtenti, setOpenLogsUtenti] = useState(false);
    const [openSelectAnagrafiche, setOpenSelectAnagrafiche] = useState(false);
    const [groupToRemoveConfirm, setGroupToRemoveConfirm] = useState(false);
    const [updateSoggettiModal, setUpdateSoggettiModal] = useState(false);
    const [updateUtente, setUpdateUtente] = useState(false);

    const soggetiListRequest = useGetCustom(
        "archiveanagrafiche/archivelist",
        defaultParams
    );

    const removeGroupReq = usePostCustom(
    "archiveanagrafiche/removegroup?noTemplateVars=true"
    );

    const {roles, groups, internals, relazioniutenti, fileHeaderData} = useSoggettiHooks();

    useEffect(() => {
        const getColumns = async () => {
            const gridColumns: any[] = await getSoggettiGrid(t);
            setColumns(gridColumns);
        };

        if (!columns.length) {
            getColumns();
        }
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
    ]);


    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? soggetiListRequest.doFetch(true)
            : soggetiListRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;
        setData(currentPage);
        setTotalRows(totalRows);
    };

    const openGroupModal = () => {
      setOpenSelectGroup(!openSelectGroup)
    };

    const openUserModal = () => {
        setOpenSelectUser(!openSelectUser)
    };

    const openAnagraficheModal = () => {
        setOpenSelectAnagrafiche(!openSelectAnagrafiche)
    };

    const openUpdateUtente = () =>{
        setUpdateUtente(!updateUtente)
    }
    const openUpdateSoggettiModal = () => {
        setUpdateSoggettiModal(!updateSoggettiModal)
    };

    const { t } = useTranslation();

    const onPageChange = (
        model: GridPaginationModel
    ) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const [anchorEl, setAnchorEl] = useState(null);

    const open = Boolean(anchorEl);

    const handleClickMenu = (event:any) => {
        setAnchorEl(event.currentTarget);
    };


    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleLogsUtenti = () => {
        setOpenLogsUtenti(true);
    };

    const onClickCallback = (row: any) => {   
        if(row.tableName === "gruppo"){
            setRemoveGroupId(row.id);
            setGroupToRemoveConfirm(true);
        } else if(row.tableName === "anagrafiche"){
           setRowToUpdate(row);
           openUpdateSoggettiModal()
        } else{
            setRowToUpdate(row);
            openUpdateUtente()
        }
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }
        let customData = data.map((item: any, index: number) => ({
            ...item,
            uniqueid: `${item.uniqueid}-${index}`,
        }));

        return (
            <CustomDataGrid
                name="soggetti"
                setQuery={setDefaultParams}
                columns={columns}
                data={customData}             
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    soggetiListRequest.loading ||
                    soggetiListRequest.loading
                }
                onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickCallback={onClickCallback}
                onClickKey="row"
            />
        );
    };

    const toggleUserModal = (isAfterSubmit: boolean = false) => {
        if(isAfterSubmit){
            setOpenSelectUser(false);
            startSearchList();
        }else{
            setOpenSelectUser(!openSelectUser)
        }
    }

      const toggleUtenteUpdateModal = (isAfterSubmit: boolean = false) => {
        if(isAfterSubmit){
            setUpdateUtente(false);
            startSearchList();
        }else{
            setUpdateUtente(!updateUtente)
        }
    }

    const removeGroup = async () => {
      const formData = new FormData();
      formData.append("group_id", removeGroupId);
      formData.append("file_uid", fileUniqueid || "");
      const response: any = await removeGroupReq.doFetch(true, formData);
        if (response.data === 1) {
            setGroupToRemoveConfirm(false);
            startSearchList();
        }
      setRemoveGroupId("");
    };

 
    return (
        <>
            <Box>
                <ConfirmModal
                    open={groupToRemoveConfirm}
                    agree={t("Conferma")}
                    decline={t("Annulla")}
                    title={t("Rimuovere il gruppo dalle riserve?")}
                    handleAgree={removeGroup}
                    handleDecline={() => setGroupToRemoveConfirm(false)}
                    confirmText={t("N.B. Ogni utente del gruppo che non ha un'altra relazione con la pratica non potrà più accedere")}
                />
                {openSelectGroup && (
                    <GroupSoggettiModal
                        fileUniqueid={fileUniqueid}
                        openModal={openSelectGroup}
                        setOpenModal={setOpenSelectGroup}
                        params={defaultParams}
                        users={data}
                        fetchAllData={startSearchList}
                        groups={groups}
                    />
                )}    
                {openSelectUser && (
                    <CreateUtenteModal
                        fileUniqueid={fileUniqueid}
                        openModal={openSelectUser}
                        toggleUserModal={toggleUserModal}
                        params={defaultParams}
                        users={data}
                        fetchAllData={startSearchList}
                        internals={internals}
                        relazioniutenti={relazioniutenti}
                    />
                )} 
                {openLogsUtenti && (
                    <LogsUtentiModal
                        fileUniqueid={fileUniqueid}
                        openModal={openLogsUtenti}
                        setOpenModal={setOpenLogsUtenti}
                        relazioniutenti={relazioniutenti}                        
                    />
                )}
                {openSelectAnagrafiche && (
                    <AnagraficheModal
                        fileUniqueid={fileUniqueid}
                        openModal={openSelectAnagrafiche}
                        setOpenModal={setOpenSelectAnagrafiche}
                        roles={roles}
                        fetchSoggettiData={() => startSearchList()}
                    />
                )}
                {updateSoggettiModal && ( 
                    <UpdateSoggettiModal
                        fileUniqueid={fileUniqueid}
                        openModal={openUpdateSoggettiModal}
                        setOpenModal={setUpdateSoggettiModal}
                        params={defaultParams}
                        users={data}
                        fetchAllData={() => startSearchList()}
                        rowToUpdate={rowToUpdate}
                        initialRoles={roles}
                    />)}

                {updateUtente && (
                    <UpdateUtenteModal
                        fileUniqueid={fileUniqueid}
                        openModal={openUpdateUtente}
                        toggleUserModal={toggleUtenteUpdateModal}
                        params={defaultParams}
                        users={data}
                        fetchAllData={() => startSearchList()}
                        internals={internals}
                        relazioniutenti={relazioniutenti}
                        rowToUpdate={rowToUpdate}
                    />
                )}
                
                    <Header title={t("Soggetti della pratica")} fileHeaderData={fileHeaderData}
                        buttons={[
                            <Button
                                variant="outlined"
                                color="primary"
                                onClick={() => navigate(`/archive/archive`)}
                            >
                                {t("Esci dalla pratica")}
                            </Button>,
                            <div>
                                <Button
                                    type="button"
                                    variant="outlined"
                                    aria-haspopup="true"
                                    id="basic-button"
                                    onClick={handleClickMenu}
                                    endIcon={<ArrowDropDownIcon />}
                                >
                                    {t("Strumenti")}
                                </Button>
                                <Menu
                                    id="basic-menu"
                                    anchorEl={anchorEl}
                                    open={open}
                                    onClose={handleClose}
                                    anchorOrigin={{
                                        vertical: "bottom",
                                        horizontal: "left",
                                    }}
                                    transformOrigin={{
                                        vertical: "top",
                                        horizontal: "left",
                                    }}
                                >
                                    <MenuItem onClick={handleLogsUtenti}>{t("Logs utenti")}</MenuItem>
                                </Menu>
                            </div>,
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={() => openGroupModal()}
                            >
                                {t("Seleziona Gruppi")}
                            </Button>,
                             <Button
                                variant="contained"
                                color="primary"
                                 onClick={() => openUserModal()}
                            >
                                {t("Seleziona Utenti")}
                            </Button>,
                              <Button
                                variant="contained"
                                color="primary"
                                onClick={() => openAnagraficheModal()}
                            >
                                {t("Seleziona Anagrafiche")}
                            </Button>,
                           
                        ]} />
                        {renderDataTable()}
                   
            </Box>
        </>
    );
}
