import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../../custom-components/PageTitle";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    NotificationInline,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import useGetMergeList from "./useGetMergeList";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import { useEffect, useState } from "react";
import { GridPaginationModel } from "@mui/x-data-grid/models";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import { Typography } from "@vapor/react-extended";
import debounce from "lodash/debounce";
import usePostCustom from "../../../../../hooks/usePostCustom";

const DEFAULT_MERGELIST_PARAMS = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    codeType: -1,
    code: "",
    customer: "",
    counterpart: "",
    search: 0,
};

export const MergeList = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");
    const codicearchivio = searchParams.get("codicearchivio");
    const { t } = useTranslation();
    const [params, setParams] = useState(DEFAULT_MERGELIST_PARAMS);
    const { list, loading, query, setQuery } = useGetMergeList();

    const [selectedId, setSelectedId] = useState<number | undefined>(undefined);

    const selectedArchive =
        list.rows.filter(row => row.uniqueid === selectedId) ?? [];

    const id = selectedArchive.length > 0 ? selectedArchive[0].id : "";
    const uniqueid =
        selectedArchive.length > 0 ? selectedArchive[0].uniqueid : null;

    const mergeListRequest = usePostCustom("archive/merge?noTemplateVars=true");

    useEffect(() => {
        if (mergeListRequest.hasLoaded && mergeListRequest.data.result === 0) {
            navigate(`/archive/summary?uid=${uniqueid}`);
        }
    }, [mergeListRequest.hasLoaded, mergeListRequest.data, navigate, uniqueid]);

    useEffect(() => {
        if (mergeListRequest.hasLoaded) {
            setSelectedId(undefined);
        }
    }, [mergeListRequest.hasLoaded]);

    useEffect(() => {
        setQuery((prevQuery: any) => ({
            ...prevQuery,
            page: params.page,
            pageSize: params.pageSize,
            sortColumn: params.sortColumn,
            sortOrder: params.sortOrder,
            codeType: params.codeType,
            search: params.search,
        }));
    }, [
        params.page,
        params.pageSize,
        params.sortColumn,
        params.sortOrder,
        params.codeType,
        params.search,
        setQuery,
    ]);

    useEffect(() => {
        const debouncedUpdate = debounce(() => {
            setQuery((prevQuery: any) => ({
                ...prevQuery,
                code: params.code,
                customer: params.customer,
                counterpart: params.counterpart,
            }));
        }, 500);

        debouncedUpdate();

        return () => {
            debouncedUpdate.cancel();
        };
    }, [params.code, params.customer, params.counterpart, setQuery]);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery((prevQuery: any) => ({
            ...prevQuery,
            page: model.page,
            pageSize: model.pageSize,
        }));
    };

    const handleRowSelection = (id: number) => {
        setSelectedId(id);
    };

    const mergeList = () => {
        mergeListRequest.doFetch(true, {
            sourceUid: uid,
            destinationUid: uniqueid,
        });
    };

    const handleDecline = () => {
        setSelectedId(undefined);
    };

    const handleSetDefault = () => {
        setParams(DEFAULT_MERGELIST_PARAMS);
    };

    const handleSearch = () => {
        setParams(prevParams => ({
            ...prevParams,
            search: prevParams.search + 1,
        }));
    };

    const handleParamsChange =
        (key: keyof typeof DEFAULT_MERGELIST_PARAMS) => (e: any) => {
            setParams(prevParams => ({ ...prevParams, [key]: e.target.value }));
        };

    return (
        <VaporPage>
            <PageTitle
                title={t("Pratica di destinazione")}
                pathToPrevPage={`/archive/summary?uid=${uid}`}
            />
            <ConfirmModal
                agree="Fondi"
                decline="Annulla"
                handleAgree={mergeList}
                handleDecline={handleDecline}
                colorConfirmButton="error"
                loading={mergeListRequest.loading}
                open={selectedId !== undefined}
                title="Attenzione!">
                <Typography>
                    {t(
                        `Procedendo alcuni dati (r.g., tipologia, avvocato, ruolo, etc.) della pratica ${id} Saranno sovrascritti, e la pratica ${codicearchivio} Verrà eliminata. Si è sicuri di voler continuare?`
                    )}
                </Typography>
            </ConfirmModal>
            <VaporPage.Section>
                <NotificationInline
                    variant="outlined"
                    severity="info">
                    {t(
                        "Le pratiche a cui è possibile accorpare i dati della pratica sono quelle a cui non è stato ancora assegnato alcun R.G. e che quindi non contengono dati polisweb."
                    )}
                </NotificationInline>
            </VaporPage.Section>
            <VaporPage.Section>
                <Stack
                    direction="row"
                    alignItems="end"
                    gap={2}>
                    <FormControl sx={{ width: 400 }}>
                        <InputLabel>{t("Ripetizione")}</InputLabel>
                        <Select
                            value={params.codeType}
                            onChange={handleParamsChange("codeType")}>
                            <MenuItem value={-1}>
                                {t("Tutti i codici")}
                            </MenuItem>
                            <MenuItem value={0}>{t("Codice")}</MenuItem>
                            <MenuItem value={1}>
                                {t("Codice Archivio")}
                            </MenuItem>
                            <MenuItem value={2}>
                                {t("Decreto ingiuntivo")}
                            </MenuItem>
                            <MenuItem value={-2}>
                                {t("Codice puntuale")}
                            </MenuItem>
                        </Select>
                    </FormControl>
                    <TextField
                        sx={{ width: 150 }}
                        label="Codice"
                        value={params.code}
                        onChange={handleParamsChange("code")}
                    />
                    <TextField
                        value={params.customer}
                        onChange={handleParamsChange("customer")}
                        label="Cliente"
                        sx={{ width: 300 }}
                    />
                    <TextField
                        value={params.counterpart}
                        onChange={handleParamsChange("counterpart")}
                        label="Controparte"
                        sx={{ width: 300 }}
                    />
                    <Button
                        onClick={handleSearch}
                        variant="contained">
                        {t("Ricerca")}
                    </Button>
                    <Button onClick={handleSetDefault}>
                        {t("Mostra tutte")}
                    </Button>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                <CustomDataGrid
                    name="mergeList"
                    setQuery={setQuery}
                    columns={list.columns}
                    data={list.rows}
                    loading={loading}
                    page={list.page}
                    totalRows={list.totalRows}
                    pageSize={list.pageSize}
                    query={query}
                    onPageChangeCallback={onPageChangeCallback}
                    onClickCallback={handleRowSelection}
                />
            </VaporPage.Section>
        </VaporPage>
    );
};
