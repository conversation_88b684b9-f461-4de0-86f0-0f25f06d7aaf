import { useEffect } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";

export const useMergeList = ({
    sourceUid,
    destinationUid,
    merge,
}: {
    sourceUid: string | null;
    destinationUid: string | null;
    merge: boolean;
}) => {
    const { doFetch, hasLoaded, loading, data } = usePostCustom(
        "archive/merge?noTemplateVars=true"
    );

    useEffect(() => {
        if (merge && sourceUid && destinationUid) {
            doFetch(true, {
                sourceUid: sourceUid,
                destinationUid: destinationUid,
            });
        }
    }, [merge, sourceUid, destinationUid]);

    return { loading, hasLoaded, data };
};
