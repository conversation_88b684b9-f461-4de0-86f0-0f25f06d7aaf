export interface Document {
    id: string;
    codicearchivio: string;
    nomepratica: string;
    oggetto: string | null;
    stato: string;
    esito: string | null;
    referent_id: string | null;
    sede: string | null;
    is_archived: string;
    archived_id: string | null;
    general_protocol: string | null;
    rgnRga: string;
    descrizione: string | null;
    idDfa: string | null;
    data_chiusura: string | null;
    color: string;
    font_color: string;
    uniqueid: string;
    istruttore: string | null;
    referent: string | null;
    listaclienti: string | null;
    listacontroparti: string | null;
    object: string | null;
    status: string;
    linkedFiles: number;
}

export interface DocumentResponse {
    currentPage: Document[];
    totalRows: string;
}


export interface useGetMergeListResponse {
    loading: boolean;
    data: DocumentResponse;
    doFetch: any;
    hasLoaded: boolean;
}