import { useCallback, useEffect, useState } from "react";
import useGet<PERSON>ustom from "../../../../../hooks/useGetCustom";
import { useTranslation } from "@1f/react-sdk";
import { getMergeListArchiveGrid } from "../../../../../utilities/mergeList/gridColumn";
import { IList } from "../../../../../interfaces/general.interfaces";

export interface IDefaultQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
}

export default function useGetMergeList() {
    const { t } = useTranslation();
    const filterRequest = useGetCustom(
        "archive/to-merge-list?noTemplateVars=true"
    );

    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "id",
        sortOrder: "desc",
    };

    const [query, setQuery] = useState<any>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterMergeList = useCallback(async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getMergeListArchiveGrid(t),
            filterRequest.doFetch(true, cQuery),
        ]);
        const { currentPage, totalRows } = response.data;
        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    }, []);


    useEffect(() => {
        filterMergeList(query);
    }, [query]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterMergeList: filterMergeList,
        loading: filterRequest.loading,
    };
}
