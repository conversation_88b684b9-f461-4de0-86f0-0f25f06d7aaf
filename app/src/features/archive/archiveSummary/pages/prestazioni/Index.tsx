import VaporPage from "@vapor/react-custom/VaporPage";
import Header from "../../archiveHeader";
import { Button, Table, TableRow, TableCell, Menu, MenuItem } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import Spinner from "../../../../../custom-components/Spinner";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import { GridPaginationModel, GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { useTranslation } from "@1f/react-sdk";
import { useState } from "react";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
export default function Prestazioni({ fuid }: any) {
    const navigate = useNavigate();

    const { t } = useTranslation();
    const { defaultQuery, query, setQuery, list, filterData, loading, countData } = useFilter(fuid);

    const [totals, setTotals] = useState<any>({
        totalSpese: 0,
        totalCompensi: 0,
        total: 0,
        totalMinuti: "0 Ore, 0 minuti",
        totalSpeseTYPE: {
            giroconto: 0,
            spese: 0,
            spese_anticipate: 0,
            spese_escluse: 0,
            spese_esenti: 0,
            spese_imponibili: 0,
            spese_non_imponibili: 0,
            spese_non_specificate: 0,
            spese_esenti_art__10: 0,
            spese_escluse_art__15: 0
        }
    });
    const { mountedData, headerData } = useMountedData(fuid, setQuery);
    const [rowData, setRowData] = useState<any[]>([]);
    const handleClickCallback = (uniqueid: string) => {
        navigate("/archivedetails/update/" + uniqueid);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize
        });
    };

    const rowSelectableCustom = (params: any) => {
        if (params.row.groupId != null) {
            return false;
        }
        return params.row.invoiceUid === null;
    };

    const rowSelection = (model: GridRowSelectionModel) => {
        let data = list.rows.filter((row: any) => model.includes(row.id));
        updateSelectedTotals(data);
        setRowData(data);
    };

    const updateSelectedTotals = (checkedItemsWithValue: any[]) => {
        // Initialize totals
        let totalSpese = 0;
        let totalCompensi = 0;
        let totalMinuti = 0;
        let total = 0;

        // Initialize spese types
        const totalSpeseTYPE: any = {
            spese_non_specificate: 0,
            spese_anticipate: 0,
            spese_imponibili: 0,
            spese_escluse: 0,
            spese_esenti: 0,
            spese_esenti_art__10: 0,
            spese_escluse_art__15: 0,
            spese_non_imponibili: 0,
            giroconto: 0
        };

        // Iterate through selected items
        checkedItemsWithValue.forEach((item) => {
            const itemValue = parseFloat(item.valore ?? 0);
            const itemQuantity = parseFloat(item.quantita ?? 0);
            let isSpesa = false;
            let itemSpesaType;

            if (item.tipologia_spesa && item.tipologia_spesa >= 0) {
                isSpesa = true;
                itemSpesaType = parseInt(item.tipologia_spesa);
            } else if (item.is_spesa) {
                isSpesa = true;
                itemSpesaType = parseInt(item.competenza);
            }

            if (isSpesa) {
                // Determine spesa type
                let spesaType = "";
                switch (itemSpesaType) {
                    case 0:
                        spesaType = "spese_non_specificate";
                        break;
                    case 1:
                        spesaType = "spese_anticipate";
                        break;
                    case 2:
                        spesaType = "spese_imponibili";
                        break;
                    case 3:
                        spesaType = "spese_esenti_art__10";
                        break;
                    case 4:
                        spesaType = "spese_escluse_art__15";
                        break;
                    case 5:
                        spesaType = "spese_non_imponibili";
                        break;
                    case 10:
                        spesaType = "giroconto";
                        break;
                    default:
                        break;
                }

                // Update totals for spesa type
                totalSpeseTYPE[spesaType] += itemValue;
                totalSpese += itemValue;
            } else {
                // Update totals for other prestations
                if (itemQuantity > 1) {
                    totalMinuti += itemQuantity;
                }
                totalCompensi += itemValue;
            }
        });

        // Calculate total
        total = totalSpese + totalCompensi;

        // Format hours and minutes
        const hours = Math.floor(totalMinuti / 60);
        const mins = totalMinuti % 60;
        const hoursText = hours === 1 ? t("Ora") : t("Ore");
        const minsText = mins === 1 ? t("minuto") : t("minuti");
        const totalHM = `${hours} ${hoursText}, ${mins} ${minsText}`;

        // Update state or render totals
        setTotals({
            totalSpese,
            totalCompensi,
            total,
            totalMinuti: totalHM,
            totalSpeseTYPE
        });
    };
    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                selectableRows={true}
                onRowSelectionModelChange={rowSelection}
                name="archivedetails"
                setQuery={setQuery}
                columns={list.columns} // Use the updated columns
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
                onClickKey="id"
                isRowSelectableCustom={rowSelectableCustom}
            />
        );
    };
    const formatKey = (key: string) => {
        return key
            .split("_") // Split the key by underscores
            .join(" ") // Join the words with a space
            .replace(/^./, (char) => char.toUpperCase()); // Capitalize the first character
    };

    const getTotalSpese = () => {
        if (countData.totalsSpese && typeof countData.totalsSpese === "object") {
            return Object.entries(countData.totalsSpese).map(([key]) => {
                if (countData.totalsSpese[key] > 0 && key !== "spese" && key !== "spese_escluse" && key !== "spese_esenti") {
                    return (
                        <TableRow key={key}>
                            <TableCell align="right" width={"200px"}>
                                {t(`${formatKey(key)} (€)`)}
                            </TableCell>
                            <TableCell width={"100px"}>{countData.totalsSpese[key].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                            <TableCell>{totals.totalSpeseTYPE[key].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                    );
                } else if (key == "spese_escluse") {
                    return (
                        <TableRow key={key}>
                            <TableCell align="right" width={"200px"}>
                                {t(`${formatKey(key)} (€)`)}
                            </TableCell>
                            <TableCell width={"100px"}>{countData.totalsSpese[key].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                            <TableCell>{totals.totalSpeseTYPE["spese_escluse_art__15"].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                    );
                } else if (key == "spese_esenti") {
                    return (
                        <TableRow key={key}>
                            <TableCell align="right" width={"200px"}>
                                {t(`${formatKey(key)} (€)`)}
                            </TableCell>
                            <TableCell width={"100px"}>{countData.totalsSpese[key].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                            <TableCell>{totals.totalSpeseTYPE["spese_esenti_art__10"].toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                    );
                }

                return null;
            });
        }
        return null;
    };

    const convertMinutesToHours = (minutes: number): string => {
        if (!minutes || minutes < 0) return "0 Ore, 0 minuti"; // Handle invalid or negative input

        const hours = Math.floor(minutes / 60); // Calculate the number of hours
        const remainingMinutes = minutes % 60; // Calculate the remaining minutes

        // Handle plural and singular forms
        const hoursText = hours === 1 ? `${hours} Ora` : `${hours} Ore`;
        const minutesText = remainingMinutes === 1 ? `${remainingMinutes} minuto` : `${remainingMinutes} minuti`;

        return hoursText + ", " + minutesText; // Return the formatted string
    };

    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const handleClickMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const [anchorEl2, setAnchorEl2] = useState(null);
    const open2 = Boolean(anchorEl2);
    const handleClickMenu2 = (event: any) => {
        setAnchorEl2(event.currentTarget);
    };

    const handleClose2 = () => {
        setAnchorEl2(null);
    };

    const [anchorEl3, setAnchorEl3] = useState(null);
    const open3 = Boolean(anchorEl3);
    const handleClickMenu3 = (event: any) => {
        setAnchorEl3(event.currentTarget);
    };

    const handleClose3 = () => {
        setAnchorEl3(null);
    };

    return (
        <>
            <VaporPage>
                <Header
                    title={t("Prestazioni")}
                    fileHeaderData={headerData}
                    buttons={[
                        <Button variant="outlined" color="primary" onClick={() => navigate(`/archive/archive`)}>
                            {t("Esci dalla pratica")}
                        </Button>,
                        <div>
                            <Button type="button" variant="outlined" aria-haspopup="true" id="basic-button" onClick={handleClickMenu3} endIcon={<ArrowDropDownIcon />}>
                                {t("Esporta")}
                            </Button>
                            <Menu
                                id="basic-menu"
                                anchorEl={anchorEl3}
                                open={open3}
                                onClose={handleClose3}
                                anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "left"
                                }}
                                transformOrigin={{
                                    vertical: "top",
                                    horizontal: "left"
                                }}
                            >
                                <MenuItem>{t("PDF")}</MenuItem>
                                <MenuItem>{t("Excel")}</MenuItem>
                            </Menu>
                        </div>,
                        <Button variant="contained" disabled={rowData.length == 0} color="error">
                            {t("Elimina")}
                        </Button>,
                        <Button variant="contained" disabled={rowData.length == 0} color="primary" onClick={() => navigate(`/archive/archive`)}>
                            {t("Modifica")}
                        </Button>,
                        <Button variant="contained" disabled={rowData.length == 0} color="primary" onClick={() => navigate(`/archive/archive`)}>
                            {t("Raggruppa")}
                        </Button>,

                        <div>
                            <Button type="button" disabled={rowData.length == 0} variant="outlined" aria-haspopup="true" id="basic-button" onClick={handleClickMenu2} endIcon={<ArrowDropDownIcon />}>
                                {t("Azioni")}
                            </Button>
                            <Menu
                                id="basic-menu"
                                anchorEl={anchorEl2}
                                open={open2}
                                onClose={handleClose2}
                                anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "left"
                                }}
                                transformOrigin={{
                                    vertical: "top",
                                    horizontal: "left"
                                }}
                            >
                                <MenuItem>{t("Write Off")}</MenuItem>
                                <MenuItem>{t("Definito")}</MenuItem>
                            </Menu>
                        </div>,
                        <div>
                            <Button type="button" variant="outlined" aria-haspopup="true" id="basic-button" onClick={handleClickMenu} endIcon={<ArrowDropDownIcon />}>
                                {t("Inserisci")}
                            </Button>
                            <Menu
                                id="basic-menu"
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "left"
                                }}
                                transformOrigin={{
                                    vertical: "top",
                                    horizontal: "left"
                                }}
                            >
                                <MenuItem>{t("Prestazione libera")}</MenuItem>
                                <MenuItem>{t("Timesheet")}</MenuItem>
                                <MenuItem>{t("Incasso")}</MenuItem>
                                <MenuItem>{t("Spesa")}</MenuItem>
                                <MenuItem>{t("Voci listino")}</MenuItem>
                                <MenuItem>{t("Voci tariffario")}</MenuItem>
                            </Menu>
                        </div>,

                        <Button variant="contained" disabled={rowData.length == 0} color="primary" onClick={() => navigate(`/archive/archive`)}>
                            {t("Crea documento")}
                        </Button>
                    ]}
                />
                <VaporPage.Section>
                    <Filters fuid={fuid} defaultQuery={defaultQuery} query={query} setQuery={setQuery} filterData={filterData} mountedData={mountedData} />
                </VaporPage.Section>
                <VaporPage.Section>
                    {renderDataTable()}
                    <Table>
                        {getTotalSpese()}

                        <TableRow>
                            <TableCell align="right" width={"100px"}>
                                {t("Totale Spese (€)")}
                            </TableCell>
                            <TableCell width={"150px"}>{countData.totals}</TableCell>
                            <TableCell>{totals.total.toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell align="right" width={"100px"}>
                                {t("Totale Compensi (€)")}
                            </TableCell>
                            <TableCell width={"150px"}>{countData.totalsCompensi}</TableCell>
                            <TableCell>{totals.totalCompensi.toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell align="right">{t("Totale (€)")}</TableCell>
                            <TableCell>{countData.totals}</TableCell>
                            <TableCell>{totals.total.toLocaleString("it-IT", { minimumFractionDigits: 2 })}</TableCell>
                        </TableRow>
                        <TableRow>
                            <TableCell align="right">{t("Totale Ore")}</TableCell>
                            <TableCell>{convertMinutesToHours(countData.totalsMinuti)}</TableCell>
                            <TableCell>{totals.totalMinuti}</TableCell>
                        </TableRow>
                    </Table>
                </VaporPage.Section>
            </VaporPage>
        </>
    );
}
