import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

export default function useMountedData(fileHeaderData: string, setQuery: any) {
    const mountedRequest = useGetCustom("archivedetails/index?fileUniqueid=" + fileHeaderData);
    const [mountedData, setMountedData] = useState<any>([]);
    const [headerData, setHeaderData] = useState<any>([]);

    useEffect(() => {
        async function fetchData() {
            const { data }: any = await mountedRequest.doFetch(true);
            setMountedData(data);
            setQuery((prevQuery: any) => ({
                ...prevQuery,
                itemStartDate: data.startDate,
                itemEndDate: data.endDate
            }));
            setHeaderData(data.fileHeader);
        }

        fetchData();
    }, []);

    return { mountedData,headerData };
}
