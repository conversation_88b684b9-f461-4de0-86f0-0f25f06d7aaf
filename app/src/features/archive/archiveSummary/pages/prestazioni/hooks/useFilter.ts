import useGetCustom from "../../../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback, useRef } from "react";
import { IDefaultQuery } from "../interfaces/simpleList.interface";
import { IList } from "../../../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import { getPerformancesObjectGrid } from "../../../../../../utilities/prestazioni/gridColumn";

export default function useFilter(fuid: string) {
    const { t } = useTranslation();
    const filterRequest = useGetCustom("archivedetails/archiveitemlist?noTemplateVars=true");

    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "nome",
        sortOrder: "desc",
        fileUid: fuid,
        customerSearch: "",
        controparteSearch: "",
        fileUniqueid: "",
        itemsImp: "",
        typeSearch: "-1",
        inputSearch: "",
        statusSearch: "-1",
        feeType: "-1",
        fatturabile: "-1",
        userSearch: "-1",
        itemStartDate: "",
        itemEndDate: ""
    };

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0
    });

    const [countData, setCountData] = useState<any>({
        totals: 0,
        totalsCompensi: 0,
        totalsFromService: 0,
        totalsMinuti: 0,
        totalsSpese: 0
    });

    const filterData = async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([getPerformancesObjectGrid(t), filterRequest.doFetch(true, cQuery)]);
        if (response.data._redirectInfo === undefined) {
            const { currentPage, totalRows } = response.data;
            setCountData({
                totals: response.data.totals,
                totalsCompensi: response.data.totalsCompensi,
                totalsFromService: response.data.totalsFromService,
                totalsMinuti: response.data.totalsMinuti,
                totalsSpese: response.data.totalsSpese
            });
            setList({
                ...list,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: cQuery?.page,
                pageSize: cQuery?.pageSize
            });
        } else {
            setList({
                ...list,
                rows: [],
                columns,
                totalRows: 0,
                page: cQuery?.page,
                pageSize: cQuery?.pageSize
            });
        }
    };
    const isMountingRef = useRef(false);

    useEffect(() => {
        isMountingRef.current = true;
        filterData(query);
    }, []);
    const getSearchResults = useCallback(
        debounce((value: any) => {
            filterData(value);
        }, 500),
        []
    );

    useEffect(() => {
        if (!isMountingRef.current) {
            getSearchResults(query);
        } else {
            isMountingRef.current = false;
        }
    }, [query]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: filterRequest.loading,
        countData,
        setCountData
    };
}
