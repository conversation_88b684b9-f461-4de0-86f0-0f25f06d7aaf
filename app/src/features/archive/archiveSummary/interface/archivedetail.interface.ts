import { Dispatch, SetStateAction } from "react";

export interface fileHeader {
    neighbors: any;
    id: string;
    pwid: string;
    riforma_cartabia: string;
    codicearchivio: string;
    descrizione: string | null;
    nome_pratica: string;
    hourly_rate: string;
    importo_forfait: string;
    category: string;
    emittente_office: string;
    listaclienti: string;
    listacontroparti: string;
    tipologiapratica: string;
    uniqueid: string;
    color: string;
    font_color: string;
    responsabile: string | null;
    currency_id: string | null;
    general_protocol: string | null;
    situazione: string | null;
    situazioneContabile: string | null;
    centroProfitto: string | null;
    RG: string | null;
    autorita: string;
    fourclegal_contest: string | null;
    dataapertura: string;
    data_chiusura: string | null;
    ruolo_avvocato: string;
    numerodecretoingiuntivo: string | null;
    numerodecretoingiuntivoanno: string | null;
    quadro_d_code_id: string;
    numero_sentenza: string;
    numero_sentenza_anno: string | null;
    immessoil: string;
    modificatoil: string;
    ruologeneralenumero: string | null;
    ruologeneraleanno: string | null;
    subprocedimento: string;
    idDfa: string | null;
    valore: string;
    ufficio_giudiziario: string;
    stato: string;
    oggetto: boolean;
    avvocato: string;
    nomepratica: string;
    dominus: string | null;
    procuratore: string | null;
    emissione_di: string | null;
    notifica_di: string | null;
    decreto_esecutorieta: string | null;
    provvedimento: string | null;
    formula_esecutiva: string | null;
    formula_protesto: string | null;
    notifica_precetto: string | null;
    notifica_pignoramento: string | null;
    datareato: string | null;
    dataprescrizione: string | null;
    reato: boolean;
    rgnr: string | null;
    rgnranno: string | null;
    pubblico_ministero: boolean;
    rggip: string;
    rggipanno: string;
    rggipdescr: string;
    rggup: string;
    rggupanno: string;
    rggupdescr: string;
    rgtrib: string;
    rgtribanno: string;
    rgtribtipo: string;
    rgtribdescr: string;
    rgapp: string;
    rgappanno: string;
    rgappdescr: string;
    rgcass: string;
    rgcassanno: string;
    rgcassdescr: string;
    rgsiep: string;
    rgsiepanno: string;
    rgsiepdescr: string;
    rgsius: string;
    rgsiusanno: string;
    rgsiusdescr: string;
    rgriesame: string;
    rgriesameanno: string;
    rgriesamedescr: string;
    annotazioni: string;
    is_archived: string;
    data_archivio: string | null;
    riservata: string | null;
    sede: boolean;
    giudice: string | null;
    sezione: string | null;
    tipo_valore: string | null;
    smartmailer_address: string | null;
    smartmailer_upload: string | null;
    smartmailer_both: string | null;
    referent_id: boolean;
    esito: string;
    spese_generali: string;
    soglia_budget: string | null;
    soglia_ore: string | null;
    soglia_scoperto: string | null;
    alert_email: string;
    alert_telegram: string;
    alert_gestionale: string;
    listino: string | null;
    listino_orario: string | null;
    ufficio_giudiziario_citta: string;
    tipologi_riforma_cartabia: string;
    ref_pwid: string;
    emittente: boolean;
    id_sede: string | null;
    currency: string;
    currency_sym: string;
    cointestatari: string | null;
    cointestatariAnagrafiche: string | null;
    archived_id: string | null;
    listaavversari: string | null;
    listaaltrisoggetti: string | null;
    ordineAcquisto_id: string | null;
    ordineAcquisto_data: string | null;
    ordineAcquisto_numItem: string | null;
    ordineAcquisto_codiceCommessaConvenzione: string | null;
    ordineacquisto_cup: string | null;
    ordineacquisto_cig: string | null;
    tags: string;
    soci: any[];
    linkedFiles: any[];
    lawyerId: string;
    rgnRga: string;
    headerArchive: string;
}

export interface AltriDati {
    general_protocol: string | null;
    situazione: boolean;
    situazione_contabile: boolean;
    centro_profitto: boolean;
    externalCode: string | null;
    stanza: string | null;
    palchetto: string | null;
    scaffale: string | null;
    faldone: string | null;
    scatolone: string | null;
    data_macero: string | null;
}

export type CardType =
    | "summaryAnnotations"
    | "summaryDatiGenerali"
    | "summaryDatiPolisweb"
    | "summaryAltriDati"
    | "summaryGiudiziale"
    | "summaryPraticheCollegate"
    | "summaryTags"
    | "summaryLastItems"
    | "summaryCampiDinamici"
    | "summaryAcquisto";

export type CardElement = {
    id: CardType;
    hidden: boolean;
};

export type Columns = {
    left: CardType[] | null;
    right: CardType[] | null;
};

export interface ShowCardsMenuProps {
    columns: Columns;
    setColumns: (columns: Columns) => void;
}

export interface DatiPraticaCardProps {
    columns: CardElement[][];
    setColumns: (columns: Columns) => void;
    hasLoaded: boolean | undefined;
    praticaData: {
        fileHeader?: any;
        altriDati?: any;
        preview_campi?: any;
        poliswebData?: any;
        agendaList?: any;
        deadlinesList?: any;
        documentList?: any;
    };
}

export type printOption =
    | "alphabetic desc"
    | "alphabetic asc"
    | "Chronological desc"
    | "Chronological asc";

export interface PrintDataMenuProps {
    anchorElSubMenu: HTMLElement | null;
    setAnchorElSubMenu: Dispatch<SetStateAction<HTMLElement | null>>;
    setAnchorEl: Dispatch<SetStateAction<HTMLElement | null>>;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}

export interface Template {
    id: string;
    title: string;
    filename: string;
    user_id: string;
    upload_date: string;
    uniqueid: string;
    category: string;
    default_print: string;
    signature_points: string | null;
}

export interface PolisWebDataDetails {
    atto_introduttivo?: string | null;
    contributo_unificato?: number | string | null;
    costituzione?: string | null;
    data_citazione?: string | null;
    data_iscrizione?: string | null;
    grado?: string | null;
    giudice?: string | null;
    materia?: string | null;
    numero_sezionale?: string | number | null;
    anno_sezionale?: string | number | null;
    data_prima_comparizione?: string | null;
    rito?: string | null;
    ruolo?: string | null;
    sezione?: string | null;
    data_ultima_sincronizzazione?: string | null;
}

export interface PolisWebData {
    polisweb?: boolean;
    data?: PolisWebDataDetails;
}

export interface AgendaItem {
    data: string;
    attivita: string;
}

export interface DeadlineItem {
    data: string;
    testo: string;
}

export interface DocumentItem {
    data?: string;
    titolodocumento: string;
}

export interface FileHeader {
    uniqueid: string;
}

export interface UltimiEventiCardProps {
    agendaList: AgendaItem[] | undefined;
    deadlinesList: DeadlineItem[] | undefined;
    documentsList: DocumentItem[] | undefined;
    fileHeader: FileHeader | undefined;
}

export interface Campo {
    tipo: "regex" | "memo" | "date" | "checkbox" | "select";
    nome: string;
    valore?: string;
    select?: string;
}

export interface LabelValue {
    label: string;
    value: string;
}

export interface CampiDinamiciCardProps {
    preview_campi: Campo[] | undefined;
}

export interface IBaseArchiveDetailProps {
    fileHeaderData: any;
}
