import { Dispatch, SetStateAction, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { printOption } from "../interface/archivedetail.interface";
import { saveFile } from "../../../../utilities/utils";

export const usePrintTitlePage = ({
    uniqueid,
    print,
    setSaveFileError,
}: {
    uniqueid: string | null;
    print: boolean;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archive/printtitlepage"
    );

    useEffect(() => {
        if (print && uniqueid) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [print, uniqueid]);

    useEffect(() => {
        if (hasLoaded) {
            const title = "Frontespizio_interno_pratica";
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);

    return { data, loading, hasLoaded };
};

export const usePrintTitlePageDeposit = ({
    uniqueid,
    print,
    setSaveFileError,
}: {
    uniqueid: string;
    print: boolean;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archive/printtitlepagedeposit"
    );
    useEffect(() => {
        if (print && uniqueid) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [print, uniqueid]);

    useEffect(() => {
        if (hasLoaded) {
            const title = "Frontespizio_per_deposito_praticha";
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);
    return { data, loading, hasLoaded };
};

export const useArchiveDocumentsPrintList = ({
    uniqueid,
    print,
    setSaveFileError,
}: {
    uniqueid: string | null;
    print: boolean;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archivedocuments/printlist"
    );
    useEffect(() => {
        if (print && uniqueid) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [print, uniqueid]);

    const title = "lista_documenti";
    useEffect(() => {
        if (hasLoaded) {
            saveFile({
                data,
                fileName: title,
                setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);

    return { data, loading, hasLoaded };
};

export const useArchiveDeadlinesPrintList = ({
    uniqueid,
    print,
    setSaveFileError,
}: {
    uniqueid: string | null;
    print: boolean;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archivedeadlines/printlist"
    );
    useEffect(() => {
        if (print && uniqueid) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [print, uniqueid]);

    const title = "Lista impegni";

    useEffect(() => {
        if (hasLoaded) {
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);

    return { data, loading, hasLoaded };
};

export const useArchiveAgendaPrintList = ({
    uniqueid,
    print,
    setSaveFileError,
}: {
    uniqueid: string | null;
    print: boolean;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archivedeadlines/printlist"
    );
    useEffect(() => {
        if (print && uniqueid) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [print, uniqueid]);

    const title = "Lista_udienze";

    useEffect(() => {
        if (hasLoaded) {
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);
    return { data, loading, hasLoaded };
};

export const useArchivePrintFile = ({
    uniqueid,
    print,
    option,
    setSaveFileError,
}: {
    uniqueid: string | null;
    print: boolean;
    option: printOption | undefined;
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { data, loading, hasLoaded, doFetch } =
        useGetCustom("archive/printfile");

    const orderBy =
        option === "Chronological asc"
            ? "asc"
            : option === "Chronological desc"
            ? "desc"
            : option === "alphabetic asc"
            ? "asc"
            : option === "alphabetic desc"
            ? "desc"
            : "";

    const orderDir =
        option === "Chronological asc"
            ? "date"
            : option === "Chronological desc"
            ? "date"
            : option === "alphabetic asc"
            ? "title"
            : option === "alphabetic desc"
            ? "title"
            : "";

    useEffect(() => {
        if (print && uniqueid && orderBy && orderDir) {
            doFetch(true, {
                uniqueid: uniqueid,
                orderBy: orderBy,
                orderDir: orderDir,
            });
        }
    }, [uniqueid, print, option]);

    const title = "Dati praticha";

    useEffect(() => {
        if (hasLoaded) {
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded]);

    return { data, loading, hasLoaded };
};

export const usePrintTemplateModel = ({
    sectionid,
    category,
    docid,
    print,
}: {
    sectionid: string;
    category: string;
    docid: string | undefined;
    print: boolean;
}) => {
    const { data, loading, hasLoaded, doFetch } = useGetCustom(
        "archive/print-model"
    );

    useEffect(() => {
        if (print && sectionid && category && docid) {
            doFetch(true, {
                sectionid: sectionid,
                category: category,
                docid: docid,
            });
        }
    }, [sectionid, category, docid, print]);
    return { data, loading, hasLoaded };
};
