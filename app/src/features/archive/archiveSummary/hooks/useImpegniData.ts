import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState, useCallback } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { getDeadlinesGrid } from "../../../../utilities/deadlines/deadlinesGridColumn.tsx";
import { useNavigate, useSearchParams } from "react-router-dom";
import { PraticaData } from "..";
import { GridColDef, GridPaginationModel } from "@mui/x-data-grid-pro";

interface DeadlineItem {
    uniqueid: string;
    testo: string;
    data: string;
    tiposcadenzaNome: string;
    users: string;
    status: string;
    important: string;
    onoraridiritti?: string;
    speseimponibili?: string;
    speseesenti?: string;
    speseescluse?: string;
    evasa?: string;
}

interface IDeadlineQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    fileUniqueid: string;
}

type DeadlineList = {
    rows: DeadlineItem[];
    columns: GridColDef[];
    totalRows: number;
    pageIndex: number;
    pageSize: number;
    loading?: boolean;
    page?: number;
};

const DEFAULT_QUERY: IDeadlineQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "data",
    sortOrder: "desc",
    fileUniqueid: "",
};

export const useImpegniData = (praticaData: PraticaData | undefined) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const urlFileUniqueid = searchParams.get("fileUniqueid");
    const navigate = useNavigate();

    const [query, setQuery] = useState<IDeadlineQuery>({
        ...DEFAULT_QUERY,
        fileUniqueid:
            urlFileUniqueid || praticaData?.fileHeader?.uniqueid || "",
    });

    const [list, setList] = useState<DeadlineList>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    const impegniListRequest = useGetCustom(
        "archivedeadlines/list?noTemplateVars=true"
    );

    useEffect(() => {
        const initGrid = async () => {
            const columns = await getDeadlinesGrid(t);
            const filteredColumns = columns.filter(Boolean);
            const columnsWithHideable = filteredColumns.map(col => ({
                ...col,
                hideable: true,
            }));
            setList(prevList => ({
                ...prevList,
                columns: columnsWithHideable,
            }));
        };
        initGrid();
    }, []);

    const fetchData = useCallback(async () => {
        if (!query.fileUniqueid) return;

        try {
            const response: any = await impegniListRequest.doFetch(true, {
                fileUniqueid: query.fileUniqueid,
                page: query.page,
                pageSize: query.pageSize,
                sortColumn: query.sortColumn,
                sortOrder: query.sortOrder,
            });

            if (response && response.data) {
                setList(prevList => ({
                    ...prevList,
                    rows: response.data.currentPage || [],
                    totalRows: parseInt(response.data.totalRows) || 0,
                    pageIndex: query.page,
                    pageSize: query.pageSize,
                }));
            }
        } catch (error) {
            console.error("Error fetching impegni data:", error);
        }
    }, [query]);

    useEffect(() => {
        fetchData();
    }, [query]);

    const handlePageChange = (newModel: GridPaginationModel) => {
        setQuery(prevQuery => ({
            ...prevQuery,
            page: newModel.page,
            pageSize: newModel.pageSize,
        }));
    };

    const handleDeadlineClick = (uniqueId: string) => {
        if (uniqueId) {
            navigate(`/impegno/update/${uniqueId}?fileUniqueid=${query.fileUniqueid}`, {
                state: {
                    fileUniqueid: query.fileUniqueid,
                    isFromArchive: true
                }
            });
        }
    };

    const gridData = list.rows.length > 0 ? list.rows : [];

    return {
        list,
        query,
        setQuery,
        loading: impegniListRequest.loading,
        hasLoaded: impegniListRequest.hasLoaded,
        error: impegniListRequest.error,
        gridData,
        handlePageChange,
        handleDeadlineClick,
        fetchData,
    };
};
