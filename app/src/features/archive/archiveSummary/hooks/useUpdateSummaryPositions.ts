import { useEffect } from "react";
import { CardElement } from "../interface/archivedetail.interface";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useUpdateSummaryPositions = ({
    columns,
    hasLoaded,
}: {
    columns: CardElement[][];
    hasLoaded: boolean;
}) => {
    const { loading, doFetch } = usePostCustom(
        "archive/updatesummarypositions"
    );

    useEffect(() => {
        if (!hasLoaded || (!columns[0].length && !columns[1].length)) {
            return;
        }

        const formData = new FormData();

        const [leftColumn, rightColumn] = columns;

        const leftItems = leftColumn.map(card => ({
            name: card.id,
            hidden: card.hidden,
        }));

        const rightItems = rightColumn.map(card => ({
            name: card.id,
            hidden: card.hidden,
        }));

        leftItems.forEach((item, index) => {
            formData.append(`left[${index}][name]`, item.name);
            formData.append(`left[${index}][hidden]`, item.hidden.toString());
        });

        rightItems.forEach((item, index) => {
            formData.append(`right[${index}][name]`, item.name);
            formData.append(`right[${index}][hidden]`, item.hidden.toString());
        });

        // Only update if we have valid data to send
        if (leftItems.length > 0 || rightItems.length > 0) {
            doFetch(true, formData);
        }
    }, [columns[0].length, columns[1].length, hasLoaded]);

    return { loading };
};
