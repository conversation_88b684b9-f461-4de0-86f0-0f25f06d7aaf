import { useState, useCallback, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import useGetCustom from "../../../../hooks/useGetCustom";
import { IList } from "../../../../interfaces/general.interfaces";
import { getTemplateGrid } from "../../../../utilities/template/gridColumn";
import { useLocation } from "react-router-dom";

const defaultQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "title",
    sortOrder: "asc",
    titleSearch: "",
    authorSearch: "",
    categorySearch: "",
    extensionSearch: "",
    mySearch: "",
    fileUniqueid: "",
};

export default function useTemplatesFilter() {
    const { t } = useTranslation();
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const fileUniqueid = queryParams.get("fileUniqueid") || queryParams.get("uid") || "";

    const [query, setQuery] = useState({
        ...defaultQuery,
        fileUniqueid
    });

    const [fileHeaderData, setFileHeaderData] = useState<any[]>([]);
    const [authors, setAuthors] = useState<any[]>([]);
    const [modelCategories, setModelCategories] = useState<any[]>([]);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterRequest = useGetCustom(
        "archivetemplates/list?noTemplateVars=true"
    );

    const templateRequest = useGetCustom(
        "archivetemplates/templates"
    );

    useEffect(() => {
        const loadMetadata = async () => {
            try {
                const response: any = await templateRequest.doFetch(
                    true,
                    { fileUniqueid }
                );

                if (response?.data) {
                    setFileHeaderData(response.data.fileHeader || []);

                    if (response.data.authors) {
                        setAuthors(response.data.authors);
                    }

                    if (response.data.modelcategories) {
                        setModelCategories(response.data.modelcategories);
                    }
                }
            } catch (error) {
                console.error("Error loading template metadata:", error);
            }
        };

        if (fileUniqueid) {
            loadMetadata();
        }
    }, [fileUniqueid]);

    const fetchData = useCallback(async () => {
        try {
            const finalParams: any = { ...query };

            if (!finalParams.mySearch) {
                finalParams.mySearch = undefined;
            }

            if (finalParams.extensionSearch === "") {
                finalParams.extensionSearch = undefined;
            }

            const [columns, response]: any = await Promise.all([
                getTemplateGrid(t, true),
                filterRequest.doFetch(true, finalParams),
            ]);

            if (response?.data) {
                const { currentPage, totalRows } = response.data;
                setList(prevList => ({
                    ...prevList,
                    rows: currentPage || [],
                    columns: columns || [],
                    totalRows: parseInt(totalRows || "0"),
                    page: query.page,
                    pageSize: query.pageSize,
                }));
            }
        } catch (error) {
            console.error("Error fetching template data:", error);
        }
    }, [query, t]);

    const debouncedTitleSearch = useCallback(
        debounce(() => {
            fetchData();
        }, 500),
        [fetchData]
    );

    useEffect(() => {
        if (query.titleSearch !== defaultQuery.titleSearch) {
            debouncedTitleSearch();
        }
    }, [query.titleSearch, debouncedTitleSearch]);

    useEffect(() => {
        if (query.titleSearch !== defaultQuery.titleSearch) {
            return;
        }

        fetchData();
    }, [
        query.authorSearch,
        query.categorySearch,
        query.extensionSearch,
        query.mySearch,
        query.sortColumn,
        query.sortOrder,
        query.page,
        query.pageSize,
        fetchData
    ]);

    useEffect(() => {
        fetchData();
    }, []);

    return {
        query,
        setQuery,
        list,
        filterData: fetchData,
        defaultQuery,
        loading: filterRequest.loading,
        fileHeaderData,
        authors,
        modelCategories
    };
}
