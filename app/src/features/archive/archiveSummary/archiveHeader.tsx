import { Box, Typo<PERSON>, <PERSON>, Chip } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

interface IHeaderProps {
    fileHeaderData: any;
    title: string;
    buttons?: React.ReactNode[]; 
}

const ArchiveHeader = (props: IHeaderProps) => {
    const { t } = useTranslation();
    const { fileHeaderData, buttons = [], title } = props;

    const {
        codicearchivio,
        id,
        listaclienti,
        listacontroparti,
        color,
        font_color,
        headerArchive
    } = fileHeaderData;

    const renderContent = (content: string) => {
        if (content && content.includes("<a ")) {
            const updatedContent = content.replace(/href="([^"]*)"/g, (_, p1) => {
                return `href="/legacy${p1}"`;
            });

            return (
                <Typography
                    variant="displaySmall"
                    sx={{
                        display: "inline",
                        cursor: "pointer",
                    }}
                    component={Link}
                    underline="hover"
                    dangerouslySetInnerHTML={{ __html: updatedContent }}
                />
            );
        }

        return (
            <Typography variant="displaySmall" sx={{ display: "inline" }}>
                {content}
            </Typography>
        );
    };

    const renderHeader = () => {
        if (headerArchive) {
            return (
                <Typography
                    variant="displaySmall"
                    sx={{
                        display: "inline",
                        cursor: "pointer",
                    }}
                    component={Link}
                    underline="hover"
                    dangerouslySetInnerHTML={{ __html: headerArchive }}
                />
            );
        }

        return (
            <>
                {renderContent(listaclienti)}
                <Typography
                    variant="displaySmall"
                    sx={{
                        display: "inline",
                        marginLeft: "8px",
                        marginRight: "8px",
                        whiteSpace: "nowrap",
                    }}
                >
                    {t("contro")}
                </Typography>
                {renderContent(listacontroparti)}
            </>
        );
    };

    return (
        <>
           
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    padding: "12px 16px",
                    backgroundColor: "#fff",
                }}
            >
                
                <Box sx={{ display: "flex", flexDirection: "column" }}>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            flexWrap: "nowrap",
                            whiteSpace: "nowrap",
                            marginTop: "-8px",
                            width: "200px",
                        }}
                    >
                        {renderHeader()}
                    </Box>
                </Box>

               
                <Box sx={{ textAlign: "right" }}>
                    {codicearchivio && (
                        <Chip
                            label={
                                <Typography variant="h6" sx={{ marginBottom: "4px" }}>
                                    {codicearchivio}
                                </Typography>
                            }
                            style={{
                                backgroundColor: color,
                                color: font_color,
                            }}
                            sx={{ marginBottom: "10px" }}
                        />
                    )}
                    <Typography variant="body2" sx={{ color: "#666", fontWeight: 'bold' }}>
                        {id ? t(`CODICE PRATICA: ${id}`) : t("CODICE PRATICA: Non specificato")}
                    </Typography>
                </Box>
            </Box>

            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "8px 16px 16px",
                    borderBottom: "1px solid #ddd",
                    backgroundColor: "#fff",
                }}
            >
                
                <Typography variant="h6" >
                    {title}
                </Typography>

                
                <Box
                    sx={{
                        display: "flex",
                        gap: "8px",
                    }}
                >
                    {buttons.map((button, index) => (
                        <Box key={index}>{button}</Box>
                    ))}
                </Box>
            </Box>
        </>
    );
};

export default ArchiveHeader;
