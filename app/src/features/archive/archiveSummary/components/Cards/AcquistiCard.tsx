import { Typography } from "@vapor/react-extended";
import { fileHeader } from "../../interface/archivedetail.interface";
import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { formatWithDefault } from "../../../../../utilities/utils";

const transformAltriData = (fileHeader: fileHeader) => {
    return [
        {
            label: "Id",
            value: formatWithDefault(fileHeader.ordineAcquisto_id),
        },
        {
            label: "Data",
            value: formatWithDefault(fileHeader.ordineAcquisto_data),
        },
        {
            label: "Num Item",
            value: formatWithDefault(fileHeader.ordineAcquisto_numItem),
        },
        {
            label: "Codice commessa / Convenzione",
            value: formatWithDefault(
                fileHeader.ordineAcquisto_codiceCommessaConvenzione
            ),
        },
        {
            label: "Codice CUP",
            value: formatWithDefault(fileHeader.ordineacquisto_cup),
        },
        {
            label: "Codice CIG",
            value: formatWithDefault(fileHeader.ordineacquisto_cig),
        },
    ];
};

export const AcquistiCard = ({
    fileHeader,
}: {
    fileHeader: fileHeader | undefined;
}) => {
    const { t } = useTranslation();

    return (
        <Stack>
            <Stack
                direction="row"
                justifyContent="space-between"
                pb={3}>
                <Typography variant="displayMedium">
                    {t("Dati acquisto")}
                </Typography>
                <Button variant="outlined">{t("Modifica")}</Button>
            </Stack>
            <Table size="small">
                <TableHead>
                    <TableRow>
                        <TableCell />
                        <TableCell />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {fileHeader &&
                        transformAltriData(fileHeader).map(row => (
                            <TableRow key={row.label}>
                                <TableCell>{t(row.label)}</TableCell>
                                <TableCell>{row.value}</TableCell>
                            </TableRow>
                        ))}
                </TableBody>
            </Table>
        </Stack>
    );
};
