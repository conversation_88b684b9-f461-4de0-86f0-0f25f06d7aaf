import { <PERSON>, Divider, <PERSON>, Stack } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";
import { UltimiEventiCardProps } from "../../interface/archivedetail.interface";

export const UltimiEventiCard = ({
    agendaList,
    deadlinesList,
    documentsList,
    fileHeader,
}: UltimiEventiCardProps) => {
    const { t } = useTranslation();
    return (
        <Box>
            <Typography variant="displayMedium">
                {t("Ultimi eventi e documenti")}
            </Typography>
            <Stack gap={3}>
                <Box>
                    <Typography variant="subtitle1">
                        {t("Ultime udienze")}:
                    </Typography>
                    <Stack>
                        {agendaList &&
                            agendaList.map((item, index) => (
                                <Typography
                                    key={`agenda-${index}`}
                                    variant="body2"
                                    noWrap>
                                    {item.data} - {item.attivita}
                                </Typography>
                            ))}
                    </Stack>
                    {fileHeader && (
                        <Box>
                            <Link
                                href={`/archiveagenda/agenda?fileUniqueid=${fileHeader.uniqueid}`}
                                underline="hover">
                                Vedi tutte...
                            </Link>
                        </Box>
                    )}

                    <Divider variant="fullWidth" />
                </Box>
                <Box>
                    <Typography variant="subtitle1">
                        {t("Ultimi impegni")}:
                    </Typography>
                    <Stack>
                        {deadlinesList &&
                            deadlinesList.map((item, index) => (
                                <Typography
                                    key={`deadline-${index}`}
                                    variant="body2"
                                    noWrap>
                                    {item.data} - {item.testo}
                                </Typography>
                            ))}
                    </Stack>
                    {fileHeader && (
                        <Box>
                            <Link
                                href={`/archivedeadlines/deadlines?fileUniqueid=${fileHeader.uniqueid}`}
                                underline="hover">
                                Vedi tutte...
                            </Link>
                        </Box>
                    )}

                    <Divider variant="fullWidth" />
                </Box>
                <Box>
                    <Typography variant="subtitle1">
                        {t("Documenti fascicolo")}:
                    </Typography>
                    <Stack>
                        {documentsList &&
                            documentsList.map((item, index) => (
                                <Typography
                                    key={`document-${index}`}
                                    variant="body2"
                                    noWrap>
                                    {item.data ? `${item.data} - ` : ""}
                                    {item.titolodocumento}
                                </Typography>
                            ))}
                    </Stack>
                    {fileHeader && (
                        <Box>
                            <Link
                                href={`/archivedocuments/documents?fileUniqueid=${fileHeader.uniqueid}`}
                                underline="hover">
                                {t("Vedi tutte...")}
                            </Link>
                        </Box>
                    )}
                </Box>
            </Stack>
        </Box>
    );
};
