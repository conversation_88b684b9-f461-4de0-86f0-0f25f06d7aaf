import { Typography } from "@vapor/react-extended";
import { fileHeader } from "../../interface/archivedetail.interface";
import { useTranslation } from "@1f/react-sdk";
import { Stack } from "@vapor/react-material";

const split = (str: string): string[] => {
    return str ? str.split("<br>").filter(value => value !== "") : [];
};

export const TagCard = ({
    fileHeader,
}: {
    fileHeader: fileHeader | undefined;
}) => {
    const { t } = useTranslation();
    return (
        <Stack gap={1}>
            <Typography variant="displayMedium">{t("Tags")}</Typography>
            {fileHeader?.tags ? (
                split(fileHeader?.tags).map(tag => (
                    <Typography key={tag}>{tag}</Typography>
                ))
            ) : (
                <Typography>
                    {t(
                        "Al momento non sono stati inseriti tag all'interno della pratica"
                    )}
                </Typography>
            )}
        </Stack>
    );
};
