import { Typography } from "@vapor/react-extended";
import { fileHeader } from "../../interface/archivedetail.interface";
import { useTranslation } from "@1f/react-sdk";
import { Stack } from "@vapor/react-material";

const split = (str: string | undefined): string[] => {
    return str ? str.split("\n").filter(value => value !== "") : [];
};

export const AnotazioniCard = ({
    fileHeader,
}: {
    fileHeader: fileHeader | undefined;
}) => {
    const { t } = useTranslation();

    return (
        <Stack gap={1}>
            <Typography variant="displayMedium">{t("Annotazioni")}</Typography>
            {fileHeader?.annotazioni ? (
                split(fileHeader?.annotazioni).map(annotazioni => (
                    <Typography key={annotazioni}>{annotazioni}</Typography>
                ))
            ) : (
                <Typography>
                    {t(
                        "Al momento non sono stati inseriti tag all'interno della pratica"
                    )}
                </Typography>
            )}
        </Stack>
    );
};
