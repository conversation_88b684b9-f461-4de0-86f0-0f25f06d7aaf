import { useTranslation } from "@1f/react-sdk";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import { PracticeSearch } from "../../../../../custom-components/PracticeSearch";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import {
    Button,
    FormControl,
    FormLabel,
    MenuItem,
    NotificationInline,
    Select,
    Stack,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faCheck } from "@fortawesome/free-solid-svg-icons";
import { useNavigate, useSearchParams } from "react-router-dom";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { Practice } from "../../../../../interfaces/documents.interface";
import SpinnerButton from "../../../../../custom-components/SpinnerButton";

export interface AddLinkedFileProps {
    open: boolean;
    setOpenLinkFile: Dispatch<SetStateAction<boolean>>;
    onLinkSuccess?: () => void;
}

interface LinkResponse {
    msg: string;
    saved: boolean;
}

const values = [
    { label: "Codice archivio", value: "codicearchivio" },
    { label: "Codice pratica", value: "codicepratica" },
    { label: "Descrizione pratica", value: "descrizionepratica" },
    { label: "Oggetto", value: "oggettopratica" },
    { label: "Controparte", value: "controparte" },
    { label: "Cliente", value: "cliente" },
    { label: "RG", value: "RG" },
];

export const AddLinkedFile = ({
    open,
    setOpenLinkFile,
    onLinkSuccess,
}: AddLinkedFileProps) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uniqueid = searchParams.get("uid");
    const navigate = useNavigate();
    const [inputValue, setInputValue] = useState("");
    const [selectedValue, setSelectedValue] = useState("codicearchivio");
    const [selectedPractice, setSelectedPractice] = useState<Practice | null>(
        null
    );
    const [linkFileReponse, setLinkFileReponse] = useState<LinkResponse>();

    const linkFileRequest = useGetCustom(
        "archive/link-file?noTemplateVars=true"
    );

    const handleLinkFiles = () => {
        linkFileRequest.doFetch(true, {
            choosedFileUniqueid: selectedPractice?.uniqueid,
            currentFileUniqueid: uniqueid,
        });
    };

    useEffect(() => {
        if (linkFileRequest.hasLoaded) {
            setLinkFileReponse(linkFileRequest.data);
            onLinkSuccess?.();
        } else {
            setLinkFileReponse(undefined);
        }
    }, [linkFileRequest.hasLoaded, linkFileRequest.loading]);

    const resetState = () => {
        setSelectedPractice(null);
        setInputValue("");
        setSelectedValue("codicearchivio");
        setLinkFileReponse(undefined);
    };

    const handleConfirm = () => {
        setOpenLinkFile(false);
        resetState();
    };

    const handleDecline = () => {
        setOpenLinkFile(false);
        resetState();
    };

    const goToPractice = () => {
        selectedPractice?.uniqueid &&
            navigate(`/archive/summary?uid=${selectedPractice?.uniqueid}`);
    };

    const handleChangeValue = (e: any) => {
        setSelectedValue(e.target.value);
        setSelectedPractice(null);
        setLinkFileReponse(undefined);
    };

    const handlePracticeChange = (_: any, value: Practice | string | null) => {
        setSelectedPractice(value as Practice | null);
        setLinkFileReponse(undefined);
        if (!value) {
            setInputValue("");
        }
    };

    const handleInputChange = (_: any, value: string) => {
        setInputValue(value);
        if (!value) {
            setSelectedPractice(null);
        }
    };

    const errorMessage = t("Le due pratiche risultano già collegate");
    const successMessage = t("Pratiche collegate");

    return (
        <ConfirmModal
            decline={t("Chiudi")}
            handleAgree={handleConfirm}
            handleDecline={handleDecline}
            open={open}
            title={t("Pratica")}
            customActions={
                selectedPractice && !linkFileReponse?.saved
                    ? [
                          <Button
                              key="1"
                              variant="outlined"
                              startIcon={<FontAwesomeIcon icon={faArrowLeft} />}
                              onClick={goToPractice}>
                              {t("Vai alla pratica")}
                          </Button>,
                          <SpinnerButton
                              key="2"
                              isLoading={linkFileRequest.loading}
                              variant="contained"
                              startIcon={<FontAwesomeIcon icon={faCheck} />}
                              onClick={handleLinkFiles}
                              label={t("Collega")}
                          />,
                      ]
                    : []
            }>
            <Stack gap={2}>
                <FormControl>
                    <FormLabel>{t("Ricerca pratica per")}</FormLabel>
                    <Select
                        value={selectedValue}
                        onChange={handleChangeValue}>
                        {values.map(value => (
                            <MenuItem
                                value={value.value}
                                key={value.value}>
                                {t(value.label)}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <PracticeSearch
                    width={400}
                    inputValue={inputValue}
                    value={selectedPractice}
                    onChange={handlePracticeChange}
                    onInputChange={handleInputChange}
                    query=""
                    from=""
                />
                {linkFileReponse !== undefined &&
                    linkFileRequest.loading === false && (
                        <NotificationInline
                            variant="outlined"
                            severity={
                                linkFileReponse?.saved ? "success" : "warning"
                            }>
                            {linkFileReponse?.saved
                                ? successMessage
                                : errorMessage}
                        </NotificationInline>
                    )}
            </Stack>
        </ConfirmModal>
    );
};
