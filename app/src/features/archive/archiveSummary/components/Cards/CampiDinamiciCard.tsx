import {
    <PERSON><PERSON>,
    <PERSON>ack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { formatWithDefault } from "../../../../../utilities/utils";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import {
    CampiDinamiciCardProps,
    Campo,
    LabelValue,
} from "../../interface/archivedetail.interface";

function getDynamicFields(campi: Campo[] = []): LabelValue[] {
    return campi.map(campo => {
        const base = { label: campo.nome };

        switch (campo.tipo) {
            case "checkbox":
                return {
                    ...base,
                    value: campo.valore === "true" ? "Si" : "No",
                };

            case "date":
                return {
                    ...base,
                    value:
                        campo.valore === "01/01/1970"
                            ? "-"
                            : formatWithDefault(campo.valore),
                };

            case "select":
                return {
                    ...base,
                    value: formatWithDefault(campo.select),
                };

            case "regex":
            case "memo":
            default:
                return {
                    ...base,
                    value: formatWithDefault(campo.valore),
                };
        }
    });
}

export const CampiDinamiciCard = ({
    preview_campi,
}: CampiDinamiciCardProps) => {
    const { t } = useTranslation();
    return (
        <>
            <Stack
                pb={3}
                direction="row"
                justifyContent="space-between">
                <Typography variant="displayMedium">
                    {t("Campi dinamici")}
                </Typography>
                <Button variant="outlined">{t("Modifica")}</Button>
            </Stack>
            {!preview_campi || preview_campi.length === 0 ? (
                <Typography>
                    {t("Al momento non vi sono campi dinamici per la pratica")}
                </Typography>
            ) : (
                <TableContainer>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell></TableCell>
                                <TableCell></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getDynamicFields(preview_campi).map(field => (
                                <TableRow key={field.label}>
                                    <TableCell>{t(field.label)}</TableCell>
                                    <TableCell>{field.value}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}
        </>
    );
};
