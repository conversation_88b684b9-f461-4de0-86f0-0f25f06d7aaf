import { AltriDati } from "../../interface/archivedetail.interface";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import {
    Button,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
} from "@vapor/react-material";

import { formatWithDefault } from "../../../../../utilities/utils";

const transformAltriDati = (
    altriDati: AltriDati
): { label: string; value: string }[] => {
    return [
        {
            label: "Protocollo generale",
            value: formatWithDefault(altriDati.general_protocol),
        },
        {
            label: "Codice fascicolo",
            value: formatWithDefault(altriDati.externalCode),
        },
        { label: "Situazione", value: formatWithDefault(altriDati.situazione) },
        {
            label: "Situazione contabile",
            value: formatWithDefault(altriDati.situazione_contabile),
        },
        {
            label: "Centro profitto",
            value: formatWithDefault(altriDati.centro_profitto),
        },
        { label: "Stanza", value: formatWithDefault(altriDati.stanza) },
        { label: "Palchetto", value: formatWithDefault(altriDati.palchetto) },
        { label: "Scaffale", value: formatWithDefault(altriDati.scaffale) },
        { label: "Faldone", value: formatWithDefault(altriDati.faldone) },
        { label: "Scatolone", value: formatWithDefault(altriDati.scatolone) },
        {
            label: "Data macero",
            value: formatWithDefault(altriDati.data_macero),
        },
    ];
};

export const AltriDatiCard = ({
    altriDati,
}: {
    altriDati: AltriDati | undefined;
}) => {
    const { t } = useTranslation();

    return (
        <Stack>
            <Stack
                direction="row"
                justifyContent="space-between"
                pb={3}>
                <Typography variant="displayMedium">
                    {t("Altri dati")}
                </Typography>
                <Button variant="outlined">{t("Modifica")}</Button>
            </Stack>
            <Table size="small">
                <TableHead>
                    <TableRow>
                        <TableCell />
                        <TableCell />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {altriDati &&
                        transformAltriDati(altriDati).map(row => (
                            <TableRow key={row.label}>
                                <TableCell>{t(row.label)}</TableCell>
                                <TableCell>{row.value}</TableCell>
                            </TableRow>
                        ))}
                </TableBody>
            </Table>
        </Stack>
    );
};
