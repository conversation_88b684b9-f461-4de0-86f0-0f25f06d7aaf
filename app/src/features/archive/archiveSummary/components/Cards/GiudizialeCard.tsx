import { Typography } from "@vapor/react-extended";
import { fileHeader } from "../../interface/archivedetail.interface";
import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { formatWithDefault } from "../../../../../utilities/utils";

const formatRg = (fileHeader: fileHeader): string => {
    if (
        fileHeader.ruologeneralenumero !== "0" &&
        fileHeader.ruologeneraleanno !== "0"
    ) {
        let result = `${fileHeader.ruologeneralenumero}/${fileHeader.ruologeneraleanno}`;
        if (fileHeader.subprocedimento) {
            result += `-${fileHeader.subprocedimento}`;
        }
        return result;
    }
    return "-";
};

const formatSentenza = (fileHeader: fileHeader): string => {
    if (
        fileHeader.numero_sentenza !== "" &&
        fileHeader.numero_sentenza_anno !== "0"
    ) {
        return `${fileHeader.numero_sentenza}/${fileHeader.numero_sentenza_anno}`;
    }
    return "-";
};

const formatDecretoIngiuntivo = (fileHeader: fileHeader): string => {
    if (
        fileHeader.numerodecretoingiuntivo !== "" &&
        fileHeader.numerodecretoingiuntivoanno !== "0"
    ) {
        return `${formatWithDefault(
            fileHeader.numerodecretoingiuntivo
        )}/${formatWithDefault(fileHeader.numerodecretoingiuntivoanno)}`;
    }
    return "-";
};

const transformSummary = (fileHeader: fileHeader) => {
    return [
        ...(fileHeader.ref_pwid !== "-4"
            ? [
                  { label: "R.G.", value: formatRg(fileHeader) },
                  { label: "Sentenza", value: formatSentenza(fileHeader) },
                  {
                      label: "Decreto ingiuntivo",
                      value: formatDecretoIngiuntivo(fileHeader),
                  },
                  ...(fileHeader.ref_pwid === "1"
                      ? [
                            {
                                label: "Emissione D. I.",
                                value: formatWithDefault(
                                    fileHeader.emissione_di
                                ),
                            },
                            {
                                label: "Notifica D. I.",
                                value: formatWithDefault(
                                    fileHeader.notifica_di
                                ),
                            },
                            {
                                label: "Decreto esecutorietà",
                                value: formatWithDefault(
                                    fileHeader.decreto_esecutorieta
                                ),
                            },
                            {
                                label: "Provvedimento",
                                value: formatWithDefault(
                                    fileHeader.provvedimento
                                ),
                            },
                            {
                                label: "Formula esecutiva",
                                value: formatWithDefault(
                                    fileHeader.formula_esecutiva
                                ),
                            },
                            {
                                label: "Formula protesto",
                                value: formatWithDefault(
                                    fileHeader.formula_protesto
                                ),
                            },
                            {
                                label: "Not. precetto",
                                value: formatWithDefault(
                                    fileHeader.notifica_precetto
                                ),
                            },
                            {
                                label: "Not. pignoramento",
                                value: formatWithDefault(
                                    fileHeader.notifica_pignoramento
                                ),
                            },
                        ]
                      : []),
                  {
                      label: "Autorità",
                      value: formatWithDefault(fileHeader.ufficio_giudiziario),
                  },
                  {
                      label: "Procuratore",
                      value: formatWithDefault(fileHeader.procuratore),
                  },
              ]
            : []),

        { label: "Giudice", value: formatWithDefault(fileHeader.giudice) },
        { label: "Sezione", value: formatWithDefault(fileHeader.sezione) },
        { label: "Dominus", value: formatWithDefault(fileHeader.dominus) },

        ...(fileHeader.ref_pwid !== "-4"
            ? [
                  {
                      label: "Avvocati cointestatari",
                      value: formatWithDefault(fileHeader.cointestatari),
                  },
                  ...(fileHeader.cointestatariAnagrafiche
                      ? [
                            {
                                label: "Cointestatari Esterni",
                                value: formatWithDefault(
                                    fileHeader.cointestatariAnagrafiche
                                ),
                            },
                        ]
                      : []),
                  {
                      label: "Altri avvocati",
                      value: formatWithDefault(fileHeader.listaavversari),
                  },
              ]
            : []),
    ];
};

export const GiudizialeCard = ({
    fileHeader,
}: {
    fileHeader: fileHeader | undefined;
}) => {
    const { t } = useTranslation();

    return (
        <Stack>
            <Stack
                direction="row"
                justifyContent="space-between"
                pb={3}>
                <Typography variant="displayMedium">
                    {t("Dati Giudiziale")}
                </Typography>
                <Button variant="outlined">{t("Modifica")}</Button>
            </Stack>
            <Table size="small">
                <TableHead>
                    <TableRow>
                        <TableCell />
                        <TableCell />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {fileHeader &&
                        transformSummary(fileHeader).map(row => (
                            <TableRow key={row.label}>
                                <TableCell>{t(row.label)}</TableCell>
                                <TableCell>{row.value}</TableCell>
                            </TableRow>
                        ))}
                </TableBody>
            </Table>
        </Stack>
    );
};
