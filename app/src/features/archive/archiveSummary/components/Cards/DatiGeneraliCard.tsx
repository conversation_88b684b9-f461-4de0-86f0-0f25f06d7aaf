import { fileHeader } from "../../interface/archivedetail.interface";
import {
    <PERSON><PERSON>,
    <PERSON>ack,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import { formatWithDefault } from "../../../../../utilities/utils";

const translateRuolo = (code: string): string => {
    const roles: { [key: string]: string } = {
        AVV: "Avvocato",
        AVVPA: "Dip. PA",
        CTU: "Consulente tecnico d'ufficio",
        AVVCUR: "Curatore (Per avvocati)",
        CTUCUR: "Curatore (Per consulente tecnico d'ufficio)",
        CTUCUS: "Custode",
        CTUDEL: "Delegato (Per consulente tecnico d'ufficio)",
        CTUAUS: "Ausiliario incaricato (Per consulente tecnico d'ufficio)",
        CTUTUT: "Curatore/Tutore/Amm. Di <PERSON> (Per consulente tecnico d'ufficio)",
        PARTE: "Parte in causa",
        CFENTE: "Parte in causa (CF Ente)",
    };
    return roles[code] || "-";
};

const transformFileHeader = (
    fileHeader: fileHeader
): Array<{ label: string; value: string }> => {
    return [
        ...(fileHeader.is_archived === "1" && fileHeader.data_archivio
            ? [
                  {
                      label: "Archiviata in data",
                      value: formatWithDefault(fileHeader.data_archivio),
                  },
              ]
            : []),
        {
            label: "Tipologia",
            value: formatWithDefault(fileHeader.tipologiapratica),
        },
        ...(fileHeader.tipologi_riforma_cartabia === "1"
            ? [
                  {
                      label: "Riforma Cartabia",
                      value: fileHeader.riforma_cartabia === "1" ? "SI" : "NO",
                  },
              ]
            : []),
        { label: "Sede", value: formatWithDefault(fileHeader.sede) },
        {
            label: "Nome pratica",
            value: formatWithDefault(fileHeader.nomepratica),
        },
        {
            label: "Codice archivio",
            value: formatWithDefault(fileHeader.codicearchivio),
        },
        { label: "Codice pratica", value: formatWithDefault(fileHeader.id) },
        {
            label: "Descrizione",
            value: formatWithDefault(fileHeader.descrizione),
        },
        // { label: "Categoria", value: formatApiValue(fileHeader.nome_categoria) },
        {
            label: "Responsabile",
            value: formatWithDefault(fileHeader.referent_id),
        },
        {
            label: "Data apertura",
            value: formatWithDefault(fileHeader.dataapertura),
        },
        ...(fileHeader.data_chiusura
            ? [
                  {
                      label: "Data chiusura",
                      value: formatWithDefault(fileHeader.data_chiusura),
                  },
              ]
            : []),
        {
            label: "Valuta",
            value: fileHeader.currency
                ? `${fileHeader.currency} (${fileHeader.currency_sym || "€"})`
                : "Euro (€)",
        },
        {
            label: "Valore",
            value:
                fileHeader.valore === "-1"
                    ? "Indeterminato medio"
                    : fileHeader.valore === "-2"
                    ? "Indeterminato modesto"
                    : fileHeader.valore === "-3"
                    ? "Indeterminato rilevante"
                    : `${fileHeader.currency_sym || "€"} ${fileHeader.valore}`,
        },
        {
            label: "Tipo valore",
            value:
                fileHeader.tipo_valore === "0"
                    ? "Passivo"
                    : fileHeader.tipo_valore === "1"
                    ? "Attivo"
                    : "-",
        },
        {
            label: "Importo a forfait",
            value: `${fileHeader.currency_sym || "€"} ${formatWithDefault(
                fileHeader.importo_forfait,
                "0.00"
            )}`,
        },
        // {
        // label: "Studi di settore",
        // value: formatApiValue(fileHeader.quadro_d_description),
        // },
        {
            label: "Città",
            value: formatWithDefault(fileHeader.ufficio_giudiziario_citta),
        },
        { label: "Stato", value: formatWithDefault(fileHeader.stato) },
        // { label: "Esito", value: formatApiValue(fileHeader.esito_description) },
        { label: "Oggetto", value: formatWithDefault(fileHeader.oggetto) },
        { label: "Avvocato", value: formatWithDefault(fileHeader.avvocato) },
        { label: "Emittente", value: formatWithDefault(fileHeader.emittente) },
        {
            label: "Ruolo consultazione",
            value: translateRuolo(fileHeader.ruolo_avvocato),
        },
        {
            label: "Altri soggetti",
            value: formatWithDefault(fileHeader.listaaltrisoggetti),
        },
        {
            label: "Tariffa oraria",
            value: `${fileHeader.currency_sym || "€"} ${formatWithDefault(
                fileHeader.hourly_rate,
                "0.00"
            )}`,
        },
        {
            label: "SmartMailer email",
            value: formatWithDefault(fileHeader.smartmailer_address),
        },
        {
            label: "SmartMailer carica",
            value: formatWithDefault(fileHeader.smartmailer_upload),
        },
        {
            label: "SmartMailer email e carica",
            value: formatWithDefault(fileHeader.smartmailer_both),
        },
    ];
};

export const DatiGeneraliCard = ({
    fileHeader,
}: {
    fileHeader: fileHeader | undefined;
}) => {
    const { t } = useTranslation();

    return (
        <Stack>
            <Stack
                direction="row"
                justifyContent="space-between"
                pb={3}>
                <Typography variant="displayMedium">
                    {t("Dati Generali")}
                </Typography>
                <Button variant="outlined">{t("Modifica")}</Button>
            </Stack>
            <Table size="small">
                <TableHead>
                    <TableRow>
                        <TableCell />
                        <TableCell />
                    </TableRow>
                </TableHead>
                <TableBody>
                    {fileHeader &&
                        transformFileHeader(fileHeader).map(row => (
                            <TableRow key={row.label}>
                                <TableCell>{t(row.label)}</TableCell>
                                <TableCell>{row.value}</TableCell>
                            </TableRow>
                        ))}
                </TableBody>
            </Table>
        </Stack>
    );
};
