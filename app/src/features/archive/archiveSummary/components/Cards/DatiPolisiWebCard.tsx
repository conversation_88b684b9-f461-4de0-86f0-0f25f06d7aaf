import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Paper,
    Typography,
    Stack,
} from "@vapor/react-material";
import { formatWithDefault } from "../../../../../utilities/utils";
import { useTranslation } from "@1f/react-sdk";
import { PolisWebData } from "../../interface/archivedetail.interface";

const transformPolisWebData = (polisWebData: PolisWebData | undefined) => {
    if (!polisWebData || !polisWebData.data) {
        return [];
    }

    const { data } = polisWebData;

    const rows = [
        {
            label: "Atto introduttivo",
            value: formatWithDefault(data.atto_introduttivo),
        },
        {
            label: "Contributo unificato",
            value: data.contributo_unificato
                ? `€ ${formatWithDefault(data.contributo_unificato)}`
                : "-",
        },
        { label: "Costituzione", value: formatWithDefault(data.costituzione) },
        {
            label: "Data citazione",
            value: formatWithDefault(data.data_citazione),
        },
        {
            label: "Data iscrizione",
            value: formatWithDefault(data.data_iscrizione),
        },
        { label: "Grado", value: formatWithDefault(data.grado) },
        { label: "Giudice", value: formatWithDefault(data.giudice) },
        { label: "Materia", value: formatWithDefault(data.materia) },
        ...(data.numero_sezionale && data.anno_sezionale
            ? [
                  {
                      label: "Numero sezionale",
                      value: `${formatWithDefault(
                          data.numero_sezionale
                      )}/${formatWithDefault(data.anno_sezionale)}`,
                  },
              ]
            : [
                  {
                      label: "Numero sezionale",
                      value: "-",
                  },
              ]),
        {
            label: "Prima comparizione",
            value: formatWithDefault(data.data_prima_comparizione),
        },
        { label: "Rito", value: formatWithDefault(data.rito) },
        { label: "Ruolo", value: formatWithDefault(data.ruolo) },
        { label: "Sezione", value: formatWithDefault(data.sezione) },
        {
            label: "Ultima sincronizzazione",
            value: formatWithDefault(data.data_ultima_sincronizzazione),
        },
    ];

    return rows;
};

export const DatiPolisiweb = ({
    datipolisiweb,
}: {
    datipolisiweb: PolisWebData | undefined;
}) => {
    const { t } = useTranslation();
    const rows = transformPolisWebData(datipolisiweb);

    return (
        <Stack>
            <Typography variant="displayMedium">
                {t("Dati Polisweb")}
            </Typography>
            {rows.length === 0 ? (
                <Typography>
                    {t("Nessun dato disponibile per questa pratica")}
                </Typography>
            ) : (
                <TableContainer
                    component={Paper}
                    sx={{ mt: 2 }}>
                    <Table>
                        <TableBody>
                            {rows.map((row, index) => (
                                <TableRow key={index}>
                                    <TableCell
                                        component="th"
                                        scope="row">
                                        {row.label}
                                    </TableCell>
                                    <TableCell>{row.value}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            )}
        </Stack>
    );
};
