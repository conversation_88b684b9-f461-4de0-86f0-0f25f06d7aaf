import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import {
    Button,
    Hyperlink,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import type { fileHeader } from "../../interface/archivedetail.interface";
import { formatWithDefault } from "../../../../../utilities/utils";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";
import { useEffect, useState, memo } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { useSearchParams } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { AddLinkedFile } from "./AddLinkedFile";
import Spinner from "../../../../../custom-components/Spinner";

const PraticheCollegateCardComponent = () => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");
    const [showDelete, setConfirmDelete] = useState(false);
    const [selectedFile, setSelectedFile] = useState<string>();
    const [openLinkFile, setOpenLinkFile] = useState(false);

    const [linkedFiles, setLinkedFiles] = useState<fileHeader["linkedFiles"]>(
        []
    );
    const [netlexSettings, setNetlexSettings] = useState<any>();

    const getSummaryRequest = useGetCustom("archive/summary");
    const removeFileRequest = useGetCustom("archive/remove-linked-file");

    const refreshData = () => {
        getSummaryRequest.doFetch(true, { uid });
    };

    useEffect(() => {
        refreshData();
    }, [uid]);

    useEffect(() => {
        if (getSummaryRequest.hasLoaded) {
            setLinkedFiles(
                getSummaryRequest.data?.fileHeader?.linkedFiles || []
            );
            setNetlexSettings(getSummaryRequest.data.netlexSettings);
        }
    }, [getSummaryRequest.hasLoaded, getSummaryRequest.loading]);

    const handleRemoveFile = (fileId: string, fileUid: string) => {
        removeFileRequest.doFetch(true, { id: fileId, fUid: fileUid });
    };

    useEffect(() => {
        if (removeFileRequest.hasLoaded) {
            setConfirmDelete(false);
            setSelectedFile(undefined);
            refreshData();
        }
    }, [removeFileRequest.hasLoaded, removeFileRequest.loading]);

    const showFileCodeColumn =
        netlexSettings &&
        netlexSettings.file_id !== "none" &&
        netlexSettings.file_code === "fileCode";
    const showFileArchiveCodeColumn =
        netlexSettings &&
        netlexSettings.file_id !== "none" &&
        netlexSettings.file_code === "fileArchiveCode";

    const handleCloseDelete = () => {
        setConfirmDelete(false);
        setSelectedFile(undefined);
    };

    const handleAgree = () => {
        if (selectedFile && uid) {
            handleRemoveFile(selectedFile, uid);
        }
    };

    const handleOpenAddFileModal = () => {
        setOpenLinkFile(true);
    };

    return (
        <>
            <AddLinkedFile
                open={openLinkFile}
                setOpenLinkFile={setOpenLinkFile}
                onLinkSuccess={refreshData}
            />
            <ConfirmModal
                agree={t("Conferma")}
                decline={t("Annulla")}
                handleAgree={handleAgree}
                handleDecline={handleCloseDelete}
                colorConfirmButton="error"
                open={showDelete}
                confirmText={t(
                    "Il collegamento a questa pratica verrà rimosso. Proseguire?"
                )}
                title={t("Atenzione")}
                loading={removeFileRequest.loading}
            />
            <Stack
                pb={3}
                direction="row"
                justifyContent="space-between">
                <Typography variant="displayMedium">
                    {t("Pratiche collegate")}
                </Typography>
                <Button
                    variant="outlined"
                    startIcon={<FontAwesomeIcon icon={faPlus} />}
                    onClick={handleOpenAddFileModal}>
                    {t("Aggiungi")}
                </Button>
            </Stack>
            {getSummaryRequest.loading ? (
                <Spinner fullPage={false} />
            ) : linkedFiles === null ||
              linkedFiles === undefined ||
              linkedFiles.length === 0 ? (
                <Typography>
                    {t("Al momento non sono presenti collegamenti a pratiche")}
                </Typography>
            ) : (
                <TableContainer>
                    {removeFileRequest.loading ? (
                        <Spinner fullPage={false} />
                    ) : (
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>{t("Clienti")}</TableCell>
                                    <TableCell>{t("Controparti")}</TableCell>
                                    {showFileCodeColumn && (
                                        <TableCell>
                                            {t("Cod. pratica")}
                                        </TableCell>
                                    )}
                                    {showFileArchiveCodeColumn && (
                                        <TableCell>
                                            {t("Cod. archivio")}
                                        </TableCell>
                                    )}
                                    <TableCell>{t("R.G.(N.R.)")}</TableCell>
                                    <TableCell></TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {linkedFiles &&
                                    linkedFiles.map(file => (
                                        <TableRow key={file.uniqueid}>
                                            <TableCell>
                                                {formatWithDefault(
                                                    file.clienti
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {formatWithDefault(
                                                    file.controparti
                                                )}
                                            </TableCell>
                                            {showFileCodeColumn && (
                                                <TableCell>
                                                    {formatWithDefault(
                                                        file.fId
                                                    )}
                                                </TableCell>
                                            )}
                                            {showFileArchiveCodeColumn && (
                                                <TableCell>
                                                    {formatWithDefault(
                                                        file.codicearchivio
                                                    )}
                                                </TableCell>
                                            )}
                                            <TableCell>
                                                <Hyperlink
                                                    href={`/archive/summary?uid=${file.uniqueid}`}>
                                                    {file.rg}
                                                </Hyperlink>
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    color="error"
                                                    onClick={() => {
                                                        setSelectedFile(
                                                            file.id
                                                        );
                                                        setConfirmDelete(true);
                                                    }}>
                                                    {t("Rimuovi")}
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                            </TableBody>
                        </Table>
                    )}
                </TableContainer>
            )}
        </>
    );
};

export const PraticheCollegateCard = memo(PraticheCollegateCardComponent);
