import { useTranslation } from "@1f/react-sdk";
import {
    CircularProgress,
    ListItemText,
    MenuItem,
} from "@vapor/react-material";
import { useSearchParams } from "react-router-dom";
import { Dispatch, SetStateAction, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { saveFile } from "../../../../utilities/utils";

export const PrintArchiveDeadlinesList = ({
    setSaveFileError,
}: {
    setSaveFileError: Dispatch<SetStateAction<boolean>>;
}) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");

    const printRequest = useGetCustom("archivedeadlines/printlist", {}, null, true);

    const title = "Lista_impegni";

    useEffect(() => {
        if (printRequest.hasLoaded) {
            saveFile({
                data: printRequest.data,
                fileName: title,
                setSaveFileError,
                type: "pdf",
            });
        }
    }, [printRequest.hasLoaded, printRequest.loading]);

    const handlePrint = () => {
        if (!uid) return;
        printRequest.doFetch(true, { fileUniqueid: uid });
    };

    return (
        <MenuItem onClick={handlePrint}>
            {printRequest.loading ? (
                <CircularProgress size={20} />
            ) : (
                <div style={{ width: 20, height: 20 }}></div>
            )}
            <ListItemText inset>{t("Lista impegni")}</ListItemText>
        </MenuItem>
    );
};
