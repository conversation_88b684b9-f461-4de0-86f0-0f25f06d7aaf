import { Box, Button } from "@vapor/react-material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { useNavigate } from "react-router-dom";

interface IProps {
    t: any;
    prevLink: string;
    nextLink: string;
}

export default function PraticaNavigation(props: IProps) {
    const { t, prevLink, nextLink } = props;
    const navigate = useNavigate();

    function handlePrevNextNavigation(uid: string) {
        if (uid) {
            navigate(`/archive/summary?uid=${uid}`);
        }
    }
    return (
        <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            sx={{ flex: 1 }}
            m={2}
        >
            {nextLink && (
                <Button
                    onClick={() => handlePrevNextNavigation(nextLink)}
                    startIcon={<ArrowBackIcon />}
                >
                    {t("Vai alla pratica precendente")}
                </Button>
            )}
            {prevLink && (
                <Button
                    onClick={() => handlePrevNextNavigation(prevLink)}
                    endIcon={<ArrowForwardIcon />}
                >
                    {t(" Vai alla pratica successiva")}
                </Button>
            )}
        </Box>
    );
}
