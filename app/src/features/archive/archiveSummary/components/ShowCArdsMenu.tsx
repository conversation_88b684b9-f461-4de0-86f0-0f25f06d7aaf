import {
    <PERSON>ton,
    ListItemIcon,
    ListItemText,
    Menu,
    MenuItem,
    Checkbox,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCaretDown } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useState } from "react";
import { options } from "../constant/sidebar.constant";
import {
    CardType,
    ShowCardsMenuProps,
} from "../interface/archivedetail.interface";
import usePostCustom from "../../../../hooks/usePostCustom";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { defaultColumns } from "../constant/detail.constant";

export const ShowCardsMenu = ({ columns, setColumns }: ShowCardsMenuProps) => {
    const { t } = useTranslation();
    const [showResetModal, setShowResetModal] = useState(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleSelectedOption = (option: CardType) => {
        const isInLeft = columns?.left?.includes(option) ?? false;
        const isInRight = columns?.right?.includes(option) ?? false;

        if (isInLeft || isInRight) {
            const newColumns = { ...columns };
            if (isInLeft) {
                newColumns.left =
                    newColumns.left?.filter(id => id !== option) ?? [];
            } else {
                newColumns.right =
                    newColumns.right?.filter(id => id !== option) ?? [];
            }
            setColumns(newColumns);
        } else {
            setColumns({
                ...columns,
                left: [...(columns.left ?? []), option],
            });
        }
    };

    const resetUiRequest = usePostCustom("/archive/rebase-ui-archive");

    const handleResetOptions = () => {
        setColumns(defaultColumns);
        resetUiRequest.doFetch(true);
    };

    const handleAgree = () => {
        handleResetOptions();
        setShowResetModal(false);
    };

    const handleDecline = () => {
        setShowResetModal(false);
    };

    return (
        <>
            <ConfirmModal
                title="Atenzione!"
                agree={t("Conferma")}
                decline={t("Annulla")}
                confirmText={t(
                    "Confermando ripristinerai la disposizione di default"
                )}
                handleAgree={handleAgree}
                handleDecline={handleDecline}
                open={showResetModal}
            />
            <Button
                variant="outlined"
                onClick={handleClick}
                endIcon={<FontAwesomeIcon icon={faCaretDown} />}>
                {t("Mostra/Nascondi")}
            </Button>
            <Menu
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}>
                {Object.entries(options).map(([key, label]) => (
                    <MenuItem
                        key={key}
                        value={key}
                        onClick={() => handleSelectedOption(key as CardType)}>
                        <ListItemIcon>
                            <Checkbox
                                checked={
                                    columns?.left?.includes(key as CardType) ||
                                    columns?.right?.includes(key as CardType)
                                }
                                size="small"
                                edge="start"
                            />
                        </ListItemIcon>
                        <ListItemText>{t(label)}</ListItemText>
                    </MenuItem>
                ))}
                <MenuItem>
                    <ListItemText
                        inset
                        onClick={() => setShowResetModal(true)}>
                        {t("Ripristina")}
                    </ListItemText>
                </MenuItem>
            </Menu>
        </>
    );
};
