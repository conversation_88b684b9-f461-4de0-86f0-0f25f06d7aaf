import { useTranslation } from "@1f/react-sdk";
import {
    Checkbox,
    ListItemIcon,
    ListItemText,
    MenuItem,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPrint } from "@fortawesome/free-solid-svg-icons";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { fileHeader, Template } from "../interface/archivedetail.interface";
import useGetCustom from "../../../../hooks/useGetCustom";
import { Typography } from "@vapor/react-extended";

export const PrintTemplate = ({
    fileHeader,
}: {
    fileHeader: fileHeader;
}) => {
    const { t } = useTranslation();
    const [templates, setTemplates] = useState<Template[]>([]);
    const [showTemplates, setShowTemplates] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<Template | undefined>();
    const [printTemplate, setPrintTemplate] = useState(false);

    const templatesRequest = useGetCustom(
        "prints/filter-templates?noTemplateVars=true"
    );

    const printModelRequest = useGetCustom(
        "archive/print-model",
        {},
        null,
        true
    );

    useEffect(() => {
        templatesRequest.doFetch(true, { category: 3 });
    }, []);

    useEffect(() => {
        if (templatesRequest.hasLoaded) {
            setTemplates(templatesRequest.data);
        }
    }, [templatesRequest.hasLoaded]);

    useEffect(() => {
        if (printTemplate && fileHeader.id && selectedTemplate?.id) {
            printModelRequest.doFetch(true, {
                sectionid: fileHeader.id,
                category: "3",
                docid: selectedTemplate.id,
            });
        }
    }, [printTemplate]);

    useEffect(() => {
        if (printModelRequest.hasLoaded) {
            setShowTemplates(false);
            setSelectedTemplate(undefined);
            setPrintTemplate(false);
        }
    }, [printModelRequest.hasLoaded]);

    const handleDecline = () => {
        setShowTemplates(false);
    };

    const handleShowModal = () => setShowTemplates(true);

    const handleConfirm = () => {
        setPrintTemplate(true);
    };

    const showTable = templates.length > 0;

    return (
        <>
            <ConfirmModal
                agree={t("Conferma")}
                decline={t("Annulla")}
                handleAgree={handleConfirm}
                handleDecline={handleDecline}
                open={showTemplates}
                title={t("Scegli il template da utilizzare")}>
                {showTable ? (
                    <TableContainer>
                        <TableHead>
                            <TableCell></TableCell>
                            <TableCell>{t("Nome")}</TableCell>
                            <TableCell>{t("Descrizione")}</TableCell>
                        </TableHead>
                        <TableBody>
                            {templates.map(row => (
                                <TableRow>
                                    <TableCell>
                                        <Checkbox
                                            value={row.id}
                                            onClick={() =>
                                                setSelectedTemplate(row)
                                            }
                                            checked={
                                                selectedTemplate &&
                                                selectedTemplate.id === row.id
                                            }
                                        />
                                    </TableCell>
                                    <TableCell>{row.filename}</TableCell>
                                    <TableCell>{row.title}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </TableContainer>
                ) : (
                    <Typography>
                        {t("Non ci sono template caricati per questa sezione")}
                    </Typography>
                )}
            </ConfirmModal>
            <MenuItem onClick={handleShowModal}>
                <ListItemIcon>
                    <FontAwesomeIcon icon={faPrint} />
                </ListItemIcon>
                <div style={{ width: 20, height: 20 }}></div>
                <ListItemText>{t("Stampa template")}</ListItemText>
            </MenuItem>
        </>
    );
};
