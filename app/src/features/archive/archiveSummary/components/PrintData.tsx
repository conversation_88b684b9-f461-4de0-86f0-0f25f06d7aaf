import { useTranslation } from "@1f/react-sdk";
import {
    ListItemIcon,
    ListItemText,
    Menu,
    MenuItem,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faA, faCalendar } from "@fortawesome/free-solid-svg-icons";
import { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import {
    PrintDataMenuProps,
    printOption,
} from "../interface/archivedetail.interface";
import { saveFile } from "../../../../utilities/utils";
import useGetCustom from "../../../../hooks/useGetCustom";

export const PrintDataMenu = ({
    anchorElSubMenu,
    setAnchorElSubMenu,
    setAnchorEl,
    setSaveFileError,
}: PrintDataMenuProps) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");

    const openSubMenu = Boolean(anchorElSubMenu);
    const { data, hasLoaded, doFetch, loading } = useGetCustom(
        "archive/printfile",
        {},
        null,
        true
    );

    const handleCloseSubMenu = () => {
        setAnchorElSubMenu(null);
        setAnchorEl(null);
    };

    const handlePrintData = (option: printOption) => () => {
        const orderBy =
            option === "Chronological asc" || option === "Chronological desc"
                ? "date"
                : option === "alphabetic asc" || option === "alphabetic desc"
                ? "title"
                : "";
        const orderDir =
            option === "Chronological asc" || option === "alphabetic asc"
                ? "asc"
                : option === "Chronological desc" ||
                  option === "alphabetic desc"
                ? "desc"
                : "";

        if (uid && orderBy && orderDir) {
            doFetch(true, {
                uniqueid: uid,
                orderBy,
                orderDir,
            });
        }
        handleCloseSubMenu();
    };

    useEffect(() => {
        if (hasLoaded && !loading) {
            const title = "Dati praticha";
            saveFile({
                data: data,
                fileName: title,
                setSaveFileError: setSaveFileError,
                type: "pdf",
            });
        }
    }, [hasLoaded, loading]);

    return (
        <Menu
            open={openSubMenu}
            anchorEl={anchorElSubMenu}
            onClose={handleCloseSubMenu}
            anchorOrigin={{
                vertical: "top",
                horizontal: -235,
            }}
            transformOrigin={{
                vertical: 0,
                horizontal: "left",
            }}>
            <MenuItem onClick={handlePrintData("alphabetic desc")}>
                <ListItemIcon>
                    <FontAwesomeIcon icon={faA} />
                </ListItemIcon>
                <ListItemText inset>{t("Alphabetico DESC")}</ListItemText>
            </MenuItem>
            <MenuItem onClick={handlePrintData("alphabetic asc")}>
                <ListItemIcon>
                    <FontAwesomeIcon icon={faA} />
                </ListItemIcon>
                <ListItemText inset>{t("Alphabetico ASC")}</ListItemText>
            </MenuItem>
            <MenuItem onClick={handlePrintData("Chronological desc")}>
                <ListItemIcon>
                    <FontAwesomeIcon icon={faCalendar} />
                </ListItemIcon>
                <ListItemText inset>{t("Chronologico DESC")}</ListItemText>
            </MenuItem>
            <MenuItem onClick={handlePrintData("Chronological asc")}>
                <ListItemIcon>
                    <FontAwesomeIcon icon={faCalendar} />
                </ListItemIcon>
                <ListItemText inset>{t("Chronologico ASC")}</ListItemText>
            </MenuItem>
        </Menu>
    );
};
