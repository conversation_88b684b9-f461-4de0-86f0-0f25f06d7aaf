import { useEffect, useState, useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { Box, Card, CardContent, Typography } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpDownLeftRight } from "@fortawesome/free-solid-svg-icons";
import {
    Columns,
    DatiPraticaCardProps,
} from "../interface/archivedetail.interface";
import { AcquistiCard } from "./Cards/AcquistiCard";
import { AltriDatiCard } from "./Cards/AltriDatiCard";
import { AnotazioniCard } from "./Cards/AnotazioniCard";
import { CampiDinamiciCard } from "./Cards/CampiDinamiciCard";
import { DatiGeneraliCard } from "./Cards/DatiGeneraliCard";
import { DatiPolisiweb } from "./Cards/DatiPolisiWebCard";
import { GiudizialeCard } from "./Cards/GiudizialeCard";
import { PraticheCollegateCard } from "./Cards/PratiCollegateCard";
import { TagCard } from "./Cards/TagCard";
import { UltimiEventiCard } from "./Cards/UltimiEventiCard";
import { PraticaData } from "..";
import { defaultOptions } from "../constant/sidebar.constant";
import Spinner from "../../../../custom-components/Spinner";

const ItemType = {
    CARD: "card",
};

const createCards = (praticaData: PraticaData) => ({
    summaryAcquisto: <AcquistiCard fileHeader={praticaData?.fileHeader} />,
    summaryAltriDati: <AltriDatiCard altriDati={praticaData?.altriDati} />,
    summaryAnnotations: <AnotazioniCard fileHeader={praticaData?.fileHeader} />,
    summaryCampiDinamici: (
        <CampiDinamiciCard preview_campi={praticaData?.preview_campi} />
    ),
    summaryDatiGenerali: (
        <DatiGeneraliCard fileHeader={praticaData?.fileHeader} />
    ),
    summaryDatiPolisweb: (
        <DatiPolisiweb datipolisiweb={praticaData?.poliswebData} />
    ),
    summaryPraticheCollegate: <PraticheCollegateCard />,
    summaryTags: <TagCard fileHeader={praticaData?.fileHeader} />,
    summaryLastItems: (
        <UltimiEventiCard
            agendaList={praticaData?.agendaList}
            deadlinesList={praticaData?.deadlinesList}
            documentsList={praticaData?.documentList}
            fileHeader={praticaData?.fileHeader}
        />
    ),
    summaryGiudiziale: <GiudizialeCard fileHeader={praticaData?.fileHeader} />,
});

const DraggableCard = ({
    card,
    index,
    columnIndex,
    moveCard,
    setDragging,
    setDraggedCardHeight,
    praticaData,
}: any) => {
    const cardRef: any = useRef();

    const [{ isDragging }, dragRef]: any = useDrag({
        type: ItemType.CARD,
        item: { index, columnIndex },
        collect: monitor => ({
            isDragging: monitor.isDragging(),
        }),
    });

    useEffect(() => {
        setDragging(isDragging);
        if (cardRef.current) {
            setDraggedCardHeight(cardRef.current.offsetHeight);
        }
    }, [isDragging, cardRef.current]);

    const [{ isOver }, dropRef] = useDrop({
        accept: ItemType.CARD,
        hover: (draggedItem: any) => {
            if (
                draggedItem.index === index &&
                draggedItem.columnIndex === columnIndex
            ) {
                return; // Avoid unnecessary reorder
            } else if (
                draggedItem.index !== index &&
                draggedItem.columnIndex === columnIndex
            ) {
                if (draggedItem.index !== index) {
                    moveCard(
                        draggedItem.columnIndex,
                        draggedItem.index,
                        columnIndex,
                        index
                    );
                    draggedItem.index = index;
                    draggedItem.columnIndex = columnIndex;
                }
            }
        },
        drop: (draggedItem: any) => {
            if (
                draggedItem.columnIndex !== columnIndex ||
                draggedItem.index !== index
            ) {
                moveCard(
                    draggedItem.columnIndex,
                    draggedItem.index,
                    columnIndex,
                    index
                );
            }
        },
        collect: monitor => ({
            isOver: monitor.isOver(),
        }),
    });

    const [isHovered, setIsHovered] = useState(false);

    return (
        <Card
            ref={node => {
                dragRef(dropRef(node));
                cardRef.current = node;
            }}
            sx={{
                width: "100%",
                marginTop: "10px",
                marginBottom: "10px",
                opacity: isDragging ? 0.8 : 1,
                cursor: "move",
                transform: isDragging ? "scale(1.05)" : "scale(1)",
                border: isOver ? "2px dashed #1976d2" : "none",
                transition: "transform 0.2s, box-shadow 0.2s, border 0.2s",
                padding: "8px",
                boxShadow: isDragging
                    ? "0 8px 16px rgba(0, 0, 0, 0.2)"
                    : "0 4px 8px rgba(0, 0, 0, 0.1)",
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    transform: "translateY(-10px)",
                    transition: "opacity 1.5s ease, transform 1.5s ease",
                    animation: "fadeIn 1.5s forwards",
                    visibility: !isHovered ? "hidden" : "",
                }}>
                <FontAwesomeIcon
                    icon={faUpDownLeftRight}
                    style={{ marginTop: "2px" }}
                />
                <Typography
                    variant="caption"
                    sx={{ textAlign: "center" }}>
                    Trascina il riquadro per spostarlo
                </Typography>
            </Box>
            <CardContent>
                {defaultOptions.includes(card.id)
                    ? createCards(praticaData)[
                          card.id as keyof ReturnType<typeof createCards>
                      ]
                    : null}
            </CardContent>
        </Card>
    );
};

const DropZone = ({
    columnIndex,
    moveCard,
    draggedCardHeight,
    targetIndex,
}: any) => {
    if (!draggedCardHeight) return null;

    const [, dropRef] = useDrop({
        accept: ItemType.CARD,
        drop: (draggedItem: any) => {
            moveCard(
                draggedItem.columnIndex,
                draggedItem.index,
                columnIndex,
                targetIndex
            );
            draggedItem.index = targetIndex;
            draggedItem.columnIndex = columnIndex;
        },
    });

    return (
        <Box
            ref={dropRef}
            sx={{
                height: `${draggedCardHeight}px`,
                border: "2px dashed #ccc",
                borderRadius: "4px",
                backgroundColor: "#f9f9f9",
            }}
        />
    );
};

export const DatiPraticaCard = ({
    columns,
    setColumns,
    praticaData,
    hasLoaded,
}: DatiPraticaCardProps) => {
    const [isDragging, setDragging] = useState(false);
    const [draggedCardHeight, setDraggedCardHeight] = useState(0);

    const moveCard = (
        fromColumn: number,
        fromIndex: number,
        toColumn: number,
        toIndex: number
    ) => {
        const newColumns = [...columns];
        const [movedCard] = newColumns[fromColumn].splice(fromIndex, 1);
        newColumns[toColumn].splice(toIndex, 0, movedCard);
        const transformedColumns: Columns = {
            left: newColumns[0].map(card => card.id),
            right: newColumns[1].map(card => card.id),
        };
        setColumns(transformedColumns);
    };

    if (!hasLoaded) {
        return <Spinner></Spinner>;
    }

    return (
        <Box
            display="flex"
            gap={2}
            p={2}>
            {columns.map((cards, columnIndex) => (
                <Box
                    key={columnIndex}
                    flex={1}>
                    {/* Top DropZone */}
                    {isDragging && (
                        <DropZone
                            key={`${columnIndex}-top`}
                            columnIndex={columnIndex}
                            moveCard={moveCard}
                            draggedCardHeight={draggedCardHeight}
                            targetIndex={0}
                        />
                    )}
                    {/* Cards */}
                    {cards
                        .filter(card => !card.hidden)
                        .map((card, index) => (
                            <DraggableCard
                                key={card.id}
                                card={card}
                                index={index}
                                columnIndex={columnIndex}
                                moveCard={moveCard}
                                setDragging={setDragging}
                                setDraggedCardHeight={setDraggedCardHeight}
                                praticaData={praticaData}
                            />
                        ))}
                    {/* Bottom DropZone */}
                    {isDragging && (
                        <DropZone
                            key={`${columnIndex}-bottom`}
                            columnIndex={columnIndex}
                            moveCard={moveCard}
                            draggedCardHeight={draggedCardHeight}
                            targetIndex={cards.length}
                        />
                    )}

                    {isDragging && cards.length === 0 && (
                        <DropZone
                            key={`${columnIndex}-empty`}
                            columnIndex={columnIndex}
                            moveCard={moveCard}
                            draggedCardHeight={draggedCardHeight}
                            targetIndex={0}
                        />
                    )}
                </Box>
            ))}
        </Box>
    );
};
