import {
    <PERSON>ton,
    ListItemIcon,
    ListItemText,
    Menu,
    MenuItem,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCaretDown, faCaretLeft } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useState } from "react";
import { PrintDataMenu } from "./PrintData";
import { PrintTitlePage } from "./PrintTitlePage";
import { PrintDeposit } from "./PrintDeposit";
import { PrintDocumentList } from "./PrintDocumentList";
import { PrintArchiveDeadlinesList } from "./PrintArchivesDeadlineList";
import { PrintTemplate } from "./PrintTemplate";
import { fileHeader } from "../interface/archivedetail.interface";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { PrintListaUdienze } from "./PrintListaUdienze";

export const PrintMenu = ({ fileHeader }: { fileHeader: fileHeader }) => {
    const { t } = useTranslation();

    const [saveFileError, setSaveFileError] = useState(false);

    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const [anchorElSubMenu, setAnchorElSubMenu] = useState<null | HTMLElement>(
        null
    );

    const handleClickSubMenu = (event: React.MouseEvent<HTMLLIElement>) => {
        setAnchorElSubMenu(event.currentTarget);
    };

    return (
        <>
            <ToastNotification
                setShowNotification={setSaveFileError}
                severity="error"
                showNotification={saveFileError}
                text={t(
                    "Si è verificato un errore durante il salvataggio del file."
                )}
            />

            <Button
                variant="outlined"
                onClick={handleClick}
                endIcon={<FontAwesomeIcon icon={faCaretDown} />}>
                {t("Stampa")}
            </Button>
            <PrintDataMenu
                anchorElSubMenu={anchorElSubMenu}
                setAnchorEl={setAnchorEl}
                setAnchorElSubMenu={setAnchorElSubMenu}
                setSaveFileError={setSaveFileError}
            />
            <Menu
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}>
                <MenuItem onClick={handleClickSubMenu}>
                    <ListItemIcon>
                        <FontAwesomeIcon icon={faCaretLeft} />
                    </ListItemIcon>
                    <div style={{ width: 20, height: 20 }}></div>
                    <ListItemText>{t("Dati pratica")}</ListItemText>
                </MenuItem>
                <PrintTitlePage setSaveFileError={setSaveFileError} />
                <PrintDeposit setSaveFileError={setSaveFileError} />
                <PrintDocumentList setSaveFileError={setSaveFileError} />
                <PrintArchiveDeadlinesList
                    setSaveFileError={setSaveFileError}
                />
                <PrintListaUdienze
                    setSaveFileError={setSaveFileError}></PrintListaUdienze>
                <PrintTemplate
                    fileHeader={fileHeader}
                />
            </Menu>
        </>
    );
};
