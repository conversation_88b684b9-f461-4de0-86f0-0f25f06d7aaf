import { useTranslation } from "@1f/react-sdk";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const DuplicateArchive = ({
    showDuplicateModal,
    setShowDuplicateModal,
}: {
    showDuplicateModal: boolean;
    setShowDuplicateModal: Dispatch<SetStateAction<boolean>>;
}) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");
    const navigate = useNavigate();

    const [duplicateArchive, setDuplicateArchive] = useState(false);

    const handleHideModal = () => {
        setShowDuplicateModal(false);
    };

    const handleConfirm = () => {
        setDuplicateArchive(true);
    };

    const duplicateArchiveRespose = usePostCustom("archive/copy-paste-archive");

    useEffect(() => {
        if (uid && duplicateArchive) {
            duplicateArchiveRespose.doFetch(true, { uniqueid: uid });
        }
    }, [uid, duplicateArchive]);

    useEffect(() => {
        if (duplicateArchiveRespose.hasLoaded) {
            navigate("/archive/archive");
        }
    }, [duplicateArchiveRespose.hasLoaded]);

    return (
        <ConfirmModal
            agree={t("Conferma")}
            decline={t("Annulla")}
            handleAgree={handleConfirm}
            handleDecline={handleHideModal}
            open={showDuplicateModal}
            title={t("Atenzione!")}
            loading={duplicateArchiveRespose.loading}
            confirmText={t("Si è sicuri di voler duplicare la pratica?")}
        />
    );
};
