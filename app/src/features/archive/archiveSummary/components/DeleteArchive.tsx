import { Dispatch, SetStateAction, useEffect, useState } from "react";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate, useSearchParams } from "react-router-dom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const DeleteArchiveButton = ({
    showDeleteModal,
    setShowDeleteModal,
}: {
    showDeleteModal: boolean;
    setShowDeleteModal: Dispatch<SetStateAction<boolean>>;
}) => {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const uid = searchParams.get("uid");
    const navigate = useNavigate();

    const [removeArchive, setRemoveArchive] = useState(false);

    const deleteArchiveResponse = usePostCustom("archive/delete");

    useEffect(() => {
        if (removeArchive && uid) {
            deleteArchiveResponse.doFetch(true, { uniqueid: uid });
        }
    }, [removeArchive, uid]);

    const handleDecline = () => {
        setShowDeleteModal(false);
    };

    const handleRemove = () => {
        setRemoveArchive(true);
    };

    useEffect(() => {
        if (deleteArchiveResponse.hasLoaded) {
            navigate("/archive/archive");
        }
    });

    return (
        <ConfirmModal
            agree={t("Conferma")}
            decline={t("Annulla")}
            handleAgree={handleRemove}
            handleDecline={handleDecline}
            open={showDeleteModal}
            title={t("Attenzione!")}
            loading={deleteArchiveResponse.loading}
            confirmText={t(
                "Eliminando la pratica tutti i suoi dati andranno perduti. Si è certi di voler proseguire?"
            )}
        />
    );
};
