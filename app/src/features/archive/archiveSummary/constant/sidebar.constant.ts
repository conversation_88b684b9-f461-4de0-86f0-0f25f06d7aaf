import WorkIcon from "@mui/icons-material/Work";
import HomeIcon from "@mui/icons-material/Home";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
import SignalCellularAltIcon from "@mui/icons-material/SignalCellularAlt";
import PermIdentityIcon from "@mui/icons-material/PermIdentity";
import StorageIcon from "@mui/icons-material/Storage";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import TopicIcon from "@mui/icons-material/Topic";
import NoteIcon from "@mui/icons-material/Note";
import DoDisturbIcon from "@mui/icons-material/DoDisturb";
import SearchIcon from "@mui/icons-material/Search";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import { CardType } from "../interface/archivedetail.interface";

export const divisioneUtiliPermissions = (modules: any) => {
    return (
        modules && !modules.isExternal && modules.canAccessDivisioneDegliUtili()
    );
};


export const SIEBAR_PATHS = [
    {id:"1", url:"/archive/summary" },
    {id:"2", url: "/archivedashboard/bi" },
    {id:"3", url:"/archive-profits-distribution" },
    {id:"4", url: "/archiveanagrafiche/archiveindex" },
    {id:"5", url: "/archivedetails/index" },
    {id:"6", url: "/archiveagenda/agenda" },
    {id:"7", url: "/archivedeadlines/deadlines" },
    {id:"8", url: "/archivedocuments/documents" },
    {id:"9", url: "/archivemailbox/mailbox" },
    {id:"10", url: "/archivetemplates/templates" },
    {id:"11", url: "/archivepolisweb/polisweb" },
    {id:"12", url: "/archivepct/pct" },
    {id:"13", url: "/archivepdapayment/pda-payment" },
    {id:"14", url: "/archivenir/nir" },
    {id:"15", url: "/archivenotifications" },
    {id:"16", url: "/archiveexpensereport/expensereport" },
    {id:"17", url: "/archivefatture/fees" },
    {id:"18", url: "/archivepayments/payments" },
    {id:"19", url: "/archivecontract/contract" },
    {id:"20", url: "/archivecreditrecovery/creditrecovery" },
    {id:"21", url: "/archiveantirec/archiveantirec" },
    {id:"22", url: "/archiveinfocamere" },
    {id:"23", url: "/archivetimesheet/timesheet" },
    {id:"24", url: "/archivemessages/messages" },
    {id:"25", url: "/archiveworkflows/index" },
    {id:"26", url: "/archiveunep/unep" },
    {id:"27", url:"/archivecontractmanagement/summary" },
    {id:"28", url:"/archivecreditrecovery/creditrecovery"},
    {id:"29", url:"/archiveliquidationpa/archiveliquidationpa"},
    {id:"30", url:"/archive-claims/overview"},
]


export const getListItems = (modules: any) => {
    const LIST_ITEM = [
        { id: "1", name: "Dati Pratica", icon: FormatAlignJustifyIcon, url: "/archive/summary", queryType: "uid" },
        { id: "2", name: "Statistiche", icon: SignalCellularAltIcon, url: "/archivedashboard/bi", queryType: "fileUniqueid" },
        { id: "4", name: "Soggetti", icon: PermIdentityIcon, url: "/archiveanagrafiche/archiveindex", queryType: "fileUniqueid" },
        { id: "5", name: "Prestazioni", icon: StorageIcon, url: "/archivedetails/index", queryType: "fileUniqueid" },
        { id: "6", name: "Udienze", icon: CalendarMonthIcon, url: "/archiveagenda/agenda", queryType: "fileUniqueid" },
        { id: "7", name: "Impegni", icon: CalendarMonthIcon, url: "/archivedeadlines/deadlines", queryType: "fileUniqueid" },
        { id: "8", name: "Documenti", icon: TopicIcon, url: "/archivedocuments/documents", queryType: "fileUniqueid" },
        { id: "9", name: "Email fascicolate", icon: TopicIcon, url: "/archivemailbox/mailbox", queryType: "fileUniqueid" },
        { id: "10", name: "Modelli", icon: TopicIcon, url: "/archivetemplates/templates", queryType: "fileUniqueid" },
        { id: "11", name: "Polisweb", icon: HomeIcon, url: "/archivepolisweb/polisweb", queryType: "fileUniqueid" },
        { id: "12", name: "Deposito telematico", icon: HomeIcon, url: "/archivepct/pct", queryType: "fileUniqueid" },
        { id: "13", name: "Pagamento telematico", icon: HomeIcon, url: "/archivepdapayment/pda-payment", queryType: "fileUniqueid" },
        { id: "14", name: "Genera NIR cartacea", icon: HomeIcon, url: "/archivenir/nir", queryType: "fileUniqueid" },
        { id: "15", name: "Notifiche in proprio", icon: HomeIcon, url: "/archivenotifications", queryType: "fileUniqueid" },
        { id: "16", name: "Nota Spese guidiziale", icon: NoteIcon, url: "/archiveexpensereport/expensereport", queryType: "fileUniqueid" },
        { id: "17", name: "Fatture, Note e Preavvisi", icon: NoteIcon, url: "/archivefatture/fees", queryType: "fileUniqueid" },
        { id: "18", name: "Movimenti", icon: NoteIcon, url: "/archivepayments/payments", queryType: "fileUniqueid" },
        { id: "19", name: "Contratto", icon: NoteIcon, url: "/archivecontract/contract", queryType: "fileUniqueid" },
        { id: "20", name: "Recupero crediti", icon: NoteIcon, url: "/archivecreditrecovery/creditrecovery", queryType: "fileUniqueid" },
        { id: "21", name: "Antiriciclaggio", icon: DoDisturbIcon, url: "/archiveantirec/archiveantirec", queryType: "fileUniqueid" },
        { id: "22", name: "Visure e bilanci", icon: SearchIcon, url: "/archiveinfocamere", queryType: "fileUniqueid" },
        { id: "23", name: "TimeSheet", icon: NoteIcon, url: "/archivetimesheet/timesheet", queryType: "fileUniqueid" },
        { id: "24", name: "Messaggi", icon: MailOutlineIcon, url: "/archivemessages/messages", queryType: "fileUniqueid" },
        { id: "25", name: "Workflows", icon: WorkIcon, url: "/archiveworkflows/index", queryType: "fileUniqueid" },
        { id: "26", name: "Deposito UNEP", icon: HomeIcon, url: "/archiveunep/unep", queryType: "fileUniqueid" },
        {id:"27",name: "Contrattualistica",icon: NoteIcon,url:"/archivecontractmanagement/summary", queryType: "fileUniqueid"},
        {id:"28",name: "Ciclo Attivo",icon: NoteIcon,url:"/archivecreditrecovery/creditrecovery", queryType: "fileUniqueid"},
        {id:"29",name: "Ciclo Passivo",icon: NoteIcon,url:"/archiveliquidationpa/archiveliquidationpa", queryType: "fileUniqueid"},
        {id:"30",name: "Sinistri",icon: NoteIcon,url:"/archive-claims/overview", queryType: "fileUniqueid"},
    ];

    if (divisioneUtiliPermissions(modules)) {
        LIST_ITEM.splice(2, 0, {
            id: "3",
            name: "Divisione degli utili",
            icon: SignalCellularAltIcon,
            url: "/archive-profits-distribution",
            queryType: "fileUniqueid"
        });
    }

    return LIST_ITEM;
};

export const defaultOptions: CardType[] = [
    "summaryAnnotations",
    "summaryDatiGenerali",
    "summaryDatiPolisweb",
    "summaryAltriDati",
    "summaryGiudiziale",
    "summaryPraticheCollegate",
    "summaryTags",
    "summaryLastItems",
    "summaryCampiDinamici",
    "summaryAcquisto"
];

export const options: { [key in CardType]: string } = {
    "summaryAnnotations": "Annotazioni",
    "summaryDatiGenerali": "Dati Generali",
    "summaryDatiPolisweb": "Dati Polisweb",
    "summaryAltriDati": "Altri dati",
    "summaryGiudiziale": "Giudiziale",
    "summaryPraticheCollegate": "Pratiche collegate",
    "summaryTags": "Tag",
    "summaryLastItems": "Ultimi eventi e documenti",
    "summaryCampiDinamici": "Campi dinamici",
    "summaryAcquisto": "Acquisto"
};
