import * as React from 'react';
import {
    DialogActions,
    Button,
    Dialog,
    DialogContent,
    DialogTitle,
    Checkbox,
    FormControlLabel,
    FormGroup,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Typography,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import usePostCustom from "../../../hooks/usePostCustom";
import ToastNotification from '../../../custom-components/ToastNotification';
import SpinnerButton from '../../../custom-components/SpinnerButton';

const defaultQuery = {
    id: '-1',
    relazione: 1,
    riserva: false,
};

export default function AggiungiSoggetto({ open, setOpen, title, lawyers, relazioniutenti, selectedGridRows }: any) {
    const [query, setQuery] = React.useState(defaultQuery);
    const [loading, setLoading] = React.useState(false);
    const [openToast, setOpenToast] = React.useState(false);
    const { t } = useTranslation();

    const removeresponsabile = usePostCustom(`/archiveanagrafiche/removeresponsabile?noTemplateVars=true`);
    const aggiungiresponsabile = usePostCustom(`/archiveanagrafiche/aggiungiresponsabile?noTemplateVars=true`);
    const addutentepratica = usePostCustom(`/archiveanagrafiche/addutentepratica?noTemplateVars=true`);
    const reservefile = usePostCustom(`/archiveanagrafiche/reservefile?noTemplateVars=true`);
    const checkremove = usePostCustom(`/archiveanagrafiche/checkremove?noTemplateVars=true`);
    const getidriserva = usePostCustom(`/archiveanagrafiche/getidriserva?noTemplateVars=true`);
    const unreservefile = usePostCustom(`/archiveanagrafiche/unreservefile?noTemplateVars=true`);
    const oneDrivePermissions = usePostCustom(`/archiveanagrafiche/handle-one-drive-permissions?noTemplateVars=true`);

    let one_drive_reserve_responses: any = []
    let response

    const handleClose = () => {
        setOpen(false);
    };

    const handleCheckBoxChange = (event: any) => {
        setQuery({ ...query, [event.target.name]: event.target.checked });
    };

    const handleInputChange = (event: any) => {
        setQuery({ ...query, [event.target.name]: event.target.value });
    };

    const handleCheckremove = async ({ riserva, id, file_uid }: any) => {
        let data: any;
        let results;
        let getResponses;

        if (riserva) {
            await reservefile.doFetch(true, { person: id, fileUniqueid: file_uid, massive: true });
        } else {
            data = await checkremove.doFetch(true, { massive: true, id, fileUniqueid: file_uid });

            if (data.data) {

                results = data?.data;
                getResponses = results.filter((item: any) => {
                    if (item.res != 0 && item.res != 3 && item.res != 4) {
                        return item;
                    }
                });

                one_drive_reserve_responses = results.map((item: any) => {
                    if (item.res == 0) {
                        item.res = 0;
                    } else if (item.res == 3) {
                        item.res = 3;
                    } else if (item.res == 4) {
                        item.res = 1;
                    } else {
                        item.res = 1;
                    }
                    return item;
                })

                if (getResponses.length) {
                    getResponses.forEach(async (element: any) => {
                        response = await getidriserva.doFetch(true, { id, file_uid: element.uid });
                        if (response != -1) {
                            const response:any = await getidriserva.doFetch(true, { id, file_uid: element.uid });
                            const formData = new FormData();
                            formData.append("id", response.data);
                            formData.append("fileUniqueid", element.uid);
                            await unreservefile.doFetch(true, formData);
                        }
                    });
                }
            }
        }
    };

    const handleOneDrivePermissions = async ({ id, riserva, type }: any) => {
        try {
            if (one_drive_reserve_responses.length) {
                for (const item of one_drive_reserve_responses) {
                    const data = await oneDrivePermissions
                        .doFetch(true, { id, riserva, file_uid: item.uid, type })
                        .catch(err => console.error("Error in OneDrive permissions:", err));

                    if (data === 0) {
                        console.error("An error occurred while handling permissions!");
                    }
                }
            }
        } catch (error) {
            console.error("Error handling OneDrive permissions:", error);
        }
    };

    const handleSubmit = async (event: any) => {
        event.preventDefault();

        try {
            setLoading(true);
            if (query.id !== '-1') {
                if (query?.relazione == 2) {
                    const res: any = await removeresponsabile.doFetch(true, { file_uid: selectedGridRows.join(",") , modifica: 0 });
                    const response = res?.data;
                    await aggiungiresponsabile.doFetch(true, { id: query.id, file_uid: selectedGridRows.join(","), previous: JSON.stringify(response)});
                    await addutentepratica.doFetch(true, { id: query.id, relazione: query.relazione, file_uid: selectedGridRows.join(",") });
                    await handleCheckremove({ riserva: query.riserva, id: query.id, file_uid: selectedGridRows.join(",") });
                    await handleOneDrivePermissions({ id: query.id, riserva: query.riserva, type: 1 });
                    handleClose();
                } else {
                    await addutentepratica.doFetch(true, { id: query.id, relazione: query.relazione, file_uid: selectedGridRows.join(",") });
                    await handleCheckremove({ riserva: query.riserva, id: query.id, file_uid: selectedGridRows.join(",") });
                    await handleOneDrivePermissions({ id: query.id, riserva: query.riserva, type: 1 });
                    handleClose();
                }
            } else {
                setOpenToast(true);
            }
        } catch (error) {
            console.error("Error executing requests:", error);
        } finally {
            setQuery(defaultQuery);
            setLoading(false);
        }
    };

    return (
        <React.Fragment>
            <ToastNotification showNotification={openToast} setShowNotification={setOpenToast} text="Selezionare un utente!" severity="warning" />
            <Dialog
                open={open}
                onClose={handleClose}
                fullWidth={true}
                maxWidth={"sm"}
                PaperProps={{
                    component: 'form',
                    onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
                        handleSubmit(event);
                    },
                }}
            >
                <DialogTitle>{title}</DialogTitle>
                <DialogContent>
                    <>
                        <FormControl variant='outlined' fullWidth required>
                            <InputLabel id='select-label'>{t("Utenti")}</InputLabel>
                            <Select
                                labelId='select-label'
                                value={query.id}
                                onChange={handleInputChange}
                                name='id'
                                required>
                                <MenuItem value='-1'>{t("Seleziona un utente")}</MenuItem>
                                {lawyers && lawyers.map((lawyer: any) => (
                                    <MenuItem key={lawyer.id} value={lawyer.id}>{lawyer.nome}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <FormControl variant='outlined' fullWidth required sx={{ mt: 1 }}>
                            <InputLabel id='select-label'>{t("Relazione")}</InputLabel>
                            <Select
                                labelId='select-label'
                                value={query.relazione}
                                onChange={handleInputChange}
                                name='relazione'
                                required>
                                {relazioniutenti && relazioniutenti.map((relazione: any) => (
                                    <MenuItem key={relazione.id} value={relazione.id}>{relazione.nome}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <FormGroup>
                            <FormControlLabel control={<Checkbox name='riserva' onChange={handleCheckBoxChange} />} label="Riserva Accesso" />
                        </FormGroup>
                        <Typography sx={{ fontSize: 13, mt: 2 }}><strong>N.B.</strong> {t("La Spunta di Riserva Accesso influenzerà ogni altra relazione della stessa utenza. In presenza di riserva l'intestatario sarà sempre presente.")}</Typography>
                    </>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>{t("Annulla")}</Button>
                    <SpinnerButton type="submit" label="Conferma" isLoading={loading} variant="contained" />
                </DialogActions>
            </Dialog>
        </React.Fragment>
    );
}
