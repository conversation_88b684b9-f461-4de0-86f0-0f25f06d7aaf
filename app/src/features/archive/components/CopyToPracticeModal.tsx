import React from "react";
import {
    <PERSON><PERSON>,
    DialogTitle,
    DialogContent,
    <PERSON>alogA<PERSON>,
    Button,
    Typography,
    Box,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

interface CopyToPracticeModalProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
}

interface CopyToPracticeModalProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    selectedRowData?: any;
}

const CopyToPracticeModal: React.FC<CopyToPracticeModalProps> = ({ 
    open, 
    onClose, 
    onConfirm,
    selectedRowData 
}) => {
    const { t } = useTranslation();
    const [isVisible, setIsVisible] = React.useState(open);
    
    React.useEffect(() => {
        if (open) {
            setIsVisible(true);
        }
    }, [open]);

    const handleClose = () => {
        setIsVisible(false);
        setTimeout(() => onClose(), 300);
    };

    const handleConfirm = () => {
        onConfirm();
        handleClose();
    };



    if (!open && !isVisible) return null;

    return (
        <Dialog 
            open={isVisible}
            onClose={handleClose}
            aria-labelledby="copy-to-practice-dialog"
            sx={{
                '& .MuiDialog-paper': {
                    minWidth: '400px',
                    maxWidth: '90vw',
                    zIndex: 9999,
                    position: 'relative',
                    transition: 'opacity 300ms ease-in-out',
                    opacity: isVisible ? 1 : 0
                },
                '& .MuiBackdrop-root': {
                    backgroundColor: 'rgba(0, 0, 0, 0.5)'
                }
            }}
        >
            <Box sx={{ p: 3 }}>
                <DialogTitle sx={{ p: 0, mb: 2, fontSize: '1.25rem' }}>
                    {t("Conferma copia file")}
                </DialogTitle>
                <DialogContent sx={{ p: 0, mb: 3 }}>
                    <Typography>{t("Sei sicuro di voler copiare il file nella pratica selezionata?")}</Typography>
                    {selectedRowData && (
                        <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                            <Typography variant="subtitle2" sx={{ mb: 1 }}>{t("Dettagli pratica:")}</Typography>
                            <pre style={{ margin: 0, fontSize: '0.8rem', overflowX: 'auto' }}>
                                {JSON.stringify(selectedRowData, null, 2)}
                            </pre>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{ p: 0, justifyContent: 'flex-start', gap: 2 }}>
                    <Button 
                        onClick={handleClose} 
                        variant="outlined"
                        sx={{ minWidth: '100px' }}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button 
                        onClick={handleConfirm}
                        variant="contained" 
                        color="primary"
                        sx={{ minWidth: '100px' }}
                        autoFocus
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Box>
        </Dialog>
    );
};

export default CopyToPracticeModal;
