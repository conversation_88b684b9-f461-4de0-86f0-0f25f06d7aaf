import * as React from 'react';
import {
    TextField,
    DialogActions,
    Button,
    Dialog,
    DialogContent,
    DialogTitle,
    Checkbox,
    FormControlLabel,
    FormGroup,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Stack,
    Grid,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from '@vapor/react-extended';
import ToastNotification from "../../../custom-components/ToastNotification";
import SpinnerButton from '../../../custom-components/SpinnerButton';
import usePostCustom from "../../../hooks/usePostCustom";

const defaultQuery = {
    source: "4",
    destination: "2",
    intestatario: false,
    soggettiUtenti: false,
    appointments: false,
    timesheet: false,
    hearing: false,
    movements: false,
    assigneeLiquidation: false,
    appointmentsFrom: "",
    appointmentsTo: "",
    appointmentsEvaded: false,
    timesheetFrom: "",
    timesheetTo: "",
    timesheetEvaded: false,
    hearingFrom: "",
    hearingTo: "",
    hearingEvaded: false,
};

export default function TrasferisciUtente({ open, setOpen, title, lawyers, selectedGridRows }: any) {
    const [query, setQuery] = React.useState(defaultQuery);
    const [toastMessage, setToastMessage] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(false);
    const { t } = useTranslation();

    const useTransferProperty = usePostCustom(`archive/transfer-property-to?noTemplateVars=true`);

    const handleClose = () => {
        setOpen(false);
    };

    const handleCheckBoxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setQuery({ ...query, [event.target.name]: event.target.checked });
    };

    const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setQuery({ ...query, [event.target.name]: event.target.value });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            setIsLoading(true);
            const data = {
                data: {
                    subjects: { source: query.source, destination: query.destination },
                    accountholder: { enabled: query.intestatario },
                    subject: { enabled: query.soggettiUtenti },
                    appointments: {
                        enabled: query.appointments,
                        evaded: query.appointmentsEvaded,
                        period: {
                            from: query.appointmentsFrom ? formatDate(query.appointmentsFrom) : "",
                            to: query.appointmentsTo ? formatDate(query.appointmentsTo) : ""
                        }
                    },
                    timesheet: {
                        enabled: query.timesheet,
                        evaded: query.timesheetEvaded,
                        period: {
                            from: query.timesheetFrom ? formatDate(query.timesheetFrom) : "",
                            to: query.timesheetTo ? formatDate(query.timesheetTo) : ""
                        }
                    },
                    hearing: {
                        enabled: query.hearing,
                        evaded: query.hearingEvaded,
                        period: {
                            from: query.hearingFrom ? formatDate(query.hearingFrom) : "",
                            to: query.hearingTo ? formatDate(query.hearingTo) : ""
                        }
                    },
                    movements: { enabled: query.movements },
                    assigneeLiquidation: { enabled: query.assigneeLiquidation }
                },
                uidList: selectedGridRows
            };

            function createFormData(data: any) {
                const formData = new FormData();

                function appendFormData(obj: any, parentKey = '') {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            const value = obj[key];
                            const fullKey = parentKey ? `${parentKey}[${key}]` : key;

                            if (value && typeof value === 'object') {
                                appendFormData(value, fullKey);
                            } else {
                                formData.append(fullKey, value === null ? '' : value);
                            }
                        }
                    }
                }

                formData.append('uidList[]', "");
                appendFormData(data);
                return formData;
            }

            const formData = createFormData(data);

            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            await useTransferProperty.doFetch(true, formData);
        } catch (error) {
            console.error("Error in handleSubmit:", error);
        } finally {
            setIsLoading(false);
            setToastMessage(true);
            setOpen(false);
        }
    };

    const formatDate = (date: string) => {
        if (!date) return "";
        const formattedDate = new Date(date).toISOString().split('T')[0];
        return formattedDate;
    };

    return (
        <React.Fragment>
            <Dialog
                open={open}
                onClose={handleClose}
                fullWidth={true}
                maxWidth={"md"}
                PaperProps={{
                    component: 'form',
                    onSubmit: handleSubmit,
                }}
            >
                <DialogTitle>{title}</DialogTitle>
                <DialogContent>
                    <>
                        <Typography sx={{ fontSize: 13 }}><strong>N.B.</strong> {t("La Spunta di Riserva Accesso influenzerà ogni altra relazione della stessa utenza. In presenza di riserva l'intestatario sarà sempre presente.")}</Typography>
                        <FormControl variant='outlined' fullWidth>
                            <InputLabel id='select-label'>{t("Sorgente")}</InputLabel>
                            <Select
                                labelId='select-label'
                                sx={{ width: '60%' }}
                                name='source'>
                                {lawyers && lawyers.map((lawyer: any) => (
                                    <MenuItem key={lawyer.id} value={lawyer.id}>{lawyer.nome}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <FormControl variant='outlined' fullWidth>
                            <InputLabel id='select-label'>{t("Destinazione")}</InputLabel>
                            <Select
                                labelId='select-label'
                                sx={{ width: '60%' }}
                                name='destination'>
                                {lawyers && lawyers.map((lawyer: any) => (
                                    <MenuItem key={lawyer.id} value={lawyer.id}>{lawyer.nome}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <Stack spacing={2}>
                            <Grid container spacing={2}>
                                <Grid item xs={12} md={12}>
                                    <Typography variant='h6'>{t("Trasferisci")}</Typography>
                                </Grid>
                                <Grid item xs={12} md={12}>
                                    <FormGroup>
                                        <FormControlLabel
                                            control={<Checkbox name="intestatario" checked={query.intestatario} onChange={handleCheckBoxChange} />}
                                            label={t("Intestatario")}
                                        />
                                        <FormControlLabel
                                            control={<Checkbox name="soggettiUtenti" checked={query.soggettiUtenti} onChange={handleCheckBoxChange} />}
                                            label={t("Soggetti utenti")}
                                        />
                                        <FormControlLabel
                                            control={<Checkbox name="appointments" checked={query.appointments} onChange={handleCheckBoxChange} />}
                                            label={t("Impegni")}
                                        />
                                        {query.appointments && (
                                            <Stack direction="row" spacing={2}>
                                                <TextField
                                                    label="Data"
                                                    type="date"
                                                    name="appointmentsFrom"
                                                    value={query.appointmentsFrom}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <TextField
                                                    label="Data"
                                                    type="date"
                                                    name="appointmentsTo"
                                                    value={query.appointmentsTo}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <FormControlLabel
                                                    control={<Checkbox name="appointmentsEvaded" checked={query.appointmentsEvaded} onChange={handleCheckBoxChange} />}
                                                    label={t("Non evase")}
                                                />
                                            </Stack>
                                        )}
                                        <FormControlLabel
                                            control={<Checkbox name="timesheet" checked={query.timesheet} onChange={handleCheckBoxChange} />}
                                            label={t("Timesheet")}
                                        />
                                        {query.timesheet && (
                                            <Stack direction="row" spacing={2}>
                                                <TextField
                                                    label={t("Data")}
                                                    type="date"
                                                    name="timesheetFrom"
                                                    value={query.timesheetFrom}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <TextField
                                                    label={t("Data")}
                                                    type="date"
                                                    name="timesheetTo"
                                                    value={query.timesheetTo}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <FormControlLabel
                                                    control={<Checkbox name="timesheetEvaded" checked={query.timesheetEvaded} onChange={handleCheckBoxChange} />}
                                                    label="Non evase"
                                                />
                                            </Stack>
                                        )}
                                        <FormControlLabel
                                            control={<Checkbox name="hearing" checked={query.hearing} onChange={handleCheckBoxChange} />}
                                            label="Udienze"
                                        />
                                        {query.hearing && (
                                            <Stack direction="row" spacing={2}>
                                                <TextField
                                                    label="Data"
                                                    type="date"
                                                    name="hearingFrom"
                                                    value={query.hearingFrom}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <TextField
                                                    label="Data"
                                                    type="date"
                                                    name="hearingTo"
                                                    value={query.hearingTo}
                                                    onChange={handleDateChange}
                                                    sx={{ width: '30%' }}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <FormControlLabel
                                                    control={<Checkbox name="hearingEvaded" checked={query.hearingEvaded} onChange={handleCheckBoxChange} />}
                                                    label={t("Non evase")}
                                                />
                                            </Stack>
                                        )}
                                        <FormControlLabel
                                            control={<Checkbox name="movements" checked={query.movements} onChange={handleCheckBoxChange} />}
                                            label={t("Movimenti e spese")}
                                        />
                                        <FormControlLabel
                                            control={<Checkbox name="assigneeLiquidation" checked={query.assigneeLiquidation} onChange={handleCheckBoxChange} />}
                                            label={t("Assegnatario liquidazione")}
                                        />
                                    </FormGroup>
                                </Grid>
                            </Grid>
                        </Stack>
                    </>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>{t("Annulla")}</Button>
                    <SpinnerButton type="submit" variant="contained" color="primary" label={t("Conferma")} isLoading={isLoading} />
                </DialogActions>
            </Dialog>
            <ToastNotification
                showNotification={toastMessage}
                setShowNotification={setToastMessage}
                text={t("Trasferimento eseguito correttamente")}
                severity="success"
            />
        </React.Fragment>
    );
}
