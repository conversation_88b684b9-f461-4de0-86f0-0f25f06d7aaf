import React, { useState, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import {
    Box,
    TextField,
    Button,
    Stack,
} from "@vapor/react-material";
import { IToMergeListQuery } from "../hooks/useToMergeListData";
import debounce from "lodash/debounce";

interface ToMergeListFiltersProps {
    query: IToMergeListQuery;
    setQuery: (updates: Partial<IToMergeListQuery>) => void;
    onReset: () => void;
}

const ToMergeListFilters: React.FC<ToMergeListFiltersProps> = ({
    query,
    setQuery,
    onReset,
}) => {
    const { t } = useTranslation();
    const [localQuery, setLocalQuery] = useState(query);

    useEffect(() => {
        setLocalQuery(query);
    }, [query]);

    const debouncedSetQuery = React.useMemo(
        () => debounce((updates: Partial<IToMergeListQuery>) => {
            setQuery(updates);
        }, 500),
        [setQuery]
    );

    const handleInputChange = (field: keyof IToMergeListQuery) => (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = event.target.value;
        setLocalQuery(prev => ({ ...prev, [field]: value }));
        debouncedSetQuery({ [field]: value });
    };



    return (
        <Box>
            <Stack spacing={2}>
                <Stack direction="row" spacing={2} alignItems="flex-end">
                    {/* <FormControl fullWidth>
                        <Select
                            value={localQuery.codeType}
                            onChange={handleSelectChange("codeType")}
                            displayEmpty
                            size="small"
                        >
                            {codeTypeOptions.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl> */}
                    <TextField
                        label={t("Codice")}
                        value={localQuery.code}
                        onChange={handleInputChange("code")}
                        size="small"
                        fullWidth
                    />
                    <TextField
                        label={t("Cliente")}
                        value={localQuery.customer}
                        onChange={handleInputChange("customer")}
                        size="small"
                        fullWidth
                    />
                    <TextField
                        label={t("Controparte")}
                        value={localQuery.counterpart}
                        onChange={handleInputChange("counterpart")}
                        size="small"
                        fullWidth
                    />
                </Stack>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                        variant="text"
                        onClick={onReset}
                        size="small"
                    >
                        {t("Mostra tutte")}
                    </Button>
                </Box>
            </Stack>
        </Box>
    );
};

export default ToMergeListFilters;
