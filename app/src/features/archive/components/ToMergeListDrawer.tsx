import React from "react";
import { useTranslation } from "@1f/react-sdk";
import {
    Drawer,
    Box,
    Typography,
    IconButton,
    Divider,
} from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../custom-components/Spinner";
import ToMergeListFilters from "./ToMergeListFilters";
import { useToMergeListData } from "../hooks/useToMergeListData";
import CopyToPracticeModal from "./CopyToPracticeModal";

interface ToMergeListDrawerProps {
    open: boolean;
    onClose: () => void;
}

const ToMergeListDrawer: React.FC<ToMergeListDrawerProps> = ({
    open,
    onClose,
}) => {
    const { t } = useTranslation();
    const {
        query,
        setQuery,
        list,
        loading,
        resetFilters,
    } = useToMergeListData();

    const [modalOpen, setModalOpen] = React.useState(false);
    const [drawerWasOpen, setDrawerWasOpen] = React.useState(false);
    const [selectedRowData, setSelectedRowData] = React.useState<any>(null);

    const handleModalClose = () => {
        setModalOpen(false);
        if (!open && drawerWasOpen) {
            if (typeof onClose === 'function') {
            }
        }
    };

    const handleModalConfirm = () => {
        setModalOpen(false);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleRowClick = React.useCallback((rowData: any) => {
        setDrawerWasOpen(open);
        setSelectedRowData(rowData);
        setModalOpen(true);
        return false;
    }, [open]);

    return (
        <>
            <Drawer
                anchor="right"
                open={open}
                onClose={onClose}
                sx={{
                    '& .MuiDrawer-paper': {
                        width: '80vw',
                        maxWidth: '1200px',
                        backgroundColor: '#F2F6F8',
                        display: 'flex',
                        flexDirection: 'column',
                    },
                }}
            >
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    px: 3,
                    py: 2,
                }}
            >
                <Typography variant="h6" component="h2">
                    {t("Lista Pratiche da Unire")}
                </Typography>
                <IconButton onClick={onClose} color="primary">
                    <Close />
                </IconButton>
            </Box>
            <Divider />

            <Box
                sx={{
                    backgroundColor: "#F2F6F8",
                    flexGrow: 1,
                    p: 3,
                    display: "flex",
                    flexDirection: "column",
                    gap: 2,
                }}
            >
                <ToMergeListFilters
                    query={query}
                    setQuery={setQuery}
                    onReset={resetFilters}
                />

                <Box
                    sx={{
                        backgroundColor: "white",
                        borderRadius: 1,
                        p: 2,
                        flexGrow: 1,
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    {loading ? (
                        <Spinner fullPage={false} />
                    ) : (
                        <div onClick={(e) => e.stopPropagation()} style={{ width: '100%' }}>
                            <CustomDataGrid
                                name="toMergeList"
                                columns={list.columns}
                                data={list.rows}
                                page={list.page || 0}
                                totalRows={list.totalRows}
                                pageSize={list.pageSize}
                                loading={loading}
                                query={query}
                                onPageChangeCallback={onPageChangeCallback}
                                onClickCallback={handleRowClick}
                                onClickKey="uniqueid"
                            />
                        </div>
                    )}
                </Box>
            </Box>
        </Drawer>
            <CopyToPracticeModal
                open={modalOpen}
                onClose={handleModalClose}
                onConfirm={handleModalConfirm}
                selectedRowData={selectedRowData}
            />
    </>
    );
};

export default ToMergeListDrawer;
