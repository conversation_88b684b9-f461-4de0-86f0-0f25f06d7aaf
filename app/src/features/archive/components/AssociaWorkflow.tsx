import * as React from "react";
import {
    <PERSON>alogActions,
    Button,
    Dialog,
    DialogContent,
    DialogTitle,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

import SpinnerButton from "../../../custom-components/SpinnerButton";
import ToastNotification from "../../../custom-components/ToastNotification";
import usePostCustom from "../../../hooks/usePostCustom";

const defaultQuery = {
    selectedModel: "-1",
    file_uids: "",
};

export default function AssociaWorkflow({
    open,
    setOpen,
    title,
    workflowModels,
    selectedGridRows,
}: any) {
    const [query, setQuery] = React.useState(defaultQuery);
    const [openNotification, setOpenNotification] = React.useState(false);
    const [loading, setLoading] = React.useState(false);
    const { t } = useTranslation();

    const connectworkflows = usePostCustom(
        `/archive/connectworkflows?noTemplateVars=true`
    );

    const handleClose = () => {
        setOpen(false);
    };

    const handleInputChange = (event: any) => {
        const { name, value } = event.target;
        setQuery((prev) => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async () => {
        try {
            setLoading(true);
            if (query.selectedModel !== "-1") {
                await connectworkflows.doFetch(true, {
                    ...query,
                    file_uids: selectedGridRows.join(","),
                });
                setQuery(defaultQuery);
                handleClose();
            } else {
                setOpenNotification(true);
            }
        } catch (error) {
            console.log("Error:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <React.Fragment>
            <ToastNotification
                showNotification={openNotification}
                setShowNotification={setOpenNotification}
                text="Seleziona un modello"
                severity="warning"
            />
            <Dialog
                open={open}
                onClose={handleClose}
                fullWidth={true}
                maxWidth={"sm"}
                PaperProps={{
                    component: "form",
                    onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
                        event.preventDefault();
                        const formData = new FormData(event.currentTarget);
                        Object.fromEntries((formData as any).entries());
                        handleSubmit();
                    },
                }}
            >
                <DialogTitle>{title}</DialogTitle>
                <DialogContent>
                    <>
                        <FormControl variant="outlined" fullWidth>
                            <InputLabel id="select-label">
                                {t("Workflow")}
                            </InputLabel>
                            <Select
                                labelId="select-label"
                                value={query.selectedModel}
                                onChange={handleInputChange}
                                name="selectedModel"
                            >
                                <MenuItem value="-1">
                                    {t("Seleziona un modello")}
                                </MenuItem>
                                {workflowModels &&
                                    workflowModels.map((model: any) => (
                                        <MenuItem
                                            key={model.id}
                                            value={model.id}
                                        >
                                            {model.nome}
                                        </MenuItem>
                                    ))}
                            </Select>
                        </FormControl>
                    </>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>{t("Annulla")}</Button>
                    <SpinnerButton
                        type="submit"
                        label="Conferma"
                        isLoading={loading}
                        variant="contained"
                    />
                </DialogActions>
            </Dialog>
        </React.Fragment>
    );
}
