import { useEffect, useState } from "react";
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Grid,
    Chip,
    Typography,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import usePostCustom from "../../../hooks/usePostCustom";
import ToastNotification from "../../../custom-components/ToastNotification";
import SpinnerButton from '../../../custom-components/SpinnerButton';

const PersonalizzaList = ({ fields, dynamicFields, open, setOpen, selectedFields, selectedDynamicFields }: any) => {
    const [selectedChips, setSelectedChips] = useState<Set<string>>(new Set());
    const [selectedDynamicChips, setSelectedDynamicChips] = useState<Set<string>>(new Set());
    const [showErrorToast, setShowErrorToast] = useState(false);
    const [loading, setLoading] = useState(false);
    const [resetLoading, setResetLoading] = useState(false);

    const { t } = useTranslation();

    const saveCampi = usePostCustom("archive/setcustomfields");
    const saveCampiDinamici = usePostCustom("campidinamici/setexport");

    const handleClose = () => {
        setOpen(false);
    };

    const fieldNames = fields && Object.keys(fields).map((key) => ({
        id: key,
        name: fields[key].name,
        sql: fields[key].sql,
    }));

    const dynamicFieldsArray = dynamicFields && dynamicFields || [];

    useEffect(() => {
        if (selectedFields) {
            setSelectedChips(new Set(selectedFields));
        }
    }, [selectedFields]);

    useEffect(() => {
        if (selectedDynamicFields) {
            setSelectedDynamicChips(new Set(selectedDynamicFields));
        }
    }, [selectedDynamicFields]);

    const handleChipClick = (id: string) => {
        setSelectedChips((prevState) => {
            const newState = new Set(prevState);
            if (newState.has(id)) {
                newState.delete(id);
            } else {
                newState.add(id);
            }
            return newState;
        });
    };

    const handleDynamicChipClick = (id: string) => {
        setSelectedDynamicChips((prevState) => {
            const newState = new Set(prevState);
            if (newState.has(id)) {
                newState.delete(id);
            } else {
                newState.add(id);
            }
            return newState;
        });
    };

    const saveFields = async () => {
        const formDataDynamicFields = new FormData();
        const formDataFields = new FormData();

        const selectedFields = Array.from(selectedChips);
        const selectedDynamicFields = Array.from(selectedDynamicChips);
        let status = true;

        try {
            setLoading(true);
            selectedFields.forEach((row: any) => {
                formDataFields.append("fields[]", row);
            });

            await saveCampi.doFetch(true, formDataFields);
            selectedDynamicFields.forEach((row: any) => {
                formDataDynamicFields.append("fields[]", row);
            });

            await saveCampiDinamici.doFetch(
                true,
                formDataDynamicFields,
            );
        } catch (error) {
            console.error("Error saving fields:", error);
            status = false;
        } finally {
            setLoading(false);
        }

        if (status) {
            setOpen(false);
        } else {
            setShowErrorToast(true);
        }
    };

    const resetColumns = async () => {
        let status = true;

        try {
            setResetLoading(true);
            const emptySaveCampi = new FormData();

            await saveCampi.doFetch(true, emptySaveCampi);

            const emptySaveCampiDinamici = new FormData();
            await saveCampiDinamici.doFetch(true, emptySaveCampiDinamici);

        } catch (error) {
            console.error("Error resetting columns:", error);
            status = false;
        } finally {
            setResetLoading(false);
            setSelectedDynamicChips(new Set());
            setSelectedChips(new Set());
        }

        if (status) {
            setOpen(false);
        } else {
            setShowErrorToast(true);
        }
    };

    return (
        <>
            <Dialog
                open={open}
                onClose={handleClose}
                PaperProps={{
                    component: "form",
                    onSubmit: async (event: React.FormEvent<HTMLFormElement>) => {
                        event.preventDefault();
                        await saveFields();
                    },
                }}
            >
                <DialogTitle>{t("Personalizza Lista")}</DialogTitle>
                <DialogContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ m: 1 }}>
                        {t("Campi")}
                    </Typography>
                    <Grid container spacing={1}>
                        {fieldNames &&
                            fieldNames.map((field: any) => (
                                <Grid item key={field.id}>
                                    <Chip key={field.id} size="small" label={field.name} clickable variant={selectedChips.has(field.id) ? "outlined" : "filled"} onClick={() => handleChipClick(field.id)} />
                                </Grid>
                            ))}
                    </Grid>
                    <Typography variant="subtitle1" gutterBottom sx={{ ml: 1, mb: 1, mt: 4 }}>
                        {t("Campi Dinamici")}
                    </Typography>
                    <Grid container spacing={1}>
                        {dynamicFieldsArray &&
                            dynamicFieldsArray.map((field: any) => (
                                <Grid item key={field.id}>
                                    <Chip key={field.id} size="small" label={field.nome} clickable variant={selectedDynamicChips.has(field.id) ? "outlined" : "filled"} onClick={() => handleDynamicChipClick(field.id)} />
                                </Grid>
                            ))}
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <SpinnerButton onClick={resetColumns} variant="contained" color="error" label={t("Ripristina colonne")} isLoading={resetLoading} />
                    <Button onClick={handleClose} color="secondary">
                        {t("Annulla")}
                    </Button>
                    <SpinnerButton type="submit" variant="contained" color="primary" label="Conferma" isLoading={loading} />
                </DialogActions>
            </Dialog>
            <ToastNotification
                showNotification={showErrorToast}
                setShowNotification={setShowErrorToast}
                text={t("Si è verificato un errore nel salvataggio dei campi")}
                severity="error"
            />
        </>
    );
};

export default PersonalizzaList;