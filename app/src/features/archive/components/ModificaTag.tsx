import * as React from 'react';
import {
    Text<PERSON>ield,
    DialogActions,
    Button,
    Dialog,
    DialogContent,
    DialogTitle,
    Checkbox,
    FormControlLabel,
    FormGroup,
    Grid,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from '@vapor/react-extended';

import SpinnerButton from '../../../custom-components/SpinnerButton';
import { useGetAllTags } from "../hooks/useArchiveData";
import usePostCustom from "../../../hooks/usePostCustom";

import AddCircleOutlineOutlinedIcon from '@mui/icons-material/AddCircleOutlineOutlined';

export default function ModificaTag({ open, setOpen, title, selectedGridRows }: any) {
    const [name, setName] = React.useState("");
    const [searchTerm, setSearchTerm] = React.useState("");
    const [loading, setLoading] = React.useState(false);
    const [tags, setTags] = React.useState<any[]>([]);

    const useAddTags = usePostCustom(`archive/newtag`);
    const insertTags = usePostCustom(`archive/inserttags`);

    const { archiveTags, fetchTags } = useGetAllTags(open);

    const { t } = useTranslation();

    const handleClose = () => {
        setOpen(false);
    };

    const handleCheckBoxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const tagId = event.target.name;
        const isChecked = event.target.checked;

        const tagDescription = archiveTags.find((tag: any) => tag.id === tagId)?.id;

        if (!tagDescription) return;

        setTags((prevTags) => {
            if (isChecked) {
                return [...prevTags, tagDescription];
            } else {
                return prevTags.filter(desc => desc !== tagDescription);
            }
        });
    };

    const addTags = async () => {
        const checkParams = {
            nome: name
        }
        try {
            setLoading(true);
            await useAddTags.doFetch(false, checkParams);
        } catch (error) {
            console.log(error);
        } finally {
            setLoading(false);
            setName("");
            fetchTags();
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        const formData = new FormData();
        try {
            setLoading(true);
            tags.forEach((element: any) => {
                formData.append("tags[]", element);
            });
            selectedGridRows.forEach((element: any) => {
                formData.append("pratiche[]", element);
            });

            await insertTags.doFetch(true, formData);
        } catch (error) {
            console.log(error);
        } finally {
            setLoading(false);
            setName("");
            setSearchTerm("");
            setOpen(false);
        }
    };

    const filteredTags = (archiveTags instanceof Array ) ? archiveTags.filter((tag: any) =>
        tag.description.toLowerCase().includes(searchTerm.toLowerCase())
    ) : [];

    return (
        <React.Fragment>
            <Dialog
                open={open}
                onClose={handleClose}
                fullWidth={true}
                maxWidth={"md"}
                PaperProps={{
                    component: 'form',
                }}
            >
                <DialogTitle>{title}</DialogTitle>
                <DialogContent>
                    <Typography sx={{ fontSize: 13, display: "flex", justifyContent: "center" }}>
                        Per inserire un nuovo tag, <strong> Digitare </strong> Il nome e <strong> Premere</strong> Il bottone verde
                    </Typography>
                    <Grid container sx={{ mt: 4 }}>
                        <Grid item xs={2} md={3} sx={{ display: "flex", justifyContent: "center", mt: 1 }}>
                            {t("Nuovo tag")}
                        </Grid>
                        <Grid item xs={6} md={5}>
                            <TextField name="13" onChange={(e) => { setName(e.target.value) }} value={name} />
                        </Grid>
                        <Grid item xs={2} md={2}>
                            <Button onClick={addTags}><AddCircleOutlineOutlinedIcon /></Button>
                        </Grid>
                        <Grid item xs={12} md={12} sx={{ mt: 3, display: "flex", justifyContent: "center" }}>
                            <Typography sx={{ fontSize: 13 }}>{t("Selezionare o deselezionare i tag assegnati alle pratiche")}</Typography>
                        </Grid>
                        <Grid item md={12} sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
                            <TextField
                                name="searchTags"
                                sx={{ width: '60%' }}
                                placeholder='Cerca tag...'
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}  // Update search term
                            />
                        </Grid>
                    </Grid>
                    <Grid container spacing={2}>
                        <Grid item md={12}>
                            <FormGroup>
                                {filteredTags.map((tag: any) => (
                                    <FormControlLabel
                                        key={tag.id}
                                        control={<Checkbox name={tag.id} onChange={handleCheckBoxChange} />}
                                        label={tag.description}
                                    />
                                ))}
                            </FormGroup>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Annulla</Button>
                    <SpinnerButton onClick={handleSubmit} label="Conferma" isLoading={loading} disabled={!(tags?.length > 0)} variant="contained" />
                </DialogActions>
            </Dialog>
        </React.Fragment>
    );
}
