import { <PERSON><PERSON>, But<PERSON>, Box } from "@vapor/react-material";
import useGetCustom from "../../../hooks/useGetCustom";
import AutocompleteFilter from "./AutocompleteFilter";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "../interfaces/archive.interface";
import { useEffect, useRef } from "react";

export default function Tab5(props: IFilterProps) {
  const { defaultQuery, setQuery, query, filterArchiveData } = props;
  const archiveFilterRequest = useGetCustom("default/tags/search-tag?noTemplateVars=true");
  const { t } = useTranslation();

  const fetchOptions = async (searchValue: string) => {
    const response: any = await archiveFilterRequest.doFetch(true, { q: searchValue });
    return Array.isArray(response?.data) ? response.data : [];
  };

  const handleSearch = () => {
    filterArchiveData(query);
  };

  const handleReset = () => {
    const resetQuery = { ...defaultQuery };
    setQuery(resetQuery);
  };

  const isInitialRender = useRef(true);

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    if (query.tagsList && query.tagsList.length > 0) {
      handleSearch();
    }
  }, [query.tagsList]);

  return (
    <Grid container spacing={2} alignItems="flex-end">
      <Grid item xs={12} sm={3} md={3}>
        <AutocompleteFilter
          {...props}
          fetchOptions={fetchOptions}
          queryKey="tagsList"
          queryKey2=""
          label="Cerca tag:"
          placeholder="Cerca tag"
          onReset={handleReset}
          multiple={true}
          target="description"
          queryVal="description"
        />
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "flex-start", gap: 2, mt: 2, ml: 2 }}>
        <Button
          variant="contained"
          color="primary"
          type="submit"
          onClick={handleSearch}
        >
          {t("Cerca")}
        </Button>
        <Button variant="contained" color="primary" onClick={handleReset}>
          {t("Mostra tutti")}
        </Button>
      </Box>
    </Grid>
  );
}
