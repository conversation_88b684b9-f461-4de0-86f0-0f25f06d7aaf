import { TextField } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useState, useEffect, useMemo } from "react";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

interface Option {
    id: string;
    [key: string]: any;
}

interface AutocompleteFilterProps {
    defaultQuery: any;
    query: any;
    setQuery: (query: any) => void;
    onChangeFilterInputs?: (query: any) => void;
    fetchOptions: (searchValue: string) => Promise<Option[]>;
    queryKey: string;
    queryKey2?: string;
    label?: string;
    placeholder?: string;
    width?: string;
    onReset?: () => void;
    multiple?: boolean;
    target?: string;
    queryVal?: string;
    onChange?: (value: Option | null) => void;
}

export default function AutocompleteFilter(props: AutocompleteFilterProps) {
    const { query, setQuery, fetchOptions: fetchOptionsFromProps, queryKey, queryKey2, label = "Search:", placeholder = "Search", multiple = false, target = "nome", queryVal = "id", onChange } = props;

    const { t } = useTranslation();

    const [inputValue, setInputValue] = useState<string>("");
    const [options, setOptions] = useState<Option[]>([]);
    const [loading, setLoading] = useState(false);

    const selectedValue = useMemo(() => {
        const currentValue = query[queryKey];

        if (multiple) {
            const values = Array.isArray(currentValue) ? currentValue : [];
            return options.filter((option) => values.includes(option[queryVal]));
        } else {
            return options.find((option) => option[queryVal] === currentValue) || null;
        }
    }, [query, queryKey, queryVal, options, multiple]);

    const debounce = (func: Function, wait: number) => {
        let timeout: NodeJS.Timeout;
        return (...args: any[]) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func(...args), wait);
        };
    };

    const handleInputChange = (_event: any, newInputValue: string) => {
        setInputValue(newInputValue);
    };

    const debouncedFetchOptions = debounce(async (searchValue: string) => {
        setLoading(true);
        try {
            const newOptions = await fetchOptionsFromProps(searchValue);
            setOptions((prevOptions) => {
                const currentSelectedValues = multiple ? (Array.isArray(query[queryKey]) ? query[queryKey] : []) : [query[queryKey]];

                const selectedOptions = prevOptions.filter((opt) => currentSelectedValues.includes(opt[queryVal]));

                const combinedOptions = [...selectedOptions, ...newOptions];
                return combinedOptions.filter((opt, index, self) => index === self.findIndex((t) => t.id === opt.id));
            });
        } catch (error) {
            console.error("Error fetching options:", error);
        } finally {
            setLoading(false);
        }
    }, 300);

    useEffect(() => {
        debouncedFetchOptions(inputValue);
    }, [inputValue]);

    const handleAutocompleteChange = (_event: any, newValue: Option | Option[] | null) => {
        if (multiple) {
            const values = (newValue as Option[]) || [];
            setQuery({
                ...query,
                [queryKey]: values.map((value) => value[queryVal]),
                ...(queryKey2 && {
                    [queryKey2]: values.map((value) => value[target])
                })
            });
        } else {
            const value = newValue as Option;
            setQuery({
                ...query,
                [queryKey]: value ? value[queryVal] : "",
                ...(queryKey2 && { [queryKey2]: value ? value[target] : "" })
            });
            if (onChange) {
                onChange(value);
            }
        }
    };

    return (
        <CustomAutocomplete
            multiple={multiple}
            value={selectedValue}
            options={options}
            inputValue={inputValue}
            onInputChange={handleInputChange}
            onChange={handleAutocompleteChange}
            loading={loading}
            getOptionLabel={(option: Option) => {
                if (!option) return "";
                if (typeof option === "string") return option;
                return option[target] || "";
            }}
            isOptionEqualToValue={(option: Option, value: Option) => {
                if (!option || !value) return false;
                return option.id === value.id;
            }}
            renderOption={(props: any, option: Option) => (
                <li {...props} key={option.id}>
                    {option[target]}
                </li>
            )}
            renderInput={(params: any) => <TextField {...params} label={label} placeholder={placeholder} variant="outlined" sx={{ width: "100%" }} />}
            sx={{ width: "auto" }}
            noOptionsText={t("Nessuna opzione")}
            loadingText={t("Caricamento...")}
            componentsProps={{
                popper: {
                    sx: { zIndex: 1300 }
                },
                popupIndicator: {
                    title: t("Apri")
                },
                clearIndicator: {
                    title: t("Chiudi")
                }
            }}
        />
    );
}
