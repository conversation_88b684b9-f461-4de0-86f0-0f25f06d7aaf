import { VaporPage } from "@vapor/react-custom";
import { useLocation, useNavigate } from "react-router-dom";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { Macro } from "../../agenda/generalCalendar/sections/MacroTab";

const MacroPage = () => {
    const { t } = useTranslation();
    const location = useLocation();
    const navigate = useNavigate();

    const getBackUrl = () => {
        const params = new URLSearchParams(location.search);
        const fileUniqueid = params.get('fileUniqueid');
        return fileUniqueid ? `/archivedeadlines/deadlines?fileUniqueid=${fileUniqueid}` : '/archivedeadlines/deadlines';
    };

    const rowData = {
        date: new Date()
    };

    const changeView = (view: string) => {
        if (view === "scheda") {
            const backUrl = getBackUrl();
            navigate(backUrl);
        }
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Macro")}
                showBackButton={true}
                pathToPrevPage={getBackUrl()}
            />
            <VaporPage.Section>
                <Macro rowData={rowData} changeView={changeView} />
            </VaporPage.Section>
        </VaporPage>
    );
};

export default MacroPage;
