import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import {
    TextField,
    FormControlLabel,
    Checkbox,
    Stack,
    Button,
    Box,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { useEffect, useRef, useState } from "react";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import { useSaveOnlineDocument } from "./hooks/SaveOnlineDocument";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Typography } from "@vapor/react-extended";
import { Tab, Tabs } from "@vapor/react-extended";
import { EmailDocumentTab } from "./DocumentTabs/EmailDocumentTab";
import { SignDocumentTab } from "./DocumentTabs/SignDocumentTab";
import { ModifyDocumentDetails } from "../../../interfaces/documents.interface";
import { useCheckRemoteRignature } from "./hooks/CheckRemoteSign";
import { useSignDocument } from "./hooks/SignDocument";
import { useSendEmail } from "./hooks/SendMail";
import { useGetDocumentOnlineDetails } from "./hooks/useGetDocumentOnlineDetails";
import { OtherDataTab } from "./DocumentTabs/OtherDataTab";
import { useDeleteOnlineDocument } from "./hooks/useDeleteOnlineDocument";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { useDownloadPdf } from "./hooks/DownloadPdf";

export const OnlineUpdate = () => {
    const { t } = useTranslation();
    const [params] = useSearchParams();
    const uniqueid = params.get("uniqueid");
    const navigate = useNavigate();
    const [title, setTitle] = useState("");
    const [visibile, setVisibile] = useState(false);
    const [save, setSave] = useState(false);
    const [download, setDownload] = useState(false);
    const [editorContent, setEditorContent] = useState("");
    const [selectedTab, setSelectedTab] = useState(1);
    const [sign, setSign] = useState(false);
    const [sendEmail, setSendEmail] = useState(false);
    const [lastEditor] = useState<{
        modificatoda: string;
        modificatoil: string;
    }>();
    const [deleteFile, setDeleteFile] = useState(false);
    const [showDeleteFileModal, setShowDeleteFileModal] = useState(false);

    const saveDocumentResponse = useSaveOnlineDocument({
        save: save,
        titolodocumento: title,
        model: "0",
        action: "",
        content: editorContent,
        documentFileUniqueid: "",
        tagsbuffer: "",
        uniqueid: uniqueid || "",
    });
    const downloadDocumentResponse = useDownloadPdf({
        save: download,
        titolodocumento: title,
        model: "0",
        action: "print",
        content: editorContent,
        documentFileUniqueid: "",
        tagsbuffer: "",
        uniqueid: uniqueid || "",
    });

    const [emailData, setEmailData] = useState({
        document1: "",
        emailAccountUid: "",
        fromemail: "",
        message: "",
        saveInMessages: false,
        subject: "",
        toemail: "",
    });

    const [emailErrors, setEmailErrors] = useState<Partial<typeof emailData>>(
        {}
    );

    const validateEmail = () => {
        const updatedErrors: Partial<typeof emailData> = {};

        if (emailData.fromemail === "") {
            updatedErrors.fromemail = t("Email obbligatorio");
        }
        if (emailData.toemail === "") {
            updatedErrors.toemail = t("Email obbligatorio");
        }
        if (emailData.subject === "") {
            updatedErrors.subject = t("Oggetto obbligatorio");
        }
        if (emailData.message === "") {
            updatedErrors.message = t("Messaggio obbligatorio");
        }
        setEmailErrors(updatedErrors);
        return Object.keys(updatedErrors).length === 0;
    };

    const clearErrorForField = (name: keyof typeof emailData) => {
        if (emailErrors[name]) {
            setEmailErrors((prevErrors) => ({
                ...prevErrors,
                [name]: undefined,
            }));
        }
    };

    const handleUpdate = (event: any) => {
        const { name, value, type, checked } = event.target;
        setEmailData((prevData) => ({
            ...prevData,
            [name]: type === "checkbox" ? checked : value,
        }));
        clearErrorForField(name);
    };

    useEffect(() => {
        if (saveDocumentResponse.hasLoaded) {
            navigate("/documents/documents");
        }
    }, [saveDocumentResponse.hasLoaded, saveDocumentResponse]);

    const downloadPDF = async () => {
        if (download) {
            const blob = new Blob([downloadDocumentResponse.data], {
                type: "application/pdf",
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", title + ".pdf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            setDownload(false);
        }
    };

    useEffect(() => {
        if (downloadDocumentResponse.hasLoaded) {
            downloadPDF();
        }
    }, [downloadDocumentResponse.hasLoaded]);

    const [documentDetails, setDocumentDetails] = useState<undefined | any>();

    const { hasLoaded, data } = useGetDocumentOnlineDetails({
        uniqueId: uniqueid,
    });

    useEffect(() => {
        if (hasLoaded) {
            setTitle(data.result.titolodocumento);
            setEditorContent(data.result.content);

            setDocumentDetails({
                ...documentDetails,
                mittente: "",
            });
        }
    }, [hasLoaded]);

    useSendEmail(emailData, sendEmail);

    useEffect(() => {
        if (sendEmail) {
            setSendEmail(false);
            navigate("/documents/documents");
        }
    }, [sendEmail]);

    const fisrtSignREsponse = useSignDocument({ uid: uniqueid, sign: sign });

    const handleTabChange = (_event: React.SyntheticEvent, newTab: number) => {
        setSelectedTab(newTab);
    };

    const handleUpdateDocumentDetails =
        (field: keyof ModifyDocumentDetails) => (value: any) => {
            if (documentDetails !== undefined) {
                setDocumentDetails({
                    ...documentDetails,
                    [field]: value,
                });
            }
        };
    const [documentStatus, setDocumentStatus] = useState<any>([]);
    const [certificateId, setCertificateId] = useState("");
    const [otpPassword, setOtpPassword] = useState("");
    const [selectedLawyer, setSelectedLawyer] = useState("");
    const [remoteSignOn, setRemoteSignOn] = useState(false);

    const checkUserIdReponse = useCheckRemoteRignature({
        userId: selectedLawyer,
    });

    useEffect(() => {
        if (checkUserIdReponse.hasLoaded) {
            setRemoteSignOn(true);
        }
    }, [checkUserIdReponse.hasLoaded, checkUserIdReponse.loading]);

    const headerRef = useRef(null);

    const deleteDocumentResponse = useDeleteOnlineDocument({
        uniqueid: uniqueid,
        documentFileUniqueid: documentDetails?.documentFileUniqueid,
        remove: deleteFile,
    });

    useEffect(() => {
        if (deleteDocumentResponse.hasLoaded) {
            setDeleteFile(true);
            navigate("/documents/documents");
        }
    }, [deleteDocumentResponse.hasLoaded, deleteDocumentResponse.loading]);
    const getActionButton = () => {
        let htmlArray = [
            <Button
                variant="outlined"
                disabled={title.length === 0}
                onClick={() => setSave(true)}
            >
                {t("Salva")}
            </Button>,
            <Button color="error" onClick={() => setShowDeleteFileModal(true)}>
                {t("Elimina")}
            </Button>,
            <Button onClick={() => setDownload(true)}>{t("Stampa")}</Button>,
        ];
        // selectedTab === 1 &&
        //     htmlArray.push(
        //         <Button variant="outlined" onClick={() => setSign(true)}>
        //             {t("Firma")}
        //         </Button>
        //     );
        selectedTab === 2 &&
            htmlArray.push(
                <Button
                    variant="outlined"
                    onClick={() => {
                        if (validateEmail()) {
                            setSendEmail(true);
                        }
                    }}
                >
                    {t("Invia")}
                </Button>
            );

        return htmlArray;
    };
    return (
        <VaporPage>
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                handleAgree={() => {
                    setDeleteFile(true);
                }}
                handleDecline={() => {
                    setShowDeleteFileModal(false);
                }}
                open={showDeleteFileModal}
                title={t("Elimina documento")}
                colorConfirmButton="error"
                colorDeclineButton="primary"
                confirmText={t(
                    "Eliminare definitivamente il documento e tutti i suoi messaggi?"
                )}
                dividerVariant="fullWidth"
            />
            <PageTitle
                title={
                    uniqueid === undefined
                        ? t("Nuovo documento online")
                        : title ?? ""
                }
                pathToPrevPage="/documents/documents"
                actionButtons={
                    uniqueid === null
                        ? [
                              <Button
                                  variant="outlined"
                                  disabled={title.length === 0}
                                  onClick={() => setSave(true)}
                              >
                                  {t("Salva")}
                              </Button>,
                          ]
                        : getActionButton()
                }
            ></PageTitle>
            <VaporPage.Section>
                <Stack direction="row" justifyContent="space-between">
                    <Box>
                        <Stack width={300} alignItems="flex-start" gap={1}>
                            <TextField
                                sx={{ width: 300 }}
                                label={t("Titolo")}
                                onChange={(e: any) => {
                                    setTitle(e.target.value);
                                }}
                                value={title}
                            ></TextField>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={visibile}
                                        onChange={() => setVisibile(!visibile)}
                                    />
                                }
                                label={t("Visibile ad esterni")}
                                labelPlacement="start"
                            />
                        </Stack>
                        <Box width={600}>
                            <CKEditor
                                ref={headerRef}
                                config={{
                                    toolbar: [
                                        "heading",
                                        "|",
                                        "bold",
                                        "italic",
                                        "blockQuote",
                                        "link",
                                        "numberedList",
                                        "bulletedList",
                                        "uploadImage",
                                        "insertTable",
                                        "mediaEmbed",
                                        "|",
                                        "undo",
                                        "redo",
                                    ],
                                }}
                                editor={ClassicEditor as any}
                                data={editorContent}
                                onReady={(editor) => {
                                    if (editor) {
                                        editor.editing.view.change((writer) => {
                                            writer.setStyle(
                                                "min-height",
                                                "400px",
                                                editor.editing.view.document.getRoot()!
                                            );

                                            writer.setStyle(
                                                "color",
                                                "#343a40",
                                                editor.editing.view.document.getRoot()!
                                            );
                                        });
                                    }
                                }}
                                onChange={(_event, editor) => {
                                    const data = editor.getData();
                                    setEditorContent(data);
                                }}
                            />
                        </Box>
                        <Typography>
                            {t("Suggerimenti per l'utilizzo:")}
                        </Typography>
                        <Typography>
                            {t(
                                'La pressione del tasto "Invio" crea un nuovo paragrafo.'
                            )}
                        </Typography>
                        <Typography>
                            {t(
                                'Se si vuole andare a capo nello stesso paragrafo premere i tasti "Maiusc" + "Invio".'
                            )}
                        </Typography>
                    </Box>
                    {uniqueid && (
                        <Stack gap={3} paddingBottom={3}>
                            <Tabs
                                size="extraSmall"
                                variant="standard"
                                value={selectedTab}
                                onChange={handleTabChange}
                            >
                                <Tab value={1} label={t("Firma documento")} />
                                <Tab
                                    value={2}
                                    label={t("Invio documento tramite email")}
                                />
                                <Tab value={3} label={t("Altri dati")} />
                            </Tabs>
                            <Stack gap={2}>
                                {selectedTab === 1 && hasLoaded && (
                                    <SignDocumentTab
                                        certificateId={certificateId}
                                        checkUserIdReponse={checkUserIdReponse}
                                        otpPassword={otpPassword}
                                        selectedLawyer={selectedLawyer}
                                        setCertificateId={setCertificateId}
                                        setOtpPassword={setOtpPassword}
                                        setSelectedLawyer={setSelectedLawyer}
                                        documentData={data}
                                        nomefileForBtn={
                                            documentDetails?.nomefileForBtn
                                        }
                                        remoteSignOn={remoteSignOn}
                                        setRemoteSignOn={setRemoteSignOn}
                                        setSign={setSign}
                                        sign={sign}
                                        uniqueid={uniqueid}
                                        fisrtSignREsponse={fisrtSignREsponse}
                                    />
                                )}
                                {selectedTab === 2 && hasLoaded && (
                                    <EmailDocumentTab
                                        emailErrors={emailErrors}
                                        nomefileForEmail={
                                            documentDetails?.nomefileForEmail
                                        }
                                        emailData={emailData}
                                        handleUpdate={handleUpdate}
                                        documentData={data}
                                        lastEditor={lastEditor}
                                    />
                                )}
                                {selectedTab === 3 && hasLoaded && (
                                    <OtherDataTab
                                        documentStatus={documentStatus}
                                        setDocumentStatus={setDocumentStatus}
                                        documentData={data}
                                        documentDetails={documentDetails}
                                        handleUpdateDocumentDetails={
                                            handleUpdateDocumentDetails
                                        }
                                    />
                                )}
                            </Stack>
                        </Stack>
                    )}
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
