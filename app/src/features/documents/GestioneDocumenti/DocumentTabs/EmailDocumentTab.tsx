import { Stack, TextField, Checkbox, FormControlLabel, FormControl, InputLabel, Hyperlink, ListItem } from "@vapor/react-material";
import { DocumentData } from "../../../../interfaces/documents.interface";
import { useAddUserEmail } from "../hooks/AddUserEmail";
import { useTranslation } from "@1f/react-sdk";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { useEffect, useState } from "react";
import { useAddExternalEmail } from "../hooks/AddExternalEmail";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const filter = createFilterOptions();

interface email {
    email: string;
    uniqueid: string;
}

export const UserEmails = ({ options, handleUpdate, emailErrors }: { options: email[]; handleUpdate: any; emailErrors: any }) => {
    const { t } = useTranslation();
    const [value, setValue] = useState<email>({
        email: "",
        uniqueid: ""
    });

    const [addEmail, setAddEmail] = useState(false);

    const { loading, hasLoaded } = useAddUserEmail({
        insertItem: value.email,
        addEmail: addEmail
    });

    useEffect(() => {
        if (!loading && hasLoaded) {
            setAddEmail(false);
            setValue({ email: "", uniqueid: "" });
        }
    }, [loading, hasLoaded]);

    const handleChange = (_event: React.SyntheticEvent, newValue: any) => {
        if (typeof newValue === "string") {
            setValue({
                email: newValue,
                uniqueid: ""
            });
        } else if (newValue && newValue.inputValue) {
            setValue({
                email: newValue.inputValue,
                uniqueid: ""
            });
            setAddEmail(true);
        } else {
            setValue(newValue);
        }
        handleUpdate({
            target: {
                name: "fromemail",
                value: newValue.email || newValue.inputValue || newValue,
                type: "text",
                checked: false
            }
        });
    };

    return (
        <CustomAutocomplete
            value={value}
            onChange={handleChange}
            filterOptions={(options: any, params: any) => {
                const filtered = filter(options, params);

                const { inputValue } = params;
                const isExisting = options.some((option: any) => inputValue === option.email);
                if (inputValue !== "" && !isExisting) {
                    filtered.push({
                        inputValue,
                        email: `Aggiungi "${inputValue}"`
                    });
                }

                return filtered;
            }}
            selectOnFocus
            clearOnBlur
            handleHomeEndKeys
            options={options}
            getOptionLabel={(option: any) => {
                if (typeof option === "string") {
                    return option;
                }
                if (option.inputValue) {
                    return option.inputValue;
                }
                return option.email;
            }}
            renderOption={(props: any, option: email) => (
                <ListItem {...props} key={option.email} value={option.uniqueid}>
                    {option.email}
                </ListItem>
            )}
            sx={{ width: 300 }}
            renderInput={(params: any) => <TextField helperText={emailErrors.fromemail} error={!!emailErrors.fromemail} {...params} label={t("Da")} />}
        />
    );
};

export const ExternalEmails = ({ options, handleUpdate, emailErrors }: { options: email[]; handleUpdate: any; emailErrors: any }) => {
    const { t } = useTranslation();
    const [value, setValue] = useState<email>({
        email: "",
        uniqueid: ""
    });

    const [addEmail, setAddEmail] = useState(false);

    const { loading, hasLoaded } = useAddExternalEmail({
        insertItem: value.email,
        addEmail: addEmail
    });

    useEffect(() => {
        if (!loading && hasLoaded) {
            setAddEmail(false);
            setValue({ email: "", uniqueid: "" });
        }
    }, [loading, hasLoaded]);

    const handleChange = (_event: React.SyntheticEvent, newValue: any) => {
        if (typeof newValue === "string") {
            setValue({
                email: newValue,
                uniqueid: ""
            });
        } else if (newValue && newValue.inputValue) {
            setValue({
                email: newValue.inputValue,
                uniqueid: ""
            });
            setAddEmail(true);
        } else {
            setValue(newValue);
        }
        handleUpdate({
            target: {
                name: "toemail",
                value: newValue.email || newValue.inputValue || newValue,
                type: "text",
                checked: false
            }
        });
    };

    return (
        <CustomAutocomplete
            value={value}
            onChange={handleChange}
            filterOptions={(options: any, params: any) => {
                const filtered = filter(options, params);

                const { inputValue } = params;
                const isExisting = options.some((option: any) => inputValue === option.email);
                if (inputValue !== "" && !isExisting) {
                    filtered.push({
                        inputValue,
                        email: `Aggiungi "${inputValue}"`
                    });
                }

                return filtered;
            }}
            selectOnFocus
            clearOnBlur
            handleHomeEndKeys
            options={options}
            getOptionLabel={(option: any) => {
                if (typeof option === "string") {
                    return option;
                }
                if (option.inputValue) {
                    return option.inputValue;
                }
                return option.email;
            }}
            renderOption={(props: any, option: email) => (
                <ListItem {...props} key={option.email} value={option.uniqueid}>
                    {option.email}
                </ListItem>
            )}
            sx={{ width: 300 }}
            renderInput={(params: any) => <TextField helperText={emailErrors.toemail} error={!!emailErrors.toemail} {...params} label={t("A")} />}
        />
    );
};

export const EmailDocumentTab = ({ nomefileForEmail, emailData, handleUpdate, documentData, emailErrors, lastEditor }: { nomefileForEmail: string | undefined; handleUpdate: any; emailData: any; documentData: DocumentData; emailErrors: any; lastEditor: any }) => {
    const { t } = useTranslation();
    return (
        <>
            <Stack direction="column" gap={3} alignItems="flex-start" width={300}>
                <UserEmails handleUpdate={handleUpdate} options={documentData.fromEmails} emailErrors={emailErrors} />
                <ExternalEmails handleUpdate={handleUpdate} options={documentData.fromEmails} emailErrors={emailErrors} />
                <FormControl sx={{ minWidth: 300 }}>
                    <Stack direction="row" gap={1}>
                        <InputLabel>Allegato:</InputLabel>
                        {nomefileForEmail && <Hyperlink>{nomefileForEmail}</Hyperlink>}
                    </Stack>
                </FormControl>
                <TextField sx={{ width: 300 }} id="" name="subject" label={t("Oggetto")} onChange={handleUpdate} value={emailData.subject} helperText={emailErrors.subject} error={!!emailErrors.subject} />
                <TextField sx={{ width: 300 }} id="" name="message" label={t("Messaggio")} onChange={handleUpdate} value={emailData.message} helperText={emailErrors.message} error={!!emailErrors.message} />
                <FormControlLabel control={<Checkbox name="saveInMessages" checked={emailData.saveInMessages} onChange={handleUpdate} />} label={t("Salva in Messaggi")} labelPlacement="start" />
            </Stack>
            <p>
                {t("Ultima modifica:")} {lastEditor?.modificatoda} - {lastEditor?.modificatoil}
            </p>
        </>
    );
};
