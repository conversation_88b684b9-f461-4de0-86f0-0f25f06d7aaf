import { FormControl, InputLabel, Stack, Select, MenuItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { DocumentData, ModifyDocumentDetails } from "../../../../interfaces/documents.interface";
import { AnagraficheSearch } from "../../../../custom-components/AnagraficheSearch";
import { useState } from "react";
import { DocumentStatusSearch } from "../DocumentStatusSearch";

export const OtherDataTab = ({ documentData, documentStatus, documentDetails, handleUpdateDocumentDetails, setDocumentStatus }: { documentData: DocumentData; setDocumentStatus: any; documentStatus: any; documentDetails: ModifyDocumentDetails; handleUpdateDocumentDetails: (field: keyof ModifyDocumentDetails) => (value: any) => void }) => {
    const { t } = useTranslation();

    const [search, setSearch] = useState("");

    return (
        <Stack gap={4}>
            <DocumentStatusSearch handleUpdate={handleUpdateDocumentDetails} documentStatus={documentStatus} setDocumentStatus={setDocumentStatus} />
            <AnagraficheSearch
                width={400}
                inputValue={search}
                onChange={(_e, value) => handleUpdateDocumentDetails("mittente")(value)}
                onInputChange={(_e, value) => {
                    setSearch(value);
                }}
                query={""}
                value={documentDetails.mittente}
            />

            <FormControl>
                <InputLabel>{t("In/Out")}</InputLabel>
                <Select sx={{ width: 300 }} value={documentDetails.in_out} onChange={(e: any) => handleUpdateDocumentDetails("in_out")(e.target.value)}>
                    {documentData.documentsinout !== undefined && documentData.documentsinout.map((option: any) => <MenuItem value={option.id}>{option.nome}</MenuItem>)}
                </Select>
            </FormControl>
        </Stack>
    );
};
