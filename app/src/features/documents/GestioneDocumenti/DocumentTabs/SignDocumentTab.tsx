import {
    DocumentData,
    RemoteSignature,
} from "../../../../interfaces/documents.interface";
import {
    Stack,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    TextField,
    Button,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended/Typography";
import { StatusBadge } from "@vapor/react-custom";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { useRemoteSign } from "../hooks/useRemoteSign";
import { useGenerateOTP } from "../hooks/useGenerateOTP";

export const SignDocumentTab = ({
    documentData,
    nomefileForBtn,
    selectedLawyer,
    checkUserIdReponse,
    certificateId,
    otpPassword,
    setCertificateId,
    setOtpPassword,
    setSelectedLawyer,
    setSign,
    setRemoteSignOn,
    uniqueid,
    fisrtSignREsponse,
}: {
    documentData: DocumentData;
    nomefileForBtn: string | undefined;
    selectedLawyer: string;
    setSelectedLawyer: Dispatch<SetStateAction<string>>;
    checkUserIdReponse: {
        data: RemoteSignature;
        hasLoaded: boolean;
        loading: boolean;
    };
    certificateId: string;
    setCertificateId: Dispatch<SetStateAction<string>>;
    otpPassword: string;
    setOtpPassword: Dispatch<SetStateAction<string>>;
    setSign: Dispatch<SetStateAction<boolean>>;
    setRemoteSignOn: Dispatch<SetStateAction<boolean>>;
    remoteSignOn: boolean;
    sign: boolean;
    uniqueid: string;
    fisrtSignREsponse: any;
}) => {
    const { t } = useTranslation();

    const [remoteSign, setRemoteSign] = useState(false);
    const [generateOTPAPI, setgenerateOTPAPI] = useState(false);

    const userSigned = useRemoteSign({
        sessionId: "",
        certificates: certificateId,
        pinOtp: otpPassword,
        folderName: "documents",
        itemUid: uniqueid,
        isSigned: remoteSign,
    });

    const generateOTPAPIResponse = useGenerateOTP({
        uid: uniqueid,
        generateOTPAPI: generateOTPAPI,
    });

    useEffect(() => {
        if (userSigned.hasLoaded) {
            setRemoteSign(false);
        }
    }, [userSigned.hasLoaded, userSigned.loading]);

    useEffect(() => {
        if (generateOTPAPIResponse.hasLoaded) {
            setgenerateOTPAPI(false);
        }
    }, [generateOTPAPIResponse.hasLoaded, generateOTPAPIResponse.loading]);
    const generateOTP = () => {
        // remotesign/generateotp with uid
        setgenerateOTPAPI(true);
    };
    const getsigned = () => {
        setRemoteSign(true);
    };
    return (
        <Stack gap={3}>
            <FormControl>
                <InputLabel>{t("Documento di")}</InputLabel>
                <Select
                    sx={{ width: 300 }}
                    value={selectedLawyer}
                    name="documentoDi"
                    onChange={(e: any) =>
                        setSelectedLawyer(e.target.value as string)
                    }
                >
                    {documentData?.lawyers.map((lawyer: any) => (
                        <MenuItem value={lawyer.id}>
                            {lawyer.nomeutente}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 300 }}>
                <Stack direction="row" gap={1}>
                    {nomefileForBtn && (
                        <>
                            <InputLabel>{t("File:")}</InputLabel>
                            <Typography>{nomefileForBtn}</Typography>
                        </>
                    )}
                </Stack>
            </FormControl>

            {checkUserIdReponse.hasLoaded && !checkUserIdReponse.loading && (
                <>
                    <FormControl sx={{ minWidth: 300 }}>
                        <Stack direction="row" gap={1}>
                            <InputLabel>{t("Firma Remota")}</InputLabel>
                            {checkUserIdReponse.data.remoteSignON === 2 ? (
                                <StatusBadge
                                    bgColor="green"
                                    label={t("Attiva")}
                                />
                            ) : (
                                <StatusBadge
                                    bgColor="yellow"
                                    label={t("Non attiva")}
                                />
                            )}
                        </Stack>
                    </FormControl>
                    <Button variant="outlined" onClick={() => setSign(true)}>
                        {t("Firma")}
                    </Button>
                    {fisrtSignREsponse.data && (
                        <Stack gap={3}>
                            <FormControl>
                                <InputLabel>
                                    {t("Alias certificato")}
                                </InputLabel>
                                <Select
                                    sx={{ width: 300 }}
                                    value={certificateId}
                                    name="Alias certificato"
                                    onChange={(e: any) =>
                                        setCertificateId(
                                            e.target.value as string
                                        )
                                    }
                                >
                                    {checkUserIdReponse.data.certificates.map(
                                        (certificate) => (
                                            <MenuItem
                                                value={certificate.uniqueid}
                                            >
                                                {certificate.username}
                                                {certificate.mobile}
                                            </MenuItem>
                                        )
                                    )}
                                </Select>
                            </FormControl>
                            <TextField
                                type="password"
                                sx={{ width: 300 }}
                                id="Password OTP"
                                label={t("Password OTP")}
                                onChange={(e: any) =>
                                    setOtpPassword(e.target.value)
                                }
                                value={otpPassword}
                            />
                            <Button
                                variant="outlined"
                                onClick={() => setRemoteSignOn(false)}
                            >
                                {t("Annulla")}
                            </Button>
                            <Button variant="outlined" onClick={generateOTP}>
                                {t("Genera OTP")}
                            </Button>
                            <Button variant="outlined" onClick={getsigned}>
                                {t("OK")}
                            </Button>
                        </Stack>
                    )}
                </>
            )}
        </Stack>
    );
};
