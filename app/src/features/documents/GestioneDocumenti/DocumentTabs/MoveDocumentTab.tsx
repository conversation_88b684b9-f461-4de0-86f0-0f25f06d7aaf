import { Dispatch, SetStateAction } from "react";
import { PracticeSearch } from "../../../../custom-components/PracticeSearch";
import {
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Stack,
    Button,
} from "@vapor/react-material";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useTranslation } from "@1f/react-sdk";
import { PRACTICE_DESCRIPTION } from "../Constants";

interface MoveDocumentProps {
    category: string;
    setCategory: Dispatch<SetStateAction<string>>;
    inputValue: string;
    setInputValue: Dispatch<SetStateAction<string>>;
    value: string | null;
    setValue: (value: string | null) => void;
    successNotification: boolean;
    setSuccessNotification: Dispatch<SetStateAction<boolean>>;
}

export const MoveDocumentTab = ({
    category,
    setCategory,
    inputValue,
    setInputValue,
    setValue,
    value,
    successNotification,
    setSuccessNotification,
}: MoveDocumentProps) => {
    const { t } = useTranslation();
    return (
        <Stack direction="column" gap={4}>
            <ToastNotification
                severity="info"
                showNotification={successNotification}
                text={t("Spostamento del documento completato correttamente.")}
                setShowNotification={setSuccessNotification}
            />
            <FormControl>
                <InputLabel>{t("Ricerca pratica per")}</InputLabel>
                <Select
                    sx={{ width: 300 }}
                    value={category}
                    name="Ricerca Practica"
                    onChange={(e: any) => {
                        setCategory(e.target.value as string);
                        setValue("");
                    }}
                >
                    {PRACTICE_DESCRIPTION.map((data: any) => (
                        <MenuItem value={data.value}>{data.label}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Stack direction="row" gap={1} alignItems="flex-end">
                <PracticeSearch
                    inputValue={inputValue}
                    onChange={(_e, value) => {
                        setValue(value as string | null);
                    }}

                    onInputChange={(_e, value) => {
                        setInputValue(value);
                    }}
                    query=""
                    value={value}
                    from="documents"
                    // target={category}
                    width={300}
                />
                <Button onClick={() => setValue("")}>{t("Cambia")}</Button>
            </Stack>
        </Stack>
    );
};
