import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import {
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Stack,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { useNewDocOneDrive } from "./hooks/NewDocOneDrive";
import excel from "../../../assets/images/excel.png";
import word from "../../../assets/images/word.png";
import powerpoint from "../../../assets/images/powerpoint.png";
import { DOCUMENT_TYPES } from "./Constants";
import ToastNotification from "../../../custom-components/ToastNotification";

export const OneDriveNewFile = () => {
    const { t } = useTranslation();
    const [fileName, setFileName] = useState("");
    const [documentType, setDocumentType] = useState(0);
    const [create, setCreate] = useState(false);

    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const createDocumentResponse = useNewDocOneDrive({
        create: create,
        doc_type: documentType,
        fileUniqueid: "",
        folder_id: "",
        one_drive_filename: fileName,
    });

    useEffect(() => {
        if (createDocumentResponse.hasLoaded) {
            if (createDocumentResponse.data.error) {
                setShowErrorMessage(true);
            }
            setCreate(false);
        }
    }, [createDocumentResponse.hasLoaded]);

    const image =
        documentType === 0 ? word : documentType === 1 ? excel : powerpoint;

    return (
        <VaporPage>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={t(
                    "Si è verificato un errore. Riprovare più tardi, se l'errore persiste contattare l'assistenza."
                )}
            />
            <PageTitle
                title="Microsoft OneDrive"
                actionButtons={[
                    {
                        label: "Conferma",
                        onclick: () => setCreate(true),
                        disabled: fileName === "",
                    },
                ]}
                pathToPrevPage="/documents/documents"
            />
            <VaporPage.Section>
                <Stack gap={3}>
                    <TextField
                        sx={{ width: 300 }}
                        label={t("Nome file")}
                        onChange={(e: any) => setFileName(e.target.value)}
                        value={fileName}
                    />

                    <Stack direction="row" gap={1} alignItems="flex-end">
                        <FormControl>
                            <InputLabel>{t("Categoria")}</InputLabel>
                            <Select
                                sx={{ width: 300 }}
                                value={documentType}
                                name="searchCategories"
                                onChange={(e: any) =>
                                    setDocumentType(e.target.value as number)
                                }
                            >
                                {DOCUMENT_TYPES.map((type: any) => (
                                    <MenuItem value={type.value}>
                                        {t(type.label)}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <img
                            src={image}
                            style={{ height: 32, width: 32 }}
                            alt=""
                        />
                    </Stack>
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
