import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { DocumentsGrid } from "./DocumentsGrid";
import { useEffect, useRef, useState } from "react";
import { DocumentListFilters } from "../../../interfaces/documents.interface";
import { DocumentFilters } from "./DocumentFilters";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faTrash, faList } from "@fortawesome/pro-regular-svg-icons";
import { useNavigate } from "react-router-dom";
import { OneDriveMenu } from "./OneDriveMenu";
import { useSynchronizeOneDrive } from "./hooks/SynchronizeOneDrive";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { useDeleteDocuments } from "./hooks/DeleteDocuments";
import { <PERSON><PERSON>, CircularProgress, Button } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { ExportDocumentCsv } from "./hooks/ExportDocumentsCsv";
import { SEARCH_PARAMS } from "./Constants";
import moment from "moment";
import { useGetDocumentsData } from "./hooks/GetDocumentsData";
import { useUpload } from "./hooks/UploadFile";
import ToastNotification from "../../../custom-components/ToastNotification";
export const Documents = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const { data, hasLoaded } = useGetDocumentsData();

    const [searchParams, setSearchParams] =
        useState<DocumentListFilters>(SEARCH_PARAMS);

    const [exportDocuments, setExportDocuments] = useState(false);
    const [modalRemoveDocuments, setModalRemoveDocuments] = useState(false);
    const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>(
        []
    );
    const [synchronizeOneDrive, setSynchronizeOneDrive] = useState(false);
    const [setUploadOneDriveFileLoading] = useState(false);
    const [tempLoaderBoolean, setTempLoaderBoolean] = useState(false);

    const [removeDocuments, setRemoveDocuments] = useState(false);

    /*

    for upload 

    */

    const [file, setFile] = useState<File | null>(null);
    const [upload, setUpload] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const uploadResponse = useUpload({
        file: file,
        upload: upload,
        isExternal: false,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setTempLoaderBoolean(true);
            setFile(event.target.files[0]);
            setUpload((upload) => !upload);
        }
    };

    const handleButtonClick = () => {
        fileInputRef.current?.click();
    };

    useEffect(() => {
        if (hasLoaded) {
            console.log("uploadResponse.loading", uploadResponse.loading);
            setTempLoaderBoolean(uploadResponse.loading);
            setUpload(false);
        } else if (uploadResponse.error !== null) {
            setShowErrorMessage(true);
            setTempLoaderBoolean(uploadResponse.loading);
            setUpload(false);
        }
    }, [uploadResponse.loading, uploadResponse.hasLoaded]);

    const deleteDocumentsResponse = useDeleteDocuments({
        documentIds: selectedDocumentIds,
        remove: removeDocuments,
    });

    useEffect(() => {
        if (hasLoaded) {
            setSearchParams((prevQuery: any) => ({
                ...prevQuery,
                endDate: moment().utc().format("DD/MM/YYYY"),
                startDate: moment()
                    .subtract(1, "year")
                    .utc()
                    .format("DD/MM/YYYY"),
                afterload: true,
            }));
        }
    }, [hasLoaded]);

    useEffect(() => {
        if (deleteDocumentsResponse.hasLoaded) {
            setModalRemoveDocuments(false);
            setRemoveDocuments(false);
            setSearchParams({
                ...searchParams,
                search: true,
            });
        }
    }, [deleteDocumentsResponse.hasLoaded]);

    const oneDriveSynchResponse = useSynchronizeOneDrive({
        fileUId: "",
        synchronizeOneDrive: synchronizeOneDrive,
    });

    const exportDocumentsCsvResponse = ExportDocumentCsv(
        searchParams,
        exportDocuments
    );

    useEffect(() => {
        if (exportDocumentsCsvResponse.hasLoaded) {
            downloadCsv();
        }
    }, [exportDocumentsCsvResponse.hasLoaded, exportDocuments]);

    const downloadCsv = async () => {
        if (exportDocuments) {
            const blob = new Blob([exportDocumentsCsvResponse.data], {
                type: "text/csv",
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Documents.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        setExportDocuments(false);
    };

    const resetSearchParams = () => {
        setSearchParams((prevQuery: any) => ({
            ...prevQuery,
            ...SEARCH_PARAMS,
        }));
    };

    const onDateChange = (name: string, value: Date) => {
        let formattedDate = moment(value).utc().format("DD/MM/YYYY");

        setSearchParams((prevQuery: any) => ({
            ...prevQuery,
            [name]: formattedDate,
        }));
    };

    const onSelectChange = (event: any) => {
        const value = event.target.value;
        const name = event.target.name;

        setSearchParams((prevQuery: any) => ({
            ...prevQuery,
            [name]: value,
        }));
    };

    const onTextFieldChange = (event: any) => {
        const value = event.target.value;
        const id = event.target.id;

        setSearchParams((prevQuery: any) => ({
            ...prevQuery,
            [id]: value,
        }));
    };

    return (
        <VaporPage>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={
                    "Si è verificato un errore. Riprovare più tardi, se l'errore persiste contattare l'assistenza."
                }
            />
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                open={modalRemoveDocuments}
                handleAgree={() => {
                    setRemoveDocuments(true);
                }}
                handleDecline={() => {
                    setModalRemoveDocuments(false);
                }}
                title={t("Elimina Documenti")}
                colorConfirmButton="error"
                confirmText={t("Eliminare i documenti selezionati?")}
                colorDeclineButton="secondary"
            ></ConfirmModal>
            <PageTitle
                title={t("GESTIONE DOCUMENTI")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: t("Esporta in csv"),
                        onclick: () => {
                            setExportDocuments(true);
                        },
                        startIcon: <FontAwesomeIcon icon={faList} />,
                    },
                    <Button
                        disabled={selectedDocumentIds.length === 0}
                        onClick={() =>
                            selectedDocumentIds.length > 0 &&
                            setModalRemoveDocuments(true)
                        }
                        color="error"
                        variant="outlined"
                        startIcon={<FontAwesomeIcon icon={faTrash} />}
                    >
                        {t("Elimina doc selezionati")}
                    </Button>,
                    <OneDriveMenu
                        oneDriveSynchResponse={oneDriveSynchResponse}
                        setSynchronizeOneDrive={setSynchronizeOneDrive}
                        setUploadOneDriveFileLoading={
                            setUploadOneDriveFileLoading
                        }
                    />,

                    <>
                        <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            style={{ display: "none" }}
                        />
                        <Button
                            variant="outlined"
                            onClick={handleButtonClick}
                            disabled={uploadResponse.loading}
                        >
                            {t("Carica")}
                        </Button>
                    </>,

                    {
                        label: t("Nuovo documento online"),
                        onclick: () => {
                            navigate("/documents/onlineupdate");
                        },
                        startIcon: <FontAwesomeIcon icon={faPlus} />,
                    },
                ]}
            ></PageTitle>
            <Stack
                marginLeft={5}
                padding={1}
                direction="row"
                alignItems="center"
                gap={1}
            >
                {tempLoaderBoolean && (
                    <>
                        <CircularProgress />
                        <Typography>
                            {t("Caricamento dei documenti in corso")}
                        </Typography>
                    </>
                )}
                {oneDriveSynchResponse.loading && (
                    <>
                        <CircularProgress />
                        <Typography>
                            {t("Caricamento dei documenti in corso")}
                        </Typography>
                    </>
                )}
            </Stack>
            <VaporPage.Section>
                <DocumentFilters
                    handleDateChange={onDateChange}
                    onSelectChange={onSelectChange}
                    onTextFieldChange={onTextFieldChange}
                    resetSearchParams={resetSearchParams}
                    searchParams={searchParams}
                    setSearchParams={setSearchParams}
                    data={data}
                    hasLoaded={hasLoaded}
                ></DocumentFilters>
            </VaporPage.Section>
            <VaporPage.Section>
                <DocumentsGrid
                    queryParams={searchParams}
                    setSelectedDocumentIds={setSelectedDocumentIds}
                ></DocumentsGrid>
            </VaporPage.Section>
        </VaporPage>
    );
};
