import { useEffect, useRef, useState } from "react";
import { useUpload } from "./hooks/UploadFile";
import { Button, type ButtonProps } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleUp } from "@fortawesome/pro-regular-svg-icons";
import ToastNotification from "../../../custom-components/ToastNotification";

interface UploadButtonProps extends ButtonProps {
    isExternal?: boolean;
    title: string;
    showIcon?: boolean;
    setLoading: (value: boolean) => void;
    setUploadButtonLoader?: any;
}

export const UploadButton = ({
    isExternal = false,
    title,
    showIcon = true,
    setLoading,
    setUploadButtonLoader,
    ...buttonProps
}: UploadButtonProps) => {
    const [file, setFile] = useState<File | null>(null);
    const [upload, setUpload] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const { loading, hasLoaded, error } = useUpload({
        file: file,
        upload: upload,
        isExternal: isExternal,
    });

    useEffect(() => {
        if (hasLoaded) {
            setLoading(loading);
            setUploadButtonLoader(false);
            setUpload(false);
        } else if (error !== null) {
            setShowErrorMessage(true);
            setLoading(loading);
            setUploadButtonLoader(false);
            setUpload(false);
        }
    }, [loading]);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setUploadButtonLoader(true);
            setFile(event.target.files[0]);
            setUpload((upload) => !upload);
        }
    };

    const handleButtonClick = () => {
        fileInputRef.current?.click();
    };

    useEffect(() => {
        console.log("hasLoaded", hasLoaded);
        if (hasLoaded) {
            setFile(null);
            setUpload(false);
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    }, [hasLoaded]);

    return (
        <>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={
                    "Si è verificato un errore. Riprovare più tardi, se l'errore persiste contattare l'assistenza."
                }
            />
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: "none" }}
            />
            <Button
                variant="outlined"
                startIcon={showIcon && <FontAwesomeIcon icon={faCircleUp} />}
                onClick={handleButtonClick}
                disabled={loading}
                {...buttonProps}
            >
                {title}
            </Button>
        </>
    );
};
