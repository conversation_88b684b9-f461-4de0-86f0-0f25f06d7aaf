import Spinner from "../../../custom-components/Spinner";
import useDocumentsFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { Dispatch, SetStateAction, useCallback, useEffect } from "react";
import { DocumentListFilters } from "../../../interfaces/documents.interface";
import { debounce } from "lodash";

export const DocumentsGrid = ({
    queryParams,
    setSelectedDocumentIds,
}: {
    queryParams: DocumentListFilters;
    setSelectedDocumentIds: Dispatch<SetStateAction<string[]>>;
}) => {
    const navigate = useNavigate();
    const { query, setQuery, list, loading } = useDocumentsFilter();

    const debouncedSetQuery = useCallback(
        debounce((newQuery) => {
            setQuery(newQuery);
        }, 300),
        []
    );

    useEffect(() => {
        const newQuery = {
            ...query,
            ...queryParams,
            startDate: queryParams.startDate,
            endDate: queryParams.endDate,
        };

        debouncedSetQuery(newQuery);
    }, [queryParams]);

    const handleClickCallback = (uniqueid: any) => {
        const filename = list.rows.filter((row) => row.uniqueid === uniqueid)[0]
            .nomefile;
        filename
            ? navigate(`/documents/documents/${uniqueid}`)
            : navigate(`/documents/onlineupdate/?uniqueid=${uniqueid}`);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleRowSelection = (rowSelectionModel: GridRowSelectionModel) => {
        setSelectedDocumentIds(rowSelectionModel as string[]);
    };
    return (
        <div>
            {!loading ? (
                <CustomDataGrid
                    name="documents"
                    columns={list.columns}
                    data={list.rows}
                    page={list.page}
                    totalRows={list.totalRows}
                    pageSize={list.pageSize}
                    loading={loading}
                    query={query}
                    selectableRows
                    onRowSelectionModelChange={handleRowSelection}
                    onPageChangeCallback={onPageChangeCallback}
                    onClickCallback={handleClickCallback}
                    disableColumnResize={true}
                    disableColumnReorder={true}
                />
            ) : (
                <Spinner></Spinner>
            )}
        </div>
    );
};
