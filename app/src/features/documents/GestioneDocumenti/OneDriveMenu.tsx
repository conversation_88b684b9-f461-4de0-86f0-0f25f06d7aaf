import { <PERSON><PERSON>, <PERSON><PERSON>, Menu } from "@vapor/react-material";
import { Dispatch, SetStateAction, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate } from "react-router-dom";
import { faCircleUp } from "@fortawesome/pro-regular-svg-icons";
import { faCaretDown } from "@fortawesome/pro-solid-svg-icons";
import { UploadButton } from "./UploadButton";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export const OneDriveMenu = ({
    setSynchronizeOneDrive,
    oneDriveSynchResponse,
    setUploadOneDriveFileLoading,
}: {
    setSynchronizeOneDrive: Dispatch<SetStateAction<boolean>>;
    setUploadOneDriveFileLoading: any;
    oneDriveSynchResponse: {
        loading: any;
        hasLoaded: any;
        data: any;
    };
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
    const open = Boolean(menuAnchor);

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setMenuAnchor(event.currentTarget);
    };
    const handleClose = () => {
        setMenuAnchor(null);
    };
    return (
        <div>
            <Button
                aria-controls="menuId"
                onClick={handleClick}
                variant="outlined"
                startIcon={<FontAwesomeIcon icon={faCircleUp} />}
                endIcon={<FontAwesomeIcon icon={faCaretDown}></FontAwesomeIcon>}
            >
                {t("One drive")}
            </Button>
            <Menu
                id="menuId"
                open={open}
                anchorEl={menuAnchor}
                onClose={handleClose}
            >
                <Stack>
                    <UploadButton
                        title={t("Carica")}
                        isExternal={true}
                        showIcon={false}
                        setLoading={setUploadOneDriveFileLoading}
                    ></UploadButton>
                    <SpinnerButton
                        onClick={() => setSynchronizeOneDrive(true)}
                        label={t("Sincronizza")}
                        isLoading={oneDriveSynchResponse.loading}
                    ></SpinnerButton>
                    <Button onClick={() => navigate("/documents/new")}>
                        {t("Nuovo documento")}
                    </Button>
                </Stack>
            </Menu>
        </div>
    );
};
