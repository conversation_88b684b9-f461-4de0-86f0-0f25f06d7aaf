import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDownloadDocument = ({
    uniqueid,
    download,
}: {
    uniqueid: string | undefined | null;
    download: boolean;
}) => {
    const { loading, doFetch, hasLoaded } = usePostCustom("documents/getFile");

    useEffect(() => {
        if (uniqueid && download) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid, download]);

    return { loading, hasLoaded };
};
