import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

interface NewDocProps {
    one_drive_filename: string;
    doc_type: number;
    fileUniqueid: string;
    folder_id: string;
    create: boolean;
}

export const useNewDocOneDrive = ({
    doc_type,
    fileUniqueid,
    folder_id,
    one_drive_filename,
    create,
}: NewDocProps) => {
    const { doFetch, hasLoaded, loading, data } = usePostCustom(
        "documents/new-doc-onedrive?noTemplateVars=true"
    );

    useEffect(() => {
        if (create && one_drive_filename !== "") {
            doFetch(true, {
                doc_type: doc_type,
                fileUniqueid: fileUniqueid,
                folder_id: folder_id,
                one_drive_filename: one_drive_filename,
            });
        }
    }, [doc_type, one_drive_filename, create]);
    return { hasLoaded, loading, data };
};
