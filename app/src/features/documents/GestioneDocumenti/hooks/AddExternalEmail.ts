import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useAddExternalEmail = ({
    insertItem,
    addEmail,
}: {
    insertItem: string;
    addEmail: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = usePostCustom(
        "emailsadderssbook/toremotesave"
    );

    useEffect(() => {
        if (addEmail) {
            doFetch(true, { insertItem: insertItem });
        }
    }, [insertItem]);

    return { hasLoaded, loading };
};
