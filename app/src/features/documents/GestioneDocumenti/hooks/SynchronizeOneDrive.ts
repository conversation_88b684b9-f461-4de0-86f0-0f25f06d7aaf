import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useSynchronizeOneDrive = ({
    fileUId,
    synchronizeOneDrive,
}: {
    fileUId: string;
    synchronizeOneDrive: boolean;
}) => {
    const { doFetch, hasLoaded, loading, data } = usePostCustom(
        "documents/sync-onedrive?noTemplateVars=true"
    );

    useEffect(() => {
        if (synchronizeOneDrive) {
            doFetch(true, { fileUId: fileUId });
        }
    }, [fileUId, synchronizeOneDrive]);

    return { data, loading, hasLoaded };
};
