import { useTranslation } from "@1f/react-sdk";
import { useCallback, useEffect, useState } from "react";
import { IList } from "../../../../interfaces/general.interfaces";
import useGetCustom from "../../../../hooks/useGetCustom";
import { getDocumentGrid } from "../../../../utilities/documents/gridColumn";

export interface IDefaultQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
}

export default function useDocumentsFilter() {
    const { t } = useTranslation();
    const filterRequest = useGetCustom("documents/list?noTemplateVars=true");

    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "data",
        sortOrder: "desc",
    };

    const [query, setQuery] = useState<any>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const startDownload = async (uid: string) => {
        console.log("startDownload", uid);
        // let res = await startDownloadApi(uid);
        // if (!res) {
        //     toastNotification(
        //         ToastActionTypes.ERROR,
        //         t(
        //             "Si è verificato un errore, si prega di riprovare. Se il problema persiste contattare l'assistenza."
        //         )
        //     );
        // }
    };

    const filterDocumentData = useCallback(async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        if (cQuery.afterload !== undefined && cQuery.afterload) {
            const [columns, response]: any = await Promise.all([
                getDocumentGrid(t, { startDownload }),
                filterRequest.doFetch(true, cQuery),
            ]);
            const { currentPage, totalRows } = response.data;
            setList({
                ...list,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        }
    }, []);
    useEffect(() => {
        filterDocumentData(query);
    }, [query]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterDocumentData: filterDocumentData,
        loading: filterRequest.loading,
    };
}
