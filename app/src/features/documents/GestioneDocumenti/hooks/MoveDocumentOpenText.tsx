import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useMoveDocumentOpenText = ({
    uniqueid,
    move,
}: {
    uniqueid: string | undefined;
    move: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "documents/move-document-open-text"
    );

    useEffect(() => {
        if (move) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid, move]);

    return { data, hasLoaded, loading };
};
