import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

interface EmailData {
    document1: string | undefined;
    fromemail: string;
    toemail: string;
    subject: string;
    message: string;
    saveInMessages: boolean;
    emailAccountUid: string;
}

export const useSendEmail = (
    {
        document1,
        emailAccountUid,
        fromemail,
        message,
        saveInMessages,
        subject,
        toemail,
    }: EmailData,
    sendEmail: boolean
) => {
    const { doFetch, loading, hasLoaded } = usePostCustom(
        "documents/sendemail"
    );

    useEffect(() => {
        if (
            sendEmail &&
            document1 !== "" &&
            fromemail !== "" &&
            message !== "" &&
            subject !== "" &&
            toemail !== ""
        ) {
            doFetch(true, {
                document1: document1,
                emailAccountUid: emailAccountUid,
                fromemail: fromemail,
                message: message,
                saveInMessages: saveInMessages ? "on" : "",
                subject: subject,
                toemail: toemail,
            });
        }
    }, [
        document1,
        fromemail,
        saveInMessages,
        subject,
        toemail,
        sendEmail,
        message,
    ]);

    return { loading, hasLoaded };
};
