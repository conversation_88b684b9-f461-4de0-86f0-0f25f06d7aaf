import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

interface SaveOnlineDocumentProps {
    tagsbuffer: string;
    uniqueid: string;
    documentFileUniqueid: string;
    action: string;
    model: string;
    titolodocumento: string;
    content: string;
    save: boolean;
}

export const useSaveOnlineDocument = ({
    action,
    content,
    documentFileUniqueid,
    model,
    tagsbuffer,
    titolodocumento,
    uniqueid,
    save,
}: SaveOnlineDocumentProps) => {
    const { doFetch, hasLoaded, loading, data } = usePostCustom(
        "documents/onlinesave"
    );

    const formData = new FormData();

    formData.append("action", action);
    formData.append("content", content);
    formData.append("documentFileUniqueid", documentFileUniqueid ?? "");
    formData.append("model", model);
    formData.append("tagsbuffer", tagsbuffer);
    formData.append("titolodocumento", titolodocumento);
    formData.append("uniqueid", uniqueid);

    useEffect(() => {
        if (save) {
            doFetch(true, formData);
        }
    }, [save]);

    return { loading, hasLoaded, data };
};
