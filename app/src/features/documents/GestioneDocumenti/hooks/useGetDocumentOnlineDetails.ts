import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { DocumentData } from "../../../../interfaces/documents.interface";

export const useGetDocumentOnlineDetails = ({
    uniqueId,
}: {
    uniqueId: string | null | undefined;
}): {
    data: DocumentData;
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom(
        "documents/onlineupdate?"
    );

    useEffect(() => {
        if (uniqueId) {
            doFetch(true, { uniqueid: uniqueId });
        }
    }, [uniqueId]);

    return { data, error, hasLoaded, loading };
};
