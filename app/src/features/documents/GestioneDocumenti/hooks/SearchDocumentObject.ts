import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useSearchDocumentObject = ({
    q,
    increment,
}: {
    q: string;
    increment: string;
}) => {
    const { data, doFetch, loading, hasLoaded } = useGetCustom(
        "documents/search-oggetto-documento?noTemplateVars=true"
    );

    useEffect(() => {
        doFetch(true, { q: q, increment: increment });
    }, [q, increment]);

    return { loading, hasLoaded, data };
};
