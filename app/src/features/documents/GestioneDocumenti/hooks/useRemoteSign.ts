import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useRemoteSign = ({
    sessionId,
    certificates,
    pinOtp,
    folderName,
    itemUid,
    isSigned,
}: {
    sessionId: string | null;
    certificates: string;
    pinOtp: string;
    folderName: string;
    itemUid: string;
    isSigned: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "remotesign/sign?noTemplateVars=true"
    );

    useEffect(() => {
        if (certificates !== "" && pinOtp !== "" && isSigned) {
            doFetch(true, {
                sessionId: sessionId,
                certificates: certificates,
                pinOtp: pinOtp,
                folderName: folderName,
                itemUid: itemUid,
            });
        }
    }, [certificates, pinOtp, isSigned]);

    return { loading, hasLoaded, data };
};
