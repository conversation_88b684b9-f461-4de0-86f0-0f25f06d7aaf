import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { DocumentData } from "../../../../interfaces/documents.interface";

export const useGetDocumentsData = (): {
    data: DocumentData;
    hasLoaded: boolean;
    loading: boolean;
    error: any;
} => {
    const { data, doFetch, hasLoaded, loading, error } = useGetCustom(
        "documents/documents"
    );

    useEffect(() => {
        doFetch(true);
    }, []);

    return { data, hasLoaded, error, loading };
};
