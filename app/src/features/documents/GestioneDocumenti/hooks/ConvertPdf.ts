import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useConvertPdf = ({
    dUid,
    convert,
}: {
    dUid: string | undefined;
    convert: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "documents/pdfconverter?noTemplateVars=true"
    );

    useEffect(() => {
        if (convert && dUid) {
            doFetch(true, {
                dUid: dUid,
            });
        }
    }, [dUid, convert]);

    return { loading, hasLoaded, data };
};
