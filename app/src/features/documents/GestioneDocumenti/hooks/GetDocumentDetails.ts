import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { DocumentDetails } from "../../../../interfaces/documents.interface";

export const useGetDocumentDetails = ({
    uniqueId,
}: {
    uniqueId: string | null | undefined;
}): {
    data: DocumentDetails;
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom(
        "documents/getrowdata?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueId) {
            doFetch(true, { uniqueId: uniqueId });
        }
    }, [uniqueId]);

    return { data, error, hasLoaded, loading };
};
