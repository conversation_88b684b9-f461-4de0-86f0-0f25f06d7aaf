import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { ModifyDocumentDetails } from "../../../../interfaces/documents.interface";

export const useSaveDocumentDetails = ({
    documentDetails,
    save,
}: {
    documentDetails: undefined | ModifyDocumentDetails;
    save: boolean;
}) => {
    const { doFetch, loading, hasLoaded } = usePostCustom("documents/save");

    const documentFormData = new FormData();

    if (documentDetails) {
        Object.entries(documentDetails).forEach(([key, value]) => {
            if (key !== "folders" && key !== "documentoData") {
                if (key === "externalUser" && documentDetails.visibile === 1) {
                    documentFormData.append("externalUser[]", String(-1));
                } else {
                    documentFormData.append(key, String(value));
                }
                documentFormData.append("documentPratica", "");
            }
        });
    }

    useEffect(() => {
        if (save) {
            doFetch(true, documentFormData);
        }
    }, [save]);

    return { loading, hasLoaded };
};
