import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

interface MoveDocumentProps {
    fileUid: string | undefined;
    archiveSearchTarget: string | undefined;
    uniqueid: string | undefined;
    fileuniqueid: string | undefined;
    move: boolean;
}

export const useMoveDocument = ({
    archiveSearchTarget,
    fileUid,
    fileuniqueid,
    uniqueid,
    move,
}: MoveDocumentProps) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "default/documents/move-document?noTemplateVars=true"
    );

    useEffect(() => {
        if (archiveSearchTarget !== "" && fileUid !== "" && move) {
            doFetch(true, {
                archiveSearchTarget: archiveSearchTarget,
                fileUid: fileUid,
                fileuniqueid: fileuniqueid || "",
                uniqueid: uniqueid,
            });
        }
    }, [archiveSearchTarget, fileUid, fileuniqueid, move, uniqueid]);
    return { hasLoaded, loading, data };
};
