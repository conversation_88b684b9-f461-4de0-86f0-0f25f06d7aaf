import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useSearchArchive = ({
    q,
    increment,
}: {
    q: string;
    increment: string;
}) => {
    const { data, doFetch, loading, hasLoaded } = useGetCustom(
        "default/archive/search?noTemplateVars=true&from=calendar"
    );

    useEffect(() => {
        doFetch(true, { q: q, increment: increment });
    }, [q, increment]);

    return { loading, hasLoaded, data };
};
