import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useGenerateOTP = ({
    uid,
    generateOTPAPI,
}: {
    uid: string | null;
    generateOTPAPI: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        " remotesign/generateotp?noTemplateVars=true"
    );

    useEffect(() => {
        if (uid !== "" && generateOTPAPI) {
            doFetch(true, {
                uid: uid,
            });
        }
    }, [uid, generateOTPAPI]);

    return { loading, hasLoaded, data };
};
