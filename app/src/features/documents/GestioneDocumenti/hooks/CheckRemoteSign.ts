import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { RemoteSignature } from "../../../../interfaces/documents.interface";

export const useCheckRemoteRignature = ({
    userId,
}: {
    userId: string | undefined;
}): { data: RemoteSignature; hasLoaded: boolean; loading: boolean } => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom(
        "documents/check-remote-sign?noTemplateVars=true"
    );

    useEffect(() => {
        if (userId !== "") doFetch(true, { userId: userId });
    }, [userId]);

    return { data, hasLoaded, loading };
};
