import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import {
    DocumentListData,
    DocumentListFilters,
} from "../../../../interfaces/documents.interface";

export const useGetDocuments = (
    params: DocumentListFilters
): {
    data: DocumentListData;
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom(
        "documents/list?noTemplateVars=true"
    );

    useEffect(() => {
        if (params.search) {
            doFetch(true, {
                ...params,
                startDate:
                    params.startDate === null || params.startDate === ""
                        ? ""
                        : params.startDate,
                endDate:
                    params.endDate === null || params.endDate === ""
                        ? ""
                        : params.endDate,
            });
        }
    }, [
        params.archiveSubject,
        params.archiveSubjectRelation,
        params.contractSearch,
        params.contractUuid,
        params.documentoDi,
        params.endDate,
        params.fileSearch,
        params.fileUniqueid,
        params.note,
        params.noupdate,
        params.protocollo,
        params.searchCategories,
        params.searchField,
        params.signatureStatus,
        params.startDate,
        params.titolodocumento,
        params.visibility,
        params.search,
    ]);

    return { data, error, hasLoaded, loading };
};
