import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { DeleteDocumentsProps } from "../../../../interfaces/documents.interface";

export const useDeleteDocuments = ({
    remove,
    documentIds,
}: DeleteDocumentsProps) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "documents/delete-all"
    );

    const formData = new FormData();

    for (let i = 0; i < documentIds.length; i++) {
        formData.append(`uIds[]`, `rowChecked_${documentIds[i]}`);
    }

    useEffect(() => {
        if (remove) {
            doFetch(true, formData);
        }
    }, [remove]);

    return { data, hasLoaded, loading };
};
