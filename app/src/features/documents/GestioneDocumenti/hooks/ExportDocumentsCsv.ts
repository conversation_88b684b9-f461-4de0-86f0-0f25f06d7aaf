import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { DocumentListFilters } from "../../../../interfaces/documents.interface";

export const ExportDocumentCsv = (
    params: DocumentListFilters,
    call: boolean
) => {
    const { data, doFetch, hasLoaded } = useGetCustom(
        "documents/exportcsv?noTemplateVars=true"
    );

    useEffect(() => {
        if (call) {
            doFetch(true, params);
        }
    }, [
        call,
        params.archiveSubject,
        params.archiveSubjectRelation,
        params.contractSearch,
        params.contractUuid,
        params.documentoDi,
        params.endDate,
        params.fileSearch,
        params.fileUniqueid,
        params.note,
        params.noupdate,
        params.protocollo,
        params.searchCategories,
        params.searchField,
        params.signatureStatus,
        params.startDate,
        params.titolodocumento,
        params.visibility,
    ]);

    return { data, hasLoaded };
};
