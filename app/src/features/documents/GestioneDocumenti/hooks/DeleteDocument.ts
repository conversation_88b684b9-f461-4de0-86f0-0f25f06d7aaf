import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDeleteDocument = ({
    uniqueid,
    documentFileUniqueid,
    remove,
}: {
    uniqueid: string | undefined;
    documentFileUniqueid: string | undefined;
    remove: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = usePostCustom(
        "documents/delete?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueid !== "" && documentFileUniqueid !== "" && remove) {
            doFetch(true, {
                uniqueid: uniqueid,
                documentFileUniqueid: documentFileUniqueid,
            });
        }
    }, [uniqueid, documentFileUniqueid, remove]);

    return { loading, hasLoaded };
};
