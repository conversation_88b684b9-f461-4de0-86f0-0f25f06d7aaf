import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useUpload = ({
    file,
    upload,
    isExternal,
}: {
    file: any;
    upload: boolean;
    isExternal: boolean;
}) => {
    const { data, doFetch, error, hasLoaded, loading } = usePostCustom(
        "default/documents/upload"
    );
    const uploadData = new FormData();

    if (isExternal) {
        uploadData.append("external", "1");
    }
    uploadData.append("files[]", file);

    useEffect(() => {
        if (upload) {
            doFetch(true, uploadData);
        }
    }, [upload, file, isExternal]);

    return { data, hasLoaded, loading, error };
};
