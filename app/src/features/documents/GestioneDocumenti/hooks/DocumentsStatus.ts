import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useAddDocumentsStatus = ({
    insertItem,
    addItem,
}: {
    insertItem: string;
    addItem: boolean;
}) => {
    const { data, doFetch, loading, hasLoaded } = usePostCustom(
        "documentstatus/remotesave?noTemplateVars=true"
    );

    useEffect(() => {
        if (insertItem !== "" && addItem) {
            doFetch(true, { insertItem: insertItem });
        }
    }, [insertItem]);

    return { data, loading, hasLoaded };
};
