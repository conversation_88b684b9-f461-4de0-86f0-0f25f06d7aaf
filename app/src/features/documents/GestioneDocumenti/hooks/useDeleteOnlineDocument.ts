import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useDeleteOnlineDocument = ({
    uniqueid,
    documentFileUniqueid,
    remove,
}: {
    uniqueid: string | null;
    documentFileUniqueid: string | undefined;
    remove: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = useGetCustom(
        "documents/onlinedelete?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueid !== "" && documentFileUniqueid !== "" && remove) {
            doFetch(true, {
                uniqueid: uniqueid,
                documentFileUniqueid: documentFileUniqueid,
            });
        }
    }, [uniqueid, documentFileUniqueid, remove]);

    return { loading, hasLoaded };
};
