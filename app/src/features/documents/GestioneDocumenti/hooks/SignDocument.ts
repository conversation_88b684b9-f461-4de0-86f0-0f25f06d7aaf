import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useSignDocument = ({
    uid,
    sign,
}: {
    uid: string | null | undefined;
    sign: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = usePostCustom(
        "documents/sign?noTemplateVars=true"
    );

    useEffect(() => {
        if (sign && uid) {
            doFetch(true, { uid: uid });
        }
    }, [sign, uid]);

    return { data, hasLoaded, loading };
};
