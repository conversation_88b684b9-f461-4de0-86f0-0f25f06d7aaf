import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useAddUserEmail = ({
    insertItem,
    addEmail,
}: {
    insertItem: string;
    addEmail: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = usePostCustom(
        "emailsadderssbook/fromremotesave"
    );

    useEffect(() => {
        if (addEmail) {
            doFetch(true, { insertItem: insertItem });
        }
    }, [insertItem]);

    return { hasLoaded, loading };
};
