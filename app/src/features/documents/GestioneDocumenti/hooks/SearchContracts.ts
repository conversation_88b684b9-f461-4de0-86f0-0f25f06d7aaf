import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { SearchContractsProps } from "../../../../interfaces/documents.interface";

export const useSearchContracts = ({
    from,
    query,
}: SearchContractsProps): {
    data: any;
    error: any;
    hasLoaded: boolean;
    loading: boolean;
} => {
    const { data, doFetch, error, hasLoaded, loading } = useGetCustom(
        "default/contracts/search?noTemplateVars=true"
    );

    useEffect(() => {
        if (query !== "") {
            doFetch(true, { q: query, from: from });
        }
    }, [from, query]);

    return { data, error, hasLoaded, loading };
};
