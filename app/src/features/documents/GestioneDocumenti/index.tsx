import { Documents } from "./Documents";
import { DocumentsCreateUpdate } from "./DocumentsCreateUpdate";
import { OneDriveNewFile } from "./OneDriveNewFile";
import { OnlineUpdate } from "./OnlineUpdate";

export const documents = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documents/documents",
            element: <Documents />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documents/documents/:id?",
            element: <DocumentsCreateUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documents/new",
            element: <OneDriveNewFile />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documents/onlineupdate/:uniqueid?",
            element: <OnlineUpdate />,
        },
    },
];
