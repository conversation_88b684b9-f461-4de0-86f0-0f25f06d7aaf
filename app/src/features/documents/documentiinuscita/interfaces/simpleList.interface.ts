export interface IDefaultQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    searchProtocollo: string;
    date?: Date | string;
    date_without_format: string;
}

export interface IFilterProps {
    defaultQuery: IDefaultQuery;
    query: IDefaultQuery;
    setQuery: React.Dispatch<React.SetStateAction<IDefaultQuery>>;
    filterData: (query: IDefaultQuery) => void;
    mountedData: any;
}
