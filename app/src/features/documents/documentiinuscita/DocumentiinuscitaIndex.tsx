import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import useMountedData from "./hooks/useMountedData";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

export default function ClauseIndex() {
    const { t } = useTranslation();

    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter();

    const { mountedData } = useMountedData();

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };
    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="documentiInUscita"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
                onClickKey="id"
            />
        );
    };

    return (
        <>
            <VaporPage>
                <VaporHeaderBar title={t("Documenti in uscita")} />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
