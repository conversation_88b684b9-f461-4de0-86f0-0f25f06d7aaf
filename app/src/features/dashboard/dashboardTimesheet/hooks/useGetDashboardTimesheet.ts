import { useState, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useTranslation } from "@1f/react-sdk";
import { getDashboardTimesheetGrid } from "../../../../utilities/dashboardTimesheet/gridColumn";

export const backgroundColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];
export const borderColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];

export const useGetDashboardTimesheetCharts = (params: any, queryList: any) => {
    const [timesheet, setTimesheet] = useState<any>({
        data: [],
        timesheetUtentiColumns: [],
        timesheetUtentiRows: [],
        timesheetPraticheColumns: [],
        timesheetPraticheRows: [],
        totalRows: 0,
    });
    const [dataList, setDataList] = useState<any>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);

    const getDashboard = usePostCustom("dashboard/updatetimesheet?noTemplateVars=true");
    const timesheetPratiche = useGetCustom(
        "dashboard/getpratichetimesheetlist?noTemplateVars=true",
        { ...queryList }
    );

    const { t } = useTranslation();

    const fetchData = async () => {
        setLoading(true);
        try {
            const response: any = await getDashboard.doFetch(true, params);
            const timesheetUtentiColumns = [
                { label: "Risorsa", align: "left" },
                { label: "Ore", align: "left" },
                { label: "Numero", align: "left" },
                { label: "Pratiche", align: "left" },
                { label: "Costo", align: "right" },
            ];
            const timesheetUtentiRows = response?.data?.timesheet?.dettaglio_timesheet_utenti?.map((row: any) => ({
                "Risorsa": row.nome,
                "Ore": row.oredurata || "0",
                "Numero": row.eventi || "0",
                "Pratiche": row.pratiche,
                "Costo": `€ ${row.costo}`,
            })) || [];

            const finalColumns: any = await getDashboardTimesheetGrid(t);

            console.log("finalColumns", finalColumns);
            

            setTimesheet({
                data: response?.data?.timesheet || [],
                timesheetUtentiColumns: timesheetUtentiColumns,
                timesheetUtentiRows: timesheetUtentiRows,
                timesheetPraticheColumns: finalColumns,
                lineChartData: response?.data?.timesheet,
            });

            const responseTimesheet: any = await timesheetPratiche.doFetch(true);
            const { currentPage, totalRows } = responseTimesheet.data;
            
            const total = JSON.parse(totalRows)

            setDataList(currentPage);
            setTotalRows(total);

        } catch (error) {
            console.error("Error fetching dashboard data:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, [params, params?.startDateSearch, params?.endDateSearch]);

    return { timesheet, dataList, setDataList, totalRows, setTotalRows, loading, fetchData };
}

export const useGetDashboardTimeSheet = () => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const getDashboard = useGetCustom("dashboard/timesheet");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true);
        setData(response?.data);
        setLoading(false);
    };

    useEffect(() => {
        fetchData();
    }, []);

    return { data, loading, fetchData };
}
