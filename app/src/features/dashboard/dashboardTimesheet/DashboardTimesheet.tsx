import { useState, useEffect } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Grid,
    Card,
    CardContent,
    Box,
    Typography,
} from "@vapor/react-material";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import {
    useGetDashboardTimesheetCharts,
    useGetDashboardTimeSheet,
    borderColor,
    backgroundColor,
} from "./hooks/useGetDashboardTimesheet";
import InfoBox from "../components/InfoBox";
import Table from "../components/DashboardTable";
import Filter from "./Filters";
import LineChart from "../components/Line";

import ToastNotification from "../../../custom-components/ToastNotification";

const defaultParams = {
    customerSearch: "",
    counterpartSearch: "",
    tagSearch: "",
    sezSearch: -1,
    userSearch: -1,
    authoritySearch: -1,
    typeSearch: -1,
    statusSearch: -1,
    sectorSearch: -1,
    lawyerSearch: -1,
    objectSearch: -1,
    isArchivedSearch: "",
    startDateSearch: "",
    endDateSearch: "",
};

const listParams = {
    page: 0,
    pageSize: 10,
    sortColumn: "durata",
    sortOrder: "desc",
};

const DashboardTimesheet = () => {
    const [query, setQuery] = useState<any>(defaultParams);
    const [defaultListQuery, setDefaultListQuery] = useState<any>(listParams);
    const [selectDefaultLabel, setSelectDefaultLabel] = useState(true);
    const [currentStep, setCurrentStep] = useState(0);
    const [chart, setChart] = useState<any>(null);
    const [showNotification, setShowNotification] = useState(false);

    const { t } = useTranslation();
    const {
        dataList,
        setDataList,
        totalRows,
        setTotalRows,
        timesheet,
        fetchData,
    } = useGetDashboardTimesheetCharts(query, defaultListQuery);
    const { data } = useGetDashboardTimeSheet();

    const timesheetPratiche = useGetCustom(
        "dashboard/getpratichetimesheetlist?noTemplateVars=true",
        { ...defaultListQuery }
    );

    const resetQuery = (e: any) => {
        e.preventDefault();
        setQuery(defaultParams);
        setDefaultListQuery(listParams);
        setSelectDefaultLabel(true);
        setCurrentStep(0);
    };

    const onSubmit = async (e: any) => {
        e.preventDefault();
        await fetchData();
    };

    const startSearchList = async (): Promise<void> => {
        const response: any = await timesheetPratiche.doFetch(true);
        const { currentPage, totalRows } = response.data;

        const total = JSON.parse(totalRows);

        setDataList(currentPage);
        setTotalRows(total);
    };

    useEffect(() => {
        startSearchList();
    }, [defaultListQuery?.page, defaultListQuery?.pageSize, defaultListQuery]);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDefaultListQuery({
            ...defaultListQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const generateChartData = (
        blockIDs: any,
        blockChartType: any,
        blockChartDataType: any,
        blockData: any,
        blockDataset: any
    ) => {
        if (!blockIDs || !blockDataset) return [];

        return blockIDs.map((_block: any, indice: number) => {
            let datasets: any = [];
            let chartData: any = {};

            let bgColor =
                blockChartType[indice] === "line"
                    ? backgroundColor
                    : backgroundColor[1];
            let borColor =
                blockChartType[indice] === "line"
                    ? borderColor
                    : borderColor[1];

            if (blockChartDataType[indice] === "single") {
                datasets = [
                    {
                        label: "Tipo Pratiche",
                        data: blockData[indice] || [],
                        backgroundColor: bgColor,
                        borderColor: borColor,
                        borderWidth: 1,
                    },
                ];

                chartData = {
                    labels: blockDataset[indice]?.labels || [],
                    datasets: datasets,
                };
            } else {
                chartData = {
                    labels: blockDataset?.labels,
                    datasets: Object.keys(blockDataset?.utenti).map((key) => ({
                        label: blockDataset?.utenti[key],
                        data: Object.values(blockDataset?.datasets[key]),
                        backgroundColor: bgColor,
                        borderColor: borColor,
                    })),
                };
            }
            return chartData;
        });
    };

    useEffect(() => {
        if (data && timesheet) {
            const chartData = generateChartData(
                data?.blockIDs,
                data?.blockChartType,
                data?.blockChartDataType,
                data?.blockData,
                timesheet?.lineChartData
            );
            setChart(chartData);
        }
    }, [data, timesheet]);

    const handleRowClick = (row: any) => {
        if (row.accessibile == true) {
            if (row.uniqueid) {
                location.href = "/legacy/archive/summary?uid=" + row.uniqueid;
            }
        } else if (row.pratica != "") {
            setShowNotification(true);
        }
    };

    const customFormat = (amount = "0") => {
        const value = parseFloat(amount);
        const fixedAmount = value.toFixed(2);
        const [whole, fractional] = fixedAmount.split(".");
        const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

        return `${formattedWhole},${fractional}`;
    };

    function formatNumber(number: any) {
        const [whole, fraction] = number.toString().split(".");
        return `${whole}:${fraction}`;
    }

    return (
        <VaporPage>
            <PageTitle
                title={t("DASHBOARD TIMESHEET")}
                pathToPrevPage={"/dashboard"}
                actionButtons={[
                    {
                        label: t("Stampa"),
                        onclick: () => {
                            window.print();
                        },
                        variant: "contained",
                    },
                ]}
            />
            <VaporPage.Section>
                <Filter
                    query={query}
                    setQuery={setQuery}
                    reset={resetQuery}
                    onSubmit={onSubmit}
                    data={data}
                    currentStep={currentStep}
                    setCurrentStep={setCurrentStep}
                    selectDefaultLabel={selectDefaultLabel}
                    setSelectDefaultLabel={setSelectDefaultLabel}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container spacing={3}>
                    <Grid item md={9} xl={9}>
                        <Grid container spacing={3}>
                            <Grid item md={4} xl={4}>
                                <InfoBox
                                    value={
                                        timesheet?.data?.ore_totali
                                            ? formatNumber(
                                                  timesheet?.data?.ore_totali
                                              )
                                            : "0"
                                    }
                                    text={t("Ore Totali")}
                                    icon=""
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <InfoBox
                                    value={
                                        timesheet?.data?.media
                                            ? formatNumber(
                                                  timesheet?.data?.media
                                              )
                                            : "0"
                                    }
                                    text={t("Media sul periodo")}
                                    icon=""
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <InfoBox
                                    value={timesheet?.data?.pratiche || "0"}
                                    text={t("In pratiche")}
                                    icon=""
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <LineChart
                                    chartData={chart && chart[0]}
                                    t={t}
                                    title={"Ultimi 12 mesi"}
                                    displayLegends={true}
                                    path=""
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={3} xl={3}>
                        <Grid container spacing={3}>
                            <Grid item md={12} xl={12}>
                                <InfoBox
                                    value={
                                        timesheet?.data?.costo_totale
                                            ? customFormat(
                                                  timesheet?.data?.costo_totale
                                              )
                                            : "0,00"
                                    }
                                    text={t("Costo Risorse")}
                                    icon="€"
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox
                                    value={timesheet?.data?.efficienza || "0"}
                                    text={t("Efficienza")}
                                    icon="%"
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox
                                    value={
                                        timesheet?.data?.fatturabili
                                            ? customFormat(
                                                  timesheet?.data?.fatturabili
                                              )
                                            : "0"
                                    }
                                    text="Fatturabili"
                                    icon=""
                                    color="#71B7B7"
                                />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox
                                    value={
                                        timesheet?.data?.processate
                                            ? customFormat(
                                                  timesheet?.data?.processate
                                              )
                                            : "0,00"
                                    }
                                    text={t("Processate")}
                                    icon="€"
                                    color="#71B7B7"
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table
                            columns={timesheet?.timesheetUtentiColumns}
                            rows={timesheet?.timesheetUtentiRows}
                            title={"Timesheet Utenti"}
                            t={t}
                            navigation={false}
                        />
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Card sx={{ p: 2 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                }}
                            >
                                <Typography
                                    gutterBottom
                                    variant="h5"
                                    component="div"
                                    sx={{ pl: 2 }}
                                >
                                    {t("Timesheet per pratiche")}
                                </Typography>
                            </Box>
                            <CardContent>
                                {dataList && (
                                    <CustomDataGrid
                                        name="timesheetPratiche"
                                        columns={
                                            timesheet?.timesheetPraticheColumns
                                        }
                                        data={dataList}
                                        totalRows={totalRows}
                                        pageSize={defaultListQuery.pageSize}
                                        query={defaultListQuery}
                                        setQuery={setDefaultListQuery}
                                        selectableRows={false}
                                        page={defaultListQuery.page}
                                        paginationMode="server"
                                        onPageChangeCallback={
                                            onPageChangeCallback
                                        }
                                        onClickCallback={handleRowClick}
                                        onClickKey="row"
                                    />
                                )}
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
                <ToastNotification
                    showNotification={showNotification}
                    setShowNotification={setShowNotification}
                    text="La pratica è riservata"
                    severity="warning"
                />
            </VaporPage.Section>
        </VaporPage>
    );
};

export default DashboardTimesheet;
