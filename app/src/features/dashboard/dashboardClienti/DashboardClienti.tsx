import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Grid } from "@vapor/react-material";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate } from "react-router-dom";
import {
    useGetDashboardCharts,
    useGetDashboardClienti
} from "./hooks/useGetDashboardClienti";
import InfoBox from "../components/InfoBox";
import Table from "../components/DashboardTable";
import Filter from "./Filters";
import DoughnutChart from "../components/Doughnut";

const defaultParams = {
    customerSearch: "",
    counterpartSearch: "",
    tagSearch: "",
    sezSearch: -1,
    authoritySearch: -1,
    typeSearch: -1,
    statusSearch: -1,
    sectorSearch: -1,
    lawyerSearch: -1,
    objectSearch: -1,
    startDateSearch: "",
    endDateSearch: "",
};

const Dashboard = () => {
    const [query, setQuery] = useState<any>(defaultParams);
    const [currentStep, setCurrentStep] = useState(0);

    const { t } = useTranslation();
    const navigate = useNavigate();
    const { clienti, fetchData } = useGetDashboardCharts(query);
    const { data } = useGetDashboardClienti();

    const resetQuery = (e: any) => {
        e.preventDefault()
        setQuery(defaultParams)
        setCurrentStep(0)
    }

    const onSubmit = async (e: any) => {
        e.preventDefault()
        await fetchData()
    }

    const customFormat = (amount = "0") => {
        const value = parseFloat(amount)
        const fixedAmount = (value).toFixed(2);
        const [whole, fractional] = fixedAmount.split(".");
        const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

        return `${formattedWhole},${fractional}`;
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("DASHBOARD CLIENTI")}
                pathToPrevPage={"/dashboard"}
                actionButtons={[
                    {
                        label: t("Stampa"),
                        onclick: () => { window.print() },
                        variant: "contained"
                    },
                ]}
            />
            <VaporPage.Section>
                <Filter
                    query={query}
                    setQuery={setQuery}
                    reset={resetQuery}
                    onSubmit={onSubmit}
                    data={data}
                    currentStep={currentStep}
                    setCurrentStep={setCurrentStep}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container spacing={3}>
                    <Grid item md={12} xl={12}>
                        <Grid container spacing={3}>
                            <Grid item md={3} xl={3}>
                                <InfoBox value={clienti.data?.clienti?.numero_clienti_totali || "0"} text={t("Clienti Totali")} icon="" color="#d9b153" />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox value={clienti.data?.clienti?.numero_clienti_con_scoperto || "0"} text={t("Clienti con insoluti")} icon="" color="#d9b153" />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox value={clienti.data?.clienti?.media_pratiche_cliente ? customFormat(clienti.data?.clienti?.media_pratiche_cliente) : "0,00"} text={t("Media Pratiche/Cliente")} icon="" color="#d9b153" />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox value={clienti.data?.clienti?.numero_clienti_con_pratiche || "0"} text={t("Clienti con pratiche")} icon="" color="#d9b153" />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={6} xl={6}>
                        <DoughnutChart chartData={clienti?.data?.clienti?.clienti_per_tipologia} navigate={navigate} t={t} path="" title={t("Clienti per Tipologia")} legenda={{ position: "right", display: "right" }} />
                    </Grid>
                    <Grid item md={6} xl={6}>
                        <DoughnutChart chartData={clienti?.data?.clienti?.pratiche_per_tipologia_clienti} navigate={navigate} t={t} path="" title={t("Pratiche per Tipologia Cliente")} legenda={{ position: "right", display: "right" }} />
                    </Grid>
                    <Grid item md={6} xl={6}>
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table columns={clienti?.topClientiFattColumns} rows={clienti?.topClientiPerFattRows} title={"Top clienti per fatturato"} t={t} navigation={true} icon="" path="/anagrafiche/view/?id=" />
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table columns={clienti?.topClientiScopertoColumns} rows={clienti?.topClientiPerScopertoRows} title={"Top Clienti con scoperto"} t={t} navigation={true} icon="" path="/anagrafiche/view/?id=" />
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table columns={clienti?.topClientiPraticheColumns} rows={clienti?.topClientiPraticheRows} title={"Top Clienti per numero di pratiche"} t={t} navigation={true} path="/anagrafiche/view/?id=" />
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default Dashboard;
