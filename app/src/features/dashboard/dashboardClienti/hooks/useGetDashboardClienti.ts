import { useState, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useGetDashboardCharts = (params: any) => {
    const [clienti, setClienti] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const { t } = useTranslation();

    const topClientiFattColumns = [
        { label: t("Cliente"), align: "left" },
        { label: t("N°Fatture"), align: "right" },
        { label: t("Totale"), align: "right" },
    ];

    const topClientiScopertoColumns = [
        { label: t("Cliente"), align: "left" },
        { label: t("N°Fatture"), align: "right" },
        { label: t("Totale Scoperto"), align: "right" },
    ];

    const topClientiPraticheColumns = [
        { label: t("Cliente"), align: "left" },
        { label: t("N°Pratiche"), align: "right" },
    ];

    const getDashboard = usePostCustom("dashboard/updateclienti?noTemplateVars=true");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true, params);
        const topClientiPerFattRows = response?.data?.clienti.top_clienti_per_fatturato.map((row: any) => ({
            "Cliente": row.nome,
            "N°Fatture": row.n_Fatture || "0",
            "Totale": "€" + row.totale,
            "uniqueid": row.id,
        }));

        const topClientiPerScopertoRows = response?.data?.clienti.top_clienti_per_scoperto.map((row: any) => ({
            "Cliente": row.nome,
            "N°Fatture": row.n_Fatture || "0",
            "Totale Scoperto": "€" + row.totale || "0",
            "uniqueid": row.id,
        }));

        const topClientiPraticheRows = response?.data?.clienti.top_clienti_per_numero_pratiche.map((row: any) => ({
            "Cliente": row.nome,
            "N°Pratiche": row.quantita_pratiche,
            "uniqueid": row.id,
        }));

        setClienti({ topClientiScopertoColumns: topClientiScopertoColumns, topClientiFattColumns: topClientiFattColumns, topClientiPraticheColumns: topClientiPraticheColumns, topClientiPerFattRows: topClientiPerFattRows, topClientiPerScopertoRows: topClientiPerScopertoRows, topClientiPraticheRows: topClientiPraticheRows, data: response?.data });
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, [params, params?.startDateSearch, params?.endDateSearch]);

    return { clienti, loading, fetchData };
}

export const useGetDashboardClienti = () => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const getDashboard = useGetCustom("dashboard/clienti");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true);
        setData(response?.data);
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { data, loading, fetchData };
}
