import { useState, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useGetDashboardCharts = (params: any) => {
    const [fatturazione, setFatturazione] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const fattureColumns = [
        { label: "N°", align: "left" },
        { label: "Pratica", align: "left" },
        { label: "Emittente", align: "left" },
        { label: "Intestatario", align: "left" },
        { label: "Totale", align: "right" },
        { label: "Da incassare", align: "right" },
        { label: "uniqueid", align: "right" },
    ];

    const getDashboard = usePostCustom("dashboard/updatefatturazione?noTemplateVars=true");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true, params);
        const fattureRows = response?.data?.fatturazione.elenco_fatture_da_incassare.map((row: any) => ({
            "N°": row.progressivo,
            "Pratica": row.codicepratica || "0",
            "Emittente": row.avvocato,
            "Intestatario": row.nome_intestatario,
            "Totale": row.totale,
            "Da incassare": row.da_incassare,
            "uniqueid": row.uniqueid
        }));

        const fattureRowsTop10 = response?.data?.fatturazione.top_10_fatture_per_totale.map((row: any) => ({
            "N°": row.progressivo,
            "Pratica": row.codicepratica || "0",
            "Emittente": row.avvocato,
            "Intestatario": row.nome_intestatario,
            "Totale": row.totale,
            "Da incassare": row.da_incassare,
            "uniqueid": row.uniqueid
        }));

        setFatturazione({ columns: fattureColumns, rowsFattureDaIncassare: fattureRows, rowsTop10Fatture: fattureRowsTop10, data: response?.data?.fatturazione });
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, [params, params?.startDateSearch, params?.endDateSearch]);

    return { fatturazione, loading, fetchData };
}

export const useGetDashboardIndex = () => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const getDashboard = useGetCustom("dashboard/fatturazione");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true);
        setData(response?.data);
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { data, loading, fetchData };
}
