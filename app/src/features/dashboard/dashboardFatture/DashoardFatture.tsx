import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Grid } from "@vapor/react-material";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import {
    useGetDashboardCharts,
    useGetDashboardIndex
} from "./hooks/useGetDashboardFatture";
import InfoBox from "../components/InfoBox";
import Table from "../components/DashboardTable";
import Filter from "./Filters";
import Last12Months from "../components/Last12Months";

const defaultParams = {
    customerSearch: "",
    counterpartSearch: "",
    tagSearch: "",
    sezSearch: -1,
    authoritySearch: -1,
    typeSearch: -1,
    statusSearch: -1,
    sectorSearch: -1,
    lawyerSearch: -1,
    objectSearch: -1,
    startDateSearch: "",
    endDateSearch: "",
};

const Dashboard = () => {
    const [query, setQuery] = useState<any>(defaultParams);
    const [currentStep, setCurrentStep] = useState(0);

    const { t } = useTranslation();
    const { fatturazione, fetchData } = useGetDashboardCharts(query);
    const { data } = useGetDashboardIndex();

    const resetQuery = (e: any) => {
        e.preventDefault()
        setQuery(defaultParams)
        setCurrentStep(0)
    }

    const onSubmit = async (e: any) => {
        e.preventDefault()
        await fetchData()
    }

    const customFormat = (amount = "0") => {
        const value = parseFloat(amount)
        const fixedAmount = (value).toFixed(2);
        const [whole, fractional] = fixedAmount.split(".");
        const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

        return `${formattedWhole},${fractional}`;
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("DASHBOARD FATTURAZIONE")}
                pathToPrevPage={"/dashboard"}
                actionButtons={[
                    {
                        label: t("Stampa"),
                        onclick: () => { window.print() },
                        variant: "contained"
                    },
                ]}
            />
            <VaporPage.Section>
                <Filter
                    query={query}
                    setQuery={setQuery}
                    reset={resetQuery}
                    onSubmit={onSubmit}
                    data={data}
                    currentStep={currentStep}
                    setCurrentStep={setCurrentStep}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container spacing={3}>
                    <Grid item md={9} xl={9}>
                        <Grid container spacing={3}>
                            <Grid item md={4} xl={4}>
                                <InfoBox value={fatturazione?.data?.fatture_emesse || "0"} text={t("Fatture Emesse")} icon="" color="#ff7d99" />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <InfoBox value={fatturazione?.data?.fatture_da_incassare || "0"} text={t("Da incassare")} icon="" color="#ff7d99" />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <InfoBox value={fatturazione?.data?.totale_fatturato ? customFormat(fatturazione?.data?.totale_fatturato) : "0,00"} text={t("Totale Fatturato")} icon="€" color="#ff7d99" />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <Last12Months last12MonthsData={fatturazione?.data} t={t} chartTypes={data?.blockChartType || ["bar"]} displayLegends={true} />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={3} xl={3}>
                        <Grid container spacing={3}>
                            <Grid item md={12} xl={12}>
                                <InfoBox value={fatturazione?.data?.totale_da_incassare ? customFormat(fatturazione?.data?.totale_da_incassare) : "0,00"} text={t("Totale da incassare")} icon="€" color="#ff7d99" />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox value={fatturazione?.data?.media_tempi_incasso ? customFormat(fatturazione?.data?.media_tempi_incasso) : "0,00"} text={t("Tempo medio di incasso")} icon="gg" color="#ff7d99" />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox value={fatturazione?.data?.preavvisi_non_trasformati} text="Preavvisi non trasformati" icon="" color="#ff7d99" />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox value={fatturazione?.data?.media_valore_fatture ? customFormat(fatturazione?.data?.media_valore_fatture) : "0,00"} text={t("Media valore fattura")} icon="€" color="#ff7d99" />
                            </Grid>
                            <Grid item md={12} xl={12}>
                                <InfoBox value={fatturazione?.data?.totale_note_credito ? customFormat(fatturazione?.data?.totale_note_credito) : "0,00"} text={t("Totale Note di Credito")} icon="€" color="#ff7d99" />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table columns={fatturazione?.columns} rows={fatturazione?.rowsFattureDaIncassare} title={"Fatture da incassare"} t={t} navigation={true} path={"/legacy/fatturecomplex/viewfe?uniqueid="} />
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Table columns={fatturazione?.columns} rows={fatturazione?.rowsTop10Fatture} title={"Top 10 fatture per totale"} t={t} navigation={true} path={"/legacy/fatturecomplex/viewfe?uniqueid="}/>
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default Dashboard;
