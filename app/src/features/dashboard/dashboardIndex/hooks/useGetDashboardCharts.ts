import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

var backgroundColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];
var borderColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];

export const useGetDashboard = () => {
    const [lawyers, setLawyers] = useState<any>([]);
    const [data, setData] = useState<any>([]);
    const [loadingDashboard, setLoadingDashboard] = useState<boolean>(false);

    const getDashboard = useGetCustom("dashboard/index");

    const fetchData = async () => {
        setLoadingDashboard(true);
        try {
            const response: any = await getDashboard.doFetch(true);
            console.log("response-----------", response);
            setData(response?.data)
            setLawyers(response?.data?.lawyers);
        } catch (error) {
            console.error("Error fetching dashboard data:", error);
        } finally {
            setLoadingDashboard(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { lawyers, data, loadingDashboard, fetchData };
}

export const useGetPratiche = (params: any) => {
    const [pratiche, setPratiche] = useState<any>([]);
    const [loadingPratiche, setLoadingPratiche] = useState<boolean>(false);

    const getDashboardChartPratiche = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingPratiche(true);
        try {
            const response: any = await getDashboardChartPratiche.doFetch(true, params);
            setPratiche(response?.data);
        } catch (error) {
            console.error("Error fetching pratiche data:", error);
        } finally {
            setLoadingPratiche(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { pratiche, loadingPratiche, fetchData };
}

export const useGetFattureEmesse = (params: any) => {
    const [fattureEmesse, setFattureEmesse] = useState<any>([]);
    const [loadingFattureEmesse, setLoadingFattureEmesse] = useState<boolean>(false);

    const getDashboardChartFattureEmesse = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingFattureEmesse(true);
        try {
            const response: any = await getDashboardChartFattureEmesse.doFetch(true, params);
            setFattureEmesse(response?.data);
        } catch (error) {
            console.error("Error fetching fatture emesse data:", error);
        } finally {
            setLoadingFattureEmesse(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { fattureEmesse, loadingFattureEmesse, fetchData };
}

export const useGetOreTimesheet = (params: any) => {
    const [oreTimesheet, setOreTimesheet] = useState<any>([]);
    const [loadingOreTimesheet, setLoadingOreTimesheet] = useState<boolean>(false);

    const getDashboardChartOreTimesheet = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingOreTimesheet(true);
        try {
            const response: any = await getDashboardChartOreTimesheet.doFetch(true, params);
            setOreTimesheet(response?.data);
        } catch (error) {
            console.error("Error fetching ore timesheet data:", error);
        } finally {
            setLoadingOreTimesheet(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { oreTimesheet, loadingOreTimesheet, fetchData };
}

export const useGetClienti = (params: any) => {
    const [clienti, setClienti] = useState<any>([]);
    const [loadingClienti, setLoadingClienti] = useState<boolean>(false);

    const getDashboardChartClienti = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingClienti(true);
        try {
            const response: any = await getDashboardChartClienti.doFetch(true, params);
            setClienti(response?.data);
        } catch (error) {
            console.error("Error fetching clienti data:", error);
        } finally {
            setLoadingClienti(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { clienti, loadingClienti, fetchData };
}

export const useGetTipologiePratiche = (params: any) => {
    const [tipologiePratiche, setTipologiePratiche] = useState<any>({});
    const [loadingTipologiePratiche, setLoadingTipologiePratiche] = useState<boolean>(false);

    const getDashboardChartTipologiePratiche = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingTipologiePratiche(true);
        try {
            const response: any = await getDashboardChartTipologiePratiche.doFetch(true, params);
            setTipologiePratiche(response?.data);
        } catch (error) {
            console.error("Error fetching tipologie pratiche data:", error);
        } finally {
            setLoadingTipologiePratiche(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { tipologiePratiche, loadingTipologiePratiche, fetchData };
}

export const useGetStatoPratiche = (params: any) => {
    const [statoPratiche, setStatoPratiche] = useState<any>([]);
    const [loadingStatoPratiche, setLoadingStatoPratiche] = useState<boolean>(false);

    const getDashboardChartStatoPratiche = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingStatoPratiche(true);
        try {
            const response: any = await getDashboardChartStatoPratiche.doFetch(true, params);
            setStatoPratiche(response?.data);
        } catch (error) {
            console.error("Error fetching stato pratiche data:", error);
        } finally {
            setLoadingStatoPratiche(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { statoPratiche, loadingStatoPratiche, fetchData };
}

export const useGetPratichePerAvvocato = (params: any) => {
    const [pratichePerAvvocato, setPratichePerAvvocato] = useState<any>([]);
    const [loadingPratichePerAvvocato, setLoadingPratichePerAvvocato] = useState<boolean>(false);

    const getDashboardChartPratichePerAvvocato = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingPratichePerAvvocato(true);
        try {
            const response: any = await getDashboardChartPratichePerAvvocato.doFetch(true, params);
            setPratichePerAvvocato(response?.data);
        } catch (error) {
            console.error("Error fetching pratiche per avvocato data:", error);
        } finally {
            setLoadingPratichePerAvvocato(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.isArchivedSearch, params?.lawyerSearch]);

    return { pratichePerAvvocato, loadingPratichePerAvvocato, fetchData };
}

export const useGetPraticheMensili = (params: any) => {
    const [praticheMensili, setPraticheMensili] = useState<any>([]);
    const [loadingPraticheMensili, setLoadingPraticheMensili] = useState<boolean>(false);

    const getDashboardChartPraticheMensili = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingPraticheMensili(true);
        try {
            const response: any = await getDashboardChartPraticheMensili.doFetch(true, params);
            setPraticheMensili(response?.data);
        } catch (error) {
            console.error("Error fetching pratiche mensili data:", error);
        } finally {
            setLoadingPraticheMensili(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.isArchivedSearch, params?.lawyerSearch]);

    return { praticheMensili, loadingPraticheMensili, fetchData };
}

export const useGetImpegni = (params: any) => {
    const [impegni, setImpegni] = useState<any>([]);
    const [loadingImpegni, setLoadingImpegni] = useState<boolean>(false);

    const getDashboardChartImpegni = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingImpegni(true);
        try {
            const response: any = await getDashboardChartImpegni.doFetch(true, params);
            setImpegni(response?.data);
        } catch (error) {
            console.error("Error fetching impegni data:", error);
        } finally {
            setLoadingImpegni(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { impegni, loadingImpegni, fetchData };
}

export const useGetMigloriClienti = (params: any) => {
    const [migloriClienti, setMigloriClienti] = useState<any>();
    const [loadingMigloriClienti, setLoadingMigloriClienti] = useState<boolean>(false);

    const miglioriClientiColumns = [
        { label: "Nome", align: "left" },
        { label: "Numero Fatture", align: "left" },
        { label: "Totale", align: "left" },
    ];

    const getDashboardChartMigloriClienti = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingMigloriClienti(true);
        try {
            const response: any = await getDashboardChartMigloriClienti.doFetch(true, params);
            const miglioriClientiRows = response?.data.map((row: any) => ({
                "Nome": row.nome,
                "Numero Fatture": row.n_Fatture,
                "Totale": row.totale,
            }));

            setMigloriClienti({ columns: miglioriClientiColumns, rows: miglioriClientiRows });
        } catch (error) {
            console.error("Error fetching migliori clienti data:", error);
        } finally {
            setLoadingMigloriClienti(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.lawyerSearch]);

    return { migloriClienti, loadingMigloriClienti, fetchData };
}

export const useGetTipologieDocumenti = (params: any) => {
    const [tipologieDocumenti, setTipologieDocumenti] = useState<any>([]);
    const [loadingTipologieDocumenti, setLoadingTipologieDocumenti] = useState<boolean>(false);

    const getDashboardChartTipologieDocumenti = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingTipologieDocumenti(true);
        try {
            const response: any = await getDashboardChartTipologieDocumenti.doFetch(true, params);
            setTipologieDocumenti(response?.data);
        } catch (error) {
            console.error("Error fetching tipologie documenti data:", error);
        } finally {
            setLoadingTipologieDocumenti(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.lawyerSearch]);

    return { tipologieDocumenti, loadingTipologieDocumenti, fetchData };
}

export const useGetTimeSheet = (params: any) => {
    const [timesheet, setTimesheet] = useState<any>([]);
    const [loadingTimesheet, setLoadingTimesheet] = useState<boolean>(false);

    const getDashboardChartTimesheet = usePostCustom("dashboard/getchart?noTemplateVars=true");

    const fetchData = async () => {
        setLoadingTimesheet(true);
        try {
            const response: any = await getDashboardChartTimesheet.doFetch(true, params);
            let labels = response?.data?.labels;
            let data = response?.data?.datasets || [];
            let utenti = response?.data?.utenti;
            let dataset = [];
            let n = 0;
            for (let i in data) {
                n++;
                let obj = {
                    label: utenti[i],
                    data: Object.values(data[i]),
                    backgroundColor: backgroundColor[n],
                    borderColor: borderColor[n],
                    borderWidth: 1
                };
                dataset.push(obj);
            }
            setTimesheet([{
                labels: labels,
                datasets: dataset,
            }]);
        } catch (error) {
            console.error("Error fetching timesheet data:", error);
        } finally {
            setLoadingTimesheet(false);
        }
    }
    useEffect(() => {
        fetchData();
    }, [params?.startDashboardSearch, params?.endDashboardSearch, params?.lawyerSearch]);

    return { timesheet, loadingTimesheet, fetchData };
}
