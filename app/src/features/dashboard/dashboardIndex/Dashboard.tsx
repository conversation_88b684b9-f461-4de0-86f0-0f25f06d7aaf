import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button, Grid, CircularProgress } from "@vapor/react-material";
import moment from "moment";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import { useConfigs } from "../../../store/ConfigStore";

import {
    useGetDashboard,
    useGetPratiche,
    useGetFattureEmesse,
    useGetOreTimesheet,
    useGetClienti,
    useGetTipologiePratiche,
    useGetStatoPratiche,
    useGetPratichePerAvvocato,
    useGetPraticheMensili,
    useGetImpegni,
    useGetMigloriClienti,
    useGetTipologieDocumenti,
    useGetTimeSheet,
} from "./hooks/useGetDashboardCharts";

import Filter from "./Filter";
import InfoBox from "../components/InfoBox";
import Doughnut from "../components/Doughnut";
import Bar from "../components/Bar";
import Table from "../components/DashboardTable";
import Line from "../components/Line";

const defaultParams = {
    startDashboardSearch: "01/01/2020",
    endDashboardSearch: "31/12/2024",
    isArchivedSearch: "-1",
    lawyerSearch: "-1",
};

const Dashboard = () => {
    const [query, setQuery] = useState<any>(defaultParams);

    const navigate = useNavigate();
    const { t } = useTranslation();
    const { configs }: any = useConfigs();

    const { lawyers, data, loadingDashboard } = useGetDashboard();
    const { pratiche, loadingPratiche } = useGetPratiche({ chart: "npratiche", ...query });
    const { fattureEmesse, loadingFattureEmesse } = useGetFattureEmesse({ chart: "nfattureemesse", ...query });
    const { oreTimesheet, loadingOreTimesheet } = useGetOreTimesheet({ chart: "oretimesheet", ...query });
    const { clienti, loadingClienti } = useGetClienti({ chart: "numeroclienti" });
    const { tipologiePratiche, loadingTipologiePratiche } = useGetTipologiePratiche({ chart: "tipopratiche", ...query });
    const { statoPratiche, loadingStatoPratiche } = useGetStatoPratiche({ chart: "statipratiche", ...query });
    const { pratichePerAvvocato, loadingPratichePerAvvocato } = useGetPratichePerAvvocato({ chart: "pratichexavvocato", ...query });
    const { praticheMensili, loadingPraticheMensili } = useGetPraticheMensili({ chart: "pratichemonthgroup", isArchivedSearch: query?.isArchivedSearch, lawyerSearch: query?.lawyerSearch });
    const { impegni, loadingImpegni } = useGetImpegni({ chart: "impegniweekgroup" });
    const { migloriClienti, loadingMigloriClienti } = useGetMigloriClienti({ chart: "miglioriclienti", startDashboardSearch: query?.startDashboardSearch, endDashboardSearch: query?.endDashboardSearch, lawyerSearch: query?.lawyerSearch });
    const { tipologieDocumenti, loadingTipologieDocumenti } = useGetTipologieDocumenti({ chart: "tipoparcelle", startDashboardSearch: query?.startDashboardSearch, endDashboardSearch: query?.endDashboardSearch, lawyerSearch: query?.lawyerSearch });
    const { timesheet, loadingTimesheet } = useGetTimeSheet({ chart: "timesheet", startDashboardSearch: query?.startDashboardSearch, endDashboardSearch: query?.endDashboardSearch, lawyerSearch: query?.lawyerSearch });

    const isLoading = loadingDashboard && loadingPratiche && loadingFattureEmesse && loadingOreTimesheet && loadingClienti && loadingTipologiePratiche && loadingStatoPratiche && loadingPratichePerAvvocato && loadingPraticheMensili && loadingImpegni && loadingMigloriClienti && loadingTipologieDocumenti && loadingTimesheet;

    const handleStartDateChange = (date: any) => {
        setQuery({ ...query, startDashboardSearch: moment(date).format("DD/MM/YYYY") });
    };

    const handleEndDateChange = (date: any) => {
        setQuery({ ...query, endDashboardSearch: moment(date).format("DD/MM/YYYY") });
    };

    const handleInputChange = (e: any) => {
        const { name, value } = e.target;
        setQuery({ ...query, [name]: value });
    };

    function formatNumber(number: any) {
        const [whole, fraction] = number.toString().split('.');
        return `${whole}:${fraction}`;
    }

    if (isLoading) {
        return (
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button variant="contained" sx={{ mr: 3 }}>
                            {t("Stampa")}
                        </Button>,
                    ]}
                    title={t("DASHBOARD")}
                />
                <VaporPage.Section >
                    <Grid container spacing={1}>
                        <Grid item md={12} xl={12} sx={{ display: "flex", justifyContent: "center", height: "80vh", alignItems: "center" }}>
                            <CircularProgress />
                        </Grid>
                    </Grid>
                </VaporPage.Section>
            </VaporPage>
        );
    }

    const isSectionVisible = (section: any) => {
        return !data?.netlexSettings?.hide || !(Array.isArray(data?.netlexSettings?.hide) && data?.netlexSettings?.hide.includes(section));
    };

    return (
        <VaporPage>
            <VaporHeaderBar
                rightItems={[
                    <Button variant="contained" sx={{ mr: 3 }} onClick={() => {
                        window.print();
                    }}>
                        {t("Stampa")}
                    </Button>,
                ]}
                title={t("DASHBOARD")}
            />
            <VaporPage.Section>
                <Filter
                    query={query}
                    setQuery={setQuery}
                    handleInputChange={handleInputChange}
                    handleStartDateChange={handleStartDateChange}
                    handleEndDateChange={handleEndDateChange}
                    lawyers={lawyers}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container spacing={3}>
                    {configs?.data?.app?.practices_bool && isSectionVisible("sectionPratiche") && (
                        <Grid item md={3} xl={3}>
                            <InfoBox value={pratiche} text={t("Pratiche")} color={"#5676D4"} navigate={navigate} link="/dashboard/pratiche?tipo=pergroup" />
                        </Grid>
                    )}
                    {configs?.data?.app?.invoice_items_bool && isSectionVisible("sectionFatturaziones") && (
                        <Grid item md={3} xl={3}>
                            <InfoBox value={fattureEmesse} text={t("Fatture Emesse")} color={"#5676D4"} navigate={navigate} link="/dashboard/fatturazione" />
                        </Grid>
                    )}
                    {isSectionVisible("sectionAnagrafiche") && (
                        <Grid item md={3} xl={3}>
                            <InfoBox value={clienti} text={t("Clienti")} color={"#5676D4"} navigate={navigate} link="/dashboard/clienti" />
                        </Grid>
                    )}

                    {configs?.data?.app?.timesheet_bool && isSectionVisible("sectionAgenda") && (
                        <Grid item md={3} xl={3}>
                            <InfoBox value={formatNumber(oreTimesheet)} text={t("Ore Timesheet")} color={"#5676D4"} navigate={navigate} link="/dashboard/timesheet" />
                        </Grid>
                    )}
                    <Grid item md={8} xl={8}>
                        <Grid container spacing={3}>
                            {configs?.data?.app?.practices_bool && isSectionVisible("sectionPratiche") && (
                                <>
                                    <Grid item md={6} xl={6}>
                                        {tipologiePratiche ? <Doughnut chartData={tipologiePratiche} navigate={navigate} t={t} title="Tipologie Pratiche" path="/dashboard/pratiche?tipo=tipo" legenda={{ position: "right", display: true }} /> : ""}
                                    </Grid>
                                    <Grid item md={6} xl={6}>
                                        {statoPratiche ? <Doughnut chartData={statoPratiche} navigate={navigate} t={t} title="Stato Pratiche" path="/dashboard/pratiche?tipo=stati" legenda={{ position: "right", display: true }} /> : ""}
                                    </Grid>
                                </>
                            )}
                            {configs?.data?.app?.practices_bool && isSectionVisible("sectionPratiche") && (
                                <Grid item md={12} xl={12}>
                                    {praticheMensili ? <Bar chartData={praticheMensili} navigate={navigate} t={t} title="Pratiche Mensili ultimi 12 mesi" path="pratiche?tipo=pergroup" legenda={{ display: true, text: ["Mesi", "N* pratiche"] }} /> : ""}
                                </Grid>
                            )}
                            {isSectionVisible("sectionAgenda") && (
                                <Grid item md={12} xl={12}>
                                    {impegni ? <Bar chartData={impegni} t={t} title="Impegni settimanali ultimi 12 mesi" path="" legenda={{ display: true, text: ["Settimane", "N* Impegni"] }} /> : ""}
                                </Grid>
                            )}
                        </Grid>
                    </Grid>
                    <Grid item md={4} xl={4}>
                        <Grid container spacing={3}>
                            {configs?.data?.app?.practices_bool && isSectionVisible("sectionPratiche") && (
                                <Grid item md={12} xl={12}>
                                    {pratichePerAvvocato ? <Doughnut chartData={pratichePerAvvocato} navigate={navigate} t={t} title="Pratiche per avvocato" path="/dashboard/pratiche?tipo=peravvocato" legenda={{ position: "bottom", display: true }} /> : ""}
                                </Grid>
                            )}
                            {isSectionVisible("sectionAnagrafiche") && configs?.data?.app?.invoice_items_bool && isSectionVisible("sectionFatturaziones") && (
                                <Grid item md={12} xl={12}>
                                    {migloriClienti ? <Table columns={migloriClienti?.columns} rows={migloriClienti?.rows} title="Migliori Clienti" t={t} maxHeight={800} /> : ""}
                                </Grid>
                            )}
                        </Grid>
                    </Grid>
                    <Grid item md={6} xl={6}>
                        {tipologieDocumenti ? <Doughnut chartData={tipologieDocumenti} navigate={navigate} t={t} title="Tipologie Documenti emessi" path="/dashboard/fatturazione" legenda={{ position: "right", display: true }} /> : ""}
                    </Grid>
                    {configs?.data?.app?.timesheet_bool && isSectionVisible("sectionAgenda") && (
                        <Grid item md={6} xl={6}>
                            {timesheet?.length ? <Line chartData={timesheet[0]} title={t("TimeSheet Ultimi 12 mesi")} navigate={navigate} t={t} /> : ""}
                        </Grid>
                    )}
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default Dashboard;
