import { useState, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useGetPraticheCharts = (params: any) => {
    const [pratiche, setPratiche] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const getDashboard = usePostCustom("dashboard/updatepratiche?noTemplateVars=true");
    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true, params);
        setPratiche(response?.data);
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, [params, params?.startDateSearch, params?.endDateSearch]);

    return { pratiche, loading, fetchData };
}

export const useGetDashboardIndex = () => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const getDashboard = useGetCustom("dashboard/pratiche");

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getDashboard.doFetch(true);
        setData(response?.data);
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { data, loading, fetchData };
}