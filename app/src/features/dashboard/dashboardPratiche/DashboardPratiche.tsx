import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Grid } from "@vapor/react-material";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate, useLocation } from "react-router-dom";

import {
    useGetPraticheCharts,
    useGetDashboardIndex,
} from "./hooks/useGetDashboardPratiche";
import { getChartData, getChartTitle, getChartPath } from "./helpers";

import Filter from "./Filter";
import InfoBox from "../components/InfoBox";
import Bar from "../components/Bar";
import Table from "../components/DashboardTable";
import Doughnut from "../components/Doughnut";

const defaultParams = {
    customerSearch: "",
    isArchivedSearch: 0,
    counterpartSearch: "",
    tagSearch: "",
    sezSearch: -1,
    authoritySearch: -1,
    typeSearch: -1,
    statusSearch: -1,
    sectorSearch: -1,
    lawyerSearch: -1,
    objectSearch: -1,
    startDateSearch: "",
    endDateSearch: "",
};

const Dashboard = () => {
    const [query, setQuery] = useState<any>(defaultParams);
    const [currentStep, setCurrentStep] = useState(0);

    const navigate = useNavigate();
    const location = useLocation();
    const { t } = useTranslation();
    const { pratiche, fetchData } = useGetPraticheCharts(query);
    const { data } = useGetDashboardIndex();

    const queryParams = new URLSearchParams(location.search);
    const tipo = queryParams.get("tipo");

    const { barChart1, barChart2, doughnutChart1, doughnutChart2 } = getChartData(tipo, pratiche);
    const { pageTitle, barChartTitle1, barChartTitle2, doughnutChart1Title, doughnutChart2Title, tableTitle } = getChartTitle(tipo, t);
    const { doughnutChart1Path, doughnutChart2Path, lastBarChartPath } = getChartPath(tipo);
    const resetQuery = (e: any) => {
        e.preventDefault();
        setQuery(defaultParams);
        setCurrentStep(0)
    };

    const onSubmit = async (e: any) => {
        e.preventDefault();
        await fetchData();
    };

    const customFormat = (amount = "0") => {
        const value = parseFloat(amount);
        const fixedAmount = value.toFixed(2);
        const [whole, fractional] = fixedAmount.split(".");
        const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ".");

        return `${formattedWhole},${fractional}`;
    };

    const tableData = tipo === "pergroup"
        ? pratiche?.pergroup
        : tipo === "tipo"
            ? pratiche?.tipo
            : tipo === "peravvocato"
                ? pratiche?.peravvocato
                : tipo === "stati"
                    ? pratiche?.stati
                    : []

    const columns = [
        { label: "Descrizione", align: "left" },
        { label: "N°", align: "left" },
    ];

    const rows = tableData?.labels?.map((label: string, index: number) => ({
        "Descrizione": label,
        "N°": tableData?.data[index],
    })) || [];

    return (
        <VaporPage>
            <PageTitle
                title={pageTitle}
                pathToPrevPage={"/dashboard"}
                actionButtons={[
                    {
                        label: t("Stampa"),
                        onclick: () => {
                            window.print();
                        },
                        variant: "contained",
                    },
                ]}
            />
            <VaporPage.Section>
                <Filter
                    query={query}
                    setQuery={setQuery}
                    reset={resetQuery}
                    onSubmit={onSubmit}
                    data={data}
                    currentStep={currentStep}
                    setCurrentStep={setCurrentStep}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                <Grid container spacing={3}>
                    <Grid item md={12} xl={12}>
                        <Grid container spacing={3}>
                            <Grid item md={3} xl={3}>
                                <InfoBox
                                    value={
                                        pratiche?.pergroup?.pratiche_per_periodo
                                            ? customFormat(
                                                pratiche?.pergroup
                                                    ?.pratiche_per_periodo
                                            )
                                            : "0,00"
                                    }
                                    text={t("Pratiche per periodo")}
                                    icon="" color="#39a3eb"
                                />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox
                                    value={
                                        pratiche?.pergroup
                                            ?.pratiche_aperte_per_periodo
                                            ? customFormat(
                                                pratiche?.pergroup
                                                    ?.pratiche_aperte_per_periodo
                                            )
                                            : "0,00"
                                    }
                                    text={t("Pratiche aperte per periodo")}
                                    icon="" color="#39a3eb"
                                />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox
                                    value={
                                        pratiche?.pergroup?.media_sul_periodo
                                            ? customFormat(
                                                pratiche?.pergroup
                                                    ?.media_sul_periodo
                                            )
                                            : "0,00"
                                    }
                                    text={t("Media sul periodo")}
                                    icon="" color="#39a3eb"
                                />
                            </Grid>
                            <Grid item md={3} xl={3}>
                                <InfoBox
                                    value={
                                        pratiche?.pergroup
                                            ?.non_fatturate_sul_periodo
                                            ? customFormat(
                                                pratiche?.pergroup
                                                    ?.non_fatturate_sul_periodo
                                            )
                                            : "0,00"
                                    }
                                    text={t("Non Fatturate")}
                                    icon="" color="#39a3eb"
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid item md={12} xl={12}>
                        <Grid container spacing={3}>
                            <Grid item md={8} xl={8}>
                                <Bar
                                    chartData={barChart1}
                                    t={t}
                                    title={barChartTitle1}
                                    legenda={{ display: false, text: ["", ""] }}
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <Table
                                    columns={columns}
                                    rows={rows}
                                    t={t}
                                    maxHeight={550}
                                    title={tableTitle}
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <Doughnut
                                    chartData={doughnutChart1}
                                    navigate={navigate}
                                    t={t}
                                    path={doughnutChart1Path}
                                    title={doughnutChart1Title}
                                    legenda={{ display: false, position: "right" }}
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <Doughnut
                                    chartData={doughnutChart2}
                                    navigate={navigate}
                                    t={t}
                                    path={doughnutChart2Path}
                                    title={doughnutChart2Title}
                                    legenda={{
                                        display: true,
                                        position: "right",
                                    }}
                                />
                            </Grid>
                            <Grid item md={4} xl={4}>
                                <Bar
                                    chartData={barChart2}
                                    navigate={navigate}
                                    t={t}
                                    path={lastBarChartPath}
                                    title={barChartTitle2}
                                    legenda={{ display: false, text: ["", ""] }}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default Dashboard;
