export const getChartData = (tipo: any, pratiche: any) => {
    switch (tipo) {
        case 'pergroup':
            return {
                barChart1: pratiche?.pergroup,
                barChart2: pratiche?.stati,
                doughnutChart1: pratiche?.peravvocato,
                doughnutChart2: pratiche?.tipo,
                tableData: pratiche?.pergroup
            };
        case 'tipo':
            return {
                barChart1: pratiche?.tipo,
                barChart2: pratiche?.pergroup,
                doughnutChart1: pratiche?.peravvocato,
                doughnutChart2: pratiche?.stati,
                tableData: pratiche?.tipo
            };
        case 'peravvocato':
            return {
                barChart1: pratiche?.peravvocato,
                barChart2: pratiche?.pergroup,
                doughnutChart1: pratiche?.tipo,
                doughnutChart2: pratiche?.stati,
                tableData: pratiche?.peravvocato
            };
        case 'stati':
            return {
                barChart1: pratiche?.stati,
                barChart2: pratiche?.pergroup,
                doughnutChart1: pratiche?.tipo,
                doughnutChart2: pratiche?.peravvocato,
                tableData: pratiche?.stati
            };
        default:
            return {
                barChart1: [],
                barChart2: [],
                doughnutChart1: [],
                doughnutChart2: [],
            };
    }
};

export const getChartTitle = (tipo: any, t: any) => {
    switch (tipo) {
        case "pergroup":
            return {
                pageTitle: t("PRATICHE PER MESE"),
                barChartTitle1: "Per mese",
                doughnutChart1Title: "Per avvocato",
                doughnutChart2Title: "Tipologia",
                barChartTitle2: t("Stati"),
                tableTitle: t("Per mese")
            };
        case "tipo":
            return {
                pageTitle: "TIPOLOGIA PRATICHE",
                barChartTitle1: "Tipologia",
                doughnutChart1Title: "Per avvocato",
                doughnutChart2Title: "Stati",
                barChartTitle2: t("Per mese"),
                tableTitle: t("Tipologia")
            };
        case "peravvocato":
            return {
                pageTitle: "PRATICHE PER AVVOCATO",
                barChartTitle1: "Per avvocato",
                doughnutChart1Title: "Tipologia",
                doughnutChart2Title: "Stati",
                barChartTitle2: t("Per mese"),
                tableTitle: t("Per avvocato")
            };
        case "stati":
            return {
                pageTitle: "Dashboard",
                barChartTitle1: t(""),
                doughnutChart1Title: "Tipologia",
                doughnutChart2Title: "Per avvocato",
                barChartTitle2: t("Per mese"),
                tableTitle: t("")
            };
        default:
            return {
                pageTitle: "Dashboard",
                barChartTitle1: "",
                doughnutChart1Title: "",
                doughnutChart2Title: "",
                barChartTitle2: "",
                tableTitle: ""
            };
    }
};


export const getChartPath = (tipo: any) => {
    switch (tipo) {
        case "pergroup":
            return {
                doughnutChart1Path: "/dashboard/pratiche?tipo=peravvocato",
                doughnutChart2Path: "/dashboard/pratiche?tipo=stati",
                lastBarChartPath: "/dashboard/pratiche?tipo=tipo",
            };
        case "tipo":
            return {
                doughnutChart1Path: "/dashboard/pratiche?tipo=peravvocato",
                doughnutChart2Path: "/dashboard/pratiche?tipo=stati",
                lastBarChartPath: "/dashboard/pratiche?tipo=pergroup",
            };
        case "peravvocato":
            return {
                doughnutChart2Path: "/dashboard/pratiche?tipo=tipo",
                doughnutChart1Path: "/dashboard/pratiche?tipo=stati",
                lastBarChartPath: "/dashboard/pratiche?tipo=pergroup",
            };
        case "stati":
            return {
                doughnutChart2Path: "/dashboard/pratiche?tipo=peravvocato",
                doughnutChart1Path: "/dashboard/pratiche?tipo=tipo",
                lastBarChartPath: "/dashboard/pratiche?tipo=pergroup",
            };
        default:
            return {
                doughnutChart1Path: "",
                doughnutChart2Path: "",
                lastBarChartPath: "",
            };
    }
};
