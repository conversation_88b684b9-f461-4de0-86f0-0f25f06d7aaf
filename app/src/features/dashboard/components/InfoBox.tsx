import { Card, CardContent, Typography } from "@vapor/react-material";

interface InfoBoxProps {
    value: string;
    text: string;
    color: string;
    navigate?: any;
    link?: string;
    icon?: string;
}

export default function InfoBox({ value, text, color, navigate, link, icon = "" }: InfoBoxProps) {
    return (
        <Card sx={{ p: 2, cursor: 'pointer' }} onClick={() => navigate(link)}>
            <CardContent sx={{ color: `${color}` }}>
                <Typography variant="h5" component="div" sx={{ display: "flex", justifyContent: "center" }}>
                    {`${value} ${icon}`}
                </Typography>
                <Typography variant="h5" component="div" sx={{ display: "flex", justifyContent: "center" }}>
                    {text}
                </Typography>
            </CardContent>
        </Card>
    );
}