import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
} from "chart.js";
import { Bar } from "react-chartjs-2";
import { Card, CardContent, Typography, Box, Button } from "@vapor/react-material";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

interface BarChartProps {
    chartData: any;
    t: any;
    navigate?: any;
    title: any;
    path?: any;
    legenda?: any;
}

const BarChart = ({ chartData, t, navigate, title, path, legenda }: BarChartProps) => {

    const data = {
        labels: chartData?.labels,
        datasets: [{
            label: "",
            data: chartData?.data,
            backgroundColor: 'rgb(57, 163, 235)',
            borderColor: 'rgb(57, 163, 235)',
            borderWidth: 1
        }]
    };

    return (
        <>
            <Card sx={{ p: 2 }}>
                <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography gutterBottom variant="h5" component="div">
                            {title}
                        </Typography>
                        {path ? <Button variant="contained" sx={{ mr: 1 }} disabled={!chartData?.data} onClick={() => navigate(`${path}`)}>
                            {t("Dettagli")}
                        </Button> : ""}
                    </Box>
                    {
                        chartData ?
                            <Bar
                                style={{ maxHeight: 800 }}
                                data={data}
                                options={{
                                    scales: {
                                        x: {
                                            title: {
                                                display: legenda?.display,
                                                text: legenda?.text[0]
                                            }
                                        },
                                        y: {
                                            title: {
                                                display: legenda?.display,
                                                text: legenda?.text[1]
                                            }
                                        }
                                    }
                                }}
                            /> : <Box sx={{ display: "flex", justifyContent: "center", pt: 2 }}>{t("No data")}</Box>
                    }
                </CardContent>
            </Card>
        </>
    );
};

export default BarChart;
