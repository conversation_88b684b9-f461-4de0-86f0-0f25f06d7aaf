import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Tooltip,
    Legend,
} from 'chart.js';
import { Line } from "react-chartjs-2";
import { Card, CardContent, Typography, Box, Button } from "@vapor/react-material";

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Tooltip,
    Legend
);

const LineChart = ({ chartData, title, navigate, t, path }: any) => {
    return (
        <>
            <Card sx={{ p: 2 }}>
                <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography gutterBottom variant="h5" component="div">
                            {title}
                        </Typography>
                        {path ? <Button variant="contained" sx={{ mr: 1 }} disabled={!chartData?.data} onClick={() => navigate(`${path}`)}>
                            {t("Dettagli")}
                        </Button> : ""}
                    </Box>
                    {
                        chartData?.datasets?.[0]?.data ?
                            <Line
                                style={{ maxHeight: 400 }}
                                data={chartData}
                                options={{
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            align: 'center'
                                        }
                                    }
                                }}
                            /> : <Box sx={{ display: "flex", justifyContent: "center", pt: 2 }}>{t("No data")}</Box>
                    }
                </CardContent>
            </Card>
        </>
    );
};

export default LineChart;
