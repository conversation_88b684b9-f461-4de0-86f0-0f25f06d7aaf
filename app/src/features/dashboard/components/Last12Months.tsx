import { useState, useEffect } from "react";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
} from "chart.js";
import { <PERSON>, Doughnut, Line } from "react-chartjs-2";
import { Card, CardContent, Typography, Box } from "@vapor/react-material";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    LineElement,
    PointElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

interface Last12MonthsProps {
    last12MonthsData: any;
    t: (key: string) => string;
    chartTypes: string[];
    displayLegends: boolean;
}

const Last12Months: React.FC<Last12MonthsProps> = ({ last12MonthsData, t, chartTypes }) => {
    const [data, setData] = useState<any>({
        labels: [],
        datasets: []
    });

    const backgroundColor = [
        "rgba(255, 99, 132, 0.2)",
        "rgba(54, 162, 235, 0.2)"
    ];
    const borderColor = [
        "rgba(255, 99, 132, 1)",
        "rgba(54, 162, 235, 1)"
    ];

    useEffect(() => {
        if (last12MonthsData && last12MonthsData.datasets) {
            const labels = last12MonthsData.labels || [];

            const datasets = [
                {
                    label: "Da incassare",
                    data: Object.values(last12MonthsData?.datasets["Da incassare"]),
                    backgroundColor: backgroundColor[0],
                    borderColor: borderColor[0],
                    borderWidth: 1,
                    type: chartTypes[0] || 'bar'
                },
                {
                    label: "Incassato",
                    data: Object.values(last12MonthsData?.datasets["Incassato"]),
                    backgroundColor: backgroundColor[1],
                    borderColor: borderColor[1],
                    borderWidth: 1,
                    type: chartTypes[1] || 'bar'
                }
            ];

            setData({
                labels: labels,
                datasets: datasets
            });
        }
    }, [last12MonthsData, chartTypes]);

    const renderChart = (type: string, data: any) => {
        const chartOptions: any = {
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Mese'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Fatturato'
                    },
                }
            },
        };

        switch (type) {
            case "bar":
                return <Bar data={data} options={chartOptions} />;
            case "doughnut":
                return <Doughnut data={data} options={chartOptions} />;
            case "line":
                return <Line data={data} options={chartOptions} />;
            default:
                return null;
        }
    };

    return (
        <Card sx={{ p: 2 }}>
            <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography gutterBottom variant="h5" component="div">
                        {t("Ultimi 12 mesi")}
                    </Typography>
                </Box>
                {
                    data?.datasets?.length > 0 ? (
                        chartTypes?.map((chartType: any, index: any) => (
                            <Box key={index} sx={{maxHeight:600, mb: 4 }}>
                                {renderChart(chartType, data)}
                            </Box>
                        ))
                    ) : (
                        <Box sx={{ display: "flex", justifyContent: "center", pt: 2 }}>
                            {t("No data")}
                        </Box>
                    )
                }
            </CardContent>
        </Card>
    );
};

export default Last12Months;
