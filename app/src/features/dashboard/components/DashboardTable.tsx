import { Card, CardContent, Typography, Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";

interface ReusableTableProps {
  columns: any;
  rows: any;
  title: string;
  t: any;
  maxHeight?: number;
  navigation?: any;
  icon?: string;
  path?: string;
}

const ReusableTable = ({ columns, rows, title, t, maxHeight, navigation = false, icon = "", path }: ReusableTableProps) => {
  const navigate = useNavigate();

  const handleNavigate = (row: any) => {
    navigate(`${path}${row.uniqueid}`)
  }

  return (
    <Card sx={{ p: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography gutterBottom variant="h5" component="div">
            {t(title)}
          </Typography>
        </Box>
        <Box sx={{ maxHeight: maxHeight || "auto", overflowY: 'auto' }}>
          {rows && rows.length > 0 ? (
            <TableContainer component={Paper}>
              <Table aria-label="simple table">
                <TableHead>
                  <TableRow>
                    {columns.map((column: any, index: any) => (
                      <TableCell key={index} align={column.align || 'left'}>
                        {t(column.label)}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rows.map((row: any, rowIndex: any) => (
                    <TableRow key={rowIndex} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                      {columns.map((column: any, colIndex: any) => (
                        <TableCell
                          key={colIndex}
                          align={column.align || 'left'}
                          onClick={() => navigation ? handleNavigate(row) : null}
                        >
                          {`${icon} ${row[column.label]}`}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box sx={{ display: "flex", justifyContent: "center", pt: 2 }}>{t("No data")}</Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default ReusableTable;
