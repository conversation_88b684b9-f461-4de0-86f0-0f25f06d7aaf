import { Chart as ChartJ<PERSON>, ArcElement, Toolt<PERSON>, Legend } from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { Card, CardContent, Typography, Box, Button } from "@vapor/react-material";

ChartJS.register(ArcElement, Tooltip, Legend);

interface DoughnutChartProps {
    chartData: any;
    navigate: any;
    t: any;
    title: string;
    path?: string;
    legenda?: any;
}

var backgroundColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];
var borderColor = [
    'rgb(252, 78, 61)',
    'rgb(86, 118, 212)',
    'rgb(197, 48, 144)',
    'rgb(162, 71, 185)',
    'rgb(218, 44, 56)',
    'rgb(119, 69, 236)',
    'rgb(0, 170, 0)',
    'rgb(13, 108, 128)',
    'rgb(17, 76, 92)'
];

const DoughnutChart = ({ chartData, navigate, t, title, path, legenda }: DoughnutChartProps) => {

    const data = {
        labels: chartData?.labels,
        datasets: [{
            label: "",
            data: chartData?.data,
            backgroundColor: backgroundColor,
            borderColor: borderColor,
            borderWidth: 1
        }]
    };

    return (
        <>
            <Card sx={{ p: 2 }}>
                <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography gutterBottom variant="h5" component="div">
                            {title}
                        </Typography>
                        {path &&
                            <Button variant="contained" sx={{ mr: 1 }} disabled={!chartData?.data} onClick={() => navigate(`${path}`)}>
                                {t("Dettagli")}
                            </Button>
                        }
                    </Box>
                    {
                        data ?
                            <Doughnut
                                style={{ maxHeight: 300 }}
                                data={data}
                                options={{
                                    plugins: {
                                        legend: {
                                            position: legenda?.position,
                                            align: 'center',
                                            display: legenda?.display,
                                        }
                                    }
                                }}
                            /> : <Box sx={{ display: "flex", justifyContent: "center", pt: 2 }}>{t("No data")}</Box>
                    }
                </CardContent>
            </Card>
        </>
    );
};

export default DoughnutChart;
