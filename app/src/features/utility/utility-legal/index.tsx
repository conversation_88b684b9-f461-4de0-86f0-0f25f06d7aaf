import Interessi from "./Interessi";
import Interessimora from "./Interessimora";
import Scorporo from "./Scorporo";
import MacroPage from "../../archive/pages/MacroPage";

export const utilitylegal = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/utilities/interessi",
            element: <Interessi route="interessi" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/utilities/interessilegali",
            element: <Interessi route="interessilegali" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/utilities/interessimora",
            element: <Interessimora />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/macro/utility",
            element: <MacroPage />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/utilities/scorporo",
            element: <Scorporo />,
        },
    },
];
