import FindRates from "./FindRates";

export const useInteresi = () => {
    const getRatesArray = (mode: any, rate: any) => {
        var data = new Array();
        if (mode == 0) {
            /** Table of legal interest rate TO BE checked with Government updates */
            data[0] = new Array(
                new Date(1970, 0, 1, 0, 0, 0).getTime(),
                new Date(1990, 11, 15, 23, 59, 59).getTime(),
                0.05
            );
            data[1] = new Array(
                new Date(1990, 11, 16, 0, 0, 0).getTime(),
                new Date(1990, 11, 31, 23, 59, 59).getTime(),
                0.1
            );
            data[2] = new Array(
                new Date(1991, 0, 1, 0, 0, 0).getTime(),
                new Date(1996, 11, 31, 23, 59, 59).getTime(),
                0.1
            );
            data[3] = new Array(
                new Date(1997, 0, 1, 0, 0, 0).getTime(),
                new Date(1998, 11, 31, 23, 59, 59).getTime(),
                0.05
            );
            data[4] = new Array(
                new Date(1999, 0, 1, 0, 0, 0).getTime(),
                new Date(2000, 11, 31, 23, 59, 59).getTime(),
                0.025
            );
            data[5] = new Array(
                new Date(2001, 0, 1, 0, 0, 0).getTime(),
                new Date(2001, 11, 31, 23, 59, 59).getTime(),
                0.035
            );
            data[6] = new Array(
                new Date(2002, 0, 1, 0, 0, 0).getTime(),
                new Date(2003, 11, 31, 23, 59, 59).getTime(),
                0.03
            );
            data[7] = new Array(
                new Date(2004, 0, 1, 0, 0, 0).getTime(),
                new Date(2007, 11, 31, 23, 59, 59).getTime(),
                0.025
            );
            data[8] = new Array(
                new Date(2008, 0, 1, 0, 0, 0).getTime(),
                new Date(2009, 11, 31, 23, 59, 59).getTime(),
                0.03
            );
            data[9] = new Array(
                new Date(2010, 0, 1, 0, 0, 0).getTime(),
                new Date(2010, 11, 31, 23, 59, 59).getTime(),
                0.01
            );
            data[10] = new Array(
                new Date(2011, 0, 1, 0, 0, 0).getTime(),
                new Date(2011, 11, 31, 23, 59, 59).getTime(),
                0.015
            );
            data[11] = new Array(
                new Date(2012, 0, 1, 0, 0, 0).getTime(),
                new Date(2013, 11, 31, 23, 59, 59).getTime(),
                0.025
            );
            data[12] = new Array(
                new Date(2014, 0, 1, 0, 0, 0).getTime(),
                new Date(2014, 11, 31, 23, 59, 59).getTime(),
                0.01
            );
            data[13] = new Array(
                new Date(2015, 0, 1, 0, 0, 0).getTime(),
                new Date(2015, 11, 31, 23, 59, 59).getTime(),
                0.005
            );
            data[14] = new Array(
                new Date(2016, 0, 1, 0, 0, 0).getTime(),
                new Date(2016, 11, 31, 23, 59, 59).getTime(),
                0.002
            );
            data[15] = new Array(
                new Date(2017, 0, 1, 0, 0, 0).getTime(),
                new Date(2017, 11, 31, 23, 59, 59).getTime(),
                0.001
            );
            data[16] = new Array(
                new Date(2018, 0, 1, 0, 0, 0).getTime(),
                new Date(2018, 11, 31, 23, 59, 59).getTime(),
                0.003
            );
            data[17] = new Array(
                new Date(2019, 0, 1, 0, 0, 0).getTime(),
                new Date(2019, 11, 31, 23, 59, 59).getTime(),
                0.008
            );
            data[18] = new Array(
                new Date(2020, 0, 1, 0, 0, 0).getTime(),
                new Date(2020, 11, 31, 23, 59, 59).getTime(),
                0.0005
            );
            data[19] = new Array(
                new Date(2021, 0, 1, 0, 0, 0).getTime(),
                new Date(2021, 11, 31, 23, 59, 59).getTime(),
                0.0001
            );
            data[20] = new Array(
                new Date(2022, 0, 1, 0, 0, 0).getTime(),
                new Date(2022, 11, 31, 23, 59, 59).getTime(),
                0.0125
            );
            data[21] = new Array(
                new Date(2023, 0, 1, 0, 0, 0).getTime(),
                new Date(2023, 11, 31, 23, 59, 59).getTime(),
                0.05
            );
            data[22] = new Array(
                new Date(2024, 0, 1, 0, 0, 0).getTime(),
                new Date(2024, 11, 31, 23, 59, 59).getTime(),
                0.025
            );
            data[23] = new Array(
                new Date(2025, 0, 1, 0, 0, 0).getTime(),
                new Date(2025, 11, 31, 23, 59, 59).getTime(),
                0.02
            );
            data[24] = new Array(
                new Date(2026, 0, 1, 0, 0, 0).getTime(),
                new Date(2026, 11, 31, 23, 59, 59).getTime(),
                0.02
            );
            data[25] = new Array(
                new Date(2027, 0, 1, 0, 0, 0).getTime(),
                new Date(2027, 11, 31, 23, 59, 59).getTime(),
                0.02
            );
        } else if (mode == 1) {
            data[0] = new Array(
                new Date(1970, 0, 1, 0, 0, 0).getTime(),
                new Date().getTime(),
                rate
            );
        }
        return data;
    };

    const getRatesArrayInteressiDiMora = () => {
        const data: [number, number, number][] = [
            [
                new Date(2002, 6, 1, 0, 0, 0).getTime(),
                new Date(2002, 11, 31, 23, 59, 59).getTime(),
                0.1035,
            ],
            [
                new Date(2003, 0, 1, 0, 0, 0).getTime(),
                new Date(2003, 5, 30, 23, 59, 59).getTime(),
                0.0985,
            ],
            [
                new Date(2003, 6, 1, 0, 0, 0).getTime(),
                new Date(2003, 11, 31, 23, 59, 59).getTime(),
                0.091,
            ],
            [
                new Date(2004, 0, 1, 0, 0, 0).getTime(),
                new Date(2004, 5, 30, 23, 59, 59).getTime(),
                0.0902,
            ],
            [
                new Date(2004, 6, 1, 0, 0, 0).getTime(),
                new Date(2004, 11, 31, 23, 59, 59).getTime(),
                0.0901,
            ],
            [
                new Date(2005, 0, 1, 0, 0, 0).getTime(),
                new Date(2005, 5, 30, 23, 59, 59).getTime(),
                0.0909,
            ],
            [
                new Date(2005, 6, 1, 0, 0, 0).getTime(),
                new Date(2005, 11, 31, 23, 59, 59).getTime(),
                0.0905,
            ],
            [
                new Date(2006, 0, 1, 0, 0, 0).getTime(),
                new Date(2006, 5, 30, 23, 59, 59).getTime(),
                0.0925,
            ],
            [
                new Date(2006, 6, 1, 0, 0, 0).getTime(),
                new Date(2006, 11, 31, 23, 59, 59).getTime(),
                0.0983,
            ],
            [
                new Date(2007, 0, 1, 0, 0, 0).getTime(),
                new Date(2007, 5, 30, 23, 59, 59).getTime(),
                0.1058,
            ],
            [
                new Date(2007, 6, 1, 0, 0, 0).getTime(),
                new Date(2007, 11, 31, 23, 59, 59).getTime(),
                0.1107,
            ],
            [
                new Date(2008, 0, 1, 0, 0, 0).getTime(),
                new Date(2008, 5, 30, 23, 59, 59).getTime(),
                0.112,
            ],
            [
                new Date(2008, 6, 1, 0, 0, 0).getTime(),
                new Date(2008, 11, 31, 23, 59, 59).getTime(),
                0.111,
            ],
            [
                new Date(2009, 0, 1, 0, 0, 0).getTime(),
                new Date(2009, 5, 30, 23, 59, 59).getTime(),
                0.095,
            ],
            [
                new Date(2009, 6, 1, 0, 0, 0).getTime(),
                new Date(2009, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2010, 0, 1, 0, 0, 0).getTime(),
                new Date(2010, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2010, 6, 1, 0, 0, 0).getTime(),
                new Date(2010, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2011, 0, 1, 0, 0, 0).getTime(),
                new Date(2011, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2011, 6, 1, 0, 0, 0).getTime(),
                new Date(2011, 11, 31, 23, 59, 59).getTime(),
                0.0825,
            ],
            [
                new Date(2012, 0, 1, 0, 0, 0).getTime(),
                new Date(2012, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2012, 6, 1, 0, 0, 0).getTime(),
                new Date(2012, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2013, 0, 1, 0, 0, 0).getTime(),
                new Date(2013, 5, 30, 23, 59, 59).getTime(),
                0.0875,
            ],
            [
                new Date(2013, 6, 1, 0, 0, 0).getTime(),
                new Date(2013, 11, 31, 23, 59, 59).getTime(),
                0.085,
            ],
            [
                new Date(2014, 0, 1, 0, 0, 0).getTime(),
                new Date(2014, 5, 30, 23, 59, 59).getTime(),
                0.0825,
            ],
            [
                new Date(2014, 6, 1, 0, 0, 0).getTime(),
                new Date(2014, 11, 31, 23, 59, 59).getTime(),
                0.0815,
            ],
            [
                new Date(2015, 0, 1, 0, 0, 0).getTime(),
                new Date(2015, 5, 30, 23, 59, 59).getTime(),
                0.0805,
            ],
            [
                new Date(2015, 6, 1, 0, 0, 0).getTime(),
                new Date(2015, 11, 31, 23, 59, 59).getTime(),
                0.0805,
            ],
            [
                new Date(2016, 0, 1, 0, 0, 0).getTime(),
                new Date(2016, 5, 30, 23, 59, 59).getTime(),
                0.0805,
            ],
            [
                new Date(2016, 6, 1, 0, 0, 0).getTime(),
                new Date(2016, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2017, 0, 1, 0, 0, 0).getTime(),
                new Date(2017, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2017, 6, 1, 0, 0, 0).getTime(),
                new Date(2017, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2018, 0, 1, 0, 0, 0).getTime(),
                new Date(2018, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2018, 6, 1, 0, 0, 0).getTime(),
                new Date(2018, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2019, 0, 1, 0, 0, 0).getTime(),
                new Date(2019, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2019, 6, 1, 0, 0, 0).getTime(),
                new Date(2019, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2020, 0, 1, 0, 0, 0).getTime(),
                new Date(2020, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2020, 6, 1, 0, 0, 0).getTime(),
                new Date(2020, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2021, 0, 1, 0, 0, 0).getTime(),
                new Date(2021, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2021, 6, 1, 0, 0, 0).getTime(),
                new Date(2021, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2022, 0, 1, 0, 0, 0).getTime(),
                new Date(2022, 5, 30, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2022, 6, 1, 0, 0, 0).getTime(),
                new Date(2022, 11, 31, 23, 59, 59).getTime(),
                0.08,
            ],
            [
                new Date(2023, 0, 1, 0, 0, 0).getTime(),
                new Date(2023, 5, 30, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2023, 6, 1, 0, 0, 0).getTime(),
                new Date(2023, 11, 31, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2024, 0, 1, 0, 0, 0).getTime(),
                new Date(2024, 5, 30, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2024, 6, 1, 0, 0, 0).getTime(),
                new Date(2024, 11, 31, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2025, 0, 1, 0, 0, 0).getTime(),
                new Date(2025, 5, 30, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2025, 6, 1, 0, 0, 0).getTime(),
                new Date(2025, 11, 31, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2026, 0, 1, 0, 0, 0).getTime(),
                new Date(2026, 5, 30, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2026, 6, 1, 0, 0, 0).getTime(),
                new Date(2026, 11, 31, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2027, 0, 1, 0, 0, 0).getTime(),
                new Date(2027, 5, 30, 23, 59, 59).getTime(),
                0.105,
            ],
            [
                new Date(2027, 6, 1, 0, 0, 0).getTime(),
                new Date(2027, 11, 31, 23, 59, 59).getTime(),
                0.105,
            ],
        ];
        return data;
    };

    const findRates = (d1: any, d2: any, oneDay: any, mode: any, rate: any) => {
        var data = getRatesArray(mode, rate);

        if (mode == 55) {
            /** Calcolo interessi di mora */
            data = getRatesArrayInteressiDiMora();
        }

        oneDay = 86400000;

        var c1 = new Date(d1.getTime());
        var c2 = new Date(d2.getTime());

        var resultArray = new Array();
        var resultArrayIndex = 0;

        if (data !== undefined) {
            for (var i = 0; i < data.length; i++) {
                if (c1.getTime() >= data[i][0] && c1.getTime() <= data[i][1]) {
                    if (c2.getTime() <= data[i][1]) {
                        var tmpArray = new Array();
                        tmpArray[0] = c1;
                        tmpArray[1] = c2;
                        tmpArray[2] = data[i][2];
                        resultArray[resultArrayIndex++] = tmpArray;
                        break;
                    } else {
                        var tmpArray = new Array();
                        tmpArray[0] = c1;
                        tmpArray[1] = new Date(data[i][1]);
                        tmpArray[2] = data[i][2];
                        resultArray[resultArrayIndex++] = tmpArray;

                        var cTmpDate = new Date(data[i][1]);
                        var day = cTmpDate.getDate();
                        var month = cTmpDate.getMonth() + 1;
                        var year = cTmpDate.getFullYear();
                        c1 = new Date(year, month - 1, day, 0, 0, 0);
                        c1 = new Date(c1.getTime() + oneDay);
                    }
                }
            }
        }
        return resultArray;
    };

    return {
        getRatesArray,
        getRatesArrayInteressiDiMora,
        findRates,
    };
};

export const calculateMora = (
    startDate: string,
    endDate: string,
    id: string,
    amount: number,
    agriproducts: string,
    agriproducts_value: string,
    before2012: string
) => {
    let tableRows: any[] = [];
    const parts: string[] = startDate.split("/");
    let sday = id === "custom" ? parseInt(parts[0]) : parseInt(parts[0]) + 1;
    let smonth = parseInt(parts[1]);
    let syear = parseInt(parts[2]);
    let d1d = new Date(syear, smonth - 1, sday, 0, 0, 0);

    const endParts: string[] = endDate.split("/");
    let eday = parseInt(endParts[0]);
    let emonth = parseInt(endParts[1]);
    let eyear = parseInt(endParts[2]);

    let d2d = new Date(eyear, emonth - 1, eday, 23, 59, 59);

    let calcArray: any[] = [];

    const tmpArray: any[] = [];
    tmpArray[0] = d1d;
    tmpArray[1] = d2d;

    calcArray[0] = tmpArray;

    let superTotalInterest: number = 0;
    let superTotalDays: number = 0;
    let firstTime: boolean = true;
    let oneDay = 86400000;

    for (let i = 0; i < calcArray.length; i++) {
        const d1: Date = calcArray[i][0];
        const d2: Date = calcArray[i][1];
        const findRatesInstance = new FindRates("mora");
        const resultArray: any[] = findRatesInstance.findRates(d1, d2);
        let totalInterest: number = 0;

        for (let j = 0; j < resultArray.length; j++) {
            const startDate: Date = resultArray[j][0];
            const stopDate: Date = resultArray[j][1];
            let diff: number = Math.round(
                (stopDate.getTime() - startDate.getTime()) / oneDay
            );

            if (firstTime) {
                diff -= 1;
                firstTime = false;
            }

            superTotalDays += diff;
            let rate: number = resultArray[j][2];

            if (before2012 === "on" && startDate.getFullYear() > 2012) {
                rate = rate - 0.01;
            }
            if (agriproducts === "on") {
                let upRate = parseInt(agriproducts_value);
                rate += upRate / 100;
            }

            let interest: number = (amount * rate * diff) / 365;
            interest = Math.round(interest * 100) / 100;
            totalInterest += interest;

            /** Print result **/
            const dayStart = startDate.getDate();
            const day = dayStart < 10 ? "0" + dayStart : dayStart.toString();

            const monthStart = startDate.getMonth() + 1;
            const month =
                monthStart < 10 ? "0" + monthStart : monthStart.toString();

            const year = startDate.getFullYear();
            const dStartDate = day + "/" + month + "/" + year;

            const dayStop = stopDate.getDate();
            const dayStopStr =
                dayStop < 10 ? "0" + dayStop : dayStop.toString();
            const monthStop = stopDate.getMonth() + 1;
            const monthStopStr =
                monthStop < 10 ? "0" + monthStop : monthStop.toString();
            const yearStop = stopDate.getFullYear();
            const dStopDate = dayStopStr + "/" + monthStopStr + "/" + yearStop;
            const capitale = parseFloat(amount.toString()).toFixed(2);
            const tasso = (Math.round(rate * 100 * 100) / 100).toFixed(2);
            const interesse = interest.toFixed(2);

            let currency = "euro";

            tableRows.push([
                dStartDate,
                dStopDate,
                capitale,
                tasso,
                diff,
                interesse,
                currency,
                id,
            ]);
        }
        superTotalInterest += totalInterest;
    }
    superTotalInterest = parseFloat(
        (Math.round(superTotalInterest * 100) / 100).toFixed(2)
    );
    let totalAmount;
    totalAmount = parseFloat(amount.toString()) + superTotalInterest;

    totalAmount = parseFloat((Math.round(totalAmount * 100) / 100).toFixed(2));

    const numberFormatter = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "EUR",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });

    const superTotalInterestFormatted =
        numberFormatter.format(superTotalInterest);
    const totalAmountFormatted = numberFormatter.format(totalAmount);
    return {
        totalDays: superTotalDays,
        totalInterests: superTotalInterest,
        totalInterestsFormatted: superTotalInterestFormatted,
        totalAmountFormatted: totalAmountFormatted,
        totalAmount: totalAmount,
        id,
        tableRows,
    };
};
