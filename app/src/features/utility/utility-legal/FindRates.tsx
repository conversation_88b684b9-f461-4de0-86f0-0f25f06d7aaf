class FindRates {
    private type: string;
    private oneDay: number;
    private rate: number;

    constructor(type: string) {
        this.type = type;
        this.oneDay = 86400000; // Milliseconds in a day
        this.rate = 10;
    }

    private getRatesMora(): [number, number, number][] {
        var data = new Array();
        data[0] = new Array(
            new Date(2002, 6, 1, 0, 0, 0).getTime(),
            new Date(2002, 11, 31, 23, 59, 59).getTime(),
            0.1035
        );
        data[1] = new Array(
            new Date(2003, 0, 1, 0, 0, 0).getTime(),
            new Date(2003, 5, 30, 23, 59, 59).getTime(),
            0.0985
        );
        data[2] = new Array(
            new Date(2003, 6, 1, 0, 0, 0).getTime(),
            new Date(2003, 11, 31, 23, 59, 59).getTime(),
            0.091
        );
        data[3] = new Array(
            new Date(2004, 0, 1, 0, 0, 0).getTime(),
            new Date(2004, 5, 30, 23, 59, 59).getTime(),
            0.0902
        );
        data[4] = new Array(
            new Date(2004, 6, 1, 0, 0, 0).getTime(),
            new Date(2004, 11, 31, 23, 59, 59).getTime(),
            0.0901
        );
        data[5] = new Array(
            new Date(2005, 0, 1, 0, 0, 0).getTime(),
            new Date(2005, 5, 30, 23, 59, 59).getTime(),
            0.0909
        );
        data[6] = new Array(
            new Date(2005, 6, 1, 0, 0, 0).getTime(),
            new Date(2005, 11, 31, 23, 59, 59).getTime(),
            0.0905
        );
        data[7] = new Array(
            new Date(2006, 0, 1, 0, 0, 0).getTime(),
            new Date(2006, 5, 30, 23, 59, 59).getTime(),
            0.0925
        );
        data[8] = new Array(
            new Date(2006, 6, 1, 0, 0, 0).getTime(),
            new Date(2006, 11, 31, 23, 59, 59).getTime(),
            0.0983
        );
        data[9] = new Array(
            new Date(2007, 0, 1, 0, 0, 0).getTime(),
            new Date(2007, 5, 30, 23, 59, 59).getTime(),
            0.1058
        );
        data[10] = new Array(
            new Date(2007, 6, 1, 0, 0, 0).getTime(),
            new Date(2007, 11, 31, 23, 59, 59).getTime(),
            0.1107
        );
        data[11] = new Array(
            new Date(2008, 0, 1, 0, 0, 0).getTime(),
            new Date(2008, 5, 30, 23, 59, 59).getTime(),
            0.112
        );
        data[12] = new Array(
            new Date(2008, 6, 1, 0, 0, 0).getTime(),
            new Date(2008, 11, 31, 23, 59, 59).getTime(),
            0.111
        );
        data[13] = new Array(
            new Date(2009, 0, 1, 0, 0, 0).getTime(),
            new Date(2009, 5, 30, 23, 59, 59).getTime(),
            0.095
        );
        data[14] = new Array(
            new Date(2009, 6, 1, 0, 0, 0).getTime(),
            new Date(2009, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[15] = new Array(
            new Date(2010, 0, 1, 0, 0, 0).getTime(),
            new Date(2010, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[16] = new Array(
            new Date(2010, 6, 1, 0, 0, 0).getTime(),
            new Date(2010, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[17] = new Array(
            new Date(2011, 0, 1, 0, 0, 0).getTime(),
            new Date(2011, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[18] = new Array(
            new Date(2011, 6, 1, 0, 0, 0).getTime(),
            new Date(2011, 11, 31, 23, 59, 59).getTime(),
            0.0825
        );
        data[19] = new Array(
            new Date(2012, 0, 1, 0, 0, 0).getTime(),
            new Date(2012, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[20] = new Array(
            new Date(2012, 6, 1, 0, 0, 0).getTime(),
            new Date(2012, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[21] = new Array(
            new Date(2013, 0, 1, 0, 0, 0).getTime(),
            new Date(2013, 5, 30, 23, 59, 59).getTime(),
            0.0875
        );
        data[22] = new Array(
            new Date(2013, 6, 1, 0, 0, 0).getTime(),
            new Date(2013, 11, 31, 23, 59, 59).getTime(),
            0.085
        );
        data[23] = new Array(
            new Date(2014, 0, 1, 0, 0, 0).getTime(),
            new Date(2014, 5, 30, 23, 59, 59).getTime(),
            0.0825
        );
        data[24] = new Array(
            new Date(2014, 6, 1, 0, 0, 0).getTime(),
            new Date(2014, 11, 31, 23, 59, 59).getTime(),
            0.0815
        );
        data[25] = new Array(
            new Date(2015, 0, 1, 0, 0, 0).getTime(),
            new Date(2015, 5, 30, 23, 59, 59).getTime(),
            0.0805
        );
        data[26] = new Array(
            new Date(2015, 6, 1, 0, 0, 0).getTime(),
            new Date(2015, 11, 31, 23, 59, 59).getTime(),
            0.0805
        );
        data[27] = new Array(
            new Date(2016, 0, 1, 0, 0, 0).getTime(),
            new Date(2016, 5, 30, 23, 59, 59).getTime(),
            0.0805
        );
        data[28] = new Array(
            new Date(2016, 6, 1, 0, 0, 0).getTime(),
            new Date(2016, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[29] = new Array(
            new Date(2017, 0, 1, 0, 0, 0).getTime(),
            new Date(2017, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[30] = new Array(
            new Date(2017, 6, 1, 0, 0, 0).getTime(),
            new Date(2017, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[31] = new Array(
            new Date(2018, 0, 1, 0, 0, 0).getTime(),
            new Date(2018, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[32] = new Array(
            new Date(2018, 6, 1, 0, 0, 0).getTime(),
            new Date(2018, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[33] = new Array(
            new Date(2019, 0, 1, 0, 0, 0).getTime(),
            new Date(2019, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[34] = new Array(
            new Date(2019, 6, 1, 0, 0, 0).getTime(),
            new Date(2019, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[35] = new Array(
            new Date(2020, 0, 1, 0, 0, 0).getTime(),
            new Date(2020, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[36] = new Array(
            new Date(2020, 6, 1, 0, 0, 0).getTime(),
            new Date(2020, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[37] = new Array(
            new Date(2021, 0, 1, 0, 0, 0).getTime(),
            new Date(2021, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[38] = new Array(
            new Date(2021, 6, 1, 0, 0, 0).getTime(),
            new Date(2021, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[39] = new Array(
            new Date(2022, 0, 1, 0, 0, 0).getTime(),
            new Date(2022, 5, 30, 23, 59, 59).getTime(),
            0.08
        );
        data[40] = new Array(
            new Date(2022, 6, 1, 0, 0, 0).getTime(),
            new Date(2022, 11, 31, 23, 59, 59).getTime(),
            0.08
        );
        data[41] = new Array(
            new Date(2023, 0, 1, 0, 0, 0).getTime(),
            new Date(2023, 5, 30, 23, 59, 59).getTime(),
            0.105
        );
        data[42] = new Array(
            new Date(2023, 6, 1, 0, 0, 0).getTime(),
            new Date(2023, 11, 31, 23, 59, 59).getTime(),
            0.12
        );
        data[43] = new Array(
            new Date(2024, 0, 1, 0, 0, 0).getTime(),
            new Date(2024, 5, 30, 23, 59, 59).getTime(),
            0.12
        );
        data[44] = new Array(
            new Date(2024, 6, 1, 0, 0, 0).getTime(),
            new Date(2024, 11, 31, 23, 59, 59).getTime(),
            0.12
        );
        data[45] = new Array(
            new Date(2025, 0, 1, 0, 0, 0).getTime(),
            new Date(2025, 5, 30, 23, 59, 59).getTime(),
            0.12
        );
        data[46] = new Array(
            new Date(2025, 6, 1, 0, 0, 0).getTime(),
            new Date(2025, 11, 31, 23, 59, 59).getTime(),
            0.12
        );
        data[47] = new Array(
            new Date(2026, 0, 1, 0, 0, 0).getTime(),
            new Date(2026, 5, 30, 23, 59, 59).getTime(),
            0.12
        );
        data[48] = new Array(
            new Date(2026, 6, 1, 0, 0, 0).getTime(),
            new Date(2026, 11, 31, 23, 59, 59).getTime(),
            0.12
        );
        data[49] = new Array(
            new Date(2027, 0, 1, 0, 0, 0).getTime(),
            new Date(2027, 5, 30, 23, 59, 59).getTime(),
            0.12
        );
        data[50] = new Array(
            new Date(2027, 6, 1, 0, 0, 0).getTime(),
            new Date(2027, 11, 31, 23, 59, 59).getTime(),
            0.12
        );

        /*<-IMPORTANTE-> quando si aggiorna un tasso bisogna riportarlo anche sugli anni successivi così quando si imposta una data di scadenza lunga riesce a calcolare*/

        return data;
    }

    private getRatesTassoFisso(): [number, number, number][] {
        const data: [number, number, number][] = [
            [
                new Date(1970, 0, 1, 0, 0, 0).getTime(),
                new Date().getTime(),
                this.rate,
            ],
        ];

        return data;
    }

    private getRatesLegali(): [number, number, number][] {
        const data: [number, number, number][] = [
            [
                new Date(1970, 0, 1, 0, 0, 0).getTime(),
                new Date(1990, 11, 15, 23, 59, 59).getTime(),
                0.05,
            ],
            [
                new Date(1990, 11, 16, 0, 0, 0).getTime(),
                new Date(1990, 11, 31, 23, 59, 59).getTime(),
                0.1,
            ],
            [
                new Date(1991, 0, 1, 0, 0, 0).getTime(),
                new Date(1996, 11, 31, 23, 59, 59).getTime(),
                0.1,
            ],
            [
                new Date(1997, 0, 1, 0, 0, 0).getTime(),
                new Date(1998, 11, 31, 23, 59, 59).getTime(),
                0.05,
            ],
            [
                new Date(1999, 0, 1, 0, 0, 0).getTime(),
                new Date(2000, 11, 31, 23, 59, 59).getTime(),
                0.025,
            ],
            [
                new Date(2001, 0, 1, 0, 0, 0).getTime(),
                new Date(2001, 11, 31, 23, 59, 59).getTime(),
                0.035,
            ],
            [
                new Date(2002, 0, 1, 0, 0, 0).getTime(),
                new Date(2003, 11, 31, 23, 59, 59).getTime(),
                0.03,
            ],
            [
                new Date(2004, 0, 1, 0, 0, 0).getTime(),
                new Date(2007, 11, 31, 23, 59, 59).getTime(),
                0.025,
            ],
            [
                new Date(2008, 0, 1, 0, 0, 0).getTime(),
                new Date(2009, 11, 31, 23, 59, 59).getTime(),
                0.03,
            ],
            [
                new Date(2010, 0, 1, 0, 0, 0).getTime(),
                new Date(2010, 11, 31, 23, 59, 59).getTime(),
                0.01,
            ],
            [
                new Date(2011, 0, 1, 0, 0, 0).getTime(),
                new Date(2011, 11, 31, 23, 59, 59).getTime(),
                0.015,
            ],
            [
                new Date(2012, 0, 1, 0, 0, 0).getTime(),
                new Date(2013, 11, 31, 23, 59, 59).getTime(),
                0.025,
            ],
            [
                new Date(2014, 0, 1, 0, 0, 0).getTime(),
                new Date(2014, 11, 31, 23, 59, 59).getTime(),
                0.01,
            ],
            [
                new Date(2015, 0, 1, 0, 0, 0).getTime(),
                new Date(2015, 11, 31, 23, 59, 59).getTime(),
                0.005,
            ],
            [
                new Date(2016, 0, 1, 0, 0, 0).getTime(),
                new Date(2016, 11, 31, 23, 59, 59).getTime(),
                0.002,
            ],
            [
                new Date(2017, 0, 1, 0, 0, 0).getTime(),
                new Date(2017, 11, 31, 23, 59, 59).getTime(),
                0.001,
            ],
            [
                new Date(2018, 0, 1, 0, 0, 0).getTime(),
                new Date(2018, 11, 31, 23, 59, 59).getTime(),
                0.003,
            ],
            [
                new Date(2019, 0, 1, 0, 0, 0).getTime(),
                new Date(2019, 11, 31, 23, 59, 59).getTime(),
                0.008,
            ],
            [
                new Date(2020, 0, 1, 0, 0, 0).getTime(),
                new Date(2020, 11, 31, 23, 59, 59).getTime(),
                0.0005,
            ],
            [
                new Date(2021, 0, 1, 0, 0, 0).getTime(),
                new Date(2021, 11, 31, 23, 59, 59).getTime(),
                0.0001,
            ],
            [
                new Date(2022, 0, 1, 0, 0, 0).getTime(),
                new Date(2022, 11, 31, 23, 59, 59).getTime(),
                0.0125,
            ],
            [
                new Date(2023, 0, 1, 0, 0, 0).getTime(),
                new Date(2023, 11, 31, 23, 59, 59).getTime(),
                0.05,
            ],
            [
                new Date(2024, 0, 1, 0, 0, 0).getTime(),
                new Date(2024, 11, 31, 23, 59, 59).getTime(),
                0.2,
            ],
            [
                new Date(2025, 0, 1, 0, 0, 0).getTime(),
                new Date(2025, 11, 31, 23, 59, 59).getTime(),
                0.2,
            ],
            [
                new Date(2026, 0, 1, 0, 0, 0).getTime(),
                new Date(2026, 11, 31, 23, 59, 59).getTime(),
                0.2,
            ],
            [
                new Date(2027, 0, 1, 0, 0, 0).getTime(),
                new Date(2027, 11, 31, 23, 59, 59).getTime(),
                0.2,
            ],
        ];

        /*<-IMPORTANTE-> quando si aggiorna un tasso bisogna riportarlo anche sugli anni successivi così quando si imposta una data di scadenza lunga riesce a calcolare*/

        return data;
    }

    findRates(d1: Date, d2: Date): [Date, Date, number][] {
        const resultArray: [Date, Date, number][] = [];
        let data: [number, number, number][] = [];

        if (this.type === "mora") {
            data = this.getRatesMora();
        } else if (this.type === "tassofisso") {
            data = this.getRatesTassoFisso();
        } else if (this.type === "legali") {
            data = this.getRatesLegali();
        }

        try {
            let c1 = new Date(d1.getTime());
            const c2 = new Date(d2.getTime());

            for (let i = 0; i < data.length; i++) {
                if (c1.getTime() >= data[i][0] && c1.getTime() <= data[i][1]) {
                    if (c2.getTime() <= data[i][1]) {
                        const tmpArray: [Date, Date, number] = [
                            c1,
                            c2,
                            data[i][2],
                        ];
                        resultArray.push(tmpArray);
                        break;
                    } else {
                        const tmpArray: [Date, Date, number] = [
                            c1,
                            new Date(data[i][1]),
                            data[i][2],
                        ];
                        resultArray.push(tmpArray);

                        const cTmpDate = new Date(data[i][1]);
                        const day = cTmpDate.getDate();
                        const month = cTmpDate.getMonth() + 1;
                        const year = cTmpDate.getFullYear();
                        c1 = new Date(year, month - 1, day, 0, 0, 0);
                        c1 = new Date(c1.getTime() + this.oneDay);
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }

        return resultArray;
    }
}

export default FindRates;
