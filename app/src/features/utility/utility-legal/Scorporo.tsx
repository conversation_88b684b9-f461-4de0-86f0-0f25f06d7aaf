import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Box,
    Button,
    InputAdornment,
    InputLabel,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
} from "@vapor/react-material";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../custom-components/FormInput";
import PageTitle from "../../../custom-components/PageTitle";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import usePostCustom from "../../../hooks/usePostCustom";

const Scorporo = () => {
    const [formData, setFormData] = useState<{
        useCassaAvvocati: number;
        useRitenuta: number;
        useSpeseGenerali: number;
        amount: number;
        speseEsenti: number;
        useSpeseGeneraliCheck: string;
        useCassaAvvocatiCheck: string;
        useRitenutaCheck: string;
        regimeSemplificato: string;
        useSplitPaymentCheck: string;
    }>({
        useCassaAvvocati: 0,
        useRitenuta: 0,
        useSpeseGenerali: 0,
        amount: 0,
        speseEsenti: 0,
        useSpeseGeneraliCheck: "on",
        useCassaAvvocatiCheck: "on",
        useRitenutaCheck: "on",
        regimeSemplificato: "",
        useSplitPaymentCheck: "",
    });

    const [imponibile, setImponibile] = useState<any>();
    const [speseGenerali, setSpeseGenerali] = useState<any>();
    const [pSpeseGenerali, setPSpeseGenerali] = useState<any>();
    const [pCassa, setPCassa] = useState<any>();
    const [cassa, setCassa] = useState<any>();
    const [totaleImponibile, setTotaleImponibile] = useState<any>();
    const [pIva, setPIva] = useState<any>();
    const [iva, setIva] = useState<any>();
    const [dSpeseEsenti, setDSpeseEsenti] = useState<any>();
    const [totale, setTotale] = useState<any>();
    const [pRitenuta, setPRitenuta] = useState<any>();
    const [ritenuta, setRitenuta] = useState<any>();
    const [totaleFattura, setTotaleFattura] = useState<any>();
    //const { modules } = useModules();

    const { t } = useTranslation("translation");

    const [toggleCalc, setToggleCalc] = useState<boolean>(false);

    useEffect(() => {
        getMountedData();
    }, []);

    const getMounted = useGetCustom("utilities/scorporo");
    const getPrintData = usePostCustom(
        "utilities/saveresultsscorporo?noTemplateVars=true"
    );
    const getMountedData = async () => {
        const { data }: any = await getMounted.doFetch(true);

        setValue("useCassaAvvocati", parseFloat(data.defaultValues.cassa));
        setValue("useRitenuta", parseFloat(data.defaultValues.ritenuta));
        setValue(
            "useSpeseGenerali",
            parseFloat(data.defaultValues.speseGenerali)
        );

        setFormData({
            ...formData,
            useCassaAvvocati: parseFloat(data.defaultValues.cassa),
            useRitenuta: parseFloat(data.defaultValues.ritenuta),
            useSpeseGenerali: parseFloat(data.defaultValues.speseGenerali),
        });
    };

    /*
     * form validation schema
     */
    const schemaResolver = yupResolver(
        yup.object().shape({
            amount: yup.string().required("Importo obbligatorio"),
            useCassaAvvocati: yup.number().nullable(),
            useRitenuta: yup.number().nullable(),
            useSpeseGenerali: yup.number().nullable(),
            speseEsenti: yup.string().nullable(),
            useSpeseGeneraliCheck: yup.string().nullable(),
            useCassaAvvocatiCheck: yup.string().nullable(),
            useRitenutaCheck: yup.string().nullable(),
            regimeSemplificato: yup.string().nullable(),
            useSplitPaymentCheck: yup.string().nullable(),
        })
    );

    const methods = useForm({ resolver: schemaResolver });
    const { control, setValue, handleSubmit } = methods;

    const handleOnChange = (name: any, value: any) => {
        setFormData({ ...formData, [name]: value });
    };

    const handleCheckboxEvent = (name: any, checked: any) => {
        setFormData({ ...formData, [name]: checked ? "on" : "" });
    };

    const calcolaScorporo = (
        amount: any,
        speseEsenti: any,
        speseEscluse: any,
        params: any
    ) => {
        var defaults: any = {
            speseGenerali: 0.15,
            cassa: 0.04,
            iva: 0.22,
            ritenuta: 0.2,
        };

        for (var i in params) {
            defaults[i] = params[i];
        }

        amount = parseFloat(amount) || 0.0;
        speseEsenti = parseFloat(speseEsenti) || 0.0;
        speseEscluse = parseFloat(speseEscluse) || 0.0;

        var pSpeseGenerali = parseFloat(defaults.speseGenerali) || 0.0;
        var pCassa = parseFloat(defaults.cassa) || 0.0;
        var pRitenuta = parseFloat(defaults.ritenuta) || 0.0;
        var pIva = parseFloat(defaults.iva) || 0.0;

        /** Program setup, nothing must be changed here ! */
        var cGenerale = 1 + pSpeseGenerali;
        var cCassa = cGenerale * pCassa;
        var cImponibile = cGenerale + cCassa;
        var cIva = cImponibile * pIva;
        var cTotale = defaults.splitPayment
            ? cGenerale + cCassa
            : cImponibile + cIva;
        var cRitenuta = cGenerale * pRitenuta;
        var cNetto = cTotale - cRitenuta;

        /** Calculations */
        amount = amount - speseEsenti - speseEscluse;

        var imponibile: any = parseFloat((amount / cNetto).toFixed(2));
        var speseGenerali: any = parseFloat(
            (imponibile * pSpeseGenerali).toFixed(2)
        );
        var cassa: any = parseFloat(
            ((imponibile + speseGenerali) * pCassa).toFixed(2)
        );
        var totaleImponibile: any = parseFloat(
            (imponibile + speseGenerali + cassa).toFixed(2)
        );
        var iva: any = parseFloat((totaleImponibile * pIva).toFixed(2));
        var totale: any = totaleImponibile + iva + speseEsenti + speseEscluse;
        var ritenuta: any = parseFloat(
            ((imponibile + speseGenerali) * pRitenuta).toFixed(2)
        );
        var totaleFattura: any = defaults.splitPayment
            ? totale - ritenuta - iva
            : totale - ritenuta;
        return {
            imponibile: imponibile,
            speseGenerali: speseGenerali,
            speseEsenti: speseEsenti,
            speseEscluse: speseEscluse,
            cassa: cassa,
            totaleImponibile: totaleImponibile,
            iva: iva,
            totale: totale,
            ritenuta: ritenuta,
            totaleFattura: totaleFattura,
        };
    };

    function toFixedFix(n: any, precisione: any) {
        var k = Math.pow(10, precisione);
        return "" + Math.round(n * k) / k;
    }

    function number_format(
        numero: any,
        decimali: any,
        dec_separatore: any,
        mig_separatore: any
    ) {
        numero = (numero + "").replace(/[^0-9\.\-]?/gi, "");
        var n = 0;
        if (isFinite(+numero)) {
            n = numero;
        }
        var precisione = 0;
        if (isFinite(+decimali) && decimali > -1) {
            precisione = decimali;
        }
        var separatore = ".";
        if (typeof mig_separatore !== "undefined") {
            separatore = mig_separatore;
        }
        var dec: any = ",";
        if (typeof dec_separatore !== "undefined") {
            dec = dec_separatore;
        }
        var s: any = "";
        if (precisione !== 0) {
            s = toFixedFix(n, precisione);
        } else {
            s = "" + Math.round(n);
        }
        s = s.split(".");
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, separatore);
        }
        if ((s[1] || "").length < precisione) {
            s[1] = s[1] || "";
            s[1] += new Array(precisione - s[1].length + 1).join("0");
        }
        return s.join(dec);
    }

    const calc = () => {
        /** Parameters to be set up */
        var pSpeseGenerali =
            parseFloat(formData.useSpeseGenerali.toString()) / 100;
        var pCassa = 0.04;
        var pIva = 0.22;
        var pRitenuta = parseFloat(formData.useRitenuta.toString()) / 100;

        /** Data from page, nothing must be changed here ! */
        var amount = formData.amount;
        var speseEsenti = formData.speseEsenti.toString();
        if (speseEsenti === "") {
            speseEsenti = "0";
        }
        var useSpeseGenerali = formData.useSpeseGeneraliCheck;
        var useCassaAvvocati = formData.useCassaAvvocatiCheck;
        var useRitenuta = formData.useRitenutaCheck;
        var regimeSemplificato = formData.regimeSemplificato;
        var splitPayment = formData.useSplitPaymentCheck;

        if (useSpeseGenerali === "") {
            pSpeseGenerali = 0;
        }

        if (useCassaAvvocati === "") {
            pCassa = 0;
        }

        if (useRitenuta === "") {
            pRitenuta = 0;
        }

        if (regimeSemplificato === "on") {
            pIva = 0;
        }

        var result = calcolaScorporo(amount, speseEsenti, 0, {
            cassa: pCassa,
            speseGenerali: pSpeseGenerali,
            iva: pIva,
            ritenuta: pRitenuta,
            splitPayment: splitPayment,
        });

        setImponibile(
            "€ " + number_format(result.imponibile.toFixed(2), 2, ",", ".")
        );
        setSpeseGenerali(
            "€ " + number_format(result.speseGenerali.toFixed(2), 2, ",", ".")
        );
        setPSpeseGenerali(
            number_format((pSpeseGenerali * 100).toFixed(2), 2, ",", ".")
        );
        setPCassa(pCassa * 100);
        setCassa("€ " + number_format(result.cassa.toFixed(2), 2, ",", "."));
        setTotaleImponibile(
            "€ " +
                number_format(result.totaleImponibile.toFixed(2), 2, ",", ".")
        );
        setPIva(pIva * 100);
        setIva("€ " + number_format(result.iva.toFixed(2), 2, ",", "."));
        setDSpeseEsenti(
            "€ " +
                number_format(parseFloat(speseEsenti).toFixed(2), 2, ",", ".")
        );
        setTotale("€ " + number_format(result.totale.toFixed(2), 2, ",", "."));
        setPRitenuta(pRitenuta * 100);
        setRitenuta(
            "€ " + number_format(result.ritenuta.toFixed(2), 2, ",", ".")
        );
        setTotaleFattura(
            "€ " + number_format(result.totaleFattura.toFixed(2), 2, ",", ".")
        );
        setToggleCalc(true);
    };

    const printButton = async () => {
        try {
            let pData = {
                filename: "scorporo",
                title: "SCORPORO DEGLI IMPORTI",
                imponibile: imponibile,
                speseGenerali: speseGenerali,
                cassa: cassa,
                totaleImponibile: totaleImponibile,
                iva: iva,
                speseEsenti: dSpeseEsenti,
                totale: totale,
                ritenuta: ritenuta,
                totaleFattura: totaleFattura,
            };
            const response: any = await getPrintData.doFetch(true, pData);
            const blob = new Blob([response.data], { type: "application/rtf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "scorporo.rtf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error("Error downloading the file:", error);
            return false;
        }
    };

    return (
        <VaporPage>
            <PageTitle
                title={"Scorporo degli importi"}
                showBackButton={false}
            />

            <VaporPage.Section>
                <p>
                    {t(
                        "Questa utilità calcola l'importo imponibile da inserire in fattura a fronte di un importo specificato come 'Netto apagare'"
                    )}
                </p>
                <form onSubmit={handleSubmit(calc)}>
                    <Box>
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    handleOnChange={handleOnChange}
                                    style={{ width: 400 }}
                                    control={control}
                                    name="amount"
                                    label="Importo €"
                                    type="number"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                />
                                <FormInput
                                    handleOnChange={handleOnChange}
                                    style={{ width: 400 }}
                                    control={control}
                                    name="speseEsenti"
                                    label="Di cui spese esenti €"
                                    type="number"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                />
                                <div>
                                    <InputLabel>
                                        {t("Spese generali")}
                                    </InputLabel>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: "row",
                                        }}
                                    >
                                        <FormInput
                                            control={control}
                                            name="useSpeseGeneraliCheck"
                                            label=""
                                            type="checkbox"
                                            handleOnChange={handleCheckboxEvent}
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            options={[
                                                {
                                                    label: "",
                                                    value: "on",
                                                },
                                            ]}
                                        />
                                        <FormInput
                                            handleOnChange={handleOnChange}
                                            style={{ width: 300 }}
                                            control={control}
                                            name="useSpeseGenerali"
                                            label=""
                                            type="number"
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            InputProps={{
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        %
                                                    </InputAdornment>
                                                ),
                                            }}
                                        />
                                    </Box>
                                </div>
                                <div>
                                    <InputLabel>
                                        {t("Cassa avvocati")}
                                    </InputLabel>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: "row",
                                        }}
                                    >
                                        <FormInput
                                            control={control}
                                            name="useCassaAvvocatiCheck"
                                            label=""
                                            type="checkbox"
                                            handleOnChange={handleCheckboxEvent}
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            options={[
                                                {
                                                    label: "",
                                                    value: "on",
                                                },
                                            ]}
                                        />
                                        <FormInput
                                            handleOnChange={handleOnChange}
                                            style={{ width: 300 }}
                                            control={control}
                                            name="useCassaAvvocati"
                                            label=""
                                            type="number"
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            InputProps={{
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        %
                                                    </InputAdornment>
                                                ),
                                            }}
                                        />
                                    </Box>
                                </div>
                                <div>
                                    <InputLabel>
                                        {t("Ritenuta d'acconto")}
                                    </InputLabel>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: "row",
                                        }}
                                    >
                                        <FormInput
                                            control={control}
                                            name="useRitenutaCheck"
                                            label=""
                                            type="checkbox"
                                            handleOnChange={handleCheckboxEvent}
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            options={[
                                                {
                                                    label: "",
                                                    value: "on",
                                                },
                                            ]}
                                        />

                                        <FormInput
                                            handleOnChange={handleOnChange}
                                            style={{ width: 300 }}
                                            control={control}
                                            name="useRitenuta"
                                            label=""
                                            type="number"
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            InputProps={{
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        %
                                                    </InputAdornment>
                                                ),
                                            }}
                                        />
                                    </Box>
                                </div>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="useSplitPaymentCheck"
                                    label=""
                                    type="checkbox"
                                    handleOnChange={handleCheckboxEvent}
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        { label: "Split payment", value: "on" },
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="regimeSemplificato"
                                    label=""
                                    type="checkbox"
                                    handleOnChange={handleCheckboxEvent}
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        {
                                            label: "Regime semplificato",
                                            value: "on",
                                        },
                                    ]}
                                />
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box>
                        <Button variant="contained" type="submit">
                            {t("Scorpora Importi")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
            {toggleCalc && (
                <VaporPage.Section>
                    <h2>Dettaglio Fattura</h2>
                    <TableContainer>
                        <Table>
                            <TableBody>
                                <TableRow>
                                    <TableCell>
                                        {t("Compensi professionali")}
                                    </TableCell>
                                    <TableCell>{imponibile}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Spese Generali")} ({pSpeseGenerali}{" "}
                                        %)
                                    </TableCell>
                                    <TableCell>{speseGenerali}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Cassa Avvocati")} ({pCassa}%)
                                    </TableCell>
                                    <TableCell>{cassa}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Totale imponibile")}
                                    </TableCell>
                                    <TableCell>{totaleImponibile}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("IVA")} ({pIva}%){" "}
                                        {t("su Imponibile")}
                                    </TableCell>
                                    <TableCell>{iva}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Spese esenti art. 10")}
                                    </TableCell>
                                    <TableCell>{dSpeseEsenti}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Totale Documento")}
                                    </TableCell>
                                    <TableCell>{totale}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>
                                        {t("Ritenuta d'acconto")}
                                        &nbsp;{pRitenuta}%
                                    </TableCell>
                                    <TableCell>{ritenuta}</TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell>{t("Netto a pagare")}</TableCell>
                                    <TableCell>{totaleFattura}</TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <Button
                        className="btn w-sm btn-info waves-effect waves-light me-1"
                        type="button"
                        variant="outlined"
                        color="primary"
                        style={{ width: 300, margin: "1em" }}
                        onClick={printButton}
                    >
                        {t("Esporta in formato Word")}
                    </Button>
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
export default Scorporo;
