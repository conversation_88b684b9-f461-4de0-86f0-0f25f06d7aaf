import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Box,
    Button,
    InputAdornment,
    List,
    ListItem,
    ListItemText,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../custom-components/FormInput";
import PageTitle from "../../../custom-components/PageTitle";
import { calculateMora } from "./interessi-calculate";
import { useEffect, useState } from "react";
import { getInterestMoraGrid } from "../../../utilities/interessimora/gridColumn";

const Interessimora = () => {
    const { t } = useTranslation();

    const [formData, setFormData] = useState<{
        interest_amount: number;
        interest_start_date: string;
        interest_end_date: string;
        before2012: string;
        agriproducts: string;
        agriproducts_value: string;
    }>({
        agriproducts_value: "2",
        interest_amount: 0,
        interest_start_date: "",
        interest_end_date: "",
        before2012: "",
        agriproducts: "",
    });
    const schema = yup.object().shape({
        interest_amount: yup.number().required(),
        interest_start_date: yup.string(),
        interest_end_date: yup.string(),
        before2012: yup.string(),
        agriproducts: yup.string(),
        agriproducts_value: yup.string(),
    });
    const [dataToDisplay, setDataToDisplay] = useState<any>(null);
    const [list, setList] = useState<any>({
        rows: [],
        loading: true,
        columns: [],
    });
    const [toggleCalc, setToggleCalc] = useState<boolean>(false);
    const [agriProductValueBoolean, setAgriProductValueBoolean] =
        useState<boolean>(false);
    const { control, handleSubmit, setValue, watch } = useForm({
        resolver: yupResolver(schema),
    });

    const agriproducts_watch = watch("agriproducts");
    const handleInputChange = (name: any, value: any) => {
        setFormData({ ...formData, [name]: value });
    };

    const handleCheckboxEvent = (name: any, checked: any) => {
        setFormData({ ...formData, [name]: checked ? "on" : "" });
    };
    useEffect(() => {
        setAgriProductValueBoolean(agriproducts_watch == "on");
    }, [agriproducts_watch]);

    const onSubmit = async () => {
        // call

        let start_date = formData.interest_start_date
            .split("-")
            .reverse()
            .join("/");
        let endDate = formData.interest_end_date.split("-").reverse().join("/");
        const newItem = calculateMora(
            start_date,
            endDate,
            "custom",
            formData.interest_amount,
            formData.agriproducts ? "on" : "",
            formData.agriproducts_value,
            formData.before2012 ? "on" : ""
        );
        setDataToDisplay(newItem);
        const columns = await getInterestMoraGrid(t);

        setList((data: any) => ({
            ...data,
            rows: newItem.tableRows,
            columns,
            loading: false,
        }));
        setToggleCalc(true);
    };

    return (
        <VaporPage>
            <PageTitle
                title={"Calcolo interessi di mora"}
                showBackButton={false}
            />

            <VaporPage.Section>
                <p>
                    {t(
                        "Questa utilità calcola gli interessi di mora previsti in caso di ritardato pagamento nelle transazioni commerciali."
                    )}
                </p>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box>
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleInputChange}
                                    name="interest_amount"
                                    label="Capitale"
                                    type="number"
                                    variant="outlined"
                                    setValue={setValue}
                                    InputProps={{
                                        endAdornment: (
                                            <InputAdornment position="start">
                                                {t("€")}
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleInputChange}
                                    name="interest_start_date"
                                    label="Data inizio"
                                    type="date"
                                    variant="outlined"
                                    setValue={setValue}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleInputChange}
                                    name="interest_end_date"
                                    label="Data fine"
                                    type="date"
                                    variant="outlined"
                                    setValue={setValue}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleCheckboxEvent}
                                    name="before2012"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "Transazione conclusa entro il 31/12/2012",
                                            value: "on",
                                        },
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleCheckboxEvent}
                                    name="agriproducts"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "Applica maggiorazione del",
                                            value: "on",
                                        },
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    formData={formData}
                                    handleOnChange={handleInputChange}
                                    name="agriproducts_value"
                                    label={
                                        !agriProductValueBoolean
                                            ? '(Art. 62, comma 3, D.L. 1/2012: "Cessione di prodotti agricoli e agroalimetari")'
                                            : ""
                                    }
                                    type="select"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "2%",
                                            value: "2",
                                        },
                                        {
                                            label: "4%",
                                            value: "4",
                                        },
                                    ]}
                                />
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box>
                        <Button variant="contained" type="submit">
                            {t("Calcola")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
            {toggleCalc && (
                <VaporPage.Section>
                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    {(list.columns || []).map(
                                        (data: any, index: number) => {
                                            return (
                                                <TableCell
                                                    key={data.name + index}
                                                >
                                                    {data.name}
                                                </TableCell>
                                            );
                                        }
                                    )}
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {(list.rows || []).map(
                                    (data: any, index: number) => {
                                        return (
                                            <TableRow key={"custom" + index}>
                                                <TableCell>{data[0]}</TableCell>
                                                <TableCell>{data[1]}</TableCell>
                                                <TableCell>{data[2]}</TableCell>
                                                <TableCell>{data[3]}</TableCell>
                                                <TableCell>{data[4]}</TableCell>
                                                <TableCell>{data[5]}</TableCell>
                                            </TableRow>
                                        );
                                    }
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <List>
                        <ListItem>
                            <ListItemText
                                primary={t("Giorni totali")}
                                secondary={dataToDisplay.totalDays}
                            />
                        </ListItem>
                        <ListItem>
                            <ListItemText
                                primary={t("Totale interessi")}
                                secondary={"€" + dataToDisplay.totalInterests}
                            />
                        </ListItem>
                        <ListItem>
                            <ListItemText
                                primary={t("Capitale + interessi")}
                                secondary={dataToDisplay.totalAmountFormatted}
                            />
                        </ListItem>
                    </List>
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
export default Interessimora;
