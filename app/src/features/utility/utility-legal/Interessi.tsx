import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Box,
    Button,
    List,
    ListItem,
    ListItemText,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../custom-components/FormInput";
import PageTitle from "../../../custom-components/PageTitle";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import usePostCustom from "../../../hooks/usePostCustom";
import moment from "moment";
import { useInteresi } from "./interessi-calculate";
import { number_format } from "../../../utilities/utils";

const Interessi = (props: { route: string }) => {
    let routeName = props.route;
    const [formData, setFormData] = useState<{
        amount: number;
        rate: number;
        startDate: string;
        endDate: string;
        capitalization: string;
    }>({
        amount: 0,
        rate: 0,
        startDate: "2020-01-01",
        endDate: moment(new Date()).format("YYYY-MM-DD"),
        capitalization: "0",
    });

    const [mode, setMode] = useState<string>("");

    const { findRates } = useInteresi();
    const [superTotalDays, setSuperTotalDays] = useState<number>(0);
    const [superTotalInterestFormatted, setSuperTotalInterestFormatted] =
        useState<number>(0);
    const [totalAmountFormatted, setTotalAmountFormatted] = useState<number>(0);
    const [toggleCalc, setToggleCalc] = useState<boolean>(false);

    const [tableRows, setTableRows] = useState<any>([]);

    const { t } = useTranslation("translation");

    useEffect(() => {
        getMountedData();
    }, []);

    const getMounted = useGetCustom("utilities/" + routeName);
    const getPrintData = usePostCustom(
        "utilities/saveresultsinteressi?noTemplateVars=true"
    );
    const getMountedData = async () => {
        const { data }: any = await getMounted.doFetch(true);

        setMode(data.mode);
    };

    const schemaResolver = yupResolver(
        yup.object().shape({
            amount: yup.string().required("Importo obbligatorio"),
        })
    );

    const methods = useForm({ resolver: schemaResolver });
    const { control, setValue, handleSubmit } = methods;

    const handleInputChange = (name: any, value: any) => {
        setFormData({ ...formData, [name]: value });
    };

    const handleCheckboxEvent = (name: any, _checked: any, value?: string) => {
        setFormData({ ...formData, [name]: value });
    };

    const calculateIntersi = async () => {
        //here
        var amount = formData.amount;
        amount = parseFloat(amount.toString());
        var rate = -1;
        if (mode == "1") {
            rate = parseFloat((formData.rate / 100).toString());
        }

        const parts: string[] = formData.startDate.split("-");
        let sday = parseInt(parts[2]);
        let smonth = parseInt(parts[1]);
        let syear = parseInt(parts[0]);
        let d1d = new Date(syear, smonth - 1, sday, 0, 0, 0);

        const endParts: string[] = formData.endDate.split("-");
        let eday = parseInt(endParts[2]);
        let emonth = parseInt(endParts[1]);
        let eyear = parseInt(endParts[0]);
        var d2d = new Date(eyear, emonth - 1, eday, 23, 59, 59);
        var oneDay = 86400000;

        var calcArray = new Array();
        var calcArrayCounter = 0;
        var counter = 1;

        var year = d1d.getFullYear();
        var prevDate = null;
        let tableRowsData: any[] = [];
        /** Create calculation array */
        if (
            parseInt(formData.capitalization) > 0 &&
            formData.capitalization !== "0"
        ) {
            while (true) {
                let dcd = new Date(year, counter - 1, 1, 0, 0, 0);
                dcd = new Date(dcd.getTime() - 1000);

                /** End of period */
                if (dcd.getTime() + 1000 > d2d.getTime()) {
                    const diff = d2d.getTime() - dcd.getTime();
                    dcd = new Date(dcd.getTime() + diff);

                    const dataArray = [prevDate, dcd];
                    calcArray[calcArrayCounter++] = dataArray;
                    break;
                }

                /** If capitalization date > start */
                if (dcd.getTime() > d1d.getTime()) {
                    const dataArray = [prevDate ? prevDate : d1d, dcd];
                    calcArray[calcArrayCounter++] = dataArray;

                    const pyear = dcd.getFullYear();
                    const pmonth = dcd.getMonth() + 1;
                    const pday = dcd.getDate();

                    prevDate = new Date(pyear, pmonth - 1, pday, 0, 0, 0);
                    prevDate = new Date(prevDate.getTime() + oneDay);
                }

                /** Adjust counter and year based on capitalization type */
                switch (formData.capitalization) {
                    case "1": // Quarter
                        counter += 3;
                        if (counter > 10) {
                            year += 1;
                            counter = 1;
                        }
                        break;
                    case "2": // Six-month
                        counter += 6;
                        if (counter > 8) {
                            year += 1;
                            counter = 1;
                        }
                        break;
                    case "3": // Year
                        year += 1;
                        counter = 1;
                        break;
                    default:
                        break;
                }
            }
        } else {
            var tmpArray = new Array();
            tmpArray[0] = d1d;
            tmpArray[1] = d2d;
            calcArray[0] = tmpArray;
        }

        var rowCounter = 0;
        var superTotalInterest = 0;
        var superTotalDays = 0;
        var firstTime = true;
        for (var i = 0; i < calcArray.length; i++) {
            var d1 = calcArray[i][0];
            var d2 = calcArray[i][1];
            var resultArray = findRates(d1, d2, oneDay, mode, rate);
            var totalInterest = 0;
            for (var j = 0; j < resultArray.length; j++) {
                var startDate = resultArray[j][0];
                var stopDate = resultArray[j][1];
                var diff = Math.round(
                    (stopDate.getTime() - startDate.getTime()) / oneDay
                );
                if (firstTime) {
                    diff -= 1;
                    firstTime = false;
                }
                superTotalDays += diff;
                rate = resultArray[j][2];
                var interest = (amount * rate * diff) / 365;
                interest = Math.round(interest * 100) / 100;
                totalInterest += interest;

                /** Print result */
                var day = startDate.getDate();
                if (day < 10) day = "0" + day;
                var month = startDate.getMonth() + 1;
                if (month < 10) month = "0" + month;
                year = startDate.getFullYear();
                var dStartDate = day + "/" + month + "/" + year;

                var day = stopDate.getDate();
                if (day < 10) day = "0" + day;
                var month = stopDate.getMonth() + 1;
                if (month < 10) month = "0" + month;
                year = stopDate.getFullYear();
                var dStopDate = day + "/" + month + "/" + year;

                var capitale = number_format(amount.toFixed(2), 2, ",", ".");
                var tasso = (Math.round(rate * 100 * 100) / 100).toFixed(2);
                var interesse = number_format(interest.toFixed(2), 2, ",", ".");

                //add into array
                tableRowsData.push({
                    dStartDate: dStartDate,
                    dStopDate: dStopDate,
                    capitale: "€" + capitale,
                    tasso: tasso + "%",
                    diff: diff,
                    interesse: "€" + interesse,
                });
                rowCounter++;
            }

            if (parseInt(formData.capitalization) > 0) {
                amount = amount + totalInterest;
            }
            superTotalInterest += totalInterest;
        }

        setTableRows(tableRowsData);

        var totalAmount;
        if (formData.capitalization == "0") {
            totalAmount = amount + superTotalInterest;
        } else {
            totalAmount = amount;
        }

        var superTotalInterestFormatted = number_format(
            (Math.round(superTotalInterest * 100) / 100).toFixed(2),
            2,
            ",",
            "."
        );
        var totalAmountFormatted = number_format(
            (Math.round(totalAmount * 100) / 100).toFixed(2),
            2,
            ",",
            "."
        );

        setSuperTotalDays(superTotalDays);
        setSuperTotalInterestFormatted(superTotalInterestFormatted);
        setTotalAmountFormatted(totalAmountFormatted);
        setToggleCalc(true);
        //final calculation
    };
    const printButton = async () => {
        try {
            let pData: any = {
                filename:
                    routeName == "interessilegali"
                        ? "interessilegali"
                        : "interessi",
                title:
                    routeName == "interessi"
                        ? "Calcolo interessi a tasso fisso"
                        : "Calcolo interessi legali",
                totalRows: tableRows.length,
                giorniTot: superTotalDays,
                interessiTot: superTotalInterestFormatted,
                capitaleInteressi: totalAmountFormatted,
            };

            tableRows.map((rdata: any, index: number) => {
                pData["dal" + (index + 1)] = rdata.dStartDate;
                pData["al" + (index + 1)] = rdata.dStopDate;
                pData["capitale" + (index + 1)] = rdata.capitale;
                pData["tasso" + (index + 1)] = rdata.tasso;
                pData["diff" + (index + 1)] = rdata.diff;
                pData["interesse" + (index + 1)] = rdata.interesse;
            });
            const response: any = await getPrintData.doFetch(true, pData);
            const blob = new Blob([response.data], { type: "application/rtf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", pData.filename + ".rtf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error("Error downloading the file:", error);
            return false;
        }
    };

    const calculateButton = async () => {
        //here
        var amount = formData.amount;
        amount = parseFloat(amount.toString());
        var rate = -1;
        if (mode == "1") {
            rate = parseFloat((formData.rate / 100).toString());
        }

        const parts: string[] = formData.startDate.split("-");
        let sday = parseInt(parts[2]);
        let smonth = parseInt(parts[1]);
        let syear = parseInt(parts[0]);
        let d1d = new Date(syear, smonth - 1, sday, 0, 0, 0);

        const endParts: string[] = formData.endDate.split("-");
        let eday = parseInt(endParts[2]);
        let emonth = parseInt(endParts[1]);
        let eyear = parseInt(endParts[0]);
        var d2d = new Date(eyear, emonth - 1, eday, 23, 59, 59);
        var oneDay = 86400000;

        var calcArray = new Array();
        var calcArrayCounter = 0;
        var counter = 1;

        var year = d1d.getFullYear();
        var prevDate = null;
        let tableRowsData: any[] = [];
        /** Create calculation array */
        if (
            formData.capitalization !== "" &&
            parseInt(formData.capitalization) > 0
        ) {
            while (true) {
                var dcd = new Date(year, counter - 1, 1, 0, 0, 0);
                dcd = new Date(dcd.getTime() - 1000);

                /** End of period */
                if (dcd.getTime() + 1000 > d2d.getTime()) {
                    var diff = d2d.getTime() - dcd.getTime();
                    dcd = new Date(dcd.getTime() + diff);
                    var dataArray = new Array();
                    dataArray[0] = prevDate;
                    dataArray[1] = dcd;
                    calcArray[calcArrayCounter++] = dataArray;
                    break;
                }

                /** If capitalization date > start */
                if (dcd.getTime() > d1d.getTime()) {
                    var dataArray = new Array();
                    if (prevDate == null) {
                        dataArray[0] = d1d;
                    } else {
                        dataArray[0] = prevDate;
                    }
                    dataArray[1] = dcd;
                    calcArray[calcArrayCounter++] = dataArray;

                    var pyear = dcd.getFullYear();
                    var pmonth = dcd.getMonth() + 1;
                    var pday = dcd.getDate();
                    prevDate = new Date(pyear, pmonth - 1, pday, 0, 0, 0);
                    prevDate = new Date(prevDate.getTime() + oneDay);
                }

                /** quarter */
                if (formData.capitalization == "1") {
                    counter = counter + 3;
                    if (counter > 10) {
                        year = year + 1;
                        counter = 1;
                    }
                } else if (formData.capitalization == "2") {
                    /** six-month */
                    counter = counter + 6;
                    if (counter > 8) {
                        year = year + 1;
                        counter = 1;
                    }
                } else if (formData.capitalization == "3") {
                    /** Year */
                    year = year + 1;
                    counter = 1;
                }
            }
        } else {
            var tmpArray = new Array();
            tmpArray[0] = d1d;
            tmpArray[1] = d2d;
            calcArray[0] = tmpArray;
        }

        var rowCounter = 0;
        var superTotalInterest = 0;
        var superTotalDays = 0;
        var firstTime = true;
        for (var i = 0; i < calcArray.length; i++) {
            var d1 = calcArray[i][0];
            var d2 = calcArray[i][1];
            var resultArray = findRates(d1, d2, oneDay, mode, rate);
            var totalInterest = 0;
            for (var j = 0; j < resultArray.length; j++) {
                var startDate = resultArray[j][0];
                var stopDate = resultArray[j][1];
                var diff = Math.round(
                    (stopDate.getTime() - startDate.getTime()) / oneDay
                );
                if (firstTime) {
                    diff -= 1;
                    firstTime = false;
                }
                superTotalDays += diff;
                rate = resultArray[j][2];
                var interest = (amount * rate * diff) / 365;
                interest = Math.round(interest * 100) / 100;
                totalInterest += interest;

                /** Print result */
                var day = startDate.getDate();
                if (day < 10) day = "0" + day;
                var month = startDate.getMonth() + 1;
                if (month < 10) month = "0" + month;
                year = startDate.getFullYear();
                var dStartDate = day + "/" + month + "/" + year;

                var day = stopDate.getDate();
                if (day < 10) day = "0" + day;
                var month = stopDate.getMonth() + 1;
                if (month < 10) month = "0" + month;
                year = stopDate.getFullYear();
                var dStopDate = day + "/" + month + "/" + year;

                var capitale = number_format(amount.toFixed(2), 2, ",", ".");
                var tasso = (Math.round(rate * 100 * 100) / 100).toFixed(2);
                var interesse = number_format(interest.toFixed(2), 2, ",", ".");

                //add into array
                tableRowsData.push({
                    dStartDate: dStartDate,
                    dStopDate: dStopDate,
                    capitale: "€" + capitale,
                    tasso: tasso + "%",
                    diff: diff,
                    interesse: "€" + interesse,
                });
                rowCounter++;
            }

            if (parseInt(formData.capitalization) > 0) {
                amount = amount + totalInterest;
            }
            superTotalInterest += totalInterest;
        }

        setTableRows(tableRowsData);
        var totalAmount;
        if (formData.capitalization == "0") {
            totalAmount = amount + superTotalInterest;
        } else {
            totalAmount = amount;
        }

        var superTotalInterestFormatted = number_format(
            (Math.round(superTotalInterest * 100) / 100).toFixed(2),
            2,
            ",",
            "."
        );
        var totalAmountFormatted = number_format(
            (Math.round(totalAmount * 100) / 100).toFixed(2),
            2,
            ",",
            "."
        );

        setSuperTotalDays(superTotalDays);
        setSuperTotalInterestFormatted(superTotalInterestFormatted);
        setTotalAmountFormatted(totalAmountFormatted);
        setToggleCalc(true);
        //final calculation
    };

    const getCalculation = () => {
        routeName != "interessilegali" ? calculateIntersi() : calculateButton();
    };
    return (
        <VaporPage>
            <PageTitle
                title={
                    routeName != "interessilegali"
                        ? "Calcolo interessi a tasso fisso"
                        : "Calcolo interessi legali"
                }
                showBackButton={false}
            />

            <VaporPage.Section>
                <p>
                    {t(
                        "Questa utilità calcola gli interessi con un tasso fisso specificato dall'utente."
                    )}
                </p>
                <p>
                    {t(
                        "L'anatocismo è consentito dal codice civile nell'art. 1283 in casi ristretti, fatta salva la possibilità di ricorrervi quando lo prevedano gli usi normativi."
                    )}
                </p>
                <p>
                    {t(
                        "Impostando la capitalizzazione ogni 3, 6 o 12 mesi i periodi di decorrenza partono da date prefissate:"
                    )}
                </p>
                <ul>
                    <li>
                        {t(
                            "Capitalizzazione trimestrale: 1 gennaio, 1 aprile, 1 luglio, 1 ottobre"
                        )}
                    </li>
                    <li>
                        {t("Capitalizzazione semestrale: 1 gennaio e 1 luglio")}
                    </li>
                    <li>{t("Capitalizzazione annuale: 1 gennaio")}</li>
                </ul>
                <form onSubmit={handleSubmit(getCalculation)}>
                    <Box>
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    handleOnChange={handleInputChange}
                                    style={{ width: 400 }}
                                    control={control}
                                    name="amount"
                                    label="Importo €"
                                    type="number"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                />
                                {routeName != "interessilegali" && (
                                    <FormInput
                                        handleOnChange={handleInputChange}
                                        style={{ width: 400 }}
                                        control={control}
                                        name="rate"
                                        label="Tasso"
                                        type="number"
                                        variant="outlined"
                                        setValue={setValue}
                                        formData={formData}
                                    />
                                )}

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="startDate"
                                    label="Data inizio"
                                    type="date"
                                    handleOnChange={handleInputChange}
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="endDate"
                                    label="Data fine"
                                    type="date"
                                    handleOnChange={handleInputChange}
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="capitalization"
                                    label=""
                                    type="checkbox"
                                    handleOnChange={handleCheckboxEvent}
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        {
                                            label: t(
                                                "Nessuna capitalizzazione"
                                            ),
                                            value: "0",
                                        },
                                        {
                                            label: t(
                                                "Trimestrale (Ogni tre mesi l'interesse maturato si aggiunge al capitale)"
                                            ),
                                            value: "1",
                                        },
                                        {
                                            label: t(
                                                "Semestrale (Ogni sei mesi l'interesse maturato si aggiunge al capitale)"
                                            ),
                                            value: "2",
                                        },
                                        {
                                            label: t(
                                                "Annuale (Ogni dodici mesi l'interesse maturato si aggiunge al capitale)"
                                            ),
                                            value: "3",
                                        },
                                    ]}
                                />
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box>
                        <Button variant="contained" type="submit">
                            {t("Calcola")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
            {toggleCalc && (
                <VaporPage.Section>
                    <Box display="flex" alignItems="right">
                        <h2>{t("Risultati")}</h2>
                    </Box>

                    <TableContainer>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>{t("Dal")}</TableCell>
                                    <TableCell>{t("al")}</TableCell>
                                    <TableCell>{t("Capitale")}</TableCell>
                                    <TableCell>{t("Tasso")}</TableCell>
                                    <TableCell>{t("Giorni")}</TableCell>
                                    <TableCell>{t("Interesse")}</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {(tableRows || []).map(
                                    (data: any, index: number) => {
                                        return (
                                            <TableRow key={"custom" + index}>
                                                <TableCell>
                                                    {data.dStartDate}
                                                </TableCell>
                                                <TableCell>
                                                    {data.dStopDate}
                                                </TableCell>
                                                <TableCell>
                                                    {data.capitale}
                                                </TableCell>
                                                <TableCell>
                                                    {data.tasso}
                                                </TableCell>
                                                <TableCell>
                                                    {data.diff}
                                                </TableCell>
                                                <TableCell>
                                                    {data.interesse}
                                                </TableCell>
                                            </TableRow>
                                        );
                                    }
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer>
                    <List>
                        <ListItem>
                            <ListItemText
                                primary={t("Giorni totali")}
                                secondary={superTotalDays}
                            />
                        </ListItem>
                        <ListItem>
                            <ListItemText
                                primary={t("Totale interessi")}
                                secondary={"€" + superTotalInterestFormatted}
                            />
                        </ListItem>
                        <ListItem>
                            <ListItemText
                                primary={t("Capitale + interessi")}
                                secondary={"€" + totalAmountFormatted}
                            />
                        </ListItem>
                    </List>
                    <Button
                        className="btn w-sm btn-info waves-effect waves-light me-1"
                        type="button"
                        variant="outlined"
                        color="primary"
                        style={{ width: 300, margin: "1em" }}
                        onClick={printButton}
                    >
                        {t("Esporta in formato Word")}
                    </Button>
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
export default Interessi;
