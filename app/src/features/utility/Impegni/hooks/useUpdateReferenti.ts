import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useUpdateReferenti = ({
    update,
    selectedRows,
    referentIds,
}: {
    update: boolean;
    selectedRows: string[];
    referentIds: string[];
}) => {
    const { doFetch, data, hasLoaded, loading } = usePostCustom(
        "calendar/update-referenti-impegni-udienze"
    );

    const formData = new FormData();

    for (let row in selectedRows) {
        formData.append(
            "rowsToUpdate[]",
            `rowChecked_Impegno_${selectedRows[row]}`
        );
    }

    for (let refId in referentIds) {
        formData.append("referents[]", referentIds[refId]);
    }

    useEffect(() => {
        if (update) {
            doFetch(true, formData);
        }
    }, [update]);

    return { data, hasLoaded, loading };
};
