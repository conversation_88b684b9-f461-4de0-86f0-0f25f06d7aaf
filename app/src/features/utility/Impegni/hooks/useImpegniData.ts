import { useTranslation } from "@1f/react-sdk";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { IList } from "../../../../interfaces/general.interfaces";
import {
    IGestioneParams,
    IGestioneResponse,
} from "../../../../interfaces/impegni.interface";
import { getGestioneGrid } from "../../../../utilities/gestione/gridColumn";
import { listDefaultData, SearchQuery } from "../constants";

export const useImpegniData = ({
    selectedRows,
    reload,
}: {
    selectedRows: string[];
    reload: boolean;
}) => {
    const { t } = useTranslation();

    const impegniDataResponse = useGetCustom(
        "calendar/gestione-list?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IGestioneParams>(SearchQuery);
    const [list, setList] = useState<IList<any>>(listDefaultData);

    useEffect(() => {
        const recalculateGrid = async () => {
            const columns = await getGestioneGrid(selectedRows, t);
            setList(prevList => ({
                ...prevList,
                columns,
            }));
        };
        recalculateGrid();
    }, [selectedRows, t]);

    useEffect(() => {
        const fetchData = async () => {
            const formattedQuery = {
                ...query,
                startDate:
                    query.startDate instanceof Date && query.startDate !== null
                        ? format(query.startDate, "dd/MM/yyyy")
                        : "",
                endDate:
                    query.endDate instanceof Date && query.endDate !== null
                        ? format(query.endDate, "dd/MM/yyyy")
                        : "",
            };

            const response = (await impegniDataResponse.doFetch(
                true,
                formattedQuery
            )) as unknown as IGestioneResponse;
            const { currentPage, totalRows } = response.data;

            setList(prevList => ({
                ...prevList,
                rows: currentPage,
                totalRows: parseInt(totalRows),
                page: query?.page,
                pageSize: query?.pageSize || 10,
            }));
        };

        if (query) {
            fetchData();
        }
    }, [query,reload]);

    return {
        SearchQuery,
        query,
        setQuery,
        list,
        setList,
        loading: impegniDataResponse.loading,
    };
};
