import { useTranslation } from "@1f/react-sdk";
import { Checkbox, FormControl, SelectChangeEvent } from "@mui/material";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import {
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    InputLabel,
    ListItemText,
    MenuItem,
    Select,
    Stack,
} from "@vapor/react-material";
import { ReactNode, useEffect, useState } from "react";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import PageTitle from "../../../custom-components/PageTitle";
import Spinner from "../../../custom-components/Spinner";
import {
    fieldName,
    SearchValue,
    User,
} from "../../../interfaces/impegni.interface";
import { ImpegniFilters } from "./ImpegniFilters";
import { useGetUsers } from "./hooks/useGetUsers";
import { useImpegniData } from "./hooks/useImpegniData";
import { useUpdateReferenti } from "./hooks/useUpdateReferenti";

export const Impegni = () => {
    const { t } = useTranslation();

    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [showConfirm, setShowConfirm] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [users, setUsers] = useState<User[]>([]);
    const [update, setUpdate] = useState(false);
    const [selectedReferentIds, setSelectedReferentIds] = useState<
        User["id"][]
    >([]);

    const [reload, setReload] = useState(false);

    const { query, setQuery, list, loading } = useImpegniData({
        selectedRows,
        reload,
    });

    const updateReferentsReponse = useUpdateReferenti({
        update: update,
        selectedRows: selectedRows,
        referentIds: selectedReferentIds,
    });

    useEffect(() => {
        if (updateReferentsReponse.loading) {
            setIsDialogOpen(false);
            setUpdate(false);
            setSelectedReferentIds([]);
            setQuery({ ...query });
            setReload(true);
        }
    }, [updateReferentsReponse.loading]);

    const getUsersResponse = useGetUsers();

    useEffect(() => {
        if (getUsersResponse.hasLoaded) {
            setUsers(getUsersResponse.data.people);
        }
    }, [getUsersResponse.hasLoaded]);

    const handleSearch = (field: fieldName) => (value: SearchValue) => {
        setQuery({
            ...query,
            [field]:
                value instanceof Date
                    ? value
                    : (value as SelectChangeEvent<unknown>).target?.value ??
                      value,
        });
    };

    const handleUserChange = (
        event: SelectChangeEvent<unknown>,
        _child: ReactNode
    ): void => {
        const newSelectedIds = event.target.value;
        setSelectedReferentIds(newSelectedIds as string[]);
    };

    const handleOpenDialog = () => {
        setIsDialogOpen(true);
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
    };

    const handleConfirmAssignment = () => {
        setUpdate(true);
        setShowConfirm(false);
    };

    const handleOpenConfirmDialog = () => {
        if (selectedReferentIds.length > 0) {
            setIsDialogOpen(false);
            setShowConfirm(true);
        }
    };

    const handleCloseConfirmDialog = () => {
        setShowConfirm(false);
    };

    const handleCheck = (id: string) => {
        const isChecked = selectedRows.includes(id);
        if (isChecked) {
            setSelectedRows(selectedRows.filter(rowId => rowId !== id));
        } else {
            setSelectedRows([...selectedRows, id]);
        }
    };

    const handlePageChange = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Gestione Impegni/Udienze")}
                showBackButton={false}
            />
            <Dialog open={showConfirm}>
                <DialogContent>
                    {t("Vuoi procedere con l'aggiornamento?")}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleConfirmAssignment}>{t("Ok")}</Button>
                    <Button onClick={handleCloseConfirmDialog}>
                        {t("Annulla")}
                    </Button>
                </DialogActions>
            </Dialog>
            <Dialog
                open={isDialogOpen}
                onClose={handleCloseDialog}>
                <DialogTitle>{t("Assegna Intestatari")}</DialogTitle>
                <Divider></Divider>
                <DialogContent>
                    <FormControl sx={{ width: 300, mt: 2 }}>
                        <InputLabel id="users-select-label">
                            {t("Intestatari")}
                        </InputLabel>
                        <Select
                            value={selectedReferentIds}
                            onChange={handleUserChange}
                            multiple
                            renderValue={(selected: any) => {
                                return users
                                    .filter(user => selected.includes(user.id))
                                    .map(user => user.nome)
                                    .join(", ");
                            }}
                            label={t("Intestatari")}>
                            {users.map(user => (
                                <MenuItem
                                    sx={{ width: "100%" }}
                                    key={user.id}
                                    value={user.id}>
                                    <Stack
                                        direction="row"
                                        alignItems="center">
                                        <Checkbox
                                            checked={selectedReferentIds.includes(
                                                user.id
                                            )}
                                        />
                                        <ListItemText primary={user.nome} />
                                    </Stack>
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>{t("Annulla")}</Button>
                    <Button onClick={handleOpenConfirmDialog}>
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
            <VaporPage.Section>
                <Stack
                    justifyContent="space-between"
                    direction="row"
                    alignItems="end">
                    <ImpegniFilters
                        handleSearchChange={handleSearch}
                        params={query}
                        setParams={setQuery}
                        users={users}
                    />
                    <Button
                        variant="outlined"
                        onClick={handleOpenDialog}>
                        {t("Assegna Intestatari")}
                    </Button>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="impegni"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page || 0}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        disableColumnMenu={true}
                        onClickKey="id"
                        onPageChangeCallback={handlePageChange}
                        onClickCallback={handleCheck}
                        setQuery={setQuery}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
