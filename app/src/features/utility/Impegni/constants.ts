import { IGestioneParams } from "../../../interfaces/impegni.interface";

export const SearchQuery: IGestioneParams = {
    page: 0,
    pageSize: 10,
    sortColumn: "tipologia",
    sortOrder: "asc",
    startDate: null,
    endDate: null,
    person: -1,
    tipologia: -1,
};

export const listDefaultData = {
    rows: [],
    columns: [],
    totalRows: 0,
    pageIndex: 0,
    pageSize: 10,
    page: 0,
}

export const TYPOLOGIES = [
    { value: "-1", label: "Tutte le tipologie" },
    { value: "Impegni", label: "Impegni" },
    { value: "Udienze", label: "Udienze" },
];

export const ALL_HOLDERS = {
    id: "-1",
    nome: "Tutti gli intestatari",
    nomeutente: "",
};
