import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../custom-components/FormInput";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import PageTitle from "../../../custom-components/PageTitle";
import { Accordion, AccordionDetails, AccordionSummary, Box, Card, CardContent, CardHeader, Typography, Grid, Button } from "@vapor/react-material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { DndContext, DragEndEvent } from "@dnd-kit/core";

import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { IList } from "../../../interfaces/general.interfaces";
import ToastNotification from "../../../custom-components/ToastNotification";
import Spinner from "../../../custom-components/Spinner";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { getQuerybuilderExecuteGrid } from "../../../utilities/querybuilder/gridColumn";

import { Draggable } from "./Draggable";
import { Droppable } from "./Droppable";
import { DroppableWhere } from "./DroppableWhere";
import { dynamicFieldId, dynamicFieldlabel, funtionnData } from "./constants ";
const initialFormData = {
    queryName: "",
    queryDesc: "",
    id: "",
    whereTypeSelection: "and"
};

export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    page: 0,
    pageSize: 10,
    sortColumn: "",
    sortOrder: ""
};

const QueryBuilderCreateUpdate = () => {
    const { id } = useParams<any>();
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [formData, setFormData] = useState<any>(initialFormData);
    const [dynamicField, setDynamicField] = useState<any>([]);
    const [exceuteQueryInput, setExceuteQueryInput] = useState<any>([]);

    const updateMountedRequest = useGetCustom(id !== undefined ? "querybuilder/view?id=" + id : "querybuilder/view");
    const excecuteQueryRequest = usePostCustom("querybuilder/executequery?noTemplateVars=true");

    const schema = yup.object().shape({
        queryName: yup.string().required(),
        queryDesc: yup.string()
    });

    useEffect(() => {
        const handleBeforeUnload = (event: BeforeUnloadEvent) => {
            // Check if there are unsaved changes (replace with your logic)
            const hasUnsavedChanges = true;

            if (hasUnsavedChanges) {
                console.log("unsaved changes");
                // Standard way to show the alert (browser will provide the message)
                event.preventDefault();
                event.returnValue = ""; // Required for Chrome and Firefox
                return ""; // For older browsers
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
        };
    }, []);

    const { control, handleSubmit, setValue } = useForm({
        resolver: yupResolver(schema)
    });
    const [open, setOpen] = useState(false);

    const handleInputChange = (name: any, value: any) => {
        setFormData({ ...formData, [name]: value });
    };
    const handleCheckboxChange = (name: any, checked: any) => {
        const inputValue = checked ? "on" : "";

        setFormData({ ...formData, [name]: inputValue });
    };
    //api list

    const saveRequest = usePostCustom("querybuilder/storequery");
    const dynamicFieldRequest = usePostCustom("querybuilder/getdynamicfields?noTemplateVars=true");

    const onSubmit = async () => {
        // call
        let result = formatDroppable();
        const fd: any = new FormData();
        fd.append("nome", formData.queryName);
        fd.append("descrizione", formData.queryDesc);
        fd.append("data", JSON.stringify(result));

        if (id !== undefined) {
            fd.append("id", id);
        }
        await saveRequest.doFetch(true, fd);
        navigate("/querybuilder");
    };

    const deleteRequest = usePostCustom(id !== undefined ? "querybuilder/delete?id=" + id : "querybuilder/delete");

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/querybuilder");
    };

    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0
    });

    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize
        });
        generateTable(exceuteQueryInput, model.page);
    };
    const onClickCallback = () => {
        return false;
    };
    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return <CustomDataGrid name="querybuilder" columns={list.columns} data={list.rows} page={defaultParams.page} totalRows={list.totalRows} pageSize={defaultParams.pageSize} loading={loading} query={defaultParams} setQuery={setDefaultParams} onPageChangeCallback={onPageChangeCallback} onClickCallback={onClickCallback} onClickKey="" />;
    };

    interface Card {
        id: string;
        title: string;
    }
    const [cards, setCards] = useState<Card[]>([]);
    const [groupCard, setGroupCard] = useState<Card[]>([]);

    const addFunctions = (item: any) => {
        setCards((cards: any) => [
            ...cards,
            {
                id: item.id + (cards.length + 1),
                title: item.label
            }
        ]);
        setDroppables((prevDroppables) => [...prevDroppables, { id: item.id + (cards.length + 1), items: [], title: item.label }]);
    };
    // dnd droppable
    type DroppableArea = {
        id: string;
        items: any;
        title: string;
    };

    const initialDroppables: DroppableArea[] = [
        { id: "droppable-select", items: [], title: "Select" },
        { id: "droppable-where", items: [], title: "Where" },
        { id: "droppable-group", items: [], title: "Group" }
    ];
    const [items, setItems] = useState<any>([]);
    const [droppables, setDroppables] = useState(initialDroppables);

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over }: any = event;
        // If dropped outside any droppable area
        if (!over) {
            return;
        }

        // Check if dragging from the source list
        let id: any = active.id;
        let mainKey = id.split("-")[0];
        let idKey = id.split("-")[1];
        const sourceItem: any = Object.keys(items).reduce((found, key) => {
            if (mainKey === key) {
                return items[key]["fields"].find((item: any) => item.id === idKey) || found;
            }
            return found;
        }, null);

        if (sourceItem) {
            // Add item to the droppable area
            if (over.id !== "droppable-where" && over.id !== "droppable-group" && !over.id.includes("custom")) {
                setDroppables((prev) => {
                    let itemExists = false;
                    let addingMoreThanOne = false;
                    const newState = prev.map((area) => {
                        if (area.id === over.id) {
                            const shouldAllowSingleItem = ["Somma", "Massimo", "Minimo", "Media"].includes(area.title) && area.items.length === 0;
                            const shouldAddItem = area.items.some((item: any) => item.id === sourceItem.id) ? ((itemExists = true), area.items) : shouldAllowSingleItem || area.title === "Concatena" || area.title === "Conta" || area.title === "Select" ? [...area.items, sourceItem] : ((addingMoreThanOne = true), area.items); // Set flag if adding more than one item
                            return {
                                ...area,
                                items: shouldAddItem
                            };
                        }
                        return area;
                    });
                    if (itemExists) {
                        setShowErrorMessage(true);
                        setErrorMessage(t("L'elemento già presente nella lista"));
                    }
                    if (addingMoreThanOne) {
                        setShowErrorMessage(true);
                        setErrorMessage(t("Per questa funzione non è possibile aggiungere più di un campo"));
                    }
                    return newState;
                });
            } else {
                setDroppables((prev) => {
                    const newState = prev.map((area) =>
                        area.id === over.id
                            ? {
                                  ...area,
                                  items: [...area.items, sourceItem]
                              }
                            : area
                    );
                    return newState;
                });
            }
        }
    };

    const handleRemoveItem = (droppableId: string, itemId: string) => {
        if (itemId !== "card")
            setDroppables((prev) =>
                prev.map((droppable) =>
                    droppable.id === droppableId
                        ? {
                              ...droppable,
                              items: droppable.items.filter((item: any) => {
                                  return item.id !== itemId;
                              })
                          }
                        : droppable
                )
            );
        else {
            setDroppables((prev) => prev.filter((droppable) => droppable.id !== droppableId));
            if (droppableId.includes("custom")) setGroupCard((prev) => prev.filter((card) => card.id !== droppableId));
            else setCards((prev) => prev.filter((card) => card.id !== droppableId));
        }
    };

    const getTitleById = (matchId: any) => {
        const result = funtionnData.find((element: any) => {
            return matchId.includes(element.id);
        });
        return result ? result.label : null;
    };

    const getIdById = (matchId: any) => {
        const result = funtionnData.find((element: any) => {
            return matchId.includes(element.id);
        });
        return result ? result.id : null;
    };
    const formatDroppable = () => {
        let selectData: any = [];
        let fieldsData: any = [];
        let groupByData: any = [];
        let whereData: any = [];
        let filterData: any = [];
        let filterGroupData: any = [];
        let id = 1;

        Object.keys(droppables).map((index: any) => {
            let subfilterGroupData: any = [];
            let item = droppables[index];
            if (item.id == "droppable-select") {
                droppables[index].items.map((subitem: any) => {
                    selectData.push({
                        id: subitem.id,
                        fieldType: "selectField",
                        fields: [subitem.alias]
                    });
                });
            } else if (item.id.includes("concat") || item.id.includes("count") || item.id.includes("sum") || item.id.includes("max") || item.id.includes("min") || item.id.includes("avg")) {
                droppables[index].items.map((subitem: any) => {
                    fieldsData.push(subitem.alias);
                });
                selectData.push({
                    id: index,
                    fieldType: "functionField",
                    fields: [fieldsData],
                    name: getIdById(item.id),
                    label: getTitleById(item.id),
                    multiple: fieldsData.length > 1 ? true : false,
                    param: ","
                });
                fieldsData = [];
            } else if (item.id == "droppable-group") {
                droppables[index].items.map((subitem: any) => {
                    groupByData.push(subitem.alias);
                });
            } else if (item.id == "droppable-where" || item.id.includes("custom-")) {
                droppables[index].items.map((subitem: any) => {
                    let opcode = "=";
                    let value = "";
                    let param = false;
                    if (formData["checkbox" + subitem.id] !== undefined) {
                        param = true;
                    } else if (formData["select" + subitem.id] !== undefined) {
                        opcode = formData["select" + subitem.id];
                        if (formData["whereValue" + subitem.id] !== undefined) {
                            value = formData["whereValue" + subitem.id];
                        }
                    }
                    let op = opcode;
                    if (subitem.type === "text" || subitem.type === "regex") {
                        if (opcode === "=") value = value; // esattamente uguale
                        if (opcode === "%%") value = "%" + value + "%"; // contiene
                        if (opcode === "%*") value = value + "%"; // inizia per
                        if (opcode === "*%") value = "%" + value; // finisce per
                        op = opcode === "%%" || opcode === "%*" || opcode === "*%" ? "LIKE" : op;
                    }
                    if (subitem.id === "dynamicField") {
                        value = formData["select-dynamicField"];
                        filterData.push({
                            id: id,
                            param: param,
                            op: op,
                            isDynamic: subitem.id === "dynamicField",
                            isJudicial: subitem.id === "judicialField",
                            table: "an",
                            value: value,
                            alias: "1",
                            opcode: opcode
                        });
                    } else {
                        whereData.push(subitem.alias);
                        filterData.push({
                            id: id,
                            param: param,
                            op: op,
                            isDynamic: subitem.id === "dynamicField",
                            isJudicial: subitem.id === "judicialField",
                            value: value,
                            alias: subitem.alias,
                            opcode: opcode
                        });
                    }

                    if (item.id.includes("custom-")) {
                        subfilterGroupData.push(id);
                    } else {
                        filterGroupData.push(id);
                    }

                    id++;
                });
                if (subfilterGroupData.length > 0) {
                    const resultsubfilterGroupData = subfilterGroupData.reduce((acc: any, item: any, index: any) => {
                        acc.push(item); // Push the current item
                        if (index < subfilterGroupData.length - 1) {
                            acc.push(formData.whereTypeSelection); // Add "AND" unless it's the last item
                        }
                        return acc;
                    }, []);
                    filterGroupData.push(resultsubfilterGroupData);
                    subfilterGroupData = [];
                }
            }
        });
        const filterGroupDataFinal = filterGroupData.reduce((acc: any, item: any, index: any) => {
            acc.push(item); // Push the current item
            if (index < filterGroupData.length - 1) {
                acc.push(formData.whereTypeSelection); // Add "AND" unless it's the last item
            }
            return acc;
        }, []);

        setExceuteQueryInput({
            selects: selectData,
            filter: filterData,
            filterGroup: filterGroupDataFinal,
            whereFields: whereData,
            groupBy: groupByData
        });
        return {
            selects: selectData,
            filter: filterData,
            filterGroup: filterGroupDataFinal,
            whereFields: whereData,
            groupBy: groupByData
        };
    };

    const getSubArrayByAlias = (arr: any, alias: any) => {
        return arr.filter((item: any) => item.alias === alias);
    };
    const findalias = (alias: string, items: any) => {
        let data: any = {};
        Object.keys(items).map((key: any) => {
            let sub = getSubArrayByAlias(items[key]["fields"], alias);
            if (sub.length > 0) {
                data = sub[0];
                return;
            }
        });
        return data;
    };

    const formatQueryData = (queryData: any, items: any) => {
        console.log("queryData", queryData);
        const newDroppables = [...initialDroppables]; // Start with the initial droppables structure
        let computedFormData: any = [];
        let cardLength = cards.length + 1;
        // Process "selects"
        if (queryData.selects) {
            queryData.selects.forEach((subitem: any) => {
                if (subitem.fieldType === "selectField") {
                    subitem.fields.forEach((aliasItem: any) => {
                        const aliasResult = findalias(aliasItem, items);
                        newDroppables.forEach((area) => {
                            if (area.id === "droppable-select") {
                                area.items.push(aliasResult);
                            }
                        });
                    });
                } else if (subitem.fieldType === "functionField") {
                    const cardId = `${subitem.name}${cardLength++}`;
                    addFunctions({
                        id: subitem.name,
                        label: subitem.label
                    });
                    let functionDroppable: any = {};
                    if (subitem.name === "concat") {
                        computedFormData.push({
                            [`separator${cardId}`]: subitem.param
                        });
                        if (typeof subitem.fields[0] !== "string")
                            functionDroppable = {
                                id: cardId,
                                items: subitem.fields[0].map((aliasItem: any) => findalias(aliasItem, items)),
                                title: subitem.label
                            };
                        else {
                            functionDroppable = {
                                id: cardId,
                                items: subitem.fields.map((aliasItem: any) => findalias(aliasItem, items)),
                                title: subitem.label
                            };
                        }
                    } else {
                        functionDroppable = {
                            id: cardId,
                            items: subitem.fields.map((aliasItem: any) => findalias(aliasItem, items)),
                            title: subitem.label
                        };
                    }
                    newDroppables.push(functionDroppable);
                }
            });
        }

        // Process "groupBy"
        if (queryData.groupBy) {
            queryData.groupBy.forEach((subitem: any) => {
                const aliasResult = findalias(subitem, items);
                newDroppables.forEach((area) => {
                    if (area.id === "droppable-group") {
                        area.items.push(aliasResult);
                    }
                });
            });
        }

        // Process "filterGroup"
        const processFilterGroup = (filterGroup: any, parentId: string, items: any) => {
            filterGroup.forEach((subitem: any) => {
                if (Array.isArray(subitem)) {
                    const groupId = `custom-and-${newDroppables.length + 1}`;

                    setGroupCard((groupCard: any) => [
                        ...groupCard,
                        {
                            id: groupId,
                            title: "Gruppo"
                        }
                    ]);
                    const subWhereDroppable = {
                        id: groupId,
                        items: [],
                        title: "Gruppo"
                    };
                    newDroppables.push(subWhereDroppable);
                    processFilterGroup(subitem, groupId, items);
                } else if (typeof subitem === "number") {
                    const aliasResult = findalias(queryData.whereFields[subitem - 1], items);
                    if (aliasResult) {
                        newDroppables.forEach((area) => {
                            if (area.id === parentId) {
                                area.items.push(aliasResult);
                            }
                        });
                        computedFormData.push({
                            [`whereTypeSelection${aliasResult.id}`]: "and"
                        });
                        if (queryData.filter[subitem - 1].param) {
                            console.log("here");
                            computedFormData.push({
                                [`checkbox${aliasResult.id}`]: "on"
                            });
                        } else {
                            computedFormData.push({
                                [`select${aliasResult.id}`]: queryData.filter[subitem - 1].opcode
                            });
                            if (queryData.filter[subitem - 1].opcode === "BETWEEN") {
                                let split = queryData.filter[subitem - 1].value.split("|");
                                computedFormData.push({
                                    [`whereValue${aliasResult.id}`]: split[0],
                                    [`whereValueTo${aliasResult.id}`]: split[1]
                                });
                            } else {
                                computedFormData.push({
                                    [`whereValue${aliasResult.id}`]: queryData.filter[subitem - 1].value
                                });
                            }
                        }
                    }
                }
            });
        };

        if (queryData.filterGroup) {
            processFilterGroup(queryData.filterGroup, "droppable-where", items);
        }
        setDroppables(newDroppables); // Update the droppables state
        return computedFormData;
    };

    const formatSelectOption = async (value: string = "id", label: string = "name") => {
        let { data }: any = await dynamicFieldRequest.doFetch(true);
        let optionFormatted: any = [
            {
                value: "",
                label: "Seleziona campo",
                disabled: true
            }
        ];
        (data || []).map((ref: any) => {
            return optionFormatted.push({
                value: ref[value],
                label: ref[label]
            });
        });
        setDynamicField(optionFormatted);
    };

    useEffect(() => {
        async function initData() {
            try {
                formatSelectOption("id", "nome");

                let { data }: any = await updateMountedRequest.doFetch(true);
                let appendedData = data.fields;
                let newRecord = {
                    id: dynamicFieldId,
                    label: dynamicFieldlabel
                };
                Object.keys(appendedData).forEach((key: any) => {
                    if (key === "Anagrafiche") {
                        appendedData[key]["fields"].push(newRecord);
                    }
                });
                setItems(appendedData);
                if (id !== undefined) {
                    let queryData = JSON.parse(data.queryData);
                    let computedData = formatQueryData(queryData, data.fields);
                    setFormData(() => {
                        const updatedMacro: any = {};
                        updatedMacro.queryName = data.name;
                        updatedMacro.queryDesc = data.description ?? "";
                        updatedMacro.whereTypeSelection = "and";
                        setValue("queryName", data.name);
                        setValue("queryDesc", data.description ?? "");
                        computedData.map((item: any) => {
                            for (const key in item) {
                                updatedMacro[key] = item[key];
                            }
                            ``;
                        });
                        return updatedMacro;
                    });
                }
            } catch (_error) {
                return;
            }
        }

        initData();
    }, []);

    const executeQuery = async () => {
        setLoading(true);
        let result = formatDroppable();

        generateTable(result);
    };

    const generateTable = async (result: any, page: any = 0) => {
        let queryData = {
            ignoreParam: true,
            pageSize: defaultParams.pageSize,
            sortColumn: defaultParams.sortColumn,
            sortOrder: defaultParams.sortOrder,
            data: JSON.stringify(result),
            page: page
        };
        let { data }: any = await excecuteQueryRequest.doFetch(true, queryData);
        setLoading(false);

        const finalColumns: any = await getQuerybuilderExecuteGrid(Object.keys(data.currentPage[0]));
        setList({
            rows: data.currentPage,
            columns: finalColumns,
            totalRows: data.totalRows,
            pageIndex: 0,
            pageSize: 10,
            page: 0
        });
    };

    const addAndOrSection = (itemId?: any) => {
        if (itemId !== undefined) {
            // add inner draggable item
            setDroppables((prev) => {
                const newState = prev.map((area) =>
                    area.id === itemId
                        ? {
                              ...area,
                              items: [
                                  ...area.items,
                                  {
                                      id: "subelement-" + area.items.length,
                                      label: "Gruppo"
                                  }
                              ]
                          }
                        : area
                );
                return newState;
            });
        } else {
            setGroupCard((groupCard: any) => [
                ...groupCard,
                {
                    id: "custom-" + formData.whereTypeSelection + "-" + (groupCard.length + 1),
                    title: "Gruppo"
                }
            ]);
            setDroppables((prevDroppables) => [
                ...prevDroppables,
                {
                    id: "custom-" + formData.whereTypeSelection + "-" + (groupCard.length + 1),
                    items: [],
                    title: "Gruppo"
                }
            ]);
        }
    };

    const resetForm = () => {
        setFormData(() => {
            const updatedMacro: any = {};
            updatedMacro.queryName = formData.name;
            updatedMacro.queryDesc = formData.description;
            updatedMacro.id = formData.id;
            return updatedMacro;
        });
        setDroppables(initialDroppables);
        setCards([]);
        setGroupCard([]);
        setList({
            rows: [],
            columns: [],
            totalRows: 0,
            pageIndex: 0,
            pageSize: 10,
            page: 0
        });
    };
    return (
        <VaporPage>
            <ToastNotification showNotification={showErrorMessage} setShowNotification={setShowErrorMessage} severity="error" text={errorMessage} />
            <form onSubmit={handleSubmit(onSubmit)}>
                <PageTitle
                    title={(id !== undefined ? "Modifica " : `Nuovo `) + "Query Builder"}
                    pathToPrevPage={"/querybuilder"}
                    actionButtons={[
                        {
                            label: "Esegui",
                            onclick: executeQuery,
                            color: "secondary"
                        },
                        {
                            label: "Svuota campi",
                            onclick: resetForm,
                            color: "error"
                        },
                        <Button type="submit" variant="outlined">
                            {t("Salva")}
                        </Button>
                    ]}
                />
                <ConfirmModal open={open} handleDecline={handleDecline} handleAgree={handleAgree} decline={"No"} agree={"Yes, Delete"} confirmText={"Are you sure want to Delete"} title={"Delete Action"} />
                <VaporPage.Section>
                    <DndContext onDragEnd={handleDragEnd}>
                        <Box
                            sx={{
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: "100%"
                                }
                            }}
                        >
                            <Grid container>
                                <Grid item xs={3} direction="column">
                                    <FormInput style={{ width: 250 }} control={control} name="queryName" label="Nome query" type="text" variant="outlined" setValue={setValue} formData={formData} handleOnChange={handleInputChange} />
                                </Grid>
                                <Grid item xs={3} direction="column">
                                    <FormInput style={{ width: 250 }} control={control} name="queryDesc" label="Descrizione" type="text" variant="outlined" setValue={setValue} formData={formData} handleOnChange={handleInputChange} />
                                </Grid>
                            </Grid>
                            <Grid container spacing={2} direction="row" alignItems="flex-start">
                                <Grid item xs={12} sm={6} md={3}>
                                    <Card>
                                        <CardContent>
                                            {Object.keys(items).map((key: any) => {
                                                return (
                                                    <Accordion key={key}>
                                                        <AccordionSummary expandIcon={<ExpandMoreIcon />} aria-controls="panel1-content" id="panel1-header">
                                                            <Typography variant="body500" gutterBottom component="div" color="primary.interactiveDefault">
                                                                {key}
                                                            </Typography>
                                                        </AccordionSummary>
                                                        <AccordionDetails
                                                            sx={{
                                                                padding: "0",
                                                                height: "200px",
                                                                overflowY: "auto"
                                                            }}
                                                        >
                                                            {items[key]["fields"].map((item: any) => (
                                                                <Draggable key={key + "-" + item.id} id={key + "-" + item.id} content={item.label} />
                                                            ))}
                                                        </AccordionDetails>
                                                    </Accordion>
                                                );
                                            })}
                                        </CardContent>
                                    </Card>
                                </Grid>
                                <Grid item xs={12} sm={6} md={9}>
                                    <Grid container spacing={2} direction="row" alignItems="flex-start">
                                        {droppables.map(
                                            (droppable: any, key: number) =>
                                                key < 3 && (
                                                    <>
                                                        <Grid item xs={12} sm={6} md={droppable.id == "droppable-where" || droppable.id == "droppable-select" ? 8 : 4}>
                                                            <Card>
                                                                <CardHeader
                                                                    title={droppable.title}
                                                                    action={
                                                                        droppable.id == "droppable-where" && (
                                                                            <Box
                                                                                sx={{
                                                                                    display: "flex"
                                                                                }}
                                                                            >
                                                                                <FormInput
                                                                                    control={control}
                                                                                    setValue={setValue}
                                                                                    label=""
                                                                                    name={"whereTypeSelection"}
                                                                                    formData={formData}
                                                                                    handleOnChange={handleInputChange}
                                                                                    type="select"
                                                                                    value={formData.whereTypeSelection}
                                                                                    options={[
                                                                                        {
                                                                                            value: "and",
                                                                                            label: "And"
                                                                                        },
                                                                                        {
                                                                                            value: "or",
                                                                                            label: "Or"
                                                                                        }
                                                                                    ]}
                                                                                />
                                                                                <Button onClick={() => addAndOrSection()} variant="outlined">
                                                                                    <AddIcon />
                                                                                </Button>
                                                                            </Box>
                                                                        )
                                                                    }
                                                                />

                                                                <CardContent>
                                                                    {droppable.id == "droppable-where" ? <DroppableWhere handleCheckboxChange={handleCheckboxChange} handleOnChange={handleInputChange} addAndOrSection={addAndOrSection} formData={formData} key={droppable.id} id={droppable.id} items={droppable.items} title={droppable.title} onRemoveItem={handleRemoveItem} control={control} setValue={setValue} dynamicData={dynamicField} /> : <Droppable handleOnChange={handleInputChange} formData={formData} key={droppable.id} id={droppable.id} items={droppable.items} title={droppable.title} onRemoveItem={handleRemoveItem} dynamicData={dynamicField} control={control} setValue={setValue} />}
                                                                    <Box
                                                                        sx={{
                                                                            display: "flex",
                                                                            flexWrap: "wrap"
                                                                        }}
                                                                    >
                                                                        {droppable.id == "droppable-select" &&
                                                                            cards.map((card: any, key: number) => (
                                                                                <Card
                                                                                    sx={{
                                                                                        mt: "1em",
                                                                                        ml: "0.5em",
                                                                                        pb: "0px",
                                                                                        width: 250
                                                                                    }}
                                                                                    key={card.title + "-" + key}
                                                                                >
                                                                                    <CardContent>
                                                                                        <Box
                                                                                            sx={{
                                                                                                display: "flex"
                                                                                            }}
                                                                                        >
                                                                                            <Typography
                                                                                                sx={{
                                                                                                    maxWidth: 100,
                                                                                                    wordWrap: "break-word",
                                                                                                    alignContent: "center",
                                                                                                    alignItems: "center"
                                                                                                }}
                                                                                                variant="body500"
                                                                                                component="div"
                                                                                                color="primary.interactiveDefault"
                                                                                            >
                                                                                                {card.title}
                                                                                            </Typography>
                                                                                            <Button
                                                                                                sx={{
                                                                                                    ml: "auto"
                                                                                                }}
                                                                                                size="small"
                                                                                                variant="outlined"
                                                                                                type="button"
                                                                                                onClick={() => handleRemoveItem(card.id, "card")}
                                                                                            >
                                                                                                <RemoveIcon />
                                                                                            </Button>
                                                                                        </Box>
                                                                                        {card.title === "Concatena" && (
                                                                                            <FormInput
                                                                                                defaultValue={","}
                                                                                                style={{
                                                                                                    width: 200
                                                                                                }}
                                                                                                name={"separator" + card.id}
                                                                                                label={t("separatore")}
                                                                                                control={control}
                                                                                                formData={formData}
                                                                                                handleOnChange={handleInputChange}
                                                                                            />
                                                                                        )}
                                                                                        <Droppable handleOnChange={handleInputChange} formData={formData} key={card.id} id={card.id} items={droppables[droppables.findIndex((droppable) => droppable.id === card.id)] !== undefined ? droppables[droppables.findIndex((droppable) => droppable.id === card.id)].items : []} title={droppable.title + "-" + card.title} onRemoveItem={handleRemoveItem} dynamicData={dynamicField} control={control} setValue={setValue} />
                                                                                    </CardContent>
                                                                                </Card>
                                                                            ))}
                                                                        {droppable.id == "droppable-where" && (
                                                                            <Box
                                                                                sx={{
                                                                                    display: "flex",
                                                                                    flexWrap: "wrap",
                                                                                    borderRadius: 1
                                                                                }}
                                                                            >
                                                                                {groupCard.map((card: any, key: number) => (
                                                                                    <Card
                                                                                        sx={{
                                                                                            mt: "1em",
                                                                                            ml: "0.5em",
                                                                                            pb: "0px"
                                                                                        }}
                                                                                        key={card.title + "-" + key}
                                                                                    >
                                                                                        <CardContent>
                                                                                            <Box
                                                                                                sx={{
                                                                                                    display: "flex",
                                                                                                    width: "250px"
                                                                                                }}
                                                                                            >
                                                                                                <Typography
                                                                                                    sx={{
                                                                                                        maxWidth: 100,
                                                                                                        wordWrap: "break-word",
                                                                                                        alignContent: "center",
                                                                                                        alignItems: "center"
                                                                                                    }}
                                                                                                    variant="body500"
                                                                                                    component="div"
                                                                                                    color="primary.interactiveDefault"
                                                                                                >
                                                                                                    {card.title}
                                                                                                </Typography>

                                                                                                <FormInput
                                                                                                    size="small"
                                                                                                    sx={{
                                                                                                        marginLeft: "0.5em"
                                                                                                    }}
                                                                                                    control={control}
                                                                                                    setValue={setValue}
                                                                                                    label=""
                                                                                                    name={"whereTypeSelection"}
                                                                                                    formData={formData}
                                                                                                    handleOnChange={handleInputChange}
                                                                                                    type="select"
                                                                                                    value={formData.whereTypeSelection}
                                                                                                    options={[
                                                                                                        {
                                                                                                            value: "and",
                                                                                                            label: "And"
                                                                                                        },
                                                                                                        {
                                                                                                            value: "or",
                                                                                                            label: "Or"
                                                                                                        }
                                                                                                    ]}
                                                                                                />
                                                                                                <Button
                                                                                                    sx={{
                                                                                                        ml: "auto",
                                                                                                        marginRight: "0.5em"
                                                                                                    }}
                                                                                                    onClick={() => addAndOrSection(card.id)}
                                                                                                    size="small"
                                                                                                    variant="outlined"
                                                                                                >
                                                                                                    <AddIcon />
                                                                                                </Button>

                                                                                                <Button
                                                                                                    sx={{
                                                                                                        ml: "auto",
                                                                                                        marginRight: "0.5em"
                                                                                                    }}
                                                                                                    size="small"
                                                                                                    variant="outlined"
                                                                                                    color="error"
                                                                                                    type="button"
                                                                                                    onClick={() => handleRemoveItem(card.id, "card")}
                                                                                                >
                                                                                                    <RemoveIcon />
                                                                                                </Button>
                                                                                            </Box>
                                                                                            <DroppableWhere handleCheckboxChange={handleCheckboxChange} handleOnChange={handleInputChange} addAndOrSection={addAndOrSection} formData={formData} key={card.id} id={card.id} control={control} setValue={setValue} items={droppables[droppables.findIndex((droppable) => droppable.id === card.id)] !== undefined ? droppables[droppables.findIndex((droppable) => droppable.id === card.id)].items : []} title={droppable.title + "-" + card.title} onRemoveItem={handleRemoveItem} dynamicData={dynamicField} />
                                                                                        </CardContent>
                                                                                    </Card>
                                                                                ))}
                                                                            </Box>
                                                                        )}
                                                                    </Box>
                                                                </CardContent>
                                                            </Card>
                                                        </Grid>
                                                        {key == 0 ? (
                                                            <Grid item xs={12} sm={6} md={4}>
                                                                <Card>
                                                                    <CardHeader title={t("Funzioni")} />
                                                                    <CardContent>
                                                                        {(funtionnData || []).map((item: any) => (
                                                                            <Button
                                                                                sx={{
                                                                                    margin: "0.5em"
                                                                                }}
                                                                                variant="outlined"
                                                                                onClick={() => addFunctions(item)}
                                                                            >
                                                                                {item.label}
                                                                            </Button>
                                                                        ))}
                                                                    </CardContent>
                                                                </Card>
                                                            </Grid>
                                                        ) : (
                                                            ""
                                                        )}
                                                    </>
                                                )
                                        )}
                                        <Grid item xs={12} sm={6} md={12}>
                                            <Card>
                                                <CardHeader title={`Executable Result`} />
                                                <CardContent>{renderDataTable()}</CardContent>
                                            </Card>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Box>
                    </DndContext>
                </VaporPage.Section>
            </form>
        </VaporPage>
    );
};

export default QueryBuilderCreateUpdate;
