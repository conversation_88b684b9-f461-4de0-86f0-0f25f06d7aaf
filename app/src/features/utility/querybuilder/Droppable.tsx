import { useDroppable } from "@dnd-kit/core";
import { Box, Card, CardContent, Typography, Button } from "@vapor/react-material";
import FormInput from "../../../custom-components/FormInput";
import RemoveIcon from "@mui/icons-material/Remove";
import { dynamicFieldId } from "./constants ";
// Droppable Component
export const Droppable: React.FC<{
    id: string;
    items: any;
    title: string;
    dynamicData: any;
    control: any;
    setValue: any;
    onRemoveItem: (droppableId: string, itemId: string) => void;
    formData: any;
    handleOnChange: any;
}> = ({ id, title, items, onRemoveItem, dynamicData, control, setValue, formData, handleOnChange }) => {
    const { setNodeRef, isOver } = useDroppable({
        id
    });
    const style = {
        border: isOver ? "2px dotted #4caf50" : "2px solid transparent",
        backgroundColor: isOver ? "#f0fff4" : "#fff",
        transition: "border 0.2s ease, background-color 0.2s ease",
        display: "flex",
        flexWrap: "wrap",
        borderRadius: "8px", // Rounded corners for the border
        padding: "16px", // Add padding inside the box
        boxSizing: "border-box" // Ensure padding is included within the border
    };
    return (
        <Box sx={style} ref={setNodeRef} key={title}>
            {items.map((item: any) => (
                <Card
                    key={item.id}
                    sx={{
                        mt: "1em",
                        ml: "0.5em",
                        minWidth: "30%",
                        pb: "0px"
                    }}
                >
                    <CardContent>
                        <Box
                            sx={{
                                display: "flex",
                                width: "150px"
                            }}
                        >
                            <Typography
                                sx={{
                                    maxWidth: 100,
                                    wordWrap: "break-word",
                                    alignContent: "center",
                                    alignItems: "center"
                                }}
                                variant="body500"
                                component="div"
                                color="primary.interactiveDefault"
                            >
                                {item.label}
                            </Typography>
                            <Button sx={{ ml: "auto" }} size="small" variant="outlined" type="button" onClick={() => onRemoveItem(id, item.id)} color="error">
                                <RemoveIcon />
                            </Button>
                        </Box>
                        {item.id == dynamicFieldId && <FormInput style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={dynamicData} />}
                    </CardContent>
                </Card>
            ))}
        </Box>
    );
};
