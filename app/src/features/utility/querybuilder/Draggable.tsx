import { useDraggable } from "@dnd-kit/core";
import { Card, CardHeader } from "@vapor/react-material";

// Draggable Component
export const Draggable: React.FC<{
    id: string;
    content: string;
}> = ({ id, content }) => {
    const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
        id
    });

    const style: React.CSSProperties = {
        zIndex: isDragging ? 10000 : "auto",
        transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
        position: "relative", // Ensure the dragged item is positioned correctly
        marginBottom: "10px",
        cursor: "grab"
    };

    return (
        <Card ref={setNodeRef} {...listeners} {...attributes} style={style}>
            <CardHeader subheader={content} />
        </Card>
    );
};
