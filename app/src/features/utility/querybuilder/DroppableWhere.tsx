import { useDroppable } from "@dnd-kit/core";
import { <PERSON>, Button, Card, CardContent, Typography } from "@vapor/react-material";
import RemoveIcon from "@mui/icons-material/Remove";
import FormInput from "../../../custom-components/FormInput";
import AddIcon from "@mui/icons-material/Add";
import React from "react";
import { dynamicFieldId } from "./constants ";

// DroppableWhere Component
export const DroppableWhere: React.FC<any> = ({ id, title, items, onRemoveItem, dynamicData, control, setValue, addAndOrSection, formData, handleOnChange, handleCheckboxChange }) => {
    const { setNodeRef, isOver } = useDroppable({ id });
    const style = {
        border: isOver ? "2px dotted #4caf50" : "2px solid transparent",
        backgroundColor: isOver ? "#f0fff4" : "#fff",
        transition: "border 0.2s ease, background-color 0.2s ease",
        display: "flex",
        flexWrap: "wrap",
        borderRadius: "8px", // Rounded corners for the border
        padding: "16px", // Add padding inside the box
        boxSizing: "border-box" // Ensure padding is included within the border
    };
    return (
        <Box sx={style} ref={setNodeRef} key={title}>
            {items.map((item: any) => {
                return (
                    <Card key={item.id} sx={{ mt: "1em", ml: "0.5em", pb: "0px" }}>
                        <CardContent>
                            <Box sx={{ display: "flex" }}>
                                <Typography
                                    sx={{
                                        maxWidth: 100,
                                        wordWrap: "break-word",
                                        marginRight: "0.5em"
                                    }}
                                    variant="body500"
                                    component="div"
                                    color="primary.interactiveDefault"
                                >
                                    {item.label}
                                </Typography>

                                {item.label == "Gruppo" && (
                                    <>
                                        <FormInput
                                            size="small"
                                            sx={{
                                                marginRight: "0.5em",
                                                width: "70px"
                                            }}
                                            control={control}
                                            setValue={setValue}
                                            name={"whereTypeSelection" + id}
                                            formData={formData}
                                            handleOnChange={handleOnChange}
                                            type="select"
                                            value={`${formData.whereTypeSelection}${id}`}
                                            options={[
                                                { value: "and", label: "And" },
                                                { value: "or", label: "Or" }
                                            ]}
                                        />
                                        <Button
                                            sx={{
                                                ml: "auto",
                                                marginRight: "0.5em"
                                            }}
                                            onClick={() => addAndOrSection(id)}
                                            size="small"
                                            variant="outlined"
                                        >
                                            <AddIcon />
                                        </Button>
                                    </>
                                )}
                                <Button sx={{ ml: "auto" }} size="small" variant="outlined" type="button" onClick={() => onRemoveItem(id, item.id)} color="error">
                                    <RemoveIcon />
                                </Button>
                            </Box>

                            {item.label !== "Gruppo" ? item.id == dynamicFieldId ? <FormInput style={{ width: 200 }} control={control} setValue={setValue} name={"select-" + item.id} label="" type="select" options={dynamicData} formData={formData} handleOnChange={handleOnChange} /> : <DynamicForm control={control} setValue={setValue} id={item.id} type={item.type} alias={item.alias} formData={formData} handleOnChange={handleOnChange} handleCheckboxChange={handleCheckboxChange} /> : ""}
                        </CardContent>
                    </Card>
                );
            })}
        </Box>
    );
};

export const DynamicForm = React.memo((props: any) => {
    const { control, setValue, id, type, selectValues = [], formData, handleOnChange, handleCheckboxChange } = props;

    const regexTextOption = [
        { value: "=", label: "=" },
        { value: "%*", label: "Inizia per" },
        { value: "*%", label: "Finisce per" },
        { value: "%%", label: "Contiene" },
        { value: "<>", label: "!=" },
        { value: "IN", label: "IN" },
        { value: "NOT IN", label: "NOT IN" },
        { value: "IS NULL", label: "IS NULL" },
        { value: "IS NOT NULL", label: "IS NOT NULL" }
    ];
    const dateOption = [
        { value: "=", label: "=" },
        { value: ">", label: ">" },
        { value: "<", label: "<" },
        { value: "BETWEEN", label: "Da...A" },
        { value: "<>", label: "!:" },
        { value: "IS NULL", label: "IS NULL" },
        { value: "IS NOT NULL", label: "IS NOT NULL" }
    ];
    const numberOption = [
        { value: "=", label: "=" },
        { value: ">", label: ">" },
        { value: "<", label: "<" },
        { value: "<>", label: "<>" },
        { value: "IN", label: "IN" },
        { value: "NOT IN", label: "NOT IN" },
        { value: "IS NULL", label: "IS NULL" },
        { value: "IS NOT NULL", label: "IS NOT NULL" }
    ];
    const checkboxOption = [
        { value: "=", label: "=" },
        { value: "<>", label: "!=" },
        { value: "IS NULL", label: "IS NULL" },
        { value: "IS NOT NULL", label: "IS NOT NULL" }
    ];

    const booleanOption = [
        { value: "=", label: "=" },
        { value: "<>", label: "!=" },
        { value: "IS NULL", label: "IS NULL" },
        { value: "IS NOT NULL", label: "IS NOT NULL" }
    ];
    return (
        <div>
            <FormInput
                style={{ width: 200, marginLeft: "0.5em" }}
                control={control}
                setValue={setValue}
                name={"checkbox" + id}
                formData={formData}
                handleOnChange={handleCheckboxChange}
                type="checkbox"
                variant="outlined"
                options={[
                    {
                        label: "Param",
                        value: "on"
                    }
                ]}
            />
            {type == "regex" || type == "text" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={regexTextOption} />
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} className="whereValue" type="text" name={"whereValue" + id} formData={formData} handleOnChange={handleOnChange} />
                </>
            ) : type == "date" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={dateOption} />
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"whereValue" + id} formData={formData} handleOnChange={handleOnChange} type="date" />
                    {formData["select" + id] === "BETWEEN" && <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"whereValueTo" + id} formData={formData} handleOnChange={handleOnChange} type="date" />}
                </>
            ) : type == "number" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={numberOption} />
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"whereValue" + id} formData={formData} handleOnChange={handleOnChange} type="text" />
                </>
            ) : type == "checkbox" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={checkboxOption} />
                    <FormInput
                        disabled={formData["checkbox" + id] === "on"}
                        style={{ width: 200 }}
                        control={control}
                        setValue={setValue}
                        name={"whereValue" + id}
                        formData={formData}
                        handleOnChange={handleOnChange}
                        type="checkbox"
                        options={[
                            {
                                label: "No",
                                value: "false"
                            },
                            {
                                label: "Si",
                                value: "true"
                            }
                        ]}
                    />
                </>
            ) : type == "select" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={[]} />
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"whereValue" + id} formData={formData} handleOnChange={handleOnChange} type="checkbox" options={selectValues} />
                </>
            ) : type == "bool" ? (
                <>
                    <FormInput disabled={formData["checkbox" + id] === "on"} style={{ width: 200 }} control={control} setValue={setValue} name={"select" + id} formData={formData} handleOnChange={handleOnChange} label="" type="select" options={booleanOption} />
                    <FormInput
                        disabled={formData["checkbox" + id] === "on"}
                        style={{ width: 200 }}
                        control={control}
                        setValue={setValue}
                        name={"whereValue" + id}
                        formData={formData}
                        handleOnChange={handleOnChange}
                        type="select"
                        options={[
                            {
                                label: "No",
                                value: "false"
                            },
                            {
                                label: "Si",
                                value: "true"
                            }
                        ]}
                    />
                </>
            ) : (
                ""
            )}
        </div>
    );
});
