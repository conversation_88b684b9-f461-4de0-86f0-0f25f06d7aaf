import VaporPage from "@vapor/react-custom/VaporPage";
import Vapor<PERSON>eaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import { GridPaginationModel, GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { useState, useCallback } from "react";
import usePostCustom from "../../../hooks/usePostCustom";

export default function QueryBuilderIndex() {
    const navigate = useNavigate();
    const [selectedGridRows, setSelectedGridRows] = useState<GridRowSelectionModel>([]);
    const { t } = useTranslation();

    const { query, setQuery, list, loading, filterData } = useFilter();

    const setQueryAndLocalStorage = (query: any) => {
        setQuery(query);
        localStorage.setItem("queryBuilderQuery", JSON.stringify(query));
    };

    const deleteItem = usePostCustom(`querybuilder/delete-query?noTemplateVars=true`);
    const handleClickCallback = (uniqueid: string) => {
        navigate("/querybuilder/view/" + uniqueid);
    };
    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQueryAndLocalStorage({
            ...query,
            page: model.page,
            pageSize: model.pageSize
        });
    };
    const handleRowSelectionChange = useCallback((newSelectionModel: GridRowSelectionModel) => {
        setSelectedGridRows(newSelectionModel);
    }, []);
    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return <CustomDataGrid name="querybuilder" setQuery={setQuery} columns={list.columns} data={list.rows} page={list.page ?? 0} totalRows={list.totalRows} pageSize={list.pageSize} loading={loading} query={query} onPageChangeCallback={onPageChangeCallback} onClickCallback={handleClickCallback} disableColumnResize={true} disableColumnReorder={true} onClickKey="id" selectableRows onRowSelectionModelChange={handleRowSelectionChange} />;
    };
    const redirectAction = () => {
        navigate("/querybuilder/view");
    };
    const deleteAction = async () => {
        let formData = new FormData();
        selectedGridRows.forEach((ref: any) => formData.append("ids[]", ref));
        await deleteItem.doFetch(true, formData);
        filterData(query);
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button color="error" disabled={selectedGridRows.length > 0 ? false : true} onClick={deleteAction}>
                            {t("Elimina")}
                        </Button>,
                        <Button variant="contained" onClick={redirectAction}>
                            {t("Nuova query")}
                        </Button>
                    ]}
                    title={"Query builder"}
                />
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
