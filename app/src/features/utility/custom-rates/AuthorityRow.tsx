import {
    ListItem,
    IconButton,
    CircularProgress,
    ListItemText,
    Radio,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash } from "@fortawesome/free-solid-svg-icons";
import { IAuthorityRow } from "../../../interfaces/CustomRate.interface";

export const AuthorityRow = ({
    selectedAuthority,
    selectedAuthorityDelete,
    value,
    setSelectedAuthorityDelete,
    setSelectedAuthority,
}: IAuthorityRow) => {
    return (
        <ListItem
            secondaryAction={
                selectedAuthorityDelete === value.id ? (
                    <CircularProgress />
                ) : (
                    <IconButton
                        color="error"
                        onClick={() => setSelectedAuthorityDelete(value.id)}
                    >
                        <FontAwesomeIcon icon={faTrash} />
                    </IconButton>
                )
            }
        >
            <Radio
                checked={value.id === selectedAuthority}
                onClick={() => setSelectedAuthority(value.id)}
            ></Radio>
            <ListItemText>{value.nome}</ListItemText>
        </ListItem>
    );
};
