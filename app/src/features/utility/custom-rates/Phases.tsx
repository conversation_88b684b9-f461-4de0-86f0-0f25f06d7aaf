import { Typography } from "@vapor/react-extended";
import { Stack, List, ListItem, CircularProgress } from "@vapor/react-material";
import { useGetPhases } from "./hooks/GetPhases";
import { PhaseRow } from "./PhaseRow";
import { useTranslation } from "@1f/react-sdk";
import { IPhaseData, IPhasesProps } from "../../../interfaces/CustomRate.interface";

export const Phases = ({
    selectedThreshold,
    selectedAuthority,
    selectedFieldModify,
    setSelectedFieldModify,
}: IPhasesProps) => {

    const getPhasesResponse = useGetPhases({
        thresholdId: selectedThreshold,
        authorityId: selectedAuthority,
    });

    const { t } = useTranslation();

    return (
        <Stack>
            <Typography>{t("Fasi")}</Typography>
            <ListItem style={{ borderColor: "white" }}>
                <Stack
                    direction="row"
                    paddingLeft={5}
                    style={{
                        width: "90%",
                    }}
                    justifyContent="space-between"
                >
                    <Typography>{t("Fase")}</Typography>
                    <Stack direction="row" gap={10}>
                        <Typography>{t("Min")}</Typography>
                        <Typography>{t("Max")}</Typography>
                    </Stack>
                </Stack>
            </ListItem>
            <List>
                {getPhasesResponse.loading ? (
                    <Stack
                        style={{ width: "100%", height: "100%" }}
                        alignItems="center"
                        justifyContent="center"
                    >
                        <CircularProgress />
                    </Stack>
                ) : (
                    getPhasesResponse.hasLoaded &&
                    getPhasesResponse.data.map(
                        (value: IPhaseData, index: number) => (
                            <PhaseRow
                                selectedFieldModify={selectedFieldModify}
                                index={index}
                                value={value}
                                setSelectedFieldModify={setSelectedFieldModify}
                            ></PhaseRow>
                        )
                    )
                )}
            </List>
        </Stack>
    );
};
