import {
    Stack,
    TextField,
    FormControlLabel,
    Checkbox,
} from "@vapor/react-material";
import { ChangeEvent, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import { ITariffProps } from "../../../interfaces/CustomRate.interface";
import { useGetTariffarioUpdate } from "./hooks/GetTariffarioUpdate";
import { useSaveCustomRate } from "./hooks/SaveCustomRate";
import { useDeleteCustomRate } from "./hooks/DeleteCustomRate";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { useNavigate } from "react-router-dom";

export const Tariff = ({
    id,
    saveCustomRate,
    deleteCustomRate,
    deleteCustomRateModal,
    setDeleteCustomRateModal,
    setDeleteCustomRate,
    authorityData,
    setAuthorityData,
}: ITariffProps) => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const getTariffarioResponse = useGetTariffarioUpdate(id, false);

    useEffect(() => {
        if (getTariffarioResponse.hasLoaded && getTariffarioResponse.data) {
            setAuthorityData(getTariffarioResponse.data);
        }
    }, [getTariffarioResponse.data, getTariffarioResponse.hasLoaded]);

    const saveCustomRateResponse = useSaveCustomRate({
        descrizione: authorityData.descrizione,
        nome: authorityData.nome,
        riservato: authorityData.riservato === "1" ? "true" : "false",
        uid: id,
        save: saveCustomRate,
    });

    const deleteCustomRateResponse = useDeleteCustomRate({
        uid: id,
        remove: deleteCustomRate,
    });

    useEffect(() => {
        if (
            saveCustomRateResponse.hasLoaded ||
            deleteCustomRateResponse.hasLoaded
        ) {
            navigate("/customrate");
        }
    }, [saveCustomRateResponse.hasLoaded, deleteCustomRateResponse.hasLoaded]);

    const handleNameChange = (
        event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setAuthorityData((prevData) => ({
            ...prevData,
            nome: event.target.value,
        }));
    };

    const handleDescriptionChange = (
        event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setAuthorityData((prevData) => ({
            ...prevData,
            descrizione: event.target.value,
        }));
    };

    const handleReservedChange = (event: ChangeEvent<HTMLInputElement>) => {
        setAuthorityData((prevData) => ({
            ...prevData,
            riservato: event.target.checked ? "1" : "0",
        }));
    };

    const handleDeleteCustomerModal = () => {
        setDeleteCustomRateModal(false);
    };

    const handleAgree = () => {
        setDeleteCustomRate(true);
    };

    return (
        <Stack direction="row" gap={3}>
            <ConfirmModal
                open={deleteCustomRateModal}
                title={t("Eliminare tariffario?")}
                confirmText={`${t(
                    "Eliminare definitivamente il tariffario"
                )} "${authorityData.nome}"?`}
                agree={t("Si")}
                decline={t("No")}
                handleDecline={handleDeleteCustomerModal}
                handleAgree={handleAgree}
            />
            <TextField
                sx={{ width: 350 }}
                label={t("Nome")}
                value={authorityData.nome}
                onChange={handleNameChange}
            />
            <TextField
                sx={{ width: 350 }}
                label={t("Descrizione")}
                value={authorityData.descrizione}
                onChange={handleDescriptionChange}
            />
            <FormControlLabel
                value={authorityData.riservato === "1"}
                control={
                    <Checkbox
                        checked={authorityData.riservato === "1"}
                        onChange={handleReservedChange}
                    />
                }
                label={t("Riservato")}
                labelPlacement="top"
            />
        </Stack>
    );
};
