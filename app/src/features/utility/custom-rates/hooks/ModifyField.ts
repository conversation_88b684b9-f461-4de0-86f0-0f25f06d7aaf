import { useEffect, useRef } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IModifyFieldProps } from "../../../../interfaces/CustomRate.interface";

export const useModifyField: IModifyFieldProps = ({
    fieldType,
    idAuthority,
    idPhase,
    idThreshold,
    index,
    thresholds,
    value,
}) => {
    const { data, hasLoaded, doFetch, loading, error } = usePostCustom(
        "customrate/modify-field?noTemplateVars=true"
    );

    const prevFieldType = useRef(fieldType);
    const prevValue = useRef(value);

    const requestData = {
        fascie: thresholds.map((value) => value.id),
        fieldType: fieldType,
        value: value,
        idFascia: idThreshold,
        idFase: idPhase,
        idAutorita: idAuthority,
        index: index !== null ? index + 1 : "",
    };

    useEffect(() => {
        if (
            fieldType !== "" &&
            value !== "" &&
            index !== null &&
            (fieldType !== prevFieldType.current || value !== prevValue.current)
        ) {
            doFetch(true, requestData);
            prevFieldType.current = fieldType;
            prevValue.current = value;
        }
    }, [fieldType, value, idPhase, idThreshold, index]);

    return { data, hasLoaded, loading, error };
};
