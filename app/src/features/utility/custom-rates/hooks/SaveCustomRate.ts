import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { ISaveCustomRateProps } from "../../../../interfaces/CustomRate.interface";

export const useSaveCustomRate: ISaveCustomRateProps = ({
    descrizione,
    nome,
    riservato,
    uid,
    save,
}) => {
    const { data, hasLoaded, loading, doFetch, error } = usePostCustom(
        "customrate/save?noTemplateVars=true"
    );

    useEffect(() => {
        if (save) {
            doFetch(true, {
                descrizione: descrizione,
                nome: nome,
                riservato: riservato,
                uid: uid,
            });
        }
    }, [descrizione, nome, riservato, uid, save]);
    return { data, hasLoaded, loading, error };
};
