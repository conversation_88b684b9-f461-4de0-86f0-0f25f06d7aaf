import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IRemoveThresholdProps } from "../../../../interfaces/CustomRate.interface";

export const useRemoveThreshold: IRemoveThresholdProps = ({
    id,
    removeAll,
    uid,
}) => {
    const { data, hasLoaded, doFetch, error, loading } = usePostCustom(
        "customrate/remove-threshold?noTemplateVars=true"
    );

    useEffect(() => {
        if (id !== "" && uid !== "") {
            doFetch(true, { id: id, removeAll: removeAll, uid: uid });
        }
    }, [id, uid]);
    return { data, hasLoaded, error, loading };
};
