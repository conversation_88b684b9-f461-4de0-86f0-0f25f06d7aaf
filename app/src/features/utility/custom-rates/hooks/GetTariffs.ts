import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { ITariffsProps } from "../../../../interfaces/CustomRate.interface";
import { debounce } from "lodash";

export const useSearchTarifs = ({
    page,
    pageSize,
    searchField,
    sortColumn,
    sortOrder,
}: ITariffsProps) => {
    const { hasLoaded, loading, doFetch, data, error } = useGetCustom(
        "customrate/list?noTemplateVars=true"
    );

    const debouncedFetch = debounce((params: any) => {
        doFetch(true, params);
    }, 1000);

    useEffect(() => {
        debouncedFetch({
            page: page,
            pageSize: pageSize,
            searchField: searchField,
            sortColumn: sortColumn,
            sortOrder: sortOrder,
        });

        return () => {
            debouncedFetch.cancel();
        };
    }, [page, pageSize, searchField, sortColumn, sortOrder]);

    return { data, hasLoaded, error, loading };
};
