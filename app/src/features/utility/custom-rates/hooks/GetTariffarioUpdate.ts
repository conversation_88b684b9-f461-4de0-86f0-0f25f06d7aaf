import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { ITariffarioData } from "../../../../interfaces/CustomRate.interface";

export const useGetTariffarioUpdate = (id: string, reload: boolean) => {
    const { data, hasLoaded, doFetch, error, loading } =
        useGetCustom("customrate/update");

    useEffect(() => {
        if (id !== "" && id) {
            doFetch(true, { uid: id });
        }
    }, [id, reload]);

    if (data && data.result) {
        return {
            data: data.result as ITariffarioData,
            autoritaSelect: data.autoritaSelect,
            hasLoaded,
            error,
            loading,
        };
    }

    return { data: undefined, hasLoaded, error, loading };
};
