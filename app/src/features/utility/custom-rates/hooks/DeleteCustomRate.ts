import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { IDeleteCustomRateProps } from "../../../../interfaces/CustomRate.interface";

export const useDeleteCustomRate: IDeleteCustomRateProps = ({
    remove,
    uid,
}) => {
    const { data, hasLoaded, doFetch, error, loading } = useGetCustom(
        "customrate/delete?noTemplateVars=true"
    );

    useEffect(() => {
        if (remove && uid) {
            doFetch(true, { uniqueid: uid });
        }
    }, [uid, remove]);

    return { data, hasLoaded, error, loading };
};
