import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import {
    IAddThresholdProps,
} from "../../../../interfaces/CustomRate.interface";

export const useAddThreshold: IAddThresholdProps = ({
    authorityId,
    uid,
    addThreshold,
}) => {
    const { data, doFetch, hasLoaded, loading, error } = usePostCustom(
        "customrate/add-threshold?noTemplateVars=true"
    );

    useEffect(() => {
        if (addThreshold && uid !== "" && authorityId !== "") {
            doFetch(true, {
                uid: uid,
                idAutorita: authorityId,
                nome: "",
            });
        }
    }, [authorityId, uid, addThreshold]);

    return { data, hasLoaded, loading, error };
};
