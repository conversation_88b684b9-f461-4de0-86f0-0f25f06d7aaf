import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IThresholdsProps } from "../../../../interfaces/CustomRate.interface";

export const useGetThresholds: IThresholdsProps = ({
    authorityId,
    reloadThreshold,
    uid,
}) => {
    const { data, hasLoaded, loading, doFetch, error } = usePostCustom(
        "customrate/get-thresholds?noTemplateVars=true"
    );

    useEffect(() => {
        if (authorityId !== "" && uid !== "") {
            doFetch(true, { uid: uid, idAutorita: authorityId });
        }
    }, [authorityId, uid, reloadThreshold]);

    return { data, hasLoaded, error, loading };
};
