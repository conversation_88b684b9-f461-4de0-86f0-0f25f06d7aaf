import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IRemoveAuthorityProps } from "../../../../interfaces/CustomRate.interface";

export const useRemoveAuthority: IRemoveAuthorityProps = ({
    id,
    removeAll,
    uid,
}) => {
    const { data, hasLoaded, doFetch, error, loading } = usePostCustom(
        "customrate/remove-threshold"
    );

    useEffect(() => {
        if (id !== "" && uid !== "") {
            doFetch(true, { id: id, removeAll: removeAll, uid: uid });
        }
    }, [id, uid]);
    return { data, hasLoaded, error, loading };
};
