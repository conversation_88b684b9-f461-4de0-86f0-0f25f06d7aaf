import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IAddAuthorityProps } from "../../../../interfaces/CustomRate.interface";

export const useAddAuthority: IAddAuthorityProps = ({
    uid,
    authorityId,
    add,
}) => {
    const { data, hasLoaded, loading, doFetch, error } = usePostCustom(
        "customrate/add-authority?noTemplateVars=true"
    );

    useEffect(() => {
        if (authorityId !== "" && uid !== "" && add === true) {
            doFetch(true, { uid: uid, idAutorita: authorityId });
        }
    }, [authorityId, uid, add]);

    return { data, hasLoaded, error, loading };
};
