import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IGetPhasesProps } from "../../../../interfaces/CustomRate.interface";

export const useGetPhases: IGetPhasesProps = ({ authorityId, thresholdId }) => {
    const { data, hasLoaded, doFetch, loading, error } = usePostCustom(
        "customrate/get-phases?noTemplateVars=true"
    );

    useEffect(() => {
        if (authorityId !== "" && thresholdId !== "") {
            doFetch(true, {
                idFascia: thresholdId,
                idAutorita: authorityId,
            });
        }
    }, [authorityId, thresholdId]);

    return { data, hasLoaded, error, loading };
};
