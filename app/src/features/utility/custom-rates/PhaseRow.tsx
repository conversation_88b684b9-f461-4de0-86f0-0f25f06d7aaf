import { <PERSON>I<PERSON>, Stack, TextField } from "@vapor/react-material";
import { ChangeEvent, useState } from "react";
import { IPhaseRowProps } from "../../../interfaces/CustomRate.interface";

export const PhaseRow = ({
    value,
    index,
    selectedFieldModify,
    setSelectedFieldModify,
}: IPhaseRowProps) => {
    const [phaseValues, setPhaseValues] = useState(value);

    const onKeyDownCheckNumeric = (event: any) => {
        const key = event.key;
        if (
            [
                "ArrowLeft",
                "ArrowRight",
                "ArrowUp",
                "ArrowDown",
                "Backspace",
                "Tab",
                "Home",
                "End",
            ].includes(key)
        ) {
            return;
        }

        if (/^[0-9.,]$/.test(key)) {
            const { value } = event.target;
            const commaIndex = value.indexOf(",");

            if (commaIndex !== -1 && key === ",") {
                event.preventDefault();
            }
        } else {
            event.preventDefault();
        }
    };

    const handleUpdateField = (fieldType: "desc" | "min" | "max") => () => {
        const fieldValue =
            phaseValues[fieldType === "desc" ? "descrizione" : fieldType];

        const formatedValue =
            fieldType !== "desc"
                ? fieldValue.replace(/\./g, "").replace(/,(?=[^,]*$)/, ".")
                : fieldValue;

        setSelectedFieldModify({
            ...selectedFieldModify,
            fieldType,
            index,
            value: formatedValue,
            idPhase: phaseValues.id,
        });
    };

    const handleUpdateState =
        (field: "descrizione" | "min" | "max") =>
        (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            setPhaseValues({
                ...phaseValues,
                [field]: event.target.value,
            });
        };

    return (
        <ListItem
            dense
            disableGutters
            disablePadding
            style={{ borderColor: "white" }}
        >
            <Stack direction="row" gap={3}>
                <TextField
                    sx={{ width: 280 }}
                    value={phaseValues.descrizione}
                    onChange={handleUpdateState("descrizione")}
                    onBlur={handleUpdateField("desc")}
                />
                <Stack direction="row" gap={1}>
                    <TextField
                        value={phaseValues.min}
                        onChange={handleUpdateState("min")}
                        onBlur={handleUpdateField("min")}
                        onKeyDown={onKeyDownCheckNumeric}
                        inputProps={{
                            style: {
                                textAlign: "right",
                            },
                        }}
                    />
                    <TextField
                        value={phaseValues.max}
                        onChange={handleUpdateState("max")}
                        onBlur={handleUpdateField("max")}
                        onKeyDown={onKeyDownCheckNumeric}
                        inputProps={{
                            style: {
                                textAlign: "right",
                            },
                        }}
                    />
                </Stack>
            </Stack>
        </ListItem>
    );
};
