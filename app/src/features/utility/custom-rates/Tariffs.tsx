import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../custom-components/PageTitle";
import { TextField, Stack, Button } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch } from "@fortawesome/free-solid-svg-icons";
import { ChangeEvent, useState } from "react";
import { useSearchTarifs } from "./hooks/GetTariffs";
import { TariffsList } from "./TariffsList";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import { ITariffsProps } from "../../../interfaces/CustomRate.interface";

export const Tariffs = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const [searchTariffsParams, setSearchTariffsParams] = useState<ITariffsProps>({
        page: 0,
        pageSize: 10,
        searchField: "",
        sortColumn: "nome",
        sortOrder: "asc",
    });

    const handleUpdateSearchField = (
        event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setSearchTariffsParams({
            ...searchTariffsParams,
            searchField: event.target.value,
        });
    };

    const tariffsSearchRespose = useSearchTarifs(searchTariffsParams);

    const handleNavigation = () => {
        navigate("/customrate/update");
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("GESTIONE TARIFFARI")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: t("Nuovo tariffario"),
                        onclick: () => handleNavigation(),
                    },
                ]}
            />
            <VaporPage.Section>
                <Stack direction="row" alignItems="flex-end" maxWidth={350}>
                    <TextField
                        label={t("Nome")}
                        value={searchTariffsParams.searchField}
                        onChange={handleUpdateSearchField}
                    />
                    <Button variant="contained">
                        <FontAwesomeIcon icon={faSearch}></FontAwesomeIcon>
                    </Button>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                {tariffsSearchRespose.hasLoaded && (
                    <TariffsList
                        data={tariffsSearchRespose}
                        searchTariffsParams={searchTariffsParams}
                        setSearchTariffsParams={setSearchTariffsParams}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
