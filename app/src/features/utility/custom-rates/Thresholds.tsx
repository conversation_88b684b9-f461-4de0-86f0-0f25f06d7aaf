import { Typography } from "@vapor/react-extended";
import { Stack, List, ListItem, IconButton } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCirclePlus } from "@fortawesome/free-solid-svg-icons";
import { ThresholdRow } from "./ThresholdRow";
import { useTranslation } from "@1f/react-sdk";
import {
    IThresholdData,
    IThresholdProps,
} from "../../../interfaces/CustomRate.interface";
import { useEffect, useState } from "react";
import { useGetThresholds } from "./hooks/GetThresholds";
import { useAddThreshold } from "./hooks/AddThreshold";
import { useModifyField } from "./hooks/ModifyField";
import { useRemoveThreshold } from "./hooks/RemoveThreshold";

export const Thresholds = ({
    id,
    selectedAuthority,
    selectedThreshold,
    setSelectedThreshold,
    selectedFieldModify,
    thresholds,
    setThresholds,
    setSelectedFieldModify,
}: IThresholdProps) => {
    const { t } = useTranslation();

    const [addThreshold, setAddThreshold] = useState(false);
    const [selectedThresholdDelete, setSelectedThresholdDelete] = useState("");

    const deleteThresholdResponse = useRemoveThreshold({
        uid: id,
        id: selectedThresholdDelete,
        removeAll: false,
    });

    useEffect(() => {
        if (deleteThresholdResponse.hasLoaded) {
            setSelectedThresholdDelete("");
        }
    }, [deleteThresholdResponse.hasLoaded, deleteThresholdResponse.loading]);

    const modifyAuthorityResponse = useModifyField(selectedFieldModify);

    const addThresholdResponse = useAddThreshold({
        authorityId: selectedAuthority,
        uid: id,
        addThreshold: addThreshold,
    });

    const getThresholdsResponse = useGetThresholds({
        uid: id,
        authorityId: selectedAuthority,
        reloadThreshold:
            addThresholdResponse.loading ||
            modifyAuthorityResponse.hasLoaded ||
            deleteThresholdResponse.loading,
    });

    useEffect(() => {
        if (selectedThreshold === "" && thresholds.length !== 0) {
            setSelectedThreshold(thresholds[0].id);
        }
    }, [thresholds.length]);

    useEffect(() => {
        if (getThresholdsResponse.hasLoaded && getThresholdsResponse.data) {
            setAddThreshold(false);
            setThresholds(getThresholdsResponse.data);
        }
    }, [getThresholdsResponse.data, getThresholdsResponse.hasLoaded]);

    return (
        <Stack>
            <Typography>{t("Fasce")}</Typography>
            <ListItem
                style={{ borderColor: "white" }}
                secondaryAction={
                    <IconButton
                        color="primary"
                        onClick={() => setAddThreshold(true)}
                    >
                        <FontAwesomeIcon icon={faCirclePlus}></FontAwesomeIcon>
                    </IconButton>
                }
            >
                <Stack
                    direction="row"
                    paddingLeft={5}
                    style={{
                        width: "90%",
                    }}
                    justifyContent="space-between"
                >
                    <Typography>{t("Descrizione")}</Typography>
                    <Stack direction="row" gap={15}>
                        <Typography>{t("Min")}</Typography>
                        <Typography>{t("Max")}</Typography>
                    </Stack>
                </Stack>
            </ListItem>
            <List>
                {thresholds.map((value: IThresholdData, index: number) => (
                    <ThresholdRow
                        key={value.id}
                        index={index}
                        selectedThreshold={selectedThreshold}
                        setSelectedThreshold={setSelectedThreshold}
                        selectedThresholdDelete={selectedThresholdDelete}
                        setSelectedThresholdDelete={setSelectedThresholdDelete}
                        selectedFieldModify={selectedFieldModify}
                        setSelectedFieldModify={setSelectedFieldModify}
                        value={value}
                    ></ThresholdRow>
                ))}
            </List>
        </Stack>
    );
};
