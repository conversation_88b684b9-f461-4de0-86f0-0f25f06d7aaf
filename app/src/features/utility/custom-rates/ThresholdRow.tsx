import {
    ListItem,
    Radio,
    Stack,
    TextField,
    IconButton,
    CircularProgress,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash } from "@fortawesome/free-solid-svg-icons";
import { ChangeEvent, useState } from "react";
import { IThresholdRowProps } from "../../../interfaces/CustomRate.interface";

export const ThresholdRow = ({
    selectedThreshold,
    setSelectedThreshold,
    setSelectedThresholdDelete,
    selectedThresholdDelete,
    setSelectedFieldModify,
    selectedFieldModify,
    value,
    index,
}: IThresholdRowProps) => {
    const [thresholdValues, setThresholdValues] = useState(value);

    const onKeyDownCheckNumeric = (event: any) => {
        const key = event.key;
        if (
            [
                "ArrowLeft",
                "ArrowRight",
                "ArrowUp",
                "ArrowDown",
                "Backspace",
                "Tab",
                "Home",
                "End",
            ].includes(key)
        ) {
            return;
        }

        if (/^[0-9.,]$/.test(key)) {
            const { value } = event.target;
            const commaIndex = value.indexOf(",");

            if (commaIndex !== -1 && key === ",") {
                event.preventDefault();
            }
        } else {
            event.preventDefault();
        }
    };

    const handleUpdateField = (fieldType: "desc" | "min" | "max") => () => {
        const fieldValue =
            thresholdValues[fieldType === "desc" ? "nome" : fieldType];

        const formatedValue =
            fieldType !== "desc"
                ? fieldValue.replace(/\./g, "").replace(/,(?=[^,]*$)/, ".")
                : fieldValue;

        setSelectedFieldModify({
            ...selectedFieldModify,
            fieldType,
            index,
            value: formatedValue,
            idPhase: "",
        });
    };

    const handleUpdateState =
        (field: "nome" | "min" | "max") =>
        (event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            setThresholdValues({
                ...thresholdValues,
                [field]: event.target.value,
            });
        };

    const handleSetSelectedThreshold = (id: string) => () => {
        setSelectedThreshold(id);
    };

    const handleSetSelectedThresholdDelete = (id: string) => () => {
        setSelectedThresholdDelete(id);
    };

    return (
        <ListItem
            dense
            style={{ borderColor: "white" }}
            secondaryAction={
                value.id === selectedThresholdDelete ? (
                    <CircularProgress></CircularProgress>
                ) : (
                    <IconButton
                        color="error"
                        onClick={handleSetSelectedThresholdDelete(value.id)}
                    >
                        <FontAwesomeIcon icon={faTrash}></FontAwesomeIcon>
                    </IconButton>
                )
            }
        >
            <Radio
                checked={selectedThreshold === value.id}
                onChange={handleSetSelectedThreshold(value.id)}
            ></Radio>
            <Stack
                direction="row"
                gap={3}
                style={{
                    width: "90%",
                }}
            >
                <TextField
                    onClick={handleSetSelectedThreshold(value.id)}
                    value={thresholdValues.nome}
                    onChange={handleUpdateState("nome")}
                    onBlur={handleUpdateField("desc")}
                ></TextField>
                <Stack direction="row" gap={1}>
                    <TextField
                        onClick={handleSetSelectedThreshold(value.id)}
                        value={thresholdValues.min}
                        onChange={handleUpdateState("min")}
                        onBlur={handleUpdateField("min")}
                        onKeyDown={onKeyDownCheckNumeric}
                        inputProps={{
                            style: {
                                textAlign: "right",
                            },
                        }}
                    ></TextField>
                    <TextField
                        onClick={handleSetSelectedThreshold(value.id)}
                        value={thresholdValues.max}
                        onChange={handleUpdateState("max")}
                        onBlur={handleUpdateField("max")}
                        onKeyDown={onKeyDownCheckNumeric}
                        inputProps={{
                            style: {
                                textAlign: "right",
                            },
                        }}
                    ></TextField>
                </Stack>
            </Stack>
        </ListItem>
    );
};
