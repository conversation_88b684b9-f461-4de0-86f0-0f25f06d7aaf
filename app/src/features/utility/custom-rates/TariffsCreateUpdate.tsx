import { VaporPage } from "@vapor/react-custom";
import { useParams } from "react-router-dom";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState } from "react";
import { Tariff } from "./Tariff";
import { Grid } from "@vapor/react-material";
import { Authority } from "./Authority";
import {
    ITariffarioData,
    IThresholdData,
    ModifyFields,
} from "../../../interfaces/CustomRate.interface";
import { Thresholds } from "./Thresholds";
import { Phases } from "./Phases";

export const CreateUpdateTariffs = () => {
    const { t } = useTranslation();

    const { uid } = useParams();
    const id = uid ? uid.split("=")[1] : "";

    const [authorityData, setAuthorityData] = useState<ITariffarioData>({
        autorita: [],
        descrizione: "",
        external_code: "",
        id: "",
        immessoil: "",
        nome: "",
        riservato: "",
        uniqueid: "",
    });

    const [saveCustomRate, setSaveCustomRate] = useState(false);
    const [deleteCustomRate, setDeleteCustomRate] = useState(false);
    const [deleteCustomRateModal, setDeleteCustomRateModal] = useState(false);

    const [selectedAuthority, setSelectedAuthority] = useState<string>("");
    const [selectedThreshold, setSelectedThreshold] = useState<string>("");

    const [thresholds, setThresholds] = useState<IThresholdData[]>([]);

    const [selectedFieldModify, setSelectedFieldModify] =
        useState<ModifyFields>({
            fieldType: "",
            idAuthority: "",
            idPhase: "",
            idThreshold: "",
            index: null,
            thresholds: [],
            value: "",
        });

    useEffect(() => {
        setSelectedFieldModify((previousValues) => ({
            ...previousValues,
            idAuthority: selectedAuthority,
            idThreshold: selectedThreshold,
            thresholds: thresholds,
            idPhase: "",
        }));
    }, [selectedAuthority, selectedThreshold, thresholds]);

    const handleSaveCustomRate = () => {
        setSaveCustomRate(true);
    };

    const handleDeleteCustomRate = () => {
        setDeleteCustomRateModal(true);
    };
    return (
        <VaporPage>
            <PageTitle
                title={id ? t("Riepilogo tariffario") : t("Nuovo Tariffario")}
                pathToPrevPage="/customrate"
                actionButtons={[
                    {
                        label: t("Elimina"),
                        onclick: handleDeleteCustomRate,
                        color: "error",
                    },
                    { label: t("Salva"), onclick: handleSaveCustomRate },
                ]}
            />
            <VaporPage.Section>
                <Tariff
                    id={id}
                    deleteCustomRateModal={deleteCustomRateModal}
                    saveCustomRate={saveCustomRate}
                    deleteCustomRate={deleteCustomRate}
                    setDeleteCustomRateModal={setDeleteCustomRateModal}
                    setDeleteCustomRate={setDeleteCustomRate}
                    authorityData={authorityData}
                    setAuthorityData={setAuthorityData}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                {id !== "" && (
                    <Grid container spacing={5}>
                        <Grid item xs={3}>
                            <Authority
                                id={id}
                                selectedAuthority={selectedAuthority}
                                setSelectedAuthority={setSelectedAuthority}
                            />
                        </Grid>
                        <Grid item xs={6}>
                            <Thresholds
                                id={id}
                                selectedAuthority={selectedAuthority}
                                selectedThreshold={selectedThreshold}
                                setSelectedThreshold={setSelectedThreshold}
                                selectedFieldModify={selectedFieldModify}
                                setSelectedFieldModify={setSelectedFieldModify}
                                thresholds={thresholds}
                                setThresholds={setThresholds}
                            />
                        </Grid>
                        <Grid item xs={3}>
                            <Phases
                                selectedFieldModify={selectedFieldModify}
                                setSelectedFieldModify={setSelectedFieldModify}
                                selectedThreshold={selectedThreshold}
                                selectedAuthority={selectedAuthority}
                            />
                        </Grid>
                    </Grid>
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
