import { Typography } from "@vapor/react-extended";
import { Stack, Select, MenuItem, List } from "@vapor/react-material";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import { useTranslation } from "@1f/react-sdk";
import {
    IAuthorityProps,
    IAutorita,
} from "../../../interfaces/CustomRate.interface";
import { useGetTariffarioUpdate } from "./hooks/GetTariffarioUpdate";
import { useEffect, useState } from "react";
import { useAddAuthority } from "./hooks/AddAuthority";
import { useRemoveAuthority } from "./hooks/RemoveAuthority";
import { AuthorityRow } from "./AuthorityRow";

export const Authority = ({
    id,
    selectedAuthority,
    setSelectedAuthority,
}: IAuthorityProps) => {
    const { t } = useTranslation();

    const [authorityToAdd, setAuthorityToAdd] = useState<string>("");
    const [addAuthorityToTariffario, setAddAuthorityToTariffario] =
        useState(false);
    const [selectedAuthorityDelete, setSelectedAuthorityDelete] = useState("");

    const [authorities, setAuthorities] = useState<IAutorita[]>([]);

    const addAuthorityResponse = useAddAuthority({
        uid: id,
        authorityId: authorityToAdd,
        add: addAuthorityToTariffario,
    });

    const removeAuthorityResponse = useRemoveAuthority({
        id: selectedAuthorityDelete,
        uid: id,
        removeAll: true,
    });

    useEffect(() => {
        if (selectedAuthority === selectedAuthorityDelete) {
            setSelectedAuthority("");
        }
        setSelectedAuthorityDelete("");
    }, [removeAuthorityResponse.loading, removeAuthorityResponse.hasLoaded]);

    useEffect(() => {
        setAuthorityToAdd("");
        setAddAuthorityToTariffario(false);
    }, [addAuthorityResponse.loading]);

    const getTariffarioResponse = useGetTariffarioUpdate(
        id,
        addAuthorityResponse.loading || removeAuthorityResponse.loading
    );

    useEffect(() => {
        if (selectedAuthority === "" && authorities.length !== 0) {
            setSelectedAuthority(authorities[0].id);
        }
    }, [authorities.length]);

    useEffect(() => {
        if (
            getTariffarioResponse.hasLoaded &&
            getTariffarioResponse.data?.autorita
        ) {
            setAuthorities(getTariffarioResponse.data.autorita);
        }
    }, [
        getTariffarioResponse.loading ||
            addAuthorityResponse.loading ||
            removeAuthorityResponse.loading,
    ]);

    const handleAuthoritySelectChange = (event: any) => {
        setAuthorityToAdd(event.target.value);
        if (addAuthorityToTariffario) {
            setAddAuthorityToTariffario(false);
        }
    };

    const handleAddAuthority = () => {
        setAddAuthorityToTariffario(true);
    };

    return (
        <Stack gap={4}>
            <Typography>{t("Autorità")}</Typography>
            <Stack direction="row" maxWidth={500} gap={3}>
                {getTariffarioResponse.hasLoaded && (
                    <Select
                        onChange={handleAuthoritySelectChange}
                        value={authorityToAdd}
                        sx={{ width: 250 }}
                    >
                        {getTariffarioResponse.autoritaSelect.map(
                            (authority: any) => (
                                <MenuItem
                                    key={authority.id}
                                    value={authority.id}
                                >
                                    {authority.nome}
                                </MenuItem>
                            )
                        )}
                    </Select>
                )}
                <SpinnerButton
                    label={"Aggiungi"}
                    onClick={handleAddAuthority}
                    isLoading={addAuthorityResponse.loading}
                    disabled={
                        authorityToAdd === "" || addAuthorityResponse.loading
                    }
                ></SpinnerButton>
            </Stack>
            {authorities.length === 0 && getTariffarioResponse.hasLoaded && (
                <Typography>
                    {t(
                        "Per questo tariffario non è stata ancora inserita alcuna autorità"
                    )}
                </Typography>
            )}
            <Stack>
                <List>
                    {authorities.map((value: IAutorita) => (
                        <AuthorityRow
                            selectedAuthority={selectedAuthority}
                            selectedAuthorityDelete={selectedAuthorityDelete}
                            setSelectedAuthority={setSelectedAuthority}
                            setSelectedAuthorityDelete={
                                setSelectedAuthorityDelete
                            }
                            value={value}
                        />
                    ))}
                </List>
            </Stack>
        </Stack>
    );
};
