import { useTranslation } from "@1f/react-sdk";
import { Dispatch, SetStateAction } from "react";
import { useNavigate } from "react-router-dom";
import { Typography } from "@vapor/react-extended";
import { ITariffsProps } from "../../../interfaces/CustomRate.interface";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";

export const TariffsList = ({
    data,
    searchTariffsParams,
    setSearchTariffsParams,
}: {
    data: any;
    searchTariffsParams: ITariffsProps;
    setSearchTariffsParams: Dispatch<SetStateAction<ITariffsProps>>;
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setSearchTariffsParams({
            ...searchTariffsParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const columns = [
        {
            field: "nome",
            headerName: t("Nome"),
            width: 400,
            sortable: true,
        },
        {
            field: "descrizione",
            headerName: t("descrizione"),
            width: 400,
            sortable: true,
        },
        {
            field: "autorita",
            headerName: t("autorita"),
            width: 400,
            sortable: false,
            renderCell: (params: any) => (
                <Typography noWrap sx={{ maxWidth: 700 }}>
                    {params.value}
                </Typography>
            ),
        },
        {
            field: "immessoil",
            headerName: t("Data inserimento"),
            width: 300,
            sortable: true,
        },
    ];

    const handleNavigation = (id: string) => {
        if (id === "") {
            navigate(`/customrate/update`);
        } else {
            navigate(`/customrate/update/uid=${id}`);
        }
    };

    return (
        <>
            {data && (
                <CustomDataGrid
                    name="tariffsList"
                    page={searchTariffsParams.page}
                    pageSize={searchTariffsParams.pageSize}
                    data={data.data.currentPage}
                    totalRows={data.data.totalRows}
                    columns={columns}
                    onPageChangeCallback={onPageChangeCallback}
                    onClickKey="uniqueid"
                    onClickCallback={handleNavigation}
                    query={searchTariffsParams}
                    setQuery={setSearchTariffsParams}
                />
            )}
        </>
    );
};
