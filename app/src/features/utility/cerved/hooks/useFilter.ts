import { useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import usePostCustom from "../../../../hooks/usePostCustom";
import { IList } from "../../../../interfaces/general.interfaces";
import { getCervedGrid } from "../../../../utilities/cerved/gridColumn";

const defaultQuery: any = {
    subjectType: "",
    searchProvince: "",
    searchType: "name",
    data: "",
};
export default function useFilter(handleModal: any) {
    const { t } = useTranslation();
    const filterRequest = usePostCustom("cerved/search-results");

    const [query, setQuery] = useState<any>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const searchResult = async (query: any) => {
        const [columns, response]: any = await Promise.all([
            getCervedGrid(t, handleModal),
            filterRequest.doFetch(true, query),
        ]);
        const { results, totalCount } = response.data;

        console.log("results", results);

        setList({
            ...list,
            rows: results,
            columns,
            totalRows: totalCount,
            page: query?.page,
            pageSize: query?.pageSize,
        });
    };
    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        searchResult,
        loading: filterRequest.loading,
    };
}
