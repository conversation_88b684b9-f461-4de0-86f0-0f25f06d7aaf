import { useTranslation } from "@1f/react-sdk";
import {
    <PERSON>ton,
    <PERSON>alog,
    IconButton,
    DialogTitle,
    DialogActions,
    DialogContent,
    Divider,
} from "@vapor/react-material";
import List from "@vapor/react-material/List";
import ListItem from "@vapor/react-material/ListItem";
import ListItemText from "@vapor/react-material/ListItemText";
import { Close } from "@mui/icons-material";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import { useState } from "react";
const SearchViewModal = (props: {
    details: any;
    setOpenModal: any;
    openModal: any;
    importData: any;
}) => {
    const { details, setOpenModal, openModal, importData } = props;

    const { t } = useTranslation();
    const [isLoading] = useState<boolean>(false);
    return (
        <Dialog
            fullScreen
            open={openModal}
            onClose={() => setOpenModal(false)}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <DialogTitle>
                Dati anagrafici
                <IconButton color="primary" onClick={() => setOpenModal(false)}>
                    <Close />
                </IconButton>
            </DialogTitle>
            <Divider variant="fullWidth" />
            <DialogContent>
                {Object.keys(details).length > 0 && (
                    <List disablePadding>
                        <ListItem dense>
                            <ListItemText
                                primary={t("Nome")}
                                secondary={details?.nome ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Cognome")}
                                secondary={details?.cognome ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Denominazione")}
                                secondary={details?.denominazione ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Codice fiscale")}
                                secondary={details?.codicefiscale ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Partita iva")}
                                secondary={details?.partitaiva ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Tipo")}
                                secondary={details?.tipo ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Via")}
                                secondary={details?.indirizzi?.via ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("CAP")}
                                secondary={details?.indirizzi?.cap ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Citta")}
                                secondary={details?.indirizzi?.citta ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Regione")}
                                secondary={details?.indirizzi?.regione ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Nazione")}
                                secondary={details?.indirizzi?.nazione ?? "-"}
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("PEC")}
                                secondary={
                                    details?.contatti !== null
                                        ? details?.contatti[7]
                                        : "-"
                                }
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Email")}
                                secondary={
                                    details?.contatti !== null
                                        ? details?.contatti[6]
                                        : "-"
                                }
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Telefono")}
                                secondary={
                                    details?.contatti !== null
                                        ? details?.contatti[1]
                                        : "-"
                                }
                            />
                        </ListItem>
                        <Divider light />
                        <ListItem dense>
                            <ListItemText
                                primary={t("Web")}
                                secondary={
                                    details?.contatti !== null
                                        ? details?.contatti[8]
                                        : "-"
                                }
                            />
                        </ListItem>
                        {details?.tiponetlex != 60 ? (
                            <>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Codice rea")}
                                        secondary={details?.rea ?? "-"}
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Data iscrizione rea")}
                                        secondary={
                                            details?.dataiscrizionerea ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Stato attività")}
                                        secondary={
                                            details?.statoattivita ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Forma")}
                                        secondary={
                                            details?.formaattivita ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Ateco")}
                                        secondary={details?.ateco ?? "-"}
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Macrosettore")}
                                        secondary={
                                            details?.ateco_macrosettore ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Num. dipendenti")}
                                        secondary={
                                            typeof details?.numdipendenti !==
                                            "undefined"
                                                ? details?.numdipendenti
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Num. unità locali")}
                                        secondary={
                                            details?.numunitalocali ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Ultimo bilancio")}
                                        secondary={
                                            details?.annoultimobilancio ?? "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Chiusura bilancio")}
                                        secondary={
                                            details?.datachiusuraultimobilancio ??
                                            "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Fatturato")}
                                        secondary={
                                            typeof details?.fatturato !==
                                            "undefined"
                                                ? details?.fatturato
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Capitale sociale")}
                                        secondary={
                                            typeof details?.capitalesociale !==
                                            "undefined"
                                                ? details?.capitalesociale
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Mol")}
                                        secondary={
                                            typeof details?.mol !== "undefined"
                                                ? details?.mol
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Attivo")}
                                        secondary={
                                            typeof details?.attivo !==
                                            "undefined"
                                                ? details?.attivo
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Patrimonio netto")}
                                        secondary={
                                            typeof details?.patrimonionetto !==
                                            "undefined"
                                                ? details?.patrimonionetto
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                                <Divider light />
                                <ListItem dense>
                                    <ListItemText
                                        primary={t("Utile perdita esercizio")}
                                        secondary={
                                            typeof details?.utileperditaesercizio !==
                                            "undefined"
                                                ? details?.utileperditaesercizio
                                                      ?.toString()
                                                      .replace(
                                                          /\B(?=(\d{3})+(?!\d))/g,
                                                          "."
                                                      )
                                                : "-"
                                        }
                                    />
                                </ListItem>
                            </>
                        ) : (
                            ""
                        )}
                    </List>
                )}
            </DialogContent>
            <DialogActions>
                <Button variant="outlined" onClick={() => setOpenModal(false)}>
                    {t("Chiudi")}
                </Button>
                <SpinnerButton
                    label={t("Importa")}
                    variant="contained"
                    isLoading={isLoading}
                    onClick={() => importData()}
                />
            </DialogActions>
        </Dialog>
    );
};

export default SearchViewModal;
