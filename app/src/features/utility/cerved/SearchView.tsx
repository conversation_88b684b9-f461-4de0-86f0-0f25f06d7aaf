import VaporPage from "@vapor/react-custom/VaporPage";
import Spinner from "../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { useTranslation } from "@1f/react-sdk";
import useFilter from "./hooks/useFilter";
import { useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import SearchViewModal from "./SearchViewModal";
import PageTitle from "../../../custom-components/PageTitle";
import usePostCustom from "../../../hooks/usePostCustom";
import ToastNotification from "../../../custom-components/ToastNotification";

export default function SearchView() {
    const [show, setShow] = useState(false);
    const [alertText, setAlertText] = useState("");
    const location: any = useLocation();
    const { t } = useTranslation();

    const [openModal, setOpenModal] = useState<boolean>(false);
    const [rowData, setRowData] = useState<any>([]);

    const importAnagrafica = usePostCustom(
        "cerved/import-subject?noTemplateVars=true"
    );
    const mountedRequest = usePostCustom(
        "cerved/details-subject?noTemplateVars=true"
    );
    const [details, setDetails] = useState<any>([]);
    const detailRequestData = async (row: any) => {
        let { data }: any = await mountedRequest.doFetch(true, {
            subjectId: row.subject_id,
        });
        var indirizzi = JSON.parse(data.details.indirizzi)[9];
        var contatti = JSON.parse(data.details.contatti);
        data.details["indirizzi"] = indirizzi;
        data.details["contatti"] = contatti;
        setDetails(data.details);
    };
    const handleModal = (row: any) => {
        setRowData(row);
        detailRequestData(row);
        setOpenModal(true);
    };

    const { query, setQuery, list, searchResult, loading } =
        useFilter(handleModal);

    useEffect(() => {
        if (location.data !== "") {
            getSearchResult();
        }
    }, [location.state]);

    const getSearchResult = async () => {
        let data = location.state;
        setQuery({ ...query, data });
        await searchResult(location.state);
    };
    const importData = async () => {
        //insert data anagrafica
        const { data }: any = await importAnagrafica.doFetch(true, {
            subjectId: rowData.subject_id,
        });

        setShow(true);
        setOpenModal(false);
        setRowData([]);
        if (data !== "") {
            setAlertText(t("Imported successfully"));
        } else {
            setAlertText(
                t(
                    "Attenzione, a causa di un errore non è stato possibile terminare l'operazione. Se il problema persiste contattare l'assistenza tecnica."
                )
            );
        }
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="searchView"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle title={"Risultati"} pathToPrevPage={"/cerved"} />
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
                {openModal && (
                    <SearchViewModal
                        details={details}
                        setOpenModal={setOpenModal}
                        openModal={openModal}
                        importData={importData}
                    />
                )}
                <ToastNotification
                    showNotification={show}
                    setShowNotification={setShow}
                    severity="success"
                    text={alertText}
                />
            </VaporPage>
        </>
    );
}
