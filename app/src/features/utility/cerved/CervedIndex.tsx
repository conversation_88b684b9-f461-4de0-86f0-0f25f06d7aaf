import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../custom-components/FormInput";
import PageTitle from "../../../custom-components/PageTitle";

const CervedIndex = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [provincesList, setProvincesList] = useState<any>([]);

    const updateMountedRequest = useGetCustom("cerved");
    const schema = yup.object().shape({
        subjectType: yup.string(),
        searchProvince: yup.string(),
        searchType: yup.string(),
        data: yup.string().required(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setProvincesList(
                    response.data.provinces.map((row: any) => {
                        return {
                            label: row.name,
                            value: row.code,
                        };
                    })
                );
                setValue("searchProvince", "-1");
                setValue("subjectType", "-1");
                setValue("searchType", "name");
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        navigate(`/cerved/search-results`, {
            state: {
                subjectType:
                    getValues("subjectType") == "-1"
                        ? ""
                        : getValues("subjectType"),
                searchProvince:
                    getValues("searchProvince") == "-1"
                        ? ""
                        : getValues("searchProvince"),
                searchType: getValues("searchType"),
                data: getValues("data"),
            },
        });
    };

    return (
        <VaporPage>
            <PageTitle title={"Consultazione CERVED"} />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="subjectType"
                                    label="Tipo soggetto"
                                    type="select"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["subjectType"] &&
                                        errors["subjectType"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["subjectType"] &&
                                        errors["subjectType"]["message"]
                                    }
                                    options={[
                                        { value: "-1", label: "Tutti" },
                                        {
                                            value: "COMPANY",
                                            label: "Societa Di capitali o di persone",
                                        },
                                        {
                                            value: "INDIVIDUAL_COMPANY",
                                            label: "Ditta individuale",
                                        },
                                        {
                                            value: "PERSON",
                                            label: "Persona fisica",
                                        },
                                        {
                                            value: "FOREIGN",
                                            label: "Società o organizzazion non italiana",
                                        },
                                        { value: "OTHER", label: "Altro" },
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="searchProvince"
                                    label="Provincia soggetto"
                                    type="select"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["searchProvince"] &&
                                        errors["searchProvince"]["message"] !==
                                            ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["searchProvince"] &&
                                        errors["searchProvince"]["message"]
                                    }
                                    options={[
                                        { value: "-1", label: "Tutte" },
                                        ...provincesList,
                                    ]}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="searchType"
                                    label="Cerca per"
                                    type="select"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["searchType"] &&
                                        errors["searchType"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["searchType"] &&
                                        errors["searchType"]["message"]
                                    }
                                    options={[
                                        {
                                            value: "name",
                                            label: "Nome impresa",
                                        },
                                        {
                                            value: "vat_number",
                                            label: "Partita iva",
                                        },
                                        {
                                            value: "tax_code",
                                            label: "Cod. Fiscale",
                                        },
                                        {
                                            value: "search_text",
                                            label: "Tutti i campi",
                                        },
                                    ]}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="data"
                                    label="Data"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["data"] &&
                                        errors["data"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["data"] &&
                                        errors["data"]["message"]
                                    }
                                />
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button variant="contained" type="submit">
                            {t("Avvia la ricerca")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default CervedIndex;
