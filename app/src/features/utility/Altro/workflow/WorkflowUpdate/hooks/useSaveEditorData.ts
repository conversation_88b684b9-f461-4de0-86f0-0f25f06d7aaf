import { useEffect } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useTranslation } from "@1f/react-sdk";

interface SaveEditorDataProps {
    uniqueid: string | null;
    flowJSON: any;
    nome: string;
    descrizione: string;
    save: boolean;
}

export const useSaveEditorData = ({
    descrizione,
    flowJSON,
    nome,
    uniqueid,
    save,
}: SaveEditorDataProps) => {
    const { loading, hasLoaded, doFetch } = usePostCustom(
        "/workflows/save?noTemplateVars=true"
    );

    const { t } = useTranslation();

    useEffect(() => {
        if (save) {
            doFetch(true, {
                descrizione: descrizione,
                flowJSON: JSON.stringify(flowJSON),
                nome: nome === "" ? t("Nome non definito") : nome,
                uniqueid: uniqueid,
            });
        }
    }, [save]);

    return { loading, hasLoaded };
};
