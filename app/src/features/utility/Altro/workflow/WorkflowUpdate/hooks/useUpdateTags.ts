import { useEffect } from "react";
import usePostCustom from "../../../../../../hooks/usePostCustom";

export const useUpdateTags = ({
    uniqueid,
    fileTags,
    save,
}: {
    uniqueid: string | null;
    fileTags: string[];
    save: boolean;
}) => {
    const { loading, hasLoaded, doFetch } = usePostCustom(
        "/workflows/aggiornatags?noTemplateVars=true"
    );

    useEffect(() => {
        if (save && fileTags.length && uniqueid) {
            doFetch(true, { uniqueid: uniqueid, fileTags: fileTags });
        }
    }, [uniqueid, fileTags.length, save]);
    return { loading, hasLoaded };
};
