import { useEffect, useState } from "react";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import usePostCustom from "../../../../../../hooks/usePostCustom";

export const useGetWorkflowData = ({ uniqueid }: { uniqueid: string | null }) => {
    const requests = {
        getemailmodeloptions:                   usePostCustom("workflows/getemailmodeloptions?noTemplateVars=true"),
        getavailableusersoptions:               usePostCustom("workflows/getavailableusersoptions?noTemplateVars=true"),
        getavailablegroupsoptions:              usePostCustom("workflows/getavailablegroupsoptions?noTemplateVars=true"),
        getavailablemodeloptions:               usePostCustom("workflows/getavailablemodeloptions?noTemplateVars=true"),
        getavailablerelationoptions:            usePostCustom("workflows/getavailablerelationoptions?noTemplateVars=true"),
        getavailablemacrooptions:               usePostCustom("workflows/getavailablemacrooptions?noTemplateVars=true"),
        getavailableworkflowptions:             usePostCustom("workflows/getavailableworkflowptions?noTemplateVars=true"),
        getavailablestatusoptions:              usePostCustom("workflows/getavailablestatusoptions?noTemplateVars=true"),
        getavailableimpegnitypeoptions:         usePostCustom("workflows/getavailableimpegnitypeoptions?noTemplateVars=true"),
        getavailabledocumentmodelsoptions:      usePostCustom("workflows/getavailabledocumentmodelsoptions?noTemplateVars=true"),
        getavailableroleoptions:                usePostCustom("workflows/getavailableroleoptions?noTemplateVars=true"),
        getavailablechangearchivefieldsoptions: usePostCustom("/workflows/get-change-archive-fields-options?noTemplateVars=true"),
        getjson:                                usePostCustom("workflows/getjson?noTemplateVars=true"),
        getavailabletemplates:                  useGetCustom("workflows/getavailabletemplates?noTemplateVars=true"),
    };

    const [allLoading, setAllLoading] = useState(true);
    const [allLoaded, setAllLoaded] = useState(false);

    useEffect(() => {
        const fetchAllData = async () => {
            setAllLoading(true);
            setAllLoaded(false);

            await Promise.all(
                Object.values(requests).map((request) => 
                    request === requests.getjson 
                        ? request.doFetch(true, { uniqueid }) 
                        : request.doFetch(true)
                )
            );

            setAllLoading(false);
            setAllLoaded(true);
        };

        fetchAllData();
    }, [uniqueid]);

    return {
        requests,
        allLoading,
        allLoaded,
    };
};
