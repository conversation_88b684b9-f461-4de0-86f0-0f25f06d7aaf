import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAlignJustify } from "@fortawesome/free-solid-svg-icons";
import { Details } from "./Details";
import { Button, Stack } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { Toolbar } from "@vapor/react-custom";
import { Rete, useStore } from "./Rete/Rete";
import { CONFIG } from "./Rete/constants";
import { addNewNode } from "./Rete/editor";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";

const BlocksMenu = () => {
    const { t } = useTranslation();

    return (
        <Stack gap={1}>
            {Object.values(CONFIG)
                .filter(node => node.addNodeLabel !== "")
                .map(button => (
                    <Button
                        onClick={() => addNewNode(button.nodeLabel)}
                        key={button.key}
                        variant="text"
                        sx={{ width: "14vw", justifyContent: "flex-start" }}
                        startIcon={<FontAwesomeIcon icon={faAlignJustify} />}>
                        <Typography noWrap>{t(button.addNodeLabel)}</Typography>
                    </Button>
                ))}
        </Stack>
    );
};

export const WorkflowUpdate = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const uniqueid = searchParams.get("uniqueid");

    const isUpdate = uniqueid !== null;
    const { setSaveState, setCenter } = useStore();
    const deleteRequest = usePostCustom("workflows/delete?noTemplateVars=true");

    const goBack = () => {
        setSaveState(false);
        useStore.getState().setEditorState(undefined);
        useStore.getState().setEditorData(null);
        navigate("/workflows");
    };

    useEffect(() => {
        if (deleteRequest.hasLoaded) {
            goBack();
        }
    }, [deleteRequest.hasLoaded]);

    const handleDelete = () => {
        if (uniqueid) {
            deleteRequest.doFetch(true, { uniqueid: uniqueid });
        }
    };

    const [deleteOpen, setDeleteOpen] = useState(false);

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack
                            direction="row"
                            pr={6}
                            gap={1}>
                            <Button onClick={goBack}>{t("Annulla")}</Button>
                            {isUpdate && (
                                <Button
                                    color="error"
                                    onClick={() => setDeleteOpen(true)}>
                                    {t("Elimina")}
                                </Button>
                            )}
                            <ConfirmModal
                                open={deleteOpen}
                                handleDecline={() => setDeleteOpen(false)}
                                handleAgree={() => {
                                    setDeleteOpen(false);
                                    handleDelete();
                                }}
                                decline={t("Annulla")}
                                agree={t("Conferma")}
                                confirmText={t(
                                    "Eliminare definitivamente il workflow?"
                                )}
                                title={t("Elimina workflow")}
                                colorConfirmButton="error"
                            />
                            <Button
                                variant="contained"
                                onClick={() => setSaveState(true)}>
                                {isUpdate ? t("Modifica") : t("Crea Modello")}
                            </Button>
                        </Stack>
                    }
                />
            }>
            <PageTitle
                title={isUpdate ? t("Modifica Workflow") : t("Nuovo Workflow")}
                pathToPrevPage="/workflows"
            />
            <VaporPage.Section>
                <Details />
            </VaporPage.Section>
            <VaporPage.Section>
                <Stack
                    direction="row"
                    justifyContent="end"
                    justifyItems="end"
                    p={1}>
                    <Button
                        variant="outlined"
                        onClick={() => setCenter(true)}>
                        {t("Ricentra")}
                    </Button>
                </Stack>
                <Stack direction="row">
                    <BlocksMenu />
                    <Rete />
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
