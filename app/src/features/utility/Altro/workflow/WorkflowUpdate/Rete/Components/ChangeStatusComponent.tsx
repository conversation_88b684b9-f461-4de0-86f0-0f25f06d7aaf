import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { ChangeStatusParams } from "../interfaces";
import { useStore } from "../Rete";
import { useState, useEffect } from "react";

export class ChangeStatusComponentControl extends ClassicPreset.Control {
    constructor(public data: ChangeStatusParams) {
        super();
    }

    setValue(data: ChangeStatusParams) {
        this.data = data;
    }
}

const defaultOptions: ChangeStatusParams = {
    statusId: "",
};

export const CustomChangeStatusComponentInputs = (props: {
    data: ChangeStatusComponentControl;
}) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<ChangeStatusParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof ChangeStatusParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Stato}</InputLabel>
                    <Select
                        value={componentState.statusId}
                        onChange={handleChange("statusId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>
                            {t["Sposta in sollecito"]}
                        </MenuItem>
                        <MenuItem value={2}>
                            {t["Rimuovi da sollecito"]}
                        </MenuItem>
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createChangeStatusComponent =
    (socket: Sockets) => (data?: ChangeStatusParams) => {
        const node = new ChangeStatus(socket, data);
        return node;
    };

export class ChangeStatus extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: ChangeStatusParams) {
        const t = useStore.getState().translations;
        super(CONFIG.CHANGE_STATUS.nodeLabel);
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t.Aziona)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui)
        );
        this.addControl(
            "status",
            new ChangeStatusComponentControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new ChangeStatus(sockets);
    }
}
