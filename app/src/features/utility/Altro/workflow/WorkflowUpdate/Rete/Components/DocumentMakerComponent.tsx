import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { useState, useEffect } from "react";
import { sockets, Sockets } from "../editor";
import { DocumentMakerParams } from "../interfaces";
import { useStore } from "../Rete";

export class DocumentMakerComponentControl extends ClassicPreset.Control {
    constructor(public data: DocumentMakerParams) {
        super();
    }

    setValue(data: DocumentMakerParams) {
        this.data = data;
    }
}

const defaultOptions: DocumentMakerParams = {
    documentType: "",
};

export const CostumDocumentMakerComponentInputs = (props: {
    data: DocumentMakerComponentControl;
}) => {
    const t = useStore.getState().translations;
    const availabletemplates =
        useStore.getState().componentOptions?.getavailabletemplates ?? [];
    const [componentState, setComponentState] = useState<DocumentMakerParams>(
        props.data.data ? props.data.data : defaultOptions
    );
    const handleChange = (field: keyof DocumentMakerParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Modello}</InputLabel>
                    <Select
                        value={componentState.documentType}
                        onChange={handleChange("documentType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availabletemplates &&
                            availabletemplates.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.name}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createDocumentMakerComponent =
    (socket: Sockets) => (data?: DocumentMakerParams) => {
        const node = new DocumentMaker(socket, data);
        return node;
    };

export class DocumentMaker extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: DocumentMakerParams) {
        const t = useStore.getState().translations;
        super(CONFIG.DOCUMENTMAKER.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Action, false)
        );
        this.addOutput(
            "send_doc",
            new ClassicPreset.Output(socket.string, t.Trasmetti)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore)
        );

        this.addControl(
            "email",
            new DocumentMakerComponentControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new DocumentMaker(sockets);
    }
}
