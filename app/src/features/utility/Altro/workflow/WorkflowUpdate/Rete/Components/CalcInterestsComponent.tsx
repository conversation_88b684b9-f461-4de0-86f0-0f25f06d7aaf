import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack } from "@vapor/react-material";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { CalcInterestsParams } from "../interfaces";
import { useStore } from "../Rete";
import { useState, useEffect } from "react";

export class CalcInterestsComponentControl extends ClassicPreset.Control {
    constructor(public data: CalcInterestsParams) {
        super();
    }
    setValue(data: CalcInterestsParams) {
        this.data = data;
    }
}

const defaultOptions: CalcInterestsParams = {
    interestsType: "",
    warningsType: "",
};

export const CustomCalcInterestsComponentInputs = (props: {
    data: CalcInterestsComponentControl;
}) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<CalcInterestsParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof CalcInterestsParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t["Tipologia interesi"]}</InputLabel>
                    <Select
                        value={componentState.interestsType}
                        onChange={handleChange("interestsType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>
                            {t["Interessi personali"]}
                        </MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Tipo sollecito"]}</InputLabel>
                    <Select
                        value={componentState.warningsType}
                        onChange={handleChange("warningsType")}>
                        <MenuItem value={0}>{t["Nessun tipo"]}</MenuItem>
                        <MenuItem value={1}>{t["Mora e solleciti"]}</MenuItem>
                        <MenuItem value={2}>{t["Diffida"]}</MenuItem>
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createCalcInterestsComponent =
    (socket: Sockets) => (data?: CalcInterestsParams) => {
        const node = new CalcInterest(socket, data);
        return node;
    };

export class CalcInterest extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: CalcInterestsParams) {
        const t = useStore.getState().translations;
        super(CONFIG.CALC_INTERESTS.nodeLabel);
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t.Aziona)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui)
        );
        this.addControl(
            "status",
            new CalcInterestsComponentControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new CalcInterest(sockets);
    }
}
