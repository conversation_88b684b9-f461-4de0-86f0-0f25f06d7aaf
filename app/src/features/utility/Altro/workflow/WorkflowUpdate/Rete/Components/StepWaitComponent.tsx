import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack, TextField, VaporThemeProvider } from "@vapor/v3-components";
import { useEffect, useState } from "react";
import { sockets, Sockets } from "../editor";
import { StepWaitParams } from "../interfaces";
import { useStore } from "../Rete";

export class StepWaitControl extends ClassicPreset.Control {
    constructor(public data: StepWaitParams) {
        super();
    }

    setValue(data: StepWaitParams) {
        this.data = data;
    }
}

const defaultOptions: StepWaitParams = {
    linked_key: "",
};

export const CustomStepWaitInputs = (props: { data: StepWaitControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<StepWaitParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof StepWaitParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <TextField
                    label={t["Chiave link"]}
                    value={componentState.linked_key}
                    onChange={handleChange("linked_key")}
                    placeholder={t["Parola chiave"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createStepWaitComponent =
    (socket: Sockets) => (data?: StepWaitParams) => {
        const node = new StepWait(socket, data);

        return node;
    };

export class StepWait extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: StepWaitParams) {
        const t = useStore.getState().translations;
        super(CONFIG.STEPWAIT.nodeLabel);
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui, true)
        );
        this.addInput(
            "obtained",
            new ClassicPreset.Input(socket.act, t["Prendi dati"], true)
        );
        this.addInput(
            "ignore",
            new ClassicPreset.Input(socket.act, t["Ignora dati"], true)
        );
        this.addControl(
            "stepwait",
            new StepWaitControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new StepWait(sockets);
    }
}
