import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { sockets, Sockets } from "../editor";
import { BeginParams } from "../interfaces";
import { useStore } from "../Rete";

export const createBeginNode = (socket: Sockets) => (data?: BeginParams) => {
    const node = new Begin(socket, data);
    return node;
};

export class Begin extends ClassicPreset.Node {
    constructor(socket: Sockets, _data?: BeginParams) {
        const t = useStore.getState().translations;
        super(CONFIG.BEGIN_NODE.nodeLabel);
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.iniza, false)
        );
    }
    clone() {
        return new Begin(sockets);
    }
}
