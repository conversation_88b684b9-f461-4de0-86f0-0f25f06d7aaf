import { translated, CONFIG } from "./constants";

export type NodeName = (typeof CONFIG)[keyof typeof CONFIG]["key"];

export type NODE_TYPE = keyof typeof CONFIG;

export type NODE_LABEL = (typeof CONFIG)[keyof typeof CONFIG]["nodeLabel"];

export type ADD_NODE_LABEL =
    (typeof CONFIG)[keyof typeof CONFIG]["addNodeLabel"];

export interface EditorState {
    State: EditorData;
}

export type TranslatedType = Record<keyof typeof translated, string>;

type keys =
    | "getemailmodeloptions"
    | "getavailableusersoptions"
    | "getavailablegroupsoptions"
    | "getavailablemodeloptions"
    | "getavailablerelationoptions"
    | "getavailablemacrooptions"
    | "getavailableworkflowptions"
    | "getavailablestatusoptions"
    | "getavailableimpegnitypeoptions"
    | "getavailabledocumentmodelsoptions"
    | "getavailableroleoptions"
    | "getavailablechangearchivefieldsoptions"
    | "getavailabletemplates";

export interface StoreState {
    editorState: EditorState | undefined;
    componentOptions: Record<keys, any> | undefined;
    setComponentOptions: (data: any) => void;
    translations: TranslatedType;
    setEditorState: (value: EditorState | undefined) => void;
    setTranslations: (translations: TranslatedType) => void;
    saveState: boolean;
    setSaveState: (value: boolean) => void;
    editorData: any;
    setEditorData: (newData: any) => void;
    center: boolean;
    setCenter: (value: boolean) => void;
    isUpdate: boolean;
    setIsUpdate: (value: boolean) => void;
}

type ConnectionData = {
    data: Record<string, any>;
};

type OutputConnection = {
    node: number;
    input: string;
} & ConnectionData;

type InputConnection = {
    node: number;
    output: string;
} & ConnectionData;

type Inputs = {
    [inputName: string]: {
        connections: InputConnection[];
    };
};

type Outputs = {
    [outputName: string]: {
        connections: OutputConnection[];
    };
};

type Position = [number, number];

type Node = {
    id: number;
    data: Record<string, any>;
    inputs: Inputs;
    outputs: Outputs;
    position: Position;
    name: string;
};

type Nodes = Record<string, Node>;

export type EditorData = {
    id: string;
    nodes: Nodes;
};

export interface EmailSenderParams {
    userEmailFrom: string;
    targetReceiverType: string;
    messageTitle: string;
    modelId: string;
    messageBody: string;
}

export interface AddGroupParams {
    groupId: string;
}

export interface AddUserSubjectParams {
    userId: string;
    userRelation: string;
    reserveFile: boolean;
}

export interface BeginParams {}

export interface CalcInterestsParams {
    interestsType: string;
    warningsType: string;
}

export interface ChangeStatusParams {
    statusId: string;
}

export interface TimeCheckParams {
    timeblockType: string;
    timeDayQuantity: string;
    timeHourQuantity: string;
    timeMinQuantity: string;
}
export interface CounterParams {
    giri: string;
}
export interface CreateModelParams {
    model: string;
    ruoloCliente: string;
    ruoloControparte: string;
    ruoloAvversario: string;
    ruoloAltro: string;
}
export interface CreateWarningParams {
    actionType: string;
    printType: string;
}
export interface DocumentMakerParams {
    documentType: string;
}
export interface EndNodeParams {}
export interface EventTriggerAddDocumentParams {}
export interface EventTriggerAddSubjectParams {}
export interface EventTriggerExpireDeadlineParams {
    impegnoType: string;
}
export interface EventTriggerChangeArchiveFieldsParams {
    archivioField: number;
}
export interface InsertEventParams {
    oggetto: string;
    avvisaprima: string;
    durata: string;
    timeblockType: string;
    timeDayQuantity: string;
    timeHour: string;
    tipologiaImpegno: string;
    relazioneIntestatario: string;
    deadlineEvasa: boolean;
    deadlineNonevadere: boolean;
    deadlineImportant: boolean;
    deadlineId: boolean;
    deadlineVisible: boolean;
}
export interface MacroParams {
    macroId: string;
    assignedId: string;
    timeDayQuantity: string;
    evadifestivi: boolean;
}
export interface NotifParams {
    notificationChannelType: string;
    userIdToNotify: string;
    messageBody: string;
}
export interface StatusUpdaterParams {
    statusId: string;
}

export interface StepWaitParams {
    linked_key: string;
}

export interface TaskParams {
    title: string;
    descrizione: string;
    assignedId: string;
}

export interface TimeParams {
    timeblockType: number;
    timeDayQuantity: number;
    timeHourQuantity: number;
    timeMinQuantity: number;
}

export interface WorkflowLauncherParams {
    workflowId: string;
}
