import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useState, useEffect } from "react";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { sockets, Sockets } from "../editor";
import { AddGroupParams } from "../interfaces";
import { useStore } from "../Rete";

export class AddGroupControl extends ClassicPreset.Control {
    constructor(public data: AddGroupParams) {
        super();
    }

    setValue(data: AddGroupParams) {
        this.data = data;
    }
}

const defaultOptions: AddGroupParams = {
    groupId: "",
};

export const CustomAddGroupInputs = (props: { data: AddGroupControl }) => {
    const t = useStore.getState().translations;
    const availablegroupsoptions =
        useStore.getState().componentOptions?.getavailablegroupsoptions ?? [];
    const [componentState, setComponentState] = useState<AddGroupParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof AddGroupParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Gruppo}</InputLabel>
                    <Select
                        value={componentState.groupId}
                        onChange={handleChange("groupId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availablegroupsoptions &&
                            availablegroupsoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.name}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createAddGroupComponent =
    (socket: Sockets) => (data?: AddGroupParams) => {
        const node = new AddGroup(socket, data);
        return node;
    };

export class AddGroup extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: AddGroupParams) {
        const t = useStore.getState().translations;
        super(CONFIG.ADD_GROUP.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Action, true)
        );

        this.addControl(
            t["add group"],
            new AddGroupControl(data ?? defaultOptions)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui, true)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore, true)
        );
    }
    clone() {
        return new AddGroup(sockets);
    }
}
