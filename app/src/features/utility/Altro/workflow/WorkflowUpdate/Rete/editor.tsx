import { createRoot } from "react-dom/client";
import { ClassicPreset, GetSchemes, NodeEditor } from "rete";
import { AreaPlugin, AreaExtensions } from "rete-area-plugin";
import {
    ConnectionPlugin,
    Presets as ConnectionPresets,
} from "rete-connection-plugin";
import {
    ContextMenuPlugin,
    Presets as ContextMenuPresets,
} from "rete-context-menu-plugin";
import {
    HistoryExtensions,
    HistoryPlugin,
    Presets as HistoryPresets,
} from "rete-history-plugin";
import { Presets, ReactArea2D, ReactPlugin } from "rete-react-plugin";
import * as Components from "./Components";
import { getControlComponent } from "./Presets";
import {
    actSocket,
    argumentsSocket,
    btnArgumentsSocket,
    decisionInSocket,
    decisionOutSocket,
    responseSocket,
    stringSocket,
} from "./Sockets";
import { NODE_LABEL } from "./interfaces";
import { menuItems, CONFIG } from "./constants";
import { useStore } from "./Rete";

export const sockets = {
    act: new actSocket(),
    argument: new argumentsSocket(),
    btnArgument: new btnArgumentsSocket(),
    decisionIn: new decisionInSocket(),
    decisionOut: new decisionOutSocket(),
    response: new responseSocket(),
    string: new stringSocket(),
};

export type Sockets = typeof sockets;

const nodeTypes = {
    [CONFIG.BEGIN_NODE.nodeLabel]: Components.createBeginNode(sockets),
    [CONFIG.EMAIL.nodeLabel]: Components.createEmailSenderComponent(sockets),
    [CONFIG.END_NODE.nodeLabel]: Components.createEndNode(sockets),
    [CONFIG.TIME_NODE.nodeLabel]: Components.createTimeComponent(sockets),
    [CONFIG.INSERTEVENT.nodeLabel]:
        Components.createInsertEventComponent(sockets),
    [CONFIG.DOCUMENTMAKER.nodeLabel]:
        Components.createDocumentMakerComponent(sockets),
    [CONFIG.NOTIFICATION.nodeLabel]: Components.createNotifComponent(sockets),
    [CONFIG.ADD_GROUP.nodeLabel]: Components.createAddGroupComponent(sockets),
    [CONFIG.ADD_USER_SUBJECT.nodeLabel]:
        Components.createAddUserSubjectComponent(sockets),
    [CONFIG.STEPWAIT.nodeLabel]: Components.createStepWaitComponent(sockets),
    [CONFIG.CALC_INTERESTS.nodeLabel]:
        Components.createCalcInterestsComponent(sockets),
    [CONFIG.CHANGE_STATUS.nodeLabel]:
        Components.createChangeStatusComponent(sockets),
    [CONFIG.CREATE_WARNING.nodeLabel]:
        Components.createWarningComponent(sockets),
    [CONFIG.TRIGGER_EXPIRE_DEADLINE.nodeLabel]:
        Components.createEventTriggerExpireDeadlineComponent(sockets),
    [CONFIG.TRIGGER_ADD_DOCUMENT.nodeLabel]:
        Components.createEventTriggerAddDocumentComponent(sockets),
    [CONFIG.TRIGGER_ADD_SUBJECT.nodeLabel]:
        Components.createEventTriggerAddSubjectComponent(sockets),
    [CONFIG.TRIGGER_CHANGE_ARCHIVE_FIELDS.nodeLabel]:
        Components.createEventTriggerChangeArchiveFieldsComponent(sockets),
    [CONFIG.CREATE_MODEL.nodeLabel]: Components.createModelComponent(sockets),
    [CONFIG.STATUS_UPDATER.nodeLabel]:
        (data?: any) => Components.createStatusUpdaterComponent(sockets, data),
    [CONFIG.WORKFLOW_LAUNCHER.nodeLabel]:
        Components.createWorkflowLauncherComponent(sockets),
    [CONFIG.MACRO_STARTER.nodeLabel]:
        Components.createMacroStarterComponent(sockets),
    [CONFIG.TASK.nodeLabel]: Components.createTaskComponent(sockets),
    [CONFIG.CHECK_TIME.nodeLabel]: Components.createCheckTimeComponent(sockets),
    [CONFIG.COUNTER.nodeLabel]: Components.createCounterComponent(sockets),
};

interface NodeConnections {
    sourceNodeId: string;
    sourceOutput: string;
    targetNodeId: string;
    targetInput: string;
}

function extractWorkflowConnections(workflow: any): NodeConnections[] {
    const connections: NodeConnections[] = [];

    Object.values(workflow.nodes).forEach((node: any) => {
        if (node.outputs) {
            Object.entries(node.outputs).forEach(
                ([outputName, outputData]: [string, any]) => {
                    if (
                        outputData.connections &&
                        outputData.connections.length > 0
                    ) {
                        outputData.connections.forEach((connection: any) => {
                            connections.push({
                                sourceNodeId: node.id,
                                sourceOutput: outputName,
                                targetNodeId: connection.node,
                                targetInput: connection.input,
                            });
                        });
                    }
                }
            );
        }
    });

    return connections;
}

function getControllerData(data: ClassicPreset.Node) {
    const controls = Object.values(data.controls);
    return controls.length > 0 ? (controls[0] as unknown as { data: unknown }).data : null;
}

async function importGraph(data: any, editor: NodeEditor<Schemes>, area: any) {
    if (!data || !data.nodes || Object.keys(data.nodes).length === 0) {
        const beginNode = nodeTypes[CONFIG.BEGIN_NODE.nodeLabel]();
        await editor.addNode(beginNode);
        await area.translate(beginNode.id, { x: 250, y: 250 });
        return;
    }

    const nodeCount = Object.keys(data.nodes).length;
    const isUpdate = useStore.getState().isUpdate;

    if (nodeCount > 1 && !isUpdate) {
        let foundBeginNode = false;

        for (const nodeIdStr in data.nodes) {
            const newNode = data.nodes[nodeIdStr];
            if (newNode.name === CONFIG.BEGIN_NODE.nodeLabel) {
                const nodeCreator = nodeTypes[CONFIG.BEGIN_NODE.nodeLabel];
                const node = nodeCreator(newNode.data);
                node.id = nodeIdStr;
                (node as unknown as { data: unknown }).data = newNode.data;
                await editor.addNode(node);
                await area.translate(node.id, { x: 250, y: 250 });
                foundBeginNode = true;
                break;
            }
        }

        if (!foundBeginNode) {
            const beginNode = nodeTypes[CONFIG.BEGIN_NODE.nodeLabel]();
            await editor.addNode(beginNode);
            await area.translate(beginNode.id, { x: 250, y: 250 });
        }

        return;
    }

    const createdNodes = new Map();
    for (const nodeIdStr in data.nodes) {
        const newNode = data.nodes[nodeIdStr];
        const nodeName = newNode.name as NODE_LABEL;

        if (!nodeTypes[nodeName]) {
            continue;
        }

        const nodeCreator = nodeTypes[nodeName];
        const node = nodeCreator(newNode.data);
        node.id = nodeIdStr;
        (node as unknown as { data: unknown }).data = newNode.data;
        await editor.addNode(node);
        createdNodes.set(nodeIdStr, node);

        if (
            newNode.position &&
            Array.isArray(newNode.position) &&
            newNode.position.length >= 2
        ) {
            await area.translate(node.id, {
                x: newNode.position[0],
                y: newNode.position[1],
            });
        }
    }

    const connections = extractWorkflowConnections(data);
    for (const connection of connections) {
        const sourceNode = editor.getNode(connection.sourceNodeId);
        const targetNode = editor.getNode(connection.targetNodeId);

        if (sourceNode && targetNode) {
            try {
                await editor.addConnection(
                    new ClassicPreset.Connection(
                        sourceNode,
                        connection.sourceOutput,
                        targetNode,
                        connection.targetInput
                    )
                );
            } catch (error) {
                console.error("Failed to create connection:", error);
            }
        }
    }

    if (editor.getNodes().length > 0) {
        AreaExtensions.zoomAt(area, editor.getNodes());
    } else {
        const beginNode = nodeTypes[CONFIG.BEGIN_NODE.nodeLabel]();
        await editor.addNode(beginNode);
        await area.translate(beginNode.id, { x: 250, y: 250 });
    }
}

class Connection<N extends ClassicPreset.Node> extends ClassicPreset.Connection<N, N> {}

export type Schemes = GetSchemes<ClassicPreset.Node, Connection<ClassicPreset.Node>>;

type AreaExtra = ReactArea2D<any>;

export const editor = new NodeEditor<Schemes>();
const connection = new ConnectionPlugin<Schemes, AreaExtra>();

export const addNewNode = async (key: NODE_LABEL) => {
    const newNode = nodeTypes[key]();
    editor.addNode(newNode);
};
function transformNodesAndConnections(
    nodes: ClassicPreset.Node[],
    connections: Connection<ClassicPreset.Node>[],
    area: { nodeViews: Map<string, { position: { x: number; y: number } }> }
) {
    const getNodeConnections = (nodeId: string) => {
        const inputs = connections
            .filter(conn => conn.target === nodeId)
            .reduce((acc, conn) => {
                const inputName = conn.targetInput;
                if (!acc[inputName]) {
                    acc[inputName] = { connections: [] };
                }
                acc[inputName].connections.push({
                    node: conn.source,
                    output: conn.sourceOutput,
                    data: {},
                });
                return acc;
            }, {} as Record<string, { connections: Array<{ node: string; output: string; data: {} }> }>);

        const outputs = connections
            .filter(conn => conn.source === nodeId)
            .reduce((acc, conn) => {
                const outputName = conn.sourceOutput;
                if (!acc[outputName]) {
                    acc[outputName] = { connections: [] };
                }
                acc[outputName].connections.push({
                    node: conn.target,
                    input: conn.targetInput,
                    data: {},
                });
                return acc;
            }, {} as Record<string, { connections: Array<{ node: string; input: string; data: {} }> }>);

        return [inputs, outputs];
    };

    const nodeIds = nodes.map(node => node.id);

    const controllerData = Object.fromEntries(
        nodes.map(node => [node.id, getControllerData(node)])
    );

    const nodesPositions = nodeIds.map(id => {
        const position = [
            area.nodeViews.get(id)?.position.x,
            area.nodeViews.get(id)?.position.y,
        ];
        return { id, position };
    });

    const nodeNames = Object.fromEntries(
        nodes.map(item => [item.id, item.label.replace(" ", "+")])
    );

    const initialData = nodeIds.reduce((merged: any, id: string) => {
        merged[id] = {
            id,
            name: nodeNames[id],
            data: controllerData[id] || {},
            position: nodesPositions.find(pos => pos.id === id)?.position,
            inputs: {},
            outputs: {},
        };
        return merged;
    }, {});

    const result: Record<string, any> = {};
    for (const [nodeId, nodeValues] of Object.entries(initialData)) {
        const [inputs, outputs] = getNodeConnections(nodeId);
        result[nodeId] = {
            ...(nodeValues as any),
            inputs,
            outputs,
        };
    }

    return { id: "demo@0.1.0", nodes: result };
}

export const createEditor = async (container: HTMLElement) => {
    const existingNodes = editor.getNodes();
    if (existingNodes.length > 0) {
        existingNodes.map(node => node.id).forEach(id => editor.removeNode(id));
    }

    const area = new AreaPlugin<Schemes, AreaExtra>(container);
    const render = new ReactPlugin<Schemes, AreaExtra>({ createRoot });

    const editorState = useStore.getState().editorState?.State;
    const setEditorData = useStore.getState().setEditorData;
    const setCenter = useStore.getState().setCenter;
    const isUpdate = useStore.getState().isUpdate;

    useStore.subscribe(
        state => state.saveState,
        saveState => {
            if (saveState) {
                const nodes = editor.getNodes();
                const visibleNodes = nodes.filter(node => {
                    return area.nodeViews.has(node.id);
                });

                const editorData = transformNodesAndConnections(
                    visibleNodes,
                    editor.getConnections(),
                    area
                );

                setEditorData(editorData);
            }
        }
    );

    useStore.subscribe(
        state => state.center,
        async center => {
            if (center) {
                await AreaExtensions.zoomAt(area, editor.getNodes(), {
                    scale: 0.9,
                });
                setCenter(false);
            }
        }
    );
    const history = new HistoryPlugin<Schemes>();
    HistoryExtensions.keyboard(history);
    history.addPreset(HistoryPresets.classic.setup());

    AreaExtensions.selectableNodes(area, AreaExtensions.selector(), {
        accumulating: AreaExtensions.accumulateOnCtrl(),
    });

    const contextMenu = new ContextMenuPlugin<Schemes>({
        items: ContextMenuPresets.classic.setup(menuItems as any),
    });

    render.addPreset(Presets.contextMenu.setup() as any);

    render.addPreset(
        Presets.classic.setup({
            customize: {
                control(data) {
                    return getControlComponent(data.payload);
                },
            },
        })
    );

    connection.addPreset(ConnectionPresets.classic.setup());

    editor.use(area);
    area.use(connection);
    area.use(render);
    area.use(history);

    area.use(contextMenu as any);

    const remainingNodes = editor.getNodes();
    if (remainingNodes.length > 0) {
        remainingNodes
            .map(node => node.id)
            .forEach(id => editor.removeNode(id));
    }

    if (editorState !== undefined && isUpdate) {
        await importGraph(editorState, editor, area);
    } else {
        const beginNode = nodeTypes[CONFIG.BEGIN_NODE.nodeLabel]();
        await editor.addNode(beginNode);
        await area.translate(beginNode.id, { x: 250, y: 250 });
        await AreaExtensions.zoomAt(area, [beginNode]);
    }

    return {
        destroy: () => area.destroy(),
    };
};
