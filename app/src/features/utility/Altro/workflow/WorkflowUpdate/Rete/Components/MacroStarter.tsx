import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useState } from "react";
import { sockets, Sockets } from "../editor";
import { MacroParams } from "../interfaces";
import { useStore } from "../Rete";

export class MacroStarterControl extends ClassicPreset.Control {
    constructor(public data?: MacroParams) {
        super();
    }

    setValue(data: MacroParams) {
        this.data = data;
    }
}

const defaultOptions: MacroParams = {
    macroId: "",
    assignedId: "",
    timeDayQuantity: "",
    evadifestivi: false,
};

export const CustomMacroStarterInputs = (props: {
    data: MacroStarterControl;
}) => {
    const t = useStore.getState().translations;
    const availablemacrooptions =
        useStore.getState().componentOptions?.getavailablemacrooptions ?? [];
    const availableusersoptions =
        useStore.getState().componentOptions?.getavailableusersoptions ?? [];
    const [componentState, setComponentState] = useState<MacroParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof MacroParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
        props.data.setValue(updatedState);
    };
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t["Modello output"]}</InputLabel>
                    <Select
                        value={componentState.macroId}
                        onChange={handleChange("macroId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availablemacrooptions &&
                            availablemacrooptions.map((value: any) => (
                                <MenuItem key={value.id} value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Utente assegnato"]}</InputLabel>
                    <Select
                        value={componentState.assignedId}
                        onChange={handleChange("assignedId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availableusersoptions &&
                            availableusersoptions.map((value: any) => (
                                <MenuItem key={value.id} value={value.id}>
                                    {value.nomeutente}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControlLabel
                    label={t["No festivi"]}
                    control={
                        <Checkbox
                            checked={componentState.evadifestivi}
                            onChange={(e) => {
                                const updatedState = {
                                    ...componentState,
                                    evadifestivi: e.target.checked
                                };
                                setComponentState(updatedState);
                                props.data.setValue(updatedState);
                            }}
                        />
                    }
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createMacroStarterComponent =
    (socket: Sockets) => (data?: MacroParams) => {
        const node = new MacroStarter(socket, data);
        return node;
    };

export class MacroStarter extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: MacroParams) {
        const t = useStore.getState().translations;
        super(CONFIG.MACRO_STARTER.nodeLabel);
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t["Avvia macro"])
        );
        this.addControl(
            "macro",
            new MacroStarterControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new MacroStarter(sockets);
    }
}
