import { ClassicPreset } from "rete";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextareaAutosize,
    TextField,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { CONFIG } from "../constants";
import { useEffect, useState } from "react";
import { sockets, Sockets } from "../editor";
import { EmailSenderParams } from "../interfaces";
import { useStore } from "../Rete";

export class EmailSenderComponentControl extends ClassicPreset.Control {
    constructor(public data: EmailSenderParams) {
        super();
    }

    setValue(data: EmailSenderParams) {
        this.data = data;
    }
}

const defaultOptions: EmailSenderParams = {
    messageBody: "",
    messageTitle: "",
    modelId: "",
    targetReceiverType: "",
    userEmailFrom: "",
};

export const CustomEmailSenderComponentInputs = (props: {
    data: EmailSenderComponentControl;
}) => {
    const t = useStore.getState().translations;
    const emailmodeloptions =
        useStore.getState().componentOptions?.getemailmodeloptions ?? [];
    const [componentState, setComponentState] = useState<EmailSenderParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof EmailSenderParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };
        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Da}</InputLabel>
                    <Select
                        value={componentState.userEmailFrom}
                        onChange={handleChange("userEmailFrom")}>
                        <MenuItem value={"0"}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={"1"}>{t.Responsabile}</MenuItem>
                        <MenuItem value={"2"}>{t.Cointestatario}</MenuItem>
                        <MenuItem value={"3"}>{t.Collaboratore}</MenuItem>
                        <MenuItem value={"30"}>{t["Utente interno"]}</MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t.A}</InputLabel>
                    <Select
                        value={componentState.targetReceiverType}
                        onChange={handleChange("targetReceiverType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>{t.Cliente}</MenuItem>
                        <MenuItem value={2}>{t.Controparte}</MenuItem>
                        <MenuItem value={3}>{t.Avversario}</MenuItem>
                        <MenuItem value={4}>{t.Altro}</MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    label={t.Titolo}
                    value={componentState.messageTitle}
                    onChange={handleChange("messageTitle")}
                    placeholder={t["Oggetto email"]}
                />
                <FormControl>
                    <InputLabel>{t.Modello}</InputLabel>
                    <Select
                        value={componentState.modelId}
                        onChange={handleChange("modelId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {emailmodeloptions &&
                            emailmodeloptions?.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <TextareaAutosize
                    onChange={handleChange("messageBody")}
                    minRows={3}
                    value={componentState.messageBody}
                    placeholder={t["Testo aggiuntivo"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createEmailSenderComponent =
    (socket: Sockets) => (data?: EmailSenderParams) => {
        const node = new EmailSender(socket, data);
        return node;
    };

export class EmailSender extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: EmailSenderParams) {
        const t = useStore.getState().translations;
        super(CONFIG.EMAIL.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t["Nessuna azione"], false)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Inviata)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore)
        );
        this.addControl(
            "email",
            new EmailSenderComponentControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new EmailSender(sockets);
    }
}
