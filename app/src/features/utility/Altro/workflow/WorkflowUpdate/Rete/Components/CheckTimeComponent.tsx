import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack, TextField, VaporThemeProvider } from "@vapor/v3-components";
import { useEffect, useState } from "react";
import { sockets, Sockets } from "../editor";
import { TimeCheckParams } from "../interfaces";
import { useStore } from "../Rete";

export class CheckTimeControl extends ClassicPreset.Control {
    constructor(public data: TimeCheckParams) {
        super();
    }

    setValue(data: TimeCheckParams) {
        this.data = data;
    }
}

const defaultOptions: TimeCheckParams = {
    timeblockType: "",
    timeDayQuantity: "",
    timeHourQuantity: "",
    timeMinQuantity: "",
};

export const CustomCheckTimeInputs = (props: { data: CheckTimeControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<TimeCheckParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof TimeCheckParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <TextField
                    onChange={handleChange("timeDayQuantity")}
                    value={componentState.timeDayQuantity}
                    label={t.Giorni}
                    placeholder={t["Max giorni attesa"]}
                />
                <TextField
                    onChange={handleChange("timeHourQuantity")}
                    value={componentState.timeHourQuantity}
                    label={t.Ore}
                    placeholder={t["Max ore attesa"]}
                />
                <TextField
                    onChange={handleChange("timeMinQuantity")}
                    value={componentState.timeMinQuantity}
                    label={t.Minuti}
                    placeholder={t["Max minuti attesa"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createCheckTimeComponent =
    (socket: Sockets) => (data?: TimeCheckParams) => {
        const node = new CheckTime(socket, data);
        return node;
    };

export class CheckTime extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: TimeCheckParams) {
        const t = useStore.getState().translations;
        super(CONFIG.CHECK_TIME.nodeLabel);
        this.addInput(
            "begin",
            new ClassicPreset.Input(socket.string, t.Inizio)
        );
        this.addInput("end", new ClassicPreset.Input(socket.argument, t.Fine));
        this.addControl("time", new CheckTimeControl(data ?? defaultOptions));
        this.addOutput(
            "overtime",
            new ClassicPreset.Output(socket.response, t.OverTime)
        );
        this.addOutput(
            "intime",
            new ClassicPreset.Output(socket.response, t.InTime)
        );
    }
    clone() {
        return new CheckTime(sockets);
    }
}
