import {
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { useState, useEffect } from "react";
import { sockets, Sockets } from "../editor";
import { AddUserSubjectParams } from "../interfaces";
import { useStore } from "../Rete";

export class AddUserSubjectControl extends ClassicPreset.Control {
    constructor(public data: AddUserSubjectParams) {
        super();
    }
    setValue(data: AddUserSubjectParams) {
        this.data = data;
    }
}

const defaultOptions: AddUserSubjectParams = {
    reserveFile: true,
    userId: "",
    userRelation: "",
};

export const CustomAddUserSubjectInputs = (props: {
    data: AddUserSubjectControl;
}) => {
    const t = useStore.getState().translations;
    const availableusersoptions =
        useStore.getState().componentOptions?.getavailableusersoptions ?? [];
    const [componentState, setComponentState] = useState<AddUserSubjectParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange =
        (field: keyof AddUserSubjectParams) => (event: any) => {
            const updatedState = {
                ...componentState,
                [field]: event.target.value,
            };

            setComponentState(updatedState);
            props.data.setValue(updatedState);
        };

    const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const updatedState = {
            ...componentState,
            reserveFile: event.target.checked,
        };

        setComponentState(updatedState);
        props.data.setValue(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Utente}</InputLabel>
                    <Select
                        value={componentState.userId}
                        onChange={handleChange("userId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availableusersoptions &&
                            availableusersoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nomeutente}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t.Relazione}</InputLabel>
                    <Select
                        value={componentState.userRelation}
                        onChange={handleChange("userRelation")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>{t.Cliente}</MenuItem>
                        <MenuItem value={2}>{t.Controparte}</MenuItem>
                        <MenuItem value={3}>{t.Avversario}</MenuItem>
                        <MenuItem value={4}>{t.Altro}</MenuItem>
                    </Select>
                </FormControl>
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={componentState.reserveFile}
                            onChange={handleCheckboxChange}
                        />
                    }
                    label={t["Aggiungi riserva"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createAddUserSubjectComponent =
    (socket: Sockets) => (data?: AddUserSubjectParams) => {
        const node = new AddUserSubject(socket, data);
        return node;
    };

export class AddUserSubject extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: AddUserSubjectParams) {
        const t = useStore.getState().translations;
        super(CONFIG.ADD_USER_SUBJECT.nodeLabel);
        this.addControl(
            t["add subject"],
            new AddUserSubjectControl(data ?? defaultOptions)
        );
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Action, true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui, true)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore, true)
        );
    }
    clone() {
        return new AddUserSubject(sockets);
    }
}
