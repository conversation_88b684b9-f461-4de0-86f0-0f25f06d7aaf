import { sockets } from "./editor";
import * as Components from "./Components"

export const CONFIG = {
    TASK: { key: "TASK", nodeLabel: "Crea Task", addNodeLabel: "" },
    BEGIN_NODE: {
        key: "BEGIN_NODE",
        nodeLabel: "Inizio Workflow",
        addNodeLabel: "",
    },
    EMAIL: {
        key: "EMAIL",
        nodeLabel: "Invio Email",
        addNodeLabel: "Invio Email",
    },
    DOCUMENTMAKER: {
        key: "DOCUMENTMAKER",
        nodeLabel: "Genera Documento",
        addNodeLabel: "Genera Documento",
    },
    NOTIFICATION: {
        key: "NOTIFICATION",
        nodeLabel: "Notifica",
        addNodeLabel: "Notifica",
    },
    INSERTEVENT: {
        key: "INSERTEVENT",
        nodeLabel: "Inserisci Impegno",
        addNodeLabel: "Inserimento Impegno",
    },
    TIME_NODE: {
        key: "TIME_NODE",
        nodeLabel: "Attesa",
        addNodeLabel: "Attesa",
    },
    STEPWAIT: {
        key: "STEPWAIT",
        nodeLabel: "Attendi Ingressi",
        addNodeLabel: "Attendi ingressi",
    },
    ADD_USER_SUBJECT: {
        key: "ADD_USER_SUBJECT",
        nodeLabel: "Aggiungi Utente",
        addNodeLabel: "Aggiungi Soggetto",
    },
    ADD_GROUP: {
        key: "ADD_GROUP",
        nodeLabel: "Riserva Gruppo",
        addNodeLabel: "Riserva Gruppo",
    },
    COUNTER: {
        key: "COUNTER",
        nodeLabel: "Contatore",
        addNodeLabel: "Contatore",
    },
    CHECK_TIME: {
        key: "CHECK_TIME",
        nodeLabel: "Verifica Overtime",
        addNodeLabel: "Verifica Overtime",
    },
    MACRO_STARTER: {
        key: "MACRO_STARTER",
        nodeLabel: "Avvia Macro",
        addNodeLabel: "Avvia Macro",
    },
    WORKFLOW_LAUNCHER: {
        key: "WORKFLOW_LAUNCHER",
        nodeLabel: "Avvia Workflow",
        addNodeLabel: "Avvia Workflow",
    },
    STATUS_UPDATER: {
        key: "STATUS_UPDATER",
        nodeLabel: "Cambia Stato Pratica",
        addNodeLabel: "Cambia Stato",
    },
    CREATE_MODEL: {
        key: "CREATE_MODEL",
        nodeLabel: "Genera Modello",
        addNodeLabel: "Genera Modello",
    },
    CREATE_WARNING: {
        key: "CREATE_WARNING",
        nodeLabel: "Crea Sollecito",
        addNodeLabel: "Crea Sollecito",
    },
    CHANGE_STATUS: {
        key: "CHANGE_STATUS",
        nodeLabel: "Sposta in Solleciti",
        addNodeLabel: "Sposta in Solleciti",
    },
    CALC_INTERESTS: {
        key: "CALC_INTERESTS",
        nodeLabel: "Calcola Interessi",
        addNodeLabel: "Calcola Interessi",
    },
    TRIGGER_ADD_SUBJECT: {
        key: "TRIGGER_ADD_SUBJECT",
        nodeLabel: "Attendi Aggiunta Soggetto",
        addNodeLabel: "Evento Aggiunta Soggetto",
    },
    TRIGGER_ADD_DOCUMENT: {
        key: "TRIGGER_ADD_DOCUMENT",
        nodeLabel: "Attendi Aggiunta Documento",
        addNodeLabel: "Evento Aggiunta Documento",
    },
    TRIGGER_EXPIRE_DEADLINE: {
        key: "TRIGGER_EXPIRE_DEADLINE",
        nodeLabel: "Attendi Impegno Evaso",
        addNodeLabel: "Evento Impegno Evaso",
    },
    TRIGGER_CHANGE_ARCHIVE_FIELDS: {
        key: "TRIGGER_CHANGE_ARCHIVE_FIELDS",
        nodeLabel: "Attendi Cambio Campo Pratica",
        addNodeLabel: "Evento Cambio Campo Pratica",
    },
    END_NODE: {
        key: "END_NODE",
        nodeLabel: "Termina Workflow",
        addNodeLabel: "Termina Workflow",
    },
} as const;

export const translated = {
    "A":"A",
    "action": "action",
    "Action": "Action",
    "add group": "add group",
    "add subject": "add subject",
    "Aggiungi riserva": "Aggiungi riserva",
    "Alert":"Alert",
    "Altro": "Altro",
    "Argomenti":"Argomenti",
    "Attiva": "Attiva",
    "Automatico (il primo inserimento)":"Automatico (il primo inserimento)",
    "Automatico": "Automatico",
    "Avversario": "Avversario",
    "Avvia macro":"Avvia macro",
    "Avvia workflow":"Avvia workflow",
    "Aziona": "Aziona",
    "Azione": "Azione",
    "Campo": "Campo",
    "Cliente": "Cliente",
    "Cointestatario": "Cointestatario",
    "Collaboratore": "Collaboratore",
    "Completato":"Completato",
    "Contagiro": "Contagiro",
    "Contatore": "Contatore",
    "Continua": "Continua",
    "Chiave link":"Chiave link",
    "Controparte": "Controparte",
    "Crea Task": "Crea Task",
    "create model": "create model",
    "Da non evadere": "Da non evadere",
    "Da":"Da",
    "Dal termine del blocco precedente":"Dal termine del blocco precedente",
    "Dall' inizio del workflow": "Dall' inizio del workflow",
    "Dall' inizio di questo bloco": "Dall' inizio di questo bloco",
    "Dall'inizio del workflow":"Dall'inizio del workflow",
    "Data evento calcolate":"Data evento calcolate",
    "Dati creazione": "Dati creazione",
    "Diffida":"Diffida",
    "Durata": "Durata",
    "Email":"Email",
    "Errore": "Errore",
    "Esito Negativo":"Esito Negativo",
    "Esito Positivo":"Esito Positivo",
    "Evaso": "Evaso",
    "Evento Completato": "Evento Completato",
    "Fine": "Fine",
    "Giri": "Giri",
    "Genera": "Genera",
    "Giorni": "Giorni",
    "Gruppo": "Gruppo",
    "Giri prima di uscire":"Giri prima di uscire",
    "Identificativo pratica nell'oggetto":"Identificativo pratica nell'oggetto",
    "Ignora dati":"Ignora dati",
    "Importante": "Importante",
    "iniza": "iniza",
    "Inizio": "Inizio",
    "Interessi personali":"Interessi personali",
    "Intestato a":"Intestato a",
    "InTime": "InTime",
    "Inviata": "Inviata",
    "Minuti": "Minuti",
    "Modello macro":"Modello macro",
    "Modello output":"Modello output",
    "Modello Workflow":"Modello Workflow",
    "Modello": "Modello",
    "Mora e solleciti": "Mora e solleciti",
    "Max giorni attesa": "Max giorni attesa",
    "Max ore attesa": "Max ore attesa",
    "Max minuti attesa": "Max minuti attesa",
    "Nessun tipo":"Nessun tipo",
    "Nessuna azione": "Nessuna azione",
    "Non Stampare":"Non Stampare",
    "No festivi": "No festivi",
    "Notifica": "Notifica",
    "Numero giorni attesa": "Numero giorni attesa",
    "Numero ore attesa": "Numero ore attesa",
    "Numero minuti attesa": "Numero minuti attesa",
    "Parola chiave":"Parola chiave",
    "Oggetto": "Oggetto",
    "Ore": "Ore",
    "OverTime": "OverTime",
    "Oggetto email": "Oggetto email",
    "Prendi dati":"Prendi dati",
    "Prosegui": "Prosegui",
    "Qualsiasi impegno":"Qualsiasi impegno",
    "Relazione intestatari":"Relazione intestatari",
    "Relazione": "Relazione",
    "Responsabile": "Responsabile",
    "Rimuovi da sollecito":"Rimuovi da sollecito",
    "Risposta":"Risposta",
    "Ruolo altro": "Ruolo altro",
    "Ruolo avversario": "Ruolo avversario",
    "Ruolo clienti": "Ruolo clienti",
    "Ruolo controparte": "Ruolo controparte",
    "Sposta in sollecito":"Sposta in sollecito",
    "Stampa":"Stampa",
    "Stato": "Stato",
    "Superato": "Superato",
    "termina": "termina",
    "Tipo sollecito": "Tipo sollecito",
    "Tipologia interesi": "Tipologia interesi",
    "Tipologia":"Tipologia",
    "Titolo": "Titolo",
    "Testo aggiuntivo": "Testo aggiuntivo",
    "Trasmetti": "Trasmetti",
    "Titolo del task": "Titolo del task",
    "Descrizione del task": "Descrizione del task",
    "User":"User",
    "Utente interno": "Utente interno",
    "Utente assegnato": "Utente assegnato",
    "Utente": "Utente",
    "Visibile utenti esterni": "Visibile utenti esterni",
} as const;





export const menuItems = [
    [
        CONFIG.EMAIL.nodeLabel,
        () => new Components.EmailSender(sockets),
    ],
    [
        CONFIG.BEGIN_NODE.nodeLabel,
        () => new Components.Begin(sockets),
    ],
    [
        CONFIG.ADD_GROUP.nodeLabel,
        () => new Components.AddGroup(sockets),
    ],
    [
        CONFIG.ADD_USER_SUBJECT.nodeLabel,
        () => new Components.AddUserSubject(sockets),
    ],
    [
        CONFIG.CALC_INTERESTS.nodeLabel,
        () => new Components.CalcInterest(sockets),
    ],
    [
        CONFIG.CHANGE_STATUS.nodeLabel,
        () => new Components.ChangeStatus(sockets),
    ],
    [
        CONFIG.CHECK_TIME.nodeLabel,
        () => new Components.CheckTime(sockets),
    ],
    [
        CONFIG.COUNTER.nodeLabel,
        () => new Components.Counter(sockets),
    ],
    [
        CONFIG.CREATE_MODEL.nodeLabel,
        () => new Components.CreateModel(sockets),
    ],
    [
        CONFIG.CREATE_WARNING.nodeLabel,
        () => new Components.CreateWarning(sockets),
    ],
    [
        CONFIG.DOCUMENTMAKER.nodeLabel,
        () => new Components.DocumentMaker(sockets),
    ],
    [
        CONFIG.END_NODE.nodeLabel,
        () => new Components.End(sockets),
    ],
    [
        CONFIG.INSERTEVENT.nodeLabel,
        () => new Components.InsertEvent(sockets),
    ],
    [
        CONFIG.MACRO_STARTER.nodeLabel,
        () => new Components.MacroStarter(sockets),
    ],
    [
        CONFIG.NOTIFICATION.nodeLabel,
        () => new Components.Notif(sockets),
    ],
    [
        CONFIG.STATUS_UPDATER.nodeLabel,
        () => new Components.StatusUpdater(sockets),
    ],
    [
        CONFIG.STEPWAIT.nodeLabel,
        () => new Components.StepWait(sockets),
    ],
    [CONFIG.TASK.nodeLabel, () => new Components.Task(sockets)],
    [
        CONFIG.TIME_NODE.nodeLabel,
        () => new Components.Time(sockets),
    ],
    [
        CONFIG.TRIGGER_ADD_DOCUMENT.nodeLabel,
        () => new Components.EventTriggerAddDocument(sockets),
    ],
    [
        CONFIG.TRIGGER_ADD_SUBJECT.nodeLabel,
        () => new Components.EventTriggerAddSubject(sockets),
    ],
    [
        CONFIG.TRIGGER_EXPIRE_DEADLINE.nodeLabel,
        () => new Components.EventTriggerDeadline(sockets),
    ],
    [
        CONFIG.WORKFLOW_LAUNCHER.nodeLabel,
        () => new Components.WorkflowLauncher(sockets),
    ],
];



