import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { sockets, Sockets } from "../editor";
import { EndNodeParams } from "../interfaces";
import { useStore } from "../Rete";

export const createEndNode = (socket: Sockets) => (data?: EndNodeParams) => {
    const node = new End(socket, data);
    return node;
};

export class End extends ClassicPreset.Node {
    constructor(socket: Sockets, _data?: EndNodeParams) {
        const t = useStore.getState().translations;
        super(CONFIG.END_NODE.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.string, t.termina, false)
        );
    }
    clone() {
        return new End(sockets);
    }
}
