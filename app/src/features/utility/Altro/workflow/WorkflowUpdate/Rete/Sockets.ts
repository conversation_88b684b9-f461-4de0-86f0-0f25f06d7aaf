import { ClassicPreset } from "rete";

export class actSocket extends ClassicPreset.Socket {
    constructor() {
        super("Action");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof actSocket ||
            socket instanceof argumentsSocket ||
            socket instanceof responseSocket ||
            socket instanceof decisionInSocket
        );
    }
}

export class argumentsSocket extends ClassicPreset.Socket {
    constructor() {
        super("Arguments");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof argumentsSocket ||
            socket instanceof stringSocket ||
            socket instanceof btnArgumentsSocket ||
            socket instanceof actSocket
        );
    }
}

export class btnArgumentsSocket extends ClassicPreset.Socket {
    constructor() {
        super("Button Arguments");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof btnArgumentsSocket ||
            socket instanceof argumentsSocket
        );
    }
}

export class decisionInSocket extends ClassicPreset.Socket {
    constructor() {
        super("DecisionIn");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof decisionInSocket ||
            socket instanceof responseSocket ||
            socket instanceof actSocket
        );
    }
}

export class decisionOutSocket extends ClassicPreset.Socket {
    constructor() {
        super("DecisionOut");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof decisionOutSocket ||
            socket instanceof actSocket
        );
    }
}

export class responseSocket extends ClassicPreset.Socket {
    constructor() {
        super("Response");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof responseSocket ||
            socket instanceof stringSocket ||
            socket instanceof decisionInSocket ||
            socket instanceof argumentsSocket ||
            socket instanceof actSocket
        );
    }
}

export class stringSocket extends ClassicPreset.Socket {
    constructor() {
        super("string");
    }

    isCompatibleWith(socket: ClassicPreset.Socket) {
        return (
            socket instanceof stringSocket ||
            socket instanceof responseSocket ||
            socket instanceof argumentsSocket ||
            socket instanceof actSocket
        );
    }
}
