import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useState } from "react";
import { sockets, Sockets } from "../editor";
import { CreateModelParams } from "../interfaces";
import { useStore } from "../Rete";

export class CreateModelControl extends ClassicPreset.Control {
    constructor(public data: CreateModelParams) {
        super();
    }

    setValue(data: CreateModelParams) {
        this.data = data;
    }
}

const defaultOptions: CreateModelParams = {
    model: "-1",
    ruoloCliente: "-1",
    ruoloControparte: "-1",
    ruoloAvversario: "-1",
    ruoloAltro: "-1",
};

export const CustomCreateModelInputs = (props: {
    data: CreateModelControl;
}) => {
    const t = useStore.getState().translations;
    const availableroleoptions =
        useStore.getState().componentOptions?.getavailableroleoptions ?? [];
    const availabledocumentmodelsoptions =
        useStore.getState().componentOptions
            ?.getavailabledocumentmodelsoptions ?? [];
    const [componentState, setComponentState] = useState<CreateModelParams>(
        props.data.data ? props.data.data : defaultOptions
    );


    const handleChange = (field: keyof CreateModelParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Modello}</InputLabel>
                    <Select
                        value={componentState.model}
                        onChange={handleChange("model")}>
                        <MenuItem value={-1}>{t["Nessuna azione"]}</MenuItem>
                        {availabledocumentmodelsoptions &&
                            availabledocumentmodelsoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.title}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Ruolo clienti"]}</InputLabel>
                    <Select
                        onChange={handleChange("ruoloCliente")}
                        value={componentState.ruoloCliente}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>
                            {t["Automatico (il primo inserimento)"]}
                        </MenuItem>
                        {availableroleoptions &&
                            availableroleoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Ruolo controparte"]}</InputLabel>
                    <Select
                        onChange={handleChange("ruoloControparte")}
                        value={componentState.ruoloControparte}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>
                            {t["Automatico (il primo inserimento)"]}
                        </MenuItem>
                        {availableroleoptions &&
                            availableroleoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Ruolo avversario"]}</InputLabel>
                    <Select
                        value={componentState.ruoloAvversario}
                        onChange={handleChange("ruoloAvversario")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>
                            {t["Automatico (il primo inserimento)"]}
                        </MenuItem>
                        {availableroleoptions &&
                            availableroleoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Ruolo altro"]}</InputLabel>
                    <Select
                        value={componentState.ruoloAltro}
                        onChange={handleChange("ruoloAltro")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>
                            {t["Automatico (il primo inserimento)"]}
                        </MenuItem>
                        {availableroleoptions &&
                            availableroleoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createModelComponent =
    (socket: Sockets) => (data?: CreateModelParams) => {
        const node = new CreateModel(socket, data);

        return node;
    };

export class CreateModel extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: CreateModelParams) {
        const t = useStore.getState().translations;
        super(CONFIG.CREATE_MODEL.nodeLabel);

        this.addControl(
            t["create model"],
            new CreateModelControl(data ?? defaultOptions)
        );
        this.addInput(
            "dataEvent",
            new ClassicPreset.Input(socket.act, t.Genera)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Trasmetti)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore)
        );
    }
    clone() {
        return new CreateModel(sockets);
    }
}
