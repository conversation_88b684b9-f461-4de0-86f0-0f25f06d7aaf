import { ClassicPreset } from "rete";
import { EventTriggerChangeArchiveFieldsParams } from "../interfaces";
import { useState } from "react";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { useStore } from "../Rete";
import { CONFIG } from "../constants";

export class EventTriggerChangeArchiveFieldsComponentControl extends ClassicPreset.Control {
    constructor(public data: EventTriggerChangeArchiveFieldsParams) {
        super();
    }

    setValue(data: EventTriggerChangeArchiveFieldsParams) {
        this.data = data;
    }
}

const defaultOptions: EventTriggerChangeArchiveFieldsParams = {
    archivioField: 0,
};

export const CustomEventTriggerChangeArchiveFieldsComponentInputs = (props: {
    data: EventTriggerChangeArchiveFieldsComponentControl;
}) => {
    const t = useStore.getState().translations;
    const getavailablechangearchivefieldsoptions =
        useStore.getState().componentOptions
            ?.getavailablechangearchivefieldsoptions ?? [];
    const [componentState, setComponentState] =
        useState<EventTriggerChangeArchiveFieldsParams>(
            props.data.data ? props.data.data : defaultOptions
        );

    const handleChange = (event: any) => {
        const newState = {
            ...componentState,
            archivioField: event.target.value,
        };
        setComponentState(newState);
        props.data.setValue(newState);
    };

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Campo}</InputLabel>
                    <Select
                        onChange={handleChange}
                        value={componentState.archivioField}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {getavailablechangearchivefieldsoptions &&
                            getavailablechangearchivefieldsoptions.map(
                                (value: any) => (
                                    <MenuItem key={value.id} value={value.id}>
                                        {value.nome}
                                    </MenuItem>
                                )
                            )}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createEventTriggerChangeArchiveFieldsComponent =
    (socket: Sockets) => (data?: EventTriggerChangeArchiveFieldsParams) => {
        const node = new EventTriggerChangeArchiveFields(socket, data);

        return node;
    };

export class EventTriggerChangeArchiveFields extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: EventTriggerChangeArchiveFieldsParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TRIGGER_CHANGE_ARCHIVE_FIELDS.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Attiva, true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(
                socket.string,
                t["Evento Completato"],
                true
            )
        );
        this.addControl(
            "trigger_change_archive_fields",
            new EventTriggerChangeArchiveFieldsComponentControl(
                data ?? defaultOptions
            )
        );
    }
    clone() {
        return new EventTriggerChangeArchiveFields(sockets);
    }
}
