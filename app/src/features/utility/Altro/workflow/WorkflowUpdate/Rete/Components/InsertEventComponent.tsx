import {
    Checkbox,
    FormControl,
    FormControlLabel,
    FormGroup,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { useEffect, useState } from "react";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { DesktopTimePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { VaporThemeProvider } from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { InsertEventParams } from "../interfaces";
import { useStore } from "../Rete";

export class InsertEventControl extends ClassicPreset.Control {
    constructor(public data: InsertEventParams) {
        super();
    }

    setValue(data: InsertEventParams) {
        this.data = data;
    }
}

const defaultOptions: InsertEventParams = {
    avvisaprima: "",
    deadlineEvasa: false,
    deadlineId: false,
    deadlineImportant: false,
    deadlineNonevadere: false,
    deadlineVisible: false,
    durata: "",
    oggetto: "",
    relazioneIntestatario: "",
    timeblockType: "",
    timeDayQuantity: "",
    timeHour: "",
    tipologiaImpegno: "",
};

const formatTimeToString = (date: Date | null): string => {
    if (!date) return "";
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
};

const parseTimeString = (timeString: string): Date | null => {
    if (!timeString) return null;
    return new Date(`2000-01-01T${timeString}`);
};

export const CustomInsertEventsInput = (props: {
    data: InsertEventControl;
}) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<InsertEventParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof InsertEventParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    const handleTimeChange = (newValue: Date | null) => {
        setComponentState({
            ...componentState,
            timeHour: formatTimeToString(newValue),
        });
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <TextField
                    value={componentState.oggetto}
                    onChange={handleChange("oggetto")}
                    label={t.Oggetto}>
                    {componentState.oggetto}
                </TextField>
                <TextField
                    type="number"
                    label={t.Attiva}
                    onChange={handleChange("avvisaprima")}
                    value={componentState.avvisaprima}
                />
                <TextField
                    type="number"
                    label={t.Durata}
                    onChange={handleChange("durata")}
                    value={componentState.durata}
                />
                <FormControl>
                    <InputLabel>{t["Data evento calcolate"]}</InputLabel>
                    <Select
                        value={componentState.timeblockType}
                        onChange={handleChange("timeblockType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>
                            {t["Dall' inizio del workflow"]}
                        </MenuItem>
                        <MenuItem value={2}>
                            {t["Dall' inizio di questo bloco"]}
                        </MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    type="number"
                    label={t.Giorni}
                    onChange={handleChange("timeDayQuantity")}
                    value={componentState.timeDayQuantity}
                />
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DesktopTimePicker
                        value={parseTimeString(componentState.timeHour)}
                        onChange={handleTimeChange}
                    />
                </LocalizationProvider>
                <FormControl>
                    <InputLabel>{t.Tipologia}</InputLabel>
                    <Select
                        value={componentState.tipologiaImpegno}
                        onChange={handleChange("tipologiaImpegno")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Relazione intestatari"]}</InputLabel>
                    <Select
                        value={componentState.relazioneIntestatario}
                        onChange={handleChange("relazioneIntestatario")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>{t.Automatico}</MenuItem>
                        <MenuItem value={2}>{t.Responsabile}</MenuItem>
                        <MenuItem value={1}>{t.Cointestatario}</MenuItem>
                        <MenuItem value={3}>{t.Collaboratore}</MenuItem>
                        <MenuItem value={30}>{t["Utente interno"]}</MenuItem>
                    </Select>
                </FormControl>
                <FormGroup>
                    <FormControlLabel
                        label={t.Evaso}
                        value={componentState.deadlineEvasa}
                        control={
                            <Checkbox
                                checked={componentState.deadlineEvasa}
                                onChange={handleChange("deadlineEvasa")}
                            />
                        }></FormControlLabel>
                    <FormControlLabel
                        label={t["Da non evadere"]}
                        control={
                            <Checkbox
                                checked={componentState.deadlineNonevadere}
                                onChange={handleChange("deadlineNonevadere")}
                            />
                        }></FormControlLabel>
                    <FormControlLabel
                        label={t.Importante}
                        control={
                            <Checkbox
                                checked={componentState.deadlineImportant}
                                onChange={handleChange("deadlineImportant")}
                            />
                        }></FormControlLabel>
                    <FormControlLabel
                        label={t["Visibile utenti esterni"]}
                        control={
                            <Checkbox
                                checked={componentState.deadlineVisible}
                                onChange={handleChange("deadlineVisible")}
                            />
                        }></FormControlLabel>
                    <FormControlLabel
                        label={t["Identificativo pratica nell'oggetto"]}
                        control={
                            <Checkbox
                                checked={componentState.deadlineId}
                                onChange={handleChange("deadlineId")}
                            />
                        }></FormControlLabel>
                </FormGroup>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createInsertEventComponent =
    (socket: Sockets) => (data?: InsertEventParams) => {
        const node = new InsertEvent(socket, data);

        return node;
    };

export class InsertEvent extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: InsertEventParams) {
        const t = useStore.getState().translations;
        super(CONFIG.INSERTEVENT.nodeLabel);
        this.addInput(
            "dataEvent",
            new ClassicPreset.Input(socket.act, t["Dati creazione"], true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui, true)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Errore, true)
        );
        this.addControl(
            "event maker",
            new InsertEventControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new InsertEvent(sockets);
    }
}
