import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack, TextField, VaporThemeProvider } from "@vapor/v3-components";

import { useState, useEffect } from "react";
import { sockets, Sockets } from "../editor";
import { CounterParams } from "../interfaces";
import { useStore } from "../Rete";

export class CounterControl extends ClassicPreset.Control {
    constructor(public data: CounterParams) {
        super();
    }

    setValue(data: CounterParams) {
        this.data = data;
    }
}

const defaultOptions: CounterParams = {
    giri: "",
};

export const CustomCounterInputs = (props: { data: CounterControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<CounterParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof CounterParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <TextField
                    onChange={handleChange("giri")}
                    value={componentState.giri}
                    label={t.Giri}
                    placeholder={t["Giri prima di uscire"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createCounterComponent =
    (socket: Sockets) => (data?: CounterParams) => {
        const node = new Counter(socket, data);
        return node;
    };

export class Counter extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: CounterParams) {
        const t = useStore.getState().translations;
        super(CONFIG.COUNTER.nodeLabel);
        this.addControl("counter", new CounterControl(data ?? defaultOptions));
        this.addInput(
            "obtained",
            new ClassicPreset.Input(socket.string, t.Contagiro, true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Superato)
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.string, t.Continua)
        );
    }
    clone() {
        return new Counter(sockets);
    }
}
