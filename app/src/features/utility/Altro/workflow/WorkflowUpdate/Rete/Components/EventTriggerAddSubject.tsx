import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack, VaporThemeProvider } from "@vapor/v3-components";
import { EventTriggerAddSubjectParams } from "../interfaces";
import { useStore } from "../Rete";
import { Sockets, sockets } from "../editor";

export class EventTriggerAddSubjectControl extends ClassicPreset.Control {
    constructor(public data: EventTriggerAddSubjectParams) {
        super();
    }
    setValue(data: EventTriggerAddSubjectParams) {
        this.data = data;
    }
}


export const CustomEventTriggerAddSubjectInputs = (_props: {
    data: EventTriggerAddSubjectControl;
}) => {
    return (
        <VaporThemeProvider>
            <Stack gap={2}></Stack>
        </VaporThemeProvider>
    );
};

export const createEventTriggerAddSubjectComponent =
    (socket: Sockets) => (data?: EventTriggerAddSubjectParams) => {
        const node = new EventTriggerAddSubject(socket, data);
        return node;
    };

export class EventTriggerAddSubject extends ClassicPreset.Node {
    constructor(socket: Sockets, _data?: EventTriggerAddSubjectParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TRIGGER_ADD_SUBJECT.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.string, t.Attiva, true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(
                socket.string,
                t["Evento Completato"],
                true
            )
        );
    }
    clone() {
        return new EventTriggerAddSubject(sockets);
    }
}
