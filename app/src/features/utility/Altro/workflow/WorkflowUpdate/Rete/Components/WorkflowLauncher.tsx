import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { useState } from "react";
import { sockets, Sockets } from "../editor";
import { WorkflowLauncherParams } from "../interfaces";
import { useStore } from "../Rete";

export class WorkflowLauncherControl extends ClassicPreset.Control {
    constructor(public data: WorkflowLauncherParams) {
        super();
    }

    setValue(data: WorkflowLauncherParams) {
        this.data = data;
    }
}

const defaultOptions: WorkflowLauncherParams = {
    workflowId: "",
};

export const CustomWorkflowLauncherInputs = (props: {
    data: WorkflowLauncherControl;
}) => {
    const t = useStore.getState().translations;
    const availableworkflowptions =
        useStore.getState().componentOptions?.getavailableworkflowptions ?? [];
    const [componentState, setComponentState] =
        useState<WorkflowLauncherParams>(
            props.data.data ? props.data.data : defaultOptions
        );

    const handleChange =
        (field: keyof WorkflowLauncherParams) => (event: any) => {
            const updatedState = {
                ...componentState,
                [field]: event.target.value,
            };

            setComponentState(updatedState);
            props.data.setValue(updatedState);
        };
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t["Modello Workflow"]}</InputLabel>
                    <Select
                        value={componentState.workflowId}
                        onChange={handleChange("workflowId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availableworkflowptions &&
                            availableworkflowptions.map((value: any) => (
                                <MenuItem key={value.id} value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createWorkflowLauncherComponent =
    (socket: Sockets) => (data?: WorkflowLauncherParams) => {
        const node = new WorkflowLauncher(socket, data);
        return node;
    };

export class WorkflowLauncher extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: WorkflowLauncherParams) {
        const t = useStore.getState().translations;
        super(CONFIG.WORKFLOW_LAUNCHER.nodeLabel);
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t["Avvia workflow"])
        );
        this.addControl(
            "workflow",
            new WorkflowLauncherControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new WorkflowLauncher(sockets);
    }
}
