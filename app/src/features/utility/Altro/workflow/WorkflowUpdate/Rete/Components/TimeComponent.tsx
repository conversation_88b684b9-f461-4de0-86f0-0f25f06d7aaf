import {
    FormControl,
    MenuItem,
    Select,
    Stack,
    TextField,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useEffect, useState } from "react";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { sockets, Sockets } from "../editor";
import { TimeParams } from "../interfaces";
import { useStore } from "../Rete";

export class TimeControl extends ClassicPreset.Control {
    constructor(public data: TimeParams) {
        super();
    }

    setValue(data: TimeParams) {
        this.data = data;
    }
}

const defaultOptions: TimeParams = {
    timeblockType: 0,
    timeDayQuantity: 0,
    timeHourQuantity: 0,
    timeMinQuantity: 0,
};

export const CustomTimeInputs = (props: { data: TimeControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<TimeParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof TimeParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <Select
                        value={componentState.timeblockType}
                        onChange={handleChange("timeblockType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>
                            {t["Dall' inizio del workflow"]}
                        </MenuItem>
                        <MenuItem value={2}>
                            {t["Dal termine del blocco precedente"]}
                        </MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    label={t.Giorni}
                    value={componentState.timeDayQuantity}
                    onChange={handleChange("timeDayQuantity")}
                    placeholder={t["Numero giorni attesa"]}
                />
                <TextField
                    label={t.Ore}
                    value={componentState.timeHourQuantity}
                    onChange={handleChange("timeHourQuantity")}
                    placeholder={t["Numero ore attesa"]}
                />
                <TextField
                    label={t.Minuti}
                    value={componentState.timeMinQuantity}
                    onChange={handleChange("timeMinQuantity")}
                    placeholder={t["Numero minuti attesa"]}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createTimeComponent = (socket: Sockets) => (data?: TimeParams) => {
    const node = new Time(socket, data);

    return node;
};

export class Time extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: TimeParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TIME_NODE.nodeLabel);
        this.addInput(
            "args",
            new ClassicPreset.Input(socket.argument, t.Argomenti)
        );
        this.addControl("response", new TimeControl(data ?? defaultOptions));
        this.addOutput(
            "response",
            new ClassicPreset.Output(socket.response, t.Risposta)
        );
    }
    clone() {
        return new Time(sockets);
    }
}
