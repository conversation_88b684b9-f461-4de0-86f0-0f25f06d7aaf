import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextareaAutosize,
    TextField,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useState, useEffect } from "react";
import { sockets, Sockets } from "../editor";
import { TaskParams } from "../interfaces";
import { useStore } from "../Rete";

export class TaskControl extends ClassicPreset.Control {
    constructor(public data: TaskParams) {
        super();
    }

    setValue(data: TaskParams) {
        this.data = data;
    }
}

const defaultOptions: TaskParams = {
    assignedId: "",
    descrizione: "",
    title: "",
};

export const CustomTaskInputs = (props: { data: TaskControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<TaskParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof TaskParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <TextField
                    label={t.Titolo}
                    value={componentState.title}
                    onChange={handleChange("title")}
                    placeholder={t["Titolo del task"]}
                />
                <TextareaAutosize
                    minRows={6}
                    value={componentState.descrizione}
                    onChange={handleChange("descrizione")}
                    placeholder={t["Descrizione del task"]}
                />
                <FormControl>
                    <InputLabel>{t["Modello macro"]}</InputLabel>
                    <Select>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t["Intestato a"]}</InputLabel>
                    <Select
                        value={componentState.assignedId}
                        onChange={handleChange("assignedId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createTaskComponent = (socket: Sockets) => (data?: TaskParams) => {
    const node = new Task(socket, data);
    return node;
};

export class Task extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: TaskParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TASK.nodeLabel);
        this.addInput(
            "make",
            new ClassicPreset.Input(socket.string, t["Crea Task"])
        );
        this.addControl("task", new TaskControl(data ?? defaultOptions));
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.response, t["Esito Positivo"])
        );
        this.addOutput(
            "false",
            new ClassicPreset.Output(socket.response, t["Esito Negativo"])
        );
        this.addOutput(
            "done",
            new ClassicPreset.Output(socket.response, t.Completato)
        );
    }
    clone() {
        return new Task(sockets);
    }
}
