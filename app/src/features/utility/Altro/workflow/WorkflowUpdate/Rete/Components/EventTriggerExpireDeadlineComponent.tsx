import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { EventTriggerExpireDeadlineParams } from "../interfaces";
import { useStore } from "../Rete";
import { useState, useEffect } from "react";

export class EventTriggerExpireDeadlineComponentControl extends ClassicPreset.Control {
    constructor(public data: EventTriggerExpireDeadlineParams) {
        super();
    }

    setValue(data: EventTriggerExpireDeadlineParams) {
        this.data = data;
    }
}

const defaultOptions: EventTriggerExpireDeadlineParams = {
    impegnoType: "",
};
export const CustomEventTriggerExpireDeadlineComponentInputs = (props: {
    data: EventTriggerExpireDeadlineComponentControl;
}) => {
    const t = useStore.getState().translations;
    const availableimpegnitypeoptions =
        useStore.getState().componentOptions?.getavailableimpegnitypeoptions ??
        [];
    const [componentState, setComponentState] =
        useState<EventTriggerExpireDeadlineParams>(
            props.data.data ? props.data.data : defaultOptions
        );

    const handleChange =
        (field: keyof EventTriggerExpireDeadlineParams) => (event: any) => {
            const updatedState = {
                ...componentState,
                [field]: event.target.value,
            };

            setComponentState(updatedState);
        };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Tipologia}</InputLabel>
                    <Select
                        value={componentState.impegnoType}
                        onChange={handleChange("impegnoType")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={-1}>{t["Qualsiasi impegno"]}</MenuItem>
                        {availableimpegnitypeoptions &&
                            availableimpegnitypeoptions.map((value: any) => (
                                <MenuItem value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createEventTriggerExpireDeadlineComponent =
    (socket: Sockets) => (data?: EventTriggerExpireDeadlineParams) => {
        const node = new EventTriggerDeadline(socket, data);

        return node;
    };

export class EventTriggerDeadline extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: EventTriggerExpireDeadlineParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TRIGGER_EXPIRE_DEADLINE.nodeLabel);
        this.addControl(
            "trigger_expire_event",
            new EventTriggerExpireDeadlineComponentControl(
                data ?? defaultOptions
            )
        );
        this.addInput("act", new ClassicPreset.Input(socket.act, t.Attiva));
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t["Evento Completato"])
        );
    }
    clone() {
        return new EventTriggerDeadline(sockets);
    }
}
