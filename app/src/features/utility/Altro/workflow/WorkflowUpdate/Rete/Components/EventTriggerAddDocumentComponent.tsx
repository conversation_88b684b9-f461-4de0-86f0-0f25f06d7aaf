import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { Stack, VaporThemeProvider } from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { EventTriggerAddDocumentParams } from "../interfaces";
import { useStore } from "../Rete";

export class EventTriggerAddDocumentComponentControl extends ClassicPreset.Control {
    constructor(public data: EventTriggerAddDocumentParams) {
        super();
    }

    setValue(data: EventTriggerAddDocumentParams) {
        this.data = data;
    }
}


export const CustomEventTriggerAddDocumentComponentInputs = (_props: {
    data: EventTriggerAddDocumentComponentControl;
}) => {
    return (
        <VaporThemeProvider>
            <Stack gap={2}></Stack>
        </VaporThemeProvider>
    );
};

export const createEventTriggerAddDocumentComponent =
    (socket: Sockets) => (data?: EventTriggerAddDocumentParams) => {
        const node = new EventTriggerAddDocument(socket, data);

        return node;
    };

export class EventTriggerAddDocument extends ClassicPreset.Node {
    constructor(socket: Sockets, _data?: EventTriggerAddDocumentParams) {
        const t = useStore.getState().translations;
        super(CONFIG.TRIGGER_ADD_DOCUMENT.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Attiva, true)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(
                socket.string,
                t["Evento Completato"],
                true
            )
        );
    }
    clone() {
        return new EventTriggerAddDocument(sockets);
    }
}
