import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import { useEffect, useState } from "react";
import { sockets, Sockets } from "../editor";
import { NotifParams } from "../interfaces";
import { useStore } from "../Rete";

export class NotifControl extends ClassicPreset.Control {
    constructor(public data: NotifParams) {
        super();
    }

    setValue(data: NotifParams) {
        this.data = data;
    }
}

const defaultOptions: NotifParams = {
    messageBody: "",
    notificationChannelType: "",
    userIdToNotify: "",
};

export const CustomNotifyInputs = (props: { data: NotifControl }) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<NotifParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof NotifParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <Select
                    value={componentState.notificationChannelType}
                    onChange={handleChange("notificationChannelType")}>
                    <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                    <MenuItem value={1}>{t.Email}</MenuItem>
                    <MenuItem value={3}>{t.Alert}</MenuItem>
                </Select>
                <FormControl fullWidth>
                    <InputLabel>{t.User}</InputLabel>
                    <Select
                        label="User"
                        value={componentState.userIdToNotify}
                        onChange={handleChange("userIdToNotify")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={2}>{t.Responsabile}</MenuItem>
                        <MenuItem value={1}>{t.Cointestatario}</MenuItem>
                        <MenuItem value={3}>{t.Collaboratore}</MenuItem>
                        <MenuItem value={30}>{t["Utente interno"]}</MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    value={componentState.messageBody}
                    onChange={handleChange("messageBody")}
                    placeholder={t.Notifica}
                />
            </Stack>
        </VaporThemeProvider>
    );
};

export const createNotifComponent =
    (socket: Sockets) => (data?: NotifParams) => {
        const node = new Notif(socket, data);
        return node;
    };

export class Notif extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: NotifParams) {
        const t = useStore.getState().translations;
        super(CONFIG.NOTIFICATION.nodeLabel);
        this.addInput(
            "act",
            new ClassicPreset.Input(socket.act, t.Action, true)
        );
        this.addControl("notif", new NotifControl(data ?? defaultOptions));
    }
    clone() {
        return new Notif(sockets);
    }
}
