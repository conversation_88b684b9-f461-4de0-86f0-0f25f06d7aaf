import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { useState } from "react";
import { StatusUpdaterParams } from "../interfaces";
import { useStore } from "../Rete";
import { sockets, Sockets } from "../editor";

export class StatusUpdaterControl extends ClassicPreset.Control {
    constructor(public data: StatusUpdaterParams) {
        super();
    }

    setValue(data: StatusUpdaterParams) {
        this.data = data;
    }
}

const defaultOptions: StatusUpdaterParams = {
    statusId: "",
};

export const CustomStatusUpdaterInputs = (props: {
    data: StatusUpdaterControl;
}) => {
    const t = useStore.getState().translations;
    const availablestatusoptions =
        useStore.getState().componentOptions?.getavailablestatusoptions ?? [];
    const [componentState, setComponentState] = useState<StatusUpdaterParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof StatusUpdaterParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
        props.data.setValue(updatedState);
    };
    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Stato}</InputLabel>
                    <Select
                        value={componentState.statusId}
                        onChange={handleChange("statusId")}>
                        <MenuItem value={0}>{t["Nessuna azione"]}</MenuItem>
                        {availablestatusoptions &&
                            availablestatusoptions.map((value: any) => (
                                <MenuItem key={value.id} value={value.id}>
                                    {value.nome}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createStatusUpdaterComponent = (
    socket: Sockets,
    data: StatusUpdaterParams
) => {
    const node = new StatusUpdater(socket, data);
    return node;
};

export class StatusUpdater extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: StatusUpdaterParams) {
        const t = useStore.getState().translations;
        super(CONFIG.STATUS_UPDATER.nodeLabel);
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t.Aziona)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui)
        );
        this.addControl(
            "status",
            new StatusUpdaterControl(data ?? defaultOptions)
        );
    }
    clone() {
        return new StatusUpdater(sockets);
    }
}
