import * as Components from "./Components";
import {  ClassicPreset } from "rete";
import { Presets } from "rete-react-plugin";


export type AllowedControls =
    | Components.EmailSenderComponentControl
    | Components.DocumentMakerComponentControl
    | Components.AddGroupControl
    | Components.AddUserSubjectControl
    | Components.CalcInterestsComponentControl
    | Components.ChangeStatusComponentControl
    | Components.CheckTimeControl
    | Components.CounterControl
    | Components.CreateModelControl
    | Components.CreateWarningComponentControl
    | Components.EventTriggerAddDocumentComponentControl
    | Components.EventTriggerExpireDeadlineComponentControl
    | Components.EventTriggerChangeArchiveFieldsComponentControl
    | Components.InsertEventControl
    | Components.MacroStarterControl
    | Components.NotifControl
    | Components.StatusUpdaterControl
    | Components.StepWaitControl
    | Components.TaskControl
    | Components.TimeControl
    | Components.WorkflowLauncherControl
    | ClassicPreset.Control
    | ClassicPreset.InputControl<"number">
    | ClassicPreset.InputControl<"text">

export class Node extends ClassicPreset.Node<
    { [key in string]: ClassicPreset.Socket },
    { [key in string]: ClassicPreset.Socket },
    { [key in string]: AllowedControls | undefined }
> {}



const CONTROL_COMPONENT_MAP = {
    'EmailSenderComponentControl':                     Components.CustomEmailSenderComponentInputs,
    'DocumentMakerComponentControl':                   Components.CostumDocumentMakerComponentInputs,
    'InsertEventControl':                              Components.CustomInsertEventsInput,
    'NotifControl':                                    Components.CustomNotifyInputs,
    'TimeControl':                                     Components.CustomTimeInputs,
    'StepWaitControl':                                 Components.CustomStepWaitInputs,
    'AddUserSubjectControl':                           Components.CustomAddUserSubjectInputs,
    'AddGroupControl':                                 Components.CustomAddGroupInputs,
    'CounterControl':                                  Components.CustomCounterInputs,
    'CheckTimeControl':                                Components.CustomCheckTimeInputs,
    'TaskControl':                                     Components.CustomTaskInputs,
    'MacroStarterControl':                             Components.CustomMacroStarterInputs,
    'WorkflowLauncherControl':                         Components.CustomWorkflowLauncherInputs,
    'StatusUpdaterControl':                            Components.CustomStatusUpdaterInputs,
    'CreateModelControl':                              Components.CustomCreateModelInputs,
    'EventTriggerAddDocumentComponentControl':         Components.CustomEventTriggerAddDocumentComponentInputs,
    'EventTriggerExpireDeadlineComponentControl':      Components.CustomEventTriggerExpireDeadlineComponentInputs,
    'EventTriggerChangeArchiveFieldsComponentControl': Components.CustomEventTriggerChangeArchiveFieldsComponentInputs,
    'CreateWarningComponentControl':                   Components.CustomCreateWarningComponentInputs,
    'ChangeStatusComponentControl':                    Components.CustomChangeStatusComponentInputs,
    'CalcInterestsComponentControl':                   Components.CustomCalcInterestsComponentInputs,
} as const;





export type ControlType = keyof typeof CONTROL_COMPONENT_MAP;


function isInputControl(control: any): control is ClassicPreset.InputControl<"text" | "number"> {
    return control instanceof ClassicPreset.InputControl;
}

export function getControlComponent(control: AllowedControls): React.ComponentType<any> | null {
    if (!control) return null;
    
    if (isInputControl(control)) {
        return Presets.classic.Control;
    }
    
    const controlType = control.constructor.name as ControlType;
    return CONTROL_COMPONENT_MAP[controlType] || null;
}

