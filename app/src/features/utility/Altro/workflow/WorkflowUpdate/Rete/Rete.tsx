import { useEffect } from "react";
import { useGetWorkflowData } from "../hooks/useGetWorkflowData";
import { createEditor } from "./editor";
import { useRete } from "rete-react-plugin";
import { create } from "zustand";
import { EditorState, StoreState, TranslatedType } from "./interfaces";
import { useTranslation } from "@1f/react-sdk";
import { translated } from "./constants";
import { useSearchParams } from "react-router-dom";
import { subscribeWithSelector } from "zustand/middleware";

const createInitialTranslations = (): TranslatedType =>
    Object.keys(translated).reduce(
        (acc, key) => ({
            ...acc,
            [key]: "",
        }),
        {} as TranslatedType
    );

export const useStore = create(
    subscribeWithSelector<StoreState>(set => ({
        editorState: undefined,
        translations: createInitialTranslations(),
        setEditorState: (value: EditorState | undefined) =>
            set({ editorState: value }),
        setTranslations: (translations: TranslatedType) =>
            set({ translations }),
        componentOptions: undefined,
        setComponentOptions: (data: any) => set({ componentOptions: data }),
        saveState: false,
        setSaveState: (value: boolean) => set({ saveState: value }),
        editorData: null,
        setEditorData: (newData: any) => set({ editorData: newData }),
        center: false,
        setCenter: (val: boolean) => set({ center: val }),
        isUpdate: false,
        setIsUpdate: (val: boolean) => set({ isUpdate: val }),
    }))
);

export const Rete = () => {
    const [searchParams] = useSearchParams();
    const uniqueid = searchParams.get("uniqueid");
    const response = useGetWorkflowData({ uniqueid: uniqueid });
    const setEditorState = useStore(state => state.setEditorState);
    const setComponentOptions = useStore(state => state.setComponentOptions);
    const setIsUpdate = useStore(state => state.setIsUpdate);
    const { t } = useTranslation();
    const setTranslations = useStore(state => state.setTranslations);

    const [ref] = useRete(createEditor);

    useEffect(() => {
        setIsUpdate(!!uniqueid);
    }, [uniqueid]);

    useEffect(() => {
        if (response.allLoaded) {
            const currentEditorState = useStore.getState().editorState;
            if (currentEditorState !== undefined) {
                if (
                    JSON.stringify(currentEditorState.State) !==
                    JSON.stringify(response.requests.getjson.data)
                ) {
                    setEditorState({ State: response.requests.getjson.data });
                }
            } else {
                setEditorState({ State: response.requests.getjson.data });
            }

            const responses = {
                getavailabledocumentmodelsoptions:
                    response.requests.getavailabledocumentmodelsoptions.data,
                getavailablegroupsoptions:
                    response.requests.getavailablegroupsoptions.data,
                getavailableimpegnitypeoptions:
                    response.requests.getavailableimpegnitypeoptions.data,
                getavailablemacrooptions:
                    response.requests.getavailablemacrooptions.data,
                getavailablemodeloptions:
                    response.requests.getavailablemodeloptions.data,
                getavailablerelationoptions:
                    response.requests.getavailablerelationoptions.data,
                getavailableroleoptions:
                    response.requests.getavailableroleoptions.data,
                getavailablestatusoptions:
                    response.requests.getavailablestatusoptions.data,
                getavailabletemplates:
                    response.requests.getavailabletemplates.data,
                getavailableusersoptions:
                    response.requests.getavailableusersoptions.data,
                getavailableworkflowptions:
                    response.requests.getavailableworkflowptions.data,
                getemailmodeloptions:
                    response.requests.getemailmodeloptions.data,
                getavailablechangearchivefieldsoptions:
                    response.requests.getavailablechangearchivefieldsoptions
                        .data,
            };

            setComponentOptions(responses);
            const translatedEntries = Object.entries(
                translated
            ).reduce<TranslatedType>(
                (acc, [key, value]) => ({
                    ...acc,
                    [key]: t(value),
                }),
                createInitialTranslations()
            );
            setTranslations(translatedEntries);
            if (ref.current) {
                (ref.current as unknown as HTMLElement).innerHTML = "";
                setTimeout(() => {
                    if (ref.current) {
                        createEditor(ref.current);
                    }
                }, 0);
            }
        }
    }, [response.allLoaded]);

    return (
        <div>
            <div
                ref={ref}
                style={{
                    height: "110vh",
                    width: "77vw",
                    border: "1px solid black",
                }}
            />
        </div>
    );
};
