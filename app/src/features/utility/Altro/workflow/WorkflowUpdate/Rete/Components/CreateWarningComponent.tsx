import { ClassicPreset } from "rete";
import { CONFIG } from "../constants";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    VaporThemeProvider,
} from "@vapor/v3-components";
import { sockets, Sockets } from "../editor";
import { CreateWarningParams } from "../interfaces";
import { useStore } from "../Rete";
import { useState, useEffect } from "react";

export class CreateWarningComponentControl extends ClassicPreset.Control {
    constructor(public data: CreateWarningParams) {
        super();
    }

    setValue(data: CreateWarningParams) {
        this.data = data;
    }
}

const defaultOptions: CreateWarningParams = {
    actionType: "",
    printType: "",
};

export const CustomCreateWarningComponentInputs = (props: {
    data: CreateWarningComponentControl;
}) => {
    const t = useStore.getState().translations;
    const [componentState, setComponentState] = useState<CreateWarningParams>(
        props.data.data ? props.data.data : defaultOptions
    );

    const handleChange = (field: keyof CreateWarningParams) => (event: any) => {
        const updatedState = {
            ...componentState,
            [field]: event.target.value,
        };

        setComponentState(updatedState);
    };

    useEffect(() => {
        props.data.setValue({
            ...componentState,
        });
    }, [componentState]);

    return (
        <VaporThemeProvider>
            <Stack gap={2}>
                <FormControl>
                    <InputLabel>{t.Azione}</InputLabel>
                    <Select
                        value={componentState.actionType}
                        onChange={handleChange("actionType")}>
                        <MenuItem value={-1}>{t["Nessuna azione"]}</MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t.Stampa}</InputLabel>
                    <Select
                        value={componentState.printType}
                        onChange={handleChange("printType")}>
                        <MenuItem value={-1}>{t["Nessuna azione"]}</MenuItem>
                        <MenuItem value={1}>{t["Non Stampare"]}</MenuItem>
                        <MenuItem value={2}>{t["Stampa"]}</MenuItem>
                    </Select>
                </FormControl>
            </Stack>
        </VaporThemeProvider>
    );
};

export const createWarningComponent =
    (socket: Sockets) => (data?: CreateWarningParams) => {
        const node = new CreateWarning(socket, data);
        return node;
    };

export class CreateWarning extends ClassicPreset.Node {
    constructor(socket: Sockets, data?: CreateWarningParams) {
        const t = useStore.getState().translations;
        super(CONFIG.CREATE_WARNING.nodeLabel);

        this.addControl(
            "create warning",
            new CreateWarningComponentControl(data ?? defaultOptions)
        );
        this.addInput(
            "start",
            new ClassicPreset.Input(socket.string, t.Aziona)
        );
        this.addOutput(
            "true",
            new ClassicPreset.Output(socket.string, t.Prosegui)
        );
    }
    clone() {
        return new CreateWarning(sockets);
    }
}
