import { Other } from "../Altro";
import { RunningInstances } from "./RunningInstances/RunningInstances";
import { Workflows } from "./Workflows";
import { WorkflowUpdate } from "./WorkflowUpdate/WorkflowUpdate";

export const other = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/index/utilitiesmainpage",
            element: <Other />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/workflows",
            element: <Workflows />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/workflows/update",
            element: <WorkflowUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/workflows/runningistances",
            element: <RunningInstances />,
        },
    },
];
