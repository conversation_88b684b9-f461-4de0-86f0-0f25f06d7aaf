import { useTranslation } from "@1f/react-sdk";
import { useEffect, useRef, useState } from "react";
import { debounce } from "lodash";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import type { IList } from "../../../../../../interfaces/general.interfaces";
import { DEFAULT_ITEMS_LIST, DEFAULT_INSTANCE_QUERY } from "../constants";
import type { IInstancesQuery } from "../interfaces";
import { getInstancesGrid } from "../../../../../../utilities/instances/gridColumn";

export const useInstancesData = () => {
    const { t } = useTranslation();
    const isReset = useRef(false);

    const instancesDataResponse = useGetCustom(
        "workflows/istanceslist?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IInstancesQuery>(DEFAULT_INSTANCE_QUERY);
    const [list, setList] = useState<IList<any>>(DEFAULT_ITEMS_LIST);

    const filterData = async (queryToUse?: IInstancesQuery) => {
        const cQuery = queryToUse || query;
        const [columns, response] = await Promise.all([
            getInstancesGrid(t),
            instancesDataResponse.doFetch(true, cQuery),
        ]);

        const { currentPage, totalRows } = (response as any).data;

        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    };

    const applyFilters = async (query?: IInstancesQuery) => {
        const cQuery = query || DEFAULT_INSTANCE_QUERY;
        isReset.current = true;
        debouncedFilter.current.cancel();
        setQuery(cQuery);
        filterData(cQuery);
    };

    const debouncedFilter = useRef(debounce(query => filterData(query), 500));

    useEffect(() => {
        return () => {
            debouncedFilter.current.cancel();
        };
    }, []);

    useEffect(() => {
        if (isReset.current) {
            isReset.current = false;
            return;
        }
        debouncedFilter.current(query);
    }, [
        query.searchFieldPratica,
        query.searchFieldStato,
        query.searchField,
        query.sortColumn,
        query.sortOrder,
        query.page,
        query.pageSize,
    ]);

    return {
        query,
        setQuery,
        list,
        setList,
        filterData,
        applyFilters,
        loading: instancesDataResponse.loading,
    };
};
