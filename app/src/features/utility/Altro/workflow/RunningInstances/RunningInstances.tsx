import { VaporPage } from "@vapor/react-custom";
import {
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import PageTitle from "../../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { faPlusCircle } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useNavigate } from "react-router-dom";
import { DEFAULT_INSTANCE_QUERY, STATES } from "./constants";
import { useInstancesData } from "./hooks/useInstancesData";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { IInstancesQuery } from "./interfaces";

export const RunningInstances = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const { list, loading, query, setQuery, filterData, applyFilters } = useInstancesData();

    const onPageChange = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleClickCallback = (uniqueid: string) => {
        if (uniqueid) {
            navigate(`/workflows/updateistances?uniqueid=${uniqueid}`);
        }
    };

    const handleQueryChange = (key: keyof IInstancesQuery) => (event: any) => {
        const value = event?.target.value;
        setQuery({ ...query, [key]: value });
    };

    const handleShowAll = () => {
        applyFilters(DEFAULT_INSTANCE_QUERY);
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Gestione Istanze Workflows")}
                showBackButton={false}
                actionButtons={[
                    <Button
                        variant="outlined"
                        onClick={() => navigate("/workflows")}>
                        {t("Modelli")}
                    </Button>,
                    <Button
                        onClick={() => navigate("/workflows/update")}
                        startIcon={<FontAwesomeIcon icon={faPlusCircle} />}
                        variant="contained">
                        {t("Nuovo modello")}
                    </Button>,
                ]}
            />
            <VaporPage.Section>
                <Stack
                    direction="row"
                    alignItems="end"
                    gap={2}>
                    <TextField
                        value={query.searchField}
                        onChange={handleQueryChange("searchField")}
                        label={t("Nome")}
                        sx={{ width: 300 }}
                    />
                    <TextField
                        value={query.searchFieldPratica}
                        onChange={handleQueryChange("searchFieldPratica")}
                        label={t("Pratica")}
                        sx={{ width: 300 }}
                    />
                    <FormControl sx={{ width: 200 }}>
                        <InputLabel>{t("Tipo")}</InputLabel>
                        <Select
                            value={query.searchFieldStato}
                            onChange={handleQueryChange("searchFieldStato")}>
                            {STATES.map(option => (
                                <MenuItem
                                    value={option.value}
                                    key={option.value}>
                                    {t(option.label)}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <Button
                        variant="outlined"
                        onClick={() => filterData()}>
                        {t("Cerca")}
                    </Button>
                    <Button
                        variant="outlined"
                        onClick={handleShowAll}>
                        {t("Mostra tutti")}
                    </Button>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                <CustomDataGrid
                    name="runningInstances"
                    columns={list.columns}
                    data={list.rows}
                    page={list.page || 0}
                    totalRows={list.totalRows}
                    pageSize={list.pageSize}
                    loading={loading}
                    query={query}
                    setQuery={setQuery}
                    disableColumnResize={true}
                    disableColumnReorder={true}
                    onClickKey="uniqueid"
                    disableColumnMenu
                    onPageChangeCallback={onPageChange}
                    onClickCallback={handleClickCallback}
                />
            </VaporPage.Section>
        </VaporPage>
    );
};
