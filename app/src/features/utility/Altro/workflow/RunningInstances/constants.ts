import { Option } from "./interfaces";

export const STATES: Option[] = [
    { label: "Tutti", value: "0" },
    { label: "IN PAUSA", value: "1" },
    { label: "IN CORSO", value: "3" },
    { label: "COMPLETATO", value: "4" },
];

export const DEFAULT_INSTANCE_QUERY = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    searchField: "",
    searchFieldPratica: "",
    searchFieldStato: 0,
};

export const DEFAULT_ITEMS_LIST = {
    rows: [],
    columns: [],
    totalRows: 0,
    pageIndex: 0,
    pageSize: 10,
    page: 0,
};
