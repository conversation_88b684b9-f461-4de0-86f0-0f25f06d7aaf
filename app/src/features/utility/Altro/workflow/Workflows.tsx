import { useTranslation } from "@1f/react-sdk";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { <PERSON><PERSON>, <PERSON><PERSON>, TextField } from "@vapor/react-material";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import PageTitle from "../../../../custom-components/PageTitle";
import Spinner from "../../../../custom-components/Spinner";
import { defaultQuery } from "./constants";
import { useWorkflowsData } from "./useWorkflowsData";
import { faPlusCircle } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export const Workflows = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [searchParam, setSearchParam] = useState<string>("");

    const { list, loading, query, setQuery } = useWorkflowsData();

    const handleClick = (id: string | number) => {
        navigate(`/workflows/update?uniqueid=${id}`);
    };

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleResetQuery = () => {
        setQuery(defaultQuery);
        setSearchParam("");
    };

    const handleQuery = () => {
        setQuery({ ...query, searchField: searchParam });
    };

    const handleSearchUpdate = (e: any) => {
        setSearchParam(e.target.value);
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("GESTIONE MODELLI WORKFLOWS")}
                showBackButton={false}
                actionButtons={[
                    <Button
                        variant="outlined"
                        onClick={() => navigate("/workflows/runningistances")}>
                        {t("Istanze")}
                    </Button>,
                    <Button
                        onClick={() => navigate("/workflows/update")}
                        startIcon={<FontAwesomeIcon icon={faPlusCircle} />}
                        variant="contained">
                        {t("Nuovo modello")}
                    </Button>,
                ]}
            />
            <VaporPage.Section>
                <Stack
                    direction="row"
                    alignItems="end"
                    gap={2}>
                    <TextField
                        value={searchParam}
                        onChange={handleSearchUpdate}
                        label={t("Ricerca")}
                        sx={{ width: 300 }}
                    />
                    <Button
                        variant="outlined"
                        onClick={handleQuery}>
                        {t("Cerca")}
                    </Button>
                    <Button
                        variant="outlined"
                        onClick={handleResetQuery}>
                        {t("Mostra tutti")}
                    </Button>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="workflows"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page || 0}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        disableColumnMenu={true}
                        onClickKey="uniqueid"
                        onPageChangeCallback={onPageChangeCallback}
                        onClickCallback={handleClick}
                        setQuery={setQuery}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
