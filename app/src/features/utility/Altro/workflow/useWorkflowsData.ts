import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { IList } from "../../../../interfaces/general.interfaces";
import { getWorkflowsGrid } from "../../../../utilities/workflows/gridColumn";
import { useTranslation } from "@1f/react-sdk";
import { defaultQuery } from "./constants";

export const useWorkflowsData = () => {
    const { t } = useTranslation();

    const workflowsDataResponse = useGetCustom(
        "workflows/list?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterData = async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getWorkflowsGrid(t),
            workflowsDataResponse.doFetch(true, cQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    };

    useEffect(() => {
        filterData(query);
    }, [
        query.page,
        query.pageSize,
        query.searchField !== "",
        query.sortOrder,
        query.sortColumn,
    ]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: workflowsDataResponse.loading,
    };
};
