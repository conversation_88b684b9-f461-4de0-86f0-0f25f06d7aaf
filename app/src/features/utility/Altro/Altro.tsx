import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import {
    Hyperlink,
    IconButton,
    NotificationInline,
    Stack,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faCircle,
    faDotCircle,
    faXmark,
} from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import Spinner from "../../../custom-components/Spinner";

type PermissionProps = {
    showWorkflows: boolean;
    showLog: boolean;
    showLogPec: boolean;
    showBackup: boolean;
    showOggetti: boolean;
    showRecuperoCodici: boolean;
    showStatistiche: boolean;
    showImportazioneBest: boolean;
};

type element = {
    key: string;
    label: string;
    link: string;
    element?: element[];
    type: "link" | "path" | "" | "notification";
    OS: "Windows" | "Linux" | "Mac" | "All";
};

const elements: element[] = [
    {
        key: "Workflows",
        label: "Workflows",
        link: "/workflows",
        type: "path",
        OS: "All",
    },
    {
        key: "Log",
        label: "Log",
        link: "/entries",
        type: "path",
        OS: "All",
    },
    {
        key: "Log pec",
        label: "Log pec",
        link: "/logpec/logpec",
        type: "path",
        OS: "All",
    },

    {
        key: "Backup",
        label: "Backup",
        link: "/index/utilitiesmainpage#",
        type: "notification",
        OS: "All",
    },
    {
        key: "Oggetti",
        label: "Oggetti",
        link: "/item/item",
        type: "path",
        OS: "All",
    },
    {
        key: "Recupero codici pratiche",
        label: "Recupero codici pratiche",
        link: "/recoveryid",
        type: "path",
        OS: "All",
    },
    {
        key: "Statistiche",
        label: "Statistiche",
        link: "/index/utilities/",
        type: "path",
        OS: "All",
    },
    {
        key: "Termini e Condizioni",
        label: "Termini e Condizioni",
        link: "https://test.netlex.cloud/index/terminiPDF",
        type: "link",
        OS: "All",
    },
    {
        key: "Importazione Best Italia",
        label: "Importazione Best Italia",
        link: "/tools/uploadbsttl",
        type: "path",
        OS: "All",
    },
    {
        key: "Strumenti",
        label: "Strumenti",
        link: "",
        type: "",
        OS: "All",
        element: [
            {
                key: "7zip",
                label: "7zip",
                link: "http://www.7-zip.org/a/7z1604.exe",
                type: "link",
                OS: "Windows",
            },
            {
                key: "Adobe Reader",
                label: "Adobe Reader",
                link: "https://get.adobe.com/reader/",
                type: "link",
                OS: "All",
            },
            {
                key: "Dike",
                label: "Dike",
                link: `https://rinnovofirma.infocert.it/download/x86_64/latest`,
                type: "link",
                OS: "Linux",
            },
            {
                key: "Dike",
                label: "Dike",
                link: `https://rinnovofirma.infocert.it/download/darwin/latest`,
                type: "link",
                OS: "Mac",
            },
            {
                key: "Dike",
                label: "Dike",
                link: `https://rinnovofirma.infocert.it/download/win32/latest`,
                type: "link",
                OS: "Windows",
            },
            {
                key: "OpenWebStart",
                label: "OpenWebStart",
                link: "https://openwebstart.com/",
                type: "link",
                OS: "All",
            },
            {
                key: "MnlSignVerifier",
                label: "MnlSignVerifier",
                link: "https://drive.google.com/file/d/1mRmOMs8yvZqs63pwLAJmsJJzD5Z9nZ-g/view",
                type: "link",
                OS: "Windows",
            },
            {
                key: "Free pdf compressor",
                label: "Free pdf compressor",
                link: "http://www.softsea.com/download/Free-PDF-Compressor.html",
                type: "link",
                OS: "Windows",
            },
            {
                key: "Sumatra PDF",
                label: "Sumatra PDF",
                link: "https://www.sumatrapdfreader.org/dl/SumatraPDF-3.1.2-install.exe",
                type: "link",
                OS: "Windows",
            },
            {
                key: "Per connessione da remoto",
                label: "Per connessione da remoto",
                link: "",
                type: "",
                OS: "All",
                element: [
                    {
                        key: "TeamViewer",
                        label: "TeamViewer",
                        link: `http://download.teamviewer.com/download/teamviewer_i386.deb`,
                        type: "link",
                        OS: "Linux",
                    },
                    {
                        key: "TeamViewer",
                        label: "TeamViewer",
                        link: `http://download.teamviewer.com/download/version_14x/TeamViewerQS.dmg`,
                        type: "link",
                        OS: "Mac",
                    },
                    {
                        key: "TeamViewer",
                        label: "TeamViewer",
                        link: `http://download.teamviewer.com/download/version_14x/TeamViewerQS.exe`,
                        type: "link",
                        OS: "Windows",
                    },
                    {
                        key: "Bomgar",
                        label: "Bomgar",
                        link: "https://remoteaccess.teamsystem.com/",
                        type: "link",
                        OS: "All",
                    },
                ],
            },
        ],
    },
];

const Links = ({
    elements,
    level,
    setShowNotification,
    permisions,
}: {
    elements: element[];
    level: number;
    setShowNotification: any;
    permisions: PermissionProps;
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const isElementVisible = (
        element: element,
        permissions: PermissionProps
    ): boolean => {
        switch (element.key) {
            case "Workflows":
                return permissions.showWorkflows;
            case "Log":
                return permissions.showLog;
            case "Log pec":
                return permissions.showLogPec;
            case "Backup":
                return permissions.showBackup;
            case "Oggetti":
                return permissions.showOggetti;
            case "Recupero codici pratiche":
                return permissions.showRecuperoCodici;
            case "Statistiche":
                return permissions.showStatistiche;
            case "Importazione Best Italia":
                return permissions.showImportazioneBest;
            default:
                return true;
        }
    };

    const showElement = (element: element, permisions: PermissionProps) => {
        const hasPermission = isElementVisible(element, permisions);
        const platform = (navigator as any).userAgent;
        const isSamePlatform = platform
            .toLowerCase()
            .includes(element.OS.toLowerCase());
        const show =
            hasPermission && (element.OS === "All" ? true : isSamePlatform);
        return show;
    };
    

    return (
        <Stack gap={1}>
            {elements
                .filter(element => showElement(element, permisions))
                .map(element => (
                    <div key={element.key}>
                        <Stack
                            direction="row"
                            gap={1}
                            alignItems="center"
                            paddingLeft={level * 5}>
                            <FontAwesomeIcon
                                icon={level % 2 == 0 ? faCircle : faDotCircle}
                                style={{
                                    width: "5px",
                                    height: "5px",
                                }}
                            />
                            <Hyperlink
                                href={
                                    element.type === "link"
                                        ? element.link
                                        : undefined
                                }
                                color={element.link === "" ? "inherit" : ""}
                                onClick={(e: {
                                    preventDefault: () => void;
                                }) => {
                                    e.preventDefault();
                                    element.type === "path"
                                        ? navigate(element.link)
                                        : element.type === "notification"
                                        ? setShowNotification(true)
                                        : element.type === "link"
                                        ? window.open(element.link, "_blank")
                                        : undefined;
                                }}
                                underline={
                                    element.link === "" ? "none" : "hover"
                                }
                                variant={
                                    element.link === ""
                                        ? "bodyLarge700"
                                        : "body500"
                                }>
                                {t(element.label)}
                            </Hyperlink>
                        </Stack>
                        {element.element && (
                            <Links
                                setShowNotification={setShowNotification}
                                elements={element.element}
                                level={level + 1}
                                permisions={permisions}
                            />
                        )}
                    </div>
                ))}
        </Stack>
    );
};
export const Other = () => {
    const { t } = useTranslation();
    const [showNotification, setShowNotification] = useState(false);

    const { data, hasLoaded, doFetch, loading } = useGetCustom(
        "index/utilitiesmainpage"
    );

    useEffect(() => {
        doFetch(true);
    }, []);

    const handleShowNotification = () => setShowNotification(false);

    const defaultPermissions = {
        showWorkflows: false,
        showLog: false,
        showLogPec: false,
        showBackup: false,
        showOggetti: false,
        showRecuperoCodici: false,
        showStatistiche: false,
        showImportazioneBest: false,
    };

    const permissions: PermissionProps = hasLoaded
        ? {
              ...defaultPermissions,
              showWorkflows:
                  ["betastudiolegal", "betadebug"].includes(
                      data.provisioningRow.subdomain_name
                  ) || data.configs.app.workflow_bool,
              showLog: Boolean(
                  (data.isLawyerUser && data.configs.app.log_bool) ||
                      (!data.configs.app.log_bool &&
                          (data.loggedUser.isSuperAdmin ||
                              data.permissions[50]?.r))
              ),
              showLogPec: Boolean(data.permissions?.[44]?.r),
              showBackup: Boolean(
                  data.isLawyerUser &&
                      (data.configs.app.backup_bool ||
                          data.loggedUser.isSuperAdmin)
              ),
              showOggetti: data.configs.app.item_bool,
              showRecuperoCodici:
                  data.configs.app.practices_bool &&
                  !data.loggedUser.isNetlexeasynotaUser,
              showStatistiche: data.configs.app.statistics_bool,
              showImportazioneBest: true,
              ...data,
          }
        : defaultPermissions;

    return (
        <VaporPage>
            <PageTitle
                title={t("UTILITÀ")}
                showBackButton={false}
            />
            <>
                {showNotification && (
                    <VaporPage.Section>
                        <NotificationInline
                            variant="outlined"
                            severity="warning"
                            action={
                                <IconButton onClick={handleShowNotification}>
                                    <FontAwesomeIcon icon={faXmark} />
                                </IconButton>
                            }
                            sx={{ width: 600 }}>
                            {t(
                                "Attenzione: Per richiedere il backup dei dati , è possibile aprire una richiesta attraverso il pulsante di supporto. La richiesta sarà successivamente elaborata dai tecnici."
                            )}
                        </NotificationInline>
                    </VaporPage.Section>
                )}
            </>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <Links
                        elements={elements}
                        level={0}
                        setShowNotification={setShowNotification}
                        permisions={permissions}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
