import { IEntriesQuery } from "./interfaces";
import { startOfYear, endOfYear } from "date-fns";

export const DEFAULT_ENTRIES_QUERY: IEntriesQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "qualifica",
    sortOrder: "desc",
    startDate: startOfYear(new Date()),
    endDate: endOfYear(new Date()),
    qualification: -1,
    user: -1,
    operation: "",
    search:0
};

export const DEFAULT_ENTRIES_LIST = {
    rows: [],
    columns: [],
    totalRows: 0,
    pageIndex: 0,
    pageSize: 10,
    page: 0,
};

export const QUALIFICATION_OPTIONS = [
    { label: "Tutte le qualifiche", value: "-1" },
    { label: "Altro", value: "5" },
    { label: "Avvocato", value: "1" },
    { label: "Basic", value: "6" },
    { label: "<PERSON><PERSON><PERSON>", value: "3" },
    { label: "Segretaria", value: "4" },
    { label: "Utente Esterno", value: "2" },
];
