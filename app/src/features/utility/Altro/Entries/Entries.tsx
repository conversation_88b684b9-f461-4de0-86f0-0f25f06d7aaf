import { useTranslation } from "@1f/react-sdk";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { Button } from "@vapor/react-material";
import { useEffect } from "react";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import PageTitle from "../../../../custom-components/PageTitle";
import Spinner from "../../../../custom-components/Spinner";
import { DEFAULT_ENTRIES_QUERY } from "./constants";
import { Filters } from "./Filters";
import { useEntriesData } from "./hooks/useEntriesData";
import useGetCustom from "../../../../hooks/useGetCustom";
import { saveFile } from "../../../../utilities/utils";

export const Entries = () => {
    const { t } = useTranslation();
    const { list, loading, query, setQuery } = useEntriesData();

    const exportCsvRequest = useGetCustom(
        "entries/export-csv?noTemplateVars=true"
    );

    const handleExportCsv = () => {
        exportCsvRequest.doFetch(true, query);
    };

    useEffect(() => {
        if (exportCsvRequest.hasLoaded && exportCsvRequest.data) {
            saveFile({
                data: exportCsvRequest.data,
                fileName: "Accessi.csv",
                type: "csv",
            });
        }
    }, [exportCsvRequest.hasLoaded, exportCsvRequest.data]);

    const onPageChange = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleQueryChange =
        (key: keyof typeof DEFAULT_ENTRIES_QUERY) => (event: any) => {
            if (key === "startDate" || key === "endDate") {
                setQuery({ ...query, [key]: event });
            } else {
                const value = event?.target.value;
                setQuery({ ...query, [key]: value });
            }
        };

    return (
        <VaporPage>
            <PageTitle
                title={t("Accessi")}
                showBackButton={false}
                actionButtons={[
                    <Button
                        variant="outlined"
                        onClick={handleExportCsv}>
                        {t("Esporta in csv")}
                    </Button>,
                ]}
            />
            <VaporPage.Section>
                <Filters
                    handleQueryChange={handleQueryChange}
                    query={query}
                    setQuery={setQuery}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="entries"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page || 0}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        setQuery={setQuery}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        onClickKey="id"
                        disableColumnMenu
                        onPageChangeCallback={onPageChange}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
