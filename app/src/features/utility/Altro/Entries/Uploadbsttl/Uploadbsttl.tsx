import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    FormControl,
    FormLabel,
    MenuItem,
    NotificationInline,
    Select,
    Stack,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from "@vapor/react-material";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpload } from "@fortawesome/free-solid-svg-icons";
import { Typography } from "@vapor/react-extended";
import usePostCustom from "../../../../../hooks/usePostCustom";

interface LawyerDataFormat {
    nomeutente: string;
    id: string;
}

type response = {
    result: string;
    counter: number;
    numOk: number;
    numErrors: number;
};

const csvColumns = [
    "Denominazione",
    "Controparte",
    "Client_surname",
    "Client_fiscal_code",
    "Client_vat",
    "Client_date_of_birth",
    "Client_address",
    "Client_province",
    "Court",
    "Client_city",
    "Client_zip_code",
    "Valore",
    "Stato",
    "Tipologia",
    "Dominus",
];

export const Uploadbsttl = () => {
    const { t } = useTranslation();
    const [lawyers, setLawyers] = useState<LawyerDataFormat[]>([]);
    const [selectedLawyer, setSelectedLawyer] = useState("");
    const [fileContent, setFileContent] = useState<Blob | null>(null);
    const [fileName, setFileName] = useState<string | null>(null);
    const [csvResponseData, setCsvResponseData] = useState<
        response | undefined
    >(undefined);
    const [failedRows, setFailedRows] = useState<any>({});


    const handleFileRead = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setFileName(file.name);
            const fileBlob: Blob = file;
            setFileContent(fileBlob);
        }
    };

    const uploadsttlRequest = useGetCustom("tools/uploadbsttl");

    useEffect(() => {
        uploadsttlRequest.doFetch(true);
    }, []);

    useEffect(() => {
        if (uploadsttlRequest.hasLoaded) {
            setLawyers(uploadsttlRequest.data.lawyers);
            setSelectedLawyer(uploadsttlRequest.data.lawyers[0].id);
        }
    }, [uploadsttlRequest.loading]);

    const saveCsvResponse = usePostCustom("tools/upload-csv");

    const uploadCsv =
        (selectedLawyer: string, fileContent: Blob | null) => () => {
            if (selectedLawyer && fileContent) {
                const formData = new FormData();
                formData.append("lawyer", selectedLawyer);
                formData.append("csvuploaded", fileContent);
                saveCsvResponse.doFetch(true, formData);
            }
        };

    useEffect(() => {
        if (saveCsvResponse.hasLoaded) {
            setCsvResponseData(saveCsvResponse.data.uploadresult);
            setFailedRows(saveCsvResponse.data.failed);
        }
    }, [saveCsvResponse.hasLoaded]);

    const handleChangeLawyer = (e: any) => setSelectedLawyer(e.target.value);

    return (
        <VaporPage>
            <PageTitle
                title={t("Upload csv")}
                description={t("caricamento file")}
                showBackButton={false}
            />
            <VaporPage.Section>
                <NotificationInline
                    variant="outlined"
                    severity="info">
                    <Typography>
                        {t(
                            "E' possibile caricare file in formato .csv In cui sia stato utilizzato come separatore di campo il carattere Punto e virgola"
                        )}
                    </Typography>
                    <Typography>
                        {t("Il file deve avere le seguenti colonne:")}
                    </Typography>
                    <Stack
                        direction="row"
                        gap={1}
                        flexWrap={"wrap"}
                        pt={2}>
                        {csvColumns.map(column => (
                            <Typography
                                key={column}
                                style={{
                                    border: "1px solid black",
                                    padding: 4,
                                }}>
                                {t(column)}
                            </Typography>
                        ))}
                    </Stack>
                </NotificationInline>
            </VaporPage.Section>
            <VaporPage.Section>
                {csvResponseData && (
                    <NotificationInline
                        variant="outlined"
                        severity={
                            csvResponseData === null ||
                            csvResponseData.counter == -1 ||
                            csvResponseData.numErrors > 0
                                ? "error"
                                : csvResponseData.numErrors == 0
                                ? "success"
                                : "success"
                        }>
                        <Stack>
                            <Typography>
                                {csvResponseData.result == "1"
                                    ? t("Importazione effettuata!")
                                    : t("Si è verificato un errore!")}
                            </Typography>
                            <Typography>{`${t("N° Tot righe processate")}: ${
                                csvResponseData.counter >= 0
                                    ? csvResponseData.counter
                                    : 0
                            }`}</Typography>
                            <Typography>{`${t("N° Righe inserite")}:${
                                csvResponseData.numOk
                            }`}</Typography>
                            <Typography>{`${t("N° Righe in errore")}:${
                                csvResponseData.numErrors
                            }`}</Typography>
                        </Stack>
                    </NotificationInline>
                )}
                {csvResponseData && Object.entries(failedRows).length > 0 && (
                    <TableContainer sx={{ width: 600, pt: 5 }}>
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell>{t("Riga")}</TableCell>
                                    <TableCell>{t("Campo errato")}</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {Object.entries(failedRows).map((row: any) => (
                                    <TableRow>
                                        <TableCell>{row[0]}</TableCell>
                                        <TableCell style={{ whiteSpace: 'pre-line' }}>
                                            {Object.keys(row[1]).join("\n")}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                )}
            </VaporPage.Section>
            <VaporPage.Section>
                <Stack gap={2}>
                    {uploadsttlRequest.hasLoaded && (
                        <FormControl sx={{ width: 400 }}>
                            <FormLabel>
                                {t("Selezionare un avvocato")}
                            </FormLabel>
                            <Select
                                value={selectedLawyer}
                                onChange={handleChangeLawyer}>
                                {lawyers.map(l => (
                                    <MenuItem
                                        key={l.id}
                                        value={l.id}>
                                        {l.nomeutente}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    )}

                    <Button
                        sx={{ width: 200 }}
                        variant="outlined"
                        component="label">
                        <Typography>{t("Selezionare un file")}</Typography>
                        <input
                            type="file"
                            hidden
                            onChange={handleFileRead}
                        />
                    </Button>
                    {fileName && <Typography>{fileName}</Typography>}
                    {fileName && (
                        <Button
                            startIcon={<FontAwesomeIcon icon={faUpload} />}
                            sx={{ width: 200 }}
                            variant="outlined"
                            onClick={uploadCsv(selectedLawyer, fileContent)}>
                            {t("Carica")}
                        </Button>
                    )}
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
