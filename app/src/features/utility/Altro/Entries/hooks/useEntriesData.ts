import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { IList } from "../../../../../interfaces/general.interfaces";
import { getEntriesGrid } from "../../../../../utilities/entries/gridColumn";
import { DEFAULT_ENTRIES_LIST, DEFAULT_ENTRIES_QUERY } from "../constants";
import { IEntriesQuery } from "../interfaces";

export const useEntriesData = () => {
    const { t } = useTranslation();

    const entriesDataResponse = useGetCustom(
        "entries/list?noTemplateVars=true"
    );

    const [query, setQuery] = useState<IEntriesQuery>(DEFAULT_ENTRIES_QUERY);
    const [list, setList] = useState<IList<any>>(DEFAULT_ENTRIES_LIST);

    const filterData = async (query: IEntriesQuery) => {
        const { search, ...newQuery } = query;

        const [columns, response]: any = await Promise.all([
            getEntriesGrid(t),
            entriesDataResponse.doFetch(true, newQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: query?.page,
            pageSize: query?.pageSize ?? 10,
        });
    };

    useEffect(() => {
        filterData(query);
    }, [
        query.endDate,
        query.operation,
        query.page,
        query.pageSize,
        query.qualification,
        query.sortColumn,
        query.sortOrder,
        query.startDate,
        query.user,
        query.search,
    ]);

    return {
        defaultEntriesQuery: DEFAULT_ENTRIES_QUERY,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: entriesDataResponse.loading,
    };
};
