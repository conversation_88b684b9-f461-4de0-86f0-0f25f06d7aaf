import { useTranslation } from "@1f/react-sdk";
import { Too<PERSON>bar, VaporPage } from "@vapor/react-custom";
import {
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    FormHelperText,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import PageTitle from "../../../../custom-components/PageTitle";
import { ITEM_DATA, TYPE, UNITS_OF_MEASURE } from "./constants";
import Spinner from "../../../../custom-components/Spinner";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import usePostCustom from "../../../../hooks/usePostCustom";

export const ItemsCreateUpdate = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [params] = useSearchParams();
    const uniqueId = params.get("uniqueid");

    const [data, setData] = useState(ITEM_DATA);
    const [errors, setErrors] = useState<any>({});
    const [confirmDeleteModal, setConfirmDeleteModal] = useState(false);

    const deleteItemRequest = usePostCustom("item/delete");

    const getItemsRequest = usePostCustom(
        "item/getrowdata?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueId) {
            getItemsRequest.doFetch(true, { uniqueId: uniqueId });
        }
    }, [uniqueId]);



    useEffect(() => {
        if (getItemsRequest.hasLoaded) {
            setData(getItemsRequest.data.form);
        }
    }, [getItemsRequest.hasLoaded]);

    const saveRequest = usePostCustom("item/save");

    const saveItem = () => {
        saveRequest.doFetch(true, {
            uniqueid: data.uniqueid,
            nome: data.nome,
            tipo: data.tipo,
            unita_misura: data.unita_misura,
            quantita: data.quantita,
            valore: data.valore,
            ...(data.fatturabile === "1" && { fatturabile: "on" }),
        });
    };

    useEffect(() => {
        if (saveRequest.hasLoaded || deleteItemRequest.hasLoaded) {
            navigate("/item/item");
        }
    }, [saveRequest.hasLoaded, deleteItemRequest.hasLoaded]);

    const handleUpdate = (key: string) => (event: any) => {
        if (key === "fatturabile") {
            const value = event.target.checked;
            setData({
                ...data,
                [key]: value === true ? "1" : "0",
            });
        } else {
            const value = event.target.value;
            setData({
                ...data,
                [key]: value,
            });
            setErrors((prevErrors: any) => ({ ...prevErrors, [key]: "" }));
        }
    };

    const handleSave = () => {
        const newErrors: any = {};
        if (!data.nome) newErrors.nome = t("Titolo obbligatorio");
        if (!data.tipo) newErrors.tipo = t("Campo obbligatorio");
        if (!data.unita_misura)
            newErrors.unita_misura = t("Campo obbligatorio");

        if (Object.keys(newErrors).length === 0) {
            saveItem();
        } else {
            setErrors(newErrors);
        }
    };

    const handleCloseModal = () => {
        setConfirmDeleteModal(false);
    };

    const handleConfirmModal = () => {
        deleteItemRequest.doFetch(true, { uniqueid: uniqueId });
    };

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack
                            direction="row"
                            gap={2}
                            pr={10}>
                            <Button
                                variant="outlined"
                                color="error"
                                onClick={() => setConfirmDeleteModal(true)}>
                                {t("Elimina")}
                            </Button>
                            <Button
                                variant="outlined"
                                onClick={handleSave}>
                                {t("Conferma")}
                            </Button>
                        </Stack>
                    }
                />
            }>
            <PageTitle
                title="Dati oggetto"
                pathToPrevPage="/item/item"
            />
            <ConfirmModal
                agree="Conferma"
                decline="Annulla"
                handleAgree={handleConfirmModal}
                handleDecline={handleCloseModal}
                open={confirmDeleteModal}
                colorConfirmButton="error"
                confirmText={t("Eliminare definitivamente l'oggetto?")}
                title="Attenzione!"
            />
            {getItemsRequest.loading ? (
                <Spinner />
            ) : (
                <VaporPage.Section>
                    <Stack
                        sx={{ width: 300 }}
                        gap={2}>
                        <TextField
                            label={t("Nome") + " *"}
                            value={data.nome}
                            onChange={handleUpdate("nome")}
                            error={!!errors.nome}
                            helperText={errors.nome}
                        />
                        <FormControl
                            sx={{ minWidth: 300 }}
                            error={!!errors.tipo}>
                            <InputLabel>{t("Tipo") + " *"}</InputLabel>
                            <Select
                                value={data.tipo}
                                onChange={handleUpdate("tipo")}>
                                {TYPE.map(option => (
                                    <MenuItem
                                        value={option.value}
                                        key={option.value}>
                                        {t(option.label)}
                                    </MenuItem>
                                ))}
                            </Select>
                            {errors.tipo && (
                                <FormHelperText error>
                                    {errors.tipo}
                                </FormHelperText>
                            )}
                        </FormControl>

                        <FormControl
                            sx={{ minWidth: 300 }}
                            error={!!errors.unita_misura}>
                            <InputLabel>
                                {t("Unità di misura") + " *"}
                            </InputLabel>
                            <Select
                                value={data.unita_misura}
                                onChange={handleUpdate("unita_misura")}>
                                {UNITS_OF_MEASURE.map(option => (
                                    <MenuItem
                                        value={option.value}
                                        key={option.value}>
                                        {t(option.label)}
                                    </MenuItem>
                                ))}
                            </Select>
                            {errors.unita_misura && (
                                <FormHelperText error>
                                    {errors.unita_misura}
                                </FormHelperText>
                            )}
                        </FormControl>
                        <TextField
                            label={t("Quantità")}
                            value={data.quantita}
                            onChange={handleUpdate("quantita")}
                            error={!!errors.quantita}
                            helperText={errors.quantita}
                        />
                        <TextField
                            label={t("Valore")}
                            value={data.valore}
                            onChange={handleUpdate("valore")}
                            error={!!errors.valore}
                            helperText={errors.valore}
                        />
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={data.fatturabile === "1"}
                                    onChange={handleUpdate("fatturabile")}
                                />
                            }
                            label={t("Fatturabile")}
                        />
                    </Stack>
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
