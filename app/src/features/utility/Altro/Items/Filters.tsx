import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import { DEFAULT_ITEMS_QUERY, TYPE, UNITS_OF_MEASURE } from "./constants";
import { IItemsQuery } from "./interfaces";

export const Filters = ({
    handleQueryChange,
    query,
    applyFilters,
}: {
    handleQueryChange: (key: keyof IItemsQuery) => (event: any) => void;
    query: IItemsQuery;
    applyFilters: (query: IItemsQuery) => void;
}) => {
    const { t } = useTranslation();

    const handleSearch = () => {
        applyFilters(query);
    };

    const handleReset = () => {
        applyFilters(DEFAULT_ITEMS_QUERY);
    };

    return (
        <Stack direction="row" gap={2} alignItems="end">
            <TextField
                sx={{ width: 300 }}
                label={t("Nome")}
                value={query.nomeSearch}
                onChange={handleQueryChange("nomeSearch")}
            />
            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Tipo")}</InputLabel>
                <Select
                    onChange={handleQueryChange("tipoSearch")}
                    value={query.tipoSearch}
                >
                    {[{ label: "Tutti", value: "-1" }, ...TYPE].map(
                        (option: any) => (
                            <MenuItem key={option.value} value={option.value}>
                                {t(option.label)}
                            </MenuItem>
                        ),
                    )}
                </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Unità di misura")}</InputLabel>
                <Select
                    onChange={handleQueryChange("unitaMisuraSearch")}
                    value={query.unitaMisuraSearch}
                >
                    {[{ label: "Tutti", value: "-1" }, ...UNITS_OF_MEASURE].map(
                        (option: any) => (
                            <MenuItem key={option.value} value={option.value}>
                                {t(option.label)}
                            </MenuItem>
                        ),
                    )}
                </Select>
            </FormControl>

            <Button variant="outlined" onClick={handleSearch}>
                {t("Cerca")}
            </Button>
            <Button variant="outlined" onClick={handleReset}>
                {t("Mostra tutti")}
            </Button>
        </Stack>
    );
};
