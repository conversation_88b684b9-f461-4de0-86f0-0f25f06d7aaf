import { useTranslation } from "@1f/react-sdk";
import { useEffect, useRef, useState } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { IList } from "../../../../../interfaces/general.interfaces";
import { getItemsGrid } from "../../../../../utilities/items/gridColumn";
import { DEFAULT_ITEMS_LIST, DEFAULT_ITEMS_QUERY } from "../constants";
import { IItemsQuery } from "../interfaces";
import { debounce } from "lodash";

const STORAGE_KEY_PREFIX = "itemsFilters";

const getSessionStorageQuery = () => {
    const savedNomeSearch = sessionStorage.getItem(
        `${STORAGE_KEY_PREFIX}.nomeSearch`
    );
    const savedTipoSearch = sessionStorage.getItem(
        `${STORAGE_KEY_PREFIX}.tipoSearch`
    );
    const savedUnitaMisuraSearch = sessionStorage.getItem(
        `${STORAGE_KEY_PREFIX}.unitaMisuraSearch`
    );

    let storedQuery = { ...DEFAULT_ITEMS_QUERY };

    if (savedNomeSearch !== null) {
        storedQuery.nomeSearch = savedNomeSearch;
    }
    if (savedTipoSearch !== null) {
        storedQuery.tipoSearch = savedTipoSearch;
    }
    if (savedUnitaMisuraSearch !== null) {
        storedQuery.unitaMisuraSearch = savedUnitaMisuraSearch;
    }
    return storedQuery;
};

const saveSessionStorageQuery = (query: IItemsQuery) => {
    sessionStorage.setItem(
        `${STORAGE_KEY_PREFIX}.nomeSearch`,
        query.nomeSearch || ""
    );
    sessionStorage.setItem(
        `${STORAGE_KEY_PREFIX}.tipoSearch`,
        query.tipoSearch || "-1"
    );
    sessionStorage.setItem(
        `${STORAGE_KEY_PREFIX}.unitaMisuraSearch`,
        query.unitaMisuraSearch || "-1"
    );
};

export const useItemsData = () => {
    const { t } = useTranslation();

    const isReset = useRef(false);

    const entriesDataResponse = useGetCustom("item/list?noTemplateVars=true");

    const initialQuery = getSessionStorageQuery();
    const [query, setQuery] = useState<IItemsQuery>(initialQuery);
    const [list, setList] = useState<IList<any>>(DEFAULT_ITEMS_LIST);

    const filterData = async (queryToUse?: IItemsQuery | undefined) => {
        let cQuery: any = queryToUse || DEFAULT_ITEMS_QUERY;

        const [columns, response]: any = await Promise.all([
            getItemsGrid(t),
            entriesDataResponse.doFetch(true, cQuery),
        ]);

        const { currentPage, totalRows } = response.data;
        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    };

    const applyFilters = async (query?: IItemsQuery | undefined) => {
        let cQuery: any = query || DEFAULT_ITEMS_QUERY;
        isReset.current = true;
        debouncedFilter.current.cancel();
        setQuery(cQuery);
        filterData(cQuery);
    };

    const debouncedFilter = useRef(debounce(query => filterData(query), 500));

    useEffect(() => {
        return () => {
            debouncedFilter.current.cancel();
        };
    }, []);

    useEffect(() => {
        if (isReset.current) {
            isReset.current = false;
            return;
        }
        debouncedFilter.current(query);
    }, [
        query.nomeSearch,
        query.tipoSearch,
        query.unitaMisuraSearch,
        query.sortColumn,
        query.sortOrder,
        query.page,
        query.pageSize,
    ]);

    useEffect(() => {
        saveSessionStorageQuery(query);
    }, [query.nomeSearch, query.tipoSearch, query.unitaMisuraSearch]);

    return {
        query,
        setQuery,
        list,
        setList,
        filterData,
        applyFilters,
        loading: entriesDataResponse.loading,
    };
};
