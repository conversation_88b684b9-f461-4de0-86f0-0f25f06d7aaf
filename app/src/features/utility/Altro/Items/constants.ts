import { IItemsQuery, IOption } from "./interfaces";

export const DEFAULT_ITEMS_QUERY: IItemsQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "tipo",
    sortOrder: "desc",
    nomeSearch: "",
    tipoSearch: "-1",
    unitaMisuraSearch: "-1",
};

export const DEFAULT_ITEMS_LIST = {
    rows: [],
    columns: [],
    totalRows: 0,
    pageIndex: 0,
    pageSize: 10,
    page: 0,
};

export const UNITS_OF_MEASURE: IOption[] = [
    { label: "Ore", value: "1" },
    { label: "Minuti", value: "2" },
    { label: "Pezzo", value: "3" },
];

export const TYPE: IOption[] = [
    { label: "Timesheet", value: "1" },
    { label: "Movimento", value: "2" },
    { label: "Impegno", value: "3" },
    { label: "Udienza", value: "4" },
    { label: "Libero", value: "5" },
    { label: "Listino", value: "6" },
    { label: "Tariffario", value: "7" },
];

export const TYPE_MAP = {
    "1": "Timesheet",
    "2": "Movimento",
    "3": "Impegno",
    "4": "Udienza",
    "5": "Libero",
    "6": "Listino",
    "7": "Tariffario",
};
export const ITEM_DATA = {
    nome: "",
    tipo: "",
    unita_misura: "",
    quantita: "",
    valore: "",
    fatturabile: "",
    uniqueid: "",
    modificato_il: null,
    modificatoda: null,
};
