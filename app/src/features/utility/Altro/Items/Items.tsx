import { useTranslation } from "@1f/react-sdk";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { Button } from "@vapor/react-material";
import { useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid.tsx";
import PageTitle from "../../../../custom-components/PageTitle";
import Spinner from "../../../../custom-components/Spinner.tsx";
import { Filters } from "./Filters.tsx";
import { useItemsData } from "./hooks/useItemsData.ts";
import { IItemsQuery } from "./interfaces.ts";

export const Items = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { query, list, loading, setQuery, applyFilters } = useItemsData();

    const handleClickCallback = (uniqueid: string) => {
        if (uniqueid) {
            navigate(`/item/item/update?uniqueid=${uniqueid}`);
        }
    };

    const handleQueryChange = useCallback(
        (key: keyof IItemsQuery) => (event: any) => {
            const value = event?.target.value;
            setQuery(prevQuery => ({
                ...prevQuery,
                [key]: value,
            }));
        },
        [setQuery]
    );

    const onPageChange = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleAddItem = () => navigate("/item/item/update");

    return (
        <VaporPage>
            <PageTitle
                title={t("Oggetto")}
                showBackButton={false}
                actionButtons={[
                    <Button
                        variant="outlined"
                        onClick={handleAddItem}>
                        {t("Aggiungi oggetto")}
                    </Button>,
                ]}
            />
            <VaporPage.Section>
                <Filters
                    handleQueryChange={handleQueryChange}
                    query={query}
                    applyFilters={applyFilters}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="items"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page || 0}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        setQuery={setQuery}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        onClickKey="uniqueid"
                        disableColumnMenu
                        onPageChangeCallback={onPageChange}
                        onClickCallback={handleClickCallback}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
