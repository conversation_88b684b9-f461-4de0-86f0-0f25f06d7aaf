import { VaporPage } from "@vapor/react-custom";
import { useEffect, useState } from "react";
import { Box, Card, CardContent, Stack } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { PraticheDoughnutChart } from "./StatisticsDoughnutChart";
import { useTranslation } from "@1f/react-sdk";
import { PraticheLineChart } from "./StatisticsLineChart";
import PageTitle from "../../../../custom-components/PageTitle";
import usePostCustom from "../../../../hooks/usePostCustom";

export const Statistics = () => {
    const utilitiesDataRequest = usePostCustom("index/utilities");
    useEffect(() => {
        utilitiesDataRequest.doFetch(true);
    }, []);

    const { t } = useTranslation();

    interface CardData {
        label: string;
        value: string;
    }

    const [data, setData] = useState<CardData[]>([]);

    useEffect(() => {
        if (utilitiesDataRequest.hasLoaded) {
            setData([
                {
                    label: t("Udienze"),
                    value: utilitiesDataRequest.data.hearings,
                },
                {
                    label: t("Impegni"),
                    value: utilitiesDataRequest.data.deadlines,
                },
                {
                    label: t("Pratiche"),
                    value: utilitiesDataRequest.data.archive,
                },
                {
                    label: t("Clienti"),
                    value: utilitiesDataRequest.data.customers,
                },
            ]);
        }
    }, [utilitiesDataRequest.hasLoaded]);

    return (
        <VaporPage>
            <PageTitle
                title={t("Statistiche")}
                showBackButton={false}
                description={t("Panoramica della settimana")}
            />
            <VaporPage.Section>
                {utilitiesDataRequest.hasLoaded && (
                    <Stack
                        direction="row"
                        alignSelf="center"
                        justifyContent="space-around"
                        width={3 / 4}
                        gap={5}>
                        {data.map(d => (
                            <Card sx={{ width: 1 / 4 }}>
                                <CardContent>
                                    <Stack p={2}>
                                        <Typography variant="titleSmall">
                                            {d.label}
                                        </Typography>
                                        <Typography variant="leadSmall">
                                            {d.value}
                                        </Typography>
                                    </Stack>
                                </CardContent>
                            </Card>
                        ))}
                    </Stack>
                )}
            </VaporPage.Section>
            <VaporPage.Section>
                {utilitiesDataRequest.hasLoaded && (
                    <Stack
                        direction="row"
                        alignSelf="center"
                        justifyContent="space-around"
                        width={3 / 4}>
                        <Card>
                            <CardContent>
                                <Box
                                    width={500}
                                    height={500}>
                                    <PraticheLineChart
                                        values={JSON.parse(
                                            "[" +
                                                utilitiesDataRequest.data.graph1
                                                    .replace(/'/g, '"')
                                                    .replace(
                                                        /([{,]\s*)(\w+):/g,
                                                        '$1"$2":'
                                                    ) +
                                                "]"
                                        )}
                                    />
                                </Box>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent>
                                <Box>
                                    <PraticheDoughnutChart
                                        values={JSON.parse(
                                            "[" +
                                                utilitiesDataRequest.data.graph2
                                                    .replace(/'/g, '"')
                                                    .replace(
                                                        /([{,]\s*)(\w+):/g,
                                                        '$1"$2":'
                                                    ) +
                                                "]"
                                        )}
                                    />
                                </Box>
                            </CardContent>
                        </Card>
                    </Stack>
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
