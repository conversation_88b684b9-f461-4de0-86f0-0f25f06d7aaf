import { useTranslation } from "@1f/react-sdk";
import { Stack, Typography } from "@vapor/react-material";
import { Chart as ChartJS, ArcElement, Tooltip } from "chart.js";
import { useState } from "react";
import { Doughnut } from "react-chartjs-2";

ChartJS.register(ArcElement, Tooltip);

interface PraticheDataFormat {
    label: string;
    value: number;
}

const backgroundColor = [
    "rgb(86, 118, 212)",
    "rgb(58, 123, 213)",
    "rgb(74, 144, 226)",
    "rgb(37, 84, 156)",
    "rgb(93, 173, 226)",
    "rgb(72, 104, 204)",
    "rgb(41, 128, 185)",
    "rgb(52, 152, 219)",
    "rgb(33, 97, 140)",
    "rgb(66, 134, 244)",
];

export const PraticheDoughnutChart = ({
    values,
}: {
    values: PraticheDataFormat[];
}) => {
    const [hoveredData, setHoveredData] = useState<any>(null);

    const { t } = useTranslation();
    const data = {
        labels: values.map((d) => d.label),
        datasets: [
            {
                label: "Tipologia pratiche",
                data: values.map((v) => v.value),
                backgroundColor: backgroundColor,
                borderColor: "white",
                borderWidth: 2,
            },
        ],
    };

    const options = {
        responsive: true,
        plugins: {
            legend: {
                display: false,
            },
            title: {
                display: true,
                text: t("Tipologia pratiche"),
            },
            tooltip: {
                animation: {
                    duration: 100,
                },
            },
        },
        onHover: (_event: any, elements: any) => {
            if (elements && elements.length > 0) {
                const dataIndex = elements[0].index;
                setHoveredData({
                    label: data.labels[dataIndex],
                    value: data.datasets[0].data[dataIndex],
                });
            } else {
                setHoveredData(null);
            }
        },
    };

    return (
        <Stack>
            <Doughnut style={{ minWidth: 500 }} data={data} options={options} />
            {hoveredData && (
                <Typography>
                    {hoveredData.label} : {hoveredData.value}
                </Typography>
            )}
        </Stack>
    );
};
