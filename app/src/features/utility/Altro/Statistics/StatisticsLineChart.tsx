import { useTranslation } from "@1f/react-sdk";
import {
    Chart as ChartJS,
    Tooltip,
    CategoryScale,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    ChartOptions,
    TimeScale,
    TimeScaleOptions,
} from "chart.js";
import { Line } from "react-chartjs-2";
import "chartjs-adapter-date-fns";
import { it } from "date-fns/locale";

ChartJS.register(
    LineElement,
    CategoryScale,
    LinearScale,
    PointElement,
    Title,
    Tooltip,
    Legend,
    TimeScale
);

interface PraticheDataFormat {
    ym: string;
    a: number;
}

export const PraticheLineChart = ({
    values,
}: {
    values: PraticheDataFormat[];
}) => {
    const { t } = useTranslation();
    const dates = values.map(v => v.ym);
    dates.reverse();
    const amounts = values.map(v => v.a);
    amounts.reverse();
    const chartData = {
        labels: dates,
        datasets: [
            {
                data: amounts,
                label: "",
                borderColor: "blue",
                borderWidth: 2,
            },
        ],
    };
    const options: ChartOptions<"line"> = {
        responsive: true,
        plugins: {
            legend: {
                display: false,
            },
            title: {
                display: true,
                text: t("Andamento pratiche"),
            },
        },
        scales: {
            x: {
                type: "time",
                time: {
                    locale: it,
                    unit: "month",
                    displayFormats: {
                        month: "yyyy MM",
                    },
                },
            } as unknown as TimeScaleOptions,
        },
    };

    return (
        <Line
            data={chartData}
            options={options}
        />
    );
};
