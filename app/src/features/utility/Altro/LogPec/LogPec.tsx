import { useTranslation } from "@1f/react-sdk";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { <PERSON><PERSON>, Stack } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid.tsx";
import PageTitle from "../../../../custom-components/PageTitle";
import Spinner from "../../../../custom-components/Spinner.tsx";
import { Filters } from "./Filters.tsx";
import { useLogPecData } from "./hooks/useLogData.ts";
import ToastNotification from "../../../../custom-components/ToastNotification.tsx";
import { useEffect, useState } from "react";
import { saveFile } from "../../../../utilities/utils.ts";
import ConfirmModal from "../../../../custom-components/ConfirmModal.tsx";
import { Typography } from "@vapor/react-extended";
import usePostCustom from "../../../../hooks/usePostCustom.ts";

interface LogData {
    id: string;
    data: string;
    inviata_da: string;
    oggetto: string;
    mittente: string;
    destinatario: string;
    tipo: string;
}

export const LogPec = () => {
    const { t } = useTranslation();
    const { query, list, loading, setQuery, setReload } = useLogPecData();
    const [cantExport, setCantExport] = useState(false);
    const [selectedRowData, setSelectedRowData] = useState<LogData | null>(
        null
    );
    const [exportRequestId, setExportRequestId] = useState(0);

    const exportCsv = usePostCustom("logpec/exportcsv?noTemplateVars=true");

    useEffect(() => {
        if (exportCsv.hasLoaded && exportCsv.data) {
            saveFile({
                data: exportCsv.data,
                fileName: "Logpec",
                type: "csv",
            });
        }
    }, [exportRequestId, exportCsv.hasLoaded]);

    const onPageChange = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleQueryChange = (key: any) => (event: any) => {
        if (key === "startDate" || key === "endDate") {
            setQuery({ ...query, [key]: event });
        } else {
            const value = event?.target.value;
            setQuery({ ...query, [key]: value });
        }
    };

    const handleExport = () => {
        if (list.rows.length === 0) {
            setCantExport(true);
        } else {
            const formData = new FormData();
            formData.append("mittenteSearch", query.mittenteSearch);
            formData.append("inviataSearch", query.inviataSearch);
            formData.append("startDate", query.startDate?.toISOString() ?? "");
            formData.append("endDate", query.endDate?.toISOString() ?? "");
            exportCsv.doFetch(true, formData);
            setExportRequestId(exportRequestId => exportRequestId + 1);
        }
    };

    const handleNotification = () => {
        setCantExport(false);
    };

    const handleClickCallback = (uniqueid: any) => {
        setSelectedRowData(list.rows.filter(row => row.id === uniqueid)[0]);
    };

    const handleConfirm = () => {
        setSelectedRowData(null);
    };

    return (
        <VaporPage>
            <ConfirmModal
                title="Dati log"
                agree="Conferma"
                decline="Annulla"
                handleAgree={handleConfirm}
                handleDecline={handleConfirm}
                open={selectedRowData !== null}>
                <Stack width={300}>
                    {selectedRowData &&
                        Object.keys(selectedRowData).map((value: string) => {
                            return (
                                <Typography key={value}>{`${value} : ${
                                    selectedRowData[value as keyof LogData] ??
                                    ""
                                }`}</Typography>
                            );
                        })}
                </Stack>
            </ConfirmModal>
            <PageTitle
                title={t("Log Pec")}
                showBackButton={false}
                actionButtons={[
                    <Button
                        variant="outlined"
                        onClick={handleExport}>
                        {t("Esporta in csv")}
                    </Button>,
                ]}
            />
            <ToastNotification
                setShowNotification={handleNotification}
                severity="warning"
                showNotification={cantExport}
                text={t("Non è presente alcun log da esportare")}
            />
            <VaporPage.Section>
                <Filters
                    handleQueryChange={handleQueryChange}
                    setQuery={setQuery}
                    setReload={setReload}
                    query={query}
                />
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="logpec"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page || 0}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        onClickKey="id"
                        onPageChangeCallback={onPageChange}
                        disableColumnMenu
                        onClickCallback={handleClickCallback}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
