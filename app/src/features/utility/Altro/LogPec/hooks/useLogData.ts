import { useTranslation } from "@1f/react-sdk";
import { useEffect, useRef, useState } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { IList } from "../../../../../interfaces/general.interfaces";
import { getLogPecGrid } from "../../../../../utilities/LogPec/gridColumn";
import { defaultLogPecList, defaultLogPecQuery } from "../constants";
import { ILogPecQuery } from "../interfaces";
import { debounce } from "lodash";
import { format } from "date-fns";

export const useLogPecData = () => {
    const { t } = useTranslation();

    const entriesDataResponse = useGetCustom("logpec/list?noTemplateVars=true");

    const [query, setQuery] = useState<ILogPecQuery>(defaultLogPecQuery);
    const [list, setList] = useState<IList<any>>(defaultLogPecList);
    const [reload, setReload] = useState(false);

    const filterData = async (query?: ILogPecQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getLogPecGrid(t),
            entriesDataResponse.doFetch(true, cQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    };

    const debouncedFilter = useRef(debounce(query => filterData(query), 500));

    useEffect(() => {
        return () => {
            debouncedFilter.current.cancel();
        };
    }, []);

    useEffect(() => {
        debouncedFilter.current({
            inviataSearch: query.inviataSearch,
            mittenteSearch: query.mittenteSearch,
            page: query.page,
            pageSize: query.pageSize,
            endDate:
                query.endDate instanceof Date
                    ? format(query.endDate, "dd/MM/yyyy")
                    : "",
            startDate:
                query.startDate instanceof Date
                    ? format(query.startDate, "dd/MM/yyyy")
                    : "",
            sortColumn: query.sortColumn,
            sortOrder: query.sortOrder,
        });
        setReload(false);
    }, [query, reload]);

    useEffect(() => {
        setReload(false);
    }, [reload]);

    return {
        defaultLogPecQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: entriesDataResponse.loading,
        setReload,
    };
};
