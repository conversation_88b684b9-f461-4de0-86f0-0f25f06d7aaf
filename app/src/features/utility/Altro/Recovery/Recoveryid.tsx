import { useTranslation } from "@1f/react-sdk";
import { faCheckSquare } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    AlertProps,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from "@mui/material";
import { VaporPage } from "@vapor/react-custom";
import {
    Button,
    NotificationInline,
    Stack,
    TextField,
} from "@vapor/react-material";
import { useEffect, useState } from "react";
import PageTitle from "../../../../custom-components/PageTitle";
import { useNavigate } from "react-router-dom";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import SpinnerButton from "../../../../custom-components/SpinnerButton";

export const RecoveryId = () => {
    const { t } = useTranslation();

    const notificationStatus = {
        numberError: {
            show: true,
            message: t("Inserire un codice numerico valido"),
            severity: "error" as AlertProps["severity"],
        },
        verifiedCodeWarning: {
            show: true,
            message: t("Codice già verificato"),
            severity: "info" as AlertProps["severity"],
        },
        verifySuccess: {
            show: true,
            message: t("Codice verificato con successo"),
            severity: "success" as AlertProps["severity"],
        },
        existingError: {
            show: true,
            message: t("Pratica già presente"),
            severity: "warning" as AlertProps["severity"],
        },
        recoverySuccess: {
            show: true,
            message: t("Codici recuperati con successo"),
            severity: "success" as AlertProps["severity"],
        },
        recoveryError: {
            show: true,
            message: t("Errore durante il recupero dei codici"),
            severity: "error" as AlertProps["severity"],
        },
        verifyError: {
            show: true,
            message: t("Errore durante la verifica del codice"),
            severity: "error" as AlertProps["severity"],
        },
    };
    const navigate = useNavigate();

    const [lawyers, setLawyers] = useState<
        Array<{ id: string; nomeutente: string }>
    >([]);
    const [selectedLawyer, setSelectedLawyer] = useState<string>("");
    const [id, setId] = useState("");
    const [verifiedCodes, setVerifiedCodes] = useState<string[]>([]);
    const [notification, setNotification] = useState<{
        show: boolean;
        message: string;
        severity: AlertProps["severity"];
    }>({ show: false, message: "", severity: "info" });
    const [loading, setLoading] = useState({ verify: false, recover: false });

    const getLawyerDataRequest = useGetCustom("recoveryid");

    useEffect(() => {
        getLawyerDataRequest.doFetch(true);
    }, []);

    useEffect(() => {
        if (
            getLawyerDataRequest.hasLoaded &&
            getLawyerDataRequest.data?.lawyers
        ) {
            setLawyers(getLawyerDataRequest.data.lawyers);
            setSelectedLawyer(getLawyerDataRequest.data.lawyers[0]?.id || "");
        }
    }, [getLawyerDataRequest.hasLoaded]);

    const verifyIdRequest = usePostCustom(
        "recoveryid/verify-id?noTemplateVars=true"
    );
    const recoverIdsRequest = usePostCustom(
        "recoveryid/recovery?noTemplateVars=true"
    );

    const handleVerifyId = async () => {
        if (!/^\d+$/.test(id)) {
            setNotification(notificationStatus.numberError);
            return;
        }

        setLoading(prev => ({ ...prev, verify: true }));

        try {
            const response = await verifyIdRequest.doFetch(true, { id });

            if ((response as any).data?.id === false) {
                if (verifiedCodes.includes(id)) {
                    setNotification(notificationStatus.verifiedCodeWarning);
                } else {
                    setVerifiedCodes(prev => [...prev, id]);
                    setId("");
                    setNotification(notificationStatus.recoverySuccess);
                }
            } else {
                setNotification(notificationStatus.existingError);
            }
        } catch (error) {
            setNotification(notificationStatus.verifyError);
        } finally {
            setLoading(prev => ({ ...prev, verify: false }));
        }
    };

    const handleRecoverIds = async () => {
        if (verifiedCodes.length === 0 || !selectedLawyer) return;

        setLoading(prev => ({ ...prev, recover: true }));
        try {
            const formData = new FormData();
            verifiedCodes.forEach(code => formData.append("id[]", code));
            formData.append("avvocato", selectedLawyer);

            await recoverIdsRequest.doFetch(true, formData);

            setNotification(notificationStatus.recoverySuccess);
            setTimeout(() => navigate("/archive/archive"), 2000);
        } catch (error) {
            setNotification(notificationStatus.recoveryError);
        } finally {
            setLoading(prev => ({ ...prev, recover: false }));
        }
    };

    const infoMessage = t(
        "Questa funzionalità permette di recuperare i codici persi con l'eliminazione delle pratiche. Dopo la verifica dei codici desiderati, cliccando sul tasto 'Recupera codici' verrà creata, per ogni codice inserito, una pratica vuota associata all'avvocato selezionato."
    );

    const handleSetId = (e: any) => {
        setId(e.target.value);
    };

    const handleSelectLawyer = (e: any) => {
        setSelectedLawyer(e.target.value);
    };

    const handleNavigate = () => {
        navigate("/index/utilitiesmainpage");
    };

    const handleNotification = () => {
        setNotification(prev => ({ ...prev, show: false }));
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Recupero codici pratiche")}
                showBackButton={false}
            />

            <VaporPage.Section>
                <NotificationInline
                    severity="info"
                    variant="outlined">
                    {infoMessage}
                </NotificationInline>
            </VaporPage.Section>

            <VaporPage.Section>
                <Stack
                    gap={4}
                    direction="row"
                    alignItems="end"
                    flexWrap="wrap">
                    <Stack
                        direction="row"
                        alignItems="end"
                        gap={1}>
                        <TextField
                            disabled={loading.verify || loading.recover}
                            value={id}
                            onChange={handleSetId}
                            label={t("Codice pratica")}
                            sx={{ width: 200 }}
                        />
                        <SpinnerButton
                            startIcon={<FontAwesomeIcon icon={faCheckSquare} />}
                            isLoading={loading.verify}
                            label={t("Verifica")}
                            variant="outlined"
                            disabled={!id || loading.verify || loading.recover}
                            onClick={handleVerifyId}
                            style={{ minWidth: 120 }}
                        />
                    </Stack>

                    <TextField
                        disabled
                        value={verifiedCodes.join(", ")}
                        label={t("Codici verificati")}
                        sx={{ width: 300 }}
                        InputProps={{ readOnly: true }}
                    />

                    <FormControl sx={{ width: 200 }}>
                        <InputLabel>{t("Avvocato")}</InputLabel>
                        <Select
                            value={selectedLawyer}
                            onChange={handleSelectLawyer}
                            disabled={loading.recover}>
                            {lawyers.map(lawyer => (
                                <MenuItem
                                    key={lawyer.id}
                                    value={lawyer.id}>
                                    {lawyer.nomeutente}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <Stack
                        direction="row"
                        gap={1}>
                        <Button
                            variant="outlined"
                            onClick={handleNavigate}
                            disabled={loading.recover}>
                            {t("Annulla")}
                        </Button>
                        <Button
                            variant="contained"
                            onClick={handleRecoverIds}
                            disabled={
                                verifiedCodes.length === 0 ||
                                !selectedLawyer ||
                                loading.recover
                            }>
                            {t("Recupera codici")}
                        </Button>
                    </Stack>
                </Stack>
            </VaporPage.Section>

            <ToastNotification
                showNotification={notification.show}
                severity={notification.severity}
                text={notification.message}
                setShowNotification={handleNotification}
            />
        </VaporPage>
    );
};
