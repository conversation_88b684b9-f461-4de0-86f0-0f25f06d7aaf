import { useTranslation } from "@1f/react-sdk";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { Tab, Tabs } from "@vapor/react-extended";
import { Box } from "@vapor/react-material";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import PageTitle from "../../../custom-components/PageTitle";
import { DocumentDetails } from "./DocumentDetails";
import { useDeleteBook } from "./hooks/useDeleteBook";
import { useGetBookDetails } from "./hooks/useGetBookData";
import { useLoansFilter } from "./hooks/useLoansFilter";
import { usePrintAllLoans } from "./hooks/usePrintAllLoans";
import Spinner from "../../../custom-components/Spinner";

export const BookDetails = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const [searchParams] = useSearchParams();
    const bookId = searchParams.get("uniqueId");

    const [selectedTab, setSelectedTab] = useState("Biblioteca");
    const [printAllLoans, setPrintAllLoans] = useState(false);
    const [deleteBook, setDeleteBook] = useState(false);
    const [confirmDelete, setConfirmDelete] = useState(false);
    const [isBorrowed, setIsBorrowed] = useState(false);
    const [newLoanConfirm, setNewLoanConfirm] = useState(false);

    const { defaultQuery, list, loading, query, setQuery } = useLoansFilter();

    const booksDetailsResponse = useGetBookDetails({ uniqueId: bookId });
    const printLoansResponse = usePrintAllLoans({ print: printAllLoans });
    const deleteBookRespose = useDeleteBook({
        uniqueid: bookId,
        remove: deleteBook,
    });

    useEffect(() => {
        if (booksDetailsResponse.hasLoaded) {
            setIsBorrowed(booksDetailsResponse.data.borrowed === 1);
        }
    }, [booksDetailsResponse.hasLoaded]);

    useEffect(() => {
        if (deleteBookRespose.hasLoaded) {
            navigate("/library/library");
        }
    }, [deleteBookRespose.hasLoaded]);

    useEffect(() => {
        if (printLoansResponse.hasLoaded) {
            setPrintAllLoans(false);
        }
    }, [printLoansResponse.hasLoaded]);

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...defaultQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };
    const handleClickCallback = (uniqueid: any) => {
        navigate(`/library/library/loan/?loanId=${uniqueid}&bookId=${bookId}`);
    };

    const ELIMINA_BUTTON = {
        label: t("Elimina"),
        onclick: () => {
            setConfirmDelete(true);
        },
        color: "error",
    };
    const ANNULLA_BUTTON = {
        label: t("Annulla"),
        onclick: () => {
            navigate("/library/library");
        },
    };
    const STAMPA_BUTTON = {
        label: t("Stampa Modulo"),
        onclick: () => setPrintAllLoans(true),
    };
    const MODIFICA_BUTTON = {
        label: t("Modifica"),
        onclick: () => {
            navigate(`/library/library/update?uniqueId=${bookId}`);
        },
    };
    const NUOVO_PRESTITO_BUTTON = {
        label: t("Nuovo prestito"),
        onclick: () => {
            if (isBorrowed) {
                setNewLoanConfirm(true);
            } else {
                navigate(`/library/library/loan?bookId=${bookId}`);
            }
        },
    };

    return (
        <VaporPage>
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                open={confirmDelete}
                title={t("Elimina?")}
                confirmText={t("Eliminare definitivamente l'oggetto?")}
                handleAgree={() => {
                    setDeleteBook(true);
                }}
                handleDecline={() => setConfirmDelete(false)}
                colorConfirmButton="error"
            />
            <ConfirmModal
                agree={t("Ok")}
                decline={t("Annulla")}
                handleAgree={() => setNewLoanConfirm(false)}
                handleDecline={() => setNewLoanConfirm(false)}
                open={newLoanConfirm}
                title=""
                confirmText={t(
                    "Attenzione! l'oggetto risulta attualmente in prestito. Per aggiungere un nuovo prestito e necesario chiudere il prestito attuale"
                )}
            />
            <PageTitle
                title="Dettagli Libro"
                pathToPrevPage="/library/library"
                actionButtons={
                    selectedTab === "Biblioteca"
                        ? [ANNULLA_BUTTON, ELIMINA_BUTTON, MODIFICA_BUTTON]
                        : [
                              ANNULLA_BUTTON,
                              STAMPA_BUTTON,
                              ELIMINA_BUTTON,
                              NUOVO_PRESTITO_BUTTON,
                              MODIFICA_BUTTON,
                          ]
                }
            />
            <VaporPage.Section>
                <Tabs
                    value={selectedTab}
                    onChange={(_e: any, value: any) => setSelectedTab(value)}
                    size="extraSmall"
                    variant="standard">
                    <Tab
                        label={t("Biblioteca")}
                        value="Biblioteca"
                    />
                    <Tab
                        label={t("Storico Prestiti")}
                        value="Storico Prestiti"
                    />
                </Tabs>
                {!loading ? (
                    <Box p={1}>
                        {selectedTab === "Biblioteca" &&
                            booksDetailsResponse.hasLoaded && (
                                <DocumentDetails
                                    data={booksDetailsResponse.data}
                                />
                            )}
                        {selectedTab === "Storico Prestiti" &&
                            !list.loading && (
                                <CustomDataGrid
                                    name="bookDetails"
                                    columns={list.columns}
                                    data={list.rows}
                                    page={list.page || 0}
                                    totalRows={list.totalRows}
                                    pageSize={list.pageSize}
                                    loading={loading}
                                    query={query}
                                    onPageChangeCallback={onPageChangeCallback}
                                    onClickKey="uniqueid"
                                    disableColumnResize={true}
                                    disableColumnReorder={true}
                                    onClickCallback={handleClickCallback}
                                />
                            )}
                    </Box>
                ) : (
                    <Spinner />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
