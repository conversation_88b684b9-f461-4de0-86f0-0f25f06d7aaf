import { useTranslation } from "@1f/react-sdk";
import type { SelectChangeEvent } from "@mui/material/Select";
import { VaporPage } from "@vapor/react-custom";
import { Tab, Tabs } from "@vapor/react-extended";
import { Box } from "@vapor/react-material";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import PageTitle from "../../../custom-components/PageTitle";
import Spinner from "../../../custom-components/Spinner";
import { BookData } from "../../../interfaces/library.interfaces";
import { BookUpdateDetails } from "./BookUpdateDetails";
import { BookUpdateExtraData } from "./BookUpdateExtraData";
import { BOOK_DATA } from "./constants";
import { useDeleteBook } from "./hooks/useDeleteBook";
import { useGetBookDetails } from "./hooks/useGetBookData";
import { useSaveBookData } from "./hooks/useSaveBookData";

export const BookCreateUpdate = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const [searchParams] = useSearchParams();
    const uniquiId = searchParams.get("uniqueId");

    const [selectedTab, setSelectedTab] = useState("Scheda Dettaglio");
    const [save, setSave] = useState(false);
    const [deleteBook, setDeleteBook] = useState(false);
    const [confirmDelete, setConfirmDelete] = useState(false);

    const [bookData, setBookData] = useState<BookData>(BOOK_DATA);

    const [errors, setErrors] = useState<Partial<BookData>>({});

    const validateData = () => {
        const updatedErrors: Partial<BookData> = {};

        if (bookData.titolo === "") {
            updatedErrors.titolo = t("Titolo obbligatorio");
        }
        if (bookData.autore === "") {
            updatedErrors.autore = t("Autore obbligatorio");
        }
        if (bookData.ubicazione === "") {
            updatedErrors.ubicazione = t("Ubicazione obbligatorio");
        }
        setErrors(updatedErrors);
        return Object.keys(updatedErrors).length === 0;
    };

    const booksDetailsResponse = useGetBookDetails({ uniqueId: uniquiId });

    const saveDetailsResponse = useSaveBookData({ data: bookData, save: save });

    const deleteBookRespose = useDeleteBook({
        uniqueid: uniquiId,
        remove: deleteBook,
    });

    useEffect(() => {
        if (deleteBookRespose.hasLoaded || saveDetailsResponse.hasLoaded) {
            navigate("/library/library");
        }
    }, [deleteBookRespose.hasLoaded, saveDetailsResponse.hasLoaded]);

    useEffect(() => {
        if (booksDetailsResponse.hasLoaded) {
            const form = booksDetailsResponse.data.form;
            setBookData({
                uniqueid: uniquiId,
                autore: form.autore || "",
                b_codice: form.b_codice || "",
                codice: form.codice || "",
                collana: form.collana || "",
                data_pubblicazione: form.data_pubblicazione || "",
                descrizione: form.descrizione || "",
                editore: form.editore || "",
                id_sede: form.id_sede || "",
                posizione: form.posizione || "",
                tipo: form.tipo || "",
                titolo: form.titolo || "",
                ubicazione: form.ubicazione || "",
                valore: form.valore || "",
            });
        }
    }, [booksDetailsResponse.hasLoaded]);

    const handleInputChange =
        (field: keyof BookData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            setBookData({ ...bookData, [field]: e.target.value });

            if (errors[field]) {
                setErrors((prevErrors) => ({
                    ...prevErrors,
                    [field]: undefined,
                }));
            }
        };

    const handleSelectChange =
        (field: keyof BookData) => (e: SelectChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            setBookData((prevData) => ({
                ...prevData,
                [field]: value,
            }));
        };

    const handleDateChange = (field: keyof BookData) => (date: Date | null) => {
        setBookData({
            ...bookData,
            [field]: date ? format(date, "dd/MM/yyyy") : date,
        });
    };

    const ANNULLA_BUTTON = {
        label: t("Annulla"),
        onclick: () => {
            navigate("/library/library");
        },
    };
    const ELIMINA_BUTTON = {
        label: t("Elimina"),
        onclick: () => {
            setConfirmDelete(true);
        },
        color: "error",
    };
    const CONFERMA_BUTTON = {
        label: t("Conferma"),
        onclick: () => {
            if (validateData()) {
                setSave(true);
            }
        },
    };

    return (
        <VaporPage>
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                open={confirmDelete}
                title={t("Elimina?")}
                confirmText={t("Eliminare definitivamente l'oggetto?")}
                handleAgree={() => {
                    setDeleteBook(true);
                }}
                handleDecline={() => setConfirmDelete(false)}
                colorConfirmButton="error"
            />
            <PageTitle
                pathToPrevPage="/library/library"
                title=""
                actionButtons={
                    uniquiId
                        ? [ANNULLA_BUTTON, ELIMINA_BUTTON, CONFERMA_BUTTON]
                        : [ANNULLA_BUTTON, CONFERMA_BUTTON]
                }
            />
            <VaporPage.Section>
                <Tabs
                    value={selectedTab}
                    onChange={(_e: any, value: any) => setSelectedTab(value)}
                    size="extraSmall"
                    variant="standard">
                    <Tab
                        label={t("Scheda Dettaglio")}
                        value="Scheda Dettaglio"
                    />
                    <Tab
                        label={t("Dati aggiuntivi")}
                        value="Dati aggiuntivi"
                    />
                </Tabs>
                {!booksDetailsResponse.loading ? (
                    <Box
                        maxWidth={300}
                        p={1}>
                        {selectedTab === "Scheda Dettaglio" && (
                            <BookUpdateDetails
                                errors={errors}
                                handleSelectChange={handleSelectChange}
                                bookData={bookData}
                                handleInputChange={handleInputChange}
                            />
                        )}
                        {selectedTab === "Dati aggiuntivi" && (
                            <BookUpdateExtraData
                                bookData={bookData}
                                handleDateChange={handleDateChange}
                                handleInputChange={handleInputChange}
                            />
                        )}
                    </Box>
                ) : (
                    <Spinner />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
