import { Biblioteca } from "./Biblioteca";
import { BookCreateUpdate } from "./BookCreateUpdate";
import { BookDetails } from "./BookDetails";
import { BooksUpload } from "./BooksUpload";
import { LoanCreateUpdate } from "./LoanCreateUpdate";

export const biblioteca = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/library/library",
            element: <Biblioteca />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/library/library/details/:uiqueId?",
            element: <BookDetails />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/library/library/update/:uniqueId?",
            element: <BookCreateUpdate />,
        },
    },

    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/library/library/loan/:uniqueId?",
            element: <LoanCreateUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/library/library/upload",
            element: <BooksUpload />,
        },
    },
];
