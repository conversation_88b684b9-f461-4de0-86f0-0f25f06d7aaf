import { useTranslation } from "@1f/react-sdk";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    FormControl,
    FormHelperText,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    Tooltip,
} from "@vapor/react-material";
import { BookUpdateDetailsProps } from "../../../interfaces/library.interfaces";
import { TYPE } from "./constants";
import { useGetLibraryData } from "./hooks/useGetLibraryData";
import { useEffect, useState } from "react";

export const BookUpdateDetails = ({
    errors,
    handleInputChange,
    bookData,
    handleSelectChange,
}: BookUpdateDetailsProps) => {
    const { t } = useTranslation();

    const libraryDataResponse = useGetLibraryData();

    const [locations, setLocations] = useState<Data[]>([]);

    interface Data {
        id: string;
        nome: string;
    }

    useEffect(() => {
        if (libraryDataResponse.hasLoaded) {
            setLocations(libraryDataResponse.data.locations);
        }
    }, [libraryDataResponse.hasLoaded]);

    return (
        <Stack gap={4}>
            <TextField
                value={bookData.titolo}
                label={t("Titolo*")}
                error={!!errors.titolo}
                helperText={errors.titolo}
                onChange={handleInputChange("titolo")}
            />
            <TextField
                value={bookData.autore}
                label={t("Autore*")}
                error={!!errors.autore}
                helperText={errors.autore}
                onChange={handleInputChange("autore")}
            />
            <TextField
                value={bookData.codice}
                label={t("Codice")}
                onChange={handleInputChange("codice")}
            />
            <FormControl>
                <InputLabel>{t("Tipo*")}</InputLabel>
                <Select
                    value={bookData.tipo}
                    onChange={handleSelectChange("tipo")}>
                    {TYPE.map(option => (
                        <MenuItem value={option.value}>
                            {t(option.label)}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl>
                <Stack
                    direction="row"
                    gap={2}
                    justifyContent="space-between"
                    marginRight="50%">
                    <InputLabel>{t("Sede*")}</InputLabel>
                    <Tooltip
                        arrow
                        placement="top"
                        title={t(
                            "Il campo Sede indica il luogo dove è conservato l'oggetto (Es. 'Roma', 'Via Rossi 10', 'Studio Bianchi', etc.)"
                        )}>
                        <FontAwesomeIcon
                            icon={faInfoCircle}
                            color="RGB(0, 139, 204)"
                        />
                    </Tooltip>
                </Stack>
                <Select
                    value={bookData.id_sede}
                    onChange={handleSelectChange("id_sede")}>
                    {locations.map(option => (
                        <MenuItem value={option.id}>{t(option.nome)}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl>
                <Stack
                    direction="row"
                    gap={2}
                    justifyContent="space-between"
                    marginRight="50%">
                    <InputLabel>{t("Ubicazione*")}</InputLabel>
                    <Tooltip
                        arrow
                        placement="top"
                        title={t(
                            "Il campo Ubicazione indica con maggior precisione dove è conservato l'oggetto (Es. '3° piano', 'Archivio 2', etc.)"
                        )}>
                        <FontAwesomeIcon
                            icon={faInfoCircle}
                            color="RGB(0, 139, 204)"
                        />
                    </Tooltip>
                </Stack>
                <TextField
                    value={bookData.ubicazione}
                    error={!!errors.ubicazione}
                    helperText={errors.ubicazione}
                    onChange={handleInputChange("ubicazione")}
                />
            </FormControl>
            <FormControl>
                <Stack
                    direction="row"
                    justifyContent="space-between"
                    marginRight="50%">
                    <InputLabel>{t("Posizione")}</InputLabel>
                    <Tooltip
                        arrow
                        placement="top"
                        title={t(
                            "Il campo Posizione aggiunge ulteriori informazioni su dove è conservato l'oggetto (Es. '1° scaffale', '2° cassetto', etc.)"
                        )}>
                        <FontAwesomeIcon
                            icon={faInfoCircle}
                            color="RGB(0, 139, 204)"
                        />
                    </Tooltip>
                </Stack>
                <TextField
                    value={bookData.posizione}
                    onChange={handleInputChange("posizione")}
                />
            </FormControl>
            <FormControl>
                <InputLabel>{t("Descrizione")}</InputLabel>
                <TextField
                    value={bookData.descrizione}
                    error={
                        bookData.descrizione
                            ? bookData.descrizione.length > 100
                            : false
                    }
                    onChange={handleInputChange("descrizione")}
                    minRows={3}
                    maxRows={5}
                    multiline
                />
                <FormHelperText>{t("Max 100 caratteri")}</FormHelperText>
            </FormControl>
        </Stack>
    );
};
