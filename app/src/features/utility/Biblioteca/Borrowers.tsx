import { useTranslation } from "@1f/react-sdk";
import { FormControl, InputLabel, ListItem, TextField } from "@vapor/react-material";
import { useEffect, useState } from "react";
import { BorrowersProps, User } from "../../../interfaces/library.interfaces";
import { useAddnewUser } from "./hooks/useAddNewUser";
import { useGetLibraryData } from "./hooks/useGetLibraryData";
import { createFilterOptions } from "@mui/material/Autocomplete";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

const filter = createFilterOptions({
    ignoreCase: true,
    trim: true,
    ignoreAccents: true
});

export const Borrowers = ({ selectedUser, setSelectedUser }: BorrowersProps) => {
    const { t } = useTranslation();

    const [users, setUsers] = useState<User[]>([]);
    const [inputValue, setInputValue] = useState("");
    const [addUser, setAddUser] = useState(false);

    const addUserRespose = useAddnewUser({
        addUser: addUser,
        insertItem: selectedUser?.nome
    });

    useEffect(() => {
        if (addUserRespose.data) {
            setSelectedUser({
                id: addUserRespose.data.id,
                nome: addUserRespose.data.description,
                type: "2"
            });
        }
    }, [addUserRespose.hasLoaded]);

    const getUsersResponse = useGetLibraryData();

    useEffect(() => {
        if (getUsersResponse.hasLoaded) {
            setUsers(Object.values(getUsersResponse.data.users));
        }
    }, [getUsersResponse.hasLoaded]);

    const handleChange = (_event: React.SyntheticEvent, newValue: User | null) => {
        setSelectedUser(newValue);
        if (newValue && newValue.nome.includes("Aggiungi")) {
            const value = newValue.nome.match(/(?<=")([^"]+)(?=")/)?.[0];
            if (value) {
                const newUser = { nome: value, id: "", type: "" };
                setUsers([...users, newUser]);
                setSelectedUser(newUser);
                setInputValue(value);
                setAddUser(true);
            }
        }
    };

    const handleInputChange = (_event: React.SyntheticEvent, newInputValue: string) => {
        setInputValue(newInputValue);
    };

    const filterOptions = (
        options: User[],
        state: {
            inputValue: string;
            getOptionLabel: (option: unknown) => string;
        }
    ) => {
        const { inputValue } = state;
        const filtered = filter(options, state);

        const isExisting = options.some((option) => option.nome.toLowerCase() === inputValue.toLowerCase());

        if (inputValue !== "" && !isExisting) {
            filtered.push({
                nome: `${t("Aggiungi")} "${inputValue}"`,
                id: "",
                tipo: ""
            });
        }

        return filtered;
    };

    return (
        <FormControl>
            <InputLabel>{t("Prestatario")}</InputLabel>
            <CustomAutocomplete
                sx={{ width: 300 }}
                clearOnBlur={false}
                noOptionsText=""
                value={selectedUser}
                inputValue={inputValue}
                onChange={handleChange}
                onInputChange={handleInputChange}
                getOptionLabel={(option: any) => option.nome}
                getOptionKey={(option: any) => option.id + option.nome}
                options={getUsersResponse.loading ? [] : users}
                loading={getUsersResponse.loading || addUserRespose.loading}
                filterOptions={filterOptions}
                renderInput={(params: any) => <TextField {...params} value={"test"} />}
                renderOption={(props: any, option: any) => (
                    <ListItem {...props} value={option.id}>
                        {option.nome}
                    </ListItem>
                )}
                isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
            />
        </FormControl>
    );
};
