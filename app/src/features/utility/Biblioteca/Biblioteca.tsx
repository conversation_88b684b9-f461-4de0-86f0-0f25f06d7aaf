import { useTranslation } from "@1f/react-sdk";
import { faPlus, faShare } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { VaporPage } from "@vapor/react-custom";
import { Tab, Tabs } from "@vapor/react-extended";
import { Button, Stack } from "@vapor/react-material";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import PageTitle from "../../../custom-components/PageTitle";
import Spinner from "../../../custom-components/Spinner";
import { LibraryQueryParams } from "../../../interfaces/library.interfaces";
import { AdvancedSearch } from "./BookListFilters/AdvancedSearch";
import { DateSearch } from "./BookListFilters/DateSearch";
import { LoanSearch } from "./BookListFilters/LoanSearch";
import { PriceSearch } from "./BookListFilters/PriceSearch";
import { Search } from "./BookListFilters/Search";
import { lIBRARY_QUERY } from "./constants";
import { useExportCsv } from "./hooks/useExportCsv";
import useFilterLibraryData from "./hooks/useLibraryData";
import { usePrintTemplate } from "./hooks/usePrintTemplate";
import { useGetLibraryData } from "./hooks/useGetLibraryData";

export const Biblioteca = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();

    const [selectedTab, setSelectedTab] = useState("Ricerca");
    const [printTemplate, setPrintTemplate] = useState(false);
    const [exportCsv, setExportCsv] = useState(false);
    const [locations, setLocations] = useState<any>([]);

    const [search, setSearch] = useState(false);
    const [libraryQuery, setLibraryQuery] =
        useState<LibraryQueryParams>(lIBRARY_QUERY);

    const { query, setQuery, list, loading, defaultQuery } =
        useFilterLibraryData();

    const libraryDataResponse = useGetLibraryData();

    useEffect(() => {
        if (libraryDataResponse.hasLoaded) {
            setLocations(libraryDataResponse.data.locations);
        }
    }, [libraryDataResponse.hasLoaded]);

    useEffect(() => {
        setQuery({ ...query, ...libraryQuery });
    }, [libraryQuery, search]);

    useEffect(() => {
        if (loading) {
            setSearch(false);
        }
    }, [loading]);

    usePrintTemplate({
        template: 1,
        print: printTemplate,
    });

    const exportCsvResponse = useExportCsv({
        query: query,
        download: exportCsv,
    });

    useEffect(() => {
        if (exportCsvResponse.hasLoaded) {
            const blob = new Blob([exportCsvResponse.data], {
                type: "text/csv",
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "Biblioteca.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            setExportCsv(false);
        }
    }, [exportCsvResponse.hasLoaded, exportCsvResponse.loading]);

    useEffect(() => {
        if (exportCsvResponse.hasLoaded) {
            setExportCsv(false);
        }
    }, [exportCsvResponse.hasLoaded]);

    const handleInputChange =
        (field: keyof LibraryQueryParams) =>
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setLibraryQuery({ ...libraryQuery, [field]: e.target.value });
        };

    const handleDateChange =
        (field: keyof LibraryQueryParams) => (date: Date | null) => {
            setLibraryQuery({
                ...libraryQuery,
                [field]: date ? format(date, "dd/MM/yyyy") : date,
            });
        };

    const handleClickCallback = (uniqueid: any) => {
        navigate(`/library/library/details/?uniqueId=${uniqueid}`);
    };

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...defaultQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const ESPORTA_BUTTON = {
        label: t("Esporta in csv"),
        onclick: () => {
            setExportCsv(true);
        },
    };
    const IMPORTA_BUTTON = {
        label: t("Importa"),
        onclick: () => {
            navigate("/library/library/upload");
        },
        startIcon: <FontAwesomeIcon icon={faShare} />,
    };

    const AGGIUNGI_BUTTON = {
        label: t("Aggiungi in Biblioteca"),
        onclick: () => navigate("/library/library/update"),
        startIcon: <FontAwesomeIcon icon={faPlus} />,
    };

    const STAMPA_BUTTON = {
        label: t("Stampa modulo"),
        onclick: () => setPrintTemplate(true),
    };

    return (
        <VaporPage>
            <PageTitle
                showBackButton={false}
                title={t("Biblioteca")}
                actionButtons={[
                    ESPORTA_BUTTON,
                    STAMPA_BUTTON,
                    IMPORTA_BUTTON,
                    AGGIUNGI_BUTTON,
                ]}
            />
            <VaporPage.Section>
                <Tabs
                    value={selectedTab}
                    onChange={(_e: any, value: any) => setSelectedTab(value)}
                    size="extraSmall"
                    variant="standard">
                    <Tab
                        label={t("Ricerca")}
                        value="Ricerca"
                    />
                    <Tab
                        label={t("Ricerca avanzata")}
                        value="Ricerca avanzata"
                    />
                    <Tab
                        label={t("Ricerca per Data Pubblicazione")}
                        value="Ricerca per Data Pubblicazione"
                    />
                    <Tab
                        label={t("Ricerca per Prestito")}
                        value="Ricerca per Prestito"
                    />
                    <Tab
                        label={t("Ricerca per Valore")}
                        value="Ricerca per Valore"
                    />
                </Tabs>
                <Stack
                    direction="row"
                    alignItems="end"
                    justifyContent="space-between"
                    width="100%"
                    gap={4}
                    p={2}>
                    {selectedTab === "Ricerca" && (
                        <Search
                            handleInputChange={handleInputChange}
                            libraryQuery={libraryQuery}
                        />
                    )}
                    {selectedTab === "Ricerca avanzata" && (
                        <AdvancedSearch
                            handleInputChange={handleInputChange}
                            libraryQuery={libraryQuery}
                            locations={locations}
                        />
                    )}
                    {selectedTab === "Ricerca per Data Pubblicazione" && (
                        <DateSearch
                            libraryQuery={libraryQuery}
                            handleDateChange={handleDateChange}
                        />
                    )}
                    {selectedTab === "Ricerca per Prestito" && (
                        <LoanSearch
                            libraryQuery={libraryQuery}
                            handleDateChange={handleDateChange}
                            handleInputChange={handleInputChange}
                        />
                    )}
                    {selectedTab === "Ricerca per Valore" && (
                        <PriceSearch
                            libraryQuery={libraryQuery}
                            handleInputChange={handleInputChange}
                        />
                    )}
                    <Stack
                        direction="row"
                        gap={2}>
                        <Button
                            variant="contained"
                            onClick={() => setSearch(true)}>
                            {t("Ricerca")}
                        </Button>
                        <Button
                            variant="contained"
                            onClick={() => setLibraryQuery(lIBRARY_QUERY)}>
                            {t("Mostra tutti")}
                        </Button>
                    </Stack>
                </Stack>
            </VaporPage.Section>
            <VaporPage.Section>
                {loading ? (
                    <Spinner />
                ) : (
                    <CustomDataGrid
                        name="biblioteca"
                        columns={list.columns}
                        data={list.rows}
                        page={list.page}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                        onPageChangeCallback={onPageChangeCallback}
                        onClickCallback={handleClickCallback}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
