import {
    BookData,
    LibraryQueryParams,
    LoanForm,
} from "../../../interfaces/library.interfaces";

export const lIBRARY_QUERY: LibraryQueryParams = {
    titoloSearch: "",
    autoreSearch: "",
    editoreSearch: "",
    codiceSearch: "",
    b_codiceSearch: "",
    tipoSearch: -1,
    collanaSearch: "",
    sedeSearch: -1,
    ubicazioneSearch: "",
    posizioneSearch: "",
    startDate: "",
    endDate: "",
    prestatoSearch: "",
    startLoanDate: "",
    endLoanDate: "",
    startValue: "",
    endValue: "",
};

export const TYPE = [
    { label: "Tutti", value: "-1" },
    { label: "Articoli Rivista", value: "2" },
    { label: "Enciclopedia", value: "3" },
    { label: "Libro", value: "4" },
    { label: "Periodico Tipo", value: "5" },
    { label: "Periodico in Abbonamento", value: "6" },
    { label: "Periodico senza Abbonamento", value: "7" },
    { label: "Rassegna Stampa", value: "8" },
    { label: "Ricerca Internet", value: "9" },
    { label: "Software", value: "10" },
];

export const BOOK_DATA: BookData = {
    uniqueid: "",
    titolo: "",
    autore: "",
    b_codice: "",
    tipo: "-1",
    id_sede: "-1",
    ubicazione: "",
    posizione: "",
    descrizione: "",
    editore: "",
    data_pubblicazione: "",
    collana: "",
    codice: "",
    valore: "",
};

export const DEFAULT_LOAN_FORM: LoanForm = {
    form: {
        nome_prestatario: "",
        data_inizio: "",
        data_fine: "",
        data_restituzione: "",
        loanUid: "",
    },
};
