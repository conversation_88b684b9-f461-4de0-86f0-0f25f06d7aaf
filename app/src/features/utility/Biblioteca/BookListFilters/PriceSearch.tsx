import { useTranslation } from "@1f/react-sdk";
import { Stack, TextField } from "@vapor/react-material";
import { LibraryQueryParams } from "../../../../interfaces/library.interfaces";

export const PriceSearch = ({
    handleInputChange,
    libraryQuery,
}: {
    handleInputChange: (
        field: keyof LibraryQueryParams
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    libraryQuery: LibraryQueryParams;
}) => {
    const { t } = useTranslation();
    return (
        <Stack
            direction="row"
            gap={2}>
            <TextField
                value={libraryQuery.startValue}
                label={t("Da")}
                onChange={handleInputChange("startValue")}
                type="number"
            />
            <TextField
                value={libraryQuery.endValue}
                label={t("al")}
                onChange={handleInputChange("endValue")}
                type="number"
            />
        </Stack>
    );
};
