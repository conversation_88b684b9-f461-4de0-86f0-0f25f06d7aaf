import { useTranslation } from "@1f/react-sdk";
import { Stack, TextField } from "@vapor/react-material";
import { LibraryQueryParams } from "../../../../interfaces/library.interfaces";

export const Search = ({
    handleInputChange,
    libraryQuery,
}: {
    handleInputChange: (
        field: keyof LibraryQueryParams
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    libraryQuery: LibraryQueryParams;
}) => {
    const { t } = useTranslation();
    return (
        <Stack
            direction="row"
            gap={2}>
            <TextField
                value={libraryQuery.titoloSearch}
                label={t("Titolo")}
                onChange={handleInputChange("titoloSearch")}
            />
            <TextField
                value={libraryQuery.autoreSearch}
                label={t("Autore")}
                onChange={handleInputChange("autoreSearch")}
            />
            <TextField
                value={libraryQuery.editoreSearch}
                label={t("Editore")}
                onChange={handleInputChange("editoreSearch")}
            />
            <TextField
                value={libraryQuery.codiceSearch}
                label={t("Codice ISBN")}
                onChange={handleInputChange("codiceSearch")}
            />
            <TextField
                value={libraryQuery.b_codiceSearch}
                label={t("Codice")}
                onChange={handleInputChange("b_codiceSearch")}
            />
        </Stack>
    );
};
