import { useTranslation } from "@1f/react-sdk";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import { LibraryQueryParams } from "../../../../interfaces/library.interfaces";
import { TYPE } from "../constants";

export const AdvancedSearch = ({
    handleInputChange,
    libraryQuery,
    locations,
}: {
    handleInputChange: (
        field: keyof LibraryQueryParams
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    libraryQuery: LibraryQueryParams;
    locations: any;
}) => {
    const { t } = useTranslation();

    return (
        <Stack
            direction="row"
            gap={2}>
            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Tipo")}</InputLabel>
                <Select
                    value={libraryQuery.tipoSearch}
                    //@ts-ignore
                    onChange={handleInputChange("tipoSearch")}>
                    {TYPE.map(type => (
                        <MenuItem value={type.value}>{type.label}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <TextField
                value={libraryQuery.collanaSearch}
                label={t("Collana")}
                onChange={handleInputChange("collanaSearch")}
            />
            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Sede")}</InputLabel>
                <Select
                    value={libraryQuery.sedeSearch}
                    //@ts-ignore
                    onChange={handleInputChange("sedeSearch")}>
                    {[{ id: -1, nome: t("Tutti") }, ...locations].map(
                        (location: any) => (
                            <MenuItem value={location.id}>
                                {location.nome}
                            </MenuItem>
                        )
                    )}
                </Select>
            </FormControl>

            <TextField
                value={libraryQuery.ubicazioneSearch}
                label={t("Ubicazione")}
                onChange={handleInputChange("ubicazioneSearch")}
            />
            <TextField
                value={libraryQuery.posizioneSearch}
                label={t("Posizione")}
                onChange={handleInputChange("posizioneSearch")}
            />
        </Stack>
    );
};
