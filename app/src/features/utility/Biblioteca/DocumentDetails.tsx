import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import { Stack } from "@vapor/react-material";
import { BookDetails } from "../../../interfaces/library.interfaces";
import { joinWithDash } from "../../../utilities/utils";

export const DocumentDetails = ({ data }: { data: BookDetails }) => {
    const { t } = useTranslation();

    const DETAILS = [
        { label: "Titolo", value: data.form.titolo },
        { label: "Autore", value: data.form.autore },
        { label: "Codice", value: data.form.codice },
        { label: "Editore", value: data.form.editore },
        { label: "Descrizione", value: data.form.descrizione },
        { label: "Data Pubblicazione", value: data.form.data_pubblicazione },
        { label: "Tipo", value: data.form.tipo },
        { label: "Collana", value: data.form.collana },
        { label: "<PERSON><PERSON>", value: data.form.valore },
        {
            label: "Ubicazione",
            value: joinWithDash([
                data.sede,
                data.form.ubicazione,
                data.form.posizione,
            ]),
        },
    ];

    return (
        <Stack
            gap={1}
            p={1}>
            {DETAILS.map(detail =>
                !detail.value ? (
                    <Typography>{`${t(detail.label)} : `}</Typography>
                ) : (
                    <Typography>{`${t(detail.label)} : ${
                        detail.value
                    }`}</Typography>
                )
            )}
        </Stack>
    );
};
