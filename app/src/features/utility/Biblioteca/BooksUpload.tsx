import { useTranslation } from "@1f/react-sdk";
import { VaporPage } from "@vapor/react-custom";
import { Typography } from "@vapor/react-extended";
import { Hyperlink, Stack } from "@vapor/react-material";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import PageTitle from "../../../custom-components/PageTitle";
import { useDownloadCsvTemplate } from "./hooks/useDownloadTemplate";
import { UploadButton } from "./UploadButton";

export const BooksUpload = () => {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [download, setDownload] = useState(false);
    const [fileName, setFileName] = useState("");
    const [upload, setUpload] = useState(false);
    const [fileUploaded, setFileUploaded] = useState(false);

    const templateDonwloadResponse = useDownloadCsvTemplate({
        download: download,
    });

    useEffect(() => {
        if (templateDonwloadResponse.hasLoaded) {
            const blob = new Blob([templateDonwloadResponse.data], {
                type: "text/csv",
            });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.setAttribute("download", "tracciato.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            setDownload(false);
        }
    }, [templateDonwloadResponse.hasLoaded, templateDonwloadResponse.loading]);

    useEffect(() => {
        if (templateDonwloadResponse.hasLoaded) {
            setDownload(false);
        }
    }, [templateDonwloadResponse.hasLoaded]);

    useEffect(() => {
        if (fileUploaded) {
            navigate("/library/library");
        }
    }, [fileUploaded]);

    const ANNULLA_BUTTON = {
        label: t("Annulla"),
        onclick: () => {
            navigate("/library/library");
        },
    };

    const CONFERMA_BUTTON = {
        label: t("Conferma"),
        onclick: () => {
            setUpload(true);
        },
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Importa")}
                pathToPrevPage="/library/library"
                actionButtons={[ANNULLA_BUTTON, CONFERMA_BUTTON]}
            />
            <VaporPage.Section>
                <Stack
                    maxWidth={300}
                    gap={2}>
                    <Stack
                        direction="row"
                        gap={2}>
                        <Typography>{t("Scarica tracciato : ")}</Typography>
                        <Hyperlink onClick={() => setDownload(true)}>
                            .csv
                        </Hyperlink>
                    </Stack>
                    <Stack>
                        <Stack
                            direction="row"
                            gap={2}
                            alignItems="center">
                            <Typography>{t("Carica file : ")}</Typography>
                            <UploadButton
                                setFilUploaded={setFileUploaded}
                                setUpload={setUpload}
                                upload={upload}
                                title={t("Upload")}
                                setFileName={setFileName}
                            />
                        </Stack>
                        <Typography>{fileName}</Typography>
                    </Stack>
                    <Stack
                        direction="column"
                        gap={2}>
                        <Typography>
                            {t(
                                "Il file .csv importato dovrà contenere le stesse colonne nel tracciato fornito in download, inoltre se ci saranno problemi sui dati di una o più righe queste non verranno importate."
                            )}
                        </Typography>
                        <Typography>
                            {t(
                                'N.B.Se la colonna "Sede" viene compilata e non esiste già una sede con quel nome, ne verrà creata una nuova.'
                            )}
                        </Typography>
                    </Stack>
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
