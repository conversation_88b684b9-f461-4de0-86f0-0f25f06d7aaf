import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useExportCsv = ({
    query,
    download,
}: {
    query: any;
    download: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom(
        "/library/exportcsv?noTemplateVars=true"
    );

    useEffect(() => {
        if (download) {
            doFetch(true, query);
        }
    }, [query, download]);

    return { data, hasLoaded, loading };
};
