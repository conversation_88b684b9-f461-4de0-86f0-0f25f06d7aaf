import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useAddnewUser = ({
    insertItem,
    addUser,
}: {
    insertItem: string | undefined;
    addUser: boolean;
}) => {
    const { data, doFetch, loading, hasLoaded } = usePostCustom(
        "/library/saverubricaitem?noTemplateVars=true"
    );

    useEffect(() => {
        if (addUser) {
            doFetch(true, { insertItem: insertItem });
        }
    }, [addUser]);

    return { data, loading, hasLoaded };
};
