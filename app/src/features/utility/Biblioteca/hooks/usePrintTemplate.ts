import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const usePrintTemplate = ({
    template,
    print,
}: {
    template: number;
    print: boolean;
}) => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom("loan/print");

    useEffect(() => {
        if (print) {
            doFetch(true, { template: template });
        }
    }, [print, template]);

    useEffect(() => {
        if (hasLoaded && data) {
            const blob = new Blob([data], { type: "application/pdf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `modulo_prestito.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }, [data, hasLoaded, template]);

    return { loading, hasLoaded };
};
