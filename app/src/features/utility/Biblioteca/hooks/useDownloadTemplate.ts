import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useDownloadCsvTemplate = ({ download }: { download: boolean }) => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom(
        "/library/download?noTemplateVars=true"
    );

    useEffect(() => {
        if (download) {
            doFetch(true);
        }
    }, [download]);

    return { data, hasLoaded, loading };
};
