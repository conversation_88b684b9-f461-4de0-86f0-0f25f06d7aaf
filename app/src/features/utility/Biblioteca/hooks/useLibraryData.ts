import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback, useRef } from "react";
import { IList } from "../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { getLibraryGrid } from "../../../../utilities/library/gridColumn";
import { debounce, isEqual } from "lodash";

interface ILibraryQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
}

const defaultQuery: ILibraryQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "titolo",
    sortOrder: "asc",
};

export default function useFilterLibraryData() {
    const { t } = useTranslation();
    const libraryDataResponse = useGetCustom(
        "library/list?noTemplateVars=true"
    );
    const [query, setQuery] = useState<ILibraryQuery>(defaultQuery);
    const previousQueryRef = useRef<ILibraryQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterLibraryData = useCallback(async (query?: ILibraryQuery) => {
        if (!query) return;

        if (isEqual(previousQueryRef.current, query)) {
            return;
        }

        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getLibraryGrid(t),
            libraryDataResponse.doFetch(true, query),
        ]);
        const { currentPage, totalRows } = response.data;

        previousQueryRef.current = query;

        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    }, []);

    const debouncedFilter = useRef(
        debounce((query: ILibraryQuery) => {
            filterLibraryData(query);
        }, 500)
    ).current;

    useEffect(() => {
        debouncedFilter(query);

        return () => {
            debouncedFilter.cancel();
        };
    }, [query, debouncedFilter]);

    useEffect(() => {
        return () => {
            debouncedFilter.cancel();
        };
    }, [debouncedFilter]);

    const setQueryWithCheck = useCallback((newQuery: ILibraryQuery) => {
        setQuery(newQuery);
    }, []);

    return {
        defaultQuery,
        query,
        setQuery: setQueryWithCheck,
        list,
        setList,
        filterInternalUserData: filterLibraryData,
        loading: libraryDataResponse.loading,
    };
}
