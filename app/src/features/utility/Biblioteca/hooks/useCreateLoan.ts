import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { LoanParams } from "../../../../interfaces/library.interfaces";

export const useCreateUpdateLoan = ({
    params,
    save,
}: {
    params: LoanParams;
    save: boolean;
}) => {
    const { doFetch, data, hasLoaded, loading } = usePostCustom(
        "loan/save?noTemplateVars=true"
    );

    useEffect(() => {
        const data = new FormData();
        Object.entries(params).forEach(([key, value]) =>
            data.append(key, value)
        );

        if (save) {
            doFetch(true, data);
        }
    }, [save]);

    return { data, loading, hasLoaded };
};
