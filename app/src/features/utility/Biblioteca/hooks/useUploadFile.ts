import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useUpload = ({ file, upload }: { file: any; upload: boolean }) => {
    const { data, doFetch, error, hasLoaded, loading } =
        usePostCustom("library/upload");
    const uploadData = new FormData();

    uploadData.append("file[]", file);

    useEffect(() => {
        if (upload) {
            doFetch(true, uploadData);
        }
    }, [upload, file]);

    return { data, hasLoaded, loading, error };
};
