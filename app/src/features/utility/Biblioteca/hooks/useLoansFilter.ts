import { getLoansGrid } from "../../../../utilities/library/gridColumn";
import { useSearchParams } from "react-router-dom";

import useGetCustom from "../../../../hooks/useGetCustom";
import { useCallback, useEffect, useState } from "react";
import { IList } from "../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";



export const useLoansFilter = () => {
    const { t } = useTranslation();

    const [searchParams] = useSearchParams();
    const uniquieId = searchParams.get("uniqueId");


    const defaultQuery = {
        uniqueid: uniquieId,
        page: 0,
        pageSize: 9,
        sortColumn: "id",
        sortOrder: "asc",
    };

    const loansRequest = useGetCustom("loan/list?noTemplateVars=true");

    const [query, setQuery] = useState<typeof defaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterLoansData = useCallback(async (query?: typeof defaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getLoansGrid(t),
            loansRequest.doFetch(true, query),
        ]);
        const { currentPage, totalRows } = response.data;
        setList({
            ...list,
            rows: currentPage || [],
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    }, []);

    useEffect(() => {
        filterLoansData(query);
    }, [query, filterLoansData]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterLoansData,
        loading: loansRequest.loading,
    };
};
