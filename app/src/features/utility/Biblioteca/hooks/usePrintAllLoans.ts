import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const usePrintAllLoans = ({ print }: { print: boolean }) => {
    const { data, doFetch, hasLoaded, loading } = useGetCustom(
        "loan/print?uniqueid="
    );

    useEffect(() => {
        if (print) {
            doFetch(true);
        }
    }, [print]);

    useEffect(() => {
        if (hasLoaded && data) {
            const blob = new Blob([data], { type: "application/pdf" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `modulo_prestito.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }, [data, hasLoaded]);

    return { loading, hasLoaded };
};
