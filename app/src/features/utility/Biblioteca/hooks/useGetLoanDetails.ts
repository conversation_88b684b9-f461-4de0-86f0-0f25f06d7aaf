import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { LoanForm } from "../../../../interfaces/library.interfaces";

interface GetLoanDetailsProps {
    uniqueId: string | undefined | null;
}

export const useGetLoanDetails = ({
    uniqueId,
}: GetLoanDetailsProps): {
    loading: boolean;
    hasLoaded: boolean;
    data: LoanForm;
} => {
    const { loading, hasLoaded, data, doFetch } = usePostCustom(
        "loan/getrowdata?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueId) {
            doFetch(true, { uniqueId: uniqueId });
        }
    }, [uniqueId]);

    return { loading, data, hasLoaded };
};
