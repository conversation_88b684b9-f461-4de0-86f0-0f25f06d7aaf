import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import {
    LoanData,
    LoanDetailsQuery,
} from "../../../../interfaces/library.interfaces";

export const useGetLoansData = ({
    query,
}: {
    query: LoanDetailsQuery;
}): { data: LoanData; hasLoaded: boolean; loading: boolean } => {
    const { data, hasLoaded, loading, doFetch } = useGetCustom(
        "loan/list?noTemplateVars=true"
    );

    useEffect(() => {
        doFetch(true, query);
    }, [
        query.page,
        query.pageSize,
        query.sortColumn,
        query.sortOrder,
        query.uniqueid,
    ]);

    return { data, hasLoaded, loading };
};
