import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { BookData } from "../../../../interfaces/library.interfaces";

export const useSaveBookData = ({
    data,
    save,
}: {
    data: BookData;
    save: boolean;
}) => {
    const { loading, hasLoaded, doFetch } = usePostCustom("library/save");

    useEffect(() => {
        if (save) {
            doFetch(true, data);
        }
    }, [save, data]);

    return { hasLoaded, loading };
};
