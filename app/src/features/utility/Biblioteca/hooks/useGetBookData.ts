import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { BookDetails } from "../../../../interfaces/library.interfaces";

export const useGetBookDetails = ({
    uniqueId,
}: {
    uniqueId: string | undefined | null;
}): { data: BookDetails; hasLoaded: boolean; loading: boolean } => {
    const { data, hasLoaded, loading, doFetch } = useGetCustom(
        "library/getrowdata?noTemplateVars=true"
    );

    useEffect(() => {
        if (uniqueId) {
            doFetch(true, { uniqueId: uniqueId });
        }
    }, [uniqueId]);

    return { data, hasLoaded, loading };
};
