import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDeleteBook = ({
    uniqueid,
    remove,
}: {
    uniqueid: string | undefined | null;
    remove: boolean;
}) => {
    const { doFetch, hasLoaded, loading } = usePostCustom("library/delete");

    useEffect(() => {
        if (uniqueid && remove) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid, remove]);

    return { loading, hasLoaded };
};
