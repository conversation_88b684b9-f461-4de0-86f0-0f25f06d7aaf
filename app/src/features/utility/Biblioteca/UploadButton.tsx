import { faCloudArrowUp } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    Button,
    CircularProgress,
    type ButtonProps,
} from "@vapor/react-material";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { useUpload } from "./hooks/useUploadFile";

interface UploadButtonProps extends ButtonProps {
    title: string;
    setFileName: (name: string) => void;
    upload: boolean;
    setUpload: Dispatch<SetStateAction<boolean>>;
    setFilUploaded: Dispatch<SetStateAction<boolean>>;
}

export const UploadButton = ({
    title,
    setFileName,
    setUpload,
    upload,
    setFilUploaded,
    ...buttonProps
}: UploadButtonProps) => {
    const [file, setFile] = useState<File | null>(null);

    const fileInputRef = useRef<HTMLInputElement>(null);

    const { loading, hasLoaded } = useUpload({
        file: file,
        upload: upload,
    });

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            setFile(event.target.files[0]);
            setFileName(event.target.files[0].name);
        }
    };

    const handleButtonClick = () => {
        fileInputRef.current?.click();
    };

    useEffect(() => {
        if (hasLoaded) {
            setFile(null);
            setUpload(false);
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
            setFilUploaded(true);
        }
    }, [hasLoaded]);

    return (
        <>
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                style={{ display: "none" }}
            />
            {loading ? (
                <CircularProgress />
            ) : (
                <Button
                    variant="outlined"
                    startIcon={<FontAwesomeIcon icon={faCloudArrowUp} />}
                    onClick={handleButtonClick}
                    disabled={loading}
                    {...buttonProps}>
                    {title}
                </Button>
            )}
        </>
    );
};
