import { VaporPage, Title } from "@vapor/react-custom";
import { Tabs, Tab } from "@vapor/react-extended";
import { Tab<PERSON>ontext, TabPanel } from "@vapor/react-lab";
import { useEffect, useState } from "react";
import {
    Box,
    Button,
    Stack,
    TextField,
    InputLabel,
    Chip,
    Select,
    Typography,
    Checkbox,
    FormControlLabel,
    FormGroup,
    FormControl,
    MenuItem,
    OutlinedInput,
    Breadcrumbs,
} from "@vapor/react-material";
import AngleRight from "@vapor/react-icons/AngleRight";
import useGetCustom from "../../../hooks/useGetCustom";
import { useNavigate, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Spinner from "../../../custom-components/Spinner";
import { IDataType, IAmounts } from "./interfaces/infocamere.interface";
import { currencyFormat } from "../../../helpers/currencyFormat";
import PriceChoice from "./helpers/priceChoiceSection";

const dataType: Array<IDataType> = [
    { value: "denominazione", name: "Nome impresa" },
    { value: "nrea", name: "Numero REA" },
    { value: "partitaiva", name: "Partita iva" },
    { value: "codicefiscale", name: "Cod. Fiscale" },
];

export default function SearchCompany() {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [tabs, setTabs] = useState<string>("1");
    const [amounts, setAmounts] = useState<IAmounts>({
        studioAmount: 0,
        userAmount: 0,
        price: 0,
        total: 0,
    });
    const [provinces, setProvinces] = useState<any[]>([]);
    const [legalForm, setLegalForm] = useState<{
        people: any[];
        capital: any[];
        other: any[];
    }>({
        people: [],
        capital: [],
        other: [],
    });
    const [dataTypeSelected, setDataTypeSelected] = useState<string>(
        dataType[0].value
    );
    const [inputLabelName, setInputLabelName] =
        useState<string>("Nome impresa");
    const [selectedCredit, setSelectedCredit] =
        useState<string>("studioCredit");
    const [insufficientCredit, setInsufficientCredit] =
        useState<boolean>(false);
    const [serviceData, setServiceData] = useState<any>({
        ambito: "imprese",
        ricerca: "imprese",
        dataType: "",
        data: "",
        cciaa: [""],
        statoImpresa: [""],
        noRd: "",
        creditChoice: selectedCredit,
    });
    const [checkboxes, setCheckboxes] = useState<any>({
        R: false,
        C: false,
        P: false,
    });
    const [selectProvinces, setSelectProvinces] = useState<string[]>([]);
    const [selectPersons, setSelectPersons] = useState<string[]>([]);
    const [selectCapitals, setSelectCapitals] = useState<string[]>([]);
    const [selectOtherFroms, setSelectOtherFroms] = useState<string[]>([]);

    const searchCompanyRequest = useGetCustom(
        "infocamere/search?search=imprese"
    );

    useEffect(() => {
        async function fetchData() {
            const response: any = await searchCompanyRequest.doFetch();
            const {
                formaGiuridicaJSON,
                statesCitiesJSON,
                studioCredit,
                userCredit,
                prezzo,
            } = response.data;
            const formaGiuridicaData = JSON.parse(formaGiuridicaJSON);
            setProvinces(JSON.parse(statesCitiesJSON));
            const total =
                parseFloat(studioCredit.studioAmount) - parseFloat(prezzo);
            setInsufficientCredit(total <= 0 ? true : false);
            setAmounts({
                studioAmount: parseFloat(studioCredit.studioAmount),
                userAmount: parseFloat(userCredit.userAmount),
                price: parseFloat(prezzo),
                total,
            });
            const legalFormOptions = Object.values(formaGiuridicaData).map(
                (categoryData: any) =>
                    categoryData.map((option: any) => ({
                        value: option.valore,
                        label: option.nome,
                    }))
            );
            setLegalForm({
                people: legalFormOptions[0],
                capital: legalFormOptions[1],
                other: legalFormOptions[2],
            });
        }
        fetchData();
    }, []);

    function switchingTabs(_event: React.SyntheticEvent, newTab: string) {
        setTabs(newTab);
    }

    function handleSearchBySelect(e: any) {
        const value = e.target.value;
        const findInputName: any = dataType.find(
            (data: any) => data.value === value
        );
        setDataTypeSelected(value);
        setInputLabelName(findInputName.name);
        setServiceData({ ...serviceData, dataType: value });
    }

    function onChangeMultipleSelection(event: any) {
        const { value } = event.target;
        setSelectProvinces(value);

        const cciaaValues = [];

        for (let i in value) {
            const provinceValue = provinces.find((item: any) => {
                return item.provincia === value[i];
            });
            if (provinceValue) {
                cciaaValues.push(provinceValue.iniziali);
            }
        }
        setServiceData({ ...serviceData, cciaa: cciaaValues });
    }

    // Function to update natGiu based on all selected values
    function updateNatGiuValues(
        persons: string[],
        capitals: string[],
        otherForms: string[]
    ) {
        const natGiuValues = [
            ...persons.map((label: any) => {
                const personValue = legalForm.people.find(
                    (item: any) => item.label === label
                );
                return personValue ? personValue.value : null;
            }),
            ...capitals.map((label: any) => {
                const capitalValue = legalForm.capital.find(
                    (item: any) => item.label === label
                );
                return capitalValue ? capitalValue.value : null;
            }),
            ...otherForms.map((label: any) => {
                const otherValue = legalForm.other.find(
                    (item: any) => item.label === label
                );
                return otherValue ? otherValue.value : null;
            }),
        ];

        setServiceData({
            ...serviceData,
            natGiu: natGiuValues.filter(Boolean),
        });
    }

    // Event handlers for each select
    const onChangePersonsSelection = (event: any) => {
        const { value } = event.target;
        setSelectPersons(value);
        updateNatGiuValues(value, selectCapitals, selectOtherFroms);
    };

    const onChangeCapitalsSelection = (event: any) => {
        const { value } = event.target;
        setSelectCapitals(value);
        updateNatGiuValues(selectPersons, value, selectOtherFroms);
    };

    const onChangeOtherFormSelection = (event: any) => {
        const { value } = event.target;
        setSelectOtherFroms(value);
        updateNatGiuValues(selectPersons, selectCapitals, value);
    };

    function handleChange(_e: any, checkboxName: string) {
        setCheckboxes((prevCheckboxes: any) => {
            const updatedCheckboxes = { ...prevCheckboxes };

            updatedCheckboxes[checkboxName] = !prevCheckboxes[checkboxName];

            let checkedCount = Object.values(updatedCheckboxes).filter(
                (value) => value
            ).length;
            if (checkedCount > 2) {
                updatedCheckboxes[checkboxName] = false;
            }
            const selectedCheckboxes = Object.keys(updatedCheckboxes).filter(
                (key) => updatedCheckboxes[key]
            );

            setServiceData({
                ...serviceData,
                statoImpresa: selectedCheckboxes,
            });

            return updatedCheckboxes;
        });
    }

    const onChangeCompanyRegister = (event: any) => {
        const { checked } = event.target;
        setServiceData({ ...serviceData, noRd: checked ? "noRd" : "" });
    };

    function handleCreditChoice(type: any) {
        let amountsUpdate = { ...amounts };

        if (type === "studioCredit") {
            amountsUpdate.total = amounts.studioAmount - amounts.price;
            if (amountsUpdate.total <= 0) {
                setInsufficientCredit(true);
            } else {
                setInsufficientCredit(false);
            }
        } else if (type === "userCredit") {
            amountsUpdate.total = amounts.userAmount - amounts.price;
            if (amountsUpdate.total <= 0) {
                setInsufficientCredit(true);
            } else {
                setInsufficientCredit(false);
            }
        }

        setSelectedCredit(type);
        setAmounts({ ...amountsUpdate });
        setServiceData({ ...serviceData, creditChoice: type });
    }

    async function handleWebServiceCall() {
        const params = {
            ...serviceData,
            dataType: dataTypeSelected,
        };
        navigate("/infocamere/webservicecall/company", {
            state: {
                params: params,
            },
        });
    }

    return (
        <VaporPage>
            <Title
                title="Ricerca tra le imprese"
                breadcrumbs={
                    <Breadcrumbs
                        aria-label="breadcrumb"
                        separator={<AngleRight />}
                    >
                        <Link to="/infocamere">Visure e Bilanci</Link>
                        <Typography>Ricerca Imprese</Typography>
                    </Breadcrumbs>
                }
            />
            <VaporPage.Section>
                <TabContext value={tabs} sx={{ width: "100%" }}>
                    <Tabs size="extraSmall" onChange={switchingTabs}>
                        <Tab label="Dati impresa" value="1" />
                        <Tab label="Ricerce avanzata (1)" value="2" />
                    </Tabs>
                    {searchCompanyRequest.loading && <Spinner />}
                    <TabPanel value="1">
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: 500,
                                },
                            }}
                        >
                            <Stack>
                                <FormControl
                                    sx={{
                                        m: 1,
                                        minWidth: 120,
                                        maxWidth: 500,
                                    }}
                                >
                                    <InputLabel id="demo-simple-select-helper-label">
                                        Cerca per
                                    </InputLabel>
                                    <Select
                                        id="dataType"
                                        name="dataType"
                                        labelId="demo-simple-select-helper-label"
                                        onChange={handleSearchBySelect}
                                        value={dataTypeSelected}
                                    >
                                        {dataType.map(
                                            (item: any, index: number) => {
                                                return (
                                                    <MenuItem
                                                        key={index}
                                                        value={item.value}
                                                    >
                                                        {item.name}
                                                    </MenuItem>
                                                );
                                            }
                                        )}
                                    </Select>
                                </FormControl>

                                <TextField
                                    id="outlined-required"
                                    name="data"
                                    onChange={(e: any) =>
                                        setServiceData({
                                            ...serviceData,
                                            data: e.target.value,
                                        })
                                    }
                                    label={inputLabelName}
                                />

                                <FormControl
                                    sx={{
                                        m: 1,
                                        minWidth: 120,
                                        maxWidth: 500,
                                    }}
                                >
                                    <InputLabel id="demo-simple-select-helper-label">
                                        Province
                                    </InputLabel>
                                    <Select
                                        multiple
                                        id="provincia"
                                        name="provincia"
                                        labelId="demo-simple-select-helper-label"
                                        onChange={onChangeMultipleSelection}
                                        value={selectProvinces}
                                        input={
                                            <OutlinedInput
                                                id="select-multiple-chip"
                                                label="Chip"
                                            />
                                        }
                                        renderValue={(selected: any) => (
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    flexWrap: "wrap",
                                                    gap: 0.5,
                                                    color: "red",
                                                    outerHeight: "auto",
                                                }}
                                            >
                                                {Object.values(selected).map(
                                                    (value: any) => {
                                                        return (
                                                            <Chip
                                                                variant="outlined"
                                                                key={value}
                                                                label={value}
                                                            />
                                                        );
                                                    }
                                                )}
                                            </Box>
                                        )}
                                    >
                                        {provinces.map(
                                            (province: any, index: number) => {
                                                return (
                                                    <MenuItem
                                                        key={index}
                                                        value={
                                                            province.provincia
                                                        }
                                                    >
                                                        {province.provincia}
                                                    </MenuItem>
                                                );
                                            }
                                        )}
                                    </Select>
                                </FormControl>
                            </Stack>
                        </Box>
                    </TabPanel>

                    <TabPanel value="2">
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: 500,
                                },
                            }}
                        >
                            <Stack>
                                <FormControl>
                                    <InputLabel
                                        id="text-label"
                                        style={{ marginLeft: "8px" }}
                                    >
                                        Stato impresa (max 2)
                                    </InputLabel>
                                    <FormGroup style={{ marginLeft: "10px" }}>
                                        <FormControlLabel
                                            value="right"
                                            control={
                                                <Checkbox
                                                    disabled={
                                                        checkboxes.C &&
                                                        checkboxes.P
                                                    }
                                                    checked={checkboxes.R}
                                                    onChange={(e: any) =>
                                                        handleChange(e, "R")
                                                    }
                                                />
                                            }
                                            label="Solo società Registrate"
                                            labelPlacement="end"
                                        />
                                        <FormControlLabel
                                            value="right"
                                            control={
                                                <Checkbox
                                                    disabled={
                                                        checkboxes.R &&
                                                        checkboxes.P
                                                    }
                                                    checked={checkboxes.C}
                                                    onChange={(e: any) =>
                                                        handleChange(e, "C")
                                                    }
                                                />
                                            }
                                            label="Solo società cessate"
                                            labelPlacement="end"
                                        />
                                        <FormControlLabel
                                            value="right"
                                            control={
                                                <Checkbox
                                                    disabled={
                                                        checkboxes.R &&
                                                        checkboxes.C
                                                    }
                                                    checked={checkboxes.P}
                                                    onChange={(e: any) =>
                                                        handleChange(e, "P")
                                                    }
                                                />
                                            }
                                            label="Solo con protocollo di iscrizione"
                                            labelPlacement="end"
                                        />
                                    </FormGroup>
                                </FormControl>

                                <FormControl style={{ marginTop: "10px" }}>
                                    <InputLabel
                                        id="text-label"
                                        style={{ marginLeft: "8px" }}
                                    >
                                        Altro
                                    </InputLabel>
                                    <FormGroup style={{ marginLeft: "10px" }}>
                                        <FormControlLabel
                                            value="right"
                                            control={
                                                <Checkbox
                                                    onChange={
                                                        onChangeCompanyRegister
                                                    }
                                                    value={
                                                        serviceData.noRd ===
                                                        "noRd"
                                                    }
                                                />
                                            }
                                            label="Escludi registro ditte"
                                            labelPlacement="end"
                                        />
                                    </FormGroup>
                                </FormControl>
                                {dataTypeSelected === "denominazione" && (
                                    <>
                                        {" "}
                                        <FormControl
                                            sx={{
                                                m: 1,
                                                minWidth: 500,
                                                maxWidth: 500,
                                            }}
                                        >
                                            <InputLabel id="demo-simple-select-helper-label">
                                                Società di persone
                                            </InputLabel>
                                            <Select
                                                multiple
                                                id="provincia"
                                                name="person"
                                                labelId="demo-simple-select-helper-label"
                                                onChange={
                                                    onChangePersonsSelection
                                                }
                                                value={selectPersons}
                                                input={
                                                    <OutlinedInput
                                                        id="select-multiple-chip"
                                                        label="Chip"
                                                    />
                                                }
                                                renderValue={(
                                                    selected: any
                                                ) => (
                                                    <Box
                                                        sx={{
                                                            display: "flex",
                                                            flexWrap: "wrap",
                                                            gap: 0.5,
                                                            color: "red",
                                                            outerHeight: "auto",
                                                        }}
                                                    >
                                                        {Object.values(
                                                            selected
                                                        ).map((value: any) => {
                                                            return (
                                                                <Chip
                                                                    variant="outlined"
                                                                    key={value}
                                                                    label={
                                                                        value
                                                                    }
                                                                />
                                                            );
                                                        })}
                                                    </Box>
                                                )}
                                            >
                                                {legalForm.people.map(
                                                    (
                                                        province: any,
                                                        index: number
                                                    ) => {
                                                        return (
                                                            <MenuItem
                                                                key={index}
                                                                value={
                                                                    province.label
                                                                }
                                                            >
                                                                {province.label}
                                                            </MenuItem>
                                                        );
                                                    }
                                                )}
                                            </Select>
                                        </FormControl>
                                        <FormControl
                                            sx={{
                                                m: 1,
                                                minWidth: 120,
                                                maxWidth: 500,
                                            }}
                                        >
                                            <InputLabel id="demo-simple-select-helper-label">
                                                Società di capitale
                                            </InputLabel>
                                            <Select
                                                multiple
                                                id="provincia"
                                                name="capital"
                                                labelId="demo-simple-select-helper-label"
                                                onChange={
                                                    onChangeCapitalsSelection
                                                }
                                                value={selectCapitals}
                                                input={
                                                    <OutlinedInput
                                                        id="select-multiple-chip"
                                                        label="Chip"
                                                    />
                                                }
                                                renderValue={(
                                                    selected: any
                                                ) => (
                                                    <Box
                                                        sx={{
                                                            display: "flex",
                                                            flexWrap: "wrap",
                                                            gap: 0.5,
                                                            color: "red",
                                                            outerHeight: "auto",
                                                        }}
                                                    >
                                                        {Object.values(
                                                            selected
                                                        ).map((value: any) => {
                                                            return (
                                                                <Chip
                                                                    variant="outlined"
                                                                    key={value}
                                                                    label={
                                                                        value
                                                                    }
                                                                />
                                                            );
                                                        })}
                                                    </Box>
                                                )}
                                            >
                                                {legalForm.capital.map(
                                                    (
                                                        province: any,
                                                        index: number
                                                    ) => {
                                                        return (
                                                            <MenuItem
                                                                key={index}
                                                                value={
                                                                    province.label
                                                                }
                                                            >
                                                                {province.label}
                                                            </MenuItem>
                                                        );
                                                    }
                                                )}
                                            </Select>
                                        </FormControl>
                                        <FormControl
                                            sx={{
                                                m: 1,
                                                minWidth: 120,
                                                maxWidth: 500,
                                            }}
                                        >
                                            <InputLabel id="demo-simple-select-helper-label">
                                                Altre forme
                                            </InputLabel>
                                            <Select
                                                multiple
                                                id="provincia"
                                                name="other"
                                                labelId="demo-simple-select-helper-label"
                                                onChange={
                                                    onChangeOtherFormSelection
                                                }
                                                value={selectOtherFroms}
                                                input={
                                                    <OutlinedInput
                                                        id="select-multiple-chip"
                                                        label="Chip"
                                                    />
                                                }
                                                renderValue={(
                                                    selected: any
                                                ) => (
                                                    <Box
                                                        sx={{
                                                            display: "flex",
                                                            flexWrap: "wrap",
                                                            gap: 0.5,
                                                            color: "red",
                                                            outerHeight: "auto",
                                                        }}
                                                    >
                                                        {Object.values(
                                                            selected
                                                        ).map((value: any) => {
                                                            return (
                                                                <Chip
                                                                    variant="outlined"
                                                                    key={value}
                                                                    label={
                                                                        value
                                                                    }
                                                                />
                                                            );
                                                        })}
                                                    </Box>
                                                )}
                                            >
                                                {legalForm.other.map(
                                                    (
                                                        province: any,
                                                        index: number
                                                    ) => {
                                                        return (
                                                            <MenuItem
                                                                key={index}
                                                                value={
                                                                    province.label
                                                                }
                                                            >
                                                                {province.label}
                                                            </MenuItem>
                                                        );
                                                    }
                                                )}
                                            </Select>
                                        </FormControl>
                                    </>
                                )}
                            </Stack>
                        </Box>
                    </TabPanel>

                    {/* common data */}
                    <PriceChoice
                        selectedCredit={selectedCredit}
                        amounts={amounts}
                        handleCreditChoice={handleCreditChoice}
                        insufficientCredit={insufficientCredit}
                    />

                    <Stack
                        direction="row"
                        justifyContent="center"
                        marginTop="30px"
                    >
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => navigate("/infocamere")}
                            type="submit"
                        >
                            Indietro
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            sx={{ marginLeft: "20px" }}
                            onClick={handleWebServiceCall}
                        >
                            {t("Avvia la ricerca")} (€{" "}
                            {currencyFormat(amounts.price, 2, ",", ".")})
                        </Button>
                    </Stack>
                </TabContext>
            </VaporPage.Section>
        </VaporPage>
    );
}
