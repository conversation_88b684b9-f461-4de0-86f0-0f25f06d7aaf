import {
    Box,
    Radio,
    InputLabel,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
} from "@vapor/react-material";
import { currencyFormat } from "../../../../helpers/currencyFormat";
import { IPriceChoice } from "../interfaces/infocamere.interface";

export default function PriceChoice(props: IPriceChoice) {
    const { selectedCredit, amounts, handleCreditChoice, insufficientCredit } =
        props;

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
            }}
        >
            <div>
                <InputLabel id="text-label">Utilizza</InputLabel>
                <div style={{ display: "flex", alignItems: "center" }}>
                    <Radio
                        checked={selectedCredit === "studioCredit"}
                        inputProps={{
                            "aria-label": "A",
                        }}
                        name="radio-buttons"
                        onChange={() => handleCreditChoice("studioCredit")}
                    />
                    <Typography>
                        Credito dello studio (€{" "}
                        {currencyFormat(
                            amounts.studioAmount.toString(),
                            2,
                            ",",
                            "."
                        )}
                        )
                    </Typography>
                </div>
                <div style={{ display: "flex", alignItems: "center" }}>
                    <Radio
                        checked={selectedCredit === "userCredit"}
                        inputProps={{
                            "aria-label": "B",
                        }}
                        name="radio-buttons"
                        onChange={() => handleCreditChoice("userCredit")}
                    />
                    <Typography>
                        Credito personale (€{" "}
                        {currencyFormat(
                            amounts.userAmount.toString(),
                            2,
                            ",",
                            "."
                        )}
                        )
                    </Typography>
                </div>
            </div>
            {!insufficientCredit ? (
                <TableContainer sx={{ width: "500px" }}>
                    <Table aria-label="caption table">
                        <TableBody>
                            <TableRow>
                                <TableCell component="th" scope="row">
                                    Credito attuale:
                                </TableCell>
                                <TableCell align="right">
                                    €{" "}
                                    {selectedCredit === "studioCredit"
                                        ? currencyFormat(
                                              amounts.studioAmount.toString(),
                                              2,
                                              ",",
                                              "."
                                          )
                                        : currencyFormat(
                                              amounts.userAmount.toString(),
                                              2,
                                              ",",
                                              "."
                                          )}
                                </TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell component="th" scope="row">
                                    Ricerca:
                                </TableCell>
                                <TableCell align="right">
                                    €{" "}
                                    {currencyFormat(amounts.price, 2, ",", ".")}
                                </TableCell>
                            </TableRow>
                            <TableRow>
                                <TableCell component="th" scope="row">
                                    Credito rimanente:
                                </TableCell>
                                <TableCell align="right">
                                    €{" "}
                                    {currencyFormat(
                                        amounts.total.toString(),
                                        2,
                                        ",",
                                        "."
                                    )}
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </TableContainer>
            ) : (
                <div
                    style={{
                        textAlign: "center",
                        maxWidth: 500,
                        margin: "auto",
                    }}
                >
                    <Typography color="red">
                        Il credito non è sufficiente per portare a termine
                        l'acquisto. Scegliere un altro credito oppure ricaricare
                        nella sezione Gestione credito
                    </Typography>
                </div>
            )}
        </Box>
    );
}
