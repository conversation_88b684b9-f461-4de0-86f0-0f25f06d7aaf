import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  Con<PERSON>er,
  <PERSON><PERSON>,
} from "@vapor/react-material";
import { useTranslation } from "react-i18next";
import BuyProspectusCompanyModal from "./buyProspectusCompanyModal";
import { useNavigate, useLocation } from "react-router-dom";
import { Title } from "@vapor/react-custom";
import usePostCustom from "../../../../hooks/usePostCustom";
import Spinner from "../../../../custom-components/Spinner";
import {
  IShowBuyModalInterface,
  IAmounts,
  IDownloadData,
} from "../interfaces/infocamere.interface";
import { currencyFormat } from "../../../../helpers/currencyFormat";

export default function CompanyResultList() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { params }: any = location?.state;
  const downloadContentRequest = usePostCustom(
    "archiveinfocamere/webservicecall"
  );
  const webServiceCallRequest = usePostCustom("infocamere/webservicecall");
  const [imprese, setImprese] = useState<any[]>([]);
  const [amounts, setAmounts] = useState<IAmounts>({
    studioAmount: 0,
    userAmount: 0,
    price: 0,
    total: 0,
  });
  const [showBuyModal, setShowBuyModal] = useState<IShowBuyModalInterface>({
    state: false,
    data: [],
    price: "",
    docType: "",
    description: "",
    selectedCredit: "studioCredit",
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  const [insufficientCredit, setInsufficientCredit] = useState<boolean>(false);

  useEffect(() => {
    //fetch data
    async function fetchData() {
      const response: any = await webServiceCallRequest.doFetch(false, params);
      const { imprese, studioCredit, userCredit, prezzo, num } = response.data;
      if (num === undefined) return; //if no result dont set any state
      const price = prezzo === undefined ? "0.60" : prezzo;
      const total = parseFloat(studioCredit.studioAmount) - parseFloat(price);

      setInsufficientCredit(total <= 0 ? true : false);
      setAmounts({
        studioAmount: parseFloat(studioCredit.studioAmount),
        userAmount: parseFloat(userCredit.userAmount),
        price: parseFloat(price),
        total,
      });
      setImprese(imprese);
    }

    fetchData();
  }, [params]);

  const navigateToDownloads = () => {
    navigate("/infocamere/downloads");
  };

  function handleCreditChoice(type: any) {
    let amountsUpdate = { ...amounts };

    if (type === "studioCredit") {
      amountsUpdate.total = amountsUpdate.total =
        amounts.studioAmount - amounts.price;
      if (amountsUpdate.total <= 0) {
        setInsufficientCredit(true);
      } else {
        setInsufficientCredit(false);
      }
    } else if (type === "userCredit") {
      amountsUpdate.total = amountsUpdate.total =
        amounts.userAmount - amounts.price;

      if (amountsUpdate.total <= 0) {
        setInsufficientCredit(true);
      } else {
        setInsufficientCredit(false);
      }
    }

    setShowBuyModal({ ...showBuyModal, selectedCredit: type });
    setAmounts({ ...amountsUpdate });
  }

  async function handleParamsDownload() {
    const params: IDownloadData = {
      fileUniqueid: "",
      ambito: "visure",
      ricerca: "acquistaprospetto",
      docType: showBuyModal.docType,
      data: showBuyModal.data.AnagraficaImpresa.NRea,
      cciaa: showBuyModal.data.AnagraficaImpresa.Cciaa,
      codFiscPosizione: "",
      naturaGiuridica: showBuyModal.data.AnagraficaImpresa.NatGiu,
      last: "",
      dataType: "",
      description: showBuyModal.description,
      creditChoice: showBuyModal.selectedCredit,
    };
    const formData = new FormData();
    for (var key in params) {
      var value = params[key as keyof IDownloadData];
      formData.append(key, value);
    }
    const response: any = await downloadContentRequest.doFetch(true, params);
    if (response.statusText === "OK") {
      setShowSuccessMessage(true);
    } else {
      setShowSuccessMessage(false);
    }
  }

  console.log("imprese", imprese);
  return (
    <>
      <Title
        divider
        reduced
        rightItems={[
          <Button
            onClick={() => navigate("/infocamere/search_company")}
            variant="outlined"
            color="info"
          >
            {t("Indietro")}
          </Button>,
          <Button
            variant="contained"
            onClick={() => navigate("/infocamere/downloads")}
            color="info"
          >
            {t("Vai all'Area download")}
          </Button>,
        ]}
        title={`Lista imprese ${imprese.length} resultati`}
      />

      {webServiceCallRequest.loading && <Spinner />}

      {imprese?.length > 0 ? (
        imprese?.map((item: any, index: number) => (
          <Card key={index}>
            <CardContent>
              <Typography variant="h5" align="center">
                {item.AnagraficaImpresa.Denominazione}
              </Typography>
              <Container>
                <Card className="mb-3">
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={8}>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Stato Attività")}:{" "}
                          </b>{" "}
                          {item.AnagraficaImpresa.DescStatoAttivita ===
                          "Cessata" ? (
                            <Badge
                              color="error"
                              sx={{
                                backgroundColor: "rgba(255, 0, 0, 0.12)",
                                color: "red",
                              }}
                              className="badge font-14 p1"
                            >
                              {t(item.AnagraficaImpresa.DescStatoAttivita)}
                            </Badge>
                          ) : (
                            <Badge
                              color="success"
                              sx={{
                                backgroundColor: "rgba(0, 255, 0, 0.12)",
                                color: "green",
                              }}
                              className="badge font-14 p1"
                            >
                              {`${t(
                                item.AnagraficaImpresa.DescStatoAttivita
                              )} ${t(
                                item.AnagraficaImpresa.DescStatoAttivitaReg
                              )}`}
                            </Badge>
                          )}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>{t("N° REA")}: </b>{" "}
                          {item.AnagraficaImpresa.NRea}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>{t("P. Iva")}: </b>{" "}
                          {item.AnagraficaImpresa.PIva
                            ? item.AnagraficaImpresa.PIva
                            : "-"}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Cod. Fiscale")}:{" "}
                          </b>{" "}
                          {item.AnagraficaImpresa.CodFisc}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Nat. Giuridica")}:{" "}
                          </b>{" "}
                          {`${item.AnagraficaImpresa.DescNatGiu} (${item.AnagraficaImpresa.NatGiu})`}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>{t("Cciaa")}: </b>{" "}
                          {`${item.AnagraficaImpresa.DescCciaa} (${item.AnagraficaImpresa.Cciaa})`}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Indirizzo")}:{" "}
                          </b>{" "}
                          {item.AnagraficaImpresa.IndirizzoSede
                            ? `${item.AnagraficaImpresa.IndirizzoSede.CodToponSede} ${item.AnagraficaImpresa.IndirizzoSede.ViaSede}, ${item.AnagraficaImpresa.IndirizzoSede.NCivicoSede} - ${item.AnagraficaImpresa.IndirizzoSede.DescComSede} ${item.AnagraficaImpresa.IndirizzoSede.CapSede}`
                            : item.AnagraficaImpresa.IndirizzoLocalizzazione
                                .length === 0
                            ? "-"
                            : `${item.AnagraficaImpresa.IndirizzoLocalizzazione.CodToponLocalizzazione} 
         ${item.AnagraficaImpresa.IndirizzoLocalizzazione.ViaLocalizzazione}, 
         ${item.AnagraficaImpresa.IndirizzoLocalizzazione.DescComLocalizzazione} - 
         ${item.AnagraficaImpresa.IndirizzoLocalizzazione.CapLocalizzazione}`}
                        </Typography>
                        {item.AnagraficaImpresa.IndirizzoPostaCertificata && (
                          <Typography sx={{ mb: 2 }}>
                            {" "}
                            <b style={{ color: "#08c" }}>
                              {t("Posta certificata")}:{" "}
                            </b>{" "}
                            {item.AnagraficaImpresa.IndirizzoPostaCertificata}
                          </Typography>
                        )}
                      </Grid>
                      <Grid item xs={4}>
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.visura_ordinaria.price,
                              docType: "ordinaria",
                            })
                          }
                        >
                          {t(item.prezzi.visura_ordinaria.title)} (€{" "}
                          {currencyFormat(
                            item.prezzi.visura_ordinaria.price,
                            2,
                            ",",
                            "."
                          )}
                          )
                        </Button>
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.visura_storica.price,
                              docType: "storica",
                            })
                          }
                        >
                          {t(item.prezzi.visura_storica?.title)} (€{" "}
                          {currencyFormat(
                            item.prezzi.visura_storica?.price,
                            2,
                            ",",
                            "."
                          )}
                          )
                        </Button>
                        {item.prezzi.bilancio && (
                          <Button
                            variant="contained"
                            color="info"
                            style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                            onClick={() =>
                              setShowBuyModal({
                                ...showBuyModal,
                                state: true,
                                data: item,
                                price: item.prezzi.bilancio.price,
                                docType: "bilanci",
                              })
                            }
                          >
                            {t("Ultimo " + item.prezzi.bilancio.title)} (€{" "}
                            {currencyFormat(
                              item.prezzi.bilancio.price,
                              2,
                              ",",
                              "."
                            )}
                            )
                          </Button>
                        )}
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.fascicolo_attuale.price,
                              docType: "attuale",
                            })
                          }
                        >
                          {t(item.prezzi.fascicolo_attuale?.title) ||
                            t("Fascicolo attuale")}{" "}
                          (€{" "}
                          {currencyFormat(
                            item.prezzi.fascicolo_attuale?.price,
                            2,
                            ",",
                            "."
                          )}
                          )
                        </Button>
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.fascicolo_storico.price,
                              docType: "storico",
                            })
                          }
                        >
                          {t(item.prezzi.fascicolo_storico?.title) ||
                            t("Fascicolo storico")}{" "}
                          (€{" "}
                          {currencyFormat(
                            item.prezzi.fascicolo_storico?.price,
                            2,
                            ",",
                            "."
                          )}
                          )
                        </Button>
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.statuto_completo.price,
                              docType: "completo",
                            })
                          }
                        >
                          {t("Statuto")} (€{" "}
                          {currencyFormat(
                            item.prezzi.statuto_completo.price,
                            2,
                            ",",
                            "."
                          )}
                          )
                        </Button>
                        <Button
                          variant="contained"
                          color="success"
                          style={{ width: "12.5rem" }}
                        >
                          {t("Vai a Visure e blocchi")}
                        </Button>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Container>
            </CardContent>
          </Card>
        ))
      ) : (
        <Alert
          variant="filled"
          severity="info"
          style={{
            backgroundColor: "#4fc6e1",
            color: "white",
            fontWeight: "bold",
            textAlign: "center",
            marginTop: "16px",
          }}
        >
          {t("La ricerca effettuata non ha restituito alcun risultato.")}
        </Alert>
      )}

      <BuyProspectusCompanyModal
        amounts={amounts}
        insufficientCredit={insufficientCredit}
        buyModalData={showBuyModal}
        setBuyModalData={setShowBuyModal}
        showSuccessMessage={showSuccessMessage}
        handleCreditChoice={handleCreditChoice}
        handleParamsDownload={handleParamsDownload}
        navigateToDownloads={navigateToDownloads}
      />
    </>
  );
}
