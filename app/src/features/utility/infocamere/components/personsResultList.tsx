import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  Container,
} from "@vapor/react-material";
import { useTranslation } from "react-i18next";
import { Title } from "@vapor/react-custom";
import { useNavigate, useLocation } from "react-router-dom";
import usePostCustom from "../../../../hooks/usePostCustom";
import BuyProspectusPersonModal from "./buyProspectusPersonModal";
import Spinner from "../../../../custom-components/Spinner";
import {
  IShowBuyModalInterface,
  IAmounts,
  IDownloadData,
} from "../interfaces/infocamere.interface";
import { currencyFormat } from "../../../../helpers/currencyFormat";

export default function PersonResultList() {
  const { t } = useTranslation(); // Initialize useTranslation hook
  const navigate = useNavigate();
  const location = useLocation();
  const { params }: any = location?.state;
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  const [showBuyModal, setShowBuyModal] = useState<IShowBuyModalInterface>({
    state: false,
    data: [],
    price: "",
    docType: "",
    description: "",
    selectedCredit: "studioCredit",
  });
  const downloadContentRequest = usePostCustom(
    "archiveinfocamere/webservicecall"
  );
  const webServiceCallRequest = usePostCustom("infocamere/webservicecall");
  const [persons, setPersons] = useState<any[]>([]);
  const [insufficientCredit, setInsufficientCredit] = useState<boolean>(false);
  const [amounts, setAmounts] = useState<IAmounts>({
    studioAmount: 0,
    userAmount: 0,
    price: 0,
    total: 0,
  });

  useEffect(() => {
    //fetch data
    async function fetchData() {
      const response: any = await webServiceCallRequest.doFetch(false, params);
      const { persone, studioCredit, userCredit, prezzo, num } = response.data;
      if (num === -1) return;  //if no result dont set any state
      const price = prezzo === undefined ? "0.60" : prezzo;
      const total = parseFloat(studioCredit.studioAmount) - parseFloat(price);

      setInsufficientCredit(total <= 0 ? true : false);
      setAmounts({
        studioAmount: parseFloat(studioCredit.studioAmount),
        userAmount: parseFloat(userCredit.userAmount),
        price: parseFloat(price),
        total,
      });
      setPersons(persone);
    }

    fetchData();
  }, [params]);

  const navigateToDownloads = () => {
    navigate("/infocamere/downloads");
  };

  function handleCreditChoice(type: any) {
    let amountsUpdate = { ...amounts };

    if (type === "studioCredit") {
      amountsUpdate.total = amounts.studioAmount - amounts.price;
      if (amountsUpdate.total <= 0) {
        setInsufficientCredit(true);
      } else {
        setInsufficientCredit(false);
      }
    } else if (type === "userCredit") {
      amountsUpdate.total = amounts.userAmount - amounts.price
      if (amountsUpdate.total <= 0) {
        setInsufficientCredit(true);
      } else {
        setInsufficientCredit(false);
      }
    }

    setShowBuyModal({ ...showBuyModal, selectedCredit: type });
    setAmounts({ ...amountsUpdate });
  }

  async function handleParamsDownload() {
    const params: IDownloadData = {
      fileUniqueid: "",
      ambito: "persone",
      ricerca: "acquistaprospetto",
      docType: showBuyModal.docType,
      data: showBuyModal.data.AnagraficaPersona.KAltnPers,
      cciaa: "",
      codFiscPosizione: "",
      naturaGiuridica: "",
      last: "",
      dataType: "codicefiscale",
      description: showBuyModal.description,
      creditChoice: showBuyModal.selectedCredit,
    };
    const response: any = await downloadContentRequest.doFetch(true, params);
    if (response.statusText === "OK") {
      setShowSuccessMessage(true);
    } else {
      setShowSuccessMessage(false);
    }
  }

  return (
    <>
      <Title
        divider
        reduced
        rightItems={[
          <Button
            onClick={() => navigate("/infocamere/search_person")}
            variant="outlined"
            color="info"
          >
            {t("Indietro")}
          </Button>,
          <Button variant="contained" color="info">
            {t("Vai all'Area download")}
          </Button>,
        ]}
        title={`Lista persone ${persons.length} resultati`}
      />

      {webServiceCallRequest.loading && <Spinner />}

      {persons?.length > 0 ? (
        persons?.map((item: any, index: number) => (
          <Card key={index}>
            <CardContent>
              <Typography variant="h5" align="center">
                {`${item.AnagraficaPersona.Cognome} ${item.AnagraficaPersona.Nome}`}
              </Typography>
              <Container>
                <Card className="mb-3">
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={8}>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Codice fiscale")}:{" "}
                          </b>{" "}
                          {item.AnagraficaPersona.CodFiscPers}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Luogo di nascita")}::{" "}
                          </b>{" "}
                          {`${
                            item.AnagraficaPersona.LuogoNas
                              ? item.AnagraficaPersona.LuogoNas
                              : ""
                          } (${item.AnagraficaPersona.SglPrvNas})`}
                        </Typography>
                        <Typography sx={{ mb: 2 }}>
                          {" "}
                          <b style={{ color: "#08c" }}>
                            {t("Data di nascita")}:{" "}
                          </b>{" "}
                          {item.AnagraficaPersona.DtNascita}
                        </Typography>
                      </Grid>
                      <Grid item container alignItems="center" xs={4}>
                        <Button
                          variant="contained"
                          color="info"
                          style={{ width: "12.5rem", marginBottom: "0.5rem" }}
                          onClick={() =>
                            setShowBuyModal({
                              ...showBuyModal,
                              state: true,
                              data: item,
                              price: item.prezzi.scheda_cariche_attuali.price,
                              docType: "",
                            })
                          }
                        >
                          {t("Cariche attuali")} (€{" "}
                          {currencyFormat(item.prezzi.scheda_cariche_attuali.price, 2, ",", ".")})
                        </Button>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Container>
            </CardContent>
          </Card>
        ))
      ) : (
        <Alert
          variant="filled"
          severity="info"
          style={{
            backgroundColor: "#4fc6e1",
            color: "white",
            fontWeight: "bold",
            textAlign: "center",
            marginTop: "16px",
          }}
        >
          {t("La ricerca effettuata non ha restituito alcun risultato.")}
        </Alert>
      )}

      <BuyProspectusPersonModal
        amounts={amounts}
        insufficientCredit={insufficientCredit}
        buyModalData={showBuyModal}
        setBuyModalData={setShowBuyModal}
        showSuccessMessage={showSuccessMessage}
        handleCreditChoice={handleCreditChoice}
        handleParamsDownload={handleParamsDownload}
        navigateToDownloads={navigateToDownloads}
      />
    </>
  );
}
