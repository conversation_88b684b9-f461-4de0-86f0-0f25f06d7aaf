import {
  Box,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  IconButton,
  Grid,
  Typography,
  Badge,
} from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { IModalProps } from "../interfaces/infocamere.interface"
import PriceChoice from "../helpers/priceChoiceSection";


export default function BuyProspectusPersonModal(props: IModalProps) {
  const {
    amounts,
    insufficientCredit,
    buyModalData,
    setBuyModalData,
    showSuccessMessage,
    handleCreditChoice,
    handleParamsDownload,
    navigateToDownloads,
  } = props;
  const { t } = useTranslation();

  return (
    <>
      <Dialog
        open={buyModalData.state}
        onClose={() => setBuyModalData({ ...buyModalData, state: false })}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>
          {t("Acquista prospetto")}
          <IconButton
            color="primary"
            onClick={() => setBuyModalData({ ...buyModalData, state: false })}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <Divider variant="fullWidth" />
        <DialogContent>
          <Box
            autoComplete="off"
            component="form"
            noValidate
            sx={{
              "& > :not(style)": {
                mt: 2,
                mb: 4,
                ml: 0,
                width: 550,
              },
            }}
          >
            <Grid item xs={12}>
              <Typography>{t("Descrizione")}</Typography>
              <TextField
                type="text"
                id="description"
                name="description"
                onChange={(e: any) =>
                  setBuyModalData({
                    ...buyModalData,
                    description: e.target.value,
                  })
                }
              />
            </Grid>
            {showSuccessMessage ? (
              <Grid item xs={12}>
                <Badge
                  variant="dot"
                  color="success"
                  sx={{
                    backgroundColor: "rgba(0, 255, 0, 0.12)",
                    color: "green",
                  }}
                  style={{ fontSize: "15px" }}
                >
                  {t(
                    "La richiesta di download è stata salvata, in pochi minuti il file sara disponibile per il download."
                  )}
                </Badge>
              </Grid>
            ) : (
              <PriceChoice
                selectedCredit={buyModalData.selectedCredit}
                amounts={amounts}
                handleCreditChoice={handleCreditChoice}
                insufficientCredit={insufficientCredit}
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setBuyModalData({ ...buyModalData, state: false })}
            variant="outlined"
          >
            {t("Annulla")}
          </Button>
          <Button
            onClick={
              showSuccessMessage ? navigateToDownloads : handleParamsDownload
            }
            variant="contained"
            sx={{
              mr: 1,
            }}
          >
            {showSuccessMessage ? t("Vai all'Area download") : t("Conferma")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
