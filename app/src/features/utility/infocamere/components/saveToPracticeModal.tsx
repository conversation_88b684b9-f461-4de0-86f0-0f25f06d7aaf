import { Box, TextField, Dialog, DialogTitle, DialogContent, Divider, IconButton, Grid, Stack, Button, DialogActions, LinearProgress, Typography } from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import { debounce } from "lodash";
import { useCallback } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { IOptionsInterface, IModalPracticeProps } from "../interfaces/infocamere.interface";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

export default function SaveToPracticeModal(props: IModalPracticeProps) {
    const { state, setOpenSearchPracticeModal, fileUniqueId, setFileUniqueId, handleSaveFileInPractice, showSelectedArchiveName, setShowSelectedArchiveName, searchResult, setSearchResult, disable<PERSON><PERSON>e<PERSON><PERSON><PERSON>, setDisableChangeButton, confirmLoading } = props;
    const { t } = useTranslation();
    const searchArchiveRequest = useGetCustom("default/archive/search?noTemplateVars=true");

    //search functions
    const debounceSearchArchive = debounce(async (value: string) => {
        let params = {
            q: value,
            from: "calendar"
        };
        const response: any = await searchArchiveRequest.doFetch(true, params);
        setSearchResult(response.data);
    }, 500);

    const memoizedSearchArchive = useCallback(debounceSearchArchive, [debounceSearchArchive]);

    const handleArchiveSearch = (value: any) => {
        setShowSelectedArchiveName(value);
        memoizedSearchArchive(value);
    };

    const handleAutocompleteChange = (_event: React.ChangeEvent<{}>, value: any) => {
        setDisableChangeButton(!!value); // Set disabled to true if a value is selected
        const selectedOption: IOptionsInterface = searchResult.find((option: any) => {
            return option.headerArchive === value;
        });
        setFileUniqueId(selectedOption ? selectedOption.id : "");
    };

    const handleChangeButton = () => {
        setDisableChangeButton(false);
        setShowSelectedArchiveName("");
    };

    return (
        <Dialog open={state} onClose={() => setOpenSearchPracticeModal({ state: false })} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
            <DialogTitle>
                {t("Acquista prospetto")}
                <IconButton color="primary" onClick={() => setOpenSearchPracticeModal({ state: false })}>
                    <Close />
                </IconButton>
            </DialogTitle>
            <Divider variant="fullWidth" />
            <DialogContent>
                <Box
                    autoComplete="off"
                    component="form"
                    noValidate
                    sx={{
                        "& > :not(style)": {
                            mt: 2,
                            mb: 4,
                            ml: 0,
                            width: 550
                        }
                    }}
                >
                    <Grid item xs={12}>
                        <Stack alignItems="end" direction="row">
                            <CustomAutocomplete
                                selectOnFocus
                                clearOnBlur
                                handleHomeEndKeys
                                freeSolo
                                loading={searchArchiveRequest.loading}
                                value={showSelectedArchiveName}
                                onInputChange={(_: any, newInputValue: any) => handleArchiveSearch(newInputValue)}
                                onChange={handleAutocompleteChange}
                                options={searchResult.map((result: any) => result.headerArchive)}
                                renderOption={(props: any, option: any) => (
                                    <div {...props}>
                                        <span
                                            dangerouslySetInnerHTML={{
                                                __html: option
                                            }}
                                        />
                                    </div>
                                )}
                                renderInput={(params: any) => <TextField {...params} disabled={disableChangeButton} label="Pratica" />}
                                sx={{
                                    width: 500
                                }}
                            />
                            <Button
                                size="medium"
                                disabled={disableChangeButton === true ? false : true}
                                onClick={handleChangeButton}
                                sx={{
                                    "&.MuiButtonBase-root.MuiButton-root.MuiButton-sizeMedium": {
                                        borderRadius: 0,
                                        minWidth: 40,
                                        p: 1
                                    }
                                }}
                                variant="contained"
                            >
                                Cambia
                            </Button>
                        </Stack>
                    </Grid>

                    {confirmLoading && (
                        <>
                            <Typography color="primary.interactiveDefault">
                                {t("Download in corso...")}
                                <LinearProgress />
                            </Typography>
                        </>
                    )}
                </Box>
            </DialogContent>
            <DialogActions>
                <Button variant="outlined" onClick={() => setOpenSearchPracticeModal(false)}>
                    {t("Annulla")}
                </Button>
                <Button
                    disabled={fileUniqueId === ""}
                    variant="contained"
                    onClick={handleSaveFileInPractice}
                    sx={{
                        mr: 1
                    }}
                >
                    {t("Conferma")}
                </Button>
            </DialogActions>
        </Dialog>
    );
}
