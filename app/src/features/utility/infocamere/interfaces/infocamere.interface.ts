export interface IDefaultQuery {
  page: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: string;
  startDate?: string;
  endDate?: string;
}

export interface IAmounts {
  studioAmount: number;
  userAmount: number;
  price: number;
  total: number;
}

export interface IDataType {
  value: string;
  name: string;
}

export interface IShowBuyModalInterface {
  state: boolean;
  data: any;
  price: string;
  docType: string;
  description: string;
  selectedCredit: string;
}

export interface IModalProps {
  amounts: IAmounts;
  insufficientCredit: boolean;
  buyModalData: IShowBuyModalInterface;
  setBuyModalData: React.Dispatch<React.SetStateAction<IShowBuyModalInterface>>;
  showSuccessMessage: boolean;
  handleCreditChoice: (type: string) => void;
  handleParamsDownload: () => void;
  navigateToDownloads: () => void;
}

export interface IDownloadData {
  fileUniqueid: string;
  ambito: string;
  ricerca: string;
  docType: string;
  data: number;
  cciaa: string;
  codFiscPosizione: any;
  naturaGiuridica: string;
  last: any;
  dataType: any;
  description: string;
  creditChoice: string;
}

export interface IOptionsInterface {
  id: string;
  codicearchivio: string;
  uniqueid: string;
  ruologeneralenumero: string;
  ruologeneraleanno: string;
  descrizione: string;
  nome_pratica: string;
  is_archived: string;
  oggetto?: string;
  listaclienti?: string;
  listacontroparti?: string;
  rgnRga: string;
  hourly_rate: string;
  tipologiapratica: string;
  headerArchive: string;
}

export interface IModalPracticeProps {
  state: boolean;
  setOpenSearchPracticeModal: React.Dispatch<React.SetStateAction<any>>;
  fileUniqueId: string;
  setFileUniqueId: React.Dispatch<React.SetStateAction<string>>;
  handleSaveFileInPractice: () => void;
  showSelectedArchiveName: string;
  setShowSelectedArchiveName: React.Dispatch<React.SetStateAction<string>>;
  searchResult: any[];
  setSearchResult: React.Dispatch<React.SetStateAction<any[]>>;
  disableChangeButton: boolean;
  setDisableChangeButton: React.Dispatch<React.SetStateAction<boolean>>;
  confirmLoading: boolean;
}

export interface IDefaultQuery {
  page: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: string;
  startDate?: string;
  endDate?: string;
}

export interface IPriceChoice {
  selectedCredit: string;
  amounts: IAmounts;
  handleCreditChoice: (type: string) => void;
  insufficientCredit: boolean;
}

