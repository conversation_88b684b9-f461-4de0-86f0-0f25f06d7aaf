import Infocamere from ".";
import PriceList from "./priceList";
import SearchCompany from "./searchCompany";
import SearchPersons from "./searchPersons";
import PersonResultList from "./components/personsResultList";
import CompanyResultList from "./components/companyResultList";
import GoToDownloads from "./goToDownloads";

export const infocamere = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere",
      element: <Infocamere />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/price-list",
      element: <PriceList />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/search_company",
      element: <SearchCompany />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/search_person",
      element: <SearchPersons />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/webservicecall/company",
      element: <CompanyResultList />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/webservicecall/person",
      element: <PersonResultList />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/infocamere/downloads",
      element: <GoToDownloads />,
    },
  },
];
