import { useState, useEffect } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import useGetCustom from "../../../hooks/useGetCustom";
import TableContainer from "@mui/material/TableContainer";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableRow from "@mui/material/TableRow";
import { styled } from "@mui/material/styles";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import Spinner from "../../../custom-components/Spinner";
import PageTitle from "../../../custom-components/PageTitle";

const StyledTableRow = styled(TableRow)(({ theme }: any) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.black,
    color: theme.palette.common.white,
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
  },
}));

export default function PriceList() {
  const [priceListData, setPriceListDate] = useState<any>([]);
  const priceListInfocamereRequest = useGetCustom("infocamere/price-list");

  useEffect(() => {
    async function fetchPriceList() {
      const response: any = await priceListInfocamereRequest.doFetch();
      const { pricelist } = response.data;
      setPriceListDate(pricelist);
    }
    fetchPriceList();
  }, []);

  return (
    <>
      <VaporPage>
        <PageTitle title="Listino prezzi" pathToPrevPage="/infocamere" />

        {priceListInfocamereRequest.loading && <Spinner />}
        <TableContainer>
          <Table sx={{ width: "100%" }} aria-label="customized table">
            <TableBody>
              {priceListData.length > 0 ? (
                priceListData.map((price: any, index: number) => {
                  return (
                    <StyledTableRow key={index}>
                      <StyledTableCell>
                        {price.nome
                          ? `${price.title} - ${price.nome}`
                          : price.title}
                      </StyledTableCell>
                      <StyledTableCell>€ {price.amount}</StyledTableCell>
                    </StyledTableRow>
                  );
                })
              ) : (
                <h1>No price listed.</h1>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </VaporPage>
    </>
  );
}
