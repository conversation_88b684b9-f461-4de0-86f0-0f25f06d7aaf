import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Button,
    InputLabel,
    Card,
    CardContent,
    Typography,
    Hyperlink,
} from "@vapor/react-material";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import PersonIcon from "@mui/icons-material/Person";
import DownloadIcon from "@mui/icons-material/Download";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import useGetCustom from "../../../hooks/useGetCustom";
import Spinner from "../../../custom-components/Spinner";

export default function Infocamere() {
    const navigate = useNavigate();
    const [amountsData, setAmountsData] = useState<any[]>([]);
    const [errorsType, setErrorsType] = useState<any>([]);
    const infocamereRequest = useGetCustom("/infocamere");

    useEffect(() => {
        async function fetchInfocamere() {
            const response: any = await infocamereRequest.doFetch(true);
            const { amounts, errors } = response.data;
            const formattedAmount = amounts.map((item: any) => {
                item.amountDescription = item.amountDescription.replace(
                    "&euro;",
                    ""
                );
                return item;
            });
            setAmountsData(formattedAmount);
            setErrorsType(errors);
        }

        fetchInfocamere();
    }, []);

    return (
        <>
            {!infocamereRequest.loading ? (
                <VaporPage title="Visure e Bilanci">
                    <VaporPage.Section>
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <Typography
                                variant="displaySmall"
                                component="div"
                                color="primary.interactiveDefault"
                                sx={{ paddingBottom: "10px" }}
                            >
                                Credito
                            </Typography>
                            <Typography>
                                Per ricaricare il credito è necessario andare
                                nella sezione{" "}
                                <Hyperlink onClick={() => navigate("/credit")}>
                                    Gestione credito.
                                </Hyperlink>
                            </Typography>
                        </div>

                        <div style={{ display: "flex" }}>
                            <Card
                                style={{ backgroundColor: "#F2F6F8" }}
                                sx={{
                                    maxWidth: 700,
                                    flex: 1,
                                    marginTop: "10px",
                                }}
                            >
                                <CardContent>
                                    {amountsData.map((amount: any) => {
                                        return (
                                            <div style={{ display: "flex" }}>
                                                <InputLabel sx={{ flex: 1 }}>
                                                    {amount.description ===
                                                    "Avvocato"
                                                        ? "Credito personale"
                                                        : amount.description}
                                                </InputLabel>{" "}
                                                <InputLabel
                                                    sx={{ marginLeft: "auto" }}
                                                >
                                                    {`${
                                                        amount.amount === "0"
                                                            ? "€ 0,000"
                                                            : "€" +
                                                              amount.amountDescription
                                                    }`}
                                                </InputLabel>
                                            </div>
                                        );
                                    })}
                                </CardContent>
                            </Card>
                            <Card
                                style={{ backgroundColor: "#F2F6F8" }}
                                sx={{
                                    maxWidth: 700,
                                    marginLeft: "auto",
                                }}
                            >
                                {(amountsData[0]?.amount ||
                                    amountsData[1]?.amount) <= 0 && (
                                    <CardContent sx={{ maxWidth: "100%" }}>
                                        <Typography
                                            sx={{
                                                textAlign: "center",
                                                whiteSpace: "pre-wrap",
                                            }}
                                        >
                                            <Typography sx={{ color: "red" }}>
                                                Il credito non è sufficiente per
                                                effettuare ricerche.
                                            </Typography>
                                            Per abilitare l'utente a poter
                                            effettuare ricerche andare nella
                                            sezione di{" "}
                                            <Hyperlink
                                                onClick={() =>
                                                    navigate("/delegations")
                                                }
                                            >
                                                Configurazione deleghe
                                            </Hyperlink>
                                        </Typography>
                                    </CardContent>
                                )}
                            </Card>
                        </div>
                    </VaporPage.Section>
                    <VaporPage.Section>
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <Typography
                                variant="displaySmall"
                                component="div"
                                color="primary.interactiveDefault"
                                sx={{ paddingBottom: "10px" }}
                            >
                                Ricerca
                            </Typography>
                            <Typography>
                                Cerca nei registri per ottenere informazioni e
                                documenti ufficiali su Imprese e Persone.
                            </Typography>
                            <br />
                        </div>

                        <div style={{ display: "flex" }}>
                            <Card
                                style={{
                                    backgroundColor: "#F2F6F8",
                                    marginTop: "10px",
                                }} // Adding margin top for spacing
                                sx={{
                                    minWidth: "50%",
                                    flex: 1,
                                    marginRight: "10px", // Adding margin right for spacing between cards
                                }}
                            >
                                <CardContent>
                                    <div style={{ display: "flex" }}>
                                        <Button
                                            sx={{ margin: "auto" }}
                                            color="primary"
                                            variant="contained"
                                            disabled={errorsType.type}
                                            onClick={() =>
                                                navigate(
                                                    "/infocamere/search_company"
                                                )
                                            }
                                        >
                                            <BusinessCenterIcon
                                                sx={{ mr: 1 }}
                                            />
                                            Ricerca imprese
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                            <Card
                                style={{
                                    backgroundColor: "#F2F6F8",
                                    marginTop: "10px",
                                }} // Adding margin top for spacing
                                sx={{
                                    minWidth: "50%",
                                    flex: 1,
                                }}
                            >
                                <CardContent>
                                    <div style={{ display: "flex" }}>
                                        <Button
                                            sx={{ margin: "auto" }}
                                            color="primary"
                                            variant="contained"
                                            disabled={errorsType.type}
                                            onClick={() =>
                                                navigate(
                                                    "/infocamere/search_person"
                                                )
                                            }
                                        >
                                            <PersonIcon />
                                            Ricerca persone
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </VaporPage.Section>
                    <VaporPage.Section>
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <Typography
                                variant="displaySmall"
                                component="div"
                                color="primary.interactiveDefault"
                                sx={{ paddingBottom: "10px" }}
                            >
                                Area download
                            </Typography>
                            <Typography>
                                Effettua il download diretto dei documenti
                                richiesti o salvali in una pratica.
                            </Typography>
                            <Typography style={{ fontWeight: "bold" }}>
                                ATTENZIONE!
                            </Typography>
                            <Typography style={{ fontWeight: "bold" }}>
                                Si raccomanda di effettuare il download o
                                salvare i documenti in una pratica nelle 24 ore
                                successive alla richiesta per evitare di far
                                scadere la richiesta stessa.
                            </Typography>
                            <br />
                        </div>
                        <div style={{ display: "flex", width: "50%" }}>
                            <Card
                                style={{
                                    backgroundColor: "#F2F6F8",
                                    marginTop: "10px",
                                }}
                                sx={{
                                    flex: 1,
                                    marginRight: "10px",
                                }}
                            >
                                <CardContent>
                                    <div style={{ display: "flex" }}>
                                        <Button
                                            sx={{ margin: "auto" }}
                                            color="primary"
                                            variant="contained"
                                            disabled={errorsType.type}
                                            onClick={() =>
                                                navigate(
                                                    "/infocamere/downloads"
                                                )
                                            }
                                        >
                                            <DownloadIcon />
                                            Vai ai download
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </VaporPage.Section>
                    <VaporPage.Section>
                        <div
                            style={{
                                width: "100%",
                            }}
                        >
                            <Typography
                                variant="displaySmall"
                                component="div"
                                color="primary.interactiveDefault"
                                sx={{ paddingBottom: "10px" }}
                            >
                                Prezzi
                            </Typography>
                            <Typography>
                                Visualizza il prezzo di ricerche e richieste di
                                download di documenti.
                            </Typography>
                            <br />
                        </div>

                        <div style={{ display: "flex", width: "50%" }}>
                            <Card
                                style={{
                                    backgroundColor: "#F2F6F8",
                                    marginTop: "10px",
                                }}
                                sx={{
                                    flex: 1,
                                    marginRight: "10px",
                                }}
                            >
                                <CardContent>
                                    <div style={{ display: "flex" }}>
                                        <Button
                                            sx={{ margin: "auto" }}
                                            color="primary"
                                            variant="contained"
                                            onClick={() =>
                                                navigate(
                                                    "/infocamere/price-list"
                                                )
                                            }
                                        >
                                            <ShoppingCartIcon />
                                            Listino prezzi
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </VaporPage.Section>
                </VaporPage>
            ) : (
                <Spinner />
            )}
        </>
    );
}
