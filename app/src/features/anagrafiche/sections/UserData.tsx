import { useEffect, useLayoutEffect } from "react";
import { Divider } from "@vapor/react-material";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import DatiGeneraliDetail from "./components/DatiGeneraliDetail";
import RecapitiDetail from "./components/RecapitiDetail";
import ContattiCollegatiDetail from "./components/ContatiCollegatiDetail";
import NoteProfilazioneDetail from "./components/NoteProfilazioneDetail";
import FiscaliBancheDetail from "./components/FiscaliBancheDetail";
import AntiriciclaggioDetail from "./components/AntiriciclaggioDetail";
import CameraDiCommercioDetail from "./components/CameraDiCommercioDetail";
// import GruppiDetail from "./components/GruppiDetail";
import DatiGeneraliForm from "../actions/components/DatiGeneraliForm";
import FisacliBancheForm from "../actions/components/FiscaliBancheForm";
import NoteProfilzaioneForm from "../actions/components/NoteProfilazioneForm";
import RecapitiForm from "../actions/components/RecapitiForm";
import CammeraDiCommercioForm from "../actions/components/CammeraDiCommercioForm";
// import GruppiForm from "../actions/components/GruppiForm";
import ContattiCollegatiForm from "../actions/components/ContatiCollegatiForm";
import AntiriciclaggioForm from "../actions/components/AntiriciclaggioForm";
import Spinner from "../../../custom-components/Spinner";
import useGetAnagraficheActionData from "./../hooks/useGetAnagraficheActionData";
import { useTranslation } from "@1f/react-sdk";
import useSaveAnagrafiche from "../hooks/useSaveAnagrafiche";
import { debounce } from "lodash";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import useGetRelationships from "../hooks/useGetRelationships";
import { useChangedKey } from "../hooks/useChangedKey";
import CampiDinamiciForm from "../actions/components/CampiDinamiciForm";
import CampiDinamiciDetail from "./components/CampiDinamiciDetail";
import { useValidationScrolling } from "../hooks/useValidationScrolling";
import { useUser } from "../../../store/UserStore";

const UserData = ({
    refs,
    setCurrentSection,
    userType,
    setUserType,
    method,
    showCreateForm,
    setShowCreateForm,
    setBottomPadding,
    // Dynamic fields props
    dynamicFields,
    dateStates,
    setDateStates,
    isLoadingFields,
    parseSelectOptions,
    validateRegex,
    isChecked
}: any) => {
    const { isInitialized, anagrafiche } = useAnagraficheProvider();

    const { anagraficheData, isUpdate, uniqueId, setAngraficheData } = useGetAnagraficheActionData();
    const { readContactData, setReadContactData } = useGetRelationships(showCreateForm);
    const { onSubmitCreate } = useSaveAnagrafiche(uniqueId);

    const { t } = useTranslation();
    const changedKey = useChangedKey(showCreateForm);
    const {
        formState: { errors }
    } = method;

    const { modules }: any = useUser();

    // Check if sections should be shown based on subscription type
    const shouldShowConditionalSections = modules?.provisioningRow?.subscription_type != 37;
    const { handleSaveAttempt } = useValidationScrolling({
        method,
        refs: {
            datiGeneraliRef: refs.datiGeneraliRef,
            noteprofilazioneRefs: refs.noteprofilazioneRefs,
            fiscaliBancheRefs: refs.fiscaliBancheRefs,
            parentRef: refs.parentRef
        },
        showCreateForm,
        setShowCreateForm,
        isUpdate,
        isSociety: userType?.type !== CUSTOMER_TYPE.Person
    });

    const debouncedHandleIntersection = debounce((entries: any, setCurrentSection: any) => {
        entries.forEach((entry: any) => {
            if (entry.isIntersecting) {
                setCurrentSection(entry.target.id);
            }
        });
    }, 100);

    const { tags } = anagrafiche;

    const { datiGeneraliRef, recapitiRef, contactsRefs, noteprofilazioneRefs, cameraDiCommercioRefs, fiscaliBancheRefs, antiriciclaggioRefs, campiDinamiciRefs, groupsRefs, parentRef } = refs;

    useEffect(() => {
        try {
            const observer = new IntersectionObserver((entries) => debouncedHandleIntersection(entries, setCurrentSection), {
                root: null,
                threshold: [0.1, 0.3, 0.5, 0.7, 1.0],
                rootMargin: "-50px 0px -50% 0px"
            });

            const sections = [
                datiGeneraliRef,
                recapitiRef,
                ...(shouldShowConditionalSections ? [contactsRefs] : []),
                noteprofilazioneRefs,
                cameraDiCommercioRefs,
                fiscaliBancheRefs,
                ...(shouldShowConditionalSections ? [antiriciclaggioRefs] : []),
                ...(shouldShowConditionalSections ? [campiDinamiciRefs] : []),
                groupsRefs
            ].filter(Boolean);

            sections.forEach((ref) => {
                if (ref.current) {
                    observer.observe(ref.current);
                }
            });

            return () => {
                sections.forEach((ref) => {
                    if (ref.current) {
                        observer.unobserve(ref.current);
                    }
                });
            };
        } catch (error: any) {
            console.log("is error in here!!!!", error);
        }
    }, [
        setCurrentSection,
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        noteprofilazioneRefs,
        cameraDiCommercioRefs,
        fiscaliBancheRefs,
        antiriciclaggioRefs,
        campiDinamiciRefs,
        groupsRefs,
        parentRef,
        shouldShowConditionalSections
    ]);

    useEffect(() => {
        if (parentRef.current) {
            const lastSection = shouldShowConditionalSections ? campiDinamiciRefs.current : fiscaliBancheRefs.current;
            if (lastSection) {
                const paddingBottom = lastSection.offsetHeight;
                setBottomPadding(paddingBottom);
            }
        }
    }, [campiDinamiciRefs, fiscaliBancheRefs, parentRef, shouldShowConditionalSections]);

    useEffect(() => {
        const sectionRefs: Record<string, React.RefObject<HTMLDivElement>> = {
            datiGenerali: datiGeneraliRef,
            recapiti: recapitiRef,
            ...(shouldShowConditionalSections && { contactiCollegati: contactsRefs }),
            noteProfilazione: noteprofilazioneRefs,
            cameraDiCommercio: cameraDiCommercioRefs,
            fiscaliBanche: fiscaliBancheRefs,
            ...(shouldShowConditionalSections && { antiriciclaggio: antiriciclaggioRefs }),
            ...(shouldShowConditionalSections && { campiDinamici: campiDinamiciRefs }),
            gruppi: groupsRefs
        };

        if (changedKey && sectionRefs[changedKey]?.current) {
            const currentElement = sectionRefs[changedKey]?.current;
            if (currentElement) {
                currentElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center"
                });
            }
        }
    }, [changedKey, parentRef, shouldShowConditionalSections]);

    useLayoutEffect(() => {
        if (isInitialized) {
            if ("scrollRestoration" in history) {
                history.scrollRestoration = "manual";
            }

            window.scrollTo(0, 0);
            if (parentRef.current) {
                parentRef.current.scrollTop = 0;
            }

            const timer = setTimeout(() => {
                if (datiGeneraliRef.current) {
                    datiGeneraliRef.current.scrollIntoView({
                        behavior: "smooth",
                        block: "center"
                    });
                }
            }, 100);

            return () => clearTimeout(timer);
        }
    }, [isInitialized]);

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            {showCreateForm.datiGenerali ? (
                <div id="1" ref={datiGeneraliRef}>
                    <DatiGeneraliForm
                        t={t}
                        method={method}
                        anagraficheData={anagraficheData}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                        isSociety={userType?.type !== CUSTOMER_TYPE.Person}
                        isPersonaFisica={userType?.parentSelected === CUSTOMER_TYPE.PersonNumber}
                        userType={userType}
                        setUserType={setUserType}
                        handleSaveAttempt={handleSaveAttempt}
                    />
                </div>
            ) : (
                <div id="1" ref={datiGeneraliRef}>
                    <DatiGeneraliDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        isPersonaFisica={userType.parentSelected === CUSTOMER_TYPE.PersonNumber}
                        hasError={!!errors?.partitaiva || !!errors?.codicefiscale}
                    />
                </div>
            )}
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            {showCreateForm.recapiti ? (
                <div id="2" ref={recapitiRef}>
                    <RecapitiForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="2" ref={recapitiRef}>
                    <RecapitiDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                </div>
            )}
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            {shouldShowConditionalSections &&
                userType.parentSelected !== CUSTOMER_TYPE.Person &&
                (showCreateForm.contactiCollegati ? (
                    <div id="3" ref={contactsRefs}>
                        <ContattiCollegatiForm
                            anagraficheData={anagraficheData}
                            setAngraficheData={setAngraficheData}
                            t={t}
                            showCreateForm={showCreateForm}
                            contactData={readContactData}
                            setReadContactData={setReadContactData}
                            setShowCreateForm={setShowCreateForm}
                        />
                    </div>
                ) : (
                    <div id="3" ref={contactsRefs}>
                        <ContattiCollegatiDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} contactData={readContactData} isInitialized={isInitialized} />
                    </div>
                ))}

            {shouldShowConditionalSections && userType.parentSelected !== CUSTOMER_TYPE.Person && (
                <Divider
                    light
                    style={{
                        marginBottom: "25px",
                        marginTop: "25px",
                        width: "105%"
                    }}
                />
            )}

            {userType.parentSelected !== CUSTOMER_TYPE.PersonNumber && (
                <>
                    {showCreateForm.cameraDiCommercio ? (
                        <div id="5" ref={cameraDiCommercioRefs}>
                            <CammeraDiCommercioForm
                                anagraficheData={anagraficheData}
                                method={method}
                                isUpdate={isUpdate}
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                                onSubmitUpdate={onSubmitCreate}
                            />
                        </div>
                    ) : (
                        <div id="5" ref={cameraDiCommercioRefs}>
                            <CameraDiCommercioDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                        </div>
                    )}
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                </>
            )}

            {showCreateForm.noteProfilazione ? (
                <div id="4" ref={noteprofilazioneRefs}>
                    <NoteProfilzaioneForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        activeTags={tags}
                        onSubmitUpdate={onSubmitCreate}
                        handleSaveAttempt={handleSaveAttempt}
                    />
                </div>
            ) : (
                <div id="4" ref={noteprofilazioneRefs}>
                    <NoteProfilazioneDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                </div>
            )}

            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            {showCreateForm.fiscaliBanche ? (
                <div id="6" ref={fiscaliBancheRefs}>
                    <FisacliBancheForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                        handleSaveAttempt={handleSaveAttempt}
                    />
                </div>
            ) : (
                <div id="6" ref={fiscaliBancheRefs}>
                    <FiscaliBancheDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                </div>
            )}

            {/* Conditionally render Antiriciclaggio section */}
            {shouldShowConditionalSections && (
                <>
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                    {showCreateForm.antiriciclaggio ? (
                        <div id="7" ref={antiriciclaggioRefs}>
                            <AntiriciclaggioForm
                                method={method}
                                anagraficheData={anagraficheData}
                                isUpdate={isUpdate}
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                                onSubmitUpdate={onSubmitCreate}
                            />
                        </div>
                    ) : (
                        <div id="7" ref={antiriciclaggioRefs}>
                            <AntiriciclaggioDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                        </div>
                    )}
                </>
            )}

            {/* Conditionally render Campi Dinamici section */}
            {shouldShowConditionalSections && (
                <>
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                    {showCreateForm.campiDinamici ? (
                        <div id="8" ref={campiDinamiciRefs}>
                            <CampiDinamiciForm
                                method={method}
                                anagraficheData={anagraficheData}
                                isUpdate={isUpdate}
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                                onSubmitUpdate={onSubmitCreate}
                                dynamicFields={dynamicFields}
                                dateStates={dateStates}
                                setDateStates={setDateStates}
                                isLoadingFields={isLoadingFields}
                                parseSelectOptions={parseSelectOptions}
                                validateRegex={validateRegex}
                                isChecked={isChecked}
                            />
                        </div>
                    ) : (
                        <div id="8" ref={campiDinamiciRefs}>
                            <CampiDinamiciDetail showCreateForm={showCreateForm} setShowCreateForm={setShowCreateForm} />
                        </div>
                    )}
                </>
            )}
        </>
    );
};
export default UserData;
