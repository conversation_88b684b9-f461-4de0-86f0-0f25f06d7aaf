import { Box, Button, Grid, Menu, MenuItem, TextField,SplitButton  } from "@vapor/v3-components";
import { EllipsisVertical, FileExport, TrashAlt } from "@vapor/react-icons";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import ControlPointIcon from "@mui/icons-material/ControlPoint";
import { useTranslation } from "@1f/react-sdk";
import SearchIcon from "@mui/icons-material/Search";
import { SelectMultiple } from "../../../custom-components/SelectMultiple";
import useGetCustom from "../../../hooks/useGetCustom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import { RELATIONS_OPTIONS } from "../constants/constants";
import AutocompleteFilter from "../../archive/tabs/AutocompleteFilter";

const cancelQueryButtonStyle = {
    marginTop: "auto",
    marginLeft: "1rem"
};

export const AnagraficheFilters = ({ params: defaultParams, defaultQuery, setParams: setDefaultParams, onChangeFunctions, categorie, selectedRows, tipoAnagrafica, tags, setOpenDeleteConfirmation }: any) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { onChangeInput, onChangeMultiSelect, onClickReset, onSubmit, onCityChange } = onChangeFunctions;

    const downloadRequest = useGetCustom("peoplecenter/exportcsv?extended=0");

    const [openExportConfirmation, setOpenExportConfirmation] = useState<boolean>(false);
    const [anchorEl, setAnchorEl] = useState<any>(null);
    const [activeTab, setActiveTab] = useState<"ricerca" | "ricercaTutti">("ricerca");

    const handleClickMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const mappedCategories = categorie.map((category: any) => ({
        value: category.id,
        label: category.nome
    }));

    const mappedTipoAnagrafica = tipoAnagrafica.map((tipo: any) => ({
        value: tipo.id,
        label: tipo.nome
    }));

    const mappedTags = tags.map((tag: any) => ({
        value: tag.id,
        label: tag.description
    }));

    const closeExportConfirmation = async () => {
        setOpenExportConfirmation(false);
    };

    const handleExport = async () => {
        setOpenExportConfirmation(true);
    };

    const handleCreate = async () => {
        navigate(`/anagrafiche/update`);
    };

    const exportAnagrafiche = async () => {
        const anagraficheIds = selectedRows.length > 0 ? selectedRows.join(",") : null;
        const response: any = await downloadRequest.doFetch(true, {
            ...defaultParams,
            anagraficheIds
        });
        const blob = new Blob([response.data], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "ExportAnagrafiche.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setOpenExportConfirmation(false);
    };

    const handleTabChange = (tab: "ricerca" | "ricercaTutti") => {
        setActiveTab(tab);
    };

    const searchcityFilterRequest = useGetCustom("peoplecenter/searchcity?noTemplateVars=true");

    const fetchSearchcityOptions = async (searchValue: string) => {
        const response: any = await searchcityFilterRequest.doFetch(true, { q: searchValue });
        return Array.isArray(response?.data) ? response.data : [];
    };

    return (
        <>
            <ConfirmModal open={openExportConfirmation} handleDecline={closeExportConfirmation} handleAgree={exportAnagrafiche} decline={t("Annulla")} agree={t("Conferma")} confirmText={selectedRows.length > 0 ? `${t("Vuoi confermare l'esportazione di")} ${selectedRows.length} ${t("contatti? L'operazione potrebbe richiedere più tempo del previsto")}` : t("Vuoi confermare l'esportazione di tutti i contatti? L'operazione potrebbe richiedere più tempo del previsto")} title={t("Conferma esportazione")} />

            <Grid container spacing={2} style={{ padding: "16px 16px 0px 16px" }}>
                <Grid item xs={12} sx={{ mb: 2, display: "flex", justifyContent: "space-between" }}>
                    <SplitButton variant="outlined" aria-label="search type button group">
                        <Button onClick={() => handleTabChange("ricerca")} variant={activeTab === "ricerca" ? "contained" : "outlined"} sx={{ }}>
                            {t("Ricerca")}
                        </Button>
                        <Button onClick={() => handleTabChange("ricercaTutti")} variant={activeTab === "ricercaTutti" ? "contained" : "outlined"}>
                            {t("Ricerca per indirizzi")}
                        </Button>
                    </SplitButton>
                    <div style={{ display: "flex", gap: "16px" }}>
                        <div>
                            <Button aria-haspopup="true" id="basic-button" onClick={handleClickMenu}>
                                <EllipsisVertical color="interactive" size="20px" />
                            </Button>
                            <Menu
                                open={Boolean(anchorEl)}
                                anchorEl={anchorEl}
                                id="basic-menu"
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "left"
                                }}
                                transformOrigin={{
                                    vertical: "top",
                                    horizontal: "left"
                                }}
                                PaperProps={{
                                    style: {
                                        transform: "translateX(-150px)",
                                        width: "200px"
                                    }
                                }}
                                MenuListProps={{
                                    "aria-labelledby": "basic-button"
                                }}
                            >
                                <MenuItem onClick={handleExport}>
                                    <FileExport color="default" />
                                    <Box sx={{ ml: 1 }}>{t("Esporta")}</Box>
                                </MenuItem>
                                <MenuItem disabled={selectedRows.length === 0} onClick={setOpenDeleteConfirmation}>
                                    <TrashAlt color="default" />
                                    <Box sx={{ ml: 1 }}>{t(" Elimina")}</Box>
                                </MenuItem>
                            </Menu>
                        </div>

                        <Button onClick={handleCreate} startIcon={<ControlPointIcon />} variant="contained">
                            {t("Crea anagrafica")}
                        </Button>
                    </div>
                </Grid>

                {activeTab === "ricerca" ? (
                    <>
                        <Grid container spacing={2} sx={{ px: 2 }}>
                            <Grid item md={3} sx={{ display: "flex", alignItems: "center", marginTop: "auto" }}>
                                <TextField variant="outlined" value={defaultParams.searchField} placeholder={t("Cerca per C.F. , P. IVA o denominazione")} name="searchField" sx={{ width: "512px" }} onChange={onChangeInput} />
                                <Button
                                    size="medium"
                                    sx={{
                                        "&.MuiButtonBase-root.MuiButton-root.MuiButton-sizeMedium": {
                                            borderRadius: 0,
                                            minWidth: 40,
                                            p: 1
                                        }
                                    }}
                                    variant="contained"
                                    onClick={onSubmit}
                                >
                                    <SearchIcon />
                                </Button>
                            </Grid>
                            <Grid item xs={12} md={1.85} sm={1.85}>
                                <SelectMultiple style={{ width: "100%" }} width="100%" name="searchRelationArray" label={t("Relazione")} selectedValues={defaultParams.searchRelationArray} options={RELATIONS_OPTIONS} onChange={onChangeMultiSelect} placeholder={t("Seleziona relazione")} target="label" keyValue="value" />
                            </Grid>
                            <Grid item xs={12} md={1.85} sm={1.85}>
                                <SelectMultiple style={{ width: "100%" }} width="100%" name="searchCategoriesArray" label={t("Categoria")} options={mappedCategories} selectedValues={defaultParams.searchCategoriesArray} onChange={onChangeMultiSelect} placeholder={t("Seleziona categoria")} target="label" keyValue="value" />
                            </Grid>
                            <Grid item xs={12} md={1.85} sm={1.85}>
                                <SelectMultiple style={{ width: "100%" }} width="100%" name="searchAnagraficheTypes" label={t("Tipo di anagrafica")} options={mappedTipoAnagrafica} onChange={onChangeMultiSelect} selectedValues={defaultParams.searchAnagraficheTypes} placeholder={t("Seleziona anagrafica")} target="label" keyValue="value" />
                            </Grid>
                            <Grid item xs={12} md={1.85} sm={1.85}>
                                <SelectMultiple style={{ width: "100%" }} width="100%" name="searchTagsArray" label={t("Etichette")} options={mappedTags} onChange={onChangeMultiSelect} placeholder={t("Seleziona etichette")} selectedValues={defaultParams.searchTagsArray} target="label" keyValue="value" />
                            </Grid>
                            <Grid item xs={12} md={1.6} sm={1.6} sx={{ display: "flex", alignItems: "center" }} >
                                <Button style={cancelQueryButtonStyle} onClick={onClickReset}>
                                    {t("Annulla ricerca")}
                                </Button>
                            </Grid>
                        </Grid>

                    </>
                ) : (
                    <Grid container spacing={2} sx={{ px: 2 }}>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <TextField fullWidth variant="outlined" name="searchNation" label={t("Nazione")} placeholder={t("Cerca nazione")} onChange={onChangeInput} value={defaultParams.searchNation || ""} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <AutocompleteFilter defaultQuery={defaultQuery} query={defaultParams} setQuery={setDefaultParams} fetchOptions={fetchSearchcityOptions} queryKey="searchCity" queryKey2="" label={t("Città")} placeholder={t("Cerca città")} multiple={false} target="nome" queryVal="id" onChange={onCityChange} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <TextField fullWidth variant="outlined" name="searchWay" label={t("Via")} placeholder={t("Cerca via")} onChange={onChangeInput} value={defaultParams.searchWay || ""} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <TextField fullWidth variant="outlined" name="searchCap" label={t("Cap")} placeholder={t("Cerca cap")} onChange={onChangeInput} value={defaultParams.searchCap || ""} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <TextField fullWidth variant="outlined" name="searchProvince" label={t("Provincia")} placeholder={t("Cerca provincia")} onChange={onChangeInput} value={defaultParams.searchProvince || ""} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7}>
                            <TextField fullWidth variant="outlined" name="searchRegion" label={t("Regione")} placeholder={t("Cerca regione")} onChange={onChangeInput} value={defaultParams.searchRegion || ""} />
                        </Grid>
                        <Grid item xs={12} md={1.7} sm={1.7} sx={{ display: "flex", alignItems: "center" }}>
                            <Button style={cancelQueryButtonStyle} onClick={onClickReset}>
                                {t("Annulla ricerca")}
                            </Button>
                        </Grid>
                    </Grid>
                )}
            </Grid>
        </>
    );
};
