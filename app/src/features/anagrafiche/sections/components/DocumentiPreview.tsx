import { useEffect, useState } from 'react';
import useGetCustom from '../../../../hooks/useGetCustom';
import { Box, CircularProgress, Dialog, DialogContent, DialogTitle, IconButton, Typography } from "@vapor/v3-components";
import { useTranslation } from '@1f/react-sdk';
import { Close } from "@mui/icons-material";
import DocViewer, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
import "@cyntler/react-doc-viewer/dist/index.css";

const restrictedExtensions = ["eml", "msg", "xml", "rtf", "odt"];
const extensionsWithUrl = ["doc", "docx", "xlsx", "pptx"];

const DocumentiPreview = ({ open, setOpen, id, fileName }: any) => {
  const [file, setFile] = useState<any>(null);
  const [fileExtension, setFileExtension] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false)

  const { t } = useTranslation();
  const getPreviewUrl = useGetCustom(`anagrafiche/get-file-content`, { id });

  const handlePreview = async () => {
    setLoading(true)
    const { data }: any = await getPreviewUrl.doFetch(true);
    if (data) {
      const extension = fileName.split(".").pop().toLowerCase();
      setFileExtension(extension);

      if (extensionsWithUrl.includes(extension)) {
        setFile(data.url);
      } else {
        setFile(data.base64);
      }

      setOpen(true);
    }
    setLoading(false)
  };

  useEffect(() => {
    if (open) {
      handlePreview();
    }
  }, [open]);

  const handleClose = () => {
    setOpen(false);
    setFile(null);
    setFileExtension(null);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        fullWidth={true}
        maxWidth="md"
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {t("Preview File")}
          <IconButton color="primary" onClick={handleClose}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {fileExtension && restrictedExtensions.includes(fileExtension) ? (
            <Typography variant="body2" color="error">
              {t("L'anteprima non è disponibile per questo tipo di file.")} ({fileExtension})
            </Typography>
          ) : loading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}><CircularProgress size={64} /></Box>
          ) : <DocViewerPage file={file} fileExtension={fileExtension} />
          }
        </DialogContent>
      </Dialog>
    </>
  );
};

const DocViewerPage = ({ file, fileExtension }: any) => {
  if (!file) return null;

  const docs = [
    {
      uri: file,
      fileType: fileExtension
    }
  ];

  return (
    <Box sx={{ 
      height: '100%', 
      minHeight: '600px',
      '& iframe': {
        minHeight: '600px !important'
      }
    }}>
      <DocViewer
        documents={docs}
        pluginRenderers={DocViewerRenderers}
        style={{ 
          height: '100%', 
          width: '100%'
        }}
        config={{
          header: {
            disableHeader: true,
            disableFileName: true,
            retainURLParams: true,
          },
          csvDelimiter: ",",
          pdfZoom: { defaultZoom: 0.9, zoomJump: 0.2 },
          pdfVerticalScrollByDefault: true,
        }}
      />
    </Box>
  );
};

export default DocumentiPreview;
