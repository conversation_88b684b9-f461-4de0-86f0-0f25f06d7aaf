
import { useTranslation } from "@1f/react-sdk";
import { <PERSON>, Grid, Divider, Button } from "@vapor/react-material";
import { faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import FormInput from "../../../../../custom-components/FormInput";
import { useFieldArray, useFormContext } from "react-hook-form";

const CatastaliTab = () => {
    const { t } = useTranslation();
    const { control } = useFormContext();

    const { fields, append,  remove } =
        useFieldArray({
            control,
            name: "daticatastali",
        });

        const onAppendClick = () => {
         append({
            sezione: "",
            foglio: "",
            particella: "",
            subparticella: "",
            subalterno: "",
            subalterno2: "",
            graffato: "",
        })
    }

   
    return (
        <Box
            sx={{
                "& .MuiTextField-root": {
                    m: 1,
                    width: 1,
                },
            }}
        >
            {fields.map((field, index: number) => {
                return (
                    <>
                        {" "}
                        <Grid container spacing={2} key={field.id}>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.sezione`}
                                    type="text"
                                    label={t("Sezione")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.foglio`}
                                    type="text"
                                    label={t("Foglio")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.particella`}
                                    type="text"
                                    label={t("Particella")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.subparticella`}
                                    type="text"
                                    label={t("Subparticella")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.subalterno`}
                                    type="text"
                                    label={t("Subalterno")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.subalterno2`}
                                    type="text"
                                    label={t("Subalterno2")}
                                />
                            </Grid>
                            <Grid item xs={12} md={12}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`daticatastali.${index}.graffato`}
                                    type="text"
                                    label={t("Graffato")}
                                />
                            </Grid>
                            {index > 0 && (
                                <>
                                    <Grid
                                        item
                                        xs={12}
                                        md={12}
                                        sx={{
                                            display: "flex",
                                            justifyContent: "flex-end",
                                        }}
                                    >
                                        <Button
                                            color="error"
                                            startIcon={
                                                <FontAwesomeIcon
                                                    style={{ color: "error" }}
                                                    icon={faTrashCan}
                                                />
                                            }
                                            onClick={() => remove(index)}
                                        >
                                            {t("Elimina")}
                                        </Button>
                                    </Grid>
                                </>
                            )}
                        </Grid>
                        <Divider
                            className="MuiDivider-VaporLight"
                            sx={{ width: "102%", mt: 2 }}
                        />
                    </>
                );
            })}

            <Box sx={{ marginBottom: 2 }}>
                <Button
                    startIcon={
                        <FontAwesomeIcon
                            style={{
                                color: "#008FD6",
                            }}
                            icon={faCirclePlus}
                        />
                    }
                    onClick={onAppendClick}
                    sx={{ marginTop: 2 }}
                >
                    {t("Aggiungi catastali")}
                </Button>
            </Box>
        </Box>
    );
};

export default CatastaliTab;
