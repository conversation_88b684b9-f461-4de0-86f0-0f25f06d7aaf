import { useTranslation } from "@1f/react-sdk";
import { Box, FormGroup, Grid, Divider, Button, TextField } from "@vapor/react-material";
import { faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useFieldArray, useFormContext } from "react-hook-form";
import FormInput from "../../../../../custom-components/FormInput";
import { ControlledCheckbox } from "../../../../../custom-components/CheckBox";
import CustomAutocomplete from "../../../../../custom-components/CustomAutocomplete";

interface Person {
    value: number | string;
    label: string;
}

const TitolaritaTab = (props: any) => {
    const { idSelected, anag_id, propertyRights, personsList } = props;
    const { t } = useTranslation();

    const { control, setValue } = useFormContext();

    const { fields, append, remove } = useFieldArray({
        control,
        name: "anagrafica"
    });

    const onAppendClick = () => {
        const defaultPropertyRight = propertyRights.length > 0 ? propertyRights[0].value : "";

        append({
            anag_id: "",
            nome_id: "",
            nome: "",
            diritto: defaultPropertyRight,
            quota: "0.00",
            pignorato: false
        });
    };

    return (
        <Box
            component="div"
            sx={{
                "& .MuiTextField-root": {
                    m: 1,
                    width: 1
                }
            }}
        >
            {idSelected === "create" && fields.length === 0 && (
                <div key={1}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={12}>
                            <CustomAutocomplete
                                key={1}
                                fullWidth
                                options={personsList || []}
                                value={
                                    personsList.find((item: any) => item.value === anag_id) || {
                                        value: anag_id,
                                        label: personsList.find((item: any) => item.value === anag_id)?.label ?? anag_id.toString()
                                    }
                                }
                                isOptionEqualToValue={(option: any, value: any) => option.value === value?.value}
                                selectOnFocus
                                disabled
                                clearOnBlur={false}
                                limitTags={1}
                                noOptionsText={t("Nessuna opzione")}
                                loadingText={t("Caricamento...")}
                                onChange={(_: unknown, value: any) => {
                                    setValue(`anagrafica.0.anag_id`, value?.value || "");
                                    setValue(`anagrafica.0.nome_id`, value?.value || "");
                                    setValue(`anagrafica.0.nome`, value?.label || "");
                                }}
                                getOptionLabel={(option: any) => option?.label ?? ""}
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        label={t("Anagrafica")}
                                        InputProps={{
                                            ...params.InputProps,
                                            sx: {
                                                height: "30px"
                                            }
                                        }}
                                    />
                                )}
                            />
                        </Grid>
                        <Grid item xs={6} md={6}>
                            <FormInput fullWidth control={control} options={propertyRights} name={`anagrafica.0.diritto`} type="select" label={t("Tipologia diritto")} />
                        </Grid>
                        <Grid item xs={6} md={6} sx={{mt: 1}}>
                            <FormInput  fullWidth control={control} name={`anagrafica.0.quota`} type="text" label={t("Quota diritto")} formatNumber={true} />
                        </Grid>

                        <Grid item xs={6} md={6}>
                            <FormGroup>
                                <ControlledCheckbox control={control} name={`anagrafica.0.pignorato`} label={t("Pignorato")} style={{ ml: 1 }} />
                            </FormGroup>
                        </Grid>
                    </Grid>

                    <Divider className="MuiDivider-VaporLight" sx={{ width: "102%", mt: 2 }} />
                </div>
            )}
            {fields.map((field: any, index: number) => {
                return (
                    <div key={field.id}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} md={12}>
                                <CustomAutocomplete
                                    key={index}
                                    fullWidth
                                    options={personsList || []}
                                    value={field.anag_id ? personsList.find((item: any) => item.value === field.anag_id) || null : null}
                                    isOptionEqualToValue={(option: any, value: any) => option?.value === value?.value}
                                    selectOnFocus
                                    disabled={index === 0}
                                    clearOnBlur={false}
                                    limitTags={1}
                                    noOptionsText={t("Nessuna opzione")}
                                    loadingText={t("Caricamento...")}
                                    onChange={(_: unknown, value: Person | null) => {
                                        setValue(`anagrafica.${index}.anag_id`, value?.value || "");
                                        setValue(`anagrafica.${index}.nome`, value?.label || "");
                                        setValue(`anagrafica.${index}.nome_id`, value?.value || "");
                                    }}
                                    getOptionLabel={(option: any) => option?.label ?? ""}
                                    renderInput={(params: any) => (
                                        <TextField
                                            {...params}
                                            label={t("Anagrafica")}
                                            InputProps={{
                                                ...params.InputProps,
                                                sx: {
                                                    height: "30px"
                                                }
                                            }}
                                        />
                                    )}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput fullWidth control={control} options={propertyRights} name={`anagrafica.${index}.diritto`} type="select" label={t("Tipologia diritto")} />
                            </Grid>
                            <Grid item xs={6} md={6} sx={{mt: 1}}>
                                <FormInput fullWidth control={control} name={`anagrafica.${index}.quota`} type="text" label={t("Quota diritto")} formatNumber={true} />
                            </Grid>

                            <Grid item xs={6} md={6}>
                                <FormGroup>
                                    <ControlledCheckbox control={control} name={`anagrafica.${index}.pignorato`} label={t("Pignorato")} style={{ ml: 1 }} />
                                </FormGroup>
                            </Grid>
                            {index > 0 && (
                                <>
                                    <Grid
                                        item
                                        xs={6}
                                        md={6}
                                        sx={{
                                            display: "flex",
                                            justifyContent: "flex-end"
                                        }}
                                    >
                                        <Button color="error" startIcon={<FontAwesomeIcon style={{ color: "error" }} icon={faTrashCan} />} onClick={() => remove(index)}>
                                            {t("Elimina")}
                                        </Button>
                                    </Grid>
                                </>
                            )}
                        </Grid>

                        <Divider className="MuiDivider-VaporLight" sx={{ width: "102%", mt: 2 }} />
                    </div>
                );
            })}

            <Box sx={{ marginBottom: 2 }}>
                <Button
                    startIcon={
                        <FontAwesomeIcon
                            style={{
                                color: "#008FD6"
                            }}
                            icon={faCirclePlus}
                        />
                    }
                    sx={{ marginTop: 2 }}
                    onClick={onAppendClick}
                >
                    {t("Aggiungi titolarità")}
                </Button>
            </Box>
        </Box>
    );
};

export default TitolaritaTab;
