import { useTranslation } from "@1f/react-sdk";
import { Box, Checkbox, FormControlLabel, FormGroup, Grid } from "@vapor/react-material";
import { useFormContext } from "react-hook-form";
import FormInput from "../../../../../custom-components/FormInput";
import { useAnagraficheProvider } from "../../../providers/AnagraficheProvider";

const ImmobileTab = () => {
    const { t } = useTranslation();
    const { anagrafiche } = useAnagraficheProvider();
    const { watch ,setValue} = useFormContext();

    const classato = watch("classato");

    const { control } = useFormContext();

    const filteredClasses = anagrafiche?.classes
    .filter((item: any) => {
        if (classato === "1" || classato === true) {
            return item.id >= 0 && item.id < 46;
        } else {
            return item.id >= 46 && item.id <= 79;
        }
    })
    .map((classe: any) => ({
        value: classe.id,
        label: `${classe.valore} - ${classe.descrizione}`,
    }));


    const onChangeClassato = (e: any) => {
        setValue("classato", e.target.checked);
        setValue("classe", e.target.checked ? "1" : "46");
    }

    return (
        <Box>
            <Grid container spacing={2}>
                <Grid item xs={12} md={12}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="descrizione"
                        type="text"
                        label={t("Descrizione")}
                    />
                </Grid>
                <Grid item xs={12} md={12}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="valore"
                        type="text"
                        label={t("Valore")}
                        formatNumber={true}
                    />
                </Grid>
                <Grid item xs={12} md={12}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="indirizzo"
                        type="text"
                        label={t("Indirizzo")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="civico"
                        type="text"
                        label={t("Civico")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="citta"
                        type="select"
                        options={anagrafiche?.cities.map((city: any) => ({
                            value: city.id,
                            label: city.nome,
                        }))}
                        label={t("Città")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="cap"
                        type="text"
                        label={t("Cap")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="prov"
                        disabled
                        type="text"
                        label={t("Provincia")}
                    />
                </Grid>
                <Grid item xs={12} md={12}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="nazione"
                        type="select"
                        label={t("Nazione")}
                        options={anagrafiche?.countries.map((country: any) => ({
                            value: country.id,
                            label: country.nome,
                        }))}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="tipocatasto"
                        type="select"
                        options={[
                            { value: "1", label: t("Edilizio Urbano") },
                            { value: "2", label: t("Terreni") },
                        ]}
                        label={t("Tipo Catasto")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="classe"
                        type="select"
                        options={filteredClasses}
                        label={t("Classe")}
                    />
                </Grid>
                <Grid item xs={12} md={12}>
                    <FormGroup>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={
                                        classato === "1" || classato === true
                                    }
                                    onChange={onChangeClassato}
                                />
                    }
                            label={t("Immobile Classato")}
                        />
                    </FormGroup>
                </Grid>
                <Grid item xs={6} md={6} sx={{ pl: 2 }}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="unitamisura"
                        type="select"
                        label={t("Unità di misura")}
                        options={[
                            { value: "MQ", label: t("Metri quadri") },
                            { value: "Ha", label: t("Ettarii") },
                            { value: "AR", label: t("Are") },
                            { value: "CA", label: t("Centiare") },
                        ]}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="superficie"
                        type="text"
                        label={t("Superficie")}
                        formatNumber={true}
                    />
                </Grid>
                <Grid item xs={12} md={12}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="edificio"
                        type="text"
                        label={t("Edificio")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="scala"
                        type="text"
                        label={t("Scala")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="vani"
                        type="text"
                        label={t("Vani")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="interno"
                        type="text"
                        label={t("Interno")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="piani"
                        type="text"
                        label={t("Piano")}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="renditadomenicale"
                        type="text"
                        label={t("Rendita Domenicale")}
                        formatNumber={true}
                    />
                </Grid>
                <Grid item xs={6} md={6}>
                    <FormInput
                        fullWidth
                        control={control}
                        name="renditaagraria"
                        type="text"
                        label={t("Rendita Agraria")}
                        formatNumber={true}
                    />
                </Grid>
            </Grid>
        </Box>
    );
};

export default ImmobileTab;
