import React, { useState } from "react";
import { <PERSON>, Button, Typography } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faHouse } from "@fortawesome/pro-regular-svg-icons";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import Spinner from "../../../../../custom-components/Spinner";
import { useImmobiliHook } from "../../../hooks/useImmobiliHook";
import { GridCallbackDetails, GridPaginationModel } from "@mui/x-data-grid-pro";
import { useTranslation } from "@1f/react-sdk";
import { ImmobiliActions } from "../../../../../interfaces/immobili.interface";
import CreateUpdateImmobili from "./CreateUpdateImmobili";
import usePostCustom from "../../../../../hooks/usePostCustom";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";

export default function Immobili() {
    const { t } = useTranslation();

    const [openCreateUpdateImmobili, setOpenCreateUpdateImmobili] =
        useState(false);
    const [showModalDelete, setShowModalDelete] = useState<boolean>(false);
    const [rowToDelete, setRowToDelete] = useState<any>(null);

    const [idSelected, setIdSelected] = React.useState("");

    const deleteRequest = usePostCustom(
        "/peopleproperties/deleteproperty?noTemplateVars=true"
    );

    const handleConfirmDeletion = async () => {
        if (!rowToDelete) return;
        
        const formData = new FormData();
        formData.append("id", rowToDelete.id);
        const response: any = await deleteRequest.doFetch(true, formData);
        
        if (response.data) {
            setShowModalDelete(false);
            fetchData();
        }
    };

    const deleteRow = (row: any) => { 
        setRowToDelete(row);
        setShowModalDelete(true);
    }
    const handleEditRow = async (row: any) => {
        setIdSelected(row.id);
        setOpenCreateUpdateImmobili(true);
    };

    const actions: ImmobiliActions = {
        handleDelete: deleteRow,
        handleEdit: handleEditRow,
    };
    const { immobiliQuery, setImmobiliQuery, loading, list, fetchData } =
        useImmobiliHook(actions);

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setImmobiliQuery({
            ...immobiliQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const navigateToNewImmobili = () => {
        setIdSelected("create");
        setOpenCreateUpdateImmobili(!openCreateUpdateImmobili);
    };

    const closeDeleteConfirmation = () => {
        setShowModalDelete(false);
        setRowToDelete(null);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="immobili"
                columns={list.columns}
                data={list?.rows || []}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                onPageChangeCallback={onPageChangeCallback}
                query={immobiliQuery}
                setQuery={setImmobiliQuery}
                onClickKey="id"
                onClickCheckboxKey="id"
            />
        );
    };

    return (
        <Box>
              <ConfirmModal
                    open={showModalDelete}
                    handleDecline={closeDeleteConfirmation}
                    handleAgree={handleConfirmDeletion}
                    colorConfirmButton="error"
                    decline={t("Annulla")}
                    agree={t("Conferma")}
                    confirmText={t(
                        "Eliminare definitivamente l'immobile?"
                    )}
                    title={t("Vuoi continuare?")}
                />
                {openCreateUpdateImmobili && <CreateUpdateImmobili
                    openDrawer={openCreateUpdateImmobili}
                    setOpenDrawer={(close:boolean) => setOpenCreateUpdateImmobili(close)}
                    idSelected={idSelected}
                    setIdSelected={setIdSelected}
                    fetchData={fetchData}
                />}      
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                    }}
                >
                    <Box sx={{ display: "flex", gap: 1 }}>
                        <FontAwesomeIcon fontSize={'24px'} icon={faHouse} />
                        <Typography
                            variant="titleMedium"
                            color="primary.main"
                            gutterBottom
                            sx={{ marginLeft: '15px' }}
                        >
                            {t("Immobili")}
                        </Typography>
                    </Box>

                    <Box sx={{ display: "flex", gap: 1 }}>
                        <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            sx={{ textTransform: "none" }}
                            onClick={navigateToNewImmobili}
                        >
                            {t("Nuovo Immobile")}
                        </Button>
                    </Box>
                </Box>
            <Box sx={{ pt: 10 }}>{renderDataTable()}</Box>
            
        </Box>
    );
}
