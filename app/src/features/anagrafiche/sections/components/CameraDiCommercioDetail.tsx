import { Typo<PERSON>, Grid, Button, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";
import moment from "moment";

interface ICameraDiCommercio {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
}

export default function CameraDiCommercioDetail(props: ICameraDiCommercio) {
    const { showCreateForm, setShowCreateForm } = props;
    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { isInitialized, anagrafiche } = useAnagraficheProvider();

    const { customer, cities, valute } = anagrafiche;

    if (!isInitialized) {
        return <Spinner />;
    }

    const comuneName = cities.find((city: any) => city.id === customer.comune);
    const valuteName = valute.find((valuta: any) => valuta.id === customer.valuta);

    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader
                        icon={faIdCard}
                        title={t("Camera di commercio")}
                    />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    cameraDiCommercio: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>

            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                <Grid item xs={6}>
                    <Typography
                        variant="titleSmall"
                        component="div"
                        color="primary.textTitleColor"
                        gutterBottom
                        style={{
                            marginTop: "20px",
                        }}
                    >
                        {t("Camera di commercio")}
                    </Typography>
                </Grid>
            </Grid>

            <Box>
                <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                    <Grid item xs={5}>
                        <CleanLabel>{t(" Comune")}:</CleanLabel>
                        <BoldValue>{comuneName?.nome || "-"}</BoldValue>
                    </Grid>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Provincia")}:</CleanLabel>
                        <BoldValue>{customer.provincia || "-"}</BoldValue>
                    </Grid>
                </Grid>
                <Grid container spacing={2}>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Sezione")}:</CleanLabel>
                        <BoldValue>{customer.sezione || "-"}</BoldValue>
                    </Grid>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Dati Iscrizione")}:</CleanLabel>
                        <BoldValue>{customer.dataiscrizione ? moment(customer.dataiscrizione).format("DD/MM/YYYY") : "-"}</BoldValue>
                    </Grid>

                    <Grid item xs={5}>
                        <CleanLabel>{t("Numero REA")}:</CleanLabel>
                        <BoldValue>{customer.nrea || "-"}</BoldValue>
                    </Grid>
                </Grid>
            </Box>

            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                <Grid item xs={6}>
                    <Typography
                        variant="titleSmall"
                        component="div"
                        color="primary.textTitleColor"
                        gutterBottom
                        style={{
                            marginTop: "20px",
                        }}
                    >
                        {t("Capitale sociale")}
                    </Typography>
                </Grid>
            </Grid>

            <Box>
                <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                    <Grid item xs={5}>
                        <CleanLabel>{t(" Deliberato")}:</CleanLabel>
                        <BoldValue>{customer.deliberato?.toString().replace(".", ",") || "-"}</BoldValue>
                    </Grid>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Sottoscritto")}:</CleanLabel>
                        <BoldValue>{customer.sottoscritto?.toString().replace(".", ",") || "-"}</BoldValue>
                    </Grid>
                </Grid>
                <Grid container spacing={2}>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Versato")}:</CleanLabel>
                        <BoldValue>{customer.versato?.toString().replace(".", ",") || "-"}</BoldValue>
                    </Grid>
                    <Grid item xs={5}>
                        <CleanLabel>{t("Valuta")}:</CleanLabel>
                        <BoldValue>{valuteName?.nome || "-"}</BoldValue>
                    </Grid>
                </Grid>
            </Box>
        </>
    );
}
