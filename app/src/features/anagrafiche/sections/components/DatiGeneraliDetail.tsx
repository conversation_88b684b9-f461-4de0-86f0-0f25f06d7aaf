import { Typo<PERSON>, Grid, Box, Button } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";
import { useEffect } from "react";

interface IDatiGenerali {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
    isPersonaFisica: boolean;
    hasError: boolean;
}

export default function DatiGeneraliDetail(props: IDatiGenerali) {
    const { showCreateForm, setShowCreateForm, isPersonaFisica, hasError } = props;
    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;

    if (!isInitialized) {
        return <Spinner />;
    }



    useEffect(() => {
        if(hasError){
            setShowCreateForm({
                ...showCreateForm,
                datiGenerali: true,
                fiscaliBanche: true,
                noteProfilazione: true,
            })
        }
    }, [hasError]);

    return (
        <div id="1">
            <Grid
                container
                justifyContent="space-between"
                alignItems="center"
                sx={{ paddingTop: "10px" }}
            >
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader icon={faIdCard} title={t("Dati generali")} hasError={hasError} />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    datiGenerali: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>

            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                <Grid item xs={6}>
                    <Typography
                        variant="titleSmall"
                        component="div"
                        color="primary.textTitleColor"
                        gutterBottom
                        style={{
                            marginTop: "20px",
                        }}
                    >
                        {customer.tipo === "Società"
                            ? t("Dati società")
                            : t("Dati soggetto")}
                    </Typography>
                </Grid>
            </Grid>
            {!isPersonaFisica ? (
                <Box>
                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={6}>
                            <CleanLabel>{t("Denominazione")}:</CleanLabel>
                            <BoldValue>{customer.nome}</BoldValue>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2}>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Codice fiscale")}:</CleanLabel>
                            <BoldValue>
                                {customer.codicefiscale
                                    ? customer.codicefiscale
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Partita iva")}:</CleanLabel>
                            <BoldValue>
                                {customer.partitaiva
                                    ? customer.partitaiva
                                    : "-"}
                            </BoldValue>
                        </Grid>
                    </Grid>
                </Box>
            ) : (
                <Box>
                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={6}>
                            <CleanLabel>{t(" Titolo")}:</CleanLabel>
                            <BoldValue>{customer.titolo}</BoldValue>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2}>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Nome")}:</CleanLabel>
                            <BoldValue>
                                {customer.nome_soggetto
                                    ? customer.nome_soggetto
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Cognome")}:</CleanLabel>
                            <BoldValue>
                                {customer.cognome_soggetto
                                    ? customer.cognome_soggetto
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Sesso")}:</CleanLabel>
                            <BoldValue>
                                {customer.sesso === "M"
                                    ? t(" Maschio")
                                    : t("Femmina")}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}></Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Luogo di nascita")}:</CleanLabel>
                            <BoldValue>
                                {customer.luogo_nascita
                                    ? customer.luogo_nascita
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Data di nascita")}:</CleanLabel>
                            <BoldValue>
                                {customer.data_nascita
                                    ? customer.data_nascita
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Codice fiscale")}:</CleanLabel>
                            <BoldValue>
                                {customer.codicefiscale
                                    ? customer.codicefiscale
                                    : "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Partita iva")}:</CleanLabel>
                            <BoldValue>
                                {customer.partitaiva
                                    ? customer.partitaiva
                                    : "-"}
                            </BoldValue>
                        </Grid>
                    </Grid>
                </Box>
            )}
        </div>
    );
}
