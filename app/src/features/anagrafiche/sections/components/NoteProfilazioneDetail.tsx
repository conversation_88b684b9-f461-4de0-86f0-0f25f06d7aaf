import { Typo<PERSON>, <PERSON>rid, Button, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useConfigs } from "../../../../store/ConfigStore";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";

interface INoteProfilazione {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
}

export default function NoteProfilazioneDetail(props: INoteProfilazione) {
    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { configs }: any = useConfigs();
    const displayCategoria = configs?.data?.app?.classificatore_bool || false;

    const { showCreateForm, setShowCreateForm } = props;

    const { isInitialized, anagrafiche } = useAnagraficheProvider();

    const { customer, categoria, tags } = anagrafiche;
    const tagsObject = JSON.parse(tags);
    const joinedDescriptions = tagsObject.map((item: any) => item.description).join(", ");

    if (!isInitialized) {
        return <Spinner />;
    }

    const shouldShowConditionalSections = modules?.provisioningRow?.subscription_type != 37;

    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader icon={faIdCard} title={t("Note e profilazione")} />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6"
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    noteProfilazione: true
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>
            <Box style={{ marginTop: "16px" }}>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <CleanLabel>{t("Note")}:</CleanLabel>
                        <BoldValue>{customer.annotazioni || "-"}</BoldValue>
                    </Grid>
                </Grid>
            </Box>

            {shouldShowConditionalSections && (
                <>
                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={6}>
                            <Typography
                                variant="subtitle"
                                component="div"
                                color="primary.textTitleColor"
                                gutterBottom
                                style={{
                                    marginTop: "20px"
                                }}
                            >
                                {t("Profilazione")}
                            </Typography>
                        </Grid>
                    </Grid>

                    <Box style={{ marginBottom: "16px" }}>
                        <Grid container spacing={2}>
                            {displayCategoria && (
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Categoria")}:</CleanLabel>
                                    <BoldValue>{categoria || "-"}</BoldValue>
                                </Grid>
                            )}

                            <Grid item xs={5}>
                                <CleanLabel>{t("Codice Esterno")}:</CleanLabel>
                                <BoldValue>{customer.external_sw_id || "-"}</BoldValue>
                            </Grid>
                            <Grid item xs={12}>
                                <CleanLabel>{t("Etichette")}:</CleanLabel>
                                <BoldValue>{joinedDescriptions}</BoldValue>
                            </Grid>
                            <Grid item xs={12}>
                                <CleanLabel>{t("Formula Saluti")}:</CleanLabel>
                                <BoldValue>{customer.formula_saluti || t("Spett.le")}</BoldValue>
                            </Grid>
                        </Grid>
                    </Box>
                </>
            )}
        </>
    );
}
