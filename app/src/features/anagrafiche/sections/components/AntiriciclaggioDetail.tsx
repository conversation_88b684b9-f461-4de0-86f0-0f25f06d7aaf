import { Typo<PERSON>, <PERSON>rid, Button, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useConfigs } from "../../../../store/ConfigStore";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";

interface IAntiriciclaggio {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
}

export default function AntiriciclaggioDetail(props: IAntiriciclaggio) {
    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { configs }: any = useConfigs();
    const displayFields = configs?.data?.app?.aui_bool || false;
    const { showCreateForm, setShowCreateForm } = props;
    const { isInitialized, anagrafiche } = useAnagraficheProvider();


    const { customer } = anagrafiche;

    const autoritaRilascio = () => {
        const lookup: any = {
            1: `Comune di ${customer?.aui?.autorita_luogo}` || "",
            2: `Ministero dell'interno`,
            3: `Prefettura di ${customer?.aui?.autorita_luogo}` || "",
            4: `Questura di ${customer?.aui?.autorita_luogo}` || "",
            5: `Ministero Esteri`,
        };

        return lookup[customer?.aui?.autorita_rilascio] || `-`;
    };

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader
                        icon={faIdCard}
                        title={t("Antiriciclaggio")}
                    />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    antiriciclaggio: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>
            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                <Grid item xs={6}>
                    <Typography
                        variant="titleSmall"
                        component="div"
                        color="primary.textTitleColor"
                        gutterBottom
                        style={{
                            marginTop: "20px",
                        }}
                    >
                        {t("Archivio Unico Informatico")}
                    </Typography>
                </Grid>
            </Grid>
            {displayFields && (
                <Box key={0} style={{ marginBottom: "16px" }}>
                    <Grid container spacing={2}>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Identificazione")}:</CleanLabel>
                            <BoldValue>
                                {customer.aui?.tipo_identificazione || "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Tipologia Documento")}:</CleanLabel>
                            <BoldValue>
                                {customer.aui?.tipo_documento || "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Numero documento")}:</CleanLabel>
                            <BoldValue>
                                {customer.aui?.num_documento || "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Dati di rilascio")}:</CleanLabel>
                            <BoldValue>
                                {customer.aui?.data_rilascio || "-"}
                            </BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>
                                {t("Autorita di rilascio")}:
                            </CleanLabel>
                            <BoldValue>{autoritaRilascio()}</BoldValue>
                        </Grid>
                        <Grid item xs={5}>
                            <CleanLabel>{t("Professione")}:</CleanLabel>
                            <BoldValue>
                                {customer.aui?.professione || "-"}
                            </BoldValue>
                        </Grid>
                    </Grid>
                </Box>
            )}
        </>
    );
}
