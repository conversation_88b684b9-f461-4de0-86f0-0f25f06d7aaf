import { <PERSON>rid, <PERSON><PERSON>, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";

interface ICampiDinamici {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
}

export default function CampiDinamiciDetail(props: ICampiDinamici) {
    const { t } = useTranslation();
    const { user, modules }: any = useUser();

    const { showCreateForm, setShowCreateForm } = props;
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { preview_campi } = anagrafiche;


   

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
         <div id="8">
            <Grid container justifyContent="space-between" alignItems="center" sx={{ marginBottom: "15px" }}>
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader
                        icon={faIdCard}
                        title={t("Campi Dinamici")}
                    />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    campiDinamici: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
                </Grid>
                {preview_campi && Array.isArray(preview_campi) && preview_campi.map((campo, index) => {                
                    return (
                        <Box key={index} style={{ marginBottom: "16px" }}>
                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    <CleanLabel>{campo.nome}:</CleanLabel>
                                    <BoldValue>
                                        {campo.tipo === 'checkbox' 
                                            ? (campo.valore === 1 || 
                                               campo.valore === '1' || 
                                               campo.valore === true || 
                                               campo.valore === 'true' ||
                                               campo.valore === 'on' ? t("Si") : t("No"))
                                            : campo.tipo === 'select'
                                            ? (campo.select || "-")
                                            : (campo.valore || "-")
                                        }
                                    </BoldValue>
                                </Grid>
                            </Grid>
                        </Box>
                    );
                })}
            </div>
        </>
    );
}
