import { Typo<PERSON>, <PERSON>rid, <PERSON><PERSON>, Box } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";

interface IRecapiti {
    showCreateForm: any;
    setShowCreateForm: any;
}

export default function RecapitiDetail(props: IRecapiti) {
    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { showCreateForm, setShowCreateForm }: any = props;
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { addresses, contacts, countries } = anagrafiche;

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <FontAwesomeIcon
                        size="lg"
                        style={{
                            color: "primary.main",
                            marginRight: "8px",
                            marginTop: "8px",
                        }}
                        icon={faIdCard}
                    />
                    <Typography
                        variant="titleMedium"
                        color="primary.main"
                        gutterBottom
                    >
                        {t("Recapiti")}
                    </Typography>
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    recapiti: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>
            {Object.keys(addresses).map((key: string) => {
                const nazione = countries.find(
                    (item: any) => item.id === addresses[key]?.nazione
                );
                return (
                    <div key={key}>
                        <Grid
                            container
                            spacing={2}
                            sx={{ marginBottom: "15px" }}
                        >
                            <Grid item xs={5}>
                                <Typography
                                    variant="titleSmall"
                                    component="div"
                                    color="primary.textTitleColor"
                                    gutterBottom
                                    style={{
                                        marginTop: "20px",
                                    }}
                                >
                                    {key}
                                </Typography>
                            </Grid>
                        </Grid>

                        <Box key={0} style={{ marginBottom: "16px" }}>
                            <Grid container spacing={2}>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Comune")}:</CleanLabel>
                                    <BoldValue>
                                        {addresses[key].citta?.nome || "-"}
                                    </BoldValue>
                                </Grid>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Provincia")}:</CleanLabel>
                                    <BoldValue>
                                        {addresses[key].citta?.provincia || "-"}
                                    </BoldValue>
                                </Grid>
                                <Grid item xs={12}>
                                    <CleanLabel>{t("Indirizzo")}:</CleanLabel>
                                    <BoldValue>
                                        {addresses[key].via || "-"}
                                    </BoldValue>
                                </Grid>

                                <Grid item xs={5}>
                                    <CleanLabel>{t("CAP")}:</CleanLabel>
                                    <BoldValue>
                                        {addresses[key].cap || "-"}
                                    </BoldValue>
                                </Grid>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Nazione")}:</CleanLabel>
                                    <BoldValue>
                                        {nazione?.nome || "-"}
                                    </BoldValue>
                                </Grid>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Regione")}:</CleanLabel>
                                    <BoldValue>
                                        {addresses[key]?.regione || "-"}
                                    </BoldValue>
                                </Grid>
                            </Grid>
                        </Box>
                    </div>
                );
            })}

            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                {Object.entries(contacts).map(([key, value], index: number) => (
                    <Grid item xs={12} key={key}>
                        <Typography
                            variant="titleSmall"
                            component="div"
                            color="primary.textTitleColor"
                            gutterBottom
                            style={{
                                marginTop: "20px",
                            }}
                        >
                        {t(`Contatto ${index + 1}`)} - {key}
                        </Typography>

                        <Typography variant="bodyLarge500">
                            {value !== undefined && value !== null
                                ? String(value)
                                : "-"}
                        </Typography>
                    </Grid>
                ))}
            </Grid>
        </>
    );
}
