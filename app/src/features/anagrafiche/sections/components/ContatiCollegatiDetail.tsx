import { Typo<PERSON>, <PERSON>rid, <PERSON><PERSON>, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";
import moment from "moment";

interface IContattiCollegati {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
    contactData: any;
    isInitialized: boolean;
}

export default function ContattiCollegati(props: IContattiCollegati) {
    const { showCreateForm, setShowCreateForm, contactData, isInitialized } =
        props;
    const { t } = useTranslation();
    const { user, modules }: any = useUser();

    const formatDateRange = (dal: string, al: string) => {
        const from = dal ? moment(new Date(dal), "DD/MM/YYYY") : null;
        const to = al ? moment(new Date(al), "DD/MM/YYYY") : null;

        if (from && to) {
            return `${from.format("DD/MM/YYYY")} - ${to.format("DD/MM/YYYY")}`;
        } else if (from) {
            return from.format("DD/MM/YYYY");
        } else if (to) {
            return to.format("DD/MM/YYYY"); 
        }

        return "";
    };
    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader
                        icon={faIdCard}
                        title={t("Contatti Collegati")}
                    />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6",
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    contactiCollegati: true,
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>
            {modules && modules.canModifyAnagrafiche(user) && (
                <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                    {(contactData || [])?.map(
                        (
                            {
                                child,
                                relazione,
                                socio,
                                dal,
                                al,
                                percentuale_partecipazione,
                                amministratore,
                            }: any,
                            index: number
                        ) => (
                            <Grid item xs={12} marginTop={2} key={index}>
                                {(contactData || [])?.length > 1 && (
                                    <Typography
                                        component="div"
                                        color="primary.textTitleColor"
                                        gutterBottom
                                        style={{ marginTop: "20px" }}
                                    >
                                        {t("Contatti collegato")} {index + 1}
                                    </Typography>
                                )}
                                <BoldValue>
                                    {child} - {relazione}
                                </BoldValue>
                                <Typography style={{ whiteSpace: "pre" }}>
                                    {socio === "1" && "Socio "}
                                    {amministratore === "1" && "Amministratore"}
                                    {formatDateRange(dal, al) && (
                                        <Box
                                            component="span"
                                            sx={{
                                                ml:
                                                    socio === "1" ||
                                                    amministratore === "1"
                                                        ? 2
                                                        : 0,
                                            }}
                                        >
                                            {formatDateRange(dal, al)}
                                        </Box>
                                    )}
                                    {percentuale_partecipazione && (
                                        <Box
                                            component="span"
                                            sx={{
                                                ml:
                                                    socio === "1" ||
                                                    amministratore === "1" ||
                                                    formatDateRange(dal, al)
                                                        ? 2
                                                        : 0,
                                            }}
                                        >
                                            {`${percentuale_partecipazione}%`}
                                        </Box>
                                    )}
                                </Typography>
                            </Grid>
                        )
                    )}
                </Grid>
            )}
        </>
    );
}
