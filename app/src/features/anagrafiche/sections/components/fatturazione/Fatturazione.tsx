import { useState } from "react";
import { GridPaginationModel, GridRowSelectionModel } from "@mui/x-data-grid-pro";
import { Typo<PERSON>, Box, Button, Menu, MenuItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../../custom-components/CustomDataGrid";
import { useFatturazioneHook } from "../../../hooks/useFatturazioneHook";
import { useNavigate } from "react-router-dom";
import { faFileInvoiceDollar } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Filters from "./Filters"
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";

const defaultQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    emittenteSearch: -1,
    statusSearch: -1,
    startDateSearch: "",
    endDateSearch: "",
}

const Fatturazione = ({ anagrafiche, id }: any) => {
    const [query, setQuery] = useState<any>({
        ...defaultQuery,
        peopleId: id,
        tipologia: "preavvisiDiParcella"
    });

    const navigate = useNavigate();
    const { t } = useTranslation();
    const { columns, list, loading,feeStatus } = useFatturazioneHook(query);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleClickMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };


    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery((prev: any) => ({ ...prev, page: model.page, pageSize: model.pageSize }));
    };

    const handleAddNew = () => {
        const fattureNavigate = handleCreateFattureNavigation()
        navigate(`/legacy${fattureNavigate}`)
    };

    const fattureTableName = () => {
        if (query?.tipologia == "preavvisiDiParcella") {
            return "peopleprefees"
        } else if (query?.tipologia == "fatture") {
            return "peoplestandardfees"
        } else if (query?.tipologia == "fattureElettroniche") {
            return "peoplefees"
        } else {
            return "peoplecreditnotes"
        }
    }

     const handleClickCallback = (uniqueid: any) => {
        if(query?.tipologia == "preavvisiDiParcella"){
            navigate(`/legacy/peopleprefees/viewfe?uniqueid=${uniqueid}&people=${id}&feeType=2&disabledBollo=false`);
        } else if(query?.tipologia == "fatture"){
            navigate(`/legacy/peoplestandardfees/viewfe?uniqueid=${uniqueid}&people=${id}&feeType=0&disabledBollo=false`);
        } else if(query?.tipologia == "fattureElettroniche"){ 
            navigate(`/legacy/peoplefees/viewfe?uniqueid=${uniqueid}&people=${id}&feeType=6&disabledBollo=false`);
        } else if(query?.tipologia == "noteDiCreditoElettroniche"){
            navigate(`/legacy/peoplecreditnotes/viewfe?uniqueid=${uniqueid}&people=${id}&feeType=7&disabledBollo=false`);
        }
    };

    const renderDataTable = () => (
        <CustomDataGrid
            name={fattureTableName()}
            columns={columns}
            data={list?.currentPage}
            page={query.page}
            totalRows={list?.totalRows}
            pageSize={query.pageSize}
            loading={loading}
            query={query}
            setQuery={setQuery}
            onPageChangeCallback={onPageChangeCallback}
            onClickKey="uniqueid"
            selectableRows
             onRowSelectionModelChange={(
                    newSelection: GridRowSelectionModel
                ) => {
                    setSelectedRows([...newSelection]);
                }}
            onClickCallback={handleClickCallback}
            onClickCheckboxKey="uniqueid"
        />
    );


    const handleCreateFattureNavigation = () => {
        if (query?.tipologia == "preavvisiDiParcella") {
            return `/fatturecomplex/updatefe?feeType=2&people=${id}&disabledBollo=`
        } else if (query?.tipologia == "fatture") {
            return `/fatturecomplex/updatefe?feeType=0&people=${id}&disabledBollo=`
        } else if (query?.tipologia == "fattureElettroniche") {
            return `/fatturecomplex/updatefe?feeType=6&people=${id}&disabledBollo=`
        } else if (query?.tipologia == "noteDiCreditoElettroniche") {
            return `/fatturecomplex/updatefe?feeType=7&people=${id}&disabledBollo=`
        }
    }
    

    const handleAddNewLabel = () => {
    switch (query?.tipologia) {
      case "preavvisiDiParcella":
        return t("Nuovo preavviso di parcella");
      case "fattureElettroniche":
      case "fatture":
        return t("Nuova fattura");
      case "noteDiCreditoElettroniche":
        return t("Nuova nota di credito");
      default:
        return t("Nuovo documento");
    }
  };

  const handleReaggruppaPreavvisi = () => {
    if(selectedRows.length > 1 && query?.tipologia == "preavvisiDiParcella"){
        return true;
    }
    return true;
  };

    return (
        <Box>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <FontAwesomeIcon fontSize={'24px'} icon={faFileInvoiceDollar} />
                    <Typography
                        variant="titleMedium"
                        sx={{ marginLeft: '15px', fontWeight: 500 }}
                    >
                        {t("Fatture")}
                    </Typography>
                </Box>
                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                        variant="outlined"
                        color="primary"
                        size="small"
                        onClick={() => navigate('/legacy/fatture')}
                    >
                        {t("Vai a Fatturazione")}
                    </Button>
                     <div>
                    <Button type="button"  variant="contained"  size="small" aria-haspopup="true" id="basic-button" onClick={handleClickMenu} endIcon={<ArrowDropDownIcon />}>
                        {t("Azioni")}
                    </Button>
                    <Menu
                        id="basic-menu"
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "right"
                        }}
                        transformOrigin={{
                            vertical: "top",
                            horizontal: "right"
                        }}
                    >
                    <MenuItem onClick={handleAddNew}>{handleAddNewLabel()}</MenuItem>
                    <MenuItem disabled={handleReaggruppaPreavvisi()}>{t("Reaggruppa preavvisi")}</MenuItem>
                    </Menu>
                    </div>
                </Box>
            </Box>
            <Box sx={{ pt: 5 }}><Filters query={query} setQuery={setQuery} defaultQuery={defaultQuery} lawyers={anagrafiche?.lawyers} feeStatus={feeStatus} /></Box>
            <Box sx={{ pt: 5 }}>{renderDataTable()}</Box>
        </Box>
    );
}

export default Fatturazione;