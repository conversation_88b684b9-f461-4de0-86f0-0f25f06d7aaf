import { useState, useCallback } from "react";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { Typography, Box, Button } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useDocumentiHook } from "../../hooks/useDocumentiHook";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";

import { faFileUser } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DocumentiPreview from "./DocumentiPreview";
import BaseModal from "../../../../custom-components/BaseModal";
import AnagraficaFileUpload from "./AnagraficaFileUpload";
import AnagraficaFileEdit from "./AnagraficaFileEdit";
import TrasferimentoFilePraticaModal from "./TrasferimentoFilePraticaModal";

interface QueryParams {
    peopleUniqueid: string;
    viewType: string;
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: "asc" | "desc";
}

const Documenti = ({ uniqueid, id }: any) => {
    const [query, setQuery] = useState<QueryParams>({
        peopleUniqueid: uniqueid,
        viewType: "",
        page: 0,
        pageSize: 7,
        sortColumn: "id",
        sortOrder: "asc"
    });
    const [openModal, setOpenModal] = useState<boolean>(false);
    const [loader, setLoader] = useState<boolean>(false);
    const [files, setFiles] = useState<any>([]);
    const [docDescription, setDocDescription] = useState<any>([]);
    const [docDate, setDocDate] = useState<any>([]);
    const [trasferimentoModalOpen, setTrasferimentoModalOpen] = useState<boolean>(false);
    const [selectedFileForTransfer, setSelectedFileForTransfer] = useState<any>(null);

    const { t } = useTranslation();
    const {
        initDocumenti,
        columns,
        list,
        loading,
        previewOpen,
        setPreviewOpen,
        selectedDocumentId,
        setSelectedDocumentId,
        selectedFile,
        setSelectedFile,
        editModalOpen,
        selectedFileData,
        handleSaveEdit,
        handleCloseEdit,
        editLoading,
    } = useDocumentiHook(query, setTrasferimentoModalOpen, setSelectedFileForTransfer);

    const documentiUploadRequest = usePostCustom(`default/anagrafiche/upload-file?id=${id}`);

    const onPageChangeCallback = useCallback((model: GridPaginationModel) => {
        setQuery((prev) => ({ ...prev, page: model.page, pageSize: model.pageSize }));
    }, []);

    // Create a stable setQuery function for the DataGrid
    const stableSetQuery = useCallback((newQuery: any) => {
        setQuery(newQuery);
    }, []);

    const onFileUpload = (filesItems: any) => {
        setFiles((files: any) => [...files, ...filesItems]);
    };
    const onRemove = (fileIndex: any) => {
        setFiles((files: any) => files.filter((_: any, i: any) => i !== fileIndex));
    };

    const handleButtonClick = () => {
        setOpenModal(true);
    };

    const handleUploadFile = async () => {
        try {
            if (!files?.length) {
                return;
            }

            setLoader(true);
            const uploadPromises = files.map(async (file: any, index: number) => {
                const formData = new FormData();
                formData.append("files[]", file);
                formData.append("doc_description", docDescription[index] || "");
                formData.append("doc_date", docDate[index] || "");

                try {
                    const response = await documentiUploadRequest.doFetch(true, formData);
                    return { success: true, file: file.name, response };
                } catch (error) {
                    console.error(t("Errore durante il caricamento del file") + ` ${file.name}:`, error);
                    return { success: false, file: file.name, error };
                }
            });

            await Promise.allSettled(uploadPromises);



            setOpenModal(false);
            setDocDescription([]);
            setDocDate([]);
            setFiles([]);
            initDocumenti();
        } catch (error) {
        } finally {
            setLoader(false);
        }
    };

    const onClickCallback = useCallback((id: string) => {
        const document = list?.currentPage?.find((doc: any) => doc.id === id);
        if (document) {
            setPreviewOpen(true);
            setSelectedDocumentId(id);
            setSelectedFile(document.nomefile);
        }
    }, [list?.currentPage, setPreviewOpen, setSelectedDocumentId, setSelectedFile]);

    const renderDataTable = () => {
        return (
            <CustomDataGrid
            name="peopleuploadedfiles"
            columns={columns}
            data={list?.currentPage}
            page={query.page}
            totalRows={list?.totalRows}
            pageSize={query.pageSize}
            loading={loading}
            query={query}
            setQuery={stableSetQuery}
            onClickCallback={onClickCallback}
            onPageChangeCallback={onPageChangeCallback}
            onClickKey="id"
        />
        );
    };

    const handleDecline = () => {
        setOpenModal(false);
        setDocDescription([]);
        setDocDate([]);
        setFiles([]);
    };

    const handleCloseTrasferimento = useCallback(() => {
        setTrasferimentoModalOpen(false);
        setSelectedFileForTransfer(null);
    }, []);
    return (
        <Box>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between"
                }}
            >
                <Box sx={{ display: "flex", gap: 1 }}>
                    <FontAwesomeIcon fontSize={"24px"} icon={faFileUser} />
                    <Typography variant="titleMedium" color="primary.main" gutterBottom sx={{ marginLeft: "15px" }}>
                        {t("Lista file")}
                    </Typography>
                </Box>

                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button variant="contained" size="small" onClick={handleButtonClick}>
                        {t("Carica file")}
                    </Button>
                </Box>
            </Box>
            <Box sx={{ pt: 10 }}>{renderDataTable()}</Box>
            <Box>
                <DocumentiPreview open={previewOpen} setOpen={setPreviewOpen} id={selectedDocumentId} fileName={selectedFile} />
            </Box>

            {openModal && (
                <BaseModal
                    open={openModal}
                    handleDecline={handleDecline}
                    decline={t("Annulla")}
                    agree={t("Salva")}
                    handleAgree={handleUploadFile}
                    content={
                        <AnagraficaFileUpload
                            setDocDescription={setDocDescription}
                            setDocDate={setDocDate}
                            docDescription={docDescription}
                            docDate={docDate}
                            files={files}
                            setFiles={setFiles}
                            onRemove={onRemove}
                            onFileUpload={onFileUpload}
                        />
                    }
                    loader={loader}
                    title={t("CARICA NUOVO FILE")}
                    isFooter={true}
                />
            )}
            <AnagraficaFileEdit
                open={editModalOpen}
                onClose={handleCloseEdit}
                onSave={handleSaveEdit}
                fileData={selectedFileData}
                loading={editLoading}
            />
            {selectedFileForTransfer && (
                <TrasferimentoFilePraticaModal
                    open={trasferimentoModalOpen}
                    onClose={handleCloseTrasferimento}
                    fileData={selectedFileForTransfer}
                    key={`transfer-modal-${selectedFileForTransfer?.id || 'new'}`}
                />
            )}
        </Box>
    );
};

export default Documenti;
