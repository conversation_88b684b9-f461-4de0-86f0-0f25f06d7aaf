import { Divider, Typo<PERSON>, <PERSON>rid, <PERSON>ton, Box } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPenToSquare } from "@fortawesome/free-regular-svg-icons";
import { faIdCard } from "@fortawesome/free-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { useConfigs } from "../../../../store/ConfigStore";
import { useAnagraficheProvider } from "../../providers/AnagraficheProvider";
import { CleanLabel, BoldValue } from "../../components/CustomTypography";
import { useUser } from "../../../../store/UserStore";
import Spinner from "../../../../custom-components/Spinner";
import ContentHeader from "../../components/ContentHeader";

interface IFiscaliBanche {
    showCreateForm: any;
    setShowCreateForm: React.Dispatch<React.SetStateAction<any>>;
}

export default function FiscaliBancheDetail(props: IFiscaliBanche) {
    const { showCreateForm, setShowCreateForm } = props;

    const { t } = useTranslation();
    const { user, modules }: any = useUser();
    const { configs }: any = useConfigs();
    const displayRitAcconto = configs?.data?.app?.input_anagrafica_dati_fiscali_bool || false;

    const { isInitialized, anagrafiche } = useAnagraficheProvider();

    const { customer, listini } = anagrafiche;

    const listinoName = () => {
        const listino = listini.find((listin: any) => listin.id === customer.listino);
        return listino?.descrizione;
    };

    const listinoOrarioName = () => {
        const listino = listini.find((listin: any) => listin.id === customer.listino_orario);
        return listino?.descrizione;
    };

    const speseGeneraliValue = (value: any) => {
        if (!value || parseInt(value) === 0) {
            return "-";
        } else {
            return `${value}%`;
        }
    };

    if (!isInitialized) {
        return <Spinner />;
    }
    const shouldShowConditionalSections = modules?.provisioningRow?.subscription_type != 37;
    return (
        <>
            <Grid container justifyContent="space-between" alignItems="center">
                <Grid item sx={{ display: "flex" }}>
                    <ContentHeader icon={faIdCard} title={t("Fiscali e banche")} />
                </Grid>
                {modules && modules.canModifyAnagrafiche(user) && (
                    <Grid item>
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "#008FD6"
                                    }}
                                    icon={faPenToSquare}
                                />
                            }
                            onClick={() =>
                                setShowCreateForm({
                                    ...showCreateForm,
                                    fiscaliBanche: true
                                })
                            }
                        >
                            {t("Modifica")}
                        </Button>
                    </Grid>
                )}
            </Grid>
            <Box key={0} style={{ marginTop: "16px" }}>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <CleanLabel>{t("Codice destinatario")}:</CleanLabel>
                        <BoldValue>{customer.codiceb2b || "-"}</BoldValue>
                    </Grid>
                </Grid>
            </Box>

            <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                <Grid item xs={6}>
                    <Typography
                        variant="titleSmall"
                        component="div"
                        color="primary.textTitleColor"
                        gutterBottom
                        style={{
                            marginTop: "20px"
                        }}
                    >
                        {t("Dati fiscali")}
                    </Typography>
                </Grid>
            </Grid>

            {shouldShowConditionalSections && (
                <Box key={1} style={{ marginBottom: "16px" }}>
                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={12}>
                            <CleanLabel>{t("Spese generali")}:</CleanLabel>
                            <BoldValue>{speseGeneraliValue(customer.spese_generali)?.toString().replace(".", ",")}</BoldValue>
                        </Grid>
                        {displayRitAcconto && (
                            <Grid item xs={12}>
                                <CleanLabel>{t("Rit.acconto")}:</CleanLabel>
                                <BoldValue>{customer.rit_acconto === "0" ? t("No") : t("Si")}</BoldValue>
                            </Grid>
                        )}
                    </Grid>
                    <Divider light style={{ width: "80%" }} />
                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={6}>
                            <Typography
                                variant="titleSmall"
                                component="div"
                                color="primary.textTitleColor"
                                gutterBottom
                                style={{
                                    marginTop: "20px"
                                }}
                            >
                                {t("Modalita di pagamento")}
                            </Typography>
                            <BoldValue>{`${customer.modalita_pagamento || "-"}`}</BoldValue>
                        </Grid>
                    </Grid>

                    <Grid container spacing={2} sx={{ marginBottom: "15px" }}>
                        <Grid item xs={12}>
                            <Typography
                                variant="titleSmall"
                                component="div"
                                color="primary.textTitleColor"
                                gutterBottom
                                style={{
                                    marginTop: "20px"
                                }}
                            >
                                {t("Prestazioni")}
                            </Typography>

                            <Grid container spacing={2}>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Listino prestazioni")}:</CleanLabel>
                                    <BoldValue>{listinoName() || "-"}</BoldValue>
                                </Grid>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Listino orario")}:</CleanLabel>
                                    <BoldValue>{listinoOrarioName() || "-"}</BoldValue>
                                </Grid>
                                <Grid item xs={5}>
                                    <CleanLabel>{t("Tariffa oraria")}:</CleanLabel>
                                    <BoldValue>{customer.tariffa_oraria?.toString().replace(".", ",") ?? 0} €</BoldValue>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Grid>
                </Box>
            )}
        </>
    );
}
