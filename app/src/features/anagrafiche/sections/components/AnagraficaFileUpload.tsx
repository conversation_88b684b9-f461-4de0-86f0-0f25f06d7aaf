import React from "react";
import { <PERSON>, But<PERSON>, TextField } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { Uploader } from "@vapor/react-custom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faFileUser, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import BaseModal from "../../../../custom-components/BaseModal";
import ToastNotification from "../../../../custom-components/ToastNotification";
interface AnagraficaFileUploadProps {
    files: any;
    setFiles: any;
    onRemove: any;
    onFileUpload: any;
    setDocDescription: any;
    setDocDate: any;
    docDescription: any;
    docDate: any;
}

const AnagraficaFileUpload: React.FC<AnagraficaFileUploadProps> = ({ files, onRemove, onFileUpload, setDocDescription, setDocDate, docDescription, docDate }) => {
    const { t } = useTranslation();
    const [showError, setError] = React.useState<boolean>(false);
    const [errorMessage, setErrorMessage] = React.useState<string>("");
    const [currentFile, setCurrentFile] = React.useState<any>(null);
    const [previewOpen, setPreviewOpen] = React.useState<boolean>(false);

    const handleFileUpload = (newFiles: FileList | File[]) => {
        const validFiles: File[] = [];
        const errors: string[] = [];

        Array.from(newFiles).forEach((file: File) => {
            const isValidType = file.type === "image/jpeg" || file.type === "image/png";
            const isValidSize = file.size <= 500 * 1024;
            if (!isValidType) {
                errors.push(`${file.name}: ${t("Solo file .jpeg o .png")}`);
            } else if (!isValidSize) {
                errors.push(`${file.name}: ${t("Dimensione massima 500kb")}`);
            } else {
                validFiles.push(file);
            }
        });

        if (errors.length) {
            setError(true);
            setErrorMessage(errors.join("\n"));
        }
        if (validFiles.length) {
            onFileUpload(validFiles);
        }
    };

    const onChangeDecription = (value: string, index: number) => {
        const newDescriptions = [...docDescription];
        newDescriptions[index] = value;
        setDocDescription(newDescriptions);
    };

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };

    const onDateChange = (_name: string, date: Date, index: number) => {
        const newDates = [...docDate];
        newDates[index] = formatDate(date);
        setDocDate(newDates);
    };

    const filePreview = (file: File) => {
        setCurrentFile(file);
        setPreviewOpen(true);
    };
    function getFilePreview(file: File): string | null {
        if (file && file.type.startsWith("image/")) {
            return URL.createObjectURL(file);
        }
        return "";
    }

    return (
        <Box>
            <ToastNotification showNotification={showError} setShowNotification={setError} severity="error" text={errorMessage} />
            {previewOpen && currentFile && (
                <BaseModal
                    open={previewOpen}
                    handleDecline={() => setPreviewOpen(false)}
                    title={t("Anteprima file")}
                    content={
                        <Box sx={{ textAlign: "center" }}>
                            <img src={getFilePreview(currentFile) ?? ""} alt={currentFile.name} style={{ maxWidth: "100%", maxHeight: "400px" }} />
                            <Button variant="outlined" color="primary" onClick={() => setPreviewOpen(false)} sx={{ mt: 2 }}>
                                {t("Chiudi")}
                            </Button>
                        </Box>
                    }
                    isFooter={false}
                />
            )}
            <Uploader
                draggable
                buttonLabel={t("Carica file")}
                draggableText={t("Trascina qui")}
                dropText={t("Trascina qui")}
                onFileUpload={handleFileUpload}
                label={t("Nuovo file")}
                description={t("Sono accettati file .jpeg e .png con dimensione massima di 500kb per file")}
                inputFileProps={{
                    multiple: true
                }}
                buttonVariant="outlined"
                files={files}
                onRemove={onRemove}
                renderItem={(file: any, fileIndex: number) => (
                    <>
                        <Box
                            key={fileIndex}
                            sx={{
                                mb: 2,
                                p: 2,
                                border: "1px solid #eee",
                                borderRadius: 2,
                                background: "#fff"
                            }}
                        >
                            <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                    {/* File icon (replace with your own logic if needed) */}
                                    <FontAwesomeIcon icon={faFileUser} fontSize={20} />
                                    <Box>
                                        <strong>{file.name}</strong>
                                        <Box sx={{ color: "#888", fontSize: 14 }}>
                                            <span style={{ color: "#888" }}>{file.type}</span>
                                        </Box>
                                        <Box sx={{ color: "#888", fontSize: 14 }}>{(file.size / 1024).toFixed(0)}kb</Box>
                                    </Box>
                                </Box>
                                <Box sx={{ display: "flex", alignItems: "center", gap: 1, ml: 2 }}>
                                    <Button variant="text" color="primary" onClick={() => filePreview(file)} startIcon={<FontAwesomeIcon icon={faEye} />} />
                                    <Button variant="text" color="error" onClick={() => onRemove(fileIndex)} startIcon={<FontAwesomeIcon icon={faTrashCan} />} />
                                </Box>
                            </Box>
                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <Box sx={{ flex: 1 }}>
                                    <TextField label={t("Descrizione")} value={docDescription[fileIndex] || ""} onChange={(e: any) => onChangeDecription(e.target.value, fileIndex)} fullWidth />
                                </Box>
                                <Box sx={{ flex: 1 }}>
                                    <DatePicker
                                        label={t("Data di aggiunta")}
                                        name="endDateSearch"
                                        value={docDate[fileIndex] || new Date()}
                                        onChange={(date: Date | null) => {
                                            if (date) onDateChange("endDateSearch", date, fileIndex);
                                        }}
                                    />
                                </Box>
                            </Box>
                        </Box>
                    </>
                )}
            />
        </Box>
    );
};

export default AnagraficaFileUpload;
