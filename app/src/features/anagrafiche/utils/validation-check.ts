export const checkData = async (request: any, data: any) => {
        const formData = new FormData();
        
        // Add mandatory fields
        if (data.categoria) {
            formData.append('mandatory[categoria]', data.categoria);
        }
        if (data.codicefiscale !== undefined) {
            formData.append('mandatory[codicefiscale]', data.codicefiscale || '');
        }
        if (data.codiceb2b !== undefined) {
            formData.append('mandatory[codiceb2b]', data.codiceb2b || '');
        }
        if (data.partitaiva !== undefined) {
            formData.append('mandatory[partitaiva]', data.partitaiva || '');
        }
        
        // Add optional fields
        if (data.codicefiscale !== undefined) {
            formData.append('optional[codicefiscale]', data.codicefiscale || '');
        }
        if (data.id !== undefined) {
            formData.append('optional[id]', data.id || '');
        }
        if (data.codicedestinatario !== undefined) {
            formData.append('optional[codicedestinatario]', data.codicedestinatario || '');
        }
        if (data.partitaiva !== undefined) {
            formData.append('optional[partitaiva]', data.partitaiva || '');
        }

        if (data?.contacts !== undefined) {
            data.contacts.forEach((contact: any) => {
                if (contact.nome === "Email pec") {
                    formData.append('mandatory[dyphone_7]', contact.valore || '');
                }
            });
        }
        
        const response: any = await request.doFetch(true, formData);
        return response.data;
}

