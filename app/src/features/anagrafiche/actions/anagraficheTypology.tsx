import { useState } from "react";
import {
    Typo<PERSON>,
    FormControlLabel,
    ListItemIcon,
    Card,
    CardContent,
    Radio,
    IconButton,
    Collapse,
} from "@vapor/react-material";
import InfoIcon from "@mui/icons-material/Info";
import ClearIcon from "@mui/icons-material/Clear";
import { Grid, NotificationInline } from "@vapor/react-material";
import { useCardData } from "../helpers/typologyData";
import { useTranslation } from "@1f/react-sdk";
import Spinner from "../../../custom-components/Spinner";

interface ITypologyInterface {
    anagraficheParams: any;
    parentTipologiaSelected: string;
    setParentTipologiaSelected: React.Dispatch<React.SetStateAction<string>>;
    userType: any;
    anagraficheData: any;
    setUserType: React.Dispatch<React.SetStateAction<any>>;
}

export default function AnagraficheTypology(props: ITypologyInterface) {
    const {
        anagraficheParams,
        parentTipologiaSelected,
        setParentTipologiaSelected,
        userType,
        setUserType,
        anagraficheData
    } = props;
    const { t } = useTranslation();
    const cardData = useCardData(anagraficheData);
    const [open, setOpen] = useState<boolean>(true);

    const handleTypologyChange = (event: any) => {
        const name = event.target.name || event.currentTarget.dataset.name;

        setParentTipologiaSelected(name);
        setUserType({ ...userType, parentSelected: name });
    };

    return (
        <>
            {cardData.length === 0 ? (
                <Spinner fullPage />
            ) : (
                <>
                    <div
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                        }}
                    >
                        <Typography
                            variant="titleMedium"
                            component="div"
                            color="primary.textTitleColor"
                            gutterBottom
                            sx={{ marginRight: "370px", marginBottom: "10px" }}
                        >
                            {t("Seleziona il tipo di anagrafica")}
                        </Typography>{" "}
                        <Collapse in={open}>
                            <NotificationInline
                                action={
                                    <IconButton onClick={() => setOpen(false)}>
                                        <ClearIcon />
                                    </IconButton>
                                }
                                icon={<InfoIcon />}
                                severity="info"
                                variant="outlined"
                                sx={{
                                    width: "670px",
                                    ml: "10px",
                                    "& .MuiAlert-message": {
                                        mt: "4px",
                                    },
                                }}
                            >
                                {t(
                                    "Una volta confermata la tipologia non si puo piu modificare"
                                )}
                            </NotificationInline>
                        </Collapse>
                    </div>
                    <Grid
                        container
                        spacing={2}
                        sx={{
                            width: "700px",
                            margin: "0 auto", // Center the grid horizontally
                        }}
                        justifyContent="flex-start"
                        alignItems="center"
                    >
                        {cardData.map((card: any, index: number) => (
                            <Grid item xs={12} data-name={card.id} sm={6} key={index} onClick={handleTypologyChange}>
                                <Card
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "center",
                                        width: 330,
                                        margin: "auto",
                                        padding: "3px",
                                        overflow: "hidden",
                                        border:
                                            anagraficheParams.tipo === card.id
                                                ? "1px solid #1e88e5"
                                                : "",
                                        borderBottom:
                                            anagraficheParams.tipo === card.id
                                                ? "3px solid #1e88e5"
                                                : "",
                                        minHeight: "100px",
                                    }}
                                >
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            width: "100%",
                                        }}
                                    >
                                        <CardContent
                                            sx={{
                                                flex: 1, // Allow CardContent to take remaining space
                                                padding: 0,
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <div
                                                style={{
                                                    display: "flex",
                                                    alignItems: "center",
                                                    marginBottom: "4px",
                                                }}
                                            >
                                                <ListItemIcon
                                                    sx={{
                                                        minWidth: "auto",
                                                        marginRight: "8px",
                                                    }}
                                                >
                                                    {card.icon}
                                                </ListItemIcon>
                                                <Typography
                                                    component="div"
                                                    variant="titleMedium"
                                                    sx={{
                                                        fontSize: "20px",
                                                        overflow: "hidden",
                                                        // wordWrap: "break-word",
                                                        // wordBreak: "break-word",
                                                    }}
                                                >
                                                    {card.title}
                                                </Typography>
                                            </div>
                                            <Typography
                                                variant="body1"
                                                gutterBottom
                                                sx={{
                                                    fontSize: "12px",
                                                    color: "#566B76",
                                                    lineHeight: 1.2,
                                                    marginLeft: "40px", // Align body text to the start of the title
                                                    overflow: "hidden",
                                                    wordWrap: "break-word",
                                                    wordBreak: "break-word",
                                                }}
                                            >
                                                {card.description}
                                            </Typography>
                                        </CardContent>
                                        <FormControlLabel
                                            control={
                                                <Radio
                                                    name={card.id}
                                                    checked={
                                                        parentTipologiaSelected ===
                                                        card.id
                                                    }
                                                    onChange={
                                                        handleTypologyChange
                                                    }
                                                />
                                            }
                                            label=""
                                            labelPlacement="start"
                                            sx={{
                                                marginRight: "8px", // Adjust margin as needed
                                                alignSelf: "center", // Vertically center the radio button
                                            }}
                                        />
                                    </div>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </>
            )}
        </>
    );
}
