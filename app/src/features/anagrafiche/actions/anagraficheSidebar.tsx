import { Divider } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import DetailList from "../../../custom-components/DetailList";
import DetailSidebar from "../../../custom-components/SidebarDetail";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import { useCardData } from "../helpers/typologyData";
import { useUser } from "../../../store/UserStore";

interface IAnagraficheSidebar {
    refs: any;
    scrollToSection: (ref: React.RefObject<HTMLDivElement>) => void;
    values: any;
    currentSection: string;
    userType: any;
    anagraficheData: any;
}

const AnagraficheSidebar = (props: IAnagraficheSidebar) => {
    const { refs, scrollToSection, values, currentSection, userType, anagraficheData } = props;
    const { t } = useTranslation();
    const cardData = useCardData(anagraficheData);

    const { modules }: any = useUser();

    const datiList = [
        { id: "1", label: t("Dati Generali"), ref: refs.datiGeneraliRef },
        { id: "2", label: t("Recapiti"), ref: refs.recapitiRef },
        ...(userType.parentSelected !== CUSTOMER_TYPE.PersonNumber
            ? [
                  {
                      id: "5",
                      label: t("Cammera di Commercio"),
                      ref: refs.cameraDiCommercioRefs
                  }
              ]
            : []),
        {
            id: "4",
            label: t("Note e profilazione"),
            ref: refs.noteprofilazioneRefs
        },
        { id: "6", label: t("Fiscali e Banche"), ref: refs.fiscaliBancheRefs },
        // Conditionally include Antiriciclaggio section
        ...(modules.provisioningRow?.subscription_type != 37 ? [{ id: "7", label: t("Antiriciclaggio"), ref: refs.antiriciclaggioRefs }] : []),
        // Conditionally include Campi Dinamici section
        ...(modules.provisioningRow?.subscription_type != 37 ? [{ id: "8", label: t("Campi Dinamici"), ref: refs.campiDinamiciRefs }] : [])
    ];

    const childrenTypologyData = cardData.reduce((acc: any, tipologia: any) => {
        if (tipologia.title === userType.type) {
            return tipologia.details.find((item: any) => item.id === userType.detailSelected);
        }
        return acc;
    }, null);

    function capitalizeWords(str?: string) {
        return (
            str
                ?.trim()
                .toLowerCase()
                .split(/\s+/)
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ") ?? ""
        );
    }

    return (
        <DetailSidebar
            childrenBadgeTitle={capitalizeWords(childrenTypologyData?.name)}
            badgeTitle={capitalizeWords(userType.type)}
            iconTitle={userType.icon}
            title={userType.parentSelected === CUSTOMER_TYPE.Societa ? `${values.nome}` : `${values.subjectName} ${values.subjectSurname}`}
        >
            <Divider light />
            <DetailList title={t("Dati")} list={datiList} scrollToSection={scrollToSection} currentSection={currentSection} />
        </DetailSidebar>
    );
};

export default AnagraficheSidebar;
