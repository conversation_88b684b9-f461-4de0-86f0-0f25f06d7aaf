import { useEffect, useState } from "react";
import DatiGeneraliForm from "./components/DatiGeneraliForm";
import RecapitiForm from "./components/RecapitiForm";
import NoteProfilzaioneForm from "./components/NoteProfilazioneForm";
import FisacliBancheForm from "./components/FiscaliBancheForm";
import AntiriciclaggioForm from "./components/AntiriciclaggioForm";
import CammeraDiCommercioForm from "./components/CammeraDiCommercioForm";
import { Divider } from "@vapor/react-material";
import { debounce } from "lodash";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import CampiDinamiciForm from "./components/CampiDinamiciForm";
import { useValidationScrolling } from "../hooks/useValidationScrolling";
import { useUser } from "../../../store/UserStore";

interface IUserData {
    anagraficheData: any;
    refs: any;
    method: any;
    t: any;
    setCurrentSection: React.Dispatch<React.SetStateAction<string>>;
    userType: any;
    setUserType: any;
    setSelectedTagsValue: any;
    optionalFields: any;
    // Dynamic fields props
    dynamicFields: any[];
    dateStates: { [key: string]: Date | null };
    setDateStates: React.Dispatch<React.SetStateAction<{ [key: string]: Date | null }>>;
    isLoadingFields: boolean;
    parseSelectOptions: (optString: string) => any[];
    validateRegex: (value: string, regexPattern: string) => boolean;
    isChecked: (val: any) => boolean;
}

const UserData = (props: IUserData) => {
    const {
        anagraficheData,
        refs,
        method,
        t,
        setCurrentSection,
        userType,
        setUserType,
        setSelectedTagsValue,
        optionalFields,
        // Dynamic fields props
        dynamicFields,
        dateStates,
        setDateStates,
        isLoadingFields,
        parseSelectOptions,
        validateRegex,
        isChecked
    } = props;
    const { datiGeneraliRef, recapitiRef, contactsRefs, noteprofilazioneRefs, cameraDiCommercioRefs, fiscaliBancheRefs, antiriciclaggioRefs, campiDinamiciRefs, groupsRefs, parentRef } = refs;

    const [bottomPadding, setBottomPadding] = useState<string>("0");

    const {
        formState: { errors }
    } = method;

    const { modules }: any = useUser();
    // Check if sections should be shown based on subscription type
    const shouldShowConditionalSections = modules?.provisioningRow?.subscription_type != 37;

    const { handleSaveAttempt } = useValidationScrolling({
        method,
        refs: {
            datiGeneraliRef: refs.datiGeneraliRef,
            noteprofilazioneRefs: refs.noteprofilazioneRefs,
            fiscaliBancheRefs: refs.fiscaliBancheRefs,
            parentRef: refs.parentRef
        },
        isOnCreate: true
    });

    const debouncedHandleIntersection = debounce((entries: any, setCurrentSection: any) => {
        entries.forEach((entry: any) => {
            if (entry.isIntersecting) {
                setCurrentSection(entry.target.id);
            }
        });
    }, 100); // Adjust

    useEffect(() => {
        try {
            const observer = new IntersectionObserver((entries) => debouncedHandleIntersection(entries, setCurrentSection), {
                root: null,
                threshold: [0.1, 0.3, 0.5, 0.7, 1.0], // Adjust based on section sizes
                rootMargin: "-50px 0px -50% 0px"
            });

            const sections = [
                datiGeneraliRef,
                recapitiRef,
                contactsRefs,
                noteprofilazioneRefs,
                cameraDiCommercioRefs,
                fiscaliBancheRefs,
                ...(shouldShowConditionalSections ? [antiriciclaggioRefs] : []),
                ...(shouldShowConditionalSections ? [campiDinamiciRefs] : []),
                groupsRefs
            ].filter(Boolean);

            sections.forEach((ref) => {
                if (ref.current) {
                    observer.observe(ref.current);
                }
            });

            return () => {
                sections.forEach((ref) => {
                    if (ref.current) {
                        observer.unobserve(ref.current);
                    }
                });
            };
        } catch (error: any) {
            console.log("is error in here!!!!", error);
        }
    }, [
        setCurrentSection,
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        noteprofilazioneRefs,
        cameraDiCommercioRefs,
        fiscaliBancheRefs,
        antiriciclaggioRefs,
        campiDinamiciRefs,
        groupsRefs,
        parentRef,
        shouldShowConditionalSections
    ]);

    //defining padding for the last section so it can be shown full and be selected in the sidebar
    useEffect(() => {
        if (parentRef.current) {
            // Use the last visible section for padding calculation
            const lastSection = shouldShowConditionalSections ? campiDinamiciRefs.current : fiscaliBancheRefs.current;
            if (lastSection) {
                const paddingBottom = lastSection.offsetHeight; // Extra buffer
                setBottomPadding(paddingBottom); // Adjust paddingBottom
            }
        }
    }, [campiDinamiciRefs, fiscaliBancheRefs, parentRef, shouldShowConditionalSections]);

    const scrollToDatiGenerale = () => {
        if (parentRef.current && datiGeneraliRef.current) {
            const parent = parentRef.current;
            const element = datiGeneraliRef.current;
            const parentTop = parent.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop - 50; // Add an offset

            window.scrollTo({ top: scrollTo, behavior: "smooth" }); // Smooth scroll
        }
    };

    useEffect(() => {
        if (errors.subjectName || errors.subjectSurname || errors.codicefiscale || errors.partitaiva) {
            scrollToDatiGenerale();
        }
    }, [errors]);

    return (
        <div
            style={{
                overflowY: "auto",
                paddingRight: "3rem",
                scrollbarWidth: "none",
                minHeight: "100vh",
                paddingBottom: `${bottomPadding}px` // Apply calculated padding
            }}
            ref={parentRef}
        >
            <div id="1" ref={datiGeneraliRef}>
                <DatiGeneraliForm
                    anagraficheData={anagraficheData}
                    method={method}
                    t={t}
                    isSociety={userType.parentSelected === CUSTOMER_TYPE.Societa}
                    isPersonaFisica={userType.parentSelected === CUSTOMER_TYPE.PersonNumber}
                    userType={userType}
                    setUserType={setUserType}
                    optionalFields={optionalFields}
                    handleSaveAttempt={handleSaveAttempt}
                />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="2" ref={recapitiRef}>
                <RecapitiForm anagraficheData={anagraficheData} method={method} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            {userType.parentSelected !== CUSTOMER_TYPE.PersonNumber && (
                <>
                    <div id="5" ref={cameraDiCommercioRefs}>
                        <CammeraDiCommercioForm anagraficheData={anagraficheData} method={method} />
                    </div>

                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                </>
            )}
            <div id="4" ref={noteprofilazioneRefs}>
                <NoteProfilzaioneForm anagraficheData={anagraficheData} method={method} setSelectedTagsValue={setSelectedTagsValue} handleSaveAttempt={handleSaveAttempt} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="6" ref={fiscaliBancheRefs}>
                <FisacliBancheForm anagraficheData={anagraficheData} method={method} handleSaveAttempt={handleSaveAttempt} />
            </div>

            {/* Conditionally render Antiriciclaggio section */}
            {shouldShowConditionalSections && (
                <>
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                    <div id="7" ref={antiriciclaggioRefs}>
                        <AntiriciclaggioForm anagraficheData={anagraficheData} method={method} />
                    </div>
                </>
            )}

            {/* Conditionally render Campi Dinamici section */}
            {shouldShowConditionalSections && (
                <>
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                    <div id="8" ref={campiDinamiciRefs}>
                        <CampiDinamiciForm
                            anagraficheData={anagraficheData}
                            method={method}
                            dynamicFields={dynamicFields}
                            dateStates={dateStates}
                            setDateStates={setDateStates}
                            isLoadingFields={isLoadingFields}
                            parseSelectOptions={parseSelectOptions}
                            validateRegex={validateRegex}
                            isChecked={isChecked}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default UserData;
