import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Box, FormControl, TextField, Button, TextareaAutosize, InputLabel } from "@vapor/v3-components";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import useTags from "../../hooks/useTags";
import ContactHeader from "../../components/ContentHeader";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";
import { useUser } from "../../../../store/UserStore";

const filter = createFilterOptions<any>();

interface INoteProfilzaione {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: any;
    onSubmitUpdate?: (data: any) => void;
    activeTags?: any[];
    id?: any;
    setSelectedTagsValue?: any;
    handleSaveAttempt?: (fromSection?: string) => Promise<boolean>;
}

export default function NoteProfilazioneForm(props: INoteProfilzaione) {
    const { anagraficheData, method, showCreateForm, isUpdate = false, setShowCreateForm, onSubmitUpdate, activeTags, setSelectedTagsValue, handleSaveAttempt }: any = props;

    const [isSaving, setIsSaving] = useState(false);
    const [saveError, setSaveError] = useState<string>("");

    const { tags, saveTags, handleTagsSearch, searchTagLoading, selectedTags, setSelectedTags } = useTags();

    const {
        control,
        watch,
        handleSubmit,
        register,
        formState: { errors }
    } = method;
    const { categorie } = anagraficheData;

    const { t } = useTranslation();
    const values = watch();

    useEffect(() => {
        if (activeTags && activeTags.length > 0) {
            setSelectedTags(
                JSON.parse(activeTags)?.map((item: any) => ({
                    title: item.description,
                    value: item.description
                }))
            );
        }
    }, [activeTags]);

    const handleTagsChange = (_: any, newInputValue: any) => {
        const cleanValues = newInputValue.map((item: any) => {
            if (typeof item === "string") {
                return;
            } else if (item?.title && item.title.startsWith('Aggiungi "')) {
                return { title: item.inputValue, value: item.inputValue }; // Return as is for regular objects
            }
            return { title: item.title, value: item.title }; // Return as is for regular objects
        });

        setSelectedTags([...cleanValues]);
        setSelectedTagsValue([...cleanValues]);
    };

    const handleEditSaves = async (event: any) => {
        event.preventDefault();
        setIsSaving(true);
        setSaveError("");

        try {
            // Use validation if available
            if (handleSaveAttempt) {
                const canSave = await handleSaveAttempt("noteProfilazione");
                if (!canSave) {
                    setSaveError("Compilare i campi obbligatori prima di salvare la categoria.");
                    setIsSaving(false);
                    return;
                }
            }

            saveTags(values.uniqueid, selectedTags);
            handleSubmit(onSubmitUpdate)();

            // Close all open forms
            setShowCreateForm({
                datiGenerali: false,
                recapiti: false,
                indirizzo: false,
                contactiCollegati: false,
                noteProfilazione: false,
                cameraDiCommercio: false,
                fiscaliBanche: false,
                antiriciclaggio: false,
                gruppi: false,
                campiDinamici: false
            });
        } catch (error) {
            setSaveError("Errore durante il salvataggio. Verificare i campi obbligatori.");
        } finally {
            setIsSaving(false);
        }
    };

    const { modules }: any = useUser();
    const shouldShowConditionalSections = modules?.provisioningRow?.subscription_type != 37;
    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1
            }}
        >
            <Box display="flex">
                <ContactHeader icon={faIdCard} title={t("Note e profilazione")} />
            </Box>
            <FormControl>
                <InputLabel>{t("Note")}</InputLabel>
                <TextareaAutosize
                    spellCheck="false"
                    minRows={5}
                    value={values.annotazioni}
                    {...register("annotazioni")}
                    style={{
                        width: 815,
                        resize: "none"
                    }}
                />
            </FormControl>
            <Typography variant="subtitle" color="primary.textTitleColor">
                {t("Profilazione")}
            </Typography>
            {shouldShowConditionalSections && (
                <Box sx={{ display: "flex", gap: 2 }}>
                    <FormControl sx={{ width: 400 }}>
                        <FormInput
                            sx={{
                                width: "100%"
                            }}
                            control={control}
                            name={`categoria`}
                            defaultValue={values.categoria}
                            label={t("Categoria")}
                            type="select"
                            variant="outlined"
                            options={categorie?.map(({ id, nome }: any) => ({
                                value: id,
                                label: nome
                            }))}
                            error={!!errors?.categoria}
                            helperText={errors?.categoria?.message}
                        />
                    </FormControl>
                    <FormControl sx={{ width: 400 }}>
                        <FormInput
                            sx={{
                                width: "100%"
                            }}
                            control={control}
                            name={`externalcode`}
                            label={t("Codice esterno")}
                            type="text"
                            variant="outlined"
                        />
                    </FormControl>
                </Box>
            )}

            {saveError && (
                <Typography color="error" variant="body2">
                    {saveError}
                </Typography>
            )}
            {shouldShowConditionalSections && (
                <FormControl sx={{ width: 815 }}>
                    <CustomAutocomplete
                        sx={{ width: "100%" }}
                        options={tags}
                        loading={searchTagLoading}
                        filterOptions={(options: any, params: any) => {
                            const filtered = filter(options, params);
                            const { inputValue } = params;
                            const isExisting = options.some((option: any) => inputValue === option.title);
                            if (inputValue !== "" && !isExisting) {
                                filtered.push({
                                    inputValue: inputValue,
                                    title: `Aggiungi "${inputValue}"`
                                });
                            }
                            return filtered;
                        }}
                        multiple
                        onChange={handleTagsChange}
                        onInputChange={(_: any, newInputValue: any) => {
                            handleTagsSearch(newInputValue);
                        }}
                        selectOnFocus
                        clearOnBlur
                        value={selectedTags}
                        isOptionEqualToValue={(option: any, value: any) => option.title === value.title}
                        getOptionLabel={(option: any) => {
                            if (!option) return ""; // Check for undefined
                            if (typeof option === "string") {
                                return option;
                            }
                            if (option.title?.startsWith(t('Aggiungi "'))) {
                                return option.inputValue;
                            }
                            return option.title;
                        }}
                        renderOption={(props: any, option: any) => {
                            return <li {...props}>{option.title}</li>;
                        }}
                        renderInput={(params: any) => <TextField {...params} label={t("Etichette")} />}
                    />
                </FormControl>
            )}

            {shouldShowConditionalSections && <FormInput sx={{ width: 400 }} control={control} label={t("Formula saluti")} name={`formula_saluti`} />}
            <Typography variant="body500" gutterBottom sx={{ color: "#566B76" }}>
                {t('N.B. Se lasciato vuoto verrà settato di default come "Spett.le"')}.
            </Typography>
            <FormInput
                control={control}
                name={`rapid_ext_access`}
                type="checkbox"
                options={[
                    {
                        label: t("Concedi subito l'accesso alla pratica se inserito come esterno"),
                        value: "1"
                    }
                ]}
            />
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                noteProfilazione: false
                            })
                        }
                        variant="outlined"
                        disabled={isSaving}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button type="submit" onClick={(event: any) => handleEditSaves(event)} variant="contained" disabled={isSaving}>
                        {isSaving ? t("Salvando...") : t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
