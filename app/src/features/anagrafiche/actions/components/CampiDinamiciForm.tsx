import {
    Box, 
    FormControl, 
    Button, 
    TextField,
    InputLabel,
    TextareaAutosize,
    IconButton, Tooltip
} from "@vapor/v3-components";
import CloseIcon from '@mui/icons-material/Close';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import ContentHeader from "../../components/ContentHeader";
import Spinner from "../../../../custom-components/Spinner";
import FormInput from "../../../../custom-components/FormInput";

interface ICampiDinamici {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: React.Dispatch<React.SetStateAction<any>>;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
    dynamicFields: any[];
    dateStates: { [key: string]: Date | null };
    setDateStates: React.Dispatch<React.SetStateAction<{ [key: string]: Date | null }>>;
    isLoadingFields: boolean;
    parseSelectOptions: (optString: string) => any[];
    validateRegex: (value: string, regexPattern: string) => boolean;
    isChecked: (val: any) => boolean;
}

export default function CampiDinamiciForm(props: ICampiDinamici) {
    const {
        method,
        showCreateForm,
        isUpdate = false,
        setShowCreateForm,
        onSubmitUpdate,
        dynamicFields,
        dateStates,
        setDateStates,
        isLoadingFields,
        parseSelectOptions,
        validateRegex,
        isChecked,
    }: any = props;
    const { t } = useTranslation();

    const {
        register,
        setValue,
        control,
        formState: { errors },
        watch,
        handleSubmit,
    } = method;
    const values = watch();

    const renderDynamicField = (field: any) => {
        const fieldName = `field_${field.id}`;
        const fieldValue = values[fieldName] || field.restore || '';
        const regexError = field.opt;
        const isRegexValid = field.tipo === 'regex' && regexError
            ? validateRegex(fieldValue, regexError)
            : true;

        switch (field.tipo) {
            case 'regex':
            case 'text':
                return (
                    <Box key={field.id} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                        <FormControl sx={{ width: 400 }}>
                            <FormInput
                                control={control}
                                name={fieldName}
                                label={field.nome}
                                type="text"
                                variant="outlined"
                                sx={{
                                    width: "100%",
                                    ...(field.tipo === 'regex' && regexError && !isRegexValid && {
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': { borderColor: 'warning.main', borderWidth: '2px' },
                                            '&:hover fieldset': { borderColor: 'warning.main' },
                                            '&.Mui-focused fieldset': { borderColor: 'warning.main' },
                                        },
                                    }),
                                }}
                            />
                        </FormControl>
                        {field.tipo === 'regex' && regexError && !isRegexValid && (
                            <Tooltip
                                title="Attenzione!"
                                arrow
                                placement="right"
                                description={"Il valore inserito non rispetta l'espressione di controllo prevista per il campo dinamico. "}
                            >
                                <ReportProblemIcon 
                                    sx={{
                                        color: 'warning.main',
                                        cursor: "pointer",
                                        marginTop: 3,
                                        marginLeft: 1
                                    }}
                                />
                            </Tooltip>
                        )}
                    </Box>
                );
            case 'checkbox':
                return (
                    <FormInput
                        key={field.id}
                        control={control}
                        name={fieldName}
                        type="checkbox"
                        onChange={(e: any) => {
                            setValue(fieldName, e.target.checked ? "1" : "0");
                        }}
                        options={[{ label: field.nome, value: "1" }]}
                        value={isChecked(fieldValue) ? "1" : "0"}
                    />
                );
            case 'select':
                const options = parseSelectOptions(field.opt || '');
                return (
                    <FormControl key={field.id} sx={{ width: 400 }}>
                        <FormInput
                            sx={{ width: "100%" }}
                            control={control}
                            name={fieldName}
                            label={field.nome}
                            type="select"
                            variant="outlined"
                            options={options}
                        />
                    </FormControl>
                );
            case 'memo':
                return (
                    <Box key={field.id} sx={{ width: 400 }}>
                        <FormControl>
                            <InputLabel>{field.nome}</InputLabel>
                            <TextareaAutosize
                                spellCheck="false"
                                minRows={5}
                                name={fieldName}
                                value={fieldValue}
                                onChange={(e) => {
                                    setValue(fieldName, e.target.value);
                                }}
                                style={{ width: 400, resize: "none" }}
                            />
                        </FormControl>
                    </Box>
                );
            case 'date':
                return (
                    <Box key={field.id} sx={{ width: 400, display: 'flex', gap: 1 }}>
                        <div style={{ width: 330, flex: 1 }}>
                            <DatePicker
                                sx={{ width: "100%" }}
                                label={field.nome}
                                name={fieldName}
                                value={dateStates[fieldName] || null}
                                onChange={(value: Date | null) => {
                                    setValue(fieldName, value);
                                    setDateStates((prev: { [key: string]: Date | null }) => ({ ...prev, [fieldName]: value }));
                                }}
                            />
                        </div>
                        {dateStates[fieldName] && (
                            <div style={{ paddingTop: '20px', display: 'flex', alignItems: 'center', width: 40 }}>
                                <IconButton
                                    edge="end"
                                    color="primary"
                                    variant="outlined"
                                    aria-label="delete"
                                    onClick={() => {
                                        setDateStates((prev: { [key: string]: Date | null }) => ({ ...prev, [fieldName]: null }));
                                        setValue(fieldName, '');
                                    }}
                                    size="medium"
                                    style={{ width: 40, height: 40 }}
                                >
                                    <CloseIcon />
                                </IconButton>
                            </div>
                        )}
                    </Box>
                );
            default:
                return (
                    <TextField
                        key={field.id}
                        label={field.nome}
                        name={fieldName}
                        helperText={field.descrizione}
                        sx={{ width: 400 }}
                        {...register(fieldName, { required: field.required === '1' })}
                        value={fieldValue}
                        error={!!errors[fieldName]}
                    />
                );
        }
    };

    const handleSaveChanges = (event: any) => {
        event.preventDefault();
        handleSubmit(onSubmitUpdate)();
        setShowCreateForm({ ...showCreateForm, campiDinamici: false });
    };

    return (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2, ml: 1 }}>
            <Box display="flex">
                <ContentHeader icon={faIdCard} title={t("Campi Dinamici")} />
            </Box>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2, position: "relative", minHeight: isLoadingFields ? 200 : "auto" }}>
                {isLoadingFields ? (
                    <Spinner fullPage={false} />
                ) : (
                    dynamicFields.map((field: any) => renderDynamicField(field))
                )}
            </Box>
            {isUpdate && (
                <Box gap={2} sx={{ display: "flex", justifyContent: "flex-end", width: 815 }}>
                    <Button
                        type="button"
                        onClick={() => setShowCreateForm({ ...showCreateForm, campiDinamici: false })}
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}