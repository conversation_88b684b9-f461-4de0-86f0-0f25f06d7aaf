import { <PERSON><PERSON>, <PERSON><PERSON>, Box, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@vapor/v3-components";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useCardData } from "./helpers/typologyData";
import CloseIcon from "@mui/icons-material/Close";
import Spinner from "../../custom-components/Spinner";
import { useNavigate } from "react-router-dom";
import { useUser } from "../../store/UserStore";

enum CUSTOMER_TYPE {
    Societa = "2",
    Person = "60",
    Altro = "54"
}
interface IProps {
    openDrawer: boolean;
    handleDrawer: () => void;
    rowIds: any;
}

export default function AnagrafichePanelDrawer(props: IProps) {
    const { openDrawer, handleDrawer, rowIds } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const anagraficheViewRequest = useGetCustom(`/anagrafiche/view/`);
    const getRelationshipsDataRequest = useGetCustom("anagraficherelationships/list?noTemplateVars=true");

    const [customerData, setCustomerData] = useState<any>([]);
    const [contactData, setContactData] = useState<any>([]);
    const [groupTypologiesData, setGroupTypologiesData] = useState<any>([]);
    const [userType, setUserType] = useState<any>({});
    const cardData = useCardData({ groupTypologies: groupTypologiesData });

    const fetchViewAnagrafiche = async (id: string) => {
        try {
            const response: any = await anagraficheViewRequest.doFetch(true, {
                id
            });
            return response.data;
        } catch (error: any) {
            return { error: error.message };
        }
    };

    const fetchRelationship = async (uniqueid: string) => {
        if (uniqueid) {
            const params = {
                peopleUniqueid: uniqueid,
                page: 0,
                pageSize: 7,
                sortColumn: "parent",
                sortOrder: "asc",
                searchField: ""
            };

            const response: any = await getRelationshipsDataRequest.doFetch(true, params);
            const { currentPage } = response.data;
            return currentPage;
        }
    };

    const findObjectByTypologyId = (id: string) => {
        return groupTypologiesData?.find((item: any) => item.typologies.some((typology: any) => typology.id === id));
    };

    function capitalizeFirstLetter(val: string) {
        return String(val).charAt(0).toUpperCase() + String(val).slice(1);
    }

    const childrenTypologyData = cardData.reduce((acc: any, tipologia: any) => {
        if (tipologia.title === userType.type) {
            return tipologia.details.find((item: any) => item.id === userType.detailSelected);
        }
        return acc;
    }, null);

    useEffect(() => {
        const fetch = async () => {
            const [anagrafiche, cData] = await Promise.all([fetchViewAnagrafiche(rowIds.id), fetchRelationship(rowIds.uniqueid)]);
            const { customer, groupTypologies } = anagrafiche;
            setCustomerData(customer);
            setGroupTypologiesData(groupTypologies);
            setContactData(cData);
        };

        fetch();
    }, [rowIds.id, rowIds.uniqueid]);

    useEffect(() => {
        if (customerData?.tipoid !== undefined) {
            const dataFind = findObjectByTypologyId(customerData?.tipoid);
            setUserType({
                type: capitalizeFirstLetter(dataFind?.name),
                detailSelected: customerData?.tipoid
            });
        }
    }, [customerData?.tipoid, groupTypologiesData]);

    const getInitials = (customerName: string): string => {
        return customerName
            ?.split(" ")
            ?.map((part) => part?.charAt(0)?.toUpperCase())
            ?.join("");
    };

    function capitalizeWords(str?: string) {
        return (
            str
                ?.trim()
                .toLowerCase()
                .split(/\s+/)
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ") ?? ""
        );
    }

    const { modules }: any = useUser();

    return (
        <Drawer
            anchor="right"
            open={openDrawer}
            onClose={handleDrawer}
            hideBackdrop
            width={800}
            sx={{
                "& .MuiDrawer-paper": {
                    width: "100%",
                    // Reduced shadow thickness and spread
                    boxShadow: "0px 0px 32px 8px rgba(0,0,0,0.22), 0px 4px 16px 0px rgba(0,0,0,0.18) !important"
                }
            }}
        >
            {anagraficheViewRequest.loading ? (
                <Spinner />
            ) : (
                <>
                    {/* Title */}
                    <div>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "space-between",
                                px: 3,
                                py: 2
                            }}
                        >
                            {/* LEFT SIDE */}
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    gap: 1
                                }}
                            >
                                <Box>
                                    <Badge
                                        style={{
                                            backgroundColor: "#E4EAF0",
                                            borderRadius: "4px",
                                            padding: "4px 8px",
                                            display: "inline-block",
                                            marginRight: "15px",
                                            marginBottom: "5px"
                                        }}
                                    >
                                        <Typography variant="body2" color="primary.textTitleColor">
                                            {userType?.type !== "Undefined" && userType.type !== "" ? capitalizeWords(userType.type) : t("Non Specificata")}
                                        </Typography>
                                    </Badge>
                                    {childrenTypologyData?.name && capitalizeWords(childrenTypologyData?.name)?.trim() !== capitalizeWords(userType?.type.trim()) && (
                                        <Badge
                                            style={{
                                                backgroundColor: "#E4EAF0",
                                                borderRadius: "4px",
                                                padding: "4px 8px",
                                                display: "inline-block",
                                                marginRight: "15px",
                                                marginBottom: "5px"
                                            }}
                                        >
                                            <Typography variant="body2" color="primary.textTitleColor">
                                                {childrenTypologyData?.name}
                                            </Typography>
                                        </Badge>
                                    )}
                                    <Typography variant="titleSmall" color="primary.textTitleColor">
                                        {customerData?.nome?.toUpperCase()}
                                    </Typography>
                                </Box>

                                {/* Second row: below the first */}
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1.5,
                                        pt: "20px"
                                    }}
                                >
                                    <Avatar
                                        sx={{
                                            bgcolor: "#f0f0f0",
                                            color: "#555",
                                            width: 36,
                                            height: 36,
                                            fontSize: "0.875rem",
                                            fontWeight: 500
                                        }}
                                    >
                                        {getInitials(customerData?.nome)}
                                    </Avatar>
                                    <Box>
                                        <Typography
                                            variant="caption"
                                            sx={{
                                                color: "#8a8a8a",
                                                display: "block",
                                                fontSize: "0.75rem"
                                            }}
                                        >
                                            {t("Proprietario")}
                                        </Typography>
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                fontWeight: 500,
                                                color: "#333",
                                                fontSize: "0.875rem"
                                            }}
                                        >
                                            {customerData?.nome}
                                        </Typography>
                                    </Box>
                                </Box>
                            </Box>

                            {/* RIGHT SIDE */}
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                    mb: "70px"
                                }}
                            >
                                {/* <Button
                                    startIcon={
                                        <FontAwesomeIcon
                                            style={{ color: "#008FD6" }}
                                            icon={faPenToSquare}
                                        />
                                    }
                                >
                                    {t("Modifica")}
                                </Button> */}
                                {/* <IconButton
                                    onClick={handleDrawer}
                                    color="primary"
                                >
                                    <FontAwesomeIcon
                                        icon={faEllipsisVertical}
                                    />
                                </IconButton> */}
                                <IconButton onClick={handleDrawer} color="primary">
                                    <CloseIcon />
                                </IconButton>
                            </Box>
                        </Box>
                        <Divider />
                    </div>

                    {/* Content */}
                    <Box
                        sx={{
                            backgroundColor: "#F2F6F8",
                            flexGrow: 1,
                            p: 3
                        }}
                    >
                        <Grid container spacing={2}>
                            {/* Top row: two cards */}
                            <Grid item xs={12} sm={6}>
                                <Card
                                    variant="outlined"
                                    sx={{
                                        border: "none",
                                        boxShadow: "none",
                                        minHeight: "200px"
                                    }}
                                >
                                    <CardContent>
                                        {/* Title */}
                                        <Typography variant="titleSmall" component="div" color="primary.textTitleColor" gutterBottom>
                                            {customerData?.tipoid === CUSTOMER_TYPE.Societa ? t("Dati società") : t("Dati anagrafica")}
                                        </Typography>

                                        {/* First row*/}
                                        <Grid container spacing={2} sx={{ mb: 2 }}>
                                            <Grid item xs={12} sm={6}>
                                                <Typography variant="body2" color="primary.textSubduedColor">
                                                    {customerData?.tipoid === CUSTOMER_TYPE.Person ? t("Nome Cognome") : t("Denominazione")}
                                                </Typography>
                                                <Typography variant="bodyLarge500">{customerData?.nome ? customerData?.nome : "-"}</Typography>
                                            </Grid>
                                        </Grid>

                                        {/* Second row: Luogo di nascita + Data di nascita */}
                                        {customerData.tipoid === CUSTOMER_TYPE.Person && (
                                            <Grid container spacing={2} sx={{ mb: 2 }}>
                                                <Grid item xs={6}>
                                                    <Typography variant="body2" color="primary.textSubduedColor">
                                                        {t("Luogo di nascita")}
                                                    </Typography>
                                                    <Typography variant="bodyLarge500">{customerData?.luogo_nascita ? customerData?.luogo_nascita : "-"}</Typography>
                                                </Grid>
                                                <Grid item xs={6}>
                                                    <Typography variant="body2" color="primary.textSubduedColor">
                                                        {t("Data di nascita")}
                                                    </Typography>
                                                    <Typography variant="bodyLarge500">{customerData?.data_nascita ? customerData?.data_nascita : "-"}</Typography>
                                                </Grid>
                                            </Grid>
                                        )}

                                        {/* Second row: Codice fiscale + Partita iva */}
                                        <Grid container spacing={2}>
                                            <Grid item xs={6}>
                                                <Typography variant="body2" color="primary.textSubduedColor">
                                                    {t("Codice fiscale")}
                                                </Typography>
                                                <Typography variant="bodyLarge500">{customerData?.codicefiscale ? customerData?.codicefiscale : "-"}</Typography>
                                            </Grid>
                                            <Grid item xs={6}>
                                                <Typography variant="body2" color="primary.textSubduedColor">
                                                    {t("Partita iva")}
                                                </Typography>
                                                <Typography variant="bodyLarge500">{customerData?.partitaiva ? customerData?.partitaiva : "-"}</Typography>
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grid>
                            {/* <Grid item xs={12} sm={6}>
                                <Card
                                    variant="outlined"
                                    sx={{
                                        border: "none",
                                        boxShadow: "none",
                                    }}
                                >
                                    <CardContent>
                                        <Typography
                                            variant="titleSmall"
                                            component="div"
                                            color="primary.textTitleColor"
                                            gutterBottom
                                        >
                                            Dati società
                                        </Typography>{" "}
                                        <Typography variant="body2">
                                            Content for the second card.
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </Grid> */}

                            {/* Bottom row: one full-width card */}

                            {modules.provisioningRow?.subscription_type != 37 && (
                                <Grid item xs={12}>
                                    <Card variant="outlined" sx={{ border: "none", boxShadow: "none" }}>
                                        <CardContent>
                                            {/* Section Title */}
                                            <Typography variant="titleSmall" color="primary.textTitleColor" gutterBottom sx={{ mb: 2 }}>
                                                {t("Contatti collegati")}
                                            </Typography>

                                            <Stack spacing={2} sx={{ mt: 2 }}>
                                                {(contactData || [])?.map((contact: any, index: any) => (
                                                    <Stack spacing={0.5} key={index}>
                                                        <Typography variant="body" color="primary.textSubduedColor">
                                                            {contact.relazione}
                                                        </Typography>
                                                        <Typography variant="bodyLarge500">{contact.child}</Typography>
                                                    </Stack>
                                                ))}
                                            </Stack>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            )}
                        </Grid>
                    </Box>

                    {/* Footer */}
                    <Box
                        sx={{
                            display: "flex",
                            height: "90px",
                            alignItems: "end",
                            justifyContent: "space-between",
                            width: "100%"
                        }}
                    >
                        <Stack
                            direction="row"
                            sx={{
                                p: 2,
                                width: "100%"
                            }}
                            justifyContent="space-between"
                        >
                            <Button onClick={handleDrawer}>{t("Chiudi")}</Button>
                            <Button variant="contained" onClick={() => navigate(`/anagrafiche/view?id=${customerData.id}`)}>
                                {t("Scheda completa")}
                            </Button>
                        </Stack>
                    </Box>
                </>
            )}
        </Drawer>
    );
}
