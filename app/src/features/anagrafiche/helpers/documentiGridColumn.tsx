import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import BaseGridList from "../../../models/BaseGridList";
import { mapOtherList } from "../../../utilities/common";
import { Box, Menu, MenuItem } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEllipsisVertical } from "@fortawesome/pro-regular-svg-icons";
import { faPenToSquare } from "@fortawesome/free-solid-svg-icons";
import { useState, useEffect } from "react";
import { useDeleteDocument } from "../hooks/useDeleteDocument";
import ConfirmModal from "../../../custom-components/ConfirmModal";

export const getDocumentiGrid = async (
    t: any,
    handlePrintDocumenti: any,
    handleEditDocument?: any,
    handleDeleteDocument?: any,
    onRefreshData?: any,
    handleCopyToPratica?: any
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome file"), t("Descrizione"), t("Data"), t("")],
            column_keys: ["nomefile", "titolodocumento", "datadoc", "Azioni"],
            column_widths: ["25%", "25%", "25%", "10%"],
            cell_templates: [null, null, null, null],
            sortable: [true, true, true, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDocumentiFieldColumnNames(
        response,
        t,
        handlePrintDocumenti,
        handleEditDocument,
        handleDeleteDocument,
        onRefreshData,
        handleCopyToPratica
    );
};

export const mapDocumentiFieldColumnNames = (
    response: any,
    _t: any,
    handlePrintDocumenti: any,
    handleEditDocument?: any,
    handleDeleteDocument?: any,
    onRefreshData?: any,
    handleCopyToPratica?: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = {};
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "Azioni") {
                returnColumn.renderCell = (row: any) => {
                    return documentiIconField({
                        row,
                        handlePrintDocumenti,
                        handleEditDocument,
                        handleDeleteDocument,
                        onRefreshData,
                        handleCopyToPratica,
                    });
                };
            } else {
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

// Documenti Field
const documentiIconField = ({
    row,
    handlePrintDocumenti,
    handleEditDocument,
    handleDeleteDocument,
    onRefreshData,
    handleCopyToPratica,
}: {
    row: any;
    handlePrintDocumenti: any;
    handleEditDocument?: any;
    handleDeleteDocument?: any;
    onRefreshData?: any;
    handleCopyToPratica?: any;
}) => {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
    const [shouldDelete, setShouldDelete] = useState(false);
    const open = Boolean(anchorEl);

    const deleteDocumentResponse = useDeleteDocument({
        id: row?.row?.id,
        remove: shouldDelete,
    });

    useEffect(() => {
        if (deleteDocumentResponse.hasLoaded && shouldDelete) {
            if (deleteDocumentResponse.error) {
            } else {
                if (onRefreshData) {
                    onRefreshData();
                }
                else if (handleDeleteDocument) {
                    handleDeleteDocument(row?.row?.id);
                }
            }
            setShouldDelete(false);
            setDeleteConfirmOpen(false);
        }
    }, [deleteDocumentResponse.hasLoaded, deleteDocumentResponse.error, shouldDelete, onRefreshData, handleDeleteDocument, row?.row?.id]);

    const handleMenuClick = (event: React.MouseEvent<SVGSVGElement>) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget as unknown as HTMLElement);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
    };

    const handleDownloadFromMenu = (e: React.MouseEvent) => {
        e.stopPropagation();
        handlePrintDocumenti(row?.row?.id, row?.row?.nomefile);
        handleMenuClose();
    };

    const handleDeleteClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        setDeleteConfirmOpen(true);
        handleMenuClose();
    };

    const handleConfirmDelete = () => {
        if (!row?.row?.id) return;
        setShouldDelete(true);
    };

    const handleCancelDelete = () => {
        setDeleteConfirmOpen(false);
    };

    const handleCopyToPraticaClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (handleCopyToPratica) {
            handleCopyToPratica(row?.row?.id, row?.row?.nomefile);
        }
        handleMenuClose();
    };

    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                gap: 1,
                width: "100%"
            }}
        >
            {handleEditDocument && (
                <FontAwesomeIcon
                    icon={faPenToSquare}
                    style={{ color: "hsl(200, 100%, 42%)", padding: "10px", cursor: "pointer" }}
                    onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleEditDocument(row?.row?.id, row?.row?.nomefile);
                    }}
                />
            )}
            <FontAwesomeIcon
                icon={faEllipsisVertical}
                style={{ color: "hsl(200, 100%, 42%)", padding: "10px", cursor: "pointer" }}
                onClick={handleMenuClick}
            />
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <MenuItem onClick={handleDownloadFromMenu}>
                    Scarica
                </MenuItem>
                <MenuItem onClick={handleDeleteClick}>
                    Elimina
                </MenuItem>
                <MenuItem onClick={handleCopyToPraticaClick}>
                    Copia in pratica
                </MenuItem>
            </Menu>

            <ConfirmModal
                open={deleteConfirmOpen}
                title="Vuoi continuare?"
                confirmText="Eliminare definitivamente il file?"
                handleDecline={handleCancelDelete}
                handleAgree={handleConfirmDelete}
                decline="Annulla"
                agree={deleteDocumentResponse.loading ? "Eliminando..." : "Elimina"}
                loading={deleteDocumentResponse.loading}
                colorConfirmButton="error"
                dividerVariant="alert"
            />
        </Box>
    );
};
