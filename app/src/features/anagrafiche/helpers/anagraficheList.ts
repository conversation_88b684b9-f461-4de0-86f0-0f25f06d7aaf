export function formatDate(date: Date) {
    if (!date) {
        return null;
    }
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
}

export const updateParams = (params: any, location: any) => {
    const {
        startAnagraficheSearchValue,
        endAnagraficheSearchValue,
        ...restParams
    } = params;

    if (
        startAnagraficheSearchValue &&
        startAnagraficheSearchValue instanceof Date
    ) {
        restParams.startAnagraficheSearch = formatDate(
            endAnagraficheSearchValue
        );
    }

    if (
        endAnagraficheSearchValue &&
        endAnagraficheSearchValue instanceof Date
    ) {
        restParams.endAnagraficheSearch = formatDate(endAnagraficheSearchValue);
    }

    if (params.searchTagsLike) {
        restParams.searchTagsLike = "on";
    }
    if (params.showAdvanceDynamic) {
        restParams.showAdvanceDynamic = "on";
    }

    if (location) {
        const searchParams = new URLSearchParams(location.search);
        const nome = searchParams.get("nome");
        if (nome) {
            restParams.searchField = nome;
        }
    }

    restParams.searchAnagraficheTypes = restParams?.searchAnagraficheTypes
        .map((value: any) => value?.value)
        .join(",");
    restParams.searchCategoriesArray = restParams?.searchCategoriesArray
        .map((value: any) => value?.value)
        .join(",");
    restParams.searchTagsArray = restParams?.searchTagsArray
        .map((value: any) => value?.value)
        .join(",");
    restParams.searchRelationArray = restParams?.searchRelationArray
        .map((value: any) => value?.value)
        .join(",");

    return restParams;
};
export const updateParamsPratiche = (params: any, location: any) => {
    const {
        startAnagraficheSearchValue,
        endAnagraficheSearchValue,
        ...restParams
    } = params;

    if (
        startAnagraficheSearchValue &&
        startAnagraficheSearchValue instanceof Date
    ) {
        restParams.startAnagraficheSearch = formatDate(
            endAnagraficheSearchValue
        );
    }

    if (
        endAnagraficheSearchValue &&
        endAnagraficheSearchValue instanceof Date
    ) {
        restParams.endAnagraficheSearch = formatDate(endAnagraficheSearchValue);
    }

    if (params.searchTagsLike) {
        restParams.searchTagsLike = "on";
    }
    if (params.showAdvanceDynamic) {
        restParams.showAdvanceDynamic = "on";
    }

    if (location) {
        const searchParams = new URLSearchParams(location.search);
        const nome = searchParams.get("nome");
        if (nome) {
            restParams.searchField = nome;
        }
    }

    

    return restParams;
};
