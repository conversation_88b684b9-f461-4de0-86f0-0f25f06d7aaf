import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useEffect, useState, useCallback, useRef, useMemo } from "react";
import { useTranslation } from "@1f/react-sdk";
import { getDocumentiGrid } from "../helpers/documentiGridColumn";

interface QueryParams {
    [key: string]: any;
}

export const useDocumentiHook = (query: QueryParams, setTrasferimentoModalOpen?: (open: boolean) => void, setSelectedFileForTransfer?: (file: any) => void) => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<any>({
        totalRows: 0,
        currentPage: [],
    });
    const [previewOpen, setPreviewOpen] = useState(false);
    const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState("")
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [selectedFileData, setSelectedFileData] = useState<any>(null);
    const [editLoading, setEditLoading] = useState(false);

    // Use ref to store current list to avoid stale closures
    const listRef = useRef(list);

    // Add a ref to prevent multiple simultaneous calls
    const isLoadingRef = useRef(false);

    // Update ref whenever list changes
    useEffect(() => {
        listRef.current = list;
    }, [list]);

    // Memoize the query to prevent unnecessary recreations
    const memoizedQuery = useMemo(() => query, [
        query.peopleUniqueid,
        query.viewType,
        query.page,
        query.pageSize,
        query.sortColumn,
        query.sortOrder
    ]);

    const documentiRequest = useGetCustom("peopleuploadedfiles/list?noTemplateVars=true", memoizedQuery);
    const updateFileRequest = usePostCustom("anagrafiche/update-file");
    const { handlePrintDocumenti } = usePrintDocumenti();

    // Create a stable reference for documentiRequest to avoid stale closures
    const documentiRequestRef = useRef(documentiRequest);
    documentiRequestRef.current = documentiRequest;

    // Create a stable reference for handleEditDocument using useRef
    const handleEditDocumentRef = useRef<(id: any, nome: any) => void>();

    handleEditDocumentRef.current = (id: any, _nome: any) => {
        // Use ref to get the current list to avoid stale closure
        const currentList = listRef.current?.currentPage || [];
        const document = currentList.find((doc: any) => doc.id === id);
        if (document) {
            setSelectedFileData(document);
            setEditModalOpen(true);
        }
    };

    const handleEditDocument = useCallback((id: any, nome: any) => {
        handleEditDocumentRef.current?.(id, nome);
    }, []); // Stable reference

    // Create a stable reference for handleCopyToPratica using useRef
    const handleCopyToPraticaRef = useRef<(id: any, nome: any) => void>();

    handleCopyToPraticaRef.current = (id: any, _nome: any) => {
        // Use ref to get the current list to avoid stale closure
        const currentList = listRef.current?.currentPage || [];
        const document = currentList.find((doc: any) => doc.id === id);
        if (document && setTrasferimentoModalOpen && setSelectedFileForTransfer) {
            setSelectedFileForTransfer(document);
            setTrasferimentoModalOpen(true);
        }
    };

    const handleCopyToPratica = useCallback((id: any, nome: any) => {
        handleCopyToPraticaRef.current?.(id, nome);
    }, []); // Stable reference

    // Create a refresh function that doesn't depend on initDocumenti
    const refreshData = useCallback(async () => {
        // Prevent multiple simultaneous calls
        if (isLoadingRef.current) {
            return;
        }

        try {
            isLoadingRef.current = true;
            setLoading(true);
            const { data }: any = await documentiRequestRef.current.doFetch(true);

            if (data) {
                setList({
                    totalRows: data.totalRows || 0,
                    currentPage: data.currentPage || [],
                });
            } else {
                setList({
                    totalRows: 0,
                    currentPage: [],
                });
            }
        } catch (error) {
            setList({
                totalRows: 0,
                currentPage: [],
            });
        } finally {
            setLoading(false);
            isLoadingRef.current = false;
        }
    }, []); // Remove documentiRequest dependency to prevent recreations

    const handleSaveEdit = useCallback(async (editData: { description: string; date: string }) => {
        if (!selectedFileData) return;

        try {
            setEditLoading(true);

            const formData = new FormData();
            formData.append("id", selectedFileData.id);
            formData.append("peopleId", query.peopleUniqueid);
            formData.append("titolodocumento", editData.description);
            formData.append("datadoc", editData.date);

            const response: any = await updateFileRequest.doFetch(true, formData);

            if (response?.data) {
                setEditModalOpen(false);
                setSelectedFileData(null);
                await refreshData();
            }
        } catch (error) {
        } finally {
            setEditLoading(false);
        }
    }, [selectedFileData, query.peopleUniqueid, updateFileRequest, refreshData]);

    const handleCloseEdit = useCallback(() => {
        setEditModalOpen(false);
        setSelectedFileData(null);
    }, []);

    // Initialize columns once when the component mounts
    useEffect(() => {
        const initColumns = async () => {
            try {
                const finalColumns = await getDocumentiGrid(
                    t,
                    handlePrintDocumenti,
                    handleEditDocument,
                    undefined, // handleDeleteDocument
                    refreshData, // onRefreshData - refresh data after delete
                    handleCopyToPratica
                );
                setColumns(finalColumns);
            } catch (error) {
                setColumns([]);
            }
        };

        initColumns();
    }, [t]); // Only depend on translation function to avoid infinite loops

    const initDocumenti = useCallback(async () => {
        // Prevent multiple simultaneous calls
        if (isLoadingRef.current) {
            return;
        }

        try {
            isLoadingRef.current = true;
            setLoading(true);

            const { data }: any = await documentiRequestRef.current.doFetch(true);

            if (data) {
                setList({
                    totalRows: data.totalRows || 0,
                    currentPage: data.currentPage || [],
                });
            } else {
                setList({
                    totalRows: 0,
                    currentPage: [],
                });
            }
        } catch (error) {
            setList({
                totalRows: 0,
                currentPage: [],
            });
        } finally {
            setLoading(false);
            isLoadingRef.current = false;
        }
    }, []); // Remove documentiRequest dependency to prevent recreations

    useEffect(() => {
        initDocumenti();
    }, [memoizedQuery]); // Depend on memoized query to prevent unnecessary calls

    return {
        initDocumenti,
        t,
        columns,
        list,
        loading,
        previewOpen,
        setPreviewOpen,
        selectedDocumentId,
        setSelectedDocumentId,
        selectedFile,
        setSelectedFile,
        editModalOpen,
        setEditModalOpen,
        selectedFileData,
        setSelectedFileData,
        handleEditDocument,
        handleSaveEdit,
        handleCloseEdit,
        editLoading,
        handleCopyToPratica,
    };
};

const usePrintDocumenti = () => {
    const printDocumenti = useGetCustom(`anagrafiche/get-file`, {}, null, true);

    const handlePrintDocumenti = async (id?: any, fileName?: any) => {
        const response: any = await printDocumenti.doFetch(true, { id });

        const fileExtension = fileName?.split('.').pop()?.toLowerCase() || '';

        const blob = new Blob([response.data], { type: `text/${fileExtension}` });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `${fileName}`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return { handlePrintDocumenti }
};