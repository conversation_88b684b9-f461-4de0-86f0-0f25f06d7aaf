import usePostCustom from "../../../hooks/usePostCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import { useState } from "react";
import { checkData } from "../utils/validation-check";
export default function useSaveAnagrafiche(id?: string | undefined | null) {
    const { fetchAnagrafiche } = useAnagraficheProvider();

    const [mandatoryFields, setMandatoryFields] = useState<any>({});
    const [optionalFields, setOptionalFields] = useState<any>({});
    const url: string = id
        ? `anagrafiche/update?id=${id}`
        : "anagrafiche/update";

    const submitRequest = usePostCustom(url);

    const checkDataRequest = usePostCustom("/anagrafiche/checkData?noTemplateVars=true");


    const onSubmitCreate = async (data: any) => {
        const checkDataResponse: any = await checkData(checkDataRequest, data);
        const { dyphone_7, ...restValidData } = checkDataResponse?.mandatory || {};
        setMandatoryFields(restValidData);
        setOptionalFields(checkDataResponse?.optional || {});
        if (checkDataResponse?.mandatory && Object.keys(checkDataResponse.mandatory).length > 0) {
            return {
                status: "error",
                message: "Mandatory fields are missing",
                mandatoryFields: checkDataResponse.mandatory,
                optionalFields: checkDataResponse.optional
            }
        }
        const transformAddresses = (addresses: any[]) => {
            const result: any = {};
            addresses.forEach((address, index) => {
                Object.keys(address).forEach((key: string) => {
                    if (address.address_id !== null) {
                        result[`dyaddress_${address.address_id}_${key}`] =
                            address[key] || "";
                    } else {
                        result[`dyaddress_${index}_${key}`] =
                            address[key] || "";
                    }
                });
            });
            return result;
        };

        const transformContacts = (contacts: any[]) => {
            const result: any = {};
            contacts.forEach((contact) => {
                Object.keys(contact).forEach((key: string) => {
                    if (key === "nome_campo") {
                        result[`dyphone_${contact.nome_campo}`] =
                            contact["valore"] || "";
                    } else {
                        result["contact"] = contact["nome"] || "";
                    }
                });
            });
            return result;
        };

        if (Array.isArray(data.addresses)) {
            const transformedAddresses = transformAddresses(data.addresses);
            data = { ...data, ...transformedAddresses };
            delete data.addresses;
        }
        if (Array.isArray(data.contacts)) {
            const transformedContacts = transformContacts(data.contacts);
            data = { ...data, ...transformedContacts };
            delete data.contacts;
        }
        data.title = data.titolo;
        if (data.subjectName && data.subjectSurname) {
            data.nome = `${data.subjectName} ${data.subjectSurname}`;
        }
        if (data.formula_saluti) {
            data.formula = data.formula_saluti;
        }

        if (!data.categoria) {
            data.categoria = "-1";
        }

        const response: any = await submitRequest.doFetch(true, data);
        if (response) {
            fetchAnagrafiche(); // update data after submit
        }

        return {
            status: response?.status,
            anagraficaUuid: response?.data?.anagraficaUuid,
        };
    };

    return { onSubmitCreate, mandatoryFields, optionalFields, checkData };
}
