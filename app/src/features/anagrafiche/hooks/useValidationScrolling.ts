import { useEffect, useState, useCallback } from "react";
import { checkData } from "../utils/validation-check";
import usePostCustom from "../../../hooks/usePostCustom";

interface UseValidationScrollingProps {
    method: any;
    refs: {
        datiGeneraliRef: React.RefObject<HTMLDivElement>;
        noteprofilazioneRefs: React.RefObject<HTMLDivElement>;
        fiscaliBancheRefs: React.RefObject<HTMLDivElement>;
        parentRef: React.RefObject<HTMLDivElement>;
    };
    showCreateForm?: {
        datiGenerali: boolean;
        recapiti: boolean;
        indirizzo: boolean;
        contactiCollegati: boolean;
        noteProfilazione: boolean;
        cameraDiCommercio: boolean;
        fiscaliBanche: boolean;
    };
    setShowCreateForm?: React.Dispatch<React.SetStateAction<any>>;
    isUpdate?: boolean;
    isOnCreate?: boolean;
    isSociety?: boolean;
}

const checkIfFormIsOpen = (showCreateForm: any) => {
    return Object.values(showCreateForm).some((isOpen: any) => isOpen === true);
}

export const useValidationScrolling = ({
    method,
    refs,
    showCreateForm,
    setShowCreateForm,
    isOnCreate,
    isSociety,
}: UseValidationScrollingProps) => {

    const [mandatoryFields, setMandatoryFields] = useState<any>({});
    const [previousMandatoryFields, setPreviousMandatoryFields] = useState<any>({});
    const [validationErrors, setValidationErrors] = useState<any>({});

    const checkDataRequest = usePostCustom("/anagrafiche/checkData?noTemplateVars=true");
    const values = method.watch();
    const { setError, clearErrors } = method;

    const { datiGeneraliRef, noteprofilazioneRefs, fiscaliBancheRefs, parentRef } = refs;


    const scrollToDatiGenerale = () => {
        if (parentRef.current && datiGeneraliRef.current) {
            const parent = parentRef.current;
            const element = datiGeneraliRef.current;
            const parentTop = parent.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop - 50;
            window.scrollTo({ top: scrollTo, behavior: "smooth" });
        }
    };

    const scrollToFiscaliBanche = () => {
        if (parentRef.current && fiscaliBancheRefs.current) {
            const parent = parentRef.current;
            const element = fiscaliBancheRefs.current;
            const parentTop = parent.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop - 50;
            window.scrollTo({ top: scrollTo, behavior: "smooth" });
        }
    };

    const scrollToNoteProfilazione = () => {
        if (parentRef.current && noteprofilazioneRefs.current) {
            const parent = parentRef.current;
            const element = noteprofilazioneRefs.current;
            const parentTop = parent.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop - 50;
            window.scrollTo({ top: scrollTo, behavior: "smooth" });
        }
    };


    const autoOpenFormsForMandatoryFields = (mandatoryFieldsObj: any) => {
        if (!mandatoryFieldsObj || Object.keys(mandatoryFieldsObj).length === 0) {
            return;
        }

        const newShowCreateForm = { ...showCreateForm };


        if (mandatoryFieldsObj.partitaiva || mandatoryFieldsObj.codicefiscale ||
            mandatoryFieldsObj.nome || mandatoryFieldsObj.subjectName || mandatoryFieldsObj.subjectSurname) {
            newShowCreateForm.datiGenerali = true;
        }


        if (mandatoryFieldsObj.codiceb2b) {
            newShowCreateForm.fiscaliBanche = true;
        }

        setShowCreateForm && setShowCreateForm(newShowCreateForm);
    };



    const debouncedScrollAfterCodiceFiscale = useCallback(
        debounce(() => {
            if (!checkIfFormIsOpen(showCreateForm)) {
                return;
            }
            if (values.partitaiva && values.codicefiscale && !values.codiceb2b) {
                scrollToFiscaliBanche();
            } else if (values.partitaiva && values.codicefiscale && values.codiceb2b) {
                scrollToNoteProfilazione();
            }
        }, 1000),
        [values.partitaiva, values.codicefiscale, values.codiceb2b]
    );


    const validateBasicMandatoryFields = () => {
        const basicErrors: any = {};


        if (isSociety) {
            if (!values.nome) {
                basicErrors.nome = "Denominazione obbligatoria";
            }
        } else {
            if (!values.subjectName) {
                basicErrors.subjectName = "Nome obbligatorio";
            }
            if (!values.subjectSurname) {
                basicErrors.subjectSurname = "Cognome obbligatorio";
            }
        }

        return basicErrors;
    };


    const checkValidation = async (skipBasicValidation = false) => {
        let allErrors: any = {};

        if (!skipBasicValidation) {
            const basicErrors = validateBasicMandatoryFields();
            allErrors = { ...allErrors, ...basicErrors };
        }


        if (values.categoria) {
            const response: any = await checkData(checkDataRequest, values);
            setPreviousMandatoryFields(mandatoryFields);
            const categoryMandatoryFields = response?.mandatory || {};
            setMandatoryFields(categoryMandatoryFields);
            allErrors = { ...allErrors, ...categoryMandatoryFields };
        }

        setValidationErrors(allErrors);
        return allErrors;
    };


    const canSave = async () => {
        const allErrors = await checkValidation();
        return Object.keys(allErrors).length === 0;
    };


    const handleSaveAttempt = async () => {
        const allErrors = await checkValidation();

        if (Object.keys(allErrors).length > 0) {

            Object.keys(allErrors).forEach((fieldName) => {
                setError(fieldName, {
                    type: "manual",
                    message: allErrors[fieldName] === true ? "Compilare i campi obbligatori" : allErrors[fieldName]
                });
            });


            autoOpenFormsForMandatoryFields(allErrors);


            setTimeout(() => {
                if (allErrors.partitaiva || allErrors.codicefiscale ||
                    allErrors.nome || allErrors.subjectName || allErrors.subjectSurname) {
                    scrollToDatiGenerale();
                } else if (allErrors.codiceb2b) {
                    scrollToFiscaliBanche();
                }
            }, 100);

            return false;
        }

        return true;
    };


    useEffect(() => {
        if (showCreateForm && checkIfFormIsOpen(showCreateForm) || isOnCreate) {
            checkValidation(true);
        }
    }, [values.categoria]);


    useEffect(() => {
        if (mandatoryFields && Object.keys(mandatoryFields).length > 0) {
            Object.keys(mandatoryFields).forEach((fieldName) => {
                setError(fieldName, {
                    type: "manual",
                    message: mandatoryFields[fieldName] === true ? "Compilare i campi obbligatori" : mandatoryFields[fieldName]
                });
            });


            autoOpenFormsForMandatoryFields(mandatoryFields);

            if (mandatoryFields.partitaiva || mandatoryFields.codicefiscale) {
                setTimeout(() => scrollToDatiGenerale(), 100);
            } else if (mandatoryFields.codiceb2b) {
                setTimeout(() => scrollToFiscaliBanche(), 100);
            }
        }
        if (values.partitaiva) {
            clearErrors(["partitaiva"]);
        }

        if (values.codicefiscale) {
            clearErrors(["codicefiscale"]);
        }

        if (values.codiceb2b) {
            clearErrors(["codiceb2b"]);
        }

    }, [mandatoryFields, previousMandatoryFields, values.partitaiva, values.codicefiscale, values.codiceb2b]);

    useEffect(() => {
        if (values.codicefiscale && values.codicefiscale.trim() !== '') {
            debouncedScrollAfterCodiceFiscale();
        }
    }, [values.codicefiscale, debouncedScrollAfterCodiceFiscale]);

    return {
        mandatoryFields,
        validationErrors,
        scrollToDatiGenerale,
        scrollToFiscaliBanche,
        scrollToNoteProfilazione,
        canSave,
        handleSaveAttempt,
        checkValidation
    };
};


function debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func(...args), delay);
    };
} 