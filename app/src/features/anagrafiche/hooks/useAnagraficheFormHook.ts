import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import useGetAnagraficheActionData from "./useGetAnagraficheActionData";
import useGetCustom from "../../../hooks/useGetCustom";
import { DEFAULT_ANAGRAFICHE_PARAMS } from "../constants/defaultAnagraficheParams";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";

const validationSchema1 = yup.object().shape({
    subjectSurname: yup.string().required("Cognome obbligatorio"),
    subjectName: yup.string().required("Nome obbligatorio"),
});

const validationSchema2 = yup.object().shape({
    nome: yup.string().required("Denominazione obbligatoria"),
});

interface ShowCreateFormType {
    datiGenerali: boolean;
    recapiti: boolean;
    indirizzo: boolean;
    contactiCollegati: boolean;
    noteProfilazione: boolean;
    cameraDiCommercio: boolean;
    fiscaliBanche: boolean;
    antiriciclaggio: boolean;
    gruppi: boolean;
    campiDinamici: boolean;
}

const isChecked = (val: any): boolean => {
    return val === 1 || val === "1" || val === true || val === "true" || val === "on" || val === "ON";
};

export const useAnagraficheFormHook = (peopleData?: any, isUpdateBool?: boolean) => {
    const { anagraficheData, isUpdate } = (!peopleData || typeof isUpdateBool === "undefined")
        ? useGetAnagraficheActionData()
        : { anagraficheData: peopleData, isUpdate: isUpdateBool };
    const { province, contacts } = anagraficheData;
    const [firstStepCompleted, setFirstStepCompleted] =
        useState<boolean>(false);
    const [showCreateForm, setShowCreateForm] = useState<ShowCreateFormType>({
        datiGenerali: false,
        recapiti: false,
        indirizzo: false,
        contactiCollegati: false,
        noteProfilazione: false,
        cameraDiCommercio: false,
        fiscaliBanche: false,
        antiriciclaggio: false,
        gruppi: false,
        campiDinamici: false,
    });
    const [userType, setUserType] = useState<{
        type: string;
        icon: null | any;
        details: any;
        detailSelected: string;
        parentSelected: string;
    }>({
        icon: null,
        type: "",
        details: [],
        detailSelected: "",
        parentSelected: "",
    });

  
    const [dynamicFields, setDynamicFields] = useState<any[]>([]);
    const [dateStates, setDateStates] = useState<{ [key: string]: Date | null }>({});
    const [isLoadingFields, setIsLoadingFields] = useState<boolean>(false);

    const schemaToUse = userType?.type !== CUSTOMER_TYPE.Person ? validationSchema2 : validationSchema1;

    const form = useForm({
        resolver: yupResolver<any>(schemaToUse),
        defaultValues: DEFAULT_ANAGRAFICHE_PARAMS,
    });

    const {
        handleSubmit,
        control,
        register,
        setValue,
        watch,
        setError,
        formState: { errors },
    } = form;

    const { additional_addresses, additional_phones } = anagraficheData;
    const values = watch();

    const loadCampiDinamiciRequest = useGetCustom(
        "campidinamici/loaddynamicfields?noTemplateVars=true"
    );

    const newData = anagraficheData?.person
        ? Object.keys(anagraficheData.person).length > 0
        : 0;

    useEffect(() => {
        if (
            isUpdate &&
            anagraficheData?.person &&
            Object.keys(anagraficheData.person).length > 0
        ) {
            setPersonValues();
        }
    }, [isUpdate, newData]);

    useEffect(() => {
        if (values.id || values.categoria) {
            loadCampiDinamici();
        }
    }, [values.id, values.categoria]);

    const parseDateString = (dateString: string) => {
        if (!dateString) return null;
        if (dateString.includes('-') && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateString.split("-").map(Number);
            return new Date(year, month - 1, day);
        }
        if (dateString.includes('/')) {
            const [day, month, year] = dateString.split("/").map(Number);
            return new Date(year, month - 1, day);
        }
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    };

    const loadCampiDinamici = async () => {
        try {
            setIsLoadingFields(true);
            const response: any = await loadCampiDinamiciRequest.doFetch(true, {
                categoryID: values.categoria,
                eventID: values.id,
                table: "anagrafiche",
            });

            if (response?.data) {
                setDynamicFields(response.data);

                const initialDateStates: { [key: string]: Date | null } = {};
                response.data.forEach((field: any) => {
                    const fieldName = `field_${field.id}`;

                    if (field.tipo === 'date' && field.restore) {
                        const parsedDate = parseDateString(field.restore);
                        initialDateStates[fieldName] = parsedDate;
                        setValue(fieldName, field.restore);
                    } else if (field.tipo === 'checkbox') {
                        setValue(fieldName, isChecked(field.restore) ? "1" : "0");
                    } else {
                        setValue(fieldName, field.restore || '');
                    }
                });

                setDateStates(initialDateStates);
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoadingFields(false);
        }
    };

    const parseSelectOptions = (optString: string) => {
        if (!optString) return [];
        try {
            const parsed = JSON.parse(optString);
            if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
                return Object.entries(parsed).map(([key, value]) => ({
                    label: key.trim(),
                    value,
                }));
            }
            if (Array.isArray(parsed)) {
                return parsed.map((item) => ({
                    value: typeof item === 'object' ? item.value || item.id : item,
                    label: typeof item === 'object' ? item.label || item.name || item.text : item,
                }));
            }
        } catch (e) {
            console.log({ e });
        }
        if (optString.includes(',')) {
            return optString.split(',').map((item) => {
                const trimmed = item.trim();
                if (trimmed.includes(':')) {
                    const [value, label] = trimmed.split(':');
                    return { value: value.trim(), label: label.trim() };
                }
                return { label: trimmed, value: trimmed };
            });
        }
        if (optString.includes('|')) {
            return optString.split('|').map((item) => {
                const trimmed = item.trim();
                if (trimmed.includes(':')) {
                    const [value, label] = trimmed.split(':');
                    return { value: value.trim(), label: label.trim() };
                }
                return { value: trimmed, label: trimmed };
            });
        }
        return [{ value: optString, label: optString }];
    };

    const validateRegex = (value: string, regexPattern: string) => {
        if (!regexPattern || !value) return true;
        try {
            let cleanPattern = regexPattern
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/\\\./g, '\\.')
                .replace(/\\d/g, '\\d')
                .replace(/\\'/g, "'");
            const regex = new RegExp(cleanPattern);
            return regex.test(value);
        } catch (error) {
            console.error('Invalid regex pattern:', regexPattern, error);
            return true;
        }
    };

    const setPersonValues = () => {
        Object.keys(anagraficheData?.person).forEach((key: string) => {
            setValue(key, anagraficheData.person[key]);
            if (key === "aui") {
                setAuiValues(anagraficheData.person[key]);
            }
        });
        setNoteProfilazioneValues(anagraficheData.person);
        setRecapitiContacts();
        setRecapitiPhones();
    };

    const setRecapitiPhones = () => {
        const arrayData = Object.entries(additional_phones).map(
            ([key, value]: any[]) => {
                const contactData: any = contacts.find(
                    (item: any) => item.nome === value.nome_campo
                );
                return {
                    contact_id: key,
                    nome_campo: contactData?.id,
                    valore: value.valore,
                    nome: contactData?.nome,
                };
            }
        );
        setValue("contacts", arrayData);
    };

    const setRecapitiContacts = () => {
        const arrayData = Object.entries(additional_addresses).map(
            ([key, value]: any[]) => {
                const provinceId: any = province?.find(
                    (item: any) => item?.iniziali === value?.provincia
                );

                return {
                    address_id: key,
                    nome_campo: value.nome_campo,
                    provincia: provinceId?.id,
                    via: value.valore.via,
                    cap: value.valore.cap,
                    citta: value.valore.citta,
                    nazione: value.valore.nazione,
                    regione: value.valore.regione,
                };
            }
        );
        setValue("addresses", arrayData);
    };

    const setAuiValues = (auiData: any) => {
        if (auiData) {
            Object.keys(auiData).forEach((auiKey: string) => {
                setValue(auiKey, auiData[auiKey]);
                if (auiKey === "data_rilascio") {
                    setValue(auiKey, auiData[auiKey]);
                }
            });
        }
    };

    const setNoteProfilazioneValues = (noteData: any) => {
        Object.keys(noteData).forEach((key) => {
            if (key === "codice_esterno") {
                setValue("externalcode", anagraficheData.person.codice_esterno);
            }
        });
    };

    return {
        handleSubmit,
        control,
        register,
        setValue,
        watch,
        setError,
        errors,
        method: form,
        showCreateForm,
        setShowCreateForm,
        firstStepCompleted,
        setFirstStepCompleted,
        userType,
        setUserType,
        // Dynamic fields exports
        dynamicFields,
        dateStates,
        setDateStates,
        isLoadingFields,
        loadCampiDinamici,
        parseSelectOptions,
        validateRegex,
        isChecked,
    };
};
