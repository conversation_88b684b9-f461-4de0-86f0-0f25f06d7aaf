import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { getFatturazioneGrid} from "../helpers/fatturazioneGridColumn";

interface QueryParams {
    [key: string]: any;
}

export const useFatturazioneHook = (query: QueryParams) => {
    const { t } = useTranslation();
    const [feeStatus, setFeeStatus] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<any>({
        totalRows: 0,
        currentPage: [],
    });

    const peopleprefees = useGetCustom("peopleprefees/list?noTemplateVars=true", {});
    const peoplestandardfees = useGetCustom("peoplestandardfees/list?noTemplateVars=true", {})
    const peoplefees = useGetCustom("peoplefees/list?noTemplateVars=true", {})
    const peoplecreditnotes = useGetCustom("peoplecreditnotes/list?noTemplateVars=true", {})
    const preavisiGetData = useGetCustom(`peopleprefees/fees?stateless=1&id=${query?.peopleId}`, {})

    const fetchFatturazione = async () => {
        try {
            if (
                query?.tipologia === "preavvisiDiParcella"
            ) {
            try {
                setLoading(true);


                const finalColumns = await getFatturazioneGrid(t, "peopleprefees");
                setColumns(finalColumns);

                if(feeStatus.length === 0){
                    const preavvisiData:any = await preavisiGetData.doFetch(true, {});
                    setFeeStatus(preavvisiData?.data?.feeNoticeStatus);
                }
                
                const { data }: any = await peopleprefees.doFetch(true, query);
                setList({
                    totalRows: data?.totalRows,
                    currentPage: data?.currentPage,
                });
                } catch (error) {
                    console.error("Error loading data:", error);
                } 
            } else if (query?.tipologia === "fatture") {
                const finalColumns = await getFatturazioneGrid(t, "peoplestandardfees");
                setColumns(finalColumns);

                setLoading(true);
                const { data }: any = await peoplestandardfees.doFetch(true, query);
                setList({
                    totalRows: data?.totalRows,
                    currentPage: data?.currentPage,
                });
            } else if (query?.tipologia === "fattureElettroniche") {
                const finalColumns = await getFatturazioneGrid(t, "peoplefees");
                setColumns(finalColumns);

                setLoading(true);
                const { data }: any = await peoplefees.doFetch(true, query);
                setList({
                    totalRows: data?.totalRows,
                    currentPage: data?.currentPage,
                });
            } else if (query?.tipologia === "noteDiCreditoElettroniche") {
                const finalColumns = await getFatturazioneGrid(t, "peoplecreditnotes");
                setColumns(finalColumns);

                setLoading(true);
                const { data }: any = await peoplecreditnotes.doFetch(true, query);
                setList({
                    totalRows: data?.totalRows,
                    currentPage: data?.currentPage,
                });
            }
        } catch (error) {
            console.error("Error loading fatturazione grid:", error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchFatturazione();
    }, [query]);

    return {
        fetchFatturazione,
        t,
        columns,
        list,
        loading,
        feeStatus
    };
};