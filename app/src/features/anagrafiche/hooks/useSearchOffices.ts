import { useCallback, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { debounce } from "lodash";

export default function useSearchOffices() {
    const searchOfficeRequest = useGetCustom(
        "default/fatturepa/search-offices?noTemplateVars=true"
    );

    const [officeResult, setOfficeResult] = useState<any[]>([]);
    const [officeNameInput, setOfficeNameInput] = useState("");
    const [increment, setIncrement] = useState(0); // Track pagination

    const debounceSearchOffices = debounce(async (value: string, inc = 0) => {
        let params = { q: value, increment: inc.toString() };
        const response: any = await searchOfficeRequest.doFetch(true, params);
        let results = response.data || [];
        setOfficeResult(results);
        setIncrement(inc);
    }, 500);

    const memoizedSearchOffices = useCallback(debounceSearchOffices, []);

    const handleOfficeSearch = (value: string, inc = 0) => {
        memoizedSearchOffices(value, inc);
    };

    return {
        handleOfficeSearch,
        officeResult,
        officesLoading: searchOfficeRequest.loading,
        officeNameInput,
        setOfficeNameInput,
        increment,
    };
}
