import useGetCustom from "../../../hooks/useGetCustom";
import { debounce } from "lodash";
import { useEffect, useCallback, useState } from "react";
import useGetAnagraficheActionData from "./useGetAnagraficheActionData";

export default function useAnagraficheRelationships() {
    const searchAnagraficheRequest = useGetCustom(
        "default/anagrafiche/search?noTemplateVars=true"
    );

    const searchUsersRequest = useGetCustom(
        "default/users/search?noTemplateVars=true"
    );

    // const listRelationshipsDataRequest = useGetCustom(
    //     "anagraficherelationships/list?noTemplateVars=true"
    // );

    const { anagraficheData, isUpdate } = useGetAnagraficheActionData(); //data to populate the menus
    const { person } = anagraficheData;

    const [searchAnagraficheResult, setSearchAnagraficheResult] = useState<
        any[]
    >([]);
    const [searchUsersResult, setSearchUsersResult] = useState<any[]>([]);
    const [contactData, setContactData] = useState<any>([]);

    //SEARCH ANAGRAFICHE
    const debounceSearchAnagrafiche = debounce(async (value: string) => {
        let params = {
            q: value,
        };
        const response: any = await searchAnagraficheRequest.doFetch(
            true,
            params
        );
        const isOkay: boolean = Array.isArray(response.data);
        setSearchAnagraficheResult(isOkay ? response.data : []);
    }, 500);

    const memoizedSearchAnagrafiche = useCallback(debounceSearchAnagrafiche, [
        debounceSearchAnagrafiche,
    ]);

    const handleAnagraficheSearch = (value: any) => {
        memoizedSearchAnagrafiche(value);
    };

    //SEARCH USERS
    const debounceSearchUsers = debounce(async (value: string) => {
        let params = {
            q: value,
            incremenet: "0",
            type: "",
        };
        const response: any = await searchUsersRequest.doFetch(true, params);
        const isOkay: boolean = Array.isArray(response.data);
        setSearchUsersResult(isOkay ? response.data : []);
    }, 500);

    const memoizedSearchUsers = useCallback(debounceSearchUsers, [
        debounceSearchUsers,
    ]);

    const handleUsersSearch = (value: any) => {
        memoizedSearchUsers(value);
    };

    // function parsingContactData(data: any) {
    //     return data.map(({ type, socio, amministratore, ...rest }: any) => ({
    //         ...rest,
    //         type,
    //         socio: socio === "0" ? false : true,
    //         amministratore: amministratore === "0" ? false : true,
    //         parent_id: person?.id,
    //     }));
    // }

    // const fetchRelationships = async () => {
    //     const params = {
    //         peopleUniqueid: person?.uniqueid || "",
    //         page: 0,
    //         pageSize: 7,
    //         sortColumn: "parent",
    //         sortOrder: "asc",
    //         searchField: "",
    //     };
    //     const response: any = await listRelationshipsDataRequest.doFetch(
    //         true,
    //         params
    //     );
    //     const { currentPage } = response.data;
    //     if (currentPage !== undefined) {
    //         const correctData =
    //             Object.keys(currentPage).length === 0 ? [] : currentPage;
    //         const parsedData = parsingContactData(correctData);
    //         setContactData(parsedData);
    //     }
    // };

    useEffect(() => {
        // fetchRelationships();
    }, [person?.uniqueid]);

    return {
        handleAnagraficheSearch,
        handleUsersSearch,
        searchAnagraficheResult,
        contactData,
        setContactData,
        isUpdate,
        searchUsersResult,
        usersListLoading: searchUsersRequest.loading,
        anagraficheListLoading: searchAnagraficheRequest.loading,
    };
}
