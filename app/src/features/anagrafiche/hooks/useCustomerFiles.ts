import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import { getPraticheGrid } from "../grids/grids";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../interfaces/general.interfaces";
import { useUser } from "../../../store/UserStore";

export default function useCustomerFiles() {
    const { t } = useTranslation();
    const { modules }: any = useUser();

    const netlexSettingsFileId = modules?.netlexSettings?.file_id || null;

    const getDefaultParams = () => {
        let sortColumn: string | undefined;
        
        if (netlexSettingsFileId === "fileArchiveCode") {
            sortColumn = "codicearchivio";
        } else if (netlexSettingsFileId === "fileCode") {
            sortColumn = "codicepratica";
        }
        
        const params: any = {
            peopleUniqueid: "",
            page: 0,
            pageSize: 7,
            sortOrder: "asc",
            itemStartDate: "",
            itemEndDate: "",
            withItemToFeeSearch: "",
            feeCustomer: "",
            situazione: -1,
            situazioneContabile: -1,
            dateType: 0,
            startDate: "",
            endDate: "",
            searchField: "",
            status: [],
            categoryIds: [],
        };
        
        if (sortColumn) {
            params.sortColumn = sortColumn;
        }
        
        return params;
    };

    const listCustomerFilesRequest = useGetCustom(
        "customersfiles/list?noTemplateVars=true", {}
    );
    const { anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;

    const [customersFileQuery, setCustomerFileQuery] = useState({
        ...getDefaultParams(),
        peopleUniqueid: customer?.uniqueid || ""
    });


    useEffect(() => {
        if (customer?.uniqueid) {
            setCustomerFileQuery((prev: any) => ({
                ...prev,
                peopleUniqueid: customer.uniqueid
            }));
        }
    }, [customer?.uniqueid]);


    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });


  

    const fetchData = async () => {
        const [columns, response]: any = await Promise.all([
            getPraticheGrid(t, netlexSettingsFileId),
            listCustomerFilesRequest.doFetch(true, customersFileQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        setList({
            ...list,
            rows: currentPage || [],
            columns: columns,
            totalRows: parseInt(totalRows),
            page: customersFileQuery?.page,
            pageSize: customersFileQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchData();
    }, [customersFileQuery]);

    return {
        customersFileQuery,
        setCustomerFileQuery,
        loading: listCustomerFilesRequest.loading,
        list,
        fetchData,
        // handleReset
    };
}
