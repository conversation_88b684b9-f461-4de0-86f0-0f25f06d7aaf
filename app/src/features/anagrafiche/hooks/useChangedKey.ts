import { useEffect, useMemo, useRef } from "react";

export function useChangedKey(showCreateForm: Record<string, boolean>): string | null {
  // Ref to store the previous showCreateForm state
  const prevShowCreateFormRef = useRef<Record<string, boolean> | null>(null);

  // Determine which key transitioned from true to false
  const changedKey = useMemo(() => {
    const prevShowCreateForm = prevShowCreateFormRef.current;
    if (!prevShowCreateForm) return null; // No previous value available yet

    // Find the first key that was true and now is false
    return Object.keys(showCreateForm).find(
      (key) => prevShowCreateForm[key] && !showCreateForm[key]
    ) || null;
  }, [showCreateForm]);

  // Update the ref with the current value after every render
  useEffect(() => {
    prevShowCreateFormRef.current = showCreateForm;
  }, [showCreateForm]);

  return changedKey;
}
