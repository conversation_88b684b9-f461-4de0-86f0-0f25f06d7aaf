import usePostCustom from "../../../hooks/usePostCustom";
import useGetCustom from "../../../hooks/useGetCustom";

export default function useGetAddressKey() {
    const getAddressKeyRequest = usePostCustom(
        "peoplecenter/getaddresskey?noTemplateVars=true"
    );
    const getAllAddressKeyRequest = useGetCustom(
        "peoplecenter/get-all-address-keys?noTemplateVars=true"
    );

    const getAddressKeyFunction = async (id: string, value: string) => {
        const response: any = await getAddressKeyRequest.doFetch(true, {
            id,
            value,
        });
        const { data } = response;
        return data;
    };

    const getAllAddressKeyFunction = async () => {
        const response: any = await getAllAddressKeyRequest.doFetch(true, {});
        const { data } = response;
        return data;
    };

    return { getAddressKeyFunction, getAllAddressKeyFunction };
}
