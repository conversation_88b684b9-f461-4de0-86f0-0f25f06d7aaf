import React from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import moment from "moment";

const getSchemaValidation = (t: any) => {
    return yup.object().shape({
        id: yup.string(),
        descrizione: yup
            .string()
            .required(t("Il campo Descrizione non puo' essere vuoto")),
        valore: yup.string(),
        indirizzo: yup.string(),
        civico: yup.string(),
        citta: yup.string(),
        cap: yup.string(),
        prov: yup.string(),
        nazione: yup.string(),
        tipocatasto: yup.string(),
        classe: yup.string(),
        classato: yup.string(),
        unitamisura: yup.string(),
        superficie: yup.string(),
        edificio: yup.string(),
        scala: yup.string(),
        vani: yup.string(),
        interno: yup.string(),
        piani: yup.string(),
        renditadomenicale: yup.string(),
        renditaagraria: yup.string(),
        provicincia: yup.string(),
        anagrafica: yup.array(),
        daticatastali: yup.array(),
        pregiud: yup.array(),
    });
};

export const useImmobileUpdateHooks = (id: string) => {
    const { t } = useTranslation();

    const { anagrafiche } = useAnagraficheProvider();
    const method = useForm<any>({
        resolver: yupResolver(getSchemaValidation(t)),
        shouldUnregister: false,
        defaultValues: {
            id: "",
            descrizione: "",
            valore: "0.00",
            indirizzo: "",
            civico: "",
            citta: "-1",
            cap: "",
            prov: "",
            nazione: "118",
            tipocatasto: "1",
            classe: "1",
            classato: "1",
            unitamisura: "MQ",
            superficie: "0.00",
            edificio: "",
            scala: "",
            vani: "",
            interno: "",
            piani: "",
            renditadomenicale: "0.00",
            renditaagraria: "0.00",
            provicincia: "",
            anagrafica: [],
            daticatastali: [],
            pregiud: [],
        },
    });

    const { setValue } = method;

    const [rowData, setRowData] = React.useState({
        tab1Data: {},
        tab2Data: {},
        tab3Data: {},
        tab4Data: {},
    });

    const getTab1DataRequest = useGetCustom(
        `peopleproperties/getpropertydata?noTemplateVars=true`
    );
    const getTab2DataRequest = useGetCustom(
        `peoplecenter/getpropertyperson?noTemplateVars=true`
    );
    const getTab3DataRequest = useGetCustom(
        `peoplecenter/getpropertycastraldata?noTemplateVars=true`
    );
    const getTab4DataRequest = useGetCustom(
        `peoplecenter/getpropertyprejudicial?noTemplateVars=true`
    );

    const fetchData = async () => {
        try {
            let params1 = {
                id,
            };
            let params = {
                immobile_id: id,
            };

            const [
                tab1Response,
                tab2Response,
                tab3Response,
                tab4Response,
            ]: any = await Promise.all([
                getTab1DataRequest.doFetch(true, params1),
                getTab2DataRequest.doFetch(true, params),
                getTab3DataRequest.doFetch(true, params),
                getTab4DataRequest.doFetch(true, params),
            ]);

            const result = anagrafiche?.cities.find(
                (item: any) => item.id === tab1Response.data.citta
            );

            Object.entries(tab1Response.data).forEach(([key, value]) => {
                setValue(key as string, value || "");
            });
            setValue("provicincia", result?.provicincia);

            setValue("anagrafica", tab2Response.data || []);
            setValue("daticatastali", tab3Response.data || []);
            setValue(
                "pregiud",
                tab4Response.data.map((item: any) => {
                    const { data_pregiud, ...rest } = item;

                    rest.tassaiscrizione = rest.tassa_iscrizione;
                    delete rest.tassa_iscrizione;
                    return {
                        ...rest,
                        data_pregiud: moment(data_pregiud).toDate(),
                    };
                }) || []
            );

            setRowData({
                tab1Data: tab1Response.data || {},
                tab2Data: tab2Response.data || {},
                tab3Data: tab3Response.data || {},
                tab4Data: tab4Response.data || {},
            });
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };

    React.useEffect(() => {
        if (id && id != "create") {
            fetchData();
        }
    }, [id]);

    return {
        method,
        rowData,
    };
};
