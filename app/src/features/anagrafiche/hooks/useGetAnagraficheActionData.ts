import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useLocation } from "react-router-dom";

export default function useGetAnagraficheActionData() {
    const location = useLocation();

    const queryParams = new URLSearchParams(location.search);
    const uniqueId = queryParams.get("id");

    const getAnagraficheDataRequest = useGetCustom(
        uniqueId ? `anagrafiche/update?id=${uniqueId}` : "anagrafiche/update"
    );

    const [anagraficheData, setAngraficheData] = useState<any>({});

    const fetchData = async () => {
        const { data }: any = await getAnagraficheDataRequest.doFetch(true);
        setAngraficheData(data);
        return data;
    };

    useEffect(() => {
        fetchData();
    }, [uniqueId]);

    return { anagraficheData, isUpdate: !!uniqueId, uniqueId, fetchData, setAngraficheData };
}
