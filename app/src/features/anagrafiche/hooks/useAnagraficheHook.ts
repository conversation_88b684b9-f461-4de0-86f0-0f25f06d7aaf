import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { AnagraficheActions, AnagrafichePraticheAction } from "../../../interfaces/anagrafiche.interface";
import { getAnagraficheNewGrid, getAnagrafichePraticheNewGrid } from "../../../utilities/anagrafiche/gridColumn";

export const useAnagraficheHook = (actions: AnagraficheActions) => {
    const { t } = useTranslation();
    const [assigneeList, setAssigneeList] = useState<any[]>([]);
    const [categorie, setCategorie] = useState<any[]>([]);
    const [tipoAnagrafica, setTipoAnagrafica] = useState<any[]>([]);
    const [tags, setTags] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);

    const anagraficheRequest = useGetCustom("anagrafiche");

    useEffect(() => {
        async function initAnagrafiche() {
            try {
                const response: any = await anagraficheRequest.doFetch();
                const { data } = response;
                setAssigneeList(data.assigneeList);
                setCategorie(data.categorie);
                setTipoAnagrafica(data.types);
                setTags(data.tags);
                const finalColumns: any = await getAnagraficheNewGrid(
                    t,
                    actions
                );
                setColumns(finalColumns);
            } catch (error) {
                console.error("anagraficheRequest error", error);
                return;
            }
        }

        initAnagrafiche();
    }, []);

    return {
        t,
        assigneeList,
        columns,
        categorie,
        tipoAnagrafica,
        tags,
    };
};


export const useAnagrafichePraticheHook = (actions: AnagrafichePraticheAction) => {
    const { t } = useTranslation();
    const [assigneeList, setAssigneeList] = useState<any[]>([]);
    const [categorie, setCategorie] = useState<any[]>([]);
    const [tipoAnagrafica, setTipoAnagrafica] = useState<any[]>([]);
    const [tags, setTags] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);

    const anagraficheRequest = useGetCustom("anagrafiche");

    useEffect(() => {
        async function initAnagrafiche() {
            try {
                const response: any = await anagraficheRequest.doFetch();
                const { data } = response;
                setAssigneeList(data.assigneeList);
                setCategorie(data.categorie);
                setTipoAnagrafica(data.types);
                setTags(data.tags);
                const finalColumns: any = await getAnagrafichePraticheNewGrid(t, actions);
                setColumns(finalColumns);
            } catch (error) {
                console.error("anagraficheRequest error", error);
                return;
            }
        }

        initAnagrafiche();
    }, []);

    return {
        t,
        assigneeList,
        columns,
        categorie,
        tipoAnagrafica,
        tags,
    };
};
