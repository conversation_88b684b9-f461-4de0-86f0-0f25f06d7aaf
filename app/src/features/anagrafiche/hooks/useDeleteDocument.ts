import { useEffect } from "react";
import useGetCustom from "../../../hooks/useGetCustom";

export const useDeleteDocument = ({
    id,
    remove,
}: {
    id: string | undefined;
    remove: boolean;
}) => {
    const { doFetch, hasLoaded, loading, error } = useGetCustom(
        "anagrafiche/delete-file?noTemplateVars=true"
    );

    useEffect(() => {
        if (id && remove) {
            doFetch(true, { id: id });
        }
    }, [id, remove]);

    return { loading, hasLoaded, error };
};
