import useGetCustom from "../../../hooks/useGetCustom";
import { useCallback, useEffect, useState } from "react";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";

export default function useGetRelationships(showCreateForm: any) {
    const getRelationshipsDataRequest = useGetCustom(
        "anagraficherelationships/list?noTemplateVars=true"
    );
    const { anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;
    const [readContactData, setReadContactData] = useState<any[]>([]);

    function parsingContactData(data: any) {
        return data.map(({ type, socio, amministratore, ...rest }: any) => ({
            ...rest,
            type,
            socio: socio === "0" ? false : true,
            amministratore: amministratore === "0" ? false : true,
            parent_id: customer.id,
        }));
    }

    const fetchRelationship = useCallback(async (update: Boolean = false) => {
        const params = {
            peopleUniqueid: customer.uniqueid,
            page: 0,
            pageSize: 7,
            sortColumn: "parent",
            sortOrder: "asc",
            searchField: "",
        };

        const response: any = await getRelationshipsDataRequest.doFetch(
            true,
            params
        );
        const { currentPage } = response.data;
        let finalContactData = currentPage;

        if (update && currentPage !== undefined) {
            const correctData =
                Object.keys(currentPage).length === 0 ? [] : currentPage;
            finalContactData = parsingContactData(correctData);
        }

        setReadContactData(finalContactData);
    }, [customer.uniqueid, showCreateForm]);

    useEffect(() => {
        fetchRelationship(showCreateForm.contactiCollegati === true);
    }, [customer.uniqueid, showCreateForm, fetchRelationship]);

    return { readContactData, setReadContactData, fetchRelationship };
}
