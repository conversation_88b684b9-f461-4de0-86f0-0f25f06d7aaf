import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import { getImmobiliGrids } from "../../../utilities/anagrafiche/immobiliGridColumn";
import { ImmobiliActions } from "../../../interfaces/immobili.interface";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../interfaces/general.interfaces";

const DEFAULT_QUERY = {
    peopleUniqueid: "",
    page: 0,
    pageSize: 7,
    sortColumn: "valore",
    sortOrder: "asc",
};

export const useImmobiliHook = (actions: ImmobiliActions) => {
    const { t } = useTranslation();
    const listImmobiliRequest = useGetCustom(
        "peopleproperties/list?noTemplateVars=true"
    );
    const { anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;

    const [immobiliQuery, setImmobiliQuery] = useState({
        ...DEFAULT_QUERY,
    });

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    const fetchData = async () => {
        const updatedQuery = {
            ...immobiliQuery,
            peopleUniqueid: customer?.uniqueid || "",
        };
        const [columns, response]: any = await Promise.all([
            getImmobiliGrids(t, actions),
            listImmobiliRequest.doFetch(true, updatedQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        setList({
            ...list,
            rows: currentPage || [],
            columns: columns,
            totalRows: parseInt(totalRows),
            page: immobiliQuery?.page,
            pageSize: immobiliQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchData();
    }, [immobiliQuery]);

    return {
        immobiliQuery,
        setImmobiliQuery,
        loading: listImmobiliRequest.loading,
        list,
        fetchData,
    };
};
