import usePostCustom from "../../../hooks/usePostCustom";
import { useState, useCallback, useEffect } from "react";
import { debounce } from "lodash";

export default function useTags() {
    const getAvailableTagsRequest = usePostCustom(
        "archive/getavailabletags?noTemplateVars=true"
    );
    const saveTagsRequest = usePostCustom(
        "anagrafiche/aggiornatags?noTemplateVars=true"
    );
    const [tags, setTags] = useState<any>([]);
    const [selectedTags, setSelectedTags] = useState<any>([]);

    //search functions
    const debounceSearchTags = debounce(async (value: string) => {
        let params = {
            searchWord: value,
        };
        const response: any = await getAvailableTagsRequest.doFetch(
            true,
            params
        );

        const { data } = response;
        const formatData = data.map((item: any) => {
            return {
                title: item.description,
                value: item.description,
            };
        });
        setTags(formatData);
    }, 500);

    const memoizedSearchTags = useCallback(debounceSearchTags, [
        debounceSearchTags,
    ]);

    const handleTagsSearch = (value: any) => {
        memoizedSearchTags(value);
    };

    const saveTags = async (peopleUniqueid: string, fileTags: any) => {
        const formData = new FormData();
        formData.append('peopleUniqueid', peopleUniqueid);

        const joinedTags = fileTags.map((tag: any) => tag.value).join(",");
        formData.append('fileTags', joinedTags);
        
        const response: any = await saveTagsRequest.doFetch(true, formData);
        return response.data;
    };

    useEffect(() => {
        const fetch = async () => {
            const response: any = await getAvailableTagsRequest.doFetch(
                true,
                ""
            );
            const { data } = response;
            const formatData = data.map((item: any) => {
                return {
                    title: item.description,
                    value: item.description,
                };
            });
            setTags(formatData);
        };

        fetch();
    }, []);

    return {
        tags,
        setTags,
        saveTags,
        handleTagsSearch,
        selectedTags,
        setSelectedTags,
        searchTagLoading: getAvailableTagsRequest.loading,
    };
}
