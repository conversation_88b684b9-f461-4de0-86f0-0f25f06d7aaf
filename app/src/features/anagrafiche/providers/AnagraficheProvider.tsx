import React, {
  ReactNode,
  useState,
  useCallback,
  useContext,
  useReducer,
} from "react";
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import useGetCustom from "../../../hooks/useGetCustom";

const INITIALIZE = "INITIALIZE";
const SET_ANAGRAFICHE = "SET_ANAGRAFICHE";

export type ActionMap<M extends { [index: string]: any }> = {
  [Key in keyof M]: M[Key] extends undefined
    ? { type: Key }
    : { type: Key; payload: M[Key] };
};

type AnagraficheActionTypes = {
  [INITIALIZE]: {
    isInitialized: boolean;
    anagrafiche: any;
  };
  [SET_ANAGRAFICHE]: {
    anagrafiche: any;
  };
};

export type AnagraficheState = {
  isInitialized: boolean;
  anagrafiche: any;
};

const initialState: AnagraficheState = {
  isInitialized: false,
  anagrafiche: {},
};

const JWTReducer = (
  state: AnagraficheState,
  action: ActionMap<AnagraficheActionTypes>[keyof ActionMap<AnagraficheActionTypes>]
) => {
  switch (action.type) {
    case INITIALIZE:
      return {
        isInitialized: true,
        anagrafiche: action.payload.anagrafiche,
      };
    case SET_ANAGRAFICHE:
      return {
        ...state,
        isInitialized: true,
        anagrafiche: action.payload.anagrafiche,
      };

    default:
      return state;
  }
};

export interface AnagraficheContextType {
  isInitialized: boolean;
  anagrafiche: any;
  fetchAnagrafiche: () => void;
}

export const AnagraficheContext = React.createContext<AnagraficheContextType>({
  isInitialized: false,
  anagrafiche: null,
  fetchAnagrafiche: () => {},
});

export function useAnagraficheProvider() {
  return useContext(AnagraficheContext);
}

export function AnagraficheProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(JWTReducer, initialState);
  const [error, setError] = useState<any>(null);
  const location = useLocation();

  const queryParams = new URLSearchParams(location.search);
  const uniqueId = queryParams.get("id");
  const anagraficheViewRequest = useGetCustom(
    `/anagrafiche/view/?id=${uniqueId}`
  );

  useEffect(() => {
    fetchAnagrafiche();
  }, []);

  const fetchAnagrafiche = useCallback(async () => {
    return await anagraficheViewRequest
      .doFetch()
      .then((response: any) => {
        dispatch({
          type: SET_ANAGRAFICHE,
          payload: {
            anagrafiche: response.data,
          },
        });
      })
      .catch((error) => {
        console.log({ error });
        setError(error);
      });
  }, [uniqueId]);

  return (
    <AnagraficheContext.Provider value={{ ...state, fetchAnagrafiche }}>
      {error ? <h1>Error</h1> : children}
    </AnagraficheContext.Provider>
  );
}
