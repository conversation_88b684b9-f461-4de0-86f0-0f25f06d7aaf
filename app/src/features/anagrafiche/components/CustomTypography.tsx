import { ReactNode } from "react";
import { Typography } from "@vapor/react-material";

type TypographyProps = {
  children: ReactNode;
};

const CleanLabel: React.FC<TypographyProps> = ({ children }) => (
  <Typography variant="body2" color="textSecondary">
    {children}
  </Typography>
);

const BoldValue: React.FC<TypographyProps> = ({ children }) => (
  <Typography variant="bodyLarge500">
    {children}
  </Typography>
);

export { CleanLabel, BoldValue };
