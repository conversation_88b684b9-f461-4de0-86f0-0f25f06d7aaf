import AnagraficheList from "./AnagraficheList";
import AnagraficheActionsIndex from "./actions";
import AnagraficheDetail from "./AnagraficheDetail";

export const DEFAULT_LIST_PARAMS = {
  noTemplateVars: true,
  page: 0,
  pageSize: 10,
  sortColumn: "nome",
  sortOrder: "asc",
  searchField: "",
  searchRelation: "-1",
  searchAssociazione: "",
  searchTags: "",
  startAnagraficheSearch: null,
  endAnagraficheSearch: null,
  startAnagraficheSearchValue: null,
  endAnagraficheSearchValue: null,
  categoryId: "",
  searchNation: "",
  searchCity: "",
  searchWay: "",
  searchCap: "",
  searchProvince: "",
  searchRegion: "",
  cumulative: "",
  assegnee: "",
  ddNumber: "",
  dateType: "",
  startDateLiquid: "",
  endDateLiquid: "",
  showAdvanceDynamic: "",
  searchTagsLike: "",
  searchRelationArray: [],
  searchCategoriesArray: [],
  searchTagsArray: [],
  searchAnagraficheTypes: [],
};

export const anagrafiche = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "anagrafiche/view/:id?",
      element: <AnagraficheDetail />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/anagrafiche",
      element: <AnagraficheList />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/anagrafiche/update",
      element: <AnagraficheActionsIndex />,
    },
  },
];
