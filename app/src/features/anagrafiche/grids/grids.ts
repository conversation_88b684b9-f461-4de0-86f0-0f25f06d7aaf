import BaseGridList from "../../../models/BaseGridList";
import { IGridColumn } from "../../../interfaces/general.interfaces";
import { mapOtherColumnNames } from "../../../utilities/common";

export const getPraticheGrid = async (t: any,netlexSettingsFileId: string): Promise<IGridColumn[]> => {
    let columnNames = [
        t("Nome pratica"),
        t("Cliente"),
        t("Controparti"),
        t("Oggetto"),
        t("R.G.(N.R.)"),
        t("Responsabile"),
        t("Stato"),
    ];
    
    let columnKeys = [
        "nome_pratica",
        "listaclienti",
        "listacontroparti",
        "oggetto",
        "rg",
        "responsabile",
        "stato",
    ];
    
    let columnWidths = [
        "10%",
        "10%",
        "10%",
        "10%",
        "10%",
        "10%",
        "10%",
    ];
    
    let sortable = [true, true, true, true, true, true, true];
    
    
    if (netlexSettingsFileId === "fileArchiveCode") {
        columnNames.unshift(t("Codice"));
        columnKeys.unshift("codicearchivio");
        columnWidths.unshift("10%");
        sortable.unshift(true);
    } else if (netlexSettingsFileId === "fileCode") {
        columnNames.unshift(t("Codice"));
        columnKeys.unshift("codicepratica");
        columnWidths.unshift("10%");
        sortable.unshift(true);
    }
   

    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: columnNames,
            column_keys: columnKeys,
            column_widths: columnWidths,
            sortable: sortable,
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};
