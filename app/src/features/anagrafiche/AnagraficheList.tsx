import VaporPage from "@vapor/react-custom/VaporPage";
import ToastNotification from "../../custom-components/ToastNotification";
import { AnagraficheFilters } from "./sections/AnagraficheFilters";
import { useAnagraficheHook } from "./hooks/useAnagraficheHook";
import { useEffect, useRef, useState } from "react";
import useGetCustom from "../../hooks/useGetCustom";
import { DEFAULT_LIST_PARAMS } from ".";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import { AnagraficheActions } from "../../interfaces/anagrafiche.interface";
import ConfirmModal from "../../custom-components/ConfirmModal";
import { debounce } from "lodash";
import { CustomDataGrid } from "../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { updateParams } from "./helpers/anagraficheList";
import AnagrafichePanelDrawer from "./AnagrafichePanel";

const AnagraficheList = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const queryFromLocalStorage = localStorage.getItem("anagraficheQuery");
    const [defaultParams, setDefaultParams] = useState(
        queryFromLocalStorage && typeof queryFromLocalStorage !== "undefined"
            ? JSON.parse(queryFromLocalStorage)
            : DEFAULT_LIST_PARAMS
    );
    const [openDeleteConfirmation, setOpenDeleteConfirmation] =
        useState<boolean>(false);
    const [selectedId, setSelectedId] = useState<string>("");
    const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
    const [showErrorMessageBulkDelete, setShowErrorMessageBulkDelete] =
        useState<boolean>(false);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const isInputSearchEnabled = useRef<boolean>(false);
    const resetClicked = useRef<boolean>(false);
    const [openDrawer, setOpenDrawer] = useState(false);
    const deleteAnagraficheRow = useGetCustom(
        `anagrafiche/delete?noTemplateVars=true`
    );
    const [rowIds, setRowIds] = useState({
        id: null,
        uniqueid: null,
    });

    const toggleDeleteRow = async (row: any) => {
        setSelectedId(row.id);
        setOpenDeleteConfirmation(true);
    };

    const handleDrawer = () => {
        setOpenDrawer(!openDrawer);
    };

    const handleDrawerRow = async (row: any) => {
        setOpenDrawer(true);
        setRowIds({
            id: row.id,
            uniqueid: row.uniqueid,
        });
    };

    const handleDeleteContent = async () => {
        const id = selectedId;
        const ids = selectedRows;
        let response: any = null;

        if (id) {
            response = await deleteAnagraficheRow.doFetch(true, { id });
            setSelectedId("");
        } else if (ids.length > 0) {
            response = await deleteAnagraficheRow.doFetch(true, { ids });
            setSelectedRows([]);
        }

        if (!response) {
            return;
        }

        setOpenDeleteConfirmation(false);

        if (id && response?.data?.result === null) {
            setShowErrorMessage(true);
            return;
        }

        if (
            Array.isArray(response?.data?.result) &&
            response?.data?.result.length > 0 &&
            selectedRows.length > 0
        ) {
            setShowErrorMessageBulkDelete(true);
            setSelectedRows(response?.data?.result);
            startSearchList();
            return;
        }

        startSearchList();
    };

    const closeDeleteConfirmation = async () => {
        setOpenDeleteConfirmation(false);
        setSelectedId("");
    };

    const actions: AnagraficheActions = {
        handleDelete: toggleDeleteRow,
        handleDrawer: handleDrawerRow,
    };

    const { columns, categorie, tipoAnagrafica, tags } =
        useAnagraficheHook(actions);

    const anagraficheListRequest = useGetCustom(
        "anagrafiche/list",
        updateParams(defaultParams, location)
    );

    const defaultAnagraficheListRequest = useGetCustom(
        "anagrafiche/list",
        updateParams(DEFAULT_LIST_PARAMS, location)
    );

    const setQueryAndLocalStorage = (query: any) => {
        const newQuery = { ...query };
        setDefaultParams(newQuery);
        localStorage.setItem("anagraficheQuery", JSON.stringify(newQuery));
    };

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const nome = searchParams.get("nome");
        if (nome) {
            setDefaultParams((prevParams: any) => ({
                ...prevParams,
                searchField: nome,
            }));
        }
    }, []);

    useEffect(() => {
        if (!resetClicked.current) {
            startSearchList();
        }
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.searchRelationArray,
        defaultParams.searchCategoriesArray,
        defaultParams.searchTagsArray,
        defaultParams.searchAnagraficheTypes,
        defaultParams.searchCity,
    ]);

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            startSearchList();
            isInputSearchEnabled.current = false;
        }, 500);
        if (isInputSearchEnabled.current) {
            debouncedSearch();
        }
        return () => {
            debouncedSearch.cancel();
        };
    }, [
        defaultParams.searchField,
        defaultParams.searchCity,
        defaultParams.searchTagsArray,
        defaultParams.searchCategoriesArray,
        defaultParams.searchAnagraficheTypes,
        defaultParams.searchRelationArray,
        defaultParams.searchTagsArray,
        defaultParams.searchNation,
        defaultParams.searchWay,
        defaultParams.searchCap,
        defaultParams.searchProvince,
        defaultParams.searchRegion,
        isInputSearchEnabled.current,
    ]);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultAnagraficheListRequest.doFetch(true)
            : anagraficheListRequest.doFetch(true));

        if (reset) {
            resetClicked.current = false;
        }

        const { currentPage, totalRows } = response.data;

        setData(currentPage);
        setTotalRows(totalRows);
    };

    const onSubmit = () => {
        startSearchList();
    };

    const onChangeMultiSelect = (name: string, values: any[]) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            [name]: values,
        });
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setQueryAndLocalStorage({
            ...defaultParams,
            [name]: value,
        });
        isInputSearchEnabled.current = true;
    };

    const onDateChange = (name: string, value: Date) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            [name]: value,
        });
    };

    const onChangeCheckbox = (e: any) => {
        const { name, checked } = e.target;
        setQueryAndLocalStorage({
            ...defaultParams,
            [name]: checked || "",
        });
    };

    const onPageChange = (_: any, page: number) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            page,
        });
    };

    const onSortChange = (column: any, direction: any) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            sortColumn: column,
            sortOrder: direction,
        });
    };

    const onClickReset = async () => {
        resetClicked.current = true;
        setDefaultParams(DEFAULT_LIST_PARAMS);
        localStorage.removeItem("anagraficheQuery");
        startSearchList(true);
    };

    const handleClickCallback = (uniqueid: any) => {
        navigate(`/anagrafiche/view?id=${uniqueid}`);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="people"
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    anagraficheListRequest.loading ||
                    defaultAnagraficheListRequest.loading
                }
                selectableRows
                query={defaultParams}
                setQuery={setQueryAndLocalStorage}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                onRowSelectionModelChange={(
                    newSelection: GridRowSelectionModel
                ) => {
                     setSelectedRows([...newSelection]);
                }}
                onClickKey="id"
                onClickCheckboxKey="id"
            />
        );
    };

    const onCityChange = (cityData: any) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            searchCity: cityData?.id || "",
        });
    };

    const onChangeFunctions = {
        onChangeCheckbox,
        onChangeInput,
        onChangeMultiSelect,
        onDateChange,
        onClickReset,
        onSortChange,
        onPageChange,
        onSubmit,
        onCityChange,
    };

    const modalConfirmText = !selectedId
        ? t("Vuoi eliminare le anagrafiche selezionate?")
        : t("Vuoi eliminare l'anagrafica?");

    return (
        <VaporPage title={t("Gestione Anagrafiche")}>
            {openDeleteConfirmation && (
                <ConfirmModal
                    open={openDeleteConfirmation}
                    handleDecline={closeDeleteConfirmation}
                    handleAgree={handleDeleteContent}
                    decline={t("Annulla")}
                    agree={t("Elimina")}
                    confirmText={modalConfirmText}
                    title={t("Conferma eliminazione")}
                    dividerVariant="alert"
                    colorConfirmButton="error"
                />
            )}
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={t(
                    "Non è possibile eliminare l'anagrafica perchè in uso.(N.B. alcune anagrafiche potrebbero essere associate a pratiche riservate non visibili)"
                )}
            />
            <ToastNotification
                showNotification={showErrorMessageBulkDelete}
                setShowNotification={setShowErrorMessageBulkDelete}
                severity="error"
                text={t(
                    "Non è possibile eliminare alcune anagrafiche perchè in uso.(N.B. alcune anagrafiche potrebbero essere associate a pratiche riservate non visibili)"
                )}
            />
            {openDrawer && (
                <AnagrafichePanelDrawer
                    openDrawer={openDrawer}
                    handleDrawer={handleDrawer}
                    rowIds={rowIds}
                />
            )}
            <VaporPage.Section>
                <AnagraficheFilters
                    params={defaultParams}
                    setParams={setDefaultParams}
                    defaultQuery={DEFAULT_LIST_PARAMS}
                    onChangeFunctions={onChangeFunctions}
                    categorie={categorie}
                    tipoAnagrafica={tipoAnagrafica}
                    tags={tags}
                    selectedRows={selectedRows}
                    setSelectedRows={setSelectedRows}
                    setOpenDeleteConfirmation={setOpenDeleteConfirmation}
                />
            </VaporPage.Section>
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
};

export default AnagraficheList;
