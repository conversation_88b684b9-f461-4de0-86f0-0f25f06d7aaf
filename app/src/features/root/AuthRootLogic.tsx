import { useEffect, useState } from 'react';
import { useAuth, useTranslation, useHideSideNav, fetch } from '@1f/react-sdk';
import { TextField, Box, InputAdornment, Stack, MenuItem } from '@vapor/react-material';
import Select from "@vapor/v3-components/Select";
import { VaporPage } from '@vapor/react-custom';
import { IconButton } from '@vapor/react-material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import Spinner from '../../custom-components/Spinner';
import usePostCustom from "../../hooks/usePostCustom"
import { useUser } from '../../store/UserStore';
import useGetCustom from '../../hooks/useGetCustom';
import { useApi } from '../../store/ApiStore';
import useCustomNavigate from '../../hooks/useCustomNavigate';
import PageTitle from '../../custom-components/PageTitle';
import SpinnerButton from '../../custom-components/SpinnerButton';

interface CustomerToSelect {
  customerId: number;
  customerName: string;
  userToken: string;
}

const IS_DEV = import.meta.env.DEV;

const AuthRootLogic = () => {
  useHideSideNav()

  const { t } = useTranslation()
  const { customNavigate } = useCustomNavigate();
  const [hideRegistration, setHideRegistration] = useState(true)
  const [hideLogin, setHideLogin] = useState(true);
  const [customersToSelect, setCustomersToSelect] = useState<CustomerToSelect[]>([])
  const [distroToken, setDistroToken] = useState<string>('0')
  const { tokenData, token } = useAuth()
  const { email, sub: tsUniqueId } = tokenData

  const [name, setName] = useState("")
  const [subdomain, setSubdomain] = useState(null)
  const [emailToLink, setEmailToLink] = useState(email)
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [fireLoginRequest, setFireLoginRequest] = useState(false)

  const { getUser }: any = useUser();
  const { setApi, setSessionToken }: any = useApi();

  const userDataRequest = useGetCustom("login/getloggeduser");
  const loginTsidRequest = usePostCustom("login/tsid");
  const checkTsIdRequest = usePostCustom("one-front/check-ts-id")
  const authenticateRequest = usePostCustom(
    "one-front/authenticate",
    subdomain
  )
  const createTsIdRequest = usePostCustom(
    "one-front/create-ts-id",
    subdomain
  )
  const loginRequest = usePostCustom(
    "login/login",
    subdomain
  )

  const onChangeName = (e: any) => {
    setName(e.target.value)
    setSubdomain(e.target.value)
  }

  const onChangeDistribution = (e: any) => {
    const selectedValue = e.target.value;
    const selectedCustomer = customersToSelect.find(customer => customer.userToken === selectedValue);
    const subdomain = selectedCustomer ? selectedCustomer.customerName : '';
    setDistroToken(selectedValue)
    setSessionToken(selectedValue)
    setApi({ subdomain });
  }

  const onChangeEmail = (e: any) => {
    setEmailToLink(e.target.value)
  }

  const onChangePassword = (e: any) => {
    setPassword(e.target.value)
  }

  useEffect(() => {
    if (fireLoginRequest) {
      launchRequestLoginTsid(distroToken);
      setFireLoginRequest(false);
    }
  }, [fireLoginRequest]);

  useEffect(() => {
    if (IS_DEV) {
      async function checkToken() {
        const response: any = await checkTsIdRequest.doFetch(true, { token })

        const { alreadyExists, teamsystemIdRow } = response.data

        if (alreadyExists) {
          if (teamsystemIdRow && teamsystemIdRow.hasOwnProperty("subdomain")) {
            setApi({ subdomain: teamsystemIdRow.subdomain });

            await authenticateRequest.doFetch(true, {
              userId: teamsystemIdRow.user_id,
            });

            await getUser(userDataRequest);
          }

          customNavigate("/calendar/calendar");
        }

        setHideRegistration(false)
      }

      checkToken();
      return;
    }

    async function getAllCustomers() {
      let customersUrl = `mkt:legal:worker:url://api/tsid/get-all-customers-from-ts-unique-id/${tsUniqueId}`;
      if (import.meta.env.DEV) {
        customersUrl = `${customersUrl}?XDEBUG_SESSION_START=PHPSTORM`;
      }

      try {
        const response: any = await fetch(
          customersUrl,
          {
            method: "GET",
            headers: {
              "Authorization": `Bearer ${import.meta.env.VITE_WORKER_AUTHMAKEUSER}`,
            },
          }
        )

        // get body content
        const body = await response.json();

        if (!body) {
          throw new Error("No customers found")
        }

        setCustomersToSelect(body)

        if (body.length === 1) {
          setDistroToken(body[0].userToken)
          setApi({ subdomain: body[0].customerName });
          setSessionToken(body[0].userToken)
          setFireLoginRequest(true);

          return;
        }

        setHideLogin(false);
      } catch (error) {
        console.error(error)
      }
    }

    getAllCustomers();
  }, [])

  const handleLogin = async (e: any) => {
    e.preventDefault()
    await loginRequest.doFetch(false, { unamelogin: emailToLink, psswdlogin: password },)

    setApi({ subdomain: name });

    await createTsIdRequest.doFetch(false, { token, email: emailToLink, password },)

    await getUser(userDataRequest);

    customNavigate("/archive/archive");
  }

  const handleLoginTsId = async (e: any) => {
    e.preventDefault()

    launchRequestLoginTsid(distroToken);
  }

  const launchRequestLoginTsid = async (jwt: string) => {
    const result: any = await loginTsidRequest.doFetch(true, {
      jwt,
    });

    await getUser(userDataRequest);

    let redirectPath = "/archive/archive";
    if (result?.data?._redirectInfo) {
      redirectPath = result.data._redirectInfo.url;
    }

    customNavigate(redirectPath);
  }

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const handleMouseDownPassword = (event: any) => {
    event.preventDefault()
  }

  const renderDevLoginFlow = () => {
    if (checkTsIdRequest.loading || hideRegistration) {
      return <Spinner />
    }

    return (
      <VaporPage title={t("insert_your_info")}>
        <VaporPage.Section>
          <Box
            component="form"
            noValidate
            autoComplete="off"
            onSubmit={handleLogin}
          >
            <TextField
              label="Dominio"
              variant="outlined"
              required
              margin="normal"
              value={name}
              onChange={onChangeName}
            />
            <TextField
              label="Email"
              variant="outlined"
              required
              margin="normal"
              value={emailToLink}
              onChange={onChangeEmail}
            />
            <TextField
              label="Password"
              type={showPassword ? "text" : "password"}
              variant="outlined"
              required
              margin="normal"
              value={password}
              onChange={onChangePassword}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      onMouseDown={handleMouseDownPassword}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Stack direction="row" alignItems="center" spacing={2} mt={2}>
              <SpinnerButton
                label='Accedi'
                variant='contained'
                isLoading={loginRequest.loading}
                type='submit'
              />
            </Stack>
          </Box>
        </VaporPage.Section>
      </VaporPage>
    )
  }

  const renderTsIdLoginFlow = () => {
    if (hideLogin) {
      return <Spinner />
    }

    return (
      <VaporPage>
        <PageTitle
          title={t('Scegli la distribuzione')}
          showBackButton={false}
        />
        <VaporPage.Section>
          <Box
            component="form"
            noValidate
            autoComplete="off"
            onSubmit={handleLoginTsId}
            sx={{ display: "flex", justifyContent: "center", flexDirection: "row" }}
          >
            <Select
              label="Dominio"
              required
              variant="outlined"
              value={distroToken}
              onChange={onChangeDistribution}
              sx={{ width: 250 }}
            >
              <MenuItem value="0">
                <em>Seleziona la distribuzione</em>
              </MenuItem>
              {customersToSelect.map((customer: CustomerToSelect) => (
                <MenuItem key={customer.customerId} value={customer.userToken}>
                  {customer.customerName}
                </MenuItem>
              ))}
            </Select>

            <Stack direction="row" alignItems="center" spacing={2} ml={2}>
              <SpinnerButton
                label='Accedi'
                variant='contained'
                isLoading={loginTsidRequest.loading}
                disabled={distroToken === '0'}
                type='submit'
              />
            </Stack>
          </Box>
        </VaporPage.Section>
      </VaporPage>
    );
  }

  if (IS_DEV) {
    return renderDevLoginFlow()
  }

  return renderTsIdLoginFlow();
}

export default AuthRootLogic;
