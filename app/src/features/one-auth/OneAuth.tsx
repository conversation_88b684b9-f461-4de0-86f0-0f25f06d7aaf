import { useEffect } from "react";
import Spinner from "../../custom-components/Spinner";
import useGetCustom from "../../hooks/useGetCustom";
import { useConfigs } from "../../store/ConfigStore";
import { useUser } from "../../store/UserStore";
import { useApi } from "../../store/ApiStore";
import { useLegacySwitch } from "../../store/LegacySwitchStore";

const OneAuth = () => {
	const { getConfigs }: any = useConfigs();
	const { getUser }: any = useUser();
	const { hydrateApi }: any = useApi();
	const { hydrateLegacySwitch }: any = useLegacySwitch();
	const apiStorage = localStorage.getItem("apiStorage");
	const subdomain = apiStorage && JSON.parse(apiStorage).subdomain;

	const configDataRequest = useGetCustom("index/configs", {}, subdomain);
	const userDataRequest = useGetCustom("login/getloggeduser", {}, subdomain);

	useEffect(() => {
		const hydrate = async () => {
			const response = await hydrateApi();
			await hydrateLegacySwitch();
			// i only make this request if the subdomain is set, that means the user is already logged in
			// and don't pass anymore inside AuthRootLogic
			if (response && response.subdomain) {
				await getConfigs(configDataRequest);
				getUser(userDataRequest);
			}
		}
		
		hydrate();
    }, []);

  	return <Spinner />;
};

export default OneAuth;