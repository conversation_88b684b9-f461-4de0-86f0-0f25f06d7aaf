import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

export default function useMountedData() {
    const mountedRequest = useGetCustom("emailmodels");
    const [mountedData, setMountedData] = useState<any>([]);

    useEffect(() => {
        async function fetchData() {
            const { data }: any = await mountedRequest.doFetch(true);
            setMountedData(data);
        }

        fetchData();
    }, []);

    return { mountedData };
}
