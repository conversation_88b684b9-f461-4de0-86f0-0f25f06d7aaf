import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import useMountedData from "./hooks/useMountedData";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import usePostCustom from "../../../hooks/usePostCustom";

export default function ClauseIndex() {
    const navigate = useNavigate();

    const { t } = useTranslation();
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter();
    const [open, setOpen] = useState(false);
    const { mountedData } = useMountedData();

    const deleteRequest = usePostCustom(
        "emailmodels/delete?noTemplateVars=true"
    );

    const handleClickCallback = (uniqueid: string) => {
        navigate("/emailmodels/update/" + uniqueid);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleRowSelection = (rowSelectionModel: GridRowSelectionModel) => {
        let ids = list.rows.map((element: any) => {
            if (rowSelectionModel.includes(element.uid_documento)) {
                console.log("element", element.id);
                return element.id;
            }
        });
        setSelectedRows(ids.filter((item) => item));
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="emailmodels"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                onClickKey="uid_documento"
                onClickCheckboxKey="id"
                selectableRows
                onRowSelectionModelChange={handleRowSelection}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };
    const redirectAction = () => {
        navigate("/emailmodels/update");
    };

    const getRightItem = () => {
        let data = [];

        if (selectedRows.length > 0) {
            data.push(
                <Button color="error" variant="outlined" onClick={handleDelete}>
                    {t("Elimina")}
                </Button>
            );
        }
        data.push(
            <Button variant="contained" onClick={redirectAction}>
                {t("Nuovo Modello")}
            </Button>
        );
        return data;
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        const formData = new FormData();
        selectedRows.forEach((range: any) => {
            formData.append("ids[]", range);
        });
        await deleteRequest.doFetch(true, formData);
        setOpen(false);
        filterData(query);
        setSelectedRows([]);
    };

    return (
        <>
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={t("No")}
                agree={t("Yes, Delete")}
                confirmText={t("Are you sure want to Delete")}
                title={t("Delete Action")}
            />
            <VaporPage>
                <VaporHeaderBar
                    rightItems={getRightItem()}
                    title={t("Modelli per le email")}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
