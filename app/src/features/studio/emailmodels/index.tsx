import EmailmodelIndex from "./EmailmodelIndex";
import EmailmodelCreateUpdate from "./CreateUpdate";

export const emailmodels = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/emailmodels",
            element: <EmailmodelIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/emailmodels/update/:uniqueId?",
            element: <EmailmodelCreateUpdate />,
        },
    },
];
