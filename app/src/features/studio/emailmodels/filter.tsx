import { Box, Button, TextField } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData } = props;
    const { t } = useTranslation();
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        searchWithDebounce(e);
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={t("Nome")}
                variant="outlined"
                name={"nome"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={query["nome"]}
            />

            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                {t("Cerca")}
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                {t("Mostra tutti")}
            </Button>
        </Box>
    );
}
