import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import {
    Box,
    Button,
    Stack,
    ListItemButton,
    ListItemText,
    ListSubheader,
    Collapse,
    List,
    Grid,
} from "@vapor/react-material";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../custom-components/FormInput";
import PageTitle from "../../../custom-components/PageTitle";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";

const EmailmodelCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [tagsbuffer, setTagsbuffer] = useState<any>([]);
    const [tags, setTags] = useState<any>([]);

    const [contentEditor, setContentEditor] = useState<any>("");

    const headerRef = useRef(null);

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? "documents/onlineupdate?model=1&uniqueid=" + uniqueId
            : "documents/onlineupdate?model=1"
    );
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        titolodocumento: yup.string().required(),
        content: yup.string(),
        visible: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });

    const [openTag, setOpenTag] = useState<any>([]);

    //api list

    const saveRequest = usePostCustom("documents/onlinesave");

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await updateMountedRequest.doFetch();
                setTags(data.tags);
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("titolodocumento", data.result.titolodocumento);
                    setValue("content", data.result.content);
                    setContentEditor(data.result.content);
                    setValue("visible", data.result.visible == 1 ? "on" : "");
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const openTagEvent = (key: string) => {
        setOpenTag({
            ...openTag,
            [key]: !openTag[key],
        });
    };

    const appendToEditor = (subRow: any) => {
        setTagsbuffer((oldArray: any) => [...oldArray, subRow.id]);

        var pos = contentEditor.lastIndexOf("</p>");
        let text =
            contentEditor.substring(0, pos) +
            " " +
            contentEditor.substring(pos + 4);

        text = text + subRow.identificatore;
        setContentEditor(text);
    };

    const onSubmit = async () => {
        // call
        let data: any = {
            uniqueid: getValues("uniqueid") ?? "",
            titolodocumento: getValues("titolodocumento"),
            content: contentEditor.toString(),
        };
        data.tagsbuffer = [...new Set(tagsbuffer)].toString();
        data.model = 1;
        data.action = "";
        data.documentFileUniqueid = "";
        if (getValues("visible") == "on") {
            data.visible = getValues("visible");
        }
        const formData = new FormData();

        Object.keys(data).forEach((key: string) => {
            formData.append(key, data[key]);
        });
        await saveRequest.doFetch(true, formData);
        navigate("/emailmodels");
    };

    const redirectBack = () => {
        navigate("/emailmodels");
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    (uniqueId !== undefined ? t("Modifica ") : t(`Nuovo `)) +
                    t("Modello")
                }
                pathToPrevPage={"/emailmodels"}
            />

            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="titolodocumento"
                                    label={t("Titolo")}
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                />

                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="visible"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["visible"] &&
                                        errors["visible"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["visible"] &&
                                        errors["visible"]["message"]
                                    }
                                    options={[
                                        {
                                            label: t(
                                                "Visibile ad utenti esterni"
                                            ),
                                            value: "on",
                                        },
                                    ]}
                                />

                                <Grid container spacing={2}>
                                    <Grid item xs={8}>
                                        <CKEditor
                                            ref={headerRef}
                                            config={{
                                                toolbar: [
                                                    "heading",
                                                    "|",
                                                    "bold",
                                                    "italic",
                                                    "blockQuote",
                                                    "link",
                                                    "numberedList",
                                                    "bulletedList",
                                                    "uploadImage",
                                                    "insertTable",
                                                    "mediaEmbed",
                                                    "|",
                                                    "undo",
                                                    "redo",
                                                ],
                                            }}
                                            editor={ClassicEditor as any}
                                            data={contentEditor}
                                            onReady={(editor: any) => {
                                                editor.editing.view.change(
                                                    (writer: any) => {
                                                        writer.setStyle(
                                                            "min-height",
                                                            "400px",
                                                            editor.editing.view.document.getRoot()
                                                        );

                                                        writer.setStyle(
                                                            "color",
                                                            "#343a40",
                                                            editor.editing.view.document.getRoot()
                                                        );
                                                    }
                                                );
                                            }}
                                            onChange={(_event, editor) => {
                                                const data = editor.getData();
                                                setContentEditor(data);
                                            }}
                                        />
                                        <p>
                                            {t("Suggerimenti per l'utilizzo:")}
                                            <br />
                                            {t(
                                                'La pressione del tasto "Invio" crea un nuovo paragrafo.'
                                            )}
                                            <br />
                                            {t(
                                                'Se si vuole andare a capo nello stesso paragrafo premere i tasti "Maiusc" + "Invio".'
                                            )}
                                        </p>
                                    </Grid>
                                    <Grid item xs={4}>
                                        <List
                                            sx={{
                                                width: "100%",
                                                maxWidth: 360,
                                                bgcolor: "background.paper",
                                            }}
                                            component="nav"
                                            aria-labelledby="nested-list-subheader"
                                            subheader={
                                                <ListSubheader
                                                    component="div"
                                                    id="nested-list-subheader"
                                                >
                                                    {t("Tags disponibili:")}
                                                </ListSubheader>
                                            }
                                        >
                                            {Object.keys(tags).map(
                                                (key: any) => {
                                                    return (
                                                        <>
                                                            <ListItemButton
                                                                onClick={() =>
                                                                    openTagEvent(
                                                                        key
                                                                    )
                                                                }
                                                                key={key}
                                                            >
                                                                <ListItemText
                                                                    primary={
                                                                        key
                                                                    }
                                                                />
                                                                {openTag[
                                                                    key
                                                                ] ? (
                                                                    <ExpandLess />
                                                                ) : (
                                                                    <ExpandMore />
                                                                )}
                                                            </ListItemButton>
                                                            <Collapse
                                                                in={
                                                                    openTag[key]
                                                                }
                                                                timeout="auto"
                                                                unmountOnExit
                                                            >
                                                                <List
                                                                    component="div"
                                                                    disablePadding
                                                                >
                                                                    {(
                                                                        tags[
                                                                            key
                                                                        ] || []
                                                                    ).map(
                                                                        (
                                                                            subRow: any,
                                                                            j: any
                                                                        ) => {
                                                                            return (
                                                                                <ListItemButton
                                                                                    sx={{
                                                                                        pl: 4,
                                                                                    }}
                                                                                    key={
                                                                                        subRow.nome +
                                                                                        "" +
                                                                                        j
                                                                                    }
                                                                                    onClick={() =>
                                                                                        appendToEditor(
                                                                                            subRow
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <ListItemText
                                                                                        primary={
                                                                                            subRow.nome
                                                                                        }
                                                                                    />
                                                                                </ListItemButton>
                                                                            );
                                                                        }
                                                                    )}
                                                                </List>
                                                            </Collapse>
                                                        </>
                                                    );
                                                }
                                            )}
                                        </List>
                                    </Grid>
                                </Grid>
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={redirectBack}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default EmailmodelCreateUpdate;
