import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Grid,
    Typography,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { SmartMailerInfoBox } from "./SmartMailerInfoBox";
import { useApi } from "../../../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../../../utilities/constants";

export const SmartMailGuide = () => {
    const { t } = useTranslation();
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    return (
        <Box>
            <Box
                style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    paddingBottom: "1rem",
                }}
            >
                <Typography
                    variant="displayMedium"
                    component="div"
                    gutterBottom
                >
                    {t("Guida Smart Mailer")}
                </Typography>
            </Box>
            <Accordion>
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                >
                    <Typography
                        variant="body500"
                        gutterBottom
                        component="div"
                        color="primary.interactiveDefault"
                    >
                        {t(
                            "Smart Mailer permette di smistare le email in arrivo su delle caselle dinamiche direttamente all'interno della pratica"
                        )}
                    </Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <SmartMailerInfoBox backgroundColor={"hsl(200, 20%, 90%)"}>
                        <Typography
                            variant="body700"
                            gutterBottom
                            component="div"
                        >
                            {t("Tramite utilizzo del codice pratica")}
                        </Typography>
                        <Typography variant="body" gutterBottom component="div">
                            {t(
                                "Puoi inviare o inoltrare una mail all'indirizzo"
                            )}
                        </Typography>
                        <Typography
                            variant="bodySmall700"
                            gutterBottom
                            component="div"
                            style={{ textAlign: "center" }}
                        >
                            {t(
                                `codice_della_pratica @ ${usedSubdomain}.netlex.cloud`
                            )}
                        </Typography>
                        <Typography variant="body" gutterBottom component="div">
                            {t(
                                "Per averla fascicolata nella pratica corrispondente al codice che hai indicato prima della chiocciola."
                            )}
                        </Typography>
                        <Typography
                            variant="bodySmall700"
                            gutterBottom
                            component="div"
                        >
                            {t("Caso d'uso")}
                        </Typography>
                        <Grid
                            container
                            gridTemplateColumns={2}
                            spacing={8}
                            pt={1}
                        >
                            <Grid
                                item
                                xs={2}
                                style={{
                                    display: "flex",
                                    justifyContent: "flex-end",
                                    alignItems: "flex-end",
                                    paddingBottom: "1rem",
                                }}
                            >
                                <Typography
                                    variant="bodySmall700"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Esigenza")}
                                </Typography>
                            </Grid>
                            <Grid item xs={10}>
                                <Typography
                                    variant="body"
                                    gutterBottom
                                    component="div"
                                >
                                    {t(
                                        "Ricevi una mail inerente ad una pratica su cui stai lavorando, ad esempio la pratica Mario Rossi contro Franco Verdi. Hai quindi bisogno di allegarla alla pratica in netlex n° 14540."
                                    )}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container gridTemplateColumns={2} spacing={8}>
                            <Grid
                                item
                                xs={2}
                                style={{
                                    display: "flex",
                                    justifyContent: "flex-end",
                                    alignItems: "flex-end",
                                    paddingBottom: "1rem",
                                }}
                            >
                                <Typography
                                    variant="bodySmall700"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Soluzione ")}
                                </Typography>
                            </Grid>
                            <Grid item xs={10}>
                                <Typography
                                    variant="body"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Inoltra l'email all'indirizzo ")}{" "}
                                    <b>
                                        {t(
                                            `14540@${usedSubdomain}.netlex.cloud `
                                        )}
                                    </b>
                                    {t(
                                        "Entrando nella pratica troverai l'email fascicolata insieme alle altre."
                                    )}
                                </Typography>
                            </Grid>
                        </Grid>
                    </SmartMailerInfoBox>

                    <div style={{ paddingBottom: "10px", paddingTop: "10px" }}>
                        <SmartMailerInfoBox
                            backgroundColor={"hsl(200, 20%, 90%)"}
                        >
                            <Typography
                                variant="body700"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Tramite utilizzo del campo personalizzabile 'SmartMailer email' che si trova nei dati generali della pratica"
                                )}
                            </Typography>
                            <Typography
                                variant="body"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Puoi inviare o inoltrare una mail all'indirizzo da te inserito"
                                )}
                            </Typography>
                            <Typography
                                variant="bodySmall700"
                                gutterBottom
                                component="div"
                                style={{ textAlign: "center" }}
                            >
                                {t(
                                    `rossiverdi @ ${usedSubdomain}.netlex.cloud`
                                )}
                            </Typography>
                            <Typography
                                variant="body"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Per averla fascicolata nella pratica che ha il campo 'SmartMailer email' uguale al testo che hai indicato prima della chiocciola."
                                )}
                            </Typography>
                            <Typography
                                variant="bodySmall700"
                                gutterBottom
                                component="div"
                            >
                                {t("Caso d'uso")}
                            </Typography>
                            <Grid
                                container
                                gridTemplateColumns={2}
                                spacing={8}
                                pt={1}
                            >
                                <Grid
                                    item
                                    xs={2}
                                    style={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        alignItems: "flex-end",
                                        paddingBottom: "1rem",
                                    }}
                                >
                                    <Typography
                                        variant="bodySmall700"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Esigenza")}
                                    </Typography>
                                </Grid>
                                <Grid item xs={10}>
                                    <Typography
                                        variant="body"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t(
                                            "Ricevi una mail inerente ad una pratica su cui stai lavorando, ad esempio la pratica Mario Rossi contro Franco Verdi. Hai quindi bisogno di allegarla alla pratica in netlex in cui hai precedentemente compilato 'SmartMailer email' con il testo 'rossiverdi'"
                                        )}
                                    </Typography>
                                </Grid>
                            </Grid>
                            <Grid container gridTemplateColumns={2} spacing={8}>
                                <Grid
                                    item
                                    xs={2}
                                    style={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        alignItems: "flex-end",
                                        paddingBottom: "1rem",
                                    }}
                                >
                                    <Typography
                                        variant="bodySmall700"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Soluzione ")}
                                    </Typography>
                                </Grid>
                                <Grid item xs={10}>
                                    <Typography
                                        variant="body"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Inoltra l'email all'indirizzo ")}{" "}
                                        <b>
                                            {t(
                                                `rossiverdi@${usedSubdomain}.netlex.cloud `
                                            )}
                                        </b>
                                        {t(
                                            "Entrando nella pratica troverai l'email fascicolata insieme alle altre."
                                        )}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </SmartMailerInfoBox>
                    </div>

                    <Typography
                        variant="body"
                        gutterBottom
                        component="div"
                        sx={{
                            fontStyle: "italic",
                            pt: 2,
                            pb: 1,
                        }}
                    >
                        {t(
                            "Ricorda che per poter inviare le email allo smart mailer della tua distribuzione devi autorizzare l'indirizzo del mittente, cliccando sul pulsante blu qui a sinistra. "
                        )}
                    </Typography>
                    <Typography
                        variant="body"
                        gutterBottom
                        component="div"
                        sx={{
                            fontStyle: "italic",
                            pb: 1,
                        }}
                    >
                        <b>{t("Attenzione! ")}</b>
                        {t(
                            "Non è possibile utilizzare il codice archivio arbitrario, Smart Mailer funziona solo con il codice della pratica, il codice numerico ed univoco."
                        )}
                    </Typography>
                    <Typography
                        variant="body"
                        gutterBottom
                        component="div"
                        sx={{
                            fontStyle: "italic",
                            pb: 1,
                        }}
                    >
                        {t(
                            "Nel caso ci fosse un errore nel codice indicato, Smart Mailer non può associare l'email ad una pratica. In tal caso verrà posizionata in MailBox --> SmartMailer da dove potrà essere smistata manualmente."
                        )}
                    </Typography>
                </AccordionDetails>
            </Accordion>
            <Accordion>
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel2-content"
                    id="panel2-header"
                >
                    <Typography
                        variant="body500"
                        gutterBottom
                        component="div"
                        color="primary.interactiveDefault"
                    >
                        {t(
                            "Smart Mailer permette di salvare gli allegati delle email in arrivo su delle caselle dinamiche direttamente nei documenti della pratica"
                        )}
                    </Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <SmartMailerInfoBox backgroundColor={"hsl(200, 20%, 90%)"}>
                        <Typography
                            variant="body700"
                            gutterBottom
                            component="div"
                        >
                            {t("Tramite utilizzo del codice pratica")}
                        </Typography>
                        <Typography variant="body" gutterBottom component="div">
                            {t(
                                "Puoi inviare o inoltrare una mail all'indirizzo"
                            )}
                        </Typography>
                        <Typography
                            variant="bodySmall700"
                            gutterBottom
                            component="div"
                            style={{ textAlign: "center" }}
                        >
                            {t(
                                `carica codice_della_pratica @ ${usedSubdomain}.netlex.cloud`
                            )}
                        </Typography>
                        <Typography variant="body" gutterBottom component="div">
                            {t(
                                "Per salvare gli allegati che invii come documenti nella pratica corrispondente al codice che hai indicato dopo il prefisso 'carica' e prima della chiocciola."
                            )}
                        </Typography>
                        <Typography
                            variant="bodySmall700"
                            gutterBottom
                            component="div"
                        >
                            {t("Caso d'uso")}
                        </Typography>
                        <Grid
                            container
                            gridTemplateColumns={2}
                            spacing={8}
                            pt={1}
                        >
                            <Grid
                                item
                                xs={2}
                                style={{
                                    display: "flex",
                                    justifyContent: "flex-end",
                                    alignItems: "flex-end",
                                    paddingBottom: "1rem",
                                }}
                            >
                                <Typography
                                    variant="bodySmall700"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Esigenza")}
                                </Typography>
                            </Grid>
                            <Grid item xs={10}>
                                <Typography
                                    variant="body"
                                    gutterBottom
                                    component="div"
                                >
                                    {t(
                                        "Ricevi una mail inerente ad una pratica su cui stai lavorando, ad esempio la pratica n° 14540 e vuoi salvare gli allegati tra i documenti della pratica."
                                    )}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container gridTemplateColumns={2} spacing={8}>
                            <Grid
                                item
                                xs={2}
                                style={{
                                    display: "flex",
                                    justifyContent: "flex-end",
                                    alignItems: "flex-end",
                                    paddingBottom: "1rem",
                                }}
                            >
                                <Typography
                                    variant="bodySmall700"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Soluzione ")}
                                </Typography>
                            </Grid>
                            <Grid item xs={10}>
                                <Typography
                                    variant="body"
                                    gutterBottom
                                    component="div"
                                >
                                    {t("Inoltra l'email all'indirizzo ")}{" "}
                                    <b>
                                        {t(
                                            `carica14540@${usedSubdomain}.netlex.cloud `
                                        )}
                                    </b>
                                    {t(
                                        "Entrando nella pratica troverai gli allegati salvati tra i documenti."
                                    )}
                                </Typography>
                            </Grid>
                        </Grid>
                    </SmartMailerInfoBox>

                    <div style={{ paddingBottom: "10px", paddingTop: "10px" }}>
                        <SmartMailerInfoBox
                            backgroundColor={"hsl(200, 20%, 90%)"}
                        >
                            <Typography
                                variant="body700"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Tramite utilizzo del campo personalizzabile 'SmartMailer carica' che si trova nei dati generali della pratica"
                                )}
                            </Typography>
                            <Typography
                                variant="body"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Puoi inviare o inoltrare una mail all'indirizzo da te inserito"
                                )}
                            </Typography>
                            <Typography
                                variant="bodySmall700"
                                gutterBottom
                                component="div"
                                style={{ textAlign: "center" }}
                            >
                                {t(
                                    `rossiverdidoc @ ${usedSubdomain}.netlex.cloud`
                                )}
                            </Typography>
                            <Typography
                                variant="body"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Per averla fascicolata nella pratica che ha il campo 'SmartMailer email' uguale al testo che hai indicato prima della chiocciola."
                                )}
                            </Typography>
                            <Typography
                                variant="bodySmall700"
                                gutterBottom
                                component="div"
                            >
                                {t("Caso d'uso")}
                            </Typography>
                            <Grid
                                container
                                gridTemplateColumns={2}
                                spacing={8}
                                pt={1}
                            >
                                <Grid
                                    item
                                    xs={2}
                                    style={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        alignItems: "flex-end",
                                        paddingBottom: "1rem",
                                    }}
                                >
                                    <Typography
                                        variant="bodySmall700"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Esigenza")}
                                    </Typography>
                                </Grid>
                                <Grid item xs={10}>
                                    <Typography
                                        variant="body"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t(
                                            "Ricevi una mail inerente ad una pratica su cui stai lavorando, ad esempio la pratica Mario Rossi contro Franco Verdi. Hai quindi bisogno di salvare gli allegati tra i documenti della pratica di netlex in cui hai precedentemente compilato 'SmartMailer carica' con il testo 'rossiverdidoc'"
                                        )}
                                    </Typography>
                                </Grid>
                            </Grid>
                            <Grid container gridTemplateColumns={2} spacing={8}>
                                <Grid
                                    item
                                    xs={2}
                                    style={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        alignItems: "flex-end",
                                        paddingBottom: "1rem",
                                    }}
                                >
                                    <Typography
                                        variant="bodySmall700"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Soluzione ")}
                                    </Typography>
                                </Grid>
                                <Grid item xs={10}>
                                    <Typography
                                        variant="body"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Inoltra l'email all'indirizzo ")}{" "}
                                        <b>
                                            {t(
                                                `rossiverdidoc@${usedSubdomain}.netlex.cloud `
                                            )}
                                        </b>
                                        {t(
                                            "Entrando nella pratica troverai gli allegati salvati tra i documenti."
                                        )}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </SmartMailerInfoBox>
                    </div>
                </AccordionDetails>
            </Accordion>
            <div style={{ paddingTop: "5px", paddingBottom: "5px" }}>
                <SmartMailerInfoBox backgroundColor={"rgb(253, 185, 39)"}>
                    <Typography variant="body" gutterBottom component="div">
                        <b>{t("N.B. ")}</b>
                        {t(
                            "Le email che devono essere smistate da Smart Mailer devono essere inviate una alla volta.(Bisogna inserire nel campo destinatario 'A:' un solo indirizzo)."
                        )}
                    </Typography>
                </SmartMailerInfoBox>
            </div>
        </Box>
    );
};
