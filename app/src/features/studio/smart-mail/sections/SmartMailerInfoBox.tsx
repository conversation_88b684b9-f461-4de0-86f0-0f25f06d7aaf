import { Box } from "@vapor/react-material";
import { ReactElement, ReactNode } from "react";

type Props = {
    backgroundColor: string;
    children: ReactNode;
};

export const SmartMailerInfoBox = ({backgroundColor, children}: Props): ReactElement => {
    return (
        <Box
            component="section"
            sx={{
                padding: "15px",
                color: "#3a87ad",
                backgroundColor: backgroundColor,
                width: "auto",
                height: "fit-content",
                borderColor: "#bce8f1",
            }}
        >
            {children}
        </Box>
    );
};
