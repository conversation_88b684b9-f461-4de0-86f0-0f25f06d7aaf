import { useTranslation } from "@1f/react-sdk";
import { DEFAULT_LIST_PARAMS } from "../index";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import Spinner from "../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { Box, Button, TextField } from "@vapor/react-material";
import AddIcon from "@mui/icons-material/Add";
import { useNavigate } from "react-router-dom";
import { debounce } from "lodash";
import { getSmartMailerGrid } from "../../../../utilities/smartMailer/gridColumn";

export const SmartMailList = () => {
    const [defaultParams, setDefaultParams] =
        useState<any>(DEFAULT_LIST_PARAMS);
    const [data, setData] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);

    const { t } = useTranslation();

    const navigate = useNavigate();
    const sortSettingsListRequest = useGetCustom(
        "sortersettings/list",
        defaultParams
    );
    const resetSettingsListRequest = useGetCustom(
        "sortersettings/list",
        DEFAULT_LIST_PARAMS
    );

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            startSearchList();
        }, 500);
        defaultParams.searchField && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [defaultParams.searchField]);

    useEffect(() => {
        const gridColumns: any[] = getSmartMailerGrid(t);
        setColumns(gridColumns);
    }, []);

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
    ]);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? resetSettingsListRequest.doFetch(true)
            : sortSettingsListRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;

        setData(currentPage);
        setTotalRows(totalRows);
    };

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickCallback = (uniqueId: string) => {
        const rowData = data.find((item: any) => item.uniqueid === uniqueId);
        rowData &&
            navigate("/sortersettings/update/" + uniqueId, { state: rowData });
    };

    const handleSearch = () => {
        startSearchList();
    };

    const handleClear = () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="smartMailer"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    sortSettingsListRequest.loading ||
                    sortSettingsListRequest.loading
                }
                onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickCallback={onClickCallback}
            />
        );
    };

    return (
        <>
            <Box
                display="flex"
                alignItems="end"
                gap={1}
                sx={{ paddingBottom: "1rem" }}
            >
                <TextField
                    label={t("Indirizzo")}
                    inputProps={{ "aria-label": "enter address" }}
                    sx={{ width: 1 / 2 }}
                    value={defaultParams.searchField}
                    onChange={(event: any) =>
                        setDefaultParams((prevParams: any) => ({
                            ...prevParams,
                            searchField: event.target.value,
                        }))
                    }
                />
                <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSearch}
                >
                    {t("Cerca")}
                </Button>

                <Button
                    variant="contained"
                    color="primary"
                    onClick={handleClear}
                >
                    {t("Mostra tutti")}
                </Button>
                <Button
                    startIcon={<AddIcon />}
                    type="button"
                    variant="outlined"
                    onClick={() => navigate("/sortersettings/create")}
                >
                    {" "}
                    {t("Nuova Email")}
                </Button>
            </Box>
            {renderDataTable()}
        </>
    );
};
