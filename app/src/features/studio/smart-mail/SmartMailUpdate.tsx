import { Box, Button } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../custom-components/FormInput";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import usePostCustom from "../../../hooks/usePostCustom";
import ConfirmModal from "../../../custom-components/ConfirmModal";

const getSchemaValidation = (t: any) => {
    return yup.object().shape({
        nome: yup.string().required(t("Nome obbligatorio")),
        id: yup.string(),
        indirizzo: yup.string(),
        uniqueid: yup.string(),
        permessi: yup.string().nullable(),
    });
};

type FormKeys = "nome" | "id" | "indirizzo" | "uniqueid" | "permessi";

export const SmartMailUpdate = () => {
    const [title, setTitle] = useState("");
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    const location = useLocation();
    const navigate = useNavigate();

    const { uid: uniqueId } = useParams<{ uid: string }>();

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
    } = useForm({
        resolver: yupResolver(getSchemaValidation(t)),
        defaultValues: {
            id: "",
            indirizzo: "",
            nome: "",
            permessi: "",
            uniqueid: "",
        },
    });

    const values = watch();

    const usePostRequest = usePostCustom(`sortersettings/save`);
    const useCheckDataRequest = usePostCustom(
        `sortersettings/checkdata?noTemplateVars=true`
    );
    const deleteRequest = usePostCustom(
        "sortersettings/delete?noTemplateVars=true&uniqueid=" + uniqueId
    );

    useEffect(() => {
        if (location?.state) {
            Object.keys(location.state).forEach((key: string) => {
                setValue(key as FormKeys, location.state[key]);
            });
            setTitle(`${t("Riepilogo")} ${location.state.nome}`);
        } else {
            setTitle(t("Nuova email"));
        }
    }, []);

    const onSubmit = async (data: any) => {
        data.merge = false;
        const checkParams = {
            key: "nome",
            value: data.nome,
            uid: uniqueId,
        };
        await useCheckDataRequest.doFetch(true, checkParams);
        await usePostRequest.doFetch(true, data);
        navigate("/sortersettings");
    };

    const handleDelete = () => {
        setOpen(true);
    };
    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        const response: any = await deleteRequest.doFetch(true, {}, "delete");
        if (response.status === 200) {
            navigate("/sortersettings");
        }
        setOpen(false);
    };

    return (
        <>
            <VaporPage title={title}>
                <ConfirmModal
                    open={open}
                    handleDecline={handleDecline}
                    handleAgree={handleAgree}
                    decline={t("No")}
                    agree={t("Si, Elimina")}
                    confirmText={`${t("Eliminare definitivamente la mail")} "${
                        values.nome
                    }"?`}
                    title={t("Elimina azione")}
                />
                <VaporPage.Section>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <Box
                            component={"section"}
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 250,
                                },
                            }}
                        >
                            <FormInput
                                control={control}
                                name="nome"
                                label={t("Nome")}
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    !!(
                                        errors &&
                                        errors["nome"] &&
                                        errors["nome"]["message"] != ""
                                    )
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                                fullWidth
                            />
                            <FormInput
                                control={control}
                                name="indirizzo"
                                label={t("Indirizzo")}
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    !!(
                                        errors &&
                                        errors["indirizzo"] &&
                                        errors["indirizzo"]["message"] != ""
                                    )
                                }
                                helperText={
                                    errors &&
                                    errors["indirizzo"] &&
                                    errors["indirizzo"]["message"]
                                }
                            />
                        </Box>
                        <Box
                            component={"section"}
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 100,
                                },
                            }}
                        >
                            <Button
                                type="button"
                                variant="outlined"
                                onClick={() => navigate("/sortersettings")}
                                sx={{ mr: 1 }}
                            >
                                {t("Annulla")}
                            </Button>
                            {uniqueId && (
                                <Button
                                    type="button"
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    sx={{ mr: 1 }}
                                >
                                    {t("Elimina")}
                                </Button>
                            )}
                            <Button
                                type="submit"
                                variant="contained"
                                sx={{ mr: 1 }}
                            >
                                {t("Conferma")}
                            </Button>
                        </Box>
                    </form>
                </VaporPage.Section>
            </VaporPage>
        </>
    );
};
