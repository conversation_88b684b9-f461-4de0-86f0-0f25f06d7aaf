import VaporPage from "@vapor/react-custom/VaporPage";
import { useTranslation } from "@1f/react-sdk";
import { Grid } from "@vapor/react-material";
import { SmartMailList } from "./sections/SmartMailList";
import { SmartMailGuide } from "./sections/SmartMailGuide";


const SmartMailIndex = () => {

    const {t} = useTranslation();

    return (
        <VaporPage title={t("AUTORIZZAZIONE SMART MAILER")}>
            <VaporPage.Section>
                <Grid container spacing={4}>
                    <Grid item md={6} sm={6} lg={6}>
                        <SmartMailList/>
                    </Grid>
                    <Grid item md={6} sm={6} lg={6}>
                        <SmartMailGuide/>
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    )
}

export default SmartMailIndex;