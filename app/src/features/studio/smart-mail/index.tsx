import SmartMailIndex from "./SmartMailIndex";
import { SmartMailUpdate } from "./SmartMailUpdate";

export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    page: 0,
    pageSize: 10,
    sortColumn: "nome",
    sortOrder: "asc",
    searchField: "",
    isOnClear: false
};


export const smartMail = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sortersettings",
            element: <SmartMailIndex/>,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sortersettings/create",
            element: <SmartMailUpdate/>,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sortersettings/update/:uid",
            element: <SmartMailUpdate/>,
        },
    },
];
