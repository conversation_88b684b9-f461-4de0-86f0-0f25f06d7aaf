import { useEffect, useState } from "react";
import PageTitle from "../../../custom-components/PageTitle";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useTranslation } from "@1f/react-sdk";
import { DEFAULT_LIST_PARAMS } from "./index";
import useGetCustom from "../../../hooks/useGetCustom";
import Spinner from "../../../custom-components/Spinner";

import {
    Box,
    Button,
    TextField,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from "@vapor/react-material";
import AddIcon from "@mui/icons-material/Add";
import { useNavigate } from "react-router-dom";
import { debounce } from "lodash";
import { useEmailHook } from "./hooks/useEmailHook";
import { getEmailAccountGrid } from "../../../utilities/emailAccount/gridColumn";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

const EmailIndex = () => {
    const [defaultParams, setDefaultParams] =
        useState<any>(DEFAULT_LIST_PARAMS);
    const [data, setData] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);

    const { t } = useTranslation();

    const navigate = useNavigate();
    const emailListRequest = useGetCustom("emailaccounts/list", defaultParams);
    const resetEmailListRequest = useGetCustom(
        "emailaccounts/list",
        DEFAULT_LIST_PARAMS
    );

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            startSearchList();
        }, 500);
        defaultParams.email && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [defaultParams.email]);

    useEffect(() => {
        const gridColumns: any[] = getEmailAccountGrid(t);
        setColumns(gridColumns);
    }, []);

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.user_id,
    ]);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? resetEmailListRequest.doFetch(true)
            : emailListRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;
        setData(currentPage);
        setTotalRows(totalRows);
    };

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const { users } = useEmailHook();

    const onClickCallback = (uniqueId: string) => {
        const rowData = data.find((item: any) => item.uniqueid === uniqueId);
        rowData &&
            navigate("/emailaccounts/update/" + uniqueId, { state: rowData });
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };
    const handleSearch = () => {
        startSearchList();
    };

    const handleClear = () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="emailAccounts"
                onClickCheckboxKey="id"
                selectableRows
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={emailListRequest.loading || emailListRequest.loading}
                onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickCallback={onClickCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("CONFIGURAZIONE ACCOUNT EMAIL")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: "Nuova account email",
                        onclick: () => navigate("/emailaccounts/create"),
                        variant: "outlined",
                        startIcon: <AddIcon />,
                    },
                ]}
            ></PageTitle>

            <VaporPage.Section>
                <Box
                    display="flex"
                    alignItems="end"
                    gap={1}
                    sx={{ paddingBottom: "1rem" }}
                >
                    <TextField
                        label={t("Email")}
                        inputProps={{ "aria-label": "enter address" }}
                        sx={{ width: 1 / 5 }}
                        value={defaultParams.email}
                        onChange={(event: any) =>
                            setDefaultParams((prevParams: any) => ({
                                ...prevParams,
                                email: event.target.value,
                            }))
                        }
                    />
                    <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
                        <InputLabel id="select-label">{t("Utenti")}</InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.user_id}
                            label={t("Tutti gli utenti")}
                            onChange={onChangeInput}
                            name="user_id"
                        >
                            <MenuItem value="0">
                                {t("Tutti gli utenti")}
                            </MenuItem>
                            {users?.map((user) => (
                                <MenuItem key={user.id} value={user.id}>
                                    {user.nomeutente}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSearch}
                    >
                        {t("Cerca")}
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleClear}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
                {renderDataTable()}
            </VaporPage.Section>
        </VaporPage>
    );
};

export default EmailIndex;
