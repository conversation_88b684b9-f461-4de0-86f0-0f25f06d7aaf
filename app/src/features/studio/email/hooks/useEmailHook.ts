import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { getEmailAccountGrid } from "../../../../utilities/emailAccount/gridColumn";

export const useEmailHook = () => {
    const { t } = useTranslation();
    const [users, setUsers] = useState<any[]>([]);
    const [pecProviders, setPecProviders] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const emailRequest = useGetCustom("emailaccounts");

    useEffect(() => {
        async function initEmail() {
            try {
                const response: any = await emailRequest.doFetch();
                setUsers(response.data.users);
                setPecProviders(response.data.pecProviders);
                const finalColumns: any = getEmailAccountGrid(t);
                setColumns(finalColumns);
            } catch (error) {
                console.log("EmailRequest error", error);
                return;
            }
        }

        initEmail();
    }, []);

    return {
        t,
        users,
        columns,
        pecProviders,
    };
};
