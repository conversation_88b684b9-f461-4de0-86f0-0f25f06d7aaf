import * as yup from "yup";

export const getSchemaValidation = (
  t: any,
  imapGestore: string,
  email_type: string,
  isOnUpdate: boolean
) => {
  const action = isOnUpdate ? "update" : "create";
  const isMicrosoftSelected = imapGestore === "1";

  const baseSchema = {
    email: yup
      .string()
      .required(t("Email obbligatorio"))
      .email(t("Email NON conforme")),
    email_type: yup.string().required(t("Tipologia obbligatorio")),
    username: isMicrosoftSelected
      ? yup.string()
      : yup.string().required(t("Username obbligatorio")),
    password: yup.string(),
    passwordConfirm: yup.string(),
    pec_provider_id: yup.string().nullable(),
    uniqueId: yup.string(),
    imap_gestore: yup.string(),
    imap_server_in: yup.string(),
    imap_server_out: yup.string(),
    use_ssl: yup.string(),
    workflow: yup.string(),
    reginde: yup.string(),
  };

  if (Number(email_type) === 1) {
    baseSchema.email = yup
      .string()
      .required(t("Email obbligatorio"))
      .email(t("Email NON conforme"));
    baseSchema.username = yup.string().required(t("Username obbligatorio"));
    baseSchema.password =
      action === "create"
        ? yup.string().required(t("Password obbligatorio"))
        : yup.string();
    baseSchema.passwordConfirm =
      action === "create"
        ? yup.string().when("password", (password, schema) => {
            return !!password[0]
              ? schema.oneOf(
                  [yup.ref("password")],
                  t("Conferma NON corrispondente")
                )
              : schema.required(t("Password confirmation is required"));
          })
        : yup
            .string()
            .oneOf([yup.ref("password")], t("Conferma NON corrispondente"));
  }

  if (Number(email_type) === 0 && !isMicrosoftSelected) {
    baseSchema.imap_gestore = yup
      .string()
      .required(t("Gestore email obbligatorio"));
    baseSchema.imap_server_in = yup
      .string()
      .required(t("IMAP server entrata obbligatorio"));
    baseSchema.imap_server_out = yup
      .string()
      .required(t("SMTP server uscita obbligatorio"));
    baseSchema.username = yup.string().required(t("Username obbligatorio"));
    baseSchema.password =
      action === "create"
        ? yup.string().required(t("Password obbligatorio"))
        : yup.string();
    baseSchema.passwordConfirm =
      action === "create"
        ? yup
            .string()
            .oneOf([yup.ref("password")], t("Conferma NON corrispondente"))
            .required("Password confirmation is required")
        : yup
            .string()
            .oneOf([yup.ref("password")], t("Conferma NON corrispondente"));
  }

  return yup.object().shape(baseSchema);
};
