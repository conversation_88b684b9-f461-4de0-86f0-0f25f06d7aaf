import EmailIndex from "./EmailIndex";
import { EmailUpdate } from "./sections/EmailUpdate";

export const DEFAULT_LIST_PARAMS = {
  noTemplateVars: true,
  page: 0,
  pageSize: 10,
  sortColumn: "email",
  sortOrder: "asc",
  email: "",
  user_id: 0,
  isOnClear: false,
};

export const Email = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/emailaccounts",
      element: <EmailIndex />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/emailaccounts/create",
      element: <EmailUpdate />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/emailaccounts/update/:uid",
      element: <EmailUpdate />,
    },
  },
];
