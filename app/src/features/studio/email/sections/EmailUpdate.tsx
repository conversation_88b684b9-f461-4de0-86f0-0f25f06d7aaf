import {
    Box,
    Button,
    FormControl,
    FormLabel,
    FormControlLabel,
    RadioGroup,
    Radio,
    FormGroup,
    Typography,
} from "@vapor/react-material";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import SpinnerButton from "../../../../custom-components/SpinnerButton";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useTranslation } from "@1f/react-sdk";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { useConfigs } from "../../../../store/ConfigStore";
import usePostCustom from "../../../../hooks/usePostCustom";
import FormInput from "../../../../custom-components/FormInput";
import { useEmailHook } from "../hooks/useEmailHook";
import { getSchemaValidation } from "../utils/validation";
import { PecFormContent } from "./PecFormContent";
import { OrdinariaFormContent } from "./OrdinariaFormContent";

type FormKeys =
    | "email"
    | "username"
    | "password"
    | "pec_provider_id"
    | "uniqueId"
    | "passwordConfirm"
    | "email_type"
    | "imap_gestore"
    | "imap_server_in"
    | "imap_server_out"
    | "use_ssl"
    | "reginde"
    | "workflow";

const BOOLEAN_FIELDS = ["workflow", "use_ssl", "reginde"];

export const EmailUpdate = () => {
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    const [show, setShow] = useState(false);
    const [modificable, setModificable] = useState(true);
    const [type, setType] = useState<string>("1");
    const [imapGestore, setImapGestore] = useState<string>("0");
    const [loader, setLoader] = useState(false);

    const navigate = useNavigate();
    const { configs }: any = useConfigs();
    const displayWorkFlow = configs?.data?.app?.workflow_bool || false;

    const { uid: uniqueId } = useParams<{ uid: string }>();
    const location = useLocation();

    const isOnUpdate = !!uniqueId;

    const emailGetRow = usePostCustom(
        `emailaccounts/getrowdata?noTemplateVars=true`
    );

    const { control, handleSubmit, setValue, watch } = useForm({
        resolver: yupResolver(
            getSchemaValidation(t, imapGestore, type, isOnUpdate)
        ),
        defaultValues: {
            pec_provider_id: "0",
            email: "",
            username: "",
            password: "",
            passwordConfirm: "",
            uniqueId: "",
            email_type: "",
            imap_gestore: "",
            imap_server_in: "",
            imap_server_out: "",
            use_ssl: "",
            workflow: "",
            reginde: "",
        },
    });

    const imapGestoreValue = watch("imap_gestore");

    useEffect(() => {
        setImapGestore(imapGestoreValue as string);
    }, [imapGestoreValue]);

    useEffect(() => {
        async function initRow() {
            try {
                const response: any = await emailGetRow.doFetch(true, {
                    uniqueId: uniqueId,
                });

                const { form, email_type, canModify } = response.data;
                setValue("email_type", email_type.toString());
                setType(email_type.toString());

                Object.keys(form).forEach((key: string) => {
                    setValue(key as FormKeys, form[key]);
                });

                setModificable(canModify !== "0");
            } catch (error) {
                console.log("EmailGetRow error", error);
                return;
            }
        }

        initRow();
    }, []);

    const usePostRequest = usePostCustom(
        `emailaccounts/save?noTemplateVars=true`
    );

    const deleteRequest = usePostCustom(
        "emailaccounts/delete?noTemplateVars=true&uniqueid=" + uniqueId
    );

    const { pecProviders } = useEmailHook();

    useEffect(() => {
        if (pecProviders.length > 0) {
            setValue("pec_provider_id", pecProviders[0].id || "");
        }
    }, [pecProviders?.length]);

    const onSubmit = async (data: any) => {
        Object.keys(data).forEach((key: string) => {
            if (BOOLEAN_FIELDS.includes(key)) {
                if (data[key] && data[key] === "1") {
                    data[key] = "on";
                } else {
                    delete data[key];
                }
            }
        });
        setLoader(true);

        const response: any = await usePostRequest.doFetch(true, data);
        setLoader(false);
        if (response?.data === true) {
            if (location.state) {
                navigate("/mailbox");
            } else {
                navigate("/emailaccounts");
            }
        } else if (response?.data?.errors?.wrongCredentials) {
            setShow(true);
        }
    };

    const handleDelete = () => {
        setOpen(true);
    };
    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        const response: any = await deleteRequest.doFetch(true, {}, "delete");
        if (response.status === 200) {
            if (location.state) {
                navigate("/mailbox");
            } else {
                navigate("/emailaccounts");
            }
        }
        setOpen(false);
    };

    const handleTipologia = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = (event.target as HTMLInputElement).value;
        setType(value);
        setValue("email_type", value);
    };

    const values = watch();

    return (
        <>
            <VaporPage>
                <PageTitle
                    title={t("Scheda account email")}
                    pathToPrevPage={
                        location.state?.prevPath ?? "/emailaccounts"
                    }
                />
                <ConfirmModal
                    open={open}
                    handleDecline={handleDecline}
                    handleAgree={handleAgree}
                    decline={t("No")}
                    agree={t("Si, Elimina")}
                    confirmText={`${t(
                        "Eliminare definitivamente l'account email?"
                    )}`}
                    title={t("Elimina azione")}
                />
                <VaporPage.Section>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <Box
                            component={"section"}
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 350,
                                },
                            }}
                        >
                            <FormGroup>
                                <FormControl>
                                    <FormLabel id="demo-row-radio-buttons-group-label">
                                        {t("Tipologia email")}
                                    </FormLabel>

                                    <RadioGroup
                                        aria-labelledby="demo-radio-buttons-group-label"
                                        onChange={handleTipologia}
                                        value={type}
                                        row
                                    >
                                        <FormControlLabel
                                            control={<Radio />}
                                            label={t("PEC")}
                                            disabled={!modificable}
                                            value="1"
                                        />
                                        <FormControlLabel
                                            control={<Radio />}
                                            label={t("Ordinaria")}
                                            disabled={!modificable}
                                            value="0"
                                        />
                                    </RadioGroup>
                                </FormControl>
                            </FormGroup>

                            {type === "1" && (
                                <PecFormContent
                                    control={control}
                                    modificable={modificable}
                                    pecProviders={pecProviders}
                                />
                            )}

                            {type === "0" && (
                                <OrdinariaFormContent
                                    control={control}
                                    values={values}
                                    modificable={modificable}
                                    imapGestore={imapGestore}
                                />
                            )}
                            {displayWorkFlow && (
                                <FormGroup>
                                    <FormInput
                                        name="workflow"
                                        control={control}
                                        type="checkbox"
                                        options={[
                                            {
                                                label: t(
                                                    "Utilizza per Workflow"
                                                ),
                                                value: "1",
                                            },
                                        ]}
                                        disabled={!modificable}
                                    />
                                    {modificable && (
                                        <Typography
                                            variant="captionSmall"
                                            gutterBottom
                                            component="div"
                                        >
                                            {t(
                                                "Può essere impostato come predefinito un solo account email per ogni utente."
                                            )}
                                        </Typography>
                                    )}
                                </FormGroup>
                            )}
                        </Box>
                        <Box
                            component={"section"}
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 100,
                                },
                            }}
                        >
                            <Button
                                type="button"
                                variant="outlined"
                                onClick={() =>
                                    navigate(
                                        location.state?.prevPath ??
                                            "/emailaccounts"
                                    )
                                }
                                sx={{ mr: 1 }}
                            >
                                {t("Annulla")}
                            </Button>
                            {uniqueId && modificable && (
                                <Button
                                    type="button"
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    sx={{ mr: 1 }}
                                >
                                    {t("Elimina")}
                                </Button>
                            )}
                            {modificable && (
                                <SpinnerButton
                                    type="submit"
                                    variant="contained"
                                    isLoading={loader}
                                    label={t("Conferma")}
                                />
                            )}
                        </Box>
                    </form>
                </VaporPage.Section>
                <ToastNotification
                    showNotification={show}
                    setShowNotification={setShow}
                    severity="error"
                    text={t(
                        "Attenzione! Le credenziali inserite risultano errato!"
                    )}
                />
            </VaporPage>
        </>
    );
};
