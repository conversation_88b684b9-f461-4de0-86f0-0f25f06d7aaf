import { FormGroup } from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";

export const OrdinariaFormContent = (props: any) => {
  const { t } = useTranslation();
  const { control, values, modificable, imapGestore } = props;

  const isMicrosoftSelected = imapGestore === "1";

  return (
    <>
      <FormGroup>
        <FormInput
          control={control}
          name="imap_gestore"
          disabled={!modificable}
          type="select"
          label={t("Gestore email *")}
          value={values.imap_gestore}
          options={[
            { value: "0", label: t("Altro (server IMAP)") },
            { value: "1", label: t("Microsoft") },
          ]}
          sx={{ width: 1 }}
        />
      </FormGroup>
      {!isMicrosoftSelected && (
        <FormGroup>
          <FormInput
            control={control}
            name="imap_server_in"
            label={t("IMAP server entrata *")}
            disabled={!modificable}
            type="text"
            variant="outlined"
            fullWidth
          />
        </FormGroup>
      )}
      {!isMicrosoftSelected && (
        <FormGroup>
          <FormInput
            control={control}
            name="imap_server_out"
            label={t("SMTP server uscita *")}
            disabled={!modificable}
            type="text"
            variant="outlined"
          />
        </FormGroup>
      )}
      {!isMicrosoftSelected && (
        <FormInput
          name="use_ssl"
          control={control}
          type="checkbox"
          options={[
            { label: t("Utilizza una connessione sicura"), value: "1" },
          ]}
          disabled={!modificable}
        />
      )}
      <FormGroup>
        <FormInput
          control={control}
          name="email"
          disabled={!modificable}
          label={t("Email *")}
          type="text"
          variant="outlined"
          fullWidth
        />
      </FormGroup>
      {!isMicrosoftSelected && (
        <FormGroup>
          <FormInput
            control={control}
            name="username"
            label={t("Username *")}
            disabled={!modificable}
            type="text"
            variant="outlined"
          />
        </FormGroup>
      )}
      {!isMicrosoftSelected && (
        <FormGroup>
          <FormInput
            control={control}
            name="password"
            disabled={!modificable}
            label={t("Password *")}
            type="password"
            variant="outlined"
            autoComplete="new-password"
          />
        </FormGroup>
      )}
      {!isMicrosoftSelected && (
        <FormGroup>
          <FormInput
            control={control}
            name="passwordConfirm"
            disabled={!modificable}
            label={t("Conferma password *")}
            type="password"
            variant="outlined"
            autoComplete="new-password"
          />
        </FormGroup>
      )}
    </>
  );
};
