import { FormGroup, Typography } from "@vapor/react-material";

import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";

export const PecFormContent = (props: any) => {
  const { t } = useTranslation();
  const { control, modificable, pecProviders } = props;
  return (
    <>
      <FormGroup>
        <FormInput
          control={control}
          disabled={!modificable}
          name="pec_provider_id"
          type="select"
          label={t("Gestore PEC")}
          options={pecProviders.map((item: any) => ({
            value: item.id,
            label: item.name,
          }))}
          sx={{ width: 1 }}
        />
      </FormGroup>
      <FormInput
        name="use_ssl"
        control={control}
        type="checkbox"
        options={[{ label: t("Utilizza una connessione sicura"), value: "1" }]}
        disabled={!modificable}
      />
      <FormGroup>
        <FormInput
          control={control}
          name="email"
          disabled={!modificable}
          label={t("Indirizzo PEC *")}
          type="text"
          variant="outlined"
          fullWidth
        />
      </FormGroup>
      <FormGroup>
        <FormInput
          control={control}
          name="username"
          label={t("Username *")}
          disabled={!modificable}
          type="text"
          variant="outlined"
        />
      </FormGroup>
      <FormGroup>
        <FormInput
          control={control}
          name="password"
          disabled={!modificable}
          label={t("Password della PEC *")}
          type="password"
          variant="outlined"
          autoComplete="new-password"
        />
      </FormGroup>
      <FormGroup>
        <FormInput
          control={control}
          name="passwordConfirm"
          disabled={!modificable}
          label={t("Conferma password *")}
          type="password"
          variant="outlined"
          autoComplete="new-password"
        />
      </FormGroup>

      <FormInput
        name="reginde"
        control={control}
        type="checkbox"
        options={[
          {
            label: t(
              "Utilizza per Deposito Telematico e Fatturazione Elettronica"
            ),
            value: "1",
          },
        ]}
        disabled={!modificable}
      />

      <Typography variant="captionSmall" gutterBottom component="div">
        {t(
          "Può essere impostato come predefinito un solo account email per ogni utente."
        )}
      </Typography>
    </>
  );
};
