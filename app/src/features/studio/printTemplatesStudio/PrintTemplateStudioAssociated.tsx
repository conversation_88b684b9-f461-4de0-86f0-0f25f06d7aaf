import { useEffect, useState } from "react";
import { Box, Grid } from "@vapor/react-material";
import {
    useGetPrintTemplate,
    useUpdatePrintTemplate,
    useGetPrintTemplateStudio,
} from "./hooks/useGetPrintTemplate";
import { useTranslation } from "@1f/react-sdk";

import VaporPage from "@vapor/react-custom/VaporPage";
import ToastNotification from "../../../custom-components/ToastNotification";
import Spinner from "../../../custom-components/Spinner";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import PageTitle from "../../../custom-components/PageTitle";
import ImageCard from "./components/ImageCard";
import ShowPreviewDialog from "./components/ShowPreviewDialog";
import Filter from "./Filter";

const PrintTemplate = () => {
    const [selectedTemplateId, setSelectedTemplateId] = useState(null);
    const [defaultParams, setDefaultParams] = useState<any>({
        officesearch: "1",
    });
    const [showNotification, setShowNotification] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { t } = useTranslation();

    const { selectedItem } = useGetPrintTemplateStudio(defaultParams);
    const { officeList, printTemplateData, loading } = useGetPrintTemplate();
    const { fetchData } = useUpdatePrintTemplate(
        selectedTemplateId,
        defaultParams?.officesearch
    );

    useEffect(() => {
        setSelectedTemplateId(selectedItem);
    }, [selectedItem]);

    const handleTemplateClick = (templateId: any) => {
        setSelectedTemplateId(
            templateId === selectedTemplateId ? selectedTemplateId : templateId
        );
    };

    const handleSave = async () => {
        try {
            setIsLoading(true);
            await fetchData();
            setShowNotification(true);
            setIsLoading(false);
        } catch (error) {
            console.log(error);
        }
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({ [name]: value });
    };

    const selectedPrintTemplate = printTemplateData.find(
        (item: any) => item.id === selectedTemplateId
    );

    return (
        <VaporPage>
            <ToastNotification
                showNotification={showNotification}
                setShowNotification={setShowNotification}
                text={t("Salvato")}
                severity={"success"}
            />
            <PageTitle
                title={t("TEMPLATE STAMPA STUDIO ASSOCIATO")}
                showBackButton={false}
            />
            <Grid container spacing={2} sx={{ pt: 4, ml: 1 }}>
                <Grid item md={12} sx={{ ml: 1, mb: 3.5 }}>
                    <Filter
                        defaultParams={defaultParams}
                        onChangeInput={onChangeInput}
                        officeList={officeList}
                    />
                </Grid>
                {loading ? (
                    <Spinner fullPage={true} />
                ) : (
                    <>
                        {printTemplateData &&
                            printTemplateData.map((item: any) => (
                                <Grid
                                    item
                                    md={2.6}
                                    xs={12}
                                    key={item.id}
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                    }}
                                >
                                    <ImageCard
                                        record={item}
                                        isSelected={
                                            item.id === selectedTemplateId
                                        }
                                        onClick={() =>
                                            handleTemplateClick(item.id)
                                        }
                                    />
                                </Grid>
                            ))}
                        <Grid item md={4} xs={12}>
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    mb: 2,
                                }}
                            >
                                <ShowPreviewDialog
                                    record={selectedPrintTemplate}
                                />
                                <SpinnerButton
                                    label="Salva"
                                    variant="contained"
                                    onClick={handleSave}
                                    isLoading={isLoading}
                                    sx={{ ml: 1 }}
                                />
                            </Box>
                        </Grid>
                    </>
                )}
            </Grid>
        </VaporPage>
    );
};
export default PrintTemplate;
