import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useGetPrintTemplate = () => {
    const actualLocation = window.location.pathname.split("/printtemplates/")[1];
    const printTemplateRequest = useGetCustom(`printtemplates/${actualLocation}`);
    const [officeList, setOfficeList] = useState<any>([]);
    const [printTemplateData, setPrintTemplateData] = useState<any>([]);
    const [printTemplateId, setPrintTemplateId] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
        async function fetchData() {
            setLoading(true);
            const { data }: any = await printTemplateRequest.doFetch(true);
            setOfficeList(data?.officeList);
            setPrintTemplateData(data?.templates);
            setPrintTemplateId(data?.template)
            setLoading(false);
        }

        fetchData();
    }, []);

    return { officeList, printTemplateData, printTemplateId, loading };
}

export const useGetPrintTemplateStudio = ({ officesearch }: any) => {
    const printTemplateRequest = useGetCustom("printtemplates/gettemplate?noTemplateVars=true", { officesearch: officesearch });
    const [selectedItem, setSelectedItem] = useState<any>('');

    useEffect(() => {
        async function fetchData() {
            const { data }: any = await printTemplateRequest.doFetch(true);
            setSelectedItem(data?.id);
        }

        fetchData();
    }, [officesearch]);

    return { selectedItem };
}

export const useUpdatePrintTemplate = (id: any, selectedOffice: any) => {
    const printTemplateUpdateRequest = usePostCustom("printtemplates/settemplate");

    async function fetchData() {
        const params = new URLSearchParams();

        params.append("selectedTemplate", id);
        if (selectedOffice) {
            params.append("tiposetting", "studioassociato");
            params.append("selectedOffice", selectedOffice);
        } else {
            params.append("tiposetting", "personale");

        }
        await printTemplateUpdateRequest.doFetch(true, params);
    }

    return { fetchData };
}