import { Box, MenuItem, Select, FormControl, InputLabel } from "@vapor/react-material";

interface FilterProps {
    defaultParams: any;
    onChangeInput: (e: any) => void;
    officeList: any
}

const Filter = ({ defaultParams, onChangeInput, officeList }: FilterProps) => {
    return (
        <div>
            <Box component="form" display="flex" alignItems="end" gap={2}>
                <InputLabel htmlFor="my-input">Studio:</InputLabel>
                <FormControl sx={{ width: 1 / 5 }} >
                    <Select
                        labelId="demo-simple-select-label"
                        id="demo-simple-select"
                        name="officesearch"
                        value={defaultParams?.officesearch}
                        onChange={onChangeInput}
                    >
                        {officeList && officeList?.map((office: any) => (
                            <MenuItem key={office?.id} value={office?.id}>{office?.nome}</MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Box>
        </div>
    )
}
export default Filter;
