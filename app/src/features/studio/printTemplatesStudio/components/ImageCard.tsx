import { Card, CardContent, Box, Typography } from "@vapor/react-material";

import Image from "../../../../custom-components/Image";

const ImageCard = ({ record, isSelected, onClick }: any) => {
    return (
        <Card
            onClick={onClick}
            sx={{
                p: 1,
                maxWidth: 300,
                border: isSelected ? '1px solid #0195D4' : '',
                backgroundColor: isSelected ? '#321de1' : '',
                borderRadius: '10px'
            }}
        >
            <Box sx={{ display: "flex", justifyContent: "center" }}>
                <Image style={{ height: "100%" }} path={`/images/printtemplates/${record && record.thumb}`} alt={record?.name} />
            </Box>
            <CardContent>
                <Typography gutterBottom variant="h5" component="div">
                    {record.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    {record.description}
                </Typography>
            </CardContent>
        </Card>
    );
}
export default ImageCard;