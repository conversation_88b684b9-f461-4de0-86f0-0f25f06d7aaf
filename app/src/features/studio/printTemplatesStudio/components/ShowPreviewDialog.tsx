import * as React from 'react';
import { But<PERSON>, <PERSON>alog, DialogContent, DialogTitle } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

import Image from "../../../../custom-components/Image";

const ShowPreviewDialog = (record: any) => {
    const [open, setOpen] = React.useState(false);

    const { t } = useTranslation();

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    return (
        <React.Fragment>
            <Button variant="outlined" onClick={handleClickOpen}>
                {t("Anterprima")}
            </Button>
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="alert-dialog-title">
                    {record.record?.name}
                </DialogTitle>
                <DialogContent>
                    <div style={{ maxWidth: '100%', maxHeight: '100%' }}>
                        <Image
                            style={{ maxWidth: '100%', maxHeight: '100%' }}
                            path={`/images/printtemplates/${record.record?.preview}`}
                            alt={record?.name}
                        />
                    </div>
                </DialogContent>
            </Dialog>
        </React.Fragment>
    );
}
export default ShowPreviewDialog;