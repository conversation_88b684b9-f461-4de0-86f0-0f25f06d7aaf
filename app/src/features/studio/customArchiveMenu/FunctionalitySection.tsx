import { useState } from 'react';
import { <PERSON>rid, <PERSON>, Typography, Divider, Button } from '@vapor/react-material';
import { useTranslation } from "@1f/react-sdk";
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';

import ToastNotification from '../../../custom-components/ToastNotification';
import ConfirmModal from '../../../custom-components/ConfirmModal';
import usePostCustom from '../../../hooks/usePostCustom';

const FunctionalitySection = ({ menuItems, isRender, setIsRender, goPrev, setGoPrev }: any) => {
    const [openSuccess, setOpenSuccess] = useState<boolean>(false);
    const [openReset, setOpenReset] = useState<boolean>(false);
    const [openResetAlert, setOpenResetAlert] = useState<boolean>(false);

    const { t } = useTranslation();

    const updateMenuSection = usePostCustom('customarchivemenu/saveconfiguration');
    const resetMenuSection = usePostCustom('customarchivemenu/resetconfiguration');

    const handleUpdateMenu = async () => {
        const { show, hide } = menuItems;
        const payload = new FormData();

        show.forEach((item: any) => {
            payload.append('show[]', item);
        });

        hide.forEach((item: any) => {
            payload.append('hide[]', item);
        });

        await updateMenuSection.doFetch(true, payload);
        setOpenSuccess(true)
    }

    const onMenuReset = async () => {
        setOpenResetAlert(true)
        setOpenReset(false);
        setIsRender(!isRender);
        await resetMenuSection.doFetch(true, {});
    }

    return (
        <Box>
            <ConfirmModal
                open={openReset}
                handleDecline={() => {
                    setOpenReset(false)
                }}
                handleAgree={
                    onMenuReset
                }
                decline={t("Annulla")}
                agree={t("Ok")}
                confirmText={t("Rispristinare la visualizzazione di default?")}
                title={t("Sei sicuro?")}
            />
            <ToastNotification showNotification={openSuccess} setShowNotification={setOpenSuccess} severity='success' text={t('Configurazione salvata con successo!')} />
            <ToastNotification showNotification={openResetAlert} setShowNotification={setOpenResetAlert} severity='success' text={t('Visualizzazione ripristinata con successo!')} />
            <Typography sx={{ fontSize: 15, pt: 2 }}>{t("Muovi i box delle sezioni da un lista all'altra per nasconderle o visualizzarle.")}</Typography>
            <Grid container sx={{ pt: 3, pl: 7 }} spacing={3}>
                <Grid item md={2} sm={2}>
                    <Typography sx={{ fontWeight: "bold" }}>{t("Visualizza")}</Typography>
                </Grid>
                <Grid item md={3} sm={3}>
                    <ArrowRightAltIcon />
                </Grid>
                <Grid item md={7} sm={7}>
                    <Typography sx={{ fontSize: 15 }}>{t("Tutte le sezioni che si desidera visualizzare, nell'ordine selezionato")}</Typography>
                </Grid>
                <Grid item md={2} sm={2}>
                    <Typography sx={{ fontWeight: "bold" }}>{t("Nascondi")}</Typography>
                </Grid>
                <Grid item md={3} sm={3}>
                    <ArrowRightAltIcon />
                </Grid>
                <Grid item md={7} sm={7}>
                    <Typography sx={{ fontSize: 15 }}>{t("Le sezioni verranno nascoste dal menù laterale")}</Typography>
                </Grid>
            </Grid>
            <Divider sx={{ mt: 6 }} />
            <Box sx={{
                display: "flex",
                justifyContent: "right",
                pr: 2,
                pb: 2
            }}>
                <Button variant="outlined" sx={{ mt: 3, ml: 1 }} onClick={() => setGoPrev(!goPrev)}>{t("Annulla")}</Button>
                <Button variant="contained" onClick={() => setOpenReset(true)
                } sx={{ mt: 3, ml: 1 }}>{t("Ripristina")}</Button>
                <Button variant="contained" onClick={
                    handleUpdateMenu
                } sx={{ mt: 3, ml: 1 }}>{t("Conferma")}</Button>
            </Box>
        </Box>
    );
}
export default FunctionalitySection;