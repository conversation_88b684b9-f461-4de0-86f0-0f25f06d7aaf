import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent, Box } from "@vapor/react-material";
import { Typography } from "@vapor/react-material";

import {
    Person,
    Storage,
    SignalCellularAlt,
    CalendarMonth,
    FolderCopy,
    Home,
    InsertDriveFile,
    Email,
} from "@mui/icons-material";

import { useConfigs } from "../../../../store/ConfigStore";
import { useUser } from "../../../../store/UserStore";

export function Item(props: any) {
    const { id, configs, modules } = props;

    let value;
    let icon;
    let hide;

    if (id === "1") {
        value = "Dati pratica";
        icon = <Person />;
    } else if (id === "2") {
        value = "Statistiche";
        icon = <Storage />;
    } else if (id === "3") {
        value = "Divisione degli utili";
        icon = <SignalCellularAlt />;
    } else if (id === "4") {
        value = "Soggetti";
        icon = <SignalCellularAlt />;
    } else if (id === "5") {
        value = "Prestazioni";
        icon = <Storage />;
    } else if (id === "6") {
        value = "Udienze";
        icon = <CalendarMonth />;
    } else if (id === "7") {
        value = "Impegni";
        icon = <CalendarMonth />;
    } else if (id === "8") {
        value = "Documenti";
        icon = <FolderCopy />;
    } else if (id === "9") {
        value = "Email fascicolate";
        icon = <FolderCopy />;
    } else if (id === "10") {
        value = "Modelli";
        icon = <FolderCopy />;
    } else if (id === "11") {
        value = "Polisweb";
        icon = <Home />;
    } else if (id === "12") {
        value = "Deposito telematico";
        icon = <Home />;
    } else if (id === "13") {
        value = "Pagamento telematico";
        icon = <Home />;
    } else if (id === "14") {
        value = "Genera NIR cartacea";
        icon = <Home />;
    } else if (id === "15") {
        value = "Notifiche in proprio";
        icon = <Home />;
    } else if (id === "16") {
        value = "Nota Spese giudiziale";
        icon = <InsertDriveFile />;
    } else if (id === "17") {
        value = "Fatture, Note e Preavvisi";
        icon = <InsertDriveFile />;
    } else if (id === "18") {
        value = "Movimenti";
        icon = <InsertDriveFile />;
    } else if (id === "19") {
        value = "Contratto";
        icon = <InsertDriveFile />;
    } else if (id === "20") {
        value = "Gestione del Rischio";
        icon = <Home />;
    } else if (id === "21") {
        hide = !configs?.data?.app?.recupero_crediti_bool;
        value =
            modules && modules?.getNetlexSettingsByField("liquidation_pa") == 1
                ? "Ciclo Attivo PA"
                : "Recupero crediti";
        icon = <Home />;
    } else if (id === "22") {
        value =
            modules && modules?.getNetlexSettingsByField("liquidation_pa") == 1
                ? "Ciclo Passivo PA"
                : "Dati Liquidazione";
        icon = <Home />;
    } else if (id === "23") {
        value = "Antiriciclaggio";
        icon = <Storage />;
    } else if (id === "24") {
        value = "Visure e bilanci";
        icon = <Storage />;
    } else if (id === "25") {
        value = "TimeSheet";
        icon = <InsertDriveFile />;
    } else if (id === "26") {
        value = "Messaggi";
        icon = <Email />;
    } else if (parseInt(id) >= 27) {
        hide = true;
    }

    return (
        <>
            <>
                {!hide && (
                    <Card
                        sx={{
                            margin: 1,
                            width: "95%",
                            height: 70,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <CardContent>
                            <>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        mt: 1.5,
                                    }}
                                >
                                    {icon}
                                </Box>
                                <Typography sx={{ color: "#04ABDD", pt: 0.7 }}>
                                    {" "}
                                    {value}
                                </Typography>
                            </>
                        </CardContent>
                    </Card>
                )}
            </>
        </>
    );
}

export default function SortableItem(props: any) {
    const { attributes, listeners, setNodeRef, transform, transition } =
        useSortable({ id: props.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    const { configs }: any = useConfigs();
    const { modules }: any = useUser();

    return (
        <>
            <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
                <Item id={props.id} modules={modules} configs={configs} />
            </div>
        </>
    );
}
