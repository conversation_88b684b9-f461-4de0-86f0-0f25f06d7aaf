import { useState } from "react";
import { Grid, Box } from "@vapor/react-material";

import Index from "./components/IndexDragDrop";
import FunctionalitySection from "./FunctionalitySection";
import PageTitle from "../../../custom-components/PageTitle";

const CustomSoftwareIndex = () => {
    const [menuItems, setMenuItems] = useState<any>({});
    const [isRender, setIsRender] = useState<boolean>(false);
    const [goPrev, setGoPrev] = useState<boolean>(false);

    return (
        <Box>
            <PageTitle
                title="Personalizzazione sezioni pratica"
                showBackButton={false}
            />
            <Grid container sx={{ pt: 3, pl: 2 }}>
                <Grid item lg={6} md={12} sm={12} xs={12}>
                    <Index
                        setMenuItems={setMenuItems}
                        isRender={isRender}
                        goPrev={goPrev}
                    />
                </Grid>
                <Grid
                    item
                    lg={4.5}
                    md={12}
                    sm={12}
                    xs={12}
                    sx={{ pt: 3, ml: 5 }}
                >
                    <FunctionalitySection
                        menuItems={menuItems}
                        isRender={isRender}
                        setIsRender={setIsRender}
                        goPrev={goPrev}
                        setGoPrev={setGoPrev}
                    />
                </Grid>
            </Grid>
        </Box>
    );
};
export default CustomSoftwareIndex;
