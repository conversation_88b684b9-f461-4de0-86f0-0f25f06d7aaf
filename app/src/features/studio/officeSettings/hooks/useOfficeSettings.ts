import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useTranslation } from "@1f/react-sdk";

import {
    getListUtentiGrid,
    getOfficeSettingsGrid,
} from "../../../../utilities/officeSetting/gridColumn";

export const useGetOfficeSettings = (query: any) => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [list, setList] = useState<any>({
        rows: [],
        loading: true,
        columns: [],
    });

    const { t } = useTranslation();

    const getOfficeSettings = useGetCustom(
        "officesettings/list-office-data?noTemplateVars=true",
        query
    );

    const fetchData = async () => {
        try {
            setLoading(true);
            const { data }: any = await getOfficeSettings.doFetch(true);
            setData(data);
            const columns = await getOfficeSettingsGrid(t);
            setList({ ...list, rows: data, columns, loading: false });
            setLoading(false);
        } catch (e) {
            console.error(e);
        }
    };

    return { data, list, setList, loading, fetchData };
};

export const useGetOneRow = (id: any) => {
    const [data, setData] = useState<any>([]);
    const [iban, setIban] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const getOfficeSettings = usePostCustom(
        "officesettings/get-row?noTemplateVars=true"
    );

    const fetchData = async () => {
        try {
            setLoading(true);
            const { data }: any = await getOfficeSettings.doFetch(true, { id });
            setData(data);
            setIban(
                data?.row
                    ? JSON.parse(data?.row?.iban)
                    : [{ id: 0, desc: "", iban: "", abi: "", cab: "", bic: "" }]
            );
            setLoading(false);
        } catch (e) {
            console.error(e);
        }
    };

    useEffect(() => {
        if (id) {
            fetchData();
        }
    }, [id]);

    return { data, iban, loading, fetchData };
};

export const useGetUserList = (query: any) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isActive, setIsActive] = useState([]);
    const [list, setList] = useState({
        rows: [],
        loading: true,
        columns: [],
        totalRows: 0,
    });

    const { t } = useTranslation();
    const getUserList = useGetCustom(
        "officesettings/list?noTemplateVars=true",
        query
    );

    const handleChange = (event: any, row: any) => {
        const { checked } = event.target;

        setIsActive((prevState: any) => {
            const updatedState = prevState.map((item: any) =>
                item.id === row.id ? { ...item, active: checked } : item
            );
            if (!updatedState.find((item: any) => item.id === row.id)) {
                updatedState.push({ id: row.id, active: checked });
            }
            return updatedState;
        });
    };

    const fetchData = async () => {
        try {
            setLoading(true);
            const { data: fetchedData }: any = await getUserList.doFetch(true);
            setData(fetchedData?.currentPage || []);

            const initialActiveStates =
                fetchedData?.currentPage?.map((row: any) => ({
                    id: row.id,
                    active: row.active === "1",
                })) || [];

            setIsActive(initialActiveStates);

            const columns: any = await getListUtentiGrid(
                handleChange,
                initialActiveStates,
                setIsActive
            );
            setList({
                rows: fetchedData?.currentPage,
                columns,
                loading: false,
                totalRows: parseInt(fetchedData?.totalRows),
            });
            setLoading(false);
        } catch (e) {
            console.error(e);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, [query]);

    useEffect(() => {
        const fetchDataWithUpdatedActive = async () => {
            const columns = await getListUtentiGrid(
                handleChange,
                isActive,
                setIsActive
            );
            setList((prevList: any) => ({ ...prevList, columns }));
        };
        fetchDataWithUpdatedActive();
    }, [isActive, t]);

    return { data, list, setList, isActive, loading, fetchData };
};
