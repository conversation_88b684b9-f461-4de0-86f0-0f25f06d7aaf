import { useCallback, useState } from "react";

export default function TabShowErrorHandler(errors: any = {}, t: any) {
    const [showTabErrorNotification, setShowTabErrorNotificaion] =
        useState<boolean>(false);
    const [tabTextError, setTabTextError] = useState<string>("");
    const [shownErrorTabs, setShownErrorTabs] = useState<Set<number>>(
        new Set()
    );

    const tabErrorMapping: any = [
        {
            tabName: t("Altro"),
            fields: ["iban"],
        },
    ];

    const HasTabErrors = (tabIndex: number): boolean => {
        const { tabName, fields } = tabErrorMapping[tabIndex];

        const fieldsHaveErrors = fields.some((field: any) => {
            if (field === "iban") {
                if (Array.isArray(errors.ibanList)) {
                    return errors.ibanList.some((ibanError: any) => {
                        return (
                            ibanError &&
                            ibanError.iban &&
                            ibanError.iban.message
                        );
                    });
                }
            }
            return false;
        });

        if (fieldsHaveErrors && !shownErrorTabs.has(tabIndex)) {
            setTabErrorCallback(tabIndex, tabName);
        }

        return fieldsHaveErrors;
    };

    const setTabErrorCallback = useCallback(
        (tabIndex: number, tabName: string) => {
            setShowTabErrorNotificaion(true);
            setTabTextError(
                t(`Correggere gli errori presenti nella tab ${tabName}`)
            );
            setShownErrorTabs((prev) => new Set(prev).add(tabIndex));
        },
        []
    );

    return {
        HasTabErrors,
        showTabErrorNotification,
        setShowTabErrorNotificaion,
        tabTextError,
    };
}
