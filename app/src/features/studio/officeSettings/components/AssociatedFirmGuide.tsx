import { Box, Card, CardContent, Typography } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

const AssociatedFirmGuide = () => {
    const { t } = useTranslation();

    return (
        <Box sx={{ maxWidth: 800, margin: "auto" }}>
            <Card>
                <CardContent>
                    <Typography variant="h5" gutterBottom>
                        <strong> {t("Guida Studio Associato")} </strong>
                    </Typography>
                    <Typography variant="body1" paragraph>
                        {t("Lo studio associato permette di gestire la fatturazione con una singola partita iva.")}
                    </Typography>
                    <Typography variant="body1" paragraph>
                        {t("La configurazione dello studio associato può essere eseguita solo da un utente di tipo amministratore e")} <strong><u>{t("Unicamente")}</u></strong> {t("da questa sezione.")}
                    </Typography>
                    <Typography variant="h6" paragraph>
                        <strong> <u>({t("Facoltativo")})</u><em>{t("Per configurare lo studio associato per la fatturazione elettronica è necessario:")}</em></strong>
                    </Typography>
                    <ol>
                        <li>
                            {t("Compilare i dati dello studio associato e premere su")} <strong>{t("Richiedi PIN")}</strong> {t("per ricevere un codice tramite SMS al numero di telefono indicato.")}
                        </li>
                        <li>
                            {t("Inserire il codice ricevuto nel campo \"PIN\" e premere su")} <strong><em>{t("Conferma PIN.")}</em></strong>
                        </li>
                        <li>
                            {t("In alcuni casi, potrebbe essere necessario un ulteriore passaggio di conferma tramite email. In tal caso,")}
                        </li>
                        <ol>
                            <li> {t("seguire le istruzioni ricevute all'indirizzo email precedentemente inserito.")}</li>
                            <li> {t("Cliccare nuovamente su ")} <strong>{t("Conferma PIN.")}</strong></li>
                        </ol>
                    </ol>
                    <Typography variant="h6" gutterBottom>
                        <strong>  {t("Si ricorda inoltre che:")} </strong>
                    </Typography>
                    <ul>
                        <li> <strong><u> {t("E' necessario fare attenzione che il codice fiscale e la partita iva inseriti siano quelli dello studio e non quelli personali dell'avvocato.")} </u></strong></li>
                        <li>
                            {t("I campi Codice Fiscale e Partita Iva non saranno modificabili una volta inseriti.")}
                        </li>
                        <li>
                            {t("Una volta configurato lo studio associato, sarà possibile indicare quali utenti dovranno farne parte.")}
                        </li>
                        <li>
                            {t("Solo gli utenti inseriti potranno emettere fatture elettroniche e visualizzare le fatture inviate/ricevute.")}
                        </li>
                    </ul>
                    <Typography variant="body1" paragraph>
                        <strong>{t("N.B.")}</strong> {t("Utenti di tipo segretaria e praticante non saranno aggiunti automaticamente con l'aggiunta del rispettivo utente referente.")}
                    </Typography>
                </CardContent>
            </Card>
        </Box>
    );
}

export default AssociatedFirmGuide;
