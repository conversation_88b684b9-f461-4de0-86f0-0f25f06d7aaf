import { useEffect } from "react";
import {
    Box,
    IconButton,
    Checkbox,
    FormControl,
    InputAdornment,
    InputLabel,
    MenuItem,
    Select,
    FormControlLabel,
    OutlinedInput,
    FormGroup,
    Divider,
    Tooltip,
    Button,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import FormInput from "../../../../custom-components/FormInput";
import { useUser } from "../../../../store/UserStore";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";

import InfoIcon from "@mui/icons-material/Info";

interface OthersProps {
    errors?: any;
    register?: any;
    control?: any;
    setValue?: any;
    data: any;
    otherData?: any;
    setOtherData?: any;
    fields?: any;
    remove?: any;
    append?: any;
    Controller: any;
    reset?: any;
    getValues?: any;
}

const OthersData = ({
    errors,
    setValue,
    control,
    register,
    data,
    otherData,
    setOtherData,
    fields,
    append,
    remove,
    Controller,
    getValues,
}: OthersProps) => {
    const { t } = useTranslation();
    const { user }: any = useUser();
    const currentValues = getValues();

    const addInput = () => {
        append({
            desc: "",
            iban: "",
            abi: "",
            cab: "",
            bic: "",
            isDefault: currentValues?.ibanList?.length === 0 ? 1 : 0,
        });
    };

    const removeInput = (index: number) => {
        remove(index);
    };

    useEffect(() => {
        if (data) {
            setValue(
                "codice_destinatario_af",
                data?.row?.codice_destinatario_af
            );
            setValue("perc_cassa", data?.row?.perc_cassa || 4.0);
            setValue("regime_fiscale", data?.row?.regime_fiscale);
            setValue("tipo_cassa", data?.row?.tipo_cassa);
            setValue("perc_cassa_2", data?.row?.perc_cassa_2 || 4.0);
            setOtherData({
                ...otherData,
                regime_fiscale: data?.row?.regime_fiscale || "1",
                tipo_cassa: data?.row?.tipo_cassa || "1",
                tipo_cassa_2: data?.row?.tipo_cassa_2 || "1",
                addCassa:
                    data?.row?.tipo_cassa_2 && data?.row?.perc_cassa_2
                        ? true
                        : false,
                nascondi_codice_fiscale:
                    data?.row?.nascondi_codice_fiscale === "1" ? true : false,
                nascondi_codice_fiscale_xml:
                    data?.row?.nascondi_codice_fiscale_xml === "1"
                        ? true
                        : false,
                nascondi_dati_pagamento_xml:
                    data?.row?.nascondi_dati_pagamento_xml === "1"
                        ? true
                        : false,
                tipologia: data?.row?.tipologia || "2",
            });
        }
    }, [data]);

    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setOtherData({ ...otherData, [name]: checked });
    };

    const handleInputChange = (e: any) => {
        const { name, value } = e.target;
        setOtherData((prevState: any) => ({
            ...prevState,
            [name]: value,
        }));
    };

    const handleIsDefaultChange = (index: any) => {
        currentValues.ibanList = currentValues?.ibanList.map(
            (field: any, i: any) => ({
                ...field,
                isDefault: i === index ? 1 : 0,
            })
        );
        setValue("ibanList", currentValues.ibanList);
    };

    const getMenuItems = () => {
        if (user?.isNetlexpdaulofUser && user?.isNetlexfeUser) {
            return [
                <MenuItem key="2" value="2">
                    {t("Preavviso di Parcella")}
                </MenuItem>,
                <MenuItem key="6" value="6">
                    {t("Fattura Elettronica")}
                </MenuItem>,
                <MenuItem key="10" value="10">
                    {t("Nota di Debito Elettronica")}
                </MenuItem>,
                <MenuItem key="7" value="7">
                    {t("Nota di Credito Elettronica")}
                </MenuItem>,
            ];
        } else {
            return [
                <MenuItem key="2" value="2">
                    {t("Preavviso di Parcella")}
                </MenuItem>,
                <MenuItem key="6" value="6">
                    {t("Fattura Elettronica")}
                </MenuItem>,
                <MenuItem key="10" value="10">
                    {t("Nota di Debito Elettronica")}
                </MenuItem>,
                <MenuItem key="7" value="7">
                    {t("Nota di Credito Elettronica")}
                </MenuItem>,
                <MenuItem key="0" value="0">
                    {t("Fattura")}
                </MenuItem>,
                <MenuItem key="1" value="1">
                    {t("Nota di Credito")}
                </MenuItem>,
                <MenuItem key="5" value="5">
                    {t("Fattura d'Acquisto")}
                </MenuItem>,
                // <MenuItem key="9" value="9">{t("Fattura d'Acconto")}</MenuItem>,
            ];
        }
    };

    return (
        <Box>
            {(fields || []).map((input: any, index: any) => (
                <Box key={input.id}>
                    <FormInput
                        sx={{ width: "50%", margin: "8px" }}
                        control={control}
                        name={`ibanList[${index}].desc`}
                        label={t("Nome")}
                        type="text"
                        variant="outlined"
                        setValue={setValue}
                    />
                    <FormInput
                        sx={{ width: "50%", margin: "8px" }}
                        control={control}
                        name={`ibanList[${index}].iban`}
                        label={t("IBAN*")}
                        type="text"
                        variant="outlined"
                        setValue={setValue}
                        error={errors[`iban_${input.id}`] ? true : false}
                        helperText={
                            errors[`iban_${input.id}`] &&
                            errors[`iban_${input.id}`].message
                        }
                    />
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <FormInput
                            sx={{ width: "50%", margin: "8px" }}
                            control={control}
                            name={`ibanList[${index}].abi`}
                            label={t("ABI")}
                            type="text"
                            variant="outlined"
                            setValue={setValue}
                        />
                        <FormGroup sx={{ mt: 3, ml: 2 }}>
                            <Controller
                                key={index}
                                name={`ibanList[${index}].isDefault`}
                                control={control}
                                render={({ input }: any) => (
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                {...input}
                                                checked={
                                                    fields[index]?.isDefault ==
                                                    "1"
                                                        ? true
                                                        : false
                                                }
                                                onChange={() =>
                                                    handleIsDefaultChange(index)
                                                }
                                            />
                                        }
                                        label={t("Default")}
                                    />
                                )}
                            />
                        </FormGroup>
                        <Button
                            sx={{ mt: 3 }}
                            onClick={() => removeInput(index)}
                            disabled={fields.length == 1}
                        >
                            <FontAwesomeIcon
                                style={{
                                    color: "#008FD6",
                                }}
                                icon={faTrashCan}
                            />
                        </Button>
                    </Box>
                    <FormInput
                        sx={{ width: "50%", margin: "8px" }}
                        control={control}
                        name={`ibanList[${index}].cab`}
                        label={t("CAB")}
                        type="text"
                        variant="outlined"
                        setValue={setValue}
                    />
                    <FormInput
                        sx={{ width: "50%", margin: "8px" }}
                        control={control}
                        name={`ibanList[${index}].bic`}
                        label={t("BIC")}
                        type="text"
                        variant="outlined"
                        setValue={setValue}
                    />
                </Box>
            ))}
            <Box>
                <Button
                    onClick={addInput}
                    sx={{ mt: 1 }}
                    disabled={errors?.ibanList?.length > 0}
                >
                    <FontAwesomeIcon
                        style={{
                            color: "#008FD6",
                            paddingRight: "5px",
                        }}
                        icon={faCirclePlus}
                    />
                    {t("Aggiungi")}
                </Button>
                <Divider variant="middle" sx={{ m: 3 }} />
            </Box>
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name={"codice_destinatario_af"}
                label={t("Codice destinatario")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors[`codice_destinatario_af`] ? true : false}
                helperText={
                    errors[`codice_destinatario_af`] &&
                    errors[`codice_destinatario_af`].message
                }
            />
            <Tooltip
                title={t("Codice destinatario utilizzato per l'autofattura")}
                sx={{ mt: 4.5 }}
            >
                <IconButton>
                    <InfoIcon />
                </IconButton>
            </Tooltip>
            <FormControl sx={{ width: "50%", margin: "8px" }}>
                <InputLabel id="regime_fiscale_label">
                    {t("Regime Fiscale")}
                </InputLabel>
                <Select
                    labelId="regime_fiscale_label"
                    id="regime_fiscale"
                    name="regime_fiscale"
                    label={t("Regime Fiscale")}
                    value={otherData?.regime_fiscale}
                    onChange={handleInputChange}
                >
                    {data?.regimeFiscale?.map((item: any) => (
                        <MenuItem key={item?.id} value={item?.id}>
                            {item?.nome}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl sx={{ width: "50%", margin: "8px" }}>
                <InputLabel id="tipo_cassa_label">
                    {t("Tipo cassa 1")}
                </InputLabel>
                <Select
                    labelId="tipo_cassa_label"
                    id="tipo_cassa"
                    name="tipo_cassa"
                    label={t("Tipo cassa 1")}
                    value={otherData?.tipo_cassa}
                    onChange={handleInputChange}
                >
                    {data?.tipoCassa?.map((item: any) => (
                        <MenuItem key={item?.id} value={item?.id}>
                            {item?.descrizione}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl
                sx={{ width: "50%", margin: "8px" }}
                variant="outlined"
            >
                <InputLabel id="perc_cassa_label">
                    {t("Percentuale cassa 1")}
                </InputLabel>
                <OutlinedInput
                    id="perc_cassa"
                    name="perc_cassa"
                    label={t("Percentuale cassa 1")}
                    endAdornment={
                        <InputAdornment position="end">%</InputAdornment>
                    }
                    aria-describedby="perc_cassa_helper_text"
                    inputProps={{
                        "aria-label": "percentuale",
                    }}
                    {...register("perc_cassa")}
                    error={errors[`perc_cassa`] ? true : false}
                />
            </FormControl>
            <FormGroup sx={{ ml: 1 }}>
                <FormControlLabel
                    control={
                        <Checkbox
                            name="addCassa"
                            onChange={handleCheckboxChange}
                            checked={otherData?.addCassa}
                        />
                    }
                    label={t("Cassa 2")}
                />
            </FormGroup>
            {otherData?.addCassa && (
                <>
                    <FormControl sx={{ width: "50%", margin: "8px" }}>
                        <InputLabel id="tipo_cassa_2_label">
                            {t("Tipo cassa 2")}
                        </InputLabel>
                        <Select
                            labelId="tipo_cassa_2_label"
                            id="tipo_cassa_2"
                            name="tipo_cassa_2"
                            label={t("Tipo cassa 2")}
                            value={otherData?.tipo_cassa_2}
                            onChange={handleInputChange}
                        >
                            {data?.tipoCassa?.map((item: any) => (
                                <MenuItem key={item?.id} value={item?.id}>
                                    {item?.descrizione}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <FormControl
                        sx={{ width: "50%", margin: "8px" }}
                        variant="outlined"
                    >
                        <InputLabel id="perc_cassa_2_label">
                            {t("Percentuale cassa 2")}
                        </InputLabel>
                        <OutlinedInput
                            id="perc_cassa_2"
                            name="perc_cassa_2"
                            label={t("Percentuale cassa 2")}
                            endAdornment={
                                <InputAdornment position="end">
                                    %
                                </InputAdornment>
                            }
                            aria-describedby="perc_cassa_2_helper_text"
                            inputProps={{
                                "aria-label": "percentuale",
                            }}
                            {...register("perc_cassa_2")}
                            error={errors[`perc_cassa_2`] ? true : false}
                        />
                    </FormControl>
                </>
            )}
            <FormGroup sx={{ ml: 1 }}>
                <FormControlLabel
                    control={
                        <Checkbox
                            onChange={handleCheckboxChange}
                            checked={otherData?.nascondi_codice_fiscale}
                            name="nascondi_codice_fiscale"
                        />
                    }
                    label={t("Nascondi Codice Fiscale in stampa")}
                />
                <FormControlLabel
                    control={
                        <Checkbox
                            onChange={handleCheckboxChange}
                            checked={otherData?.nascondi_codice_fiscale_xml}
                            name="nascondi_codice_fiscale_xml"
                        />
                    }
                    label={t("Nascondi Codice Fiscale in XML")}
                />
                <FormControlLabel
                    control={
                        <Checkbox
                            onChange={handleCheckboxChange}
                            checked={otherData?.nascondi_dati_pagamento_xml}
                            name="nascondi_dati_pagamento_xml"
                        />
                    }
                    label={t("Nascondi Dati Pagamento in XML")}
                />
            </FormGroup>
            <FormControl sx={{ width: "50%", margin: "8px" }}>
                <InputLabel id="tipologia">{t("Tipologia")}</InputLabel>
                <Select
                    labelId="tipologia"
                    id="tipologia"
                    name="tipologia"
                    label={t("Tipologia")}
                    value={otherData?.tipologia}
                    onChange={handleInputChange}
                >
                    {getMenuItems()}
                </Select>
            </FormControl>
            <Tooltip
                title={t(
                    "Personalizza la tipologia predifinita alla creazione di un nuovo documento"
                )}
                sx={{ mt: 4.5 }}
            >
                <IconButton>
                    <InfoIcon />
                </IconButton>
            </Tooltip>
        </Box>
    );
};

export default OthersData;
