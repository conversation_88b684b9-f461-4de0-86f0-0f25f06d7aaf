import { useEffect, useState } from "react";
import { Grid } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import { useGetUserList } from "../hooks/useOfficeSettings";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
interface UserListProps {
    otherData: any;
    setOtherData: any;
    ID: any;
}

const defaultParams: any = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "asc",
    isOnClear: false,
    id: 0,
};

const UserList = ({ otherData, setOtherData, ID }: UserListProps) => {
    const [query, setQuery] = useState<any>({
        ...defaultParams,
        id: ID,
    });

    const { t } = useTranslation();

    const { list, isActive } = useGetUserList(query);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({ ...query, page: model.page, pageSize: model.pageSize });
    };

    useEffect(() => {
        if (isActive) {
            setOtherData({ ...otherData, isActive });
        }
    }, [query, list]);

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="userList"
                columns={list?.columns}
                data={list?.rows}
                totalRows={list?.totalRows || 10}
                page={query.page}
                pageSize={query.pageSize}
                onPageChangeCallback={onPageChangeCallback}
                onClickKey="id"
                query={query}
                setQuery={setQuery}
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle title={t("UTENTI")} showBackButton={false} />
                <Grid container>
                    <Grid item md={12} sx={{ mt: 3, ml: 3 }}>
                        {t(
                            "Cliccare sulla riga per aggiungere/rimuovere l'utente dallo studio associato."
                        )}
                    </Grid>
                </Grid>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
};
export default UserList;
