import { useEffect } from "react";
import { Box, Typography, CardContent, Card } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

import FormInput from "../../../../custom-components/FormInput";

interface AssociatedFirmProps {
    errors?: any;
    control?: any;
    setValue?: any;
    data: any;
    isSendPin: any
    ID: any
}

const AssociatedFirmData = ({ errors, setValue, control, data, isSendPin, ID }: AssociatedFirmProps) => {
    const { t } = useTranslation();

    useEffect(() => {
        if (data) {
            setValue("nome", data?.row?.nome);
            setValue("indirizzo", data?.row?.indirizzo);
            setValue("civico", data?.row?.civico);
            setValue("mobile", data?.row?.mobile);
            setValue("cap", data?.row?.cap);
            setValue("citta", data?.row?.citta);
            setValue("provincia", data?.row?.provincia);
            setValue("partitaiva", data?.row?.partita_iva);
            setValue("codicefiscale", data?.row?.codice_fiscale);
            setValue("Email", data?.row?.email);
        }
    }, [data]);

    return (
        <Box sx={{}}>
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="nome"
                label={t("Nome*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.nome ? true : false}
                helperText={errors.nome && errors.nome.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="indirizzo"
                label={t("Indirizzo*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.indirizzo ? true : false}
                helperText={errors.indirizzo && errors.indirizzo.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="civico"
                label={t("Civico*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.civico ? true : false}
                helperText={errors.civico && errors.civico.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="mobile"
                label={t("Cellulare*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.mobile ? true : false}
                helperText={errors.mobile && errors.mobile.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="cap"
                label={t("CAP*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.cap ? true : false}
                helperText={errors.cap && errors.cap.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="citta"
                label={t("Città*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.citta ? true : false}
                helperText={errors.citta && errors.citta.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="provincia"
                label={t("Provincia*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.provincia ? true : false}
                helperText={errors.provincia && errors.provincia.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="partitaiva"
                label={t("Partita IVA*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.partitaiva ? true : false}
                helperText={errors.partitaiva && errors.partitaiva.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="codicefiscale"
                label={t("Codice Fiscale*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.codicefiscale ? true : false}
                helperText={errors.codicefiscale && errors.codicefiscale.message}
            />
            <FormInput
                sx={{ width: "50%", margin: "8px" }}
                control={control}
                name="Email"
                label={t("Email*")}
                type="text"
                variant="outlined"
                setValue={setValue}
                error={errors.Email ? true : false}
                helperText={errors.Email && errors.Email.message}
            />
            {isSendPin &&
                <FormInput
                    sx={{ width: "50%", margin: "8px" }}
                    control={control}
                    name="pinAgyo"
                    label={t("PIN*")}
                    type="text"
                    variant="outlined"
                    setValue={setValue}
                    error={errors.Email ? true : false}
                    helperText={errors.Email && errors.Email.message}
                />
            }
            {parseInt(ID as any) !== 1 && <Card sx={{ maxWidth: "100%", margin: "auto", mt: 4 }}>
                <CardContent>
                    <Typography variant="h5" gutterBottom>
                        <strong>   {t("Lo studio associato non è configurato per la fatturazione elettronica")}</strong>
                    </Typography>
                    <Typography variant="body1">
                        {t("1. Compilare i dati dello studio associato e premere su ")}
                        <strong>{t("Richiedi PIN")}</strong>
                        {t(" per ricevere un codice tramite SMS al numero di telefono indicato.")}
                    </Typography>
                    <Typography variant="body1" >
                        {t("2. Inserire il codice ricevuto nel campo ")} "PIN" {t(" e premere su ")}
                        <strong>{t("Conferma PIN")}</strong>.
                    </Typography>
                    <Typography variant="body1"  >
                        {t("3. In alcuni casi, potrebbe essere necessario un ulteriore passaggio di conferma tramite email. In tal caso")}
                    </Typography>
                    <Typography variant="body1" sx={{ pl: 2 }} >
                        {t("1. Seguire le istruzioni ricevute all'indirizzo email precedentemente inserito.")}
                    </Typography>
                    <Typography variant="body1" sx={{ pl: 2 }} >
                        {t("2. Cliccare nuovamente su ")}
                        <strong>{t("Conferma PIN")}</strong>.
                    </Typography>
                </CardContent>
            </Card>
            }
        </Box>
    );
};

export default AssociatedFirmData;
