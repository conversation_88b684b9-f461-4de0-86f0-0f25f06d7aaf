import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import { StatusBadge } from "@vapor/react-custom";

export const qualificaUtenteField = (row: any) => {
    return row["qualificautente"] === "1" || row["qualificautente"] === "2"
        ? "Avvocato"
        : "Segretaria";
};

export const idStudioDefault = (row: any) => {
    return row["id_studio_default"] === "1" ? (
        <>
            <FiberManualRecordIcon style={{ color: "green" }} />
        </>
    ) : (
        ""
    );
};

export const activeListUtentiField = (
    row: any,
    handleChange: any,
    isActive: any
) => {
    const activeState = isActive
        .slice(0, 10)
        .find((item: any) => item.id === row.id);
    const isChecked = activeState ? activeState.active : false;

    return (
        <input
            type="checkbox"
            checked={isChecked}
            onChange={(e) => handleChange && handleChange(e, row)}
        />
    );
};

export const colorProfitsDistribuitionField = (row: any) => {
    return (
        <>
            <StatusBadge
                bgColor={row.colore}
                style={{ width: "-webkit-fill-available" }}
            />
        </>
    );
};

export const handleRequestPinMessage = (
    t: any,
    setPinMessage: any,
    setIsSendPin: any,
    data: any
) => {
    let message = t("Errore: ");

    switch (data.code) {
        case "SMS_ALREADY_SENT":
            const secondsLeft = data.message.match(/\d+/g).map(Number);
            message += `${t(
                "Un SMS è stato già inviato al numero di telefono indicato. Attendere "
            )}${secondsLeft} ${t(
                "Secondi prima di inviare una nuova richiesta."
            )}`;
            setIsSendPin(true);
            break;
        case "SMS_SENT":
            message = t("SMS inviato al numero di telefono indicato.");
            setIsSendPin(true);
            break;
        case "WRONG_PIN":
            message += t("Pin errato.");
            setIsSendPin(false);
            break;
        case "COMPANY_OK":
        case "AGYO_KEY_CREATED":
            message = t("Configurazione completata!");
            break;
        case "EMAIL_SENT":
            message = `${t(
                "È stata inviata una mail di conferma ai seguenti indirizzi email"
            )}: ${data.emails}. ${t(
                "Seguire le istruzioni e confermare nuovamente il PIN."
            )}`;
            setIsSendPin(true);
            break;
        case "AGYO_COMPANY_ALREADY_EXISTS":
            message += t(
                "Company già presente su Agyo con CF e/o P.IVA differenti. Si prega di verificare i dati inseriti."
            );
            setIsSendPin(false);
            break;
        default:
            message = t(
                "Si è verificato un errore. Riprovare più tardi, se il problema persiste contattare l'assistenza tecnica."
            );
            setIsSendPin(false);
            break;
    }
    setPinMessage(message);
};

export const handleConfirmPinMessage = (
    t: any,
    setPinMessage: React.Dispatch<React.SetStateAction<string>>,
    data: {
        code: string;
        message: any;
        emails: string[] | null;
        recipients: any;
    }
) => {
    let message = "";

    if (data.code === "WRONG_PIN") {
        message = t("Pin errato.");
    } else if (data.code === "EMAIL_SENT") {
        const recipients = data.message
            ? data.message?.recipients.map((item: string) => item).join(", ")
            : "";
        message = `${t(
            "È stata inviata una mail di conferma ai seguenti indirizzi email"
        )}: ${recipients}. ${t(
            "Seguire le istruzioni e confermare nuovamente il PIN."
        )}`;
    } else {
        message = t(
            "Si è verificato un errore. Riprovare più tardi, se il problema persiste contattare l'assistenza tecnica."
        );
    }

    setPinMessage(message);
};
