import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";

import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { useGetOfficeSettings } from "./hooks/useOfficeSettings";

import AddIcon from "@mui/icons-material/Add";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
const defaultParams: any = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "asc",
    isOnClear: false,
};

const OfficeSettings = () => {
    const [query, setQuery] = useState<any>({
        ...defaultParams,
    });

    const { t } = useTranslation();
    const navigate = useNavigate();

    const { list, fetchData } = useGetOfficeSettings(query);

    useEffect(() => {
        fetchData();
    }, [query]);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onRowClick = (row: any) => {
        navigate(`/officesettings/form?id=${row}`);
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="officeSettings"
                columns={list?.columns}
                data={list?.rows}
                totalRows={list?.totalRows || 10}
                page={query.page}
                pageSize={query.pageSize}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={onRowClick}
                onClickKey="id"
                setQuery={setQuery}
                query={query}
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle
                    title={t("ELENCO STUDI ASSOCIATI/SOCIETÀ")}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: `${t("Nuovo Studio")}`,
                            variant: "contained",
                            startIcon: <AddIcon />,
                            onclick: () => {
                                navigate("/officesettings/form?id=0");
                            },
                        },
                    ]}
                />
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
};

export default OfficeSettings;
