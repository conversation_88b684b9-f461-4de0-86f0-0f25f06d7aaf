import { useEffect, useState } from "react";
import { <PERSON>, Grid, Button } from "@vapor/react-material";
import { Tabs, Tab, Typography } from "@vapor/react-extended";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import { useForm, useFieldArray, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useGetOneRow } from "./hooks/useOfficeSettings";

import PageTitle from "../../../custom-components/PageTitle";
import VaporPage from "@vapor/react-custom/VaporPage";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import AssociatedFirmGuide from "./components/AssociatedFirmGuide";
import UserList from "./components/UserList";
import AssociatedFirmDataInput from "./components/AssociatedFirmDataInputSection";
import OthersData from "./components/Others";
import ToastNotification from "../../../custom-components/ToastNotification";
import { handleConfirmPinMessage, handleRequestPinMessage } from "./helper";
import TabShowErrorHandler from "./helper/tabShowErrorHandler";

import ErrorIcon from "@mui/icons-material/Error";

const VALUE = {
    ZERO: "0",
};

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

const CreateOfficeSettings = () => {
    const [tabValue, setTabValue] = useState(0);
    const [otherData, setOtherData] = useState({
        regime_fiscale: "1",
        tipo_cassa: "1",
        tipo_cassa_2: "1",
        addCassa: false,
        nascondi_codice_fiscale: false,
        nascondi_dati_pagamento_xml: false,
        nascondi_codice_fiscale_xml: false,
        isActive: [],
        tipologia: "",
    });
    const [isLoading, setIsLoading] = useState(false);
    const [showSendPinNotification, setShowSendPinNotification] =
        useState(false);
    const [showConfirmPinNotification, setShowConfirmPinNotification] =
        useState(false);
    const [isSendPin, setIsSendPin] = useState(false);
    const [open, setOpen] = useState(false);
    const [pinMessage, setPinMessage] = useState("");

    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const id = searchParams.get("id");

    const ID = id;

    const { data, iban } = useGetOneRow(ID);
    const updateOfficeSettings = usePostCustom("officesettings/save");
    const sendPinRequest = usePostCustom("fatture/sendpin?noTemplateVars=true");
    const confirmPinRequest = usePostCustom(
        "fatture/confirmpin?noTemplateVars=true"
    );
    const deleteRequest = useGetCustom("officesettings/delete-office");

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setTabValue(newValue);
    };

    const IbanValidation = yup.object().shape({
        iban: yup.string().required("IBAN is required"),
    });

    const schema = yup.object().shape({
        uniqueid: yup.string().optional(),
        office: yup.number().optional(),
        officeId: yup.number().optional(),
        nome: yup.string().required("Nome is required"),
        indirizzo: yup.string().required("Indirizzo is required"),
        civico: yup.string().required("Civico is required"),
        mobile: yup.string().required("Cellulare is required"),
        cap: yup
            .string()
            .required("CAP is required")
            .length(5, "CAP deve avere 5 caratteri"),
        citta: yup.string().required("Città is required"),
        provincia: yup
            .string()
            .required("Provincia is required")
            .length(2, "Provincia deve avere 2 caratteri"),
        partitaiva: yup.string().required("Partita IVA is required"),
        codicefiscale: yup.string().required("Codice Fiscale is required"),
        Email: yup
            .string()
            .email("Invalid email address")
            .required("Email is required"),
        codice_destinatario_af: yup.string().optional(),
        perc_cassa_2: yup.string().nullable().optional(),
        tipo_cassa: yup.string().nullable().optional(),
        tipo_cassa_2: yup.string().nullable().optional(),
        perc_cassa: yup.string().optional(),
        tipologia: yup.string().optional(),
        pinAgyo: yup
            .string()
            .optional()
            .when("$isSendPin", {
                is: (isSendPin: any) => isSendPin === true,
                then: () =>
                    yup
                        .string()
                        .required("PIN is required")
                        .length(5, "PIN non valido"),
                otherwise: (s: any) => s,
            }),
        ibanList: yup
            .array()
            .optional()
            .when("$ID", {
                is: (ID: any) => ID !== "0",
                then: () =>
                    yup.array().of(IbanValidation).required("IBAN is required"),
                otherwise: (s: any) => s,
            }),
    });

    const {
        register,
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
        getValues,
        trigger,
    } = useForm({
        resolver: yupResolver(schema),
        defaultValues: {
            uniqueid: "",
            office: 1,
            officeId: 0,
            nome: "",
            indirizzo: "",
            civico: "",
            mobile: "",
            cap: "",
            citta: "",
            provincia: "",
            partitaiva: "",
            codicefiscale: "",
            Email: "",
            codice_destinatario_af: "",
            perc_cassa_2: "",
            tipo_cassa_2: "",
            perc_cassa: "",
            pinAgyo: "",
            ibanList: [],
        },
        context: { ID, isSendPin },
    });

    const {
        HasTabErrors,
        showTabErrorNotification,
        setShowTabErrorNotificaion,
        tabTextError,
    } = TabShowErrorHandler(errors, t);

    const { fields, append, remove } = useFieldArray({
        control,
        name: "ibanList",
    });

    useEffect(() => {
        if (ID !== VALUE?.ZERO && iban) {
            setValue(
                "ibanList",
                ID === VALUE?.ZERO
                    ? []
                    : iban?.length > 0
                    ? iban
                    : [
                          {
                              desc: "",
                              iban: "",
                              abi: "",
                              cab: "",
                              bic: "",
                              isDefault: true,
                          },
                      ]
            );
        }
    }, [iban, setValue, data]);

    const CustomTabLabel = ({
        label,
        hasError,
    }: {
        label: string;
        hasError: boolean;
    }) => (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
            }}
        >
            <span>{label}</span>
            {hasError && <ErrorIcon color="error" style={{ marginLeft: 8 }} />}
        </div>
    );
    const onSubmit = async (value: any) => {
        setIsLoading(true);

        const createParams = {
            uniqueid: "",
            office: 1,
            officeId: 0,
            codice_destinatario_af: "",
            regime_fiscale: 1,
            tipo_cassa: 1,
            perc_cassa: "",
            tipo_cassa_2: 1,
            perc_cassa_2: "",
            ibanList: [],
            nome: value?.nome,
            indirizzo: value?.indirizzo,
            civico: value?.civico,
            mobile: value?.mobile,
            cap: value?.cap,
            citta: value?.citta,
            provincia: value?.provincia,
            partitaiva: value?.partitaiva,
            codicefiscale: value?.codicefiscale,
            Email: value?.Email,
        };

        const ibanList = value?.ibanList.map((item: any) => {
            return {
                desc: item?.desc,
                iban: item?.iban,
                abi: item?.abi,
                cab: item?.cab,
                bic: item?.bic,
                isDefault: item?.isDefault ? 1 : 0,
            };
        });

        const editParams: any = {
            uniqueid: data?.row?.uniqueid,
            officeId: ID,
            office: 1,
            nome: value?.nome,
            indirizzo: value?.indirizzo,
            civico: value?.civico,
            mobile: value?.mobile,
            cap: value?.cap,
            citta: value?.citta,
            provincia: value?.provincia,
            partitaiva: value?.partitaiva,
            codicefiscale: value?.codicefiscale,
            Email: value?.Email,
            codice_destinatario_af: value?.codice_destinatario_af,
            regime_fiscale: otherData?.regime_fiscale,
            tipo_cassa: otherData?.tipo_cassa,
            perc_cassa: value?.perc_cassa,
            tipo_cassa_2: otherData?.addCassa ? otherData?.tipo_cassa_2 : null,
            perc_cassa_2: otherData?.addCassa ? value?.perc_cassa_2 : null,
            tipologia: otherData?.tipologia,
            ibanList:
                (ID as any) === VALUE?.ZERO ? [] : JSON.stringify(ibanList),
            ...Object.fromEntries(
                otherData?.isActive.map((item: any) => [
                    `user_${item?.id}`,
                    item?.active === true ? "1" : "0",
                ])
            ),
        };

        if (otherData?.addCassa) {
            editParams.addCassa = otherData?.addCassa && "on";
        }

        if (otherData?.nascondi_codice_fiscale) {
            editParams.nascondi_codice_fiscale =
                otherData?.nascondi_codice_fiscale && "on";
        }

        if (otherData?.nascondi_codice_fiscale_xml) {
            editParams.nascondi_codice_fiscale_xml =
                otherData?.nascondi_codice_fiscale_xml && "on";
        }

        if (otherData?.nascondi_dati_pagamento_xml) {
            editParams.nascondi_dati_pagamento_xml =
                otherData?.nascondi_dati_pagamento_xml && "on";
        }

        if (otherData?.nascondi_codice_fiscale) {
            editParams.nascondi_codice_fiscale =
                otherData?.nascondi_codice_fiscale && "on";
        }

        if (otherData?.nascondi_codice_fiscale_xml) {
            editParams.nascondi_codice_fiscale_xml =
                otherData?.nascondi_codice_fiscale_xml && "on";
        }

        if (otherData?.nascondi_dati_pagamento_xml) {
            editParams.nascondi_dati_pagamento_xml =
                otherData?.nascondi_dati_pagamento_xml && "on";
        }

        if (otherData?.nascondi_codice_fiscale) {
            editParams.nascondi_codice_fiscale =
                otherData?.nascondi_codice_fiscale && "on";
        }

        if (otherData?.nascondi_codice_fiscale_xml) {
            editParams.nascondi_codice_fiscale_xml =
                otherData?.nascondi_codice_fiscale_xml && "on";
        }

        if (otherData?.nascondi_dati_pagamento_xml) {
            editParams.nascondi_dati_pagamento_xml =
                otherData?.nascondi_dati_pagamento_xml && "on";
        }

        const params = ID === VALUE?.ZERO ? createParams : editParams;

        try {
            await updateOfficeSettings.doFetch(true, params);
        } catch (err: any) {
            console.error(err);
        } finally {
            navigate("/officesettings");
        }

        setIsLoading(false);
    };

    const sendPinAction = async () => {
        const currentValues = getValues();
        const params = {
            Email: currentValues?.Email,
            cap: currentValues?.cap,
            citta: currentValues?.citta,
            civico: currentValues?.civico,
            codicefiscale: currentValues?.codicefiscale,
            indirizzo: currentValues?.indirizzo,
            mobile: currentValues?.mobile,
            nome: currentValues?.nome,
            partitaiva: currentValues?.partitaiva,
            provincia: currentValues?.provincia,
        };

        try {
            const { data }: any = await sendPinRequest.doFetch(true, params);
            handleRequestPinMessage(t, setPinMessage, setIsSendPin, data);
            setShowSendPinNotification(true);
        } catch (err: any) {
            console.log(err);
        }
    };

    const confirmPinAction = async () => {
        const isValid = await trigger();

        if (isValid) {
            const currentValues = getValues();
            const params = {
                Email: currentValues?.Email,
                cap: currentValues?.cap,
                citta: currentValues?.citta,
                civico: currentValues?.civico,
                codicefiscale: currentValues?.codicefiscale,
                indirizzo: currentValues?.indirizzo,
                mobile: currentValues?.mobile,
                nome: currentValues?.nome,
                partitaiva: currentValues?.partitaiva,
                provincia: currentValues?.provincia,
                pinAgyo: currentValues?.pinAgyo,
            };

            try {
                const { data }: any = await confirmPinRequest.doFetch(
                    true,
                    params
                );
                handleConfirmPinMessage(t, setPinMessage, data);
                setShowConfirmPinNotification(true);
            } catch (err: any) {
                console.log(err);
            }
        } else {
            console.log("Form validation failed");
        }
    };

    const handleDelete = () => {
        setOpen(true);
    };
    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        await deleteRequest.doFetch(true, { id: ID });
        setOpen(false);
        navigate("/officesettings");
    };

    useEffect(() => {
        if (HasTabErrors(0)) setTabValue(1);
    }, [errors]);

    return (
        <>
            <ToastNotification
                showNotification={showSendPinNotification}
                setShowNotification={setShowSendPinNotification}
                severity="info"
                text={pinMessage}
            />
            <ToastNotification
                showNotification={showConfirmPinNotification}
                setShowNotification={setShowConfirmPinNotification}
                severity="info"
                text={pinMessage}
            />
            <ToastNotification
                showNotification={showTabErrorNotification}
                setShowNotification={setShowTabErrorNotificaion}
                severity="error"
                text={tabTextError}
            />
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={t("No")}
                agree={t("Si, Elimina")}
                confirmText={t(
                    "Confermare l'eliminazione del Studio Associato"
                )}
                title={t("Elimina azione")}
            />
            <VaporPage>
                <PageTitle
                    title={t("CONFIGURAZIONE STUDIO ASSOCIATO")}
                    pathToPrevPage={"/officesettings"}
                />
                <VaporPage.Section>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <Grid container spacing={3}>
                            <Grid item md={7} lg={6.5}>
                                <Box
                                    sx={{
                                        borderBottom: 1,
                                        borderColor: "divider",
                                    }}
                                >
                                    <Tabs
                                        value={tabValue}
                                        onChange={handleChange}
                                        size="extraSmall"
                                        variant="standard"
                                    >
                                        <Tab
                                            label={t("Dati Studio Associato")}
                                            {...a11yProps(0)}
                                        />
                                        {ID !== VALUE?.ZERO && (
                                            <Tab
                                                label={
                                                    <CustomTabLabel
                                                        label="Altro"
                                                        hasError={HasTabErrors(
                                                            0
                                                        )}
                                                    />
                                                }
                                                {...a11yProps(1)}
                                            />
                                        )}
                                        {/* {ID !== VALUE?.ZERO && <Tab label={t("Altro")} {...a11yProps(1)} />} */}
                                    </Tabs>
                                    <CustomTabPanel value={tabValue} index={0}>
                                        <AssociatedFirmDataInput
                                            control={control}
                                            setValue={setValue}
                                            errors={errors}
                                            data={data}
                                            isSendPin={isSendPin}
                                            ID={ID}
                                        />
                                    </CustomTabPanel>
                                    <CustomTabPanel value={tabValue} index={1}>
                                        <OthersData
                                            register={register}
                                            control={control}
                                            setValue={setValue}
                                            errors={errors}
                                            data={data}
                                            setOtherData={setOtherData}
                                            otherData={otherData}
                                            fields={fields}
                                            append={append}
                                            remove={remove}
                                            Controller={Controller}
                                            reset={reset}
                                            getValues={getValues}
                                        />
                                    </CustomTabPanel>
                                </Box>
                            </Grid>
                            <Grid item md={5} lg={5.5}>
                                {ID === VALUE?.ZERO ? (
                                    <AssociatedFirmGuide />
                                ) : (
                                    <UserList
                                        otherData={otherData}
                                        setOtherData={setOtherData}
                                        ID={ID}
                                    />
                                )}
                            </Grid>
                        </Grid>
                        <Box sx={{ mt: 4 }}>
                            <SpinnerButton
                                variant="contained"
                                isLoading={isLoading}
                                label="Conferma"
                                type="submit"
                            />
                            {parseInt(ID as any) > 2 && (
                                <Button
                                    type="button"
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    sx={{ ml: 3 }}
                                >
                                    {t("Elimina")}
                                </Button>
                            )}
                            {parseInt(ID as any) > 1 && (
                                <SpinnerButton
                                    variant="contained"
                                    isLoading={showSendPinNotification}
                                    label="Richiedi PIN"
                                    onClick={sendPinAction}
                                    sx={{ ml: 3 }}
                                />
                            )}
                            {isSendPin && (
                                <Button
                                    variant="contained"
                                    onClick={confirmPinAction}
                                    sx={{ ml: 3 }}
                                >
                                    {t("Conferma Pin")}
                                </Button>
                            )}
                        </Box>
                    </form>
                </VaporPage.Section>
            </VaporPage>
        </>
    );
};
export default CreateOfficeSettings;
