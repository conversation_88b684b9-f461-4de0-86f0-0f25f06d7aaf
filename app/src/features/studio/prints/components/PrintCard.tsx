import {
    Card,
    CardContent,
    CardMedia,
    Typography,
    Grid,
    Box,
} from "@mui/material";
import { <PERSON>, Button } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { useTranslation } from "@1f/react-sdk";

import Image from "../../../../custom-components/Image";

const PrintCard = ({ print }: any) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <Card>
            <CardMedia component="img" image={print.link} alt="" />
            <CardContent>
                <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        md={12}
                        sx={{ display: "flex", justifyContent: "center" }}
                    >
                        <Box sx={{ border: 1, borderColor: "#b5b5b5" }}>
                            <Image
                                path={print?.link}
                                alt={print?.title}
                                width={160}
                                height={200}
                            />
                        </Box>
                    </Grid>
                    <Grid item xs={12} md={12}>
                        <Link
                            to={`/prints/update/id=${print?.id}`}
                            color="primary"
                            style={{ textDecoration: "none", color: "#00B3E0" }}
                        >
                            <Typography
                                variant="displaySmall"
                                component="div"
                                color="primary.interactiveDefault"
                                gutterBottom
                            >
                                {print.filename}
                            </Typography>
                        </Link>
                    </Grid>
                    <Grid item xs={12} md={12}>
                        <Typography sx={{ fontSize: 12 }}>
                            {"Titolo"}: {print.title}
                        </Typography>
                    </Grid>
                    <Grid item xs={12} md={12}>
                        <Typography sx={{ fontSize: 12 }}>
                            {"Categoria"}:
                            <Chip
                                variant="outlined"
                                size="small"
                                key={print.category}
                                label={print.category}
                                sx={{ marginLeft: 1 }}
                            ></Chip>
                        </Typography>
                    </Grid>

                    {print.category === "Contrattualistica" && (
                        <Grid item xs={12} md={12}>
                            <Typography sx={{ fontSize: 12 }}>
                                <Button
                                    startIcon={<ArrowForwardIcon />}
                                    variant="contained"
                                    color="success"
                                    onClick={() =>
                                        navigate(
                                            `/legacy/digitalsignature/signaturepoints?print=1&id=${print.id}`
                                        )
                                    }
                                >
                                    {t("Punti firma")}
                                </Button>
                            </Typography>
                        </Grid>
                    )}
                </Grid>
            </CardContent>
        </Card>
    );
};

export default PrintCard;
