import * as React from "react";
import { Box, Typography } from "@vapor/react-material";
import { Tabs, Tab } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";

import PageTitle from "../../../../custom-components/PageTitle";
import InfoGuide from "./InfoGuide";
import TagsAvailable from "./TagsAvailable";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

const InfoGuideIndex = () => {
    const [value, setValue] = React.useState(0);

    const { t } = useTranslation();

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    return (
        <Box sx={{ width: "100%" }}>
            <PageTitle
                title={t("INFOGUIDA ALL'UTILIZZO DEI TEMPLATE DI STAMPA")}
                pathToPrevPage="/prints"
            />
            <Box sx={{ borderBottom: 1, borderColor: "divider", m: 3 }}>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label={t("Infoguida")} {...a11yProps(0)} />
                    <Tab label={t("Tag disponibili")} {...a11yProps(1)} />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
                <InfoGuide />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <TagsAvailable />
            </CustomTabPanel>
        </Box>
    );
};

export default InfoGuideIndex;
