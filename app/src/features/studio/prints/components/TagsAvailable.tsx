import * as React from "react";
import { Box, Typography, Grid } from "@vapor/react-material";
import { Tabs, Tab } from "@vapor/react-extended";
import { useUser } from "../../../../store/UserStore";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

const TagsAvailable = () => {
    const [value, setValue] = React.useState(0);
    const { user }: any = useUser();

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    return (
        <Grid container>
            <Grid item xs={12} md={2}>
                <Box
                    sx={{
                        flexGrow: 1,
                        display: "flex",
                        height: 500,
                        justifyContent: "center",
                    }}
                >
                    <Tabs
                        value={value}
                        onChange={handleChange}
                        size="extraSmall"
                        orientation="vertical"
                        variant="scrollable"
                    >
                        <Tab label="Calendario" {...a11yProps(0)} />
                        <Tab label="Pratica" {...a11yProps(1)} />
                        <Tab label="Fattura" {...a11yProps(2)} />
                        <Tab label="Lista udienze" {...a11yProps(3)} />
                        <Tab label="Recupero crediti" {...a11yProps(4)} />
                        <Tab label="Liquidazione PA" {...a11yProps(5)} />
                        <Tab label="Tag generali" {...a11yProps(6)} />
                        <Tab
                            label="Pratica Dati Amministrativi"
                            {...a11yProps(7)}
                        />
                        <Tab label="Pratica Dati Penali" {...a11yProps(8)} />
                        <Tab
                            label="Pratica Dati Conciliazione"
                            {...a11yProps(9)}
                        />
                        {user?.configs?.app
                            ?.customer_contrattualistica_bool && (
                            <Tab label="Contrattualistica" {...a11yProps(10)} />
                        )}
                    </Tabs>
                </Box>
            </Grid>
            <Grid item xs={12} md={10}>
                <CustomTabPanel value={value} index={0}>
                    <Grid container sx={{ fontSize: 13 }} spacing={1}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.ATTIVITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.ISTRUTTORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.ORA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.REFERENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.TIPO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_CAMPO_DINAMICO_X] = Campo
                            dinamico (X è da sostituire con l'id del campo
                            dinamico che si span)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [calendario.PRATICA_VALORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ATTIVITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ISTRUTTORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ORA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CODICE_ARCHIVIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.REFERENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.START]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.TIPO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CAMPO_DINAMICO_X] = Campo dinamico
                            (X è da sostituire con l'id del campo dinamico che
                            si span)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_VALORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.ATTIVITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.IMPORTANTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.ISTRUTTORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.ORA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.START]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.TIPO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_CAMPO_DINAMICO_X] = Campo dinamico
                            (X è da sostituire con l'id del campo dinamico che
                            si span)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [impegni.PRATICA_VALORE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ATTIVITA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ATTIVITA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AVVOCATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AVVOCATO_NOME_COGNOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CAMPO_DINAMICO_X] = Campo dinamico (X è da
                            sostituire con l'id del campo dinamico che si vuole
                            sspan className='badge badge-outline-secondary
                            m-1'mpare)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.NOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CODICE_ARCHIVIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CONTROPARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_PROSSIMA_UDIENZA_EVASA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_ULTIMA_UDIENZA_EVASA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ISTRUTTORE_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.GIUDICE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ORA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ORA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.PARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.PARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.VALORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.PROTOCOLLO_GENERALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.SITUAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.SITUAZIONE_CONTABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CENTRO_PROFITTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.SENTENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_SENTENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.NUMERO_SENTENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.SOGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.INTESTATARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.INTERESSI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.DIRITTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.ONORARI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.TOTALE_ODI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.ONERI_RIFLESSI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.PERC_ONERI_RIFLESSI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.PERC_SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.TOTALE_OS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.SPESE_ESENTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.RITENUTA_ACCONTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.PERC_RITENUTA_ACCONTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [rcdocumenti.TOTALE_DA_RECUPERARE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.CODICE_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.COGNOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.DATA_NASCITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.DENOMINAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.INDIRIZZO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.NOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.PARTITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.COMUNE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.DATA_ISCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.NUMERO_REA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.SOTTOSCRITTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.DELIBERATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.VERSATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.VALUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.NOME_PEC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetti.IBAN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.CODICE_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.COGNOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.DATA_NASCITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.DENOMINAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.INDIRIZZO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.NOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.PARTITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.COMUNE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.DATA_ISCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.NUMERO_REA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.SOTTOSCRITTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.DELIBERATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.VERSATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.VALUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.NOME_PEC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticontroparti.IBAN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.CODICE_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.COGNOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.DATA_NASCITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.DENOMINAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.INDIRIZZO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.NOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.PARTITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.COMUNE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.DATA_ISCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.NUMERO_REA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.SOTTOSCRITTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.DELIBERATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.VERSATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.VALUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.NOME_PEC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggetticlienti.IBAN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.CODICE_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.COGNOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.DATA_NASCITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.DENOMINAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.INDIRIZZO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.NOME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.PARTITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.COMUNE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.DATA_ISCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.NUMERO_REA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.SOTTOSCRITTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.DELIBERATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.VERSATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.VALUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.NOME_PEC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [soggettiavversari.IBAN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [documenti.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [documenti.DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [documenti.NOMEFILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.ANNO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DATA_SCADENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DESTINATARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DESTINATARIO_CF]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DESTINATARIO_PIVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DESTINATARIO_ALTRI_DATI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DATI_PAGAMENTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.DATI_STUDIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.EMITTENTE_EMAIL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.EMITTENTE_TELEFONO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.ESIGIBILITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_BOLLO_VIRTUALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_CASSA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_RITENUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_SPESE_ESCLUSE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_SPESE_ESENTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IMPORTO_SPESE_IMPONIBILI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.INTESTAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.MOTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PROGRESSIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PERCENTUALE_CASSA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PERCENTUALE_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PERCENTUALE_RITENUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PERCENTUALE_SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PROGRESSIVO_ALFANUMERICO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.SEZIONALE_PROGRESSIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.SEZIONALE_PROGRESSIVO_BIS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TIPO_DOCUMENTO_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE_IMPONIBILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE_NON_IMPONIBILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE_ONORARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.TOTALE_COMPETENZE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PRATICA_NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fatture.PRATICA_DESCRIZIONE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.ANNO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DATA_SCADENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DESTINATARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DESTINATARIO_CF]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DESTINATARIO_PIVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DESTINATARIO_ALTRI_DATI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DATI_PAGAMENTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.DATI_STUDIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.EMITTENTE_EMAIL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.EMITTENTE_TELEFONO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.ESIGIBILITA_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_BOLLO_VIRTUALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_CASSA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_RITENUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_SPESE_ESCLUSE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_SPESE_ESENTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IMPORTO_SPESE_IMPONIBILI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.INTESTAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.MOTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.NUMERO_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PROGRESSIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PERCENTUALE_CASSA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PERCENTUALE_IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PERCENTUALE_RITENUTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PERCENTUALE_SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PROGRESSIVO_ALFANUMERICO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.SEZIONALE_PROGRESSIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.SEZIONALE_PROGRESSIVO_BIS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TIPO_DOCUMENTO_FISCALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE_IMPONIBILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE_NON_IMPONIBILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE_ONORARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.TOTALE_COMPETENZE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PRATICA_NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [fattura.PRATICA_DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [movimenti.DESCRIZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [movimenti.IMPORTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [movimenti.IMPORTO_UNITARIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [movimenti.IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [movimenti.QUANTITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ATTIVITA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ATTIVITA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.AVVOCATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CAMPO_DINAMICO_X] = Campo dinamico (X è da
                            sostituire con l'id del campo dinamico che si vuole
                            stampare)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CITTA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CODICE_ARCHIVIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.CONTROPARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_PROSSIMA_UDIENZA_EVASA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_ULTIMA_UDIENZA_EVASA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ISTRUTTORE_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ORA_PROSSIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.ORA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.PARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.PARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.VALORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.NOTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DESCRIZIONE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={3}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ATTIVITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.AUTORITA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.CONTROPARTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.CONTROPARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.DATA_ULTIMA_UDIENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ISTRUTTORE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.ORA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PARTI_NO_COD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CODICE_PRATICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CODICE_ARCHIVIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.REFERENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.RESPONSABILE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.SEZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.START]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.TIPO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_CAMPO_DINAMICO_X] = Campo dinamico
                            (X è da sostituire con l'id del campo dinamico che
                            si vuole stampare)
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DATA_APERTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DATA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_DECRETO_INGIUNTIVO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_OGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_REATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGAPP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGCASS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGGIP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGGUP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGNR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGSIEP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGRIESAME]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGSIUS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_RGTRIB]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_STATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_TIPOLOGIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [udienze.PRATICA_VALORE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={4}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_SCADENZA_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_DOCUMENTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_SCADENZA_SOLLECITO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CAPITALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CAPITALE_RESIDUO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.RIF_FATTURA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_INIZIO_CALCOLO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_FINE_CALCOLO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_CREZIONE_CALCOLO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.TIPO_CALCOLO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.GIORNI_CALCOLATI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.IMPORTO_INTERESSI_APPLICATI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.IMPORTO_COSTI_AGGIUNTIVI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.IMPORTO_SOGLIA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.IMPORTO_PERCENTUALE_AGGIUNTIVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.TOTALE_CALCOLO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CITTA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.INTESTAZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DENOMINAZIONE_STUDIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DENOMINAZIONE_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.INDIRIZZO_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CODICEFISCALE_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.PARTITAIVA_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.PEC_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.COD_ESTERNA_CONTROPARTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.AVVOCATO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.GIORNI_SCADENZA_SOLLECITO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.TIPO_SOLLECITO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATA_SOLLECITO_PRECEDENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.DATI_BANCARI_STUDIO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CLIENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.INDIRIZZO_CLIENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.CITTA_CLIENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.COD_ESTERNA_CLIENTE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.ALTRI_COSTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [recuperocrediti.AGENTE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={5}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.CPA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.IVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.NETTO_PAGARE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.ORDINANZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.PRECETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.REGISTRAZIONE_SENTENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.RIMBORSO_CTU]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.RITENUTA_ACCONTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.SENTENZA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.SOGGETTO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.SPESE_ESENTI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.SPESE_GENERALI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.TOTALE_COMPENSI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [liquidazione.TOTALE_SPESE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={6}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={6} md={6}>
                            [generale.DATA_ODIERNA]
                        </Grid>
                        <Grid item lg={6} md={6}>
                            [generale.RIFERIMENTO_TEMPORALE_UTC]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={7}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ST_REC_STU]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ST_REC_CLI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_PRAT_ATT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_COS_SOS_CORR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_NOTIFICA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_RIC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_COST_GIUD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_RIC_RIS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IMP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ISTA_FISS]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ISTA_FISS_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ISTA_PREL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ISTA_PREL_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ORD_ISTR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ORD_ISTR_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ORD_CAU]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ORD_CAU_RG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DATA_DELLA_SENT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IST_SOSP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESEC_ORD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IMPUGNATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESEC_SENT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DATA_UD]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESITO_CAUT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESITO_SENT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_INV_DEDU]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_DED]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ATT_CIT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_MEM]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DAT_DEP_SENT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_NOT_PREC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_TRA_SENT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_TER_IMP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_TPO_SPES]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IMP_COND]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_COS_TAR]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_COS_CDS]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={8}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IMPUGNATA2]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_N_SENT_REP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DATA_TRASM]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_CONDANNA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DATA_PUB_SEN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_RIS_DAN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_TERM_IMP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_NOTIFICA2]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_COST_COM]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESITO]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_RISARC]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ST_REC_CLI2]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_FATT_AVV_A]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ALLE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_N_SINI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_AUT_INTER]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_RESP_CP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_T_MOTIVA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DEP_D_MOTIVA]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={9}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_FATT_AVV_A]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ALLE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_N_SINI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_AUT_INTER]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_RESP_CP]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IN_DATA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_VIA_STRADA_P]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DIREZIONE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_MORTALE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_KM]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DATA_INI]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DATA_FIN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_GG]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_CONSEGN]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_DELIMIT]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ENTE_PAGA]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESITO_CONCIL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_ESITO_PARERE]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_IMP_MASSIMAL]
                        </Grid>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            [pratica.DT_GIUD_NOTE]
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={10}>
                    <Grid container spacing={1} sx={{ fontSize: 13 }}>
                        <Grid item lg={8} md={8} sm={12} xs={12}>
                            [contrattualistica.CAMPO_DINAMICO_X] = Campo
                            dinamico (X è da sostituire con l'id del campo
                            dinamico che si vuole stampare)
                        </Grid>
                    </Grid>
                </CustomTabPanel>
            </Grid>
        </Grid>
    );
};
export default TagsAvailable;
