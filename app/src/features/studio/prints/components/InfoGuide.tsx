import {
    Typography,
    Card<PERSON>ontent,
    Card,
    CardActions,
    Stack,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { Link } from "react-router-dom";

import Image from "../../../../custom-components/Image";
import { useApi } from "../../../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../../../utilities/constants";

const InfoGuide = () => {
    const { t } = useTranslation();
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    return (
        <Card sx={{ minWidth: 275 }}>
            <CardContent>
                <Typography variant="h6" component="div">
                    {t(
                        "E' possibile utilizzare un template personalizzato per la stampa di: Calendario, Pratica, Fattura, Lista udienze."
                    )}
                </Typography>
                <Typography
                    sx={{ fontSize: 14 }}
                    color="text.secondary"
                    gutterBottom
                >
                    {t(
                        "Per ognuna delle categorie ci sono dei tag preimpostati che al momento della stampa verranno sostituiti con i dati specifici della sezione in cui ci si trova."
                    )}
                    <br />
                    {t("Questi sono i passi da compiere per le stampe:")}
                    <div>
                        <ul>
                            <li>
                                {t("Preparare il modello in formato")}
                                <b>{t(".docx, .doc, .odt")}</b>
                                {t(
                                    "utilizzando i tag che ci sono a disposizione"
                                )}
                            </li>
                            <li>
                                {t("Andare in: ")}
                                <b>
                                    {t(
                                        "Studio Personalizzazione Templates stampe personalizzate"
                                    )}
                                </b>{" "}
                                {t("e caricare il modello di stampa")}
                            </li>
                            <li>
                                {t("Cliccare su '")}
                                <i className="icon-print"></i>{" "}
                                <b>{t("Stampa template")}</b>
                            </li>
                        </ul>
                    </div>
                    <dl>
                        <dt>
                            <b>
                                <i>{t("Attenzione!")}</i>
                            </b>
                        </dt>
                        <dd>
                            <i>
                                {t(
                                    "I tag vanno sempre racchiusi tra parentesi quadre."
                                )}
                            </i>
                        </dd>
                        <dd>
                            <i>
                                {t(
                                    "Tutti i tag 'plurali', come udienze o impegni, richiedono di predisporre una tabella nel documento e di aggiungere un testo speciale al primo tag - ad esempio,"
                                )}{" "}
                                <strong>
                                    [fatture.TOTALE_COMPETENZE;block=tbs:row]
                                </strong>
                                {t(
                                    ". I modelli di esempio contengono impostazioni riutilizzabili per i tags plurali."
                                )}
                            </i>
                        </dd>
                    </dl>
                    {t(
                        "Qua è possibile scaricare sono dei modelli di esempio di template, uno per categoria:"
                    )}
                </Typography>
            </CardContent>
            <CardActions>
                <Stack
                    direction="row"
                    sx={{
                        m: 2,
                        width: "60%",
                        display: "flex",
                        justifyContent: "space-between",
                    }}
                    spacing={2}
                >
                    <Link
                        to={`https://${usedSubdomain}.netlex.cloud/prints/download-example?category=1`}
                        style={{ textDecoration: "none" }}
                    >
                        <Image path="/images/icon/Doc_ico.png" />{" "}
                        {t("Calendario")}
                    </Link>
                    <Link
                        to={`https://${usedSubdomain}.netlex.cloud/prints/download-example?category=2`}
                        style={{ textDecoration: "none" }}
                    >
                        <Image path="/images/icon/Doc_ico.png" /> {t("Fattura")}
                    </Link>
                    <Link
                        to={`https://${usedSubdomain}.netlex.cloud/prints/download-example?category=3`}
                        style={{ textDecoration: "none" }}
                    >
                        <Image path="/images/icon/Doc_ico.png" /> {t("Pratica")}
                    </Link>
                    <Link
                        to={`https://${usedSubdomain}.netlex.cloud/prints/download-example?category=4`}
                        style={{ textDecoration: "none" }}
                    >
                        <Image path="/images/icon/Doc_ico.png" />{" "}
                        {t("Lista udienze")}
                    </Link>
                    <Link
                        to={`https://${usedSubdomain}.netlex.cloud/prints/download-example?category=5`}
                        style={{ textDecoration: "none" }}
                    >
                        <Image path="/images/icon/Doc_ico.png" />{" "}
                        {t("Recupero crediti")}
                    </Link>
                </Stack>
            </CardActions>
        </Card>
    );
};
export default InfoGuide;
