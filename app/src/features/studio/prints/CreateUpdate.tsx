import { useEffect, useState } from "react";
import {
    Box,
    Button,
    TextField,
    Checkbox,
    FormControlLabel,
    FormGroup,
    MenuItem,
    Select,
    InputLabel,
    FormControl,
    Grid,
} from "@vapor/react-material";
import { useNavigate, useParams, Link, useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { useGetOnePrint } from "./hooks/usePrintsRequest";
import { useTranslation } from "@1f/react-sdk";

import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import Delete from "../../../custom-components/DeleteModal";
import SpinnerButton from "../../../custom-components/SpinnerButton";
import usePostCustom from "../../../hooks/usePostCustom";
import { useApi } from "../../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../../utilities/constants";

const CreateUpdate = () => {
    const [category, setCategory] = useState<any>("1");
    const [defaultPrint, setDefaultPrint] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({});

    const { t } = useTranslation();
    const navigate = useNavigate();
    const { id }: any = useParams<any>();
    const { data }: any = useGetOnePrint(id && id);

    const location: any = useLocation();
    const { categories }: any = location?.state || [];

    const postPrints = usePostCustom("prints/save", null);

    const deletePrints = usePostCustom(`prints/delete`, null);

    const onSubmit = async (data: any) => {
        const params = {
            ...data,
            category: category,
            defaultPrint,
        };

        if (id) {
            params.id = id.slice(3);
        }

        if (defaultPrint) {
            params.default_print = "on";
        }

        if (data.filename) {
            params.filename = data.filename[0];
        }

        const formData = new FormData();

        for (var key in params) {
            var value = params[key];
            formData.append(key, value);
        }

        setLoading(true);

        try {
            await postPrints.doFetch(true, formData);
            navigate("/prints");
        } catch (error) {
            console.error("Error submitting data:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (data && id) {
            setCategory(data.category || "1");
            setDefaultPrint(data.default_print === "1" ? true : false);
            setValue("filename", data.filename);
            setValue("title", data.title);
        }
    }, [data]);

    const handleDelete = async () => {
        await deletePrints.doFetch(true, { id: id.slice(3) });
        navigate("/prints");
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    id ? `${t("Modifica template")}` : `${t("Nuovo template")}`
                }
                pathToPrevPage="/prints"
            />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                maxWidth: 750,
                            },
                        }}
                    >
                        <Grid container>
                            <Grid item md={9}>
                                <FormControl fullWidth>
                                    <InputLabel id="demo-simple-select-label">
                                        {t("Tipo")}
                                    </InputLabel>
                                    <Select
                                        labelId="demo-simple-select-label"
                                        id="demo-simple-select"
                                        label="Allineamento"
                                        value={category}
                                        onChange={(e: any) =>
                                            setCategory(e.target.value)
                                        }
                                    >
                                        {categories?.map((item: any) => (
                                            <MenuItem value={item.id}>
                                                {item.name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                                <FormGroup>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={defaultPrint}
                                                onChange={(e: any) =>
                                                    setDefaultPrint(
                                                        e.target.checked
                                                    )
                                                }
                                            />
                                        }
                                        label="Default"
                                    />
                                </FormGroup>
                                {id ? (
                                    <Link
                                        to={`https://${usedSubdomain}.netlex.cloud/prints/getprint?uid=${data?.uniqueid}`}
                                        style={{ textDecoration: "none" }}
                                    >
                                        {data?.filename}
                                    </Link>
                                ) : (
                                    <TextField
                                        label="File (Max 40 MB)"
                                        type="file"
                                        variant="outlined"
                                        name="filename"
                                        inputProps={{
                                            ...register("filename"),
                                        }}
                                        error={Boolean(errors.filename)}
                                        helperText={
                                            typeof errors?.filename?.message ===
                                            "string"
                                                ? errors.filename.message
                                                : ""
                                        }
                                    />
                                )}
                                <TextField
                                    label={t("Descrizione *")}
                                    multiline={true}
                                    rows={4}
                                    {...register("title", {
                                        required: "Descrizione obbligatoria",
                                    })}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            onClick={() => {
                                navigate("/prints");
                            }}
                        >
                            {t("Annulla")}
                        </Button>
                        <SpinnerButton
                            type="submit"
                            label="Conferma"
                            isLoading={loading}
                            variant="contained"
                        />
                        {id && <Delete handleDelete={handleDelete} />}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};
export default CreateUpdate;
