import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const usePrintsData = (params?: any) => {
    const [data, setData] = useState<any>([]);
    const [categories, setCategories] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const getPrintsTemplate = useGetCustom("prints", params);

    const fetchData = async () => {
        setLoading(true);
        const response: any = await getPrintsTemplate.doFetch(true);
        setCategories(response?.data?.categories);
        setData(response?.data.prints);
        setLoading(false);
    }
    useEffect(() => {
        fetchData();
    }, []);

    return { data, categories, loading, fetchData };

}

export const useGetOnePrint = (id: string) => {
    const [data, setData] = useState<any>([]);
    const getOnePrint = useGetCustom(`prints/update?${id && id}`, {});

    useEffect(() => {
        const fetchData = async () => {
            if (!id) {
                console.warn('useGetOnePrint: ID is not provided');
                return;
            }

            const response: any = await getOnePrint.doFetch(true);
            console.log(response);
            setData(response && response?.data?.print);
        }

        fetchData()
    }, [id]);

    return { data };
}