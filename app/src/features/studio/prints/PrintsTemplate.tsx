import { useState } from "react";
import { Box, Grid, CircularProgress } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import { usePrintsData } from "./hooks/usePrintsRequest";
import { useTranslation } from "@1f/react-sdk";

import InfoIcon from '@mui/icons-material/Info';
import AddIcon from '@mui/icons-material/Add';

import PageTitle from "../../../custom-components/PageTitle";
import PrintCard from "./components/PrintCard";
import Filter from "./Filter";

const DEFAULT_PARAMS = {
    query: '',
    category: 'tutti', //stand for tutti
};

const PrintsTemplate = () => {
    const [defaultParams, setDefaultParams] = useState(DEFAULT_PARAMS);

    const { t } = useTranslation();
    const navigate = useNavigate();
    const { data, categories, loading, fetchData }: any = usePrintsData({ ...defaultParams, category: defaultParams.category === 'tutti' ? '' : defaultParams.category });

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const OnSubmit = async (e: any) => {
        e.preventDefault();
        await fetchData()
    };


    return (
        <>
            <PageTitle title={t("TEMPLATES STAMPE PERSONALIZZATE")} showBackButton={false} actionButtons={[
                {
                    label: `${t("Infoguida")}`,
                    variant: "outlined",
                    startIcon: <InfoIcon />,
                    onclick: () => { navigate('/prints/info') },
                },
                {
                    label: `${t("Nuovo")}`,
                    variant: "contained",
                    disabled: loading,
                    startIcon: <AddIcon />,
                    onclick: () => { navigate('/prints/update',{ state: { categories: categories }}) },
                }
            ]} />
            <Box><Filter defaultParams={defaultParams} categories={categories} onChangeInput={onChangeInput} onSubmit={OnSubmit} /></Box>
            {
                loading ?
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "60vh" }}><CircularProgress /></Box> :
                    <Box sx={{ pt: 4 }}>
                        <Grid
                            container
                            spacing={2}
                            sx={{ paddingInline: 7, alignItems: "stretch" }} // allunga gli elementi
                        >
                            {data?.map((item: any) => (
                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                    md={4}
                                    lg={4}
                                    xl={3}
                                    key={item.id}
                                    sx={{ display: "flex" }} // rende l'item un contenitore flex
                                >
                                    <PrintCard print={item} sx={{ flexGrow: 1 }} /> {/* Fa espandere la card */}
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
            }
        </>

    );
};

export default PrintsTemplate;
