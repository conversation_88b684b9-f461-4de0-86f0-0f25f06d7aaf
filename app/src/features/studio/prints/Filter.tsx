import { Box, Button, TextField, MenuItem, Select, FormControl, } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

interface FilterProps {
    defaultParams: any;
    categories: any;
    onChangeInput: (e: any) => void;
    onSubmit: (e: any) => void;
}

const Filter = ({ defaultParams, categories, onChangeInput, onSubmit }: FilterProps) => {

    const { t } = useTranslation();

    return (
        <Box component="form" display="flex" alignItems="end" gap={2} onSubmit={onSubmit} sx={{ pl: 5, pt: 3 }}>
            <TextField
                variant="outlined"
                label="Cerca:"
                value={defaultParams.query}
                name="query"
                sx={{ width: 1 / 5 }}
                onChange={onChangeInput}
            />
            <FormControl sx={{ width: 1 / 5 }} >
                <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    name="category"
                    value={defaultParams?.category}
                    onChange={onChangeInput}
                    defaultValue='tutti'
                >
                    <MenuItem value='tutti'>{t("Tutti")}</MenuItem>
                    {categories?.map((category: any) => (
                        <MenuItem key={category.id} value={category.id}>{category.name}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Button
                variant="contained"
                color="primary"
                type='submit'
            >
                {t("Cerca")}
            </Button>
        </Box>
    )
}
export default Filter;
