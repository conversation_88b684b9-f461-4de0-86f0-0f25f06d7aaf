import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";
const DocumentcategoriesCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;

    const { t } = useTranslation();
    const navigate = useNavigate();

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        gruppo: yup.string(),
        downloadable: yup.string(),
        abi: yup.string(),
        cab: yup.string(),
        bic: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("gruppo", response.data.result.gruppo);
                    setValue(
                        "downloadable",
                        response.data.result.downloadable === "1" ? "on" : ""
                    );
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            let data: any = {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                gruppo: getValues("gruppo"),
            };
            if (getValues("downloadable") == "on") {
                data.downloadable = getValues("downloadable");
            }
            await saveRequest.doFetch(true, data);
            navigate("/" + props.route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />
                            {/* autocomplete is pending */}
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="gruppo"
                                label="Gruppo"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["gruppo"] &&
                                    errors["gruppo"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["gruppo"] &&
                                    errors["gruppo"]["message"]
                                }
                            />
                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="downloadable"
                                label=""
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["downloadable"] &&
                                    errors["downloadable"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["downloadable"] &&
                                    errors["downloadable"]["message"]
                                }
                                options={[
                                    { label: "Visibile a tutti", value: "on" },
                                ]}
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default DocumentcategoriesCreateUpdate;
