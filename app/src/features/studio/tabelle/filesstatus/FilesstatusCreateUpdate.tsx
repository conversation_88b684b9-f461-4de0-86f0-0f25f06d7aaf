import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const FilesstatusCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        rinviabile: yup.string(),
        predefinito: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });
    //api list
    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setValue("rinviabile", "0");
                setValue("predefinito", "0");
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("rinviabile", response.data.result.segnalaritardo);
                    setValue("predefinito", response.data.result.predefinito);
                }
            } catch (error) {
                console.log("Request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                rinviabile: getValues("rinviabile"),
                predefinito: getValues("predefinito"),
            });
            navigate("/" + route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="rinviabile"
                                label="Pratica rinviabile"
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["rinviabile"] &&
                                    errors["rinviabile"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["rinviabile"] &&
                                    errors["rinviabile"]["message"]
                                }
                                options={[
                                    { label: "Si", value: "1" },
                                    { label: "No", value: "0" },
                                ]}
                            />

                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="predefinito"
                                label="Preferita"
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["predefinito"] &&
                                    errors["predefinito"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["predefinito"] &&
                                    errors["predefinito"]["message"]
                                }
                                options={[
                                    { label: "Si", value: "1" },
                                    { label: "No", value: "0" },
                                ]}
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default FilesstatusCreateUpdate;
