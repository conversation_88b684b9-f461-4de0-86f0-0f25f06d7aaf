import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useNavigate } from "react-router-dom";
import {
    AccountBalance,
    ListAlt,
    LocalAtm,
    SellOutlined,
    AccountBalanceWalletOutlined,
    VideocamOutlined,
    ReceiptOutlined,
    CurrencyExchangeOutlined,
    ImportExportOutlined,
    MonitorHeartOutlined,
    AccountBoxOutlined,
} from "@mui/icons-material";
import { Card, Link, CardContent, Box } from "@vapor/react-material";
import SimpleCreateUpdate from "./SimpleCreateUpdate";
import SimpleListIndex from "./SimpleList";
import ItemListIndex from "./itemlist/ItemlistIndex";
import ItemListCreateUpdate from "./itemlist/ItemListCreateUpdate";
import ItemVociCreateUpdate from "./itemlist/ItemVociCreateUpdate";
import TemplateListIndex from "./templates/TemplateIndex";
import TemplateCreateUpdate from "./templates/CreateUpdate";
import { Link as RouterLink } from "react-router-dom";

export const TabelleIndex = () => {
    const { t } = useTranslation("translation");
    const navigate = useNavigate();
    const tables = [
        {
            restricted: false,
            isHidden: false,
            icon: <MonitorHeartOutlined />,
            description: t("Lista delle attività udienze"),
            to: "/attivitaudienze",
            linkText: t("Attività udienze"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <AccountBoxOutlined />,
            description: t("Lista delle autorità"),
            to: "/authorities",
            linkText: t("Autorità"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <AccountBalance />,
            description: t("Lista delle banche e casse"),
            to: "/banks",
            linkText: t("Banche e casse"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <ListAlt />,
            description: t("Lista delle categorie documenti"),
            to: "/documentcategories",
            linkText: t("Categorie documenti"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Categorie Impegni"),
            to: "/deadlinecategories",
            linkText: t("Categorie Impegni"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <ListAlt />,
            description: t("Lista delle categorie macro"),
            to: "/macrocategories",
            linkText: t("Categorie macro"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Categorie modelli"),
            to: "/modelcategories",
            linkText: t("Categorie modelli"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <LocalAtm />,
            description: t("Lista dei centri profitto"),
            to: "/centroprofitto",
            linkText: t("Centro profitto"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <AccountBalance />,
            description: t("Lista delle città"),
            to: "/cities",
            linkText: t("Città"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Contatti e Indirizzi"),
            to: "/contactandaddress",
            linkText: t("Contatti e Indirizzi"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista delle cumulative liquidazione PA"),
            to: "/liquidationpacumulative",
            linkText: t("Cumulativa Liquidazione PA"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <ImportExportOutlined />,
            description: t("Descrizioni"),
            to: "/descriptions",
            linkText: t("Descrizioni"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <ImportExportOutlined />,
            description: t("In/out"),
            to: "/inout",
            linkText: t("In/out"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <VideocamOutlined />,
            description: t("Lista degli istruttori"),
            to: "/instructors",
            linkText: t("Istruttori"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <VideocamOutlined />,
            description: t("Lista degli listino"),
            to: "/itemlist",
            linkText: t("Listino"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <VideocamOutlined />,
            description: t("Lista degli modelli"),
            to: "/templates",
            linkText: t("Modelli"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <AccountBalanceWalletOutlined />,
            description: t("Lista dei modi di pagamento"),
            to: "/paymenttypes",
            linkText: t("Modi di pagamento"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <AccountBalanceWalletOutlined />,
            description: t("Lista dei note aggiuntive"),
            to: "/additionalnotes",
            linkText: t("Note aggiuntive"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista oggetti"),
            to: "/objects",
            linkText: t("Oggetti"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista oggetti documento"),
            to: "/documentobject",
            linkText: t("Oggetti documento"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista dei PM"),
            to: "/publicprosecutors",
            linkText: t("PM"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista reati"),
            to: "/crimes",
            linkText: t("Reati"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista riferimento liquidazione"),
            to: "/liquidationreference",
            linkText: t("Riferimento liquidazione"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista dei ruoli processuali"),
            to: "/proceduralroles",
            linkText: t("Ruoli processuali"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista delle situazioni"),
            to: "/situazione",
            linkText: t("Situazioni"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista delle situazioni"),
            to: "/situazionecontabile",
            linkText: t("Situazioni contabili"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista degli Stati documento"),
            to: "/documentstatus",
            linkText: t("Stati documento"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Stati impegni"),
            to: "/deadlinestatus",
            linkText: t("Stati impegni"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista degli stati pratica"),
            to: "/filesstatus",
            linkText: t("Stati pratica"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista degli sedi"),
            to: "/sedi",
            linkText: t("Sedi"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <ReceiptOutlined />,
            description: t("Lista delle sezionali"),
            to: "/sectional",
            linkText: t("Sezionali fatture"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista delle spese"),
            to: "/expenditures",
            linkText: t("Spese fisse"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista delle spiegazioni movimenti"),
            to: "/spiegazioni",
            linkText: t("Spiegazioni movimenti"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista degli stati liquidazione PA"),
            to: "/liquidationpastatus",
            linkText: t("Stato liquidazione PA"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Stati imposta di registro"),
            to: "/liquidationparegstatus",
            linkText: t("Stato imposta di registro"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Tag"),
            to: "/tags",
            linkText: t("Tag"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Timesheet standard"),
            to: "/attivita",
            linkText: t("Timesheet standard"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Impegni standard"),
            to: "/deadlinestandard",
            linkText: t("Impegni standard"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista dei tipi atto liquidazione PA"),
            to: "/liquidationpadeedtype",
            linkText: t("Tipo atto liquidazione PA"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Lista dei iipologie impegno"),
            to: "/deadlinestypes",
            linkText: t("Tipologie impegno"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Description"),
            to: "/filestypes",
            linkText: t("Tipologie pratica"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Registro unico antiriciclaggio"),
            to: "/antirecregister",
            linkText: t("Registro unico antiriciclaggio"),
        },

        {
            restricted: false,
            isHidden: false,
            icon: <SellOutlined />,
            description: t("Unità Di Misura"),
            to: "/unitmeasure",
            linkText: t("Unità Di Misura"),
        },
        {
            restricted: false,
            isHidden: false,
            icon: <CurrencyExchangeOutlined />,
            description: t("Description"),
            to: "/currencies",
            linkText: t("Valute"),
        },
    ];

    return (
        <VaporPage title={t("Tabelle")}>
            <VaporPage.Section>
                <Box display="flex" gap={2} style={{ flexWrap: "wrap" }}>
                    {tables.map((index) => {
                        return index.isHidden ? null : (
                            <Card
                                style={{
                                    cursor: "pointer",
                                    marginBottom: "0.5rem",
                                    border: "1px solid #ccc",
                                    borderRadius: "4px",
                                    maxHeight: "8rem",
                                    minWidth: "25rem",
                                }}
                                onClick={() => navigate(index.to)}
                                key={index.linkText}
                            >
                                <CardContent
                                    style={{
                                        display: "flex",
                                        padding: "0.7rem 0.7rem",
                                        alignItems: "center",
                                    }}
                                >
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                        }}
                                    >
                                        <span
                                            className="icon-span bg-light text-secondary rounded m-auto font-18"
                                            style={{
                                                display: "flex",
                                                height: "36px",
                                                width: "36px",
                                            }}
                                        >
                                            {index.icon}
                                        </span>
                                        <span
                                            style={{
                                                paddingLeft: "12px",
                                                paddingRight: "12px",
                                            }}
                                        >
                                            <Link
                                                component={RouterLink}
                                                className="tablesindex-item-link text-muted fw-bold"
                                                to={index.to}
                                            >
                                                {index?.linkText}
                                            </Link>
                                            <p className="m-0">
                                                {index.description}
                                            </p>
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })}
                </Box>
            </VaporPage.Section>
        </VaporPage>
    );
};

export const tabelle = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/index/tabelle",
            element: <TabelleIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/attivitaudienze",
            element: <SimpleListIndex route="attivitaudienze" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/authorities",
            element: <SimpleListIndex route="authorities" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/banks",
            element: <SimpleListIndex route="banks" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentcategories",
            element: <SimpleListIndex route="documentcategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/spiegazioni",
            element: <SimpleListIndex route="spiegazioni" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/cities",
            element: <SimpleListIndex route="cities" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/contactandaddress",
            element: <SimpleListIndex route="contactandaddress" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/descriptions",
            element: <SimpleListIndex route="descriptions" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/inout",
            element: <SimpleListIndex route="inout" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/paymenttypes",
            element: <SimpleListIndex route="paymenttypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/additionalnotes",
            element: <SimpleListIndex route="additionalnotes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentobject",
            element: <SimpleListIndex route="documentobject" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/filesstatus",
            element: <SimpleListIndex route="filesstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sedi",
            element: <SimpleListIndex route="sedi" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sectional",
            element: <SimpleListIndex route="sectional" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/expenditures",
            element: <SimpleListIndex route="expenditures" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/attivita",
            element: <SimpleListIndex route="attivita" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestandard",
            element: <SimpleListIndex route="deadlinestandard" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestypes",
            element: <SimpleListIndex route="deadlinestypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/filestypes",
            element: <SimpleListIndex route="filestypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/antirecregister",
            element: <SimpleListIndex route="antirecregister" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/currencies",
            element: <SimpleListIndex route="currencies" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinecategories",
            element: <SimpleListIndex route="deadlinecategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/macrocategories",
            element: <SimpleListIndex route="macrocategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/modelcategories",
            element: <SimpleListIndex route="modelcategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/centroprofitto",
            element: <SimpleListIndex route="centroprofitto" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpacumulative",
            element: <SimpleListIndex route="liquidationpacumulative" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationreference",
            element: <SimpleListIndex route="liquidationreference" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/situazione",
            element: <SimpleListIndex route="situazione" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/situazionecontabile",
            element: <SimpleListIndex route="situazionecontabile" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentstatus",
            element: <SimpleListIndex route="documentstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestatus",
            element: <SimpleListIndex route="deadlinestatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpastatus",
            element: <SimpleListIndex route="liquidationpastatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationparegstatus",
            element: <SimpleListIndex route="liquidationparegstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpadeedtype",
            element: <SimpleListIndex route="liquidationpadeedtype" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/unitmeasure",
            element: <SimpleListIndex route="unitmeasure" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/instructors",
            element: <SimpleListIndex route="instructors" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/itemlist",
            element: <ItemListIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/templates",
            element: <TemplateListIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/objects",
            element: <SimpleListIndex route="objects" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/publicprosecutors",
            element: <SimpleListIndex route="publicprosecutors" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/crimes",
            element: <SimpleListIndex route="crimes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/proceduralroles",
            element: <SimpleListIndex route="proceduralroles" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/tags",
            element: <SimpleListIndex route="tags" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/attivitaudienze/update/:uniqueId?",
            element: <SimpleCreateUpdate route="attivitaudienze" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/authorities/update/:uniqueId?",
            element: <SimpleCreateUpdate route="authorities" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/banks/update/:uniqueId?",
            element: <SimpleCreateUpdate route="banks" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentcategories/update/:uniqueId?",
            element: <SimpleCreateUpdate route="documentcategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/spiegazioni/update/:uniqueId?",
            element: <SimpleCreateUpdate route="spiegazioni" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/cities/update/:uniqueId?",
            element: <SimpleCreateUpdate route="cities" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/contactandaddress/update/:uniqueId?",
            element: <SimpleCreateUpdate route="contactandaddress" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/descriptions/update/:uniqueId?",
            element: <SimpleCreateUpdate route="descriptions" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/inout/update/:uniqueId?",
            element: <SimpleCreateUpdate route="inout" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/paymenttypes/update/:uniqueId?",
            element: <SimpleCreateUpdate route="paymenttypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/additionalnotes/update/:uniqueId?",
            element: <SimpleCreateUpdate route="additionalnotes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentobject/update/:uniqueId?",
            element: <SimpleCreateUpdate route="documentobject" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/filesstatus/update/:uniqueId?",
            element: <SimpleCreateUpdate route="filesstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sedi/update/:uniqueId?",
            element: <SimpleCreateUpdate route="sedi" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/sectional/update/:uniqueId?",
            element: <SimpleCreateUpdate route="sectional" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/expenditures/update/:uniqueId?",
            element: <SimpleCreateUpdate route="expenditures" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/attivita/update/:uniqueId?",
            element: <SimpleCreateUpdate route="attivita" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestandard/update/:uniqueId?",
            element: <SimpleCreateUpdate route="deadlinestandard" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestypes/update/:uniqueId?",
            element: <SimpleCreateUpdate route="deadlinestypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/filestypes/update/:uniqueId?",
            element: <SimpleCreateUpdate route="filestypes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/antirecregister/update/:uniqueId?",
            element: <SimpleCreateUpdate route="antirecregister" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/currencies/update/:uniqueId?",
            element: <SimpleCreateUpdate route="currencies" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinecategories/update/:uniqueId?",
            element: <SimpleCreateUpdate route="deadlinecategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/macrocategories/update/:uniqueId?",
            element: <SimpleCreateUpdate route="macrocategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/modelcategories/update/:uniqueId?",
            element: <SimpleCreateUpdate route="modelcategories" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/centroprofitto/update/:uniqueId?",
            element: <SimpleCreateUpdate route="centroprofitto" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpacumulative/update/:uniqueId?",
            element: <SimpleCreateUpdate route="liquidationpacumulative" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationreference/update/:uniqueId?",
            element: <SimpleCreateUpdate route="liquidationreference" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/situazione/update/:uniqueId?",
            element: <SimpleCreateUpdate route="situazione" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/situazionecontabile/update/:uniqueId?",
            element: <SimpleCreateUpdate route="situazionecontabile" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/documentstatus/update/:uniqueId?",
            element: <SimpleCreateUpdate route="documentstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/deadlinestatus/update/:uniqueId?",
            element: <SimpleCreateUpdate route="deadlinestatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpastatus/update/:uniqueId?",
            element: <SimpleCreateUpdate route="liquidationpastatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationparegstatus/update/:uniqueId?",
            element: <SimpleCreateUpdate route="liquidationparegstatus" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/liquidationpadeedtype/update/:uniqueId?",
            element: <SimpleCreateUpdate route="liquidationpadeedtype" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/unitmeasure/update/:uniqueId?",
            element: <SimpleCreateUpdate route="unitmeasure" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/instructors/update/:uniqueId?",
            element: <SimpleCreateUpdate route="instructors" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/itemlist/update-list/:uniqueId?",
            element: <ItemListCreateUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/itemlist/update/:uniqueId?",
            element: <ItemVociCreateUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/templates/update/:uniqueId?",
            element: <TemplateCreateUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/objects/update/:uniqueId?",
            element: <SimpleCreateUpdate route="objects" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/publicprosecutors/update/:uniqueId?",
            element: <SimpleCreateUpdate route="publicprosecutors" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/crimes/update/:uniqueId?",
            element: <SimpleCreateUpdate route="crimes" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/proceduralroles/update/:uniqueId?",
            element: <SimpleCreateUpdate route="proceduralroles" />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/tags/update/:uniqueId?",
            element: <SimpleCreateUpdate route="tags" />,
        },
    },
];
