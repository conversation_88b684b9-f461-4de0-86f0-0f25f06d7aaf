import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const AuthoritaCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [cittaOption, setCittaOption] = useState<any>([]);
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        autorita: yup.string(),
        nome: yup.string().required(),
        citta: yup.string(),
        merge: yup.string(),
        preferita: yup.string(),
        pwid: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });
    //api list
    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("citta", response.data.result.citta);
                    setValue(
                        "preferita",
                        response.data.result.preferita === "1" ? "on" : ""
                    );
                }
                setCittaOption(
                    response.data.cities.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
            } catch (error) {
                console.log("authoritaRequest error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            let data: any = {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                citta: getValues("citta"),
            };
            if (getValues("preferita") == "on") {
                data.preferita = getValues("preferita");
            }
            await saveRequest.doFetch(true, data);
            navigate("/" + route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="citta"
                                label="Città"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["citta"] &&
                                    errors["citta"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["citta"] &&
                                    errors["citta"]["message"]
                                }
                                options={cittaOption}
                            />

                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="preferita"
                                label=""
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["preferita"] &&
                                    errors["preferita"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["preferita"] &&
                                    errors["preferita"]["message"]
                                }
                                options={[{ label: "Preferita", value: "on" }]}
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default AuthoritaCreateUpdate;
