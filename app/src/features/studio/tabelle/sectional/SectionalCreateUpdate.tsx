import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack, InputLabel } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const SectionalCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
        checkValidation,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [esempioValue, setEsempioValue] = useState<string>();

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        externalCode: yup.string(),
        tipo: yup.string(),
        valore: yup
            .string()
            .matches(/^[A-Z]+$/, "Must be only Uppercase Alphabet")
            .min(2, "Must be exactly 2 digits")
            .max(2, "Must be exactly 2 digits")
            .required(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        watch,
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });
    //api list
    const saveRequest = usePostCustom(route + "/save");

    let watchtipo = watch("tipo");

    useEffect(() => {
        var str = "";
        switch (getValues("tipo")) {
            case "1":
                str = "1/2018";
                break;
            case "2":
                str = "1/2018-Bis";
                break;
            case "3":
                str = "1/2018/A";
                break;
            case "4":
                str = "1/18A";
                break;
        }
        setEsempioValue(str);
    }, [watchtipo]);

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setValue("tipo", "3");
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("externalCode", response.data.result.externalCode);
                    setValue("tipo", response.data.result.tipo);
                    setValue("valore", response.data.result.valore);
                }
            } catch (error) {
                console.log("Request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                externalCode: getValues("externalCode") ?? "",
                tipo: getValues("tipo"),
                valore: getValues("valore"),
            });
            navigate("/" + route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome Sezionale"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="externalCode"
                                label="Codice esterno"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["externalCode"] &&
                                    errors["externalCode"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["externalCode"] &&
                                    errors["externalCode"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="tipo"
                                label="Tipologia"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"]
                                }
                                options={[
                                    { label: "YYYY/T", value: "3" },
                                    { label: "YYT", value: "4" },
                                ]}
                            />
                            <div className="css-12l4ysv MuiTextField-root">
                                <InputLabel>Esempio</InputLabel>
                                <InputLabel>{esempioValue}</InputLabel>
                            </div>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="valore"
                                label="Valore"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["valore"] &&
                                    errors["valore"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["valore"] &&
                                    errors["valore"]["message"]
                                }
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default SectionalCreateUpdate;
