import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const AdditionalnoteCreateUpdate = (props: ISimpleCreateUpdate) => {
    let { handleDelete, redirectBack, uniqueId, route } = props;

    const { t } = useTranslation();
    const navigate = useNavigate();

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? route + "/update?uniqueid=" + uniqueId
            : route + "/update"
    );
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        title: yup.string().required(),
        description: yup.string().required(),
        is_default: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    const saveRequest = usePostCustom("additionalnotes/update");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("title", response.data.result.title);
                    setValue("description", response.data.result.description);
                    setValue(
                        "is_default",
                        response.data.result.is_default === "1" ? "on" : ""
                    );
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let data: any = {
            uniqueid: getValues("uniqueid") ?? "",
            title: getValues("title"),
            description: getValues("description"),
        };
        if (getValues("is_default") == "on") {
            data.is_default = getValues("is_default");
        }
        await saveRequest.doFetch(true, data);
        navigate("/" + props.route);
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="title"
                                label="Titolo"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["title"] &&
                                    errors["title"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["title"] &&
                                    errors["title"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="description"
                                label="Descrizione"
                                type="textarea"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["description"] &&
                                    errors["description"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["description"] &&
                                    errors["description"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="is_default"
                                label=""
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["is_default"] &&
                                    errors["is_default"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["is_default"] &&
                                    errors["is_default"]["message"]
                                }
                                options={[
                                    {
                                        label: "Seleziona come nota di default",
                                        value: "on",
                                    },
                                ]}
                            />
                        </Stack>
                    </div>
                </Box>
                <br />
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default AdditionalnoteCreateUpdate;
