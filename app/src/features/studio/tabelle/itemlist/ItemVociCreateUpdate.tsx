import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import PageTitle from "../../../../custom-components/PageTitle";

const ItemVociCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [categoryList, setCategoryList] = useState<any>([]);

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? "itemlist/update?uniqueid=" + uniqueId
            : "itemlist/update"
    );
    let schema: any = yup.object().shape({
        uniqueid: yup.string(),
        descrizione: yup.string().required(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({
        resolver: yupResolver(schema),
    });
    const [open, setOpen] = useState(false);

    //api list

    const saveRequest = usePostCustom("itemlist/save");

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await updateMountedRequest.doFetch();
                setCategoryList(data.result.itemlists);

                const shapes: any = Object.keys(data.result.itemlists).map(
                    (row: any) => {
                        return {
                            ["list_" + data.result.itemlists[row]["id"]]:
                                yup.string(),
                        };
                    }
                );

                schema = yup.object().shape({ ...shapes });

                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("descrizione", data.result.nome);
                    Object.keys(data.result.itemlists).map((row: any) => {
                        setValue(
                            "list_" + data.result.itemlists[row]["id"],
                            parseFloat(data.result.itemlists[row]["prezzo"])
                        );
                    });
                } else {
                    Object.keys(data.result.itemlists).map((row: any) => {
                        setValue(
                            "list_" + data.result.itemlists[row]["id"],
                            parseFloat("0")
                        );
                    });
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async (data: any) => {
        // call
        data.uniqueid = uniqueId ?? "";
        await saveRequest.doFetch(true, data);
        navigate("/itemlist");
    };

    const deleteRequest = usePostCustom(
        uniqueId !== undefined
            ? "itemlist/delete?uniqueid=" + uniqueId
            : "itemlist/delete"
    );

    const redirectBack = () => {
        navigate("/itemlist");
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/itemlist");
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    (uniqueId !== undefined ? "Modifica " : `Nuovo `) + "Voci"
                }
                pathToPrevPage={"/itemlist"}
            />
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={"No"}
                agree={"Yes, Delete"}
                confirmText={"Are you sure want to Delete"}
                title={"Delete Action"}
            />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="descrizione"
                                    label="Nome voce"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["descrizione"] &&
                                        errors["descrizione"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["descrizione"] &&
                                        errors["descrizione"]["message"]
                                    }
                                />
                                {(categoryList || []).map((row: any) => {
                                    return (
                                        <FormInput
                                            style={{ width: 250 }}
                                            control={control}
                                            name={"list_" + row.id}
                                            label={"Valore " + row.descrizione}
                                            type="number"
                                            variant="outlined"
                                            setValue={setValue}
                                        />
                                    );
                                })}
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={redirectBack}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId !== undefined && (
                            <>
                                <Button
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    type="button"
                                >
                                    {t("Delete")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default ItemVociCreateUpdate;
