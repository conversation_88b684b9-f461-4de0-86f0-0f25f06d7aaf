import VaporPage from "@vapor/react-custom/VaporPage";
import Vapor<PERSON>eaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../../custom-components/Spinner";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import useGetCustom from "../../../../hooks/useGetCustom";

export default function VociIndex() {
    const navigate = useNavigate();

    const filterRequest = useGetCustom("itemlist/list0?noTemplateVars=true");

    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter(filterRequest, "voci");

    const { mountedData } = useMountedData();

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleClickCallback = (uniqueid: string) => {
        navigate("/itemlist/update/" + uniqueid);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="voci"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChange}
                onClickCallback={handleClickCallback}
            />
        );
    };
    const redirectAction = () => {
        navigate("/itemlist/update");
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button variant="contained" onClick={redirectAction}>
                            Aggiungi
                        </Button>,
                    ]}
                    title={"VOCI"}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                        fieldLabel="Voce"
                        fieldName="nomevoceSearch"
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
