import { Box, Button, TextField } from "@vapor/react-material";
import { IFilterProps } from "./interfaces/simpleList.interface";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData, fieldLabel, fieldName } =
        props;

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={fieldLabel}
                variant="outlined"
                name={fieldName}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={
                    query[fieldName !== undefined ? fieldName : "searchField"]
                }
            />

            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
