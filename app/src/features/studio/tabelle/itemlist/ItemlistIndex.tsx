import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Grid, Button } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import ListiniIndex from "./ListiniIndex";
import VociIndex from "./VociIndex";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "@vapor/react-icons";

export default function ItemListIndex() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    leftItems={[
                        <Button onClick={() => navigate("/index/tabelle")}>
                            <ArrowLeft />
                        </Button>,
                    ]}
                    title={t("Gestione Listini")}
                />
                <VaporPage.Section>
                    <Grid container>
                        <Grid item xs={12} md={6}>
                            <ListiniIndex />
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <VociIndex />
                        </Grid>
                    </Grid>
                </VaporPage.Section>
            </VaporPage>
        </>
    );
}
