import {
    Box,
    <PERSON>ton,
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    IconButton,
} from "@vapor/react-material";
import { Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import {
    arrayMove,
    SortableContext,
    rectSortingStrategy,
} from "@dnd-kit/sortable";
import { SortableItem, Item } from "../../../../custom-components/SortableItem";
import {
    DndContext,
    DragOverlay,
    PointerSensor,
    useSensor,
    useSensors,
    DragStartEvent,
    DragEndEvent,
    TouchSensor,
    closestCenter,
} from "@dnd-kit/core";
import { useEffect, useState } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export default function PrioritaModal(props: any) {
    const getPriority = usePostCustom(
        "itemlist/getpriority?noTemplateVars=true"
    );

    const { handleClose, modalState } = props;
    const { t } = useTranslation();

    const [items, setItems] = useState<any>([]);

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await getPriority.doFetch(true);
                setItems(data);
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    // for drag overlay
    const [activeItem, setActiveItem] = useState<any>();

    // for input methods detection
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(TouchSensor)
    );

    // triggered when dragging starts
    const handleDragStart = (event: DragStartEvent) => {
        const { active } = event;
        setActiveItem(items.find((item: any) => item.id === active.id));
    };

    // triggered when dragging ends
    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over) return;

        const activeItem = items.find((item: any) => item.id === active.id);
        const overItem = items.find((item: any) => item.id === over.id);

        if (!activeItem || !overItem) {
            return;
        }

        const activeIndex = items.findIndex(
            (item: any) => item.id === active.id
        );
        const overIndex = items.findIndex((item: any) => item.id === over.id);

        if (activeIndex !== overIndex) {
            setItems((prev: any) =>
                arrayMove<any>(prev, activeIndex, overIndex)
            );
        }
        setActiveItem(undefined);
    };

    const handleDragCancel = () => {
        setActiveItem(undefined);
    };

    const saveRequest = usePostCustom("itemlist/savepriority");
    const handleButtonClick = async () => {
        const itemIds = items.map((item: any) => item.id);
        const formData = new FormData();
        let index = 0;
        for (var key in itemIds) {
            var value = itemIds[key];
            formData.append("data[" + index + "][id]", value);
            formData.append("data[" + index + "][priority]", index.toString());
            index++;
        }
        await saveRequest.doFetch(true, formData);
        handleClose();
    };
    return (
        <>
            <Dialog
                open={modalState}
                onClose={() => handleClose()}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Priorità")}
                    <IconButton color="primary" onClick={() => handleClose()}>
                        <Close />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />
                <DialogContent>
                    <Box
                        autoComplete="off"
                        component="form"
                        noValidate
                        sx={{
                            "& > :not(style)": {
                                mt: 2,
                                mb: 4,
                                ml: 0,
                                width: 550,
                            },
                        }}
                    >
                        <p>
                            {t(
                                "Spostando i blocchi puoi impostare l'ordine delle priorità. Questa impostazione verrà usata per le pratiche in cui possono confluire più listini."
                            )}
                        </p>
                        <DndContext
                            sensors={sensors}
                            collisionDetection={closestCenter}
                            onDragStart={handleDragStart}
                            onDragEnd={handleDragEnd}
                            onDragCancel={handleDragCancel}
                        >
                            <SortableContext
                                items={items}
                                strategy={rectSortingStrategy}
                            >
                                {items.map((item: any) => (
                                    <SortableItem key={item.id} item={item} />
                                ))}
                            </SortableContext>
                            <DragOverlay
                                adjustScale
                                style={{ transformOrigin: "0 0 " }}
                            >
                                {activeItem ? (
                                    <Item item={activeItem} isDragging />
                                ) : null}
                            </DragOverlay>
                        </DndContext>
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => handleClose()} variant="outlined">
                        {t("Annulla")}
                    </Button>
                    <Button
                        onClick={handleButtonClick}
                        variant="contained"
                        sx={{
                            mr: 1,
                        }}
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
}
