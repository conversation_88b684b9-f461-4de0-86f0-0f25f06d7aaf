import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import PageTitle from "../../../../custom-components/PageTitle";
import React from "react";
import { debounce } from "lodash";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";
const ItemListCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [categoryList, setCategoryList] = useState<any>([]);

    const updateMountedRequest = useGetCustom(uniqueId !== undefined ? "itemlist/update-list?uniqueid=" + uniqueId : "itemlist/update-list");
    let schema: any = yup.object().shape({
        uniqueid: yup.string(),
        descrizione: yup.string().required(),
        tagsList: yup.string(),
        orario: yup.string()
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue
    } = useForm({
        resolver: yupResolver(schema)
    });
    const [open, setOpen] = useState(false);
    // autocomplete
    const [tagList, setTagList] = React.useState<any>([]);
    const [tagData, setTagData] = React.useState<any>([]);
    const tagRequest = usePostCustom("itemlist/getavailabletags?noTemplateVars=true");

    const getData = async (searchTerm: any) => {
        const { data }: any = await tagRequest.doFetch(true, {
            searchWord: searchTerm
        });
        setTagList(
            data.map((row: any) => {
                return row.description;
            })
        );
    };

    const getSearchResults = useCallback(
        debounce((value: any) => {
            getData(value);
        }, 800),
        []
    );
    const onInputChange = (_event: any, value: any, _reason: any) => {
        if (value) {
            getSearchResults(value);
        } else {
            setTagList([]);
        }
    };
    //api list

    const saveRequest = usePostCustom("itemlist/listsave?noTemplateVars=true");
    const duplicateRequest = usePostCustom("itemlist/duplicate-list?noTemplateVars=true");

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await updateMountedRequest.doFetch();
                setCategoryList(data.result.voci);

                const shapes: any = Object.keys(data.result.voci).map((row: any) => {
                    return {
                        ["voce_" + data.result.voci[row]["id"]]: yup.string()
                    };
                });

                schema = yup.object().shape({ ...shapes });

                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("descrizione", data.result.nome);
                    setValue("orario", data.result.orario == "1" ? "on" : "");
                    Object.keys(data.result.voci).map((row: any) => {
                        setValue("voce_" + data.result.voci[row]["id"], parseFloat(data.result.voci[row]["prezzo"]));
                    });
                    setTagData(
                        JSON.parse(data.result.tagsList).map((row: any) => {
                            console.log("row", row);
                            return row.description;
                        })
                    );
                } else {
                    Object.keys(data.result.voci).map((row: any) => {
                        setValue("voce_" + data.result.voci[row]["id"], parseFloat("0"));
                    });
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async (data: any) => {
        // call
        data.uniqueid = uniqueId ?? "";
        data.tagsList = tagData.toString();
        await saveRequest.doFetch(true, data);
        navigate("/itemlist");
    };

    const deleteRequest = usePostCustom("itemlist/listdelete?noTemplateVars=true&uniqueid=" + uniqueId);

    const redirectBack = () => {
        navigate("/itemlist");
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/itemlist");
    };

    const handleDuplicate = async () => {
        let data: any = {};
        data.uniqueid = uniqueId ?? "";
        await duplicateRequest.doFetch(true, data);
        navigate("/itemlist");
    };
    return (
        <VaporPage>
            <PageTitle title={(uniqueId !== undefined ? "Modifica " : `Nuovo `) + "Listino"} pathToPrevPage={"/itemlist"} />
            <ConfirmModal open={open} handleDecline={handleDecline} handleAgree={handleAgree} decline={"No"} agree={"Yes, Delete"} confirmText={"Are you sure want to Delete"} title={"Delete Action"} />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250
                            }
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput style={{ width: 400 }} control={control} name="descrizione" label="Nome listino" type="text" variant="outlined" setValue={setValue} error={errors && errors["descrizione"] && errors["descrizione"]["message"] !== "" ? true : false} helperText={errors && errors["descrizione"] && errors["descrizione"]["message"]} />
                                <CustomAutocomplete
                                    sx={{
                                        "& .MuiTextField-root": {
                                            m: 1,
                                            width: 400
                                        }
                                    }}
                                    onChange={(_event: any, newValue: any) => {
                                        setTagData(newValue);
                                    }}
                                    freeSolo
                                    autoSelect
                                    options={tagList}
                                    onInputChange={onInputChange}
                                    multiple
                                    getOptionLabel={(option: any) => {
                                        return option;
                                    }}
                                    value={tagData}
                                    renderInput={(params: any) => <FormInput {...params} control={control} name="tagsList" label="Tags" setValue={setValue} error={errors && errors["tagsList"] && errors["tagsList"]["message"] !== "" ? true : false} helperText={errors && errors["tagsList"] && errors["tagsList"]["message"]} />}
                                />
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="orario"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    disabled={uniqueId !== undefined}
                                    error={errors && errors["orario"] && errors["orario"]["message"] !== "" ? true : false}
                                    helperText={errors && errors["orario"] && errors["orario"]["message"]}
                                    options={[
                                        {
                                            label: "Listino orario",
                                            value: "on"
                                        }
                                    ]}
                                />
                                {(categoryList || []).map((row: any, i: number) => {
                                    return <FormInput style={{ width: 250 }} control={control} name={"voce_" + row.id} label={"Voce " + (i + 1) + " (" + row.descrizione + ")"} type="number" variant="outlined" setValue={setValue} />;
                                })}
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100
                            }
                        }}
                    >
                        <Button variant="outlined" type="button" onClick={redirectBack}>
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId !== undefined && (
                            <>
                                <Button variant="outlined" onClick={handleDuplicate} type="button">
                                    {t("Duplica")}
                                </Button>
                                <Button color="error" variant="outlined" onClick={handleDelete} type="button">
                                    {t("Delete")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default ItemListCreateUpdate;
