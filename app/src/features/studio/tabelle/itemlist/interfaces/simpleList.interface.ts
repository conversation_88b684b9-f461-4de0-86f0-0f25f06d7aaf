export interface IDefaultQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    [key: string]: any;
}

export interface IFilterProps {
    defaultQuery: IDefaultQuery;
    query: IDefaultQuery;
    setQuery: React.Dispatch<React.SetStateAction<IDefaultQuery>>;
    filterData: (query: IDefaultQuery) => void;
    mountedData: any;
    fieldLabel: string;
    fieldName: string;
}
