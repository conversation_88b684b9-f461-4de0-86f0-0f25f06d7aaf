import VaporPage from "@vapor/react-custom/VaporPage";
import Vapor<PERSON>eaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../../custom-components/Spinner";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useState } from "react";
import PrioritaModal from "./PrioritaModal";

export default function ListiniIndex() {
    const navigate = useNavigate();

    const filterRequest = useGetCustom("itemlist/list1?noTemplateVars=true");

    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter(filterRequest, "listini");

    const [modalState, setModalState] = useState<boolean>(false);

    const { mountedData } = useMountedData();

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleClickCallback = (uniqueid: string) => {
        navigate("/itemlist/update-list/" + uniqueid);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="listini"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChange}
                onClickCallback={handleClickCallback}
            />
        );
    };
    const redirectAction = () => {
        navigate("/itemlist/update-list");
    };

    const handleOpen = () => {
        setModalState(true);
    };
    const handleClose = () => {
        setModalState(false);
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button variant="contained" onClick={handleOpen}>
                            Gestione priorità
                        </Button>,
                        <Button variant="contained" onClick={redirectAction}>
                            Aggiungi
                        </Button>,
                    ]}
                    title={"LISTINI"}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                        fieldLabel="Listino"
                        fieldName="nomelistinoSearch"
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
                {modalState && (
                    <PrioritaModal
                        handleClose={handleClose}
                        modalState={modalState}
                    />
                )}
            </VaporPage>
        </>
    );
}
