import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";
const FilestypesCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [tipologiaOption, setTipologiaOption] = useState<any>([]);
    const [defaultFileTypes, setDefaultFileTypes] = useState<any>([]);
    const [formData, setFormData] = useState<any>([]);
    const [showHideButton, setShowHideButton] = useState<boolean>(false);
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        tipologia: yup.string().nullable(),
        tipologia_ref: yup.string().required(),
        isErasable: yup.string().nullable(),
        pwid: yup.number().nullable(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    const saveRequest = usePostCustom(route + "/save");
    const changeVisibilityRequest = usePostCustom(route + "/change-visibility");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setTipologiaOption(
                    response.data.defaultFileTypes.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                setDefaultFileTypes(
                    response.data.filesTypes.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("tipologia_ref", response.data.result.parent_id);
                    setValue("tipologia", "");
                    setValue("pwid", parseInt(response.data.result.pwid));
                    setValue("isErasable", response.data.result.isErasable);
                    setFormData({
                        uniqueid: uniqueId,
                        nome: response.data.result.nome,
                        parent_id: response.data.result.parent_id,
                        pwid: parseInt(response.data.result.pwid),
                        isErasable: response.data.result.isErasable,
                        visible: response.data.result.visible,
                    });
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                tipologia_ref: getValues("tipologia_ref"),
                tipologia: getValues("tipologia"),
            });
            navigate("/" + props.route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    const updateVisibiliy = async () => {
        await changeVisibilityRequest.doFetch(true, {
            uniqueid: uniqueId,
            visible: formData.visible == "1" ? 0 : 1,
        });
        navigate("/" + props.route);
    };
    const mergeData = async () => {
        await saveRequest.doFetch(true, {
            uniqueid: getValues("uniqueid") ?? "",
            nome: getValues("nome"),
            tipologia_ref: getValues("tipologia_ref"),
            merge: true,
        });
        navigate("/" + props.route);
    };
    const showFilesTypesButton = async (type: boolean) => {
        setShowHideButton(type);
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                                disabled={formData.pwid}
                            />

                            {formData.pwid > 0 && (
                                <label style={{ marginLeft: "0.5em" }}>
                                    Questa tipologia è abilitata a Polisweb.
                                </label>
                            )}
                            {formData.isErasable !== undefined &&
                                !formData.isErasable && (
                                    <label style={{ marginLeft: "0.5em" }}>
                                        Questa tipologia è legata ad almeno una
                                        pratica e non può essere eliminata.
                                    </label>
                                )}
                            {formData.isErasable !== undefined &&
                                !formData.isErasable && (
                                    <label style={{ marginLeft: "0.5em" }}>
                                        Questa tipologia è legata ad almeno una
                                        pratica e la sua visibilità non può
                                        essere modificata
                                    </label>
                                )}
                            {formData.length == 0 || !formData.pwid ? (
                                !showHideButton ? (
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="tipologia_ref"
                                        label="Tipologia di riferimento"
                                        type="select"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["tipologia_ref"] &&
                                            errors["tipologia_ref"][
                                                "message"
                                            ] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["tipologia_ref"] &&
                                            errors["tipologia_ref"]["message"]
                                        }
                                        options={tipologiaOption}
                                    />
                                ) : (
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="tipologia"
                                        label="Fondi con"
                                        type="select"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["tipologia"] &&
                                            errors["tipologia"]["message"] !==
                                                ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["tipologia"] &&
                                            errors["tipologia"]["message"]
                                        }
                                        options={defaultFileTypes}
                                    />
                                )
                            ) : (
                                ""
                            )}
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                        },
                    }}
                >
                    <Button
                        size={"small"}
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>

                    {(!formData.pwid || formData.pwid < 0) &&
                    formData.isErasable ? (
                        <>
                            <Button
                                size={"small"}
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                            {formData.pwid !== "" && formData.pwid > 0 ? (
                                <>
                                    {!showHideButton && (
                                        <Button
                                            size={"small"}
                                            variant="outlined"
                                            color="primary"
                                            type="button"
                                            onClick={() =>
                                                showFilesTypesButton(true)
                                            }
                                        >
                                            {t("Fondi con")}
                                        </Button>
                                    )}
                                    {showHideButton && (
                                        <Button
                                            size={"small"}
                                            variant="outlined"
                                            color="primary"
                                            type="button"
                                            onClick={() =>
                                                showFilesTypesButton(false)
                                            }
                                        >
                                            {t("Annulla fusione")}
                                        </Button>
                                    )}
                                </>
                            ) : (
                                ""
                            )}
                        </>
                    ) : (
                        ""
                    )}
                    {formData.length == 0 || !formData.pwid ? (
                        <>
                            {!showHideButton && (
                                <Button
                                    variant="contained"
                                    size={"small"}
                                    type="submit"
                                >
                                    {t("Conferma")}
                                </Button>
                            )}
                            {showHideButton && (
                                <Button
                                    size={"small"}
                                    variant="outlined"
                                    type="button"
                                    onClick={mergeData}
                                >
                                    {t("Fusione")}
                                </Button>
                            )}
                        </>
                    ) : (
                        ""
                    )}

                    {formData.isErasable !== undefined &&
                    formData.isErasable ? (
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={updateVisibiliy}
                        >
                            {formData.visible === "1"
                                ? t("Nascondi tipologia")
                                : t("Mostra tipologia")}
                        </Button>
                    ) : (
                        ""
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default FilestypesCreateUpdate;
