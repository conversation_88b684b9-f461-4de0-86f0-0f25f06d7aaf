import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";
const CitiesCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [nazioniList, setNazioniList] = useState<any>([]);

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        nazione_id: yup.string().required(),
        provincia: yup.string(),
        regione: yup.string(),
        cap: yup.string(),
        bic: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setNazioniList(
                    response.data.nazioni.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                setValue("nazione_id", "0");
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("nazione_id", response.data.result.nazione_id);
                    setValue("provincia", response.data.result.provincia);
                    setValue("regione", response.data.result.regione);
                    setValue("cap", response.data.result.cap);
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                nazione_id: getValues("nazione_id"),
                provincia: getValues("provincia"),
                regione: getValues("regione"),
                cap: getValues("cap"),
            });
            navigate("/" + props.route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="provincia"
                                label="Provincia"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["provincia"] &&
                                    errors["provincia"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["provincia"] &&
                                    errors["provincia"]["message"]
                                }
                            />
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="regione"
                                label="Regione"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["regione"] &&
                                    errors["regione"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["regione"] &&
                                    errors["regione"]["message"]
                                }
                            />
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="cap"
                                label="Cap"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["cap"] &&
                                    errors["cap"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["cap"] &&
                                    errors["cap"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nazione_id"
                                label="Nazione"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nazione_id"] &&
                                    errors["nazione_id"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nazione_id"] &&
                                    errors["nazione_id"]["message"]
                                }
                                options={nazioniList}
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default CitiesCreateUpdate;
