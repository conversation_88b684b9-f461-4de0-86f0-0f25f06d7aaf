import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack, InputAdornment } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const ExpenditureCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [natureOption, setNatureOption] = useState<any>([]);
    const [referencesOption, setReferencesOption] = useState<any>([]);
    const [hide, setHide] = useState<boolean>(true);
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        importo: yup.number(),
        iva: yup.number(),
        deducibilita_iva: yup.number(),
        tipo: yup.string(),
        natura_iva: yup.string(),
        riferimento_normativo: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        watch,
        setValue,
        getValues,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });

    let watch_tipo = watch("tipo");

    useEffect(() => {
        handleTipoChange();
    }, [watch_tipo]);

    const handleTipoChange = () => {
        if (getValues("tipo") === "3" || getValues("tipo") === "4")
            setHide(false);
        else {
            setHide(true);
            setValue("iva", 0);
        }
    };

    //api list
    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setNatureOption(
                    response.data.nature.map((row: any) => {
                        return {
                            label: row.codice + " - " + row.nome,
                            value: row.codice,
                        };
                    })
                );
                setReferencesOption(
                    response.data.references.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("importo", response.data.result.importo);
                    setValue("iva", response.data.result.iva);
                    setValue(
                        "deducibilita_iva",
                        response.data.result.deducibilita_iva
                    );
                    setValue("tipo", response.data.result.tipo);
                    setValue("natura_iva", response.data.result.natura_iva);
                    setValue(
                        "riferimento_normativo",
                        response.data.result.riferimento_normativo
                    );
                } else {
                    setValue("iva", response.data.coeffIVA);
                    setValue("tipo", "2");
                    setValue("importo", 0);
                    setValue("deducibilita_iva", 0);
                }
            } catch (error) {
                console.log("Request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                importo: getValues("importo") ?? 0,
                iva: getValues("iva"),
                deducibilita_iva: getValues("deducibilita_iva"),
                tipo: getValues("tipo"),
                natura_iva: getValues("natura_iva"),
                riferimento_normativo: getValues("riferimento_normativo") ?? 0,
            });
            navigate("/" + route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Descrizione"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="importo"
                                label="Importo"
                                type="number"
                                variant="outlined"
                                setValue={setValue}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            €
                                        </InputAdornment>
                                    ),
                                }}
                                error={
                                    errors &&
                                    errors["importo"] &&
                                    errors["importo"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["importo"] &&
                                    errors["importo"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="iva"
                                label="IVA"
                                type="number"
                                variant="outlined"
                                readOnly={!hide}
                                setValue={setValue}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="start">
                                            %
                                        </InputAdornment>
                                    ),
                                }}
                                error={
                                    errors &&
                                    errors["iva"] &&
                                    errors["iva"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["iva"] &&
                                    errors["iva"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="deducibilita_iva"
                                label="Deducibilità IVA"
                                type="number"
                                variant="outlined"
                                setValue={setValue}
                                InputProps={{
                                    endAdornment: (
                                        <InputAdornment position="start">
                                            %
                                        </InputAdornment>
                                    ),
                                }}
                                error={
                                    errors &&
                                    errors["deducibilita_iva"] &&
                                    errors["deducibilita_iva"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["deducibilita_iva"] &&
                                    errors["deducibilita_iva"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="tipo"
                                label="Preferita"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"]
                                }
                                options={[
                                    { label: "Spesa imponibile", value: "2" },
                                    {
                                        label: "Spesa esente art. 10",
                                        value: "3",
                                    },
                                    {
                                        label: "Spesa esclusa art.15",
                                        value: "4",
                                    },
                                ]}
                            />
                            {!hide ? (
                                <>
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="natura_iva"
                                        label="Natura IVA"
                                        type="select"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["natura_iva"] &&
                                            errors["natura_iva"]["message"] !==
                                                ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["natura_iva"] &&
                                            errors["natura_iva"]["message"]
                                        }
                                        options={natureOption}
                                    />

                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="riferimento_normativo"
                                        label="Riferimento Normativo"
                                        type="select"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["riferimento_normativo"] &&
                                            errors["riferimento_normativo"][
                                                "message"
                                            ] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["riferimento_normativo"] &&
                                            errors["riferimento_normativo"][
                                                "message"
                                            ]
                                        }
                                        options={referencesOption}
                                    />
                                </>
                            ) : (
                                ""
                            )}
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default ExpenditureCreateUpdate;
