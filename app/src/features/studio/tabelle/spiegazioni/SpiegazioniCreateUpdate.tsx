import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";

const SpiegazioniCreateUpdate = (props: ISimpleCreateUpdate) => {
    let { handleDelete, redirectBack, updateMountedRequest, uniqueId, route } =
        props;

    const { t } = useTranslation();
    const navigate = useNavigate();

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        description: yup.string().required(),
        merge: yup.string(),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });

    const onSubmit = async () => {
        // call
        await saveRequest.doFetch(true, {
            uniqueid: getValues("uniqueid") ?? "",
            description: getValues("description"),
        });
        navigate("/" + props.route);
    };
    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("description", response.data.result.description);
                }
            } catch (error) {
                console.log("error", error);
                return;
            }
        }

        initData();
    }, []);

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <FormInput
                        control={control}
                        name="description"
                        label="Descrizione"
                        type="text"
                        variant="outlined"
                        setValue={setValue}
                        error={
                            errors &&
                            errors["description"] &&
                            errors["description"]["message"] !== ""
                                ? true
                                : false
                        }
                        helperText={
                            errors &&
                            errors["description"] &&
                            errors["description"]["message"]
                        }
                    />
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default SpiegazioniCreateUpdate;
