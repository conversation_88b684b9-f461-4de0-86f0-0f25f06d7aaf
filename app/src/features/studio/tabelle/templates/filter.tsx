import {
    Box,
    Button,
    TextField,
    Select,
    MenuItem,
    InputLabel,
    FormControl,
    FormControlLabel,
    Checkbox,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData, mountedData } = props;
    const { t } = useTranslation();

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const onCheckboxChange = (target: any) => {
        setQuery((listItem: any) => ({
            ...listItem,
            mySearch: target.checked ? "on" : "",
            authorSearch: mountedData.authors[0].authorName,
            page: 0,
        }));
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={"Nome modello"}
                variant="outlined"
                name={"titleSearch"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={query["titleSearch"]}
            />

            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"authorSearch-label"}>Autori</InputLabel>
                <Select
                    id={"authorSearch"}
                    name={"authorSearch"}
                    labelId={"authorSearch-label"}
                    displayEmpty
                    onChange={(e: any) => onChangeFilterInputs(e)}
                    value={query["authorSearch"]}
                >
                    <MenuItem value="">{t("Tutte le autori")}</MenuItem>
                    {(mountedData.authors || []).map((row: any, i: any) => {
                        return (
                            <MenuItem
                                value={row.authorName}
                                key={row.authorName + "-" + i}
                            >
                                {row.authorName}
                            </MenuItem>
                        );
                    })}
                </Select>
            </FormControl>
            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"categorySearch-label"}>Categoria</InputLabel>
                <Select
                    id={"categorySearch"}
                    name={"categorySearch"}
                    displayEmpty
                    labelId={"categorySearch-label"}
                    onChange={(e: any) => onChangeFilterInputs(e)}
                    value={query["categorySearch"]}
                >
                    <MenuItem value="">{t("Tutte le categorie")}</MenuItem>
                    {(mountedData.modelcategories || []).map(
                        (row: any, i: any) => {
                            return (
                                <MenuItem value={row.id} key={row.id + "-" + i}>
                                    {row.nome}
                                </MenuItem>
                            );
                        }
                    )}
                </Select>
            </FormControl>
            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"extensionSearch-label"}>Estensione</InputLabel>
                <Select
                    id={"extensionSearch"}
                    name={"extensionSearch"}
                    labelId={"extensionSearch-label"}
                    displayEmpty
                    onChange={(e: any) => onChangeFilterInputs(e)}
                    value={query["extensionSearch"]}
                >
                    <MenuItem value="">{t("Tutte le estensioni")}</MenuItem>
                    <MenuItem value="docx">.docx</MenuItem>
                    <MenuItem value="odt">.odt</MenuItem>
                    <MenuItem value="pdf">.pdf</MenuItem>
                </Select>
            </FormControl>
            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <FormControlLabel
                    value="on"
                    control={<Checkbox checked={"on" === query["mySearch"]} />}
                    name={"mySearch"}
                    label={"Solo i miei modelli"}
                    labelPlacement="end"
                    onChange={({ target }: any) => {
                        onCheckboxChange(target);
                    }}
                />
            </FormControl>
            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
