import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack, Link, InputAdornment, TextField } from "@vapor/react-material";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { Link as RouterLink } from "react-router-dom";

import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import PageTitle from "../../../../custom-components/PageTitle";
import TagsAvailable from "./TagsAvailable";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const filter = createFilterOptions<any>();

const TemplateCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const [valueCategory, setValueCategory] = useState<any>(null);

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [categoryList, setCategoryList] = useState<any>([]);
    const [fileData, setFilesData] = useState<any>([]);
    const [hide, setHide] = useState<boolean>(true);
    const [mountedLoading, setMountedLoading] = useState<boolean>(true);

    const updateMountedRequest = useGetCustom(uniqueId !== undefined ? "templates/update?tUid=" + uniqueId : "templates/update");

    const validFileExtensions: any = {
        type: ["pdf", "docx", "oppure", "odt"]
    };

    const isValidFileType = (fileName: string, fileType: string) => {
        return validFileExtensions[fileType].indexOf(fileName.split(".").pop()) > -1;
    };
    const schema = yup.object().shape({
        tUid: yup.string(),
        title: yup.string().required(),
        fileName: yup
            .mixed()
            .required()
            .test("is-valid-type", 'Il modello DEVE essere un file con estensione "docx", "odt" oppure "pdf"', (value: any) => {
                return isValidFileType(value && value.toLowerCase(), "type");
            }),
        allUsers: yup.string(),
        allNetlex: yup.string(),
        onSale: yup.string(),
        category_id: yup.string().required(),
        price: yup.number()
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        watch
    } = useForm({
        resolver: yupResolver(schema)
    });
    const [open, setOpen] = useState(false);

    const watch_onSale = watch("onSale");

    const handleFile = (event: any) => {
        setFilesData(event.target.files);
    };
    useEffect(() => {
        if (getValues("onSale") == "on") {
            setHide(false);
        } else {
            setHide(true);
        }
    }, [watch_onSale]);

    //api list

    const saveRequest = usePostCustom("templates/save");

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await updateMountedRequest.doFetch();
                setMountedLoading(false);
                setCategoryList(
                    data.modelcategories.map((row: any) => {
                        return {
                            title: row.nome,
                            value: row.id
                        };
                    })
                );

                if (uniqueId !== undefined) {
                    const isExisting = data.modelcategories.filter((option: any) => data.result.category_id === option.id);

                    setValue("tUid", uniqueId);
                    setValue("title", data.result.title);
                    setValue("fileName", data.result.filename);
                    setValue("price", parseFloat(data.result.price));
                    setValue("category_id", data.result.category_id);
                    setValueCategory({
                        value: isExisting[0].id,
                        title: isExisting[0].nome
                    });
                    setValue("allUsers", data.result.all_users === "1" ? "on" : "");
                    setValue("allNetlex", data.result.all_netlex === "1" ? "on" : "");
                    setValue("onSale", data.result.on_sale === "1" ? "on" : "");
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const saveCategory = usePostCustom("modelcategories/remotesave?noTemplateVars=true");

    const saveNewCategory = async (value: string) => {
        let res = await saveCategory.doFetch(true, {
            insertItem: value
        });
        return res;
    };

    const downloadtemplate = useGetCustom("templates/gettemplate");

    const downloadContent = async () => {
        await downloadtemplate.doFetch(true, {
            tUid: uniqueId
        });
        // download file
    };

    const onSubmit = async () => {
        // call
        // upload file pending
        let data1: any = {
            tUid: getValues("tUid") ?? "",
            title: getValues("title"),
            fileName: fileData[0],
            category_id: getValues("category_id"),
            categoriaNome: "",
            price: getValues("price") ?? ""
        };
        if (getValues("allUsers") == "on") {
            data1.allUsers = getValues("allUsers");
        }
        if (getValues("allNetlex") == "on") {
            data1.allNetlex = getValues("allNetlex");
        }
        if (getValues("onSale") == "on") {
            data1.onSale = getValues("onSale");
        }

        const formData = new FormData();
        for (var key in data1) {
            var value = data1[key];
            formData.append(key, value);
        }
        await saveRequest.doFetch(true, formData);
        navigate("/templates");
    };

    const deleteRequest = usePostCustom(uniqueId !== undefined ? "templates/delete?tUid=" + uniqueId : "templates/delete");

    const redirectBack = () => {
        navigate("/templates");
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/templates");
    };

    const categoryOnchange = async (_event: any, newValue: any) => {
        if (newValue && newValue.value == undefined) {
            let { data }: any = await saveNewCategory(newValue.inputValue);
            setValueCategory({
                value: data.id,
                title: newValue.title
            });
            setValue("category_id", data.id);
        } else {
            setValueCategory(newValue);
            setValue("category_id", newValue?.value);
        }
    };

    return (
        <VaporPage>
            <PageTitle title={(uniqueId !== undefined ? "Modifica " : `Nuovo `) + "Modello"} pathToPrevPage={"/templates"} />
            <ConfirmModal open={open} handleDecline={handleDecline} handleAgree={handleAgree} decline={"No"} agree={"Yes, Delete"} confirmText={"Are you sure want to Delete"} title={"Delete Action"} />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250
                            }
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput style={{ width: 400 }} control={control} name="title" label="Nome modello" type="text" variant="outlined" setValue={setValue} error={errors && errors["title"] && errors["title"]["message"] !== "" ? true : false} helperText={errors && errors["title"] && errors["title"]["message"]} />
                                <CustomAutocomplete
                                    value={valueCategory}
                                    onChange={(event: any, newValue: any) => categoryOnchange(event, newValue)}
                                    filterOptions={(options: any, params: any) => {
                                        const filtered = filter(options, params);

                                        const { inputValue } = params;
                                        const isExisting = options.some((option: any) => inputValue === option.title);
                                        if (inputValue !== "" && !isExisting) {
                                            filtered.push({
                                                inputValue,
                                                title: `Add "${inputValue}"`
                                            });
                                        }

                                        return filtered;
                                    }}
                                    selectOnFocus
                                    clearOnBlur
                                    handleHomeEndKeys
                                    id="category_id"
                                    options={categoryList}
                                    getOptionLabel={(option: any) => {
                                        if (typeof option === "string") {
                                            return option;
                                        }
                                        if (option.inputValue) {
                                            return option.inputValue;
                                        }
                                        return option.title;
                                    }}
                                    renderOption={(props: any, option: any) => <li {...props}>{option.title}</li>}
                                    sx={{ width: 400 }}
                                    freeSolo
                                    renderInput={(params: any) => <TextField {...params} sx={{ width: 400 }} name="category_id" label="Categoria modello" />}
                                />
                                {uniqueId ? (
                                    <Link to="#" component={RouterLink} onClick={() => downloadContent()} style={{ textDecoration: "none" }}>
                                        {getValues("fileName") ? String(getValues("fileName")) : "Default Text"}
                                    </Link>
                                ) : (
                                    <FormInput style={{ width: 400 }} control={control} name="fileName" label="File (Max 40 MB)" type="file" variant="outlined" handleFile={handleFile} setValue={setValue} />
                                )}

                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="allUsers"
                                    label="Visibile agli utenti di questo studio"
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "(Si autorizzano gli altri utenti di questo studio legale a poter utilizzare, Senza modificare, Tale modello)",
                                            value: "on"
                                        }
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="allNetlex"
                                    label="Visibile agli utenti degli altri studi"
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "(Si autorizzano gli utenti degli altri studi legali a poter utilizzare, Senza modificare, Tale modello)",
                                            value: "on"
                                        }
                                    ]}
                                />
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="onSale"
                                    label="Condividi"
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    options={[
                                        {
                                            label: "(Si rende disponibile questo modello sul portale formulelegali.it)",
                                            value: "on"
                                        }
                                    ]}
                                />
                                {!hide ? (
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="price"
                                        label="Prezzo"
                                        type="number"
                                        variant="outlined"
                                        setValue={setValue}
                                        InputProps={{
                                            startAdornment: <InputAdornment position="start">€</InputAdornment>
                                        }}
                                    />
                                ) : (
                                    ""
                                )}
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100
                            }
                        }}
                    >
                        <Button variant="outlined" type="button" onClick={redirectBack}>
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId !== undefined && (
                            <>
                                <Button color="error" variant="outlined" onClick={handleDelete} type="button">
                                    {t("Delete")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>

            {uniqueId === undefined && !mountedLoading ? (
                <VaporPage.Section>
                    <h2>Creare un nuovo modello</h2>
                    <p>Realizzare un nuovo modello presuppone la creazione di un file, con estensione "docx", "odt" Oppure "pdf", In cui Inserire i tag sotto indicati. Un modello è utilizzabile solamente dall'utente che lo realizza, ma quest'ultimo ha facoltà di renderlo fruibile:</p>
                    <ul>
                        <li>A tutti gli utenti del suo studio legale;</li>
                        <li>Agli utenti di tutti gli altri studi legali che utilizzano Netlex. Questa opzione necessita dell'approvazione, da parte del reparto tecnico di Netlex, del modello redatto.</li>
                    </ul>
                    <p>I tag utilizzabili sono presenti nel seguente elenco e debbono essere utilizzati tali e quali, il reparto tecnico di Teamsystem è a disposizione tramite il Supporto Per qualsiasi segnalazione riguardante l'aggiunta di nuovi tag.</p>
                    <TagsAvailable />
                </VaporPage.Section>
            ) : (
                ""
            )}
        </VaporPage>
    );
};

export default TemplateCreateUpdate;
