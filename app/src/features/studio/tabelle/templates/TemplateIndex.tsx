import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import { useLocation } from "react-router-dom";
import { ArrowLeft } from "@vapor/react-icons";

export default function TemplateListIndex() {
    const navigate = useNavigate();

    const { t } = useTranslation();
    const { pathname } = useLocation();

    const isArchiveRoute = pathname.includes("archive");
    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter(isArchiveRoute);

    const { mountedData } = useMountedData();

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleClickCallback = (uniqueid: string) => {
        navigate("/templates/update/" + uniqueid);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="templates"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChange}
                onClickCallback={handleClickCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };
    const redirectAction = () => {
        navigate("/templates/update");
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    leftItems={[
                        <Button onClick={() => navigate("/index/tabelle")}>
                            <ArrowLeft />
                        </Button>,
                    ]}
                    rightItems={[
                        <Button variant="contained" onClick={redirectAction}>
                            {t("Nuova modello")}
                        </Button>,
                    ]}
                    title={"Gestione Modelli"}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
