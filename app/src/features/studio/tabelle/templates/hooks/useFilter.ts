import useGetCustom from "../../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback, useRef } from "react";
import { IDefaultQuery } from "../interfaces/simpleList.interface";
import { IList } from "../../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import { getTemplateGrid } from "../../../../../utilities/template/gridColumn";

export default function useFilter(isArchiveRoute: boolean) {
    const { t } = useTranslation();
    const filterRequest = useGetCustom("templates/list?noTemplateVars=true");

    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "title",
        sortOrder: "asc",
        titleSearch: "",
        authorSearch: "",
        categorySearch: "",
        extensionSearch: "",
    };

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterData = async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getTemplateGrid(t, isArchiveRoute),
            filterRequest.doFetch(true, cQuery),
        ]);
        if (response.data._redirectInfo === undefined) {
            const { currentPage, totalRows } = response.data;
            setList({
                ...list,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        } else {
            setList({
                ...list,
                rows: [],
                columns,
                totalRows: 0,
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        }
    };

    const isMountingRef = useRef(false);
    useEffect(() => {
        isMountingRef.current = true;
        filterData(query);
    }, []);
    const getSearchResults = useCallback(
        debounce((value: any) => {
            filterData(value);
        }, 500),
        []
    );

    useEffect(() => {
        if (!isMountingRef.current) {
            getSearchResults(query);
        } else {
            isMountingRef.current = false;
        }
    }, [query]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: filterRequest.loading,
    };
}
