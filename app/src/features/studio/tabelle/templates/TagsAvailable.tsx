import * as React from "react";
import { Box, Typography, Grid } from "@vapor/react-material";
import { Tabs, Tab } from "@vapor/react-extended";
import { useConfigs } from "../../../../store/ConfigStore";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            aria-labelledby={`simple-tab-${index}`}
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            role={"tabpanel"}
            {...other}
        >
            <Box p={3}>{children}</Box>
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
        tabindex: index,
    };
}

const TagsAvailable = () => {
    const { configs }: any = useConfigs();

    const [value, setValue] = React.useState(
        configs.config("app.contracts_bool")
            ? 0
            : configs.config("app.practices_bool")
            ? 3
            : configs.config("app.recupero_crediti_bool")
            ? 10
            : 13
    );
    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    return (
        <Grid container>
            <Grid item xs={12} md={2}>
                <Box
                    sx={{
                        flexGrow: 1,
                        display: "flex",
                        height: 500,
                        justifyContent: "center",
                    }}
                >
                    <Tabs
                        value={value}
                        onChange={handleChange}
                        size="extraSmall"
                        orientation="vertical"
                        variant="scrollable"
                    >
                        <Tab
                            label="TAG CONTRATTO"
                            {...a11yProps(0)}
                            sx={{
                                display: configs.config("app.contracts_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG CONTRATTO ANAGRAFICA"
                            {...a11yProps(1)}
                            sx={{
                                display: configs.config("app.contracts_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG CONTRATTO UTENTE"
                            {...a11yProps(2)}
                            sx={{
                                display: configs.config("app.contracts_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG AVVOCATO"
                            {...a11yProps(3)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG RESPONSABILE"
                            {...a11yProps(4)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG COLLABORATORE"
                            {...a11yProps(5)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG COINTESTATARIO"
                            {...a11yProps(6)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG AVVERSARIO"
                            {...a11yProps(7)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG CLIENTE"
                            {...a11yProps(8)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG CONTROPARTE"
                            {...a11yProps(9)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG RECUPERO CREDITI"
                            {...a11yProps(10)}
                            sx={{
                                display: configs.config(
                                    "app.recupero_crediti_bool"
                                )
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG ALTRO"
                            {...a11yProps(11)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG ESTERNO"
                            {...a11yProps(12)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab label="TAG DOCUMENTO" {...a11yProps(13)} />
                        <Tab
                            label="TAG DOMINUS"
                            {...a11yProps(14)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab
                            label="TAG PRATICA"
                            {...a11yProps(15)}
                            sx={{
                                display: configs.config("app.practices_bool")
                                    ? "block"
                                    : "none",
                            }}
                        />
                        <Tab label="TAG UTENTE" {...a11yProps(16)} />
                    </Tabs>
                </Box>
            </Grid>
            <Grid item xs={12} md={10}>
                {configs.config("app.contracts_bool") && (
                    <CustomTabPanel value={value} index={0}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.NOME] = Nome del contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.NUMERO] = Numero del contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_OGGI] = Data odierna in formato
                                GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_DECORRENZA] = Data di decorrenza
                                del contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_SCADENZA] = Data di scadenza del
                                contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.OGGETTO] = l'oggetto del contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.CATEGORIA] = la categoria del
                                contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_DI_RICHIESTA] = data di
                                richiesta del contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_PERFEZIONAMENTO_RICHIESTA] =
                                data di perfezionamento contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.DATA_RINNOVO] = la data in cui si
                                rinnova il contratto
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto.CAMPO_DINAMICO_X] = Campo dinamico (X
                                è da sostituire con l'id del campo dinamico che
                                si vuole stampare)
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.contracts_bool") && (
                    <CustomTabPanel value={value} index={1}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [TAG] = [NOME, COGNOME, CODICE_FISCALE,
                                DATA_NASCITA, PARTITA_IVA, EMAIL, PEC,
                                INDIRIZZO, MOBILE_1, MOBILE_2, FAX, CAP, CITTA,
                                TELEFONO_CASA, TELEFONO_UFFICIO, NOME_COGNOME,
                                PROVINCIA]
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto_anagrafica.RELAZIONE_X_TAG] = Stampa
                                dati per singola anagrafica, dove RELAZIONE
                                Rappresenta la relazione dell'anagrafica e X
                                Rappresenta il numero dell'anagrafica in ordine
                                di selezione (in fase di generazione del
                                documento da "Contratti-Modelli") e TAG
                                Rappresenta uno qualsiasi dei tag sopracitati
                                (esempio:
                                [contratto_anagrafica.FIRMATARIO_2_NOME]
                                Ritornerà il NOME della seconda anagrafica
                                inserita con relazione FIRMATARIO) N.B. se una
                                RELAZIONE contiene uno o piu spazi devono essere
                                sostituiti da _ Es: con il 'Relazione
                                secondario' Il tag diventera
                                _RUOLO_SECONDARIO_2_NOME
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.contracts_bool") && (
                    <CustomTabPanel value={value} index={2}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [TAG] = [EMAIL, PEC, CAP, CITTA, CODICE_FISCALE,
                                FAX, INDIRIZZO, MOBILE, NOME, COGNOME,
                                NOME_COGNOME, NOME_STUDIO, PARTITA_IVA,
                                PROVINCIA, TELEFONO]
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [contratto_utente.RELAZIONE_X_TAG] = Stampa dati
                                per singolo utente, dove RELAZIONE Rappresenta
                                la relazione dell'utente e X Rappresenta il
                                numero dell'utente in ordine di selezione (in
                                fase di generazione del documento da
                                "Contratti-Modelli") e TAG Rappresenta uno
                                qualsiasi dei tag sopracitati (esempio:
                                [contratto_utente.FIRMATARIO_2_NOME] Ritornerà
                                il NOME del secondo utente inserito con
                                relazione FIRMATARIO) N.B. se una RELAZIONE
                                contiene uno o piu spazi devono essere
                                sostituiti da _ Es: con il 'Relazione
                                secondario' Il tag diventera
                                _RUOLO_SECONDARIO_2_NOME
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}

                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={3}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.CAP] = Codice avviamento postale
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.CITTA] = Città dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.CODICE_FISCALE] = Codice fiscale
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.EMAIL] = Email dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.FAX] = Fax dello studio dell'avvocato
                                titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.INDIRIZZO] = Indirizzo dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.MOBILE] = Telefonino dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.NOME] = Nome dell'avvocato titolare
                                del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.NOME_COGNOME] = Nome e cognome
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.NOME_STUDIO] = Nome dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.PARTITA_IVA] = Partita iva
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.PEC] = Pec dell'avvocato titolare del
                                fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.PROVINCIA] = Provincia dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avvocato.TELEFONO] = Telefono dello studio
                                dell'avvocato titolare del fascicolo
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={4}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.CAP] = Codice avviamento postale
                                del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.CITTA] = Città dello studio del
                                responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.CODICE_FISCALE] = Codice fiscale
                                del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.EMAIL] = Email del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.FAX] = Fax dello studio del
                                responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.INDIRIZZO] = Indirizzo dello
                                studio del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.MOBILE] = Telefonino dello studio
                                del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.NOME] = Nome del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.NOME_COGNOME] = Nome e cognome del
                                responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.SIGLA] = Sigla del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.DATA_NASCITA] = Data di nascita
                                del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.NOME_STUDIO] = Nome dello studio
                                del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.PARTITA_IVA] = Partita iva del
                                responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.PROVINCIA] = Provincia dello
                                studio del responsabile
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [responsabile.TELEFONO] = Telefono dello studio
                                del responsabile
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={5}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.CAP] = Codice avviamento postale
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.CITTA] = Città dello studio del
                                collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.CODICE_FISCALE] = Codice fiscale
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.EMAIL] = Email del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.FAX] = Fax dello studio del
                                collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.INDIRIZZO] = Indirizzo dello
                                studio del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.MOBILE] = Telefonino dello studio
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.NOME] = Nome del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.NOME_COGNOME] = Nome e cognome
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.SIGLA] = Sigla del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.DATA_NASCITA] = Data di nascita
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.NOME_STUDIO] = Nome dello studio
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.PARTITA_IVA] = Partita iva del
                                collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.PROVINCIA] = Provincia dello
                                studio del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore.TELEFONO] = Telefono dello studio
                                del collaboratore
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [collaboratore<b>X</b>.<b>TAG</b>] = Stampa dati
                                per singolo collaboratore, dove <b>X</b>{" "}
                                Rappresenta il numero del collaboratore in
                                ordine di selezione (in fase di generazione del
                                documento da "Pratica-Modelli") e <b>TAG</b>{" "}
                                Rappresenta uno qualisiasi dei tag sopracitati
                                (esempio: [collaboratore2.NOME] Ritornerà il
                                NOME del secondo collaboratore inserito)
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={6}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.CAP] = Codice avviamento postale
                                del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.CITTA] = Citt dello studio del
                                cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.CODICE_FISCALE] = Codice fiscale
                                del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.EMAIL] = Email del
                                cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.FAX] = Fax dello studio del
                                cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.INDIRIZZO] = Indirizzo dello
                                studio del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.MOBILE] = Telefonino dello
                                studio del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.NOME] = Nome del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.NOME_COGNOME] = Nome e cognome
                                del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.SIGLA] = Sigla del
                                cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.DATA_NASCITA] = Data di nascita
                                del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.NOME_STUDIO] = Nome dello studio
                                del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.PARTITA_IVA] = Partita iva del
                                cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.PROVINCIA] = Provincia dello
                                studio del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario.TELEFONO] = Telefono dello
                                studio del cointestatario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cointestatario<b>X</b>.<b>TAG</b>] = Stampa
                                dati per singolo cointestatario, dove <b>X</b>{" "}
                                Rappresenta il numero del cointestatario in
                                ordine di selezione (in fase di generazione del
                                documento da "Pratica-Modelli") e <b>TAG</b>{" "}
                                Rappresenta uno qualisiasi dei tag sopracitati
                                (esempio: [cointestatario2.NOME] Ritornerà il
                                NOME del secondo cointestatario inserito)
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={7}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.CAP] = Codice avviamento postale
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.CITTA] = Città dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.CITTA_NASCITA] = Città di nascita
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.CODICE_FISCALE] = Codice fiscale
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.COMUNE] = Comune dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.DATA_NASCITA] = Data di nascita
                                dell'avvocato avversario in formato GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.DATAISCRIZIONE] = Data iscrizione
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.DELIBERATO] = Deliberato
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.EMAIL] = Email dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.FAX] = Fax dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.IBAN] = Iban dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.INDIRIZZO] = Indirizzo dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.MOBILE_1] = Telefonino dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.MOBILE_2] = Telefonino dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.NOME] = (Denominazione o Nome
                                completo) Dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.NOME_PEC] = Nome dell'avvocato
                                avversario - pec dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.NUMEROREA] = Numero dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.PARTITA_IVA] = Partita iva
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.PEC] = Pec dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.PROVINCIA] = Provincia dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.PROVINCIA_NASCITA] = Provincia di
                                nascita dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.SEZIONE] = Sezione dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.SOTTOSCRITTO] = Sottoscritto
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.TELEFONO_CASA] = Telefono
                                dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.TELEFONO_UFFICIO] = Telefono
                                dell'ufficio dell'avvocato avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.TIPO] = Tipologia avvocato
                                avversario (Es. persona fisica, persona
                                giuridica, ecc.)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.VALUTA] = Valuta dell'avvocato
                                avversario
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [avversario.VERSATO] = Versato dell'avvocato
                                avversario
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={8}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.CAP] = Codice avviamento postale del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.CITTA] = Città del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.CITTA_NASCITA] = Città di nascita del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.CODICE_ESTERNA] = Codice esterno del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.CODICE_FISCALE] = Codice fiscale del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.COMUNE] = Comune del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.DATA_NASCITA] = Data di nascita del
                                cliente in formato GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.DATAISCRIZIONE] = Data iscrizione del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.DELIBERATO] = Deliberato del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.EMAIL] = Email del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.FAX] = Fax del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IBAN] = Iban del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.INDIRIZZO] = Indirizzo del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.MOBILE_1] = Telefonino del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.MOBILE_2] = Telefonino del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.NOME] = (Denominazione o Nome completo)
                                Del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.NUMEROREA] = Numero rea del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.NOTA] = Nota del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.PARTITA_IVA] = Partita iva del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.PEC] = Pec del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.PROVINCIA] = Provincia del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.PROVINCIA_NASCITA] = Provincia di
                                nascita del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.SEZIONE] = Sezione del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.SOTTOSCRITTO] = Sottoscritto del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.TELEFONO_CASA] = Telefono del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.TELEFONO_UFFICIO] = Telefono
                                dell'ufficio del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.TIPO] = Tipologia cliente (Es. persona
                                fisica, persona giuridica, ecc.)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.VALUTA] = Valuta del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.VERSATO] = Versato del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_DESCRIZIONE] = Descrizione
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_INDIRIZZO] = Indirizzo
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_CIVICO] = Civico dell'immobile
                                del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_CITTA] = Città dell'immobile
                                del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_CAP] = CAP dell'immobile del
                                cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_PROVINCIA] = Provincia
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_NAZIONE] = Nazione
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_VALORE] = Valore dell'immobile
                                del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_TIPOCATASTO] = Tipo catasto
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_CLASSE] = Classe dell'immobile
                                del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_DIRITTO] = Diritto
                                dell'immobile del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente.IMMOBILE_QUOTA] = Quota dell'immobile
                                del cliente
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente<b>X</b>.<b>TAG</b>] = Stampa dati per
                                singolo cliente, dove <b>X</b> Rappresenta il
                                numero del cliente in ordine di selezione (in
                                fase di generazione del documento da
                                "Pratica-Modelli") e <b>TAG</b> Rappresenta uno
                                qualisiasi dei tag sopracitati (esempio:
                                [cliente2.NOME] Ritornerà il NOME del secondo
                                cliente inserito)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [cliente_<b>RUOLO</b>_<b>X</b>.<b>TAG</b>] =
                                Stampa dati per singolo cliente, dove{" "}
                                <b>RUOLO</b> Rappresenta il ruolo del cliente e{" "}
                                <b>X</b> Rappresenta il numero del cliente in
                                ordine di selezione (in fase di generazione del
                                documento da "Pratica-Modelli") e <b>TAG</b>{" "}
                                Rappresenta uno qualisiasi dei tag sopracitati
                                (esempio: [cliente_ATTORE_2.NOME] Ritornerà il
                                NOME del secondo cliente inserito con ruolo
                                ATTORE) N.B. se un RUOLO contine uno o piu spazi
                                devono essere sostituiti da _ Es: con il 'Ruolo
                                secondario' Il tag diventera
                                &lt;tipologiautente&gt;_RUOLO_SECONDARIO_2.NOME{" "}
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={9}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.CAP] = Codice avviamento postale
                                della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.CITTA] = Città della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.CITTA_NASCITA] = Città di nascita
                                della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.CODICE_ESTERNA] = Codice esterno
                                della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.CODICE_FISCALE] = Codice fiscale
                                della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.COMUNE] = Comune della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.DATA_NASCITA] = Data di nascita
                                della controparte in formato GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.DATAISCRIZIONE] = Data iscrizione
                                della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.DELIBERATO] = Deliberato della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.EMAIL] = Email della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.FAX] = Fax della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IBAN] = Iban della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.INDIRIZZO] = Indirizzo della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.MOBILE_1] = Telefonino della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.MOBILE_2] = Telefonino della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.NOME] = (Denominazione o Nome
                                completo) Della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.NUMEROREA] = Numero della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.NOTA] = Nota della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.PARTITA_IVA] = Partita iva della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.PEC] = Pec della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.PROVINCIA] = Provincia della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.PROVINCIA_NASCITA] = Provincia di
                                nascita della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.SEZIONE] = Sezione della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.SOTTOSCRITTO] = Sottoscritto della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.TELEFONO_CASA] = Telefono della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.TELEFONO_UFFICIO] = Telefono
                                dell'ufficio della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.TIPO] = Tipologia controparte (Es.
                                persona fisica, persona giuridica, ecc.)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.VALUTA] = Valuta della controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.VERSATO] = Versato della
                                controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_DESCRIZIONE] = Descrizione
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_INDIRIZZO] = Indirizzo
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_CIVICO] = Civico
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_CITTA] = Città
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_CAP] = CAP dell'immobile
                                del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_PROVINCIA] = Provincia
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_NAZIONE] = Nazione
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_VALORE] = Valore
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_TIPOCATASTO] = Tipo
                                catasto dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_CLASSE] = Classe
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_DIRITTO] = Diritto
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte.IMMOBILE_QUOTA] = Quota
                                dell'immobile del controparte
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte<b>X</b>.<b>TAG</b>] = Stampa dati
                                per singola controparte, dove <b>X</b>{" "}
                                Rappresenta il numero della controparte in
                                ordine di selezione (in fase di generazione del
                                documento da "Pratica-Modelli") e <b>TAG</b>{" "}
                                Rappresenta uno qualisiasi dei tag sopracitati
                                (esempio: [controparte3.IBAN] Ritornerà l'IBAN
                                della terza controparte inserita)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [controparte_<b>RUOLO</b>_<b>X</b>.<b>TAG</b>] =
                                Stampa dati per singola controparte, dove{" "}
                                <b>RUOLO</b> Rappresenta il ruolo della
                                controparte e <b>X</b> Rappresenta il numero
                                della controparte in ordine di selezione (in
                                fase di generazione del documento da
                                "Pratica-Modelli") e <b>TAG</b> Rappresenta uno
                                qualisiasi dei tag sopracitati (esempio:
                                [controparte_TERZO_3.IBAN] Ritornerà l'IBAN
                                della terza controparte inserita con ruolo
                                TERZO) N.B. se un RUOLO contine uno o piu spazi
                                devono essere sostituiti da _ Es: con il 'Ruolo
                                secondario' il tag diventera
                                &lt;tipologiautente&gt;_RUOLO_SECONDARIO_2.NOME{" "}
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.recupero_crediti_bool") && (
                    <CustomTabPanel value={value} index={10}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.DATA] = Campo data del
                                documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.DIRITTI] = Campo diritti del
                                documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.INTERESSI] = Campo interessi
                                del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.INTESTATARIO] = Campo
                                intestatario del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.ONERI_RIFLESSI] = Campo oneri
                                riflessi del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.ONORARI] = Campo onorari del
                                documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.PERC_ONERI_RIFLESSI] = Campo
                                percentuale oneri riflessi del documento di
                                recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.PERC_RITENUTA_ACCONTO] = Campo
                                percentuale ritenuta acconto del documento di
                                recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.PERC_SPESE_GENERALI] = Campo
                                percentuale spese generali del documento di
                                recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.RITENUTA_ACCONTO] = Campo
                                ritenuta acconto del documento di recupero
                                crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO] = Campo soggetto del
                                documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO_NOME] = Campo nome
                                soggetto del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO_CAP] = Campo cap
                                soggetto del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO_CITTA] = Campo citta
                                soggetto del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO_PROVINCIA] = Campo
                                provincia soggetto del documento di recupero
                                crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SOGGETTO_PEC] = Campo pec
                                soggetto del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SPESE_ESENTI] = Campo spese
                                esenti del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.SPESE_GENERALI] = Campo spese
                                generali del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.TOTALE_DA_RECUPERARE] = Totale
                                da recuperare del documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.TOTALE_ODI] = Totale ODI del
                                documento di recupero crediti
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [recuperocrediti.TOTALE_OS] = Totale OS del
                                documento di recupero crediti
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={11}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.CLAUSOLE.x] = Clausole Modelli (il valore
                                x è l'ordine cardinale corrispondente al numero
                                di occorrenza, IMPORTANTE il valore parte da 1),
                                es: se vengono inserite nel modello due clausule
                                per riferirsi all prima si userà
                                [altro.CLAUSOLE.1] Per riferirsi alla seconda
                                [altro.CLAUSOLE.2]
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.CAP] = Codice avviamento postale
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.CITTA] = Città dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.CODICE_FISCALE] = Codice fiscale
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.EMAIL] = Email dell'anagrafica sotto la
                                relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.PEC] = Pec dell'anagrafica sotto la
                                relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.FAX] = Fax dello studio dell'anagrafica
                                sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.INDIRIZZO] = Indirizzo dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.MOBILE] = Telefonino dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.NOME] = (Denominazione o Nome completo)
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.SIGLA] = Sigla dell'anagrafica sotto la
                                relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.DATA_NASCITA] = Data di nascita
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.NOME_STUDIO] = Nome dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.PARTITA_IVA] = Partita iva
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.PROVINCIA] = Provincia dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro.TELEFONO] = Telefono dello studio
                                dell'anagrafica sotto la relazione 'altro'
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro<b>X</b>.<b>TAG</b>] = Stampa dati per
                                singolo altro, dove <b>X</b> Rappresenta il
                                numero dell'anagrafica sotto la relazione
                                'altro' in ordine di selezione (in fase di
                                generazione del documento da "Pratica-Modelli")
                                e <b>TAG</b> Rappresenta uno qualisiasi dei tag
                                sopracitati (Esempio: [altro2.NOME] Ritornerà il
                                NOME della seconda anagrafica sotto la relazione
                                'altro' inserito)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [altro_<b>RUOLO</b>_<b>X</b>.<b>TAG</b>] =
                                Stampa dati per singolo altro, dove <b>RUOLO</b>{" "}
                                Rappresenta il ruolo del soggetto altro e{" "}
                                <b>X</b> Rappresenta il numero dell'anagrafica
                                sotto la relazione 'altro' in ordine di
                                selezione (in fase di generazione del documento
                                da "Pratica-Modelli") e <b>TAG</b> Rappresenta
                                uno qualisiasi dei tag sopracitati (esempio:
                                [altro2_RICORRENTE_.NOME] Ritornerà il NOME
                                della seconda anagrafica sotto la relazione
                                'altro' inserito con ruolo RICORRENTE) N.B. se
                                un RUOLO contine uno o piu spazi devono essere
                                sostituiti da _ Es: con il 'Ruolo secondario' il
                                tag diventera
                                &lt;tipologiautente&gt;_RUOLO_SECONDARIO_2.NOME{" "}
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={12}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.CAP] = Codice avviamento postale
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.CITTA] = Città dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.CITTA_NASCITA] = Città di nascita
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.CODICE_ESTERNA] = Codice esterno
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.CODICE_FISCALE] = Codice fiscale
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.COMUNE] = Comune dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.DATA_NASCITA] = Data di nascita
                                dell'utente esterno in formato GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.DATAISCRIZIONE] = Data iscrizione
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.DELIBERATO] = Deliberato dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.EMAIL] = Email dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.FAX] = Fax dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.IBAN] = Iban dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.INDIRIZZO] = Indirizzo dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.MOBILE_1] = Telefonino dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.MOBILE_2] = Telefonino dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.NOME] = (Denominazione o Nome completo)
                                Dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.NUMEROREA] = Numero rea dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.PARTITA_IVA] = Partita iva dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.PEC] = Pec dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.PROVINCIA] = Provincia dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.PROVINCIA_NASCITA] = Provincia di
                                nascita dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.SEZIONE] = Sezione dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.SOTTOSCRITTO] = Sottoscritto
                                dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.TELEFONO_CASA] = Telefono dell'utente
                                esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.TELEFONO_UFFICIO] = Telefono
                                dell'ufficio dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.TIPO] = Tipologia esterno (Es. persona
                                fisica, persona giuridica, ecc.)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.VALUTA] = Valuta dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno.VERSATO] = Versato dell'utente esterno
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno<b>X</b>.<b>TAG</b>] = Stampa dati per
                                singolo utente esterno, dove <b>X</b>{" "}
                                Rappresenta il numero dell'utente esterno in
                                ordine di selezione (in fase di generazione del
                                documento da "Pratica-Modelli") e <b>TAG</b>{" "}
                                Rappresenta uno qualisiasi dei tag sopracitati
                                (Esempio: [esterno1.COMUNE] Ritornerà il COMUNE
                                del primo utente esterno inserito)
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [esterno_<b>RUOLO</b>_<b>X</b>.<b>TAG</b>] =
                                Stampa dati per singolo soggetto esterno, dove{" "}
                                <b>RUOLO</b> Rappresenta il ruolo del soggetto
                                esterno e <b>X</b> Rappresenta il numero del
                                soggetto esterno in ordine di selezione (in fase
                                di generazione del documento da
                                "Pratica-Modelli") e <b>TAG</b> Rappresenta uno
                                qualisiasi dei tag sopracitati (esempio:
                                [esterno1_ATTORE_.COMUNE] Ritornerà il COMUNE
                                del primo utente esterno inserito con ruolo
                                ATTORE) N.B. se un RUOLO contine uno o piu spazi
                                devono essere sostituiti da _ Es: con il 'Ruolo
                                secondario' il tag diventera
                                &lt;tipologiautente&gt;_RUOLO_SECONDARIO_2.NOME{" "}
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                <CustomTabPanel value={value} index={13}>
                    <Grid container sx={{ fontSize: 13 }} spacing={1}>
                        <Grid item lg={4} md={5} sm={12} xs={12}>
                            <div>[documento;block=begin;sub1]</div>
                            <div>[documento;block=end]</div>
                            <div>[documento_sub1.NOME]</div>
                        </Grid>
                    </Grid>
                </CustomTabPanel>
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={14}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.CAP] = Codice avviamento postale del
                                dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.CITTA] = Città del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.CITTA_NASCITA] = Città di nascita del
                                dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.CODICE_FISCALE] = Codice fiscale del
                                dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.COMUNE] = Comune del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.DATA_NASCITA] = Data di nascita del
                                dominus in formato GG/MM/AAAA
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.DATAISCRIZIONE] = Data iscrizione del
                                dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.DELIBERATO] = Deliberato del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.EMAIL] = Email del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.FAX] = Fax del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.IBAN] = Iban del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.INDIRIZZO] = Indirizzo del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.MOBILE_1] = Telefonino del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.MOBILE_2] = Telefonino del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.NOME] = Nome del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.NUMEROREA] = Numero del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.PARTITA_IVA] = Partita iva del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.PEC] = Pec del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.PROVINCIA] = Provincia del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.PROVINCIA_NASCITA] = Provincia di
                                nascita del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.SEZIONE] = Sezione del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.SOTTOSCRITTO] = Sottoscritto del
                                dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.TELEFONO_CASA] = Telefono del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.TELEFONO_UFFICIO] = Telefono
                                dell'ufficio del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.VALUTA] = Valuta del dominus
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                [dominus.VERSATO] = Versato del dominus
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                {configs.config("app.practices_bool") && (
                    <CustomTabPanel value={value} index={15}>
                        <Grid container sx={{ fontSize: 13 }} spacing={1}>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                <b>Tag generali</b>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.ANNOTAZIONE] = Annotazione della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.ATTIVITA_PROSSIMA_UDIENZA] =
                                    Attività della prossima udienza della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.ATTIVITA_ULTIMA_UDIENZA] = Attività
                                    dell'ultima udienza della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.AUTORITA] = Autorità della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.AUTORITA_PROSSIMA_UDIENZA] =
                                    Autorità della prossima udienza della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.AUTORITA_ULTIMA_UDIENZA] = Autorità
                                    dell'ultima udienza della pratica
                                </Grid>

                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CATEGORIA] = Categoria della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CONTR_CAMPO_DINAMICO_X] = Campo
                                    dinamico dei contratti (X è da sostituire
                                    con l'id del campo dinamico della
                                    contrattualistica che si vuole stampare)
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CITTA] = Città della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CITTA_PROSSIMA_UDIENZA] = Città
                                    della prossima udienza della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CITTA_ULTIMA_UDIENZA] = Città
                                    dell'ultima udienza della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CODICE] = Codice della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.CODICE_ARCHIVIO] = Codice archivio
                                    della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.COLLABORATORI] = Collaboratori
                                    della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_DECRETO_INGIUNTIVO] = Data
                                    pubblicazione decreto ingiuntivo
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_ISCRIZIONE_RUOLO] = Data di
                                    iscrizione al ruolo{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_OGGI] = Data odierna in
                                    formato GG/MM/AAAA
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_PROSSIMA_UDIENZA] = Data, in
                                    formato GG/MM/AAAA, della prossima udienza
                                    della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_PROSSIMA_UDIENZA_EVASA] =
                                    Data, in formato GG/MM/AAAA, della prossima
                                    udienza della pratica evasa
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.SENTENZA] = Numero sentenza della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_SENTENZA] = Data pubblicazione
                                    sentenza
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_DELLA_SENT] = Data della
                                    Sentenza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_ULTIMA_UDIENZA] = Data, in
                                    formato GG/MM/AAAA, dell'ultima udienza
                                    della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_ULTIMA_UDIENZA_EVASA] = Data,
                                    in formato GG/MM/AAAA, dell'ultima udienza
                                    della pratica evasa
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_CHIUSURA] = Data, in formato
                                    GG/MM/AAAA, di chiurura della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_ARCHIVIO] = Data, in formato
                                    GG/MM/AAAA, di archiviazione della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DECRETO_INGIUNTIVO] = Numero
                                    decreto ingiuntivo della pratica in formato
                                    NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DESCRIZIONE] = Descrizione della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.EMISSIONE_DI] = Data emissione
                                    decreto ingiuntivo della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.FORMULA_ESECUTIVA] = Data formula
                                    esecutiva della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.GIUDICE] = Giudice della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.NOME] = Nome della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.NOTIFICA_DI] = Data notifica
                                    decreto ingiuntivo della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DECRETO_ESECUTORIETA] = Data di
                                    rilascio del decreto di esecutorietà della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.NOTIFICA_PRECETTO] = Data notifica
                                    precetto della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.OGGETTO] = Oggetto della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.ORA_PROSSIMA_UDIENZA] = Ora della
                                    prossima udienza della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.ORA_ULTIMA_UDIENZA] = Ora
                                    dell'ultima udienza della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.PROTOCOLLO_GENERALE] = Protocollo
                                    generale della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.STATO] = Stato della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RG] = R.G. della pratica in formato
                                    NUMERO/ANNO-SUBPROCEDIMENTO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RIFERIMENTO_TEMPORALE_UTC] = Data
                                    in formato AAAA-MM-GGTHH:MI:SSZ
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.SEZIONE] = Sezione della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.TIPOLOGIA] = Tipologia della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.VALORE] = Valore della pratica in
                                    euro
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.SEDE] = Sede della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.PALCHETTO] = Palchetto della
                                    pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.FALDONE] = Faldone della pratica
                                </Grid>
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                <b>Tag Amministrativo</b>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ATT_CIT] = Atto di
                                    Citazione{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_COS_SOS_CORR] = Costi
                                    sostenuti Corrisp.{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_COST_GIUD] = Costituzione
                                    in Giudizio{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DAT_DEP_SENT] = Data
                                    Deposito Sentenza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DATA_UD] = Data Udienza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_COS_CDS] = Dep. Costit.
                                    Cds{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_COS_TAR] = Dep. Costit.
                                    TAR{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_DED] = Deposito
                                    Deduzione{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_MEM] = Deposito Memoria{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_RIC] = Depos. Ricorso{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESEC_ORD] = Esecuz.
                                    Ordinanza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESEC_SENT] = Esecuz.
                                    Sentenza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESITO_CAUT] = Ordinanza
                                    Cautelare Esito{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESITO_SENT] = Data Sentenza
                                    Esito{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IMP] = Importo{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IMP_COND] = Importo Condan.{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IMPUGNATA] = Impugnata{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_INV_DEDU] = Invito a
                                    Deduzione{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IST_SOSP] = Istanza
                                    Sospensione{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ISTA_FISS] = Istanza
                                    Fissazione{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ISTA_FISS_RG] = Fissazione
                                    Numero R.G{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ISTA_PREL] = Istanza
                                    Prelievo{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ISTA_PREL_RG] = Prelievo
                                    Numero R.G{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_NOT_PREC] = Notif. Precetto{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_NOTIFICA] = Notifica{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ORD_CAU] = Ordinanza
                                    Cautelare{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ORD_CAU_RG] = Ordinanza
                                    Cautelare Numero R.G{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ORD_ISTR] = Ordinanza
                                    Istruttoria{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ORD_ISTR_RG] = Ordinanza
                                    Istruttoria Numero R.G{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_PRAT_ATT] = Pratica Attiva{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_RIC_RIS] = Richiesta
                                    Risarcimento{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ST_REC_CLI] = Stima
                                    recupero Cliente{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ST_REC_STU] = Stima
                                    recupero Studio{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_TER_IMP] = Termine Imp.{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_TPO_SPES] = Tipo Spese{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_TRA_SENT] = Trasm. Sentenza{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_NOTE_GIUD] = Note giudice{" "}
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGAPP] = R.G. della corte d'appello
                                    della pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGCASS] = R.G. della corte di
                                    cassazione della pratica in formato
                                    NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGGIP] = R.G.GIP della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGGUP] = R.G.GUP della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGNR] = R.G.N.R. della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGRIESAME] = R.G. per il riesame
                                    della pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGSIEP] = R.G.S.I.E.P. della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGSIUS] = R.G.S.I.U.S. della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGTRIB] = R.G. del tribunale della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.PUBBLICO_MINISTERO] = Nome del
                                    pubblico ministero della pratica
                                </Grid>
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                <b>Tag penali</b>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DATA_REATO] = Data reato in formato
                                    GG/MM/AAAA
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.REATO] = Reato della pratica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ALLE] = Alle
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_AUT_INTER] = Autorità
                                    Intervenute
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_CONDANNA] = Condanna
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_COST_COM] = Costituzione
                                    Comune
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DATA_PUB_SEN] = Data
                                    Pubblic. Sent.
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DATA_TRASM] = Data Trasmis.
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEL] = Del
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_D_MOTIVA] = Data Dep.
                                    Motivazione
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DEP_T_MOTIVA] = Termine
                                    Dep. Motivazione
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DI] = Di
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESITO] = Esito
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_FATT_AVV_A] = Fatto
                                    avvenuto a
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IMPUGNATA2] = Impugnata
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_N_SENT_REP] = N. Sent/N.
                                    Repert.
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_N_SINI] = N° Sinistro
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_NOTIFICA2] = Notifica
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_RESP_CP] = Resp. CP
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_RIS_DAN] = Risarcimento
                                    Danni
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_RISARC] = Risarcimento
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ST_REC_CLI2] = St. recupero
                                    Cliente
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_TERM_IMP] = Termine Impugn.
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGAPP] = R.G. della corte d'appello
                                    della pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGCASS] = R.G. della corte di
                                    cassazione della pratica in formato
                                    NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGGIP] = R.G.GIP della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGGUP] = R.G.GUP della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGNR] = R.G.N.R. della pratica in
                                    formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGRIESAME] = R.G. per il riesame
                                    della pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGSIEP] = R.G.S.I.E.P. della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGSIUS] = R.G.S.I.U.S. della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.RGTRIB] = R.G. del tribunale della
                                    pratica in formato NUMERO/ANNO
                                </Grid>
                            </Grid>
                            <Grid item lg={4} md={5} sm={12} xs={12}>
                                <b>Tag Conciliazione</b>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ALLE]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_AUT_INTER]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_CONSEGN]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DATA_FIN]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DATA_INI]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DELIMIT]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DI]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_DIREZIONE]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ENTE_PAGA]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESITO_CONCIL]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_ESITO_PARERE]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_FATT_AVV_A]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_GG]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IMP_MASSIMAL]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_IN_DATA]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_KM]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_MORTALE]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_N_SINI]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_NOTE]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_RESP_CP]
                                </Grid>
                                <Grid item lg={4} md={5} sm={12} xs={12}>
                                    [pratica.DT_GIUD_VIA_STRADA_P]
                                </Grid>
                            </Grid>
                        </Grid>
                    </CustomTabPanel>
                )}
                <CustomTabPanel value={value} index={16}>
                    <Grid container sx={{ fontSize: 13 }} spacing={1}>
                        <Grid item lg={12} md={12} sm={12} xs={12}>
                            <Typography>
                                <b>Logo</b>
                            </Typography>
                            <Grid item lg={12} md={12} sm={12} xs={12}>
                                <Typography>
                                    Per inserire il logo scaricare il{" "}
                                    <a href="/templates/getdocument">
                                        Segnaposto
                                    </a>
                                </Typography>
                            </Grid>
                            <Grid item lg={12} md={12} sm={12} xs={12}>
                                <Typography variant="subtitle1">
                                    <b>Per documenti "odt":</b>
                                </Typography>
                                <ul>
                                    <li>
                                        <Typography>
                                            Inserire il segnaposto come immagine
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Selezionare l'immagine e premere il
                                            tasto destro del mouse
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Scegliere la voce "Descrizione" e
                                            inserire nel campo "Descrizione" il
                                            seguente testo:
                                            [utente.CARTA_INTESTATA;ope=changepic;tagpos=inside;adjust=0%;unique=1]
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Confermare con il pulsante OK
                                        </Typography>
                                    </li>
                                </ul>
                            </Grid>
                            <Grid item lg={12} md={12} sm={12} xs={12}>
                                <Typography variant="subtitle1">
                                    <b>Per documenti "docx":</b>
                                </Typography>
                                <ul>
                                    <li>
                                        <Typography>
                                            Inserire il segnaposto come immagine
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Selezionare l'immagine e premere il
                                            tasto destro del mouse
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Scegliere la voce "Formato immagine"
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Selezionare "Layout e proprietà"
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>
                                            Scegliere "Testo alternativo" e
                                            inserire nel campo "Descrizione" il
                                            seguente testo:
                                            [utente.CARTA_INTESTATA;ope=changepic;tagpos=inside;adjust=50%;unique=1]
                                        </Typography>
                                    </li>
                                    <li>
                                        <Typography>Salvare il file</Typography>
                                    </li>
                                </ul>
                            </Grid>
                        </Grid>
                    </Grid>
                </CustomTabPanel>
            </Grid>
        </Grid>
    );
};
export default TagsAvailable;
