import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";
const DeadlinestandardCreateUpdate = (props: ISimpleCreateUpdate) => {
    let { handleDelete, redirectBack, updateMountedRequest, uniqueId, route } =
        props;

    const { t } = useTranslation();
    const navigate = useNavigate();

    const [deadlineStatuses, setDeadlineStatuses] = useState<any>([]);
    const [deadlineCategories, setDeadlineCategories] = useState<any>([]);

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        descrizione: yup.string().required(),
        status: yup.string().nullable(),
        category: yup.string().nullable(),
        importante: yup.string(),
        fatturabile: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });

    //api list

    const saveRequest = usePostCustom(route + "/save?noTemplateVars=true");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setDeadlineCategories(
                    response.data.deadlineCategories.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                setDeadlineStatuses(
                    response.data.deadlineStatuses.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("descrizione", response.data.result.descrizione);
                    setValue("status", response.data.result.status_id);
                    setValue("category", response.data.result.category_id);
                    setValue(
                        "importante",
                        response.data.result.importante === "1" ? "on" : ""
                    );
                    setValue(
                        "fatturabile",
                        response.data.result.fatturabile === "1" ? "on" : ""
                    );
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let data: any = {
            uniqueid: getValues("uniqueid") ?? "",
            descrizione: getValues("descrizione") ?? "",
            status: getValues("status") ?? "",
            category: getValues("category") ?? "",
            importante: getValues("importante") == "on" ? true : false,
            fatturabile: getValues("fatturabile") == "on" ? true : false,
        };
        let res: any = await saveRequest.doFetch(true, data);
        if (res.data.error) {
            alert(res.data.error);
        } else navigate("/" + props.route);
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component={"section"}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="descrizione"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["descrizione"] &&
                                    errors["descrizione"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["descrizione"] &&
                                    errors["descrizione"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="status"
                                label="Stato"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["status"] &&
                                    errors["status"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["status"] &&
                                    errors["status"]["message"]
                                }
                                options={deadlineStatuses}
                            />
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="category"
                                label="Categoria"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["category"] &&
                                    errors["category"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["category"] &&
                                    errors["category"]["message"]
                                }
                                options={deadlineCategories}
                            />
                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="importante"
                                label=""
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["importante"] &&
                                    errors["importante"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["importante"] &&
                                    errors["importante"]["message"]
                                }
                                options={[{ label: "Importante", value: "on" }]}
                            />
                            <FormInput
                                style={{ width: 400, marginLeft: "0.5em" }}
                                control={control}
                                name="fatturabile"
                                label=""
                                type="checkbox"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["fatturabile"] &&
                                    errors["fatturabile"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["fatturabile"] &&
                                    errors["fatturabile"]["message"]
                                }
                                options={[
                                    { label: "Fatturabile", value: "on" },
                                ]}
                            />
                        </Stack>
                    </div>
                </Box>
                <Box
                    component={"section"}
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default DeadlinestandardCreateUpdate;
