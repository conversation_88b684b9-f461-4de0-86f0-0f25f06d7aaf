import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import AttivitaudienzeCreateUpdate from "./attivitaudienze/AttivitaudienzeCreateUpdate";
import AuthoritaCreateUpdate from "./authorita/AuthoritaCreateUpdate";
import UnitMeasureCreateUpdate from "./unitmeasure/UnitMeasureCreateUpdate";
import DefaultCreateUpdate from "./DefaultCreateUpdate";
import CurrencyCreateUpdate from "./currencies/CurrencyCreateUpdate";
import BankCreateUpdate from "./bank/BankCreateUpdate";
import DocumentcategoriesCreateUpdate from "./documentcategories/DocumentcategoriesCreateUpdate";
import SpiegazioniCreateUpdate from "./spiegazioni/SpiegazioniCreateUpdate";
import CityCreateUpdate from "./city/CityCreateUpdate";
import ContactandaddressCreateUpdate from "./contactandaddress/ContactandaddressCreateUpdate";
import InoutCreateUpdate from "./inout/InoutCreateUpdate";
import PaymenttypeCreateUpdate from "./paymenttype/PaymenttypeCreateUpdate";
import AdditionalnoteCreateUpdate from "./additionalnote/AdditionalnoteCreateUpdate";
import AttivitaCreateUpdate from "./attivita/AttivitaCreateUpdate";
import FilesstatusCreateUpdate from "./filesstatus/FilesstatusCreateUpdate";
import SectionalCreateUpdate from "./sectional/SectionalCreateUpdate";
import SediCreateUpdate from "./sedi/SediCreateUpdate";
import ExpenditureCreateUpdate from "./expenditure/ExpenditureCreateUpdate";
import DescriptionCreateUpdate from "./descriptions/DescriptionCreateUpdate";
import TagCreateUpdate from "./tags/TagCreateUpdate";
import DeadlinestandardCreateUpdate from "./deadlinestandard/DeadlinestandardCreateUpdate";
import DeadlinestypesCreateUpdate from "./deadlinestypes/DeadlinestypesCreateUpdate";
import FilestypesCreateUpdate from "./filestypes/FilestypesCreateUpdate";
import PageTitle from "../../../custom-components/PageTitle";
import { TabelleTitle } from "../../../enums/TabelleTitle";
import ToastNotification from "../../../custom-components/ToastNotification";
const SimpleCreateUpdate = (props: any) => {
    let { route } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [open, setOpen] = useState(false);
    const [showErrorToast, setShowErrorToast] = useState(false);
    const { uniqueId } = useParams<any>();
    //api list
    const updateRequest = useGetCustom(
        uniqueId !== undefined
            ? route + "/update?uid=" + uniqueId
            : route + "/update"
    );

    const nomeValidation = useGetCustom(
        route + "/checkdata?noTemplateVars=true"
    );
    const tabelleTitle: any = TabelleTitle;
    const checkValidation = async (
        value: string,
        name?: string | undefined
    ): Promise<boolean> => {
        let res: any = await nomeValidation.doFetch(true, {
            key: name === undefined ? "nome" : name,
            value: value,
            uid: uniqueId !== undefined ? uniqueId : "",
        });
        return res.data == "";
    };

    const deleteRequest = usePostCustom(
        route + "/delete?noTemplateVars=true&uniqueid=" + uniqueId
    );

    const redirectBack = () => {
        navigate("/" + props.route);
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        let { data }: any = await deleteRequest.doFetch(true);
        if (data.responseCode == 401) {
            setShowErrorToast(true);
        } else {
            navigate("/" + props.route);
        }
    };

    const mountFormComponent = () => {
        switch (props.route) {
            case "attivitaudienze":
                return (
                    <AttivitaudienzeCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "authorities":
                return (
                    <AuthoritaCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "banks":
                return (
                    <BankCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "documentcategories":
                return (
                    <DocumentcategoriesCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "deadlinecategories":
            case "macrocategories":
            case "modelcategories":
            case "centroprofitto":
            case "liquidationpacumulative":
            case "liquidationreference":
            case "situazione":
            case "situazionecontabile":
            case "documentstatus":
            case "documentobject":
            case "deadlinestatus":
            case "liquidationpastatus":
            case "liquidationparegstatus":
            case "liquidationpadeedtype":
            case "instructors":
            case "objects":
            case "publicprosecutors":
            case "crimes":
            case "proceduralroles":
                return (
                    <DefaultCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "tags":
                return (
                    <TagCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "unitmeasure":
                return (
                    <UnitMeasureCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "spiegazioni":
                return (
                    <SpiegazioniCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "cities":
                return (
                    <CityCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "contactandaddress":
                return (
                    <ContactandaddressCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "descriptions":
                return (
                    <DescriptionCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "inout":
                return (
                    <InoutCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "paymenttypes":
                return (
                    <PaymenttypeCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "additionalnotes":
                return (
                    <AdditionalnoteCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "filesstatus":
                return (
                    <FilesstatusCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "sedi":
                return (
                    <SediCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "sectional":
                return (
                    <SectionalCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "expenditures":
                return (
                    <ExpenditureCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "attivita":
                return (
                    <AttivitaCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "deadlinestandard":
                return (
                    <DeadlinestandardCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "deadlinestypes":
                return (
                    <DeadlinestypesCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "filestypes":
                return (
                    <FilestypesCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );
            case "currencies":
                return (
                    <CurrencyCreateUpdate
                        redirectBack={redirectBack}
                        checkValidation={checkValidation}
                        handleDelete={handleDelete}
                        updateMountedRequest={updateRequest}
                        uniqueId={uniqueId}
                        route={route}
                    />
                );

            case "antirecregister":
                break;
            //direct insert from listing page
        }
    };
    return (
        <VaporPage>
            <PageTitle
                title={
                    (uniqueId !== undefined ? "Modifica " : `Nuovo `) +
                    tabelleTitle[route.toUpperCase()]
                }
                pathToPrevPage={"/" + route}
            />
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={"No"}
                agree={"Yes, Delete"}
                confirmText={"Are you sure want to Delete"}
                title={"Delete Action"}
            />
            {mountFormComponent()}
            <ToastNotification
                showNotification={showErrorToast}
                setShowNotification={setShowErrorToast}
                severity="error"
                text={t("L'elemento che si vuole eliminare è utilizzato.")}
            />
        </VaporPage>
    );
};

export default SimpleCreateUpdate;
