import { useTranslation } from "@1f/react-sdk";
import { Link } from "react-router-dom";

import List from "@vapor/react-material/List";
import ListItem from "@vapor/react-material/ListItem";
import ListItemIcon from "@vapor/react-material/ListItemIcon";
import ListItemText from "@vapor/react-material/ListItemText";
import SourceIcon from "@mui/icons-material/Source";
import VisibleWhenLogged from "../../../hoc-components/VisibleWhenLogged";

export const NavContent = () => {
    const { t } = useTranslation();

    return (
        <VisibleWhenLogged>
            <List>
                <ListItem>
                    <Link to="/index/tabelle/" style={{ textDecoration: "none", color: "inherit" }}>
                        <ListItemIcon>
                            <SourceIcon />
                        </ListItemIcon>
                        <ListItemText primary={t("tabelle")} />
                    </Link>
                </ListItem>
            </List>
        </VisibleWhenLogged>
    );
};
