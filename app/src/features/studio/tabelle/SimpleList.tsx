import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button, MenuItem, Menu } from "@vapor/react-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filters";
import useFilter from "./hooks/useFilter";
import { useLocation, useNavigate } from "react-router-dom";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import moment from "moment";
import { TabelleTitle } from "../../../enums/TabelleTitle";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import FiltersFileType from "./FiltersFileType";
import { ArrowLeft } from "@vapor/react-icons";

import { GridPaginationModel } from "@mui/x-data-grid-pro";

export default function SimpleList(props: { route: string }) {
    const navigate = useNavigate();

    const { t } = useTranslation();
    const tabelleTitle: any = TabelleTitle;
    const [deleteOpen, setDeleteOpen] = useState(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [deleteId, setDeleteId] = useState<string>("");

    const location = useLocation();

    const open = Boolean(anchorEl);

    const deleteRequest = useGetCustom(
        "antirecregister/delete-register?noTemplateVars=true"
    );
    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const downloadantirecregisterInset = useGetCustom(
        "antirecregister/download-register"
    );

    const downloadContent = async (id: string) => {
        let { data }: any = await downloadantirecregisterInset.doFetch(true, {
            registerId: id,
        });
        if (data._redirectInfo.is_redirect) {
            window.open(data._redirectInfo.url);
        }
    };

    const deleteAntirecregister = async (id: string) => {
        setDeleteOpen(!deleteOpen);
        setDeleteId(id);
    };

    const handleDecline = () => {
        setDeleteOpen(false);
    };

    const handleAgree = async () => {
        setDeleteOpen(false);
        await deleteRequest.doFetch(true, {
            registerId: deleteId,
        });
        filterData(query);
    };
    const {
        defaultQuery,
        query,
        setQuery,
        list,
        filterData,
        loading,
        fieldName,
        fieldLabel,
    } = useFilter({
        route: props.route,
        downloadContent: downloadContent,
        deleteAntirecregister: deleteAntirecregister,
    });
    const [selectedRows] = useState<any[]>([]);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({ ...query, page: model.page, pageSize: model.pageSize });
    };

    const handleClickCallback = (uniqueid: string) => {
        if (props.route !== "antirecregister")
            navigate("/" + props.route + "/update/" + uniqueid);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        if (props.route === "deadlinestandard")
            return (
                <CustomDataGrid
                    name="deadlinestandard"
                    setQuery={setQuery}
                    columns={list.columns}
                    data={list.rows}
                    page={list.page}
                    totalRows={list.totalRows}
                    pageSize={list.pageSize}
                    loading={loading}
                    selectableRows
                    query={query}
                    onPageChangeCallback={onPageChangeCallback}
                    onClickCallback={handleClickCallback}
                    disableColumnResize={true}
                    disableColumnReorder={true}
                />
            );
        else
            return (
                <CustomDataGrid
                    name="deadlinestandard"
                    setQuery={setQuery}
                    columns={list.columns}
                    data={list.rows}
                    page={list.page}
                    totalRows={list.totalRows}
                    pageSize={list.pageSize}
                    loading={loading}
                    query={query}
                    onPageChangeCallback={onPageChangeCallback}
                    onClickCallback={handleClickCallback}
                    disableColumnResize={true}
                    disableColumnReorder={true}
                />
            );
    };

    const saveantirecregisterInset = useGetCustom(
        "antirecregister/create-register?year=" +
            moment().format("YYYY") +
            "&noTemplateVars=true"
    );

    const antirecregisterInset = async () => {
        //direct insert here
        //currentYear
        let response = await saveantirecregisterInset.doFetch(true);
        if (response !== "") {
            filterData(query);
        }
        handleClose();
    };
    const redirectAction = () => {
        if (props.route !== "antirecregister")
            navigate("/" + props.route + "/update");
        else antirecregisterInset();
    };
    const deleteAction = () => {
        console.log("deleteAction");
    };

    return (
        <>
            <ConfirmModal
                open={deleteOpen}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={"No"}
                agree={"Yes, Delete"}
                confirmText={"Are you sure want to Delete"}
                title={"Delete Action"}
            />
            <VaporPage>
                <VaporHeaderBar
                    leftItems={[
                        <Button
                            onClick={() =>
                                navigate(
                                    location.state !== null
                                        ? location.state.from
                                        : "/index/tabelle"
                                )
                            }
                        >
                            <ArrowLeft />
                        </Button>,
                    ]}
                    rightItems={
                        props.route !== "antirecregister" ? (
                            [
                                <Button
                                    variant="contained"
                                    onClick={redirectAction}
                                >
                                    Nuova{" "}
                                    {tabelleTitle[
                                        props.route.toUpperCase()
                                    ].toLowerCase()}
                                </Button>,
                                selectedRows.length > 0 && (
                                    <Button
                                        color="error"
                                        variant="contained"
                                        onClick={deleteAction}
                                    >
                                        {t("Elimina")}
                                    </Button>
                                ),
                            ]
                        ) : (
                            <div>
                                <Button
                                    id="demo-customized-button"
                                    aria-controls={
                                        open
                                            ? "demo-customized-menu"
                                            : undefined
                                    }
                                    aria-haspopup="true"
                                    aria-expanded={open ? "true" : undefined}
                                    variant="contained"
                                    disableElevation
                                    onClick={handleClick}
                                    endIcon={<KeyboardArrowDownIcon />}
                                >
                                    {t("Crea registro")}
                                </Button>
                                <Menu
                                    id="demo-customized-menu"
                                    MenuListProps={{
                                        "aria-labelledby":
                                            "demo-customized-button",
                                    }}
                                    anchorEl={anchorEl}
                                    open={open}
                                    onClose={handleClose}
                                >
                                    <MenuItem
                                        onClick={redirectAction}
                                        disableRipple
                                    >
                                        {moment().format("YYYY")}
                                    </MenuItem>
                                </Menu>
                            </div>
                        )
                    }
                    title={tabelleTitle[props.route.toUpperCase()]}
                />
                <VaporPage.Section>
                    {props.route == "filestypes" ? (
                        <FiltersFileType
                            defaultQuery={defaultQuery}
                            query={query}
                            setQuery={setQuery}
                            filterData={filterData}
                            fieldLabel={fieldLabel}
                            fieldname={fieldName}
                        />
                    ) : (
                        <Filters
                            defaultQuery={defaultQuery}
                            query={query}
                            setQuery={setQuery}
                            filterData={filterData}
                            fieldLabel={fieldLabel}
                            fieldname={fieldName}
                        />
                    )}
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
