import {
    Box,
    TextField,
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";
export default function FiltersFileType(props: IFilterProps) {
    const { query, setQuery, filterData, fieldname, fieldLabel } = props;

    const { t } = useTranslation();
    const clearAll = () => {
        setQuery((listItem: any) => ({
            ...listItem,
            searchField: "",
            visibility: "-1",
            page: 0,
        }));
    };
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        searchWithDebounce(e);
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={fieldLabel !== undefined ? fieldLabel : "Nome"}
                variant="outlined"
                name={fieldname !== undefined ? fieldname : "searchField"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={
                    query[fieldname !== undefined ? fieldname : "searchField"]
                }
            />

            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id="select-label"></InputLabel>
                <Select
                    labelId="select-label"
                    onChange={(e: any) => onChangeFilterInputs(e)}
                    name="visibility"
                    value={query["visibility"]}
                >
                    <MenuItem value="-1">{t("Tutte")}</MenuItem>
                    <MenuItem value="1">{t("Visibili")}</MenuItem>
                    <MenuItem value="0">{t("Nascoste")}</MenuItem>
                </Select>
            </FormControl>

            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => clearAll()}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
