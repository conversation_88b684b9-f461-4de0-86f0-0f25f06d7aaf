import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ISimpleCreateUpdate from "../../../../models/ISimpleCreateUpdate";
import { useConfigs } from "../../../../store/ConfigStore";
const BankCreateUpdate = (props: ISimpleCreateUpdate) => {
    let {
        handleDelete,
        checkValidation,
        redirectBack,
        updateMountedRequest,
        uniqueId,
        route,
    } = props;

    const { configs }: any = useConfigs();

    const { t } = useTranslation();
    const navigate = useNavigate();

    const schema = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        tipo: yup.string().required(),
        iban: yup.string(),
        abi: yup.string(),
        cab: yup.string(),
        bic: yup.string(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        watch,
        setError,
    } = useForm({
        resolver: yupResolver(schema),
    });

    const tipoWatch = watch("tipo");

    //api list

    const saveRequest = usePostCustom(route + "/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setValue("tipo", "0");
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("nome", response.data.result.nome);
                    setValue("tipo", response.data.result.tipo);
                    setValue("iban", response.data.result.datipagamento.iban);
                    setValue("abi", response.data.result.datipagamento.abi);
                    setValue("cab", response.data.result.datipagamento.cab);
                    setValue("bic", response.data.result.datipagamento.bic);
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let validate = await checkValidation(getValues("nome"));
        if (validate) {
            await saveRequest.doFetch(true, {
                uniqueid: getValues("uniqueid") ?? "",
                nome: getValues("nome"),
                tipo: getValues("tipo"),
                iban: getValues("iban"),
                abi: getValues("abi"),
                cab: getValues("cab"),
                bic: getValues("bic"),
            });
            navigate("/" + props.route);
        } else {
            setError("nome", {
                type: "custom",
                message: t("Nome già utilizzato"),
            });
        }
    };

    return (
        <VaporPage.Section>
            <form onSubmit={handleSubmit(onSubmit)}>
                <Box
                    component="section"
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 250,
                        },
                    }}
                >
                    <div>
                        <Stack direction="column" gap={2}>
                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="nome"
                                label="Nome"
                                type="text"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["nome"] &&
                                    errors["nome"]["message"]
                                }
                            />

                            <FormInput
                                style={{ width: 400 }}
                                control={control}
                                name="tipo"
                                label="Tipo"
                                type="select"
                                variant="outlined"
                                setValue={setValue}
                                error={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"] !== ""
                                        ? true
                                        : false
                                }
                                helperText={
                                    errors &&
                                    errors["tipo"] &&
                                    errors["tipo"]["message"]
                                }
                                options={[
                                    { label: "Banca", value: "0" },
                                    { label: "Entrate", value: "1" },
                                ]}
                            />
                            {configs.config("app.payment_data_bool") &&
                            tipoWatch === "0" ? (
                                <>
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="iban"
                                        label="IBAN"
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["iban"] &&
                                            errors["iban"]["message"] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["iban"] &&
                                            errors["iban"]["message"]
                                        }
                                    />
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="abi"
                                        label="ABI"
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["abi"] &&
                                            errors["abi"]["message"] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["abi"] &&
                                            errors["abi"]["message"]
                                        }
                                    />
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="cab"
                                        label="CAB"
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["cab"] &&
                                            errors["cab"]["message"] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["cab"] &&
                                            errors["cab"]["message"]
                                        }
                                    />
                                    <FormInput
                                        style={{ width: 400 }}
                                        control={control}
                                        name="bic"
                                        label="BIC"
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["bic"] &&
                                            errors["bic"]["message"] !== ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["bic"] &&
                                            errors["bic"]["message"]
                                        }
                                    />
                                </>
                            ) : (
                                ""
                            )}
                        </Stack>
                    </div>
                </Box>
                <Box
                    component="section"
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 100,
                        },
                    }}
                >
                    <Button
                        variant="outlined"
                        type="button"
                        onClick={redirectBack}
                    >
                        {t("Annulla")}
                    </Button>
                    <Button variant="contained" type="submit">
                        {t("Conferma")}
                    </Button>

                    {uniqueId !== undefined && (
                        <>
                            <Button
                                color="error"
                                variant="outlined"
                                onClick={handleDelete}
                                type="button"
                            >
                                {t("Delete")}
                            </Button>
                        </>
                    )}
                </Box>
            </form>
        </VaporPage.Section>
    );
};

export default BankCreateUpdate;
