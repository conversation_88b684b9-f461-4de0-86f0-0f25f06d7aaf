import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

export default function useMountedData(props: any) {
    const mountedRequest = useGetCustom(props.route);
    const [mountedData, setMountedData] = useState<any>([]);

    useEffect(() => {
        async function fetchData() {
            const { data }: any = await mountedRequest.doFetch(true);
            setMountedData(data);
        }

        fetchData();
    }, []);

    return { mountedData };
}
