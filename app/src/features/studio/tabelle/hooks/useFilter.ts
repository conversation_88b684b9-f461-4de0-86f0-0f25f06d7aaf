import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback, useRef } from "react";
import { IDefaultQuery } from "../interfaces/simpleList.interface";
import { IList } from "../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import { getSimpleGrid } from "../../../../utilities/tabelle/gridColumn";

export default function useFilter(props: {
    route: string;
    downloadContent: any;
    deleteAntirecregister: any;
}) {
    const { t } = useTranslation();
    const filterRequest = useGetCustom(
        props.route + "/list?noTemplateVars=true"
    );

    const [gridSettings, setGridSettings] = useState<any>();
    const [sortParam, setSortParam] = useState<string>("nome");

    const [fieldLabel, setFieldLabel] = useState<string>("Nome");
    const [fieldName, setFieldName] = useState<string>("searchField");
    const defaultQuery: IDefaultQuery =
        props.route == "filestypes"
            ? {
                  page: 0,
                  pageSize: 10,
                  sortColumn: sortParam,
                  sortOrder: "desc",
                  searchField: "",
                  visibility: "-1",
              }
            : {
                  page: 0,
                  pageSize: 10,
                  sortColumn: sortParam,
                  sortOrder: "desc",
                  searchField: "",
              };

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const getGridData = async () => {
        switch (props.route) {
            case "attivitaudienze":
                setFieldLabel("Attività");
                setSortParam("attivita");
                // setQuery({ ...query, sortColumn: "attivita" });
                return {
                    columnNames: ["Attività", "Nome"],
                    columnKeys: ["attivita", "nome"],
                    columnWidths: ["60%", "20%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "authorities":
                setFieldLabel("Nome");
                setSortParam("citta");
                // setQuery({ ...query, sortColumn: "citta" });
                return {
                    columnNames: [
                        t("Nome"),
                        t("Citta"),
                        t("Polisweb"),
                        t("Preferita"),
                        t("Udienze"),
                    ],
                    columnKeys: [
                        "nome",
                        "citta",
                        "pwid",
                        "preferita",
                        "udienze",
                    ],
                    columnWidths: ["45%", "25%", "15%", "15%", "10%"],
                    cellTemplates: [null, null, , null, null, null],
                    sortable: [true, true, true, true],
                };
            case "banks":
                // custom filter
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Tipo")],
                    columnKeys: ["nome", "tipo"],
                    columnWidths: ["50%", "50%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "documentcategories":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome"),
                        t("Gruppo"),
                        t("Visibile a tutti"),
                        t("Utente"),
                    ],
                    columnKeys: ["nome", "gruppo", "downloadable", "immessoda"],
                    columnWidths: ["40%", "20%", "20%", "20%"],
                    cellTemplates: [null, null],
                    sortable: [true, true, false, false],
                };
            case "deadlinecategories":
            case "macrocategories":
            case "modelcategories":
            case "centroprofitto":
            case "liquidationpacumulative":
            case "liquidationreference":
            case "situazione":
            case "situazionecontabile":
            case "documentstatus":
            case "documentobject":
            case "deadlinestatus":
            case "liquidationpastatus":
            case "liquidationparegstatus":
            case "liquidationpadeedtype":
            case "unitmeasure":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Utente")],
                    columnKeys: ["nome", "immessoda"],
                    columnWidths: ["60%", "40%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "instructors":
            case "objects":
            case "publicprosecutors":
            case "crimes":
            case "proceduralroles":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: ["Nome"],
                    columnKeys: ["nome"],
                    columnWidths: ["100%"],
                    cellTemplates: [null],
                    sortable: [true],
                };
            case "tags":
                setFieldLabel("Nome");
                setSortParam("description");
                // setQuery({ ...query, sortColumn: "description" });
                return {
                    columnNames: ["Nome"],
                    columnKeys: ["description"],
                    columnWidths: ["100%"],
                    cellTemplates: [null],
                    sortable: [true],
                };
            case "spiegazioni":
                setFieldLabel("Spiegazione");
                setSortParam("description");
                // setQuery({ ...query, sortColumn: "description" });
                return {
                    columnNames: [t("Descrizione")],
                    columnKeys: ["description"],
                    columnWidths: ["100%"],
                    cellTemplates: [null],
                    sortable: [true],
                };
            case "cities":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome"),
                        t("Provincia"),
                        t("CAP"),
                        t("Regione"),
                    ],
                    columnKeys: ["nome", "provincia", "cap", "regione"],
                    columnWidths: ["40%", "20%", "20%", "35%"],
                    cellTemplates: [null, null, null, null],
                    sortable: [true, false, false, false],
                };
            case "contactandaddress":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Tipo")],
                    columnKeys: ["nome", "relazione"],
                    cellTemplates: [null, null],
                    columnWidths: ["60%", "40%"],
                    sortable: [true, true],
                };
            case "descriptions":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Tipo"), t("Descrizione")],
                    columnKeys: ["nome", "tipo", "descrizione"],
                    cellTemplates: [null, null, null],
                    columnWidths: ["40%", "20%", "40%"],
                    sortable: [true, true, true],
                };
            case "inout":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome"),
                        t("Codice tipologia"),
                        t("Utente"),
                    ],
                    columnKeys: ["nome", "registerCode", "immessoda"],
                    columnWidths: ["40%", "30%", "30%"],
                    cellTemplates: [null, null, null],
                    sortable: [true, true],
                };
            case "paymenttypes":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Cassa o banca")],
                    columnKeys: ["nome", "nomeBanca"],
                    columnWidths: ["50%", "50%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "additionalnotes":
                setFieldLabel("Ricerca");
                setSortParam("title");
                // setQuery({ ...query, sortColumn: "title" });
                return {
                    columnNames: ["Titolo", "Descrizione", ""],
                    columnKeys: ["title", "description", "is_default"],
                    cellTemplates: [null, null, null],
                    columnWidths: ["20%", null, "14px"],
                    sortable: [true, true],
                };
            case "filesstatus":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Id"),
                        t("Nome"),
                        t("Pratica Rinviabile"),
                        t("Tipologia"),
                    ],
                    columnKeys: ["id", "nome", "segnalaritardo", "type"],
                    columnWidths: ["5%", "50%", "20%", "%25%"],
                    cellTemplates: [null, null],
                    sortable: [true, true, true, true],
                };
            case "sedi":
                setFieldLabel("Sede");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome Sede"),
                        t("Indirizzo"),
                        t("Città"),
                        t("Telefono"),
                    ],
                    columnKeys: ["nome", "indirizzo", "citta", "telefono"],
                    columnWidths: ["40%", "30%", "20%", "10%"],
                    cellTemplates: [null, null, null, null],
                    sortable: [true, true, false],
                };
            case "sectional":
                setFieldLabel("Sezionale");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome"),
                        t("Tipo"),
                        t("Anteprima"),
                        t("Immesso il"),
                    ],
                    columnKeys: ["nome", "tipo", "valore", "immessoil"],
                    columnWidths: ["25%", "25%", "25%", "25%"],
                    cellTemplates: [null, null, null, null],
                    sortable: [true, true, true, true],
                };
            case "expenditures":
                setFieldLabel("Descrizione");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Descrizione"),
                        t("Tipo"),
                        t("Importo"),
                        "IVA",
                        t("Deducibilità iva"),
                    ],
                    columnKeys: [
                        "nome",
                        "tipo",
                        "importo",
                        "iva",
                        "deducibilita_iva",
                    ],
                    columnWidths: ["30%", "20%", "15%", "15%", "20%"],
                    sortable: [true, true, true, true, true],
                    cellTemplates: [null, null, null, null, null],
                };
            case "attivita":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [t("Nome"), t("Durata"), t("Attivita")],
                    columnKeys: ["nome", "durata", "attivita"],
                    columnWidths: ["35%", "15%", "50%"],
                    cellTemplates: [null, null],
                    sortable: [true, false],
                };
            case "deadlinestandard":
                setFieldLabel("Descrizione");
                // setQuery({ ...query, sortColumn: "descrizione" });
                setSortParam("descrizione");
                return {
                    columnNames: [
                        t("Descrizione"),
                        t("Stato"),
                        t("Categoria"),
                        t("Importante"),
                        t("Fatturabile"),
                    ],
                    columnKeys: [
                        "descrizione",
                        "status",
                        "category",
                        "importante",
                        "fatturabile",
                    ],
                    cellTemplates: [null, null, null, null, null],
                    columnWidths: ["30%", "20%", "20%", "10%", "10%"],
                    sortable: [true, false],
                };
            case "deadlinestypes":
                setFieldLabel("Nome");
                setSortParam("nome");
                return {
                    columnNames: [
                        t("Nome"),
                        t("Tipologia"),
                        t("Colore nel calendario"),
                    ],
                    columnKeys: ["nome", "tipo", "color"],
                    columnWidths: ["50%", "30%", "20%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "filestypes":
                // custom filter
                setFieldLabel("Nome");
                setSortParam("nome");
                // setQuery({ ...query, sortColumn: "nome", sortOrder: "asc" });
                return {
                    columnNames: [
                        t("Id"),
                        t("Nome"),
                        t("Polisweb"),
                        t("Tipologia"),
                        t("Visibile"),
                    ],
                    columnKeys: ["id", "nome", "pwid", "type", "visible"],
                    columnWidths: ["5%", "40%", "20%", "20%", "15%"],
                    cellTemplates: [null, null],
                    sortable: [true, true, true, true],
                };
            case "currencies":
                setFieldLabel("Valuta");
                setSortParam("nome");
                return {
                    columnNames: [t("Valuta"), t("Cambio")],
                    columnKeys: ["nome", "cambio"],
                    columnWidths: ["40%", "30%"],
                    cellTemplates: [null, null],
                    sortable: [true, true],
                };
            case "antirecregister":
                // custom column
                setFieldLabel("Data");
                setFieldName("registerDateSearch");
                // setQuery({ ...query, sortColumn: "id" });
                setSortParam("id");
                return {
                    columnNames: [
                        t("Data creazione"),
                        t("Anno riferimento"),
                        "Download",
                        "Delete",
                    ],
                    columnKeys: ["created_at", "year", "", ""],
                    columnWidths: ["25%", "25%", "25%", "25%"],
                    cellTemplates: [null, null, null, null],
                    sortable: [true, true, false, false],
                };
            default:
                return {
                    columnNames: [],
                    columnKeys: [],
                    columnWidths: [],
                    cellTemplates: [],
                    sortable: [],
                };
        }
    };

    const isMountingRef = useRef(false);

    useEffect(() => {
        isMountingRef.current = true;
        filterData(query);
    }, []);
    const getSearchResults = useCallback(
        debounce((value: any) => {
            filterData(value);
        }, 500),
        []
    );

    useEffect(() => {
        if (!isMountingRef.current) {
            getSearchResults(query);
        } else {
            isMountingRef.current = false;
        }
    }, [query]);

    const filterData = async (query?: IDefaultQuery) => {
        let customgridSettings: any = {};
        if (gridSettings === undefined) {
            customgridSettings = await getGridData();
            setGridSettings(customgridSettings);
        } else {
            customgridSettings = gridSettings;
        }
        if (Object.keys(customgridSettings).length > 0) {
            let cQuery: any = query;
            cQuery[fieldName] = cQuery[fieldName] ?? "";
            cQuery.sortColumn = query?.sortColumn;

            if (
                props.route == "antirecregister" &&
                cQuery["registerDateSearch"] !== undefined &&
                cQuery["registerDateSearch"] !== ""
            ) {
                const formattedDate = cQuery[
                    "registerDateSearch"
                ].toLocaleDateString("en-GB", {
                    year: "numeric",
                    month: "2-digit",
                    day: "2-digit",
                });
                cQuery.registerDateSearch = formattedDate;
            }

            const [columns, response]: any = await Promise.all([
                getSimpleGrid(
                    customgridSettings,
                    t,
                    props.route,
                    props.downloadContent
                ),
                filterRequest.doFetch(true, cQuery),
            ]);
            if (response.data._redirectInfo === undefined) {
                const { currentPage, totalRows } = response.data;
                setList({
                    ...list,
                    rows: currentPage,
                    columns,
                    totalRows: parseInt(totalRows),
                    page: cQuery?.page,
                    pageSize: cQuery?.pageSize,
                });
            } else {
                setList({
                    ...list,
                    rows: [],
                    columns,
                    totalRows: 0,
                    page: cQuery?.page,
                    pageSize: cQuery?.pageSize,
                });
            }
        }
    };

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        setSortParam,
        filterData,
        loading: filterRequest.loading,
        fieldLabel,
        fieldName,
    };
}
