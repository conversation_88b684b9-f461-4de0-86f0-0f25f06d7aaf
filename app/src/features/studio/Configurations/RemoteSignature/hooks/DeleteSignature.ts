import { useEffect } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";

export const useDeleteSignature = (uniqueid: string | undefined, remove: boolean) => {
    const { data, doFetch, loading, hasLoaded } = usePostCustom(
        "remotesign/delete?noTemplateVars=true"
    );

    useEffect(() => {
        if (remove) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid, remove]);
    return { data, loading, hasLoaded };
};
