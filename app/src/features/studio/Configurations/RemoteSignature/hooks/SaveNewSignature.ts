import { useEffect } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";
import { ISignatureData } from "../../../../../interfaces/RemoteSignature.interface";

export const useSaveNewSignature = (
    {
        active,
        certificate,
        mobile,
        passwd,
        passwdConfirm,
        provider,
        uname,
        uniqueid,
    }: ISignatureData,
    save: boolean
) => {
    const { data, loading, hasLoaded, doFetch } =
        usePostCustom("/remotesign/save");

    useEffect(() => {
        if (save) {
            doFetch(true, {
                active: active,
                certificate: certificate,
                mobile: mobile,
                passwd: passwd,
                passwdConfirm: passwdConfirm,
                provider: provider,
                uname: uname,
                uniqueid: uniqueid,
            });
        }
    }, [
        active,
        certificate,
        mobile,
        passwd,
        passwdConfirm,
        provider,
        uname,
        uniqueid,
        save,
    ]);
    return { data, loading, hasLoaded };
};
