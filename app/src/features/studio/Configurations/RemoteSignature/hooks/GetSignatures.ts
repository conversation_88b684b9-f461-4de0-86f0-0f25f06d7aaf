import { useEffect } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import {
    SignatureListProps,
    SignaturesResponse,
} from "../../../../../interfaces/RemoteSignature.interface";

export const useGetSignatures = ({
    lawyerSearch,
    page,
    pageSize,
    sortColumn,
    sortOrder,
}: SignatureListProps): {
    data: SignaturesResponse;
    loading: boolean;
    hasLoaded: boolean;
    error: any;
} => {
    const { data, doFetch, loading, hasLoaded, error } = useGetCustom(
        "remotesign/list?noTemplateVars=true"
    );

    useEffect(() => {
        doFetch(true, {
            lawyerSearch: lawyerSearch,
            page: page,
            pageSize: pageSize,
            sortColumn: sortColumn,
            sortOrder: sortOrder,
        });
    }, [lawyerSearch, page, pageSize, sortColumn, sortOrder]);

    return { data, loading, hasLoaded, error };
};
