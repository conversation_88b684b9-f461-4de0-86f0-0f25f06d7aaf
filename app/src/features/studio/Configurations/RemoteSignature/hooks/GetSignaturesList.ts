import { useEffect } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";

export const useGetSignatureList = (): {
    data: any;
    loading: boolean;
    hasLoaded: boolean;
    error: any;
} => {
    const { data, doFetch, loading, hasLoaded, error } = useGetCustom(
        "remotesign/remotesign"
    );

    useEffect(() => {
        doFetch(true);
    }, []);

    return { data, loading, hasLoaded, error };
};
