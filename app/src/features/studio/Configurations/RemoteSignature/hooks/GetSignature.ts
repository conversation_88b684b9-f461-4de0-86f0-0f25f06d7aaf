import { useEffect } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import {
    IGetSignatureProps,
    IGetSignatureResponse,
} from "../../../../../interfaces/RemoteSignature.interface";

export const useGetSignature = ({
    uniqueId,
}: IGetSignatureProps): {
    data: IGetSignatureResponse;
    loading: boolean;
    hasLoaded: boolean;
    error: any;
} => {
    const { data, doFetch, loading, hasLoaded, error } = useGetCustom(
        "remotesign/getrowdata?noTemplateVars=true"
    );


    useEffect(() => {
        doFetch(true, { uniqueId: uniqueId });
    }, [uniqueId]);

    return { data, loading, hasLoaded, error };
};
