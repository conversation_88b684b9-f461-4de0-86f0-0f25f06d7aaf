import { RemoteSignature } from "./RemoteSignature";
import { RemoteSignatureCreateUpdate } from "./SignatureCreateUpdate";

export const remoteSignature = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/remotesign/remotesign",
            element: <RemoteSignature/>,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/remotesign/remotesign/update/:id?",
            element: <RemoteSignatureCreateUpdate/>,
        },
    },
];
