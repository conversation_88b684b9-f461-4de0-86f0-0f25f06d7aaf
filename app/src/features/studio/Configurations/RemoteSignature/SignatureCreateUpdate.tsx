import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate, useParams } from "react-router-dom";
import { useGetSignature } from "./hooks/GetSignature";
import { useEffect, useMemo, useState } from "react";
import { Toolbar } from "@vapor/react-custom";
import { Button, Stack } from "@vapor/react-material";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useSaveNewSignature } from "./hooks/SaveNewSignature";
import { useDeleteSignature } from "./hooks/DeleteSignature";
import { SignatureForm } from "./SignatureForm";
import { useGetSignatureList } from "./hooks/GetSignaturesList";

export const RemoteSignatureCreateUpdate = () => {
    const { t } = useTranslation();
    const { id } = useParams();
    const navigate = useNavigate();

    const [confirmDelete, setConfirmDelete] = useState(false);
    const [deleteSignature, setDeleteSignature] = useState(false);
    const [certificates, setCertificates] = useState([]);
    const [saveSignature, setSaveSIgnature] = useState(false);
    const [showErrors, setShowErrors] = useState(false);

    const [signature, setSignature] = useState({
        uniqueid: "",
        provider: "2",
        mobile: "",
        uname: "",
        certificate: "",
        passwd: "",
        passwdConfirm: "",
        active: "",
    });

    const validations = useMemo(
        () => ({
            requireUsername:
                signature.provider === "1" ? signature.uname === "" : false,

            requirePassword: signature.passwd === "",
            requirePasswordConfirm:
                signature.passwd !== "" &&
                signature.passwdConfirm !== signature.passwd,
            requireCertificate:
                signature.provider === "2"
                    ? signature.certificate === ""
                    : false,
        }),
        [
            signature.provider,
            signature.uname,
            signature.passwd,
            signature.passwdConfirm,
            signature.certificate,
        ]
    );

    const handleSubmit = () => {
        setShowErrors(true);

        if (
            !validations.requirePassword &&
            !validations.requireCertificate &&
            !validations.requireUsername &&
            !validations.requirePasswordConfirm
        ) {
            setSaveSIgnature(true);
        }
    };

    const signaturesListResponse = useGetSignatureList();

    useEffect(() => {
        if (signaturesListResponse.hasLoaded) {
            setCertificates(signaturesListResponse.data.certificatesList);
        }
    }, [signaturesListResponse.hasLoaded]);

    const handleInputChange = (fieldName: any) => (event: any) => {
        const target = event.target;
        let value: string;

        if (target.type === "checkbox") {
            value = target.checked ? "1" : "";
        } else {
            value = target.value;
        }

        setSignature({
            ...signature,
            [fieldName]: value,
        });
    };

    const getSignatureResponse = useGetSignature({ uniqueId: id });

    useEffect(() => {
        if (getSignatureResponse.hasLoaded && id) {
            setSignature({
                uniqueid: getSignatureResponse.data.form.uniqueid,
                provider: getSignatureResponse.data.form.provider,
                mobile: getSignatureResponse.data.form.mobile,
                uname: getSignatureResponse.data.form.uname,
                certificate: getSignatureResponse.data.form.certificate,
                passwd: "",
                passwdConfirm: "",
                active: getSignatureResponse.data.form.active,
            });
        }
    }, [getSignatureResponse.hasLoaded]);

    const SaveSignatureResponse = useSaveNewSignature(
        {
            uniqueid: signature.uniqueid,
            provider: signature.provider,
            mobile: signature.mobile,
            uname: signature.uname,
            certificate: signature.certificate,
            passwd: signature.passwd,
            passwdConfirm: signature.passwdConfirm,
            active: signature.active,
        },
        saveSignature
    );

    const deleteSignatureResponse = useDeleteSignature(id, deleteSignature);

    useEffect(() => {
        if (
            deleteSignatureResponse.hasLoaded ||
            SaveSignatureResponse.hasLoaded
        ) {
            navigate("/remotesign/remotesign");
        }
    }, [deleteSignatureResponse.hasLoaded, SaveSignatureResponse.hasLoaded]);

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack direction="row" gap={1}>
                            <Button
                                variant="outlined"
                                onClick={() =>
                                    navigate("/remotesign/remotesign")
                                }
                            >
                                {t("Annulla")}
                            </Button>
                            {id && (
                                <Button
                                    variant="outlined"
                                    color="error"
                                    onClick={() => setConfirmDelete(true)}
                                >
                                    {t("Elimina")}
                                </Button>
                            )}
                            <Button variant="outlined" onClick={handleSubmit}>
                                {t("Conferma")}
                            </Button>
                        </Stack>
                    }
                />
            }
        >
            <PageTitle
                title={t("CONFIGURAZIONE FIRMA REMOTA")}
                pathToPrevPage="/remotesign/remotesign"
            />
            <VaporPage.Section>
                <ConfirmModal
                    open={confirmDelete}
                    title={t("Eliminare configurazione?")}
                    confirmText={t(
                        "Eliminare definitivamente la configurazione per la firma remota selezionata?"
                    )}
                    agree={t("Si")}
                    decline={t("No")}
                    handleAgree={() => setDeleteSignature(true)}
                    handleDecline={() => setConfirmDelete(false)}
                    colorConfirmButton="error"
                />
            </VaporPage.Section>
            <VaporPage.Section>
                {getSignatureResponse.hasLoaded && (
                    <SignatureForm
                        showErrors={showErrors}
                        validations={validations}
                        certificates={certificates}
                        signature={signature}
                        handleInputChange={handleInputChange}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
