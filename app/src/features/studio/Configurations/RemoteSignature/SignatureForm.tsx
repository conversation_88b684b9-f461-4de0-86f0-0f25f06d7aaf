import {
    Stack,
    Select,
    InputLabel,
    MenuItem,
    FormControlLabel,
    Checkbox,
    TextField,
    FormControl,
    FormHelperText,
} from "@vapor/react-material";
import type { ISignatureForm } from "../../../../interfaces/RemoteSignature.interface";
import { useTranslation } from "@1f/react-sdk";

export const SignatureForm = ({
    signature,
    handleInputChange,
    certificates,
    validations,
    showErrors,
}: ISignatureForm) => {
    const { t } = useTranslation();

    const certificateStatus = (value: string) => {
        return value === "2"
            ? "(Attivo)"
            : value === "4"
            ? "(Revocato)"
            : value === "6"
            ? "(Sospeso)"
            : "";
    };

    return (
        <Stack
            gap={2}
            maxWidth={300}
            alignItems="start"
            justifyContent="start"
            justifyItems="start"
        >
            <FormControl>
                <InputLabel>{t("Fornitore del certificato.")}</InputLabel>
                <Select
                    value={signature.provider || "2"}
                    label={t("Fornitore del certificato.")}
                    sx={{ minWidth: 300 }}
                    onChange={handleInputChange("provider")}
                >
                    <MenuItem value="1">{t("INFOCERT")}</MenuItem>
                    <MenuItem value="2">{t("NETLEX")}</MenuItem>
                </Select>
            </FormControl>
            {signature.provider === "1" ? (
                <>
                    <TextField
                        label={t("Telefono del certificato.")}
                        value={signature.mobile}
                        onChange={handleInputChange("mobile")}
                    />
                    <TextField
                        label={t("Alias del certificato")}
                        value={signature.uname}
                        onChange={handleInputChange("uname")}
                        error={showErrors && validations.requireUsername}
                        helperText={
                            showErrors &&
                            validations.requireUsername &&
                            t("Username obbligatorio")
                        }
                    />
                </>
            ) : (
                <FormControl>
                    <InputLabel>{t("Certificato")}</InputLabel>
                    <Select
                        onChange={handleInputChange("certificate")}
                        sx={{ minWidth: 300 }}
                        value={signature.certificate}
                        error={showErrors && validations.requireCertificate}
                    >
                        {certificates.map((certificate) => (
                            <MenuItem
                                value={certificate.uniqueid}
                                key={certificate.uniqueid}
                            >{`${certificate.fiscal_code} ${
                                certificate.mobile
                            } ${certificateStatus(
                                certificate.status
                            )}`}</MenuItem>
                        ))}
                    </Select>
                    {showErrors && validations.requireCertificate && (
                        <FormHelperText
                            error={showErrors && validations.requireCertificate}
                        >
                            {t("Certificato obbligatorio")}
                        </FormHelperText>
                    )}
                </FormControl>
            )}

            <TextField
                type="password"
                label={t("PIN")}
                value={signature.passwd}
                onChange={handleInputChange("passwd")}
                error={showErrors && validations.requirePassword}
                helperText={
                    showErrors &&
                    validations.requirePassword &&
                    t("Password obbligatoria")
                }
            />
            <TextField
                type="password"
                label={t("Conferma PIN")}
                value={signature.passwdConfirm}
                onChange={handleInputChange("passwdConfirm")}
                error={validations.requirePasswordConfirm}
                helperText={
                    validations.requirePasswordConfirm &&
                    t("Conferma NON corrispondente")
                }
            />

            <FormControlLabel
                control={<Checkbox checked={signature.active === "1"} />}
                label={t("Attiva")}
                onChange={handleInputChange("active")}
                labelPlacement="top"
            />
        </Stack>
    );
};
