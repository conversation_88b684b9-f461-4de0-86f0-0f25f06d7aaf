import { StatusBadge, VaporPage } from "@vapor/react-custom";
import { useGetSignatures } from "./hooks/GetSignatures";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faCircleCheck,
    faCircleMinus,
} from "@fortawesome/pro-regular-svg-icons";
import PageTitle from "../../../../custom-components/PageTitle";
import type { SignatureListProps } from "../../../../interfaces/RemoteSignature.interface";
import { useGetSignatureList } from "./hooks/GetSignaturesList";
import { FormGroup, InputLabel, MenuItem, Select } from "@vapor/react-material";
import type { SelectChangeEvent } from "@mui/material/Select";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";

export const RemoteSignature = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const [signatureSearchParams, setSignatureSearchParams] =
        useState<SignatureListProps>({
            lawyerSearch: -1,
            page: 0,
            pageSize: 10,
            sortColumn: "lawyer",
            sortOrder: "asc",
        });

    const [lawyers, setLawyers] = useState<any[]>([
        {
            id: "-1",
            nome: "Tutti gli avvocati",
        },
    ]);

    const getSignaturesResponse = useGetSignatures(signatureSearchParams);

    const signaturesListResponse = useGetSignatureList();

    useEffect(() => {
        if (signaturesListResponse.hasLoaded) {
            setLawyers([
                {
                    id: "-1",
                    nome: "Tutti gli avvocati",
                },
                ...signaturesListResponse.data.lawyers,
            ]);
        }
    }, [signaturesListResponse.hasLoaded]);

    const columns = [
        {
            field: "lawyer",
            headerName: t("Avvocato"),
            width: 200,
            sortable: true,
        },
        {
            field: "provider",
            headerName: t("Fornitore del certificato"),
            width: 200,
            sortable: false,
            renderCell: (params: any) =>
                params.value === "2" ? "NETLEX" : "INFOCERT",
        },
        {
            field: "mobile",
            headerName: t("Telefono del certificato"),
            width: 300,
            sortable: false,
        },
        {
            field: "username",
            headerName: t("Alias del certificato"),
            width: 350,
            sortable: false,
        },
        {
            field: "certificate_id",
            headerName: t("Id del certificato"),
            width: 200,
            sortable: false,
        },
        {
            field: "active",
            headerName: t("Attiva per firma remota"),
            width: 150,
            sortable: false,
            renderCell: (params: any) =>
                params.value === "0" ? (
                    <StatusBadge
                        icon={<FontAwesomeIcon icon={faCircleMinus} />}
                        bgColor="gray"
                        label="No"
                    />
                ) : (
                    <StatusBadge
                        icon={<FontAwesomeIcon icon={faCircleCheck} />}
                        bgColor="green"
                        label="Si"
                    />
                ),
        },
        {
            field: "modifiable",
            headerName: t("Modificabile"),
            width: 150,
            sortable: false,
            renderCell: (params: any) =>
                params.value === "0" ? (
                    <StatusBadge
                        icon={<FontAwesomeIcon icon={faCircleMinus} />}
                        bgColor="gray"
                        label="No"
                    />
                ) : (
                    <StatusBadge
                        icon={<FontAwesomeIcon icon={faCircleCheck} />}
                        bgColor="green"
                        label="Si"
                    />
                ),
        },
    ];

    const handleLawyerChange = (event: SelectChangeEvent<any>) => {
        setSignatureSearchParams({
            ...signatureSearchParams,
            lawyerSearch: event.target.value,
        });
    };

    const handleNavigateToSignature = (uniqueid: string) => {
        navigate(`/remotesign/remotesign/update/${uniqueid}`);
    };

    return (
        <VaporPage>
            <PageTitle
                title="CONFIGURAZIONE FIRMA REMOTA"
                showBackButton={false}
                actionButtons={[
                    {
                        label: "Configura firma remota",
                        onclick: () => {
                            handleNavigateToSignature("");
                        },
                    },
                ]}
            />
            <VaporPage.Section>
                <FormGroup sx={{ maxWidth: 300 }}>
                    <InputLabel>{t("Tutti gli avvocati")}</InputLabel>
                    <Select
                        value={signatureSearchParams.lawyerSearch}
                        onChange={handleLawyerChange}
                    >
                        {lawyers.map((lawyer: any) => (
                            <MenuItem value={lawyer.id}>{lawyer.nome}</MenuItem>
                        ))}
                    </Select>
                </FormGroup>
            </VaporPage.Section>
            <VaporPage.Section>
                {getSignaturesResponse.hasLoaded && (
                    <CustomDataGrid
                        name="remoteSignatures"
                        data={getSignaturesResponse.data.currentPage}
                        totalRows={Number.parseInt(
                            getSignaturesResponse.data.totalRows
                        )}
                        page={signatureSearchParams.page}
                        pageSize={signatureSearchParams.pageSize}
                        columns={columns}
                        onClickKey="uniqueid"
                        onClickCallback={(uniqueid) =>
                            handleNavigateToSignature(uniqueid)
                        }
                        query={signatureSearchParams}
                        setQuery={setSignatureSearchParams}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
