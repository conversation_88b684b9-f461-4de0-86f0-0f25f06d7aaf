import { VaporPage } from "@vapor/react-custom";
import { useState } from "react";
import { Select } from "@vapor/react-material";
import { InputLabel } from "@vapor/react-material";
import { MenuItem } from "@vapor/react-material";
import { FormControl } from "@vapor/react-material";
import PageTitle from "../../../../custom-components/PageTitle";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCirclePlus } from "@fortawesome/free-solid-svg-icons";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import useFilter from "./hooks/GetDynamicFields";
import usePostCustom from "../../../../hooks/usePostCustom";

export const DynamicFields = () => {
    const [confirmRestore, setConfirmRestore] = useState(false);
    const [restoreId, setRestoreId] = useState<null | number>(null);
    const deleteRequest = usePostCustom(
        "campidinamici/setdeleted?noTemplateVars=true"
    );

    const deleteAction = (row: any) => {
        setRestoreId(row.id);
        setConfirmRestore(true);
    };

    const { query, setQuery, list, filterData, loading } =
        useFilter(deleteAction);

    const navigate = useNavigate();
    const { t } = useTranslation();

    const onFilterChange = (newSelect: any) => {
        setQuery({
            ...query,
            activeSelect: newSelect,
        });
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleAgree = async () => {
        await deleteRequest.doFetch(true, {
            id: restoreId,
            delete: 0,
        });
        filterData(query);
        setConfirmRestore(false);
    };

    const handleRowClick = (index: number) => {
        const isDeleted =
            list.rows.filter((row: any) => row.id === index)[0].deleted === "1";
        if (!isDeleted) {
            navigate(`update/${index}`);
        }
    };

    return (
        <VaporPage>
            <ConfirmModal
                open={confirmRestore}
                title={t(
                    "Il campo dinamico risulta eliminato, vuoi ripristinarlo?"
                )}
                agree={t("Si")}
                decline={t("No")}
                handleDecline={() => {
                    setConfirmRestore(false);
                }}
                handleAgree={handleAgree}
            ></ConfirmModal>
            <PageTitle
                title={t("Campi Dinamici")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: t("Nuovo Campo Dinamico"),
                        onclick: () =>
                            navigate(`/campidinamici/campidinamici/update`),
                        startIcon: (
                            <FontAwesomeIcon
                                icon={faCirclePlus}
                            ></FontAwesomeIcon>
                        ),
                    },
                ]}
            ></PageTitle>
            <VaporPage.Section>
                <FormControl sx={{ maxWidth: "300px" }}>
                    <InputLabel>{t("Status")}</InputLabel>
                    <Select
                        value={query.activeSelect}
                        onChange={(e: { target: { value: any } }) =>
                            onFilterChange(e.target.value)
                        }
                    >
                        <MenuItem value={"0"}>{t("Attive")}</MenuItem>
                        <MenuItem value={"1"}>{t("Non attive")}</MenuItem>
                        <MenuItem value={"Tutte"}>{t("Tutte")}</MenuItem>
                    </Select>
                </FormControl>
            </VaporPage.Section>
            {!loading && (
                <VaporPage.Section>
                    <CustomDataGrid
                        name="dynamicFields"
                        setQuery={setQuery}
                        columns={list.columns}
                        data={list.rows}
                        page={list.page}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        onPageChangeCallback={onPageChangeCallback}
                        onClickKey="id"
                        onClickCallback={handleRowClick}
                        disableColumnResize={true}
                        disableColumnReorder={true}
                    />
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
