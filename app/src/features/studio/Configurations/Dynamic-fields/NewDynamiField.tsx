import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../../custom-components/PageTitle";
import { useParams, useNavigate } from "react-router-dom";
import { useGetFieldValues } from "./hooks/GetFieldValues";
import {
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    FormGroup,
    FormLabel,
    Grid,
    InputLabel,
    ListItem,
    MenuItem,
    Select,
    Stack,
    TextField,
    Typography,
} from "@vapor/react-material";
import { VaporToolbar } from "@vapor/react-custom";
import { CommonExpressions } from "./CommonExpressions";
import { useEffect, useState } from "react";
import { NewSelectValue } from "./NewSelectValue";
import { useDeleteDynamicField } from "./hooks/DeleteField";
import { useSaveDynamicValues } from "./hooks/SaveDynamicValues";
import { useTranslation } from "@1f/react-sdk";
import ConfirmModal from "../../../../custom-components/ConfirmModal";

export const NewDynamicField = () => {
    const fieldId = useParams().id;
    const { t } = useTranslation();
    const navigate = useNavigate();

    const { data, hasLoaded } = useGetFieldValues(fieldId);

    interface FieldStateData {
        id: string;
        name: string;
        type: string;
        description: string;
        pratica: boolean;
        anagrafica: boolean;
        timesheet: boolean;
        opt: any;
        selectValues: string[];
        asigned: boolean;
    }

    const [fieldState, setFieldState] = useState<FieldStateData>();

    function unescapeBackslashes(str: string) {
        return str.replace(/\\\\/g, "\\");
    }

    useEffect(() => {
        if (hasLoaded) {
            setFieldState({
                id: data.id,
                name: data.nome,
                type: data.tipo,
                description: data.descrizione,
                pratica: data.pratica === "1",
                anagrafica: data.anagrafica === "1",
                timesheet: data.timesheet === "1",
                opt:
                    data.tipo !== "select"
                        ? unescapeBackslashes(data.opt)
                        : data.opt === "null"
                        ? []
                        : data.opt,
                asigned: data.assigned,
                selectValues:
                    data && data.tipo === "select"
                        ? data.opt === "null"
                            ? []
                            : Object.keys(JSON.parse(data.opt))
                        : [],
            });
        } else {
            setFieldState({
                id: "",
                name: "",
                type: "",
                description: "",
                pratica: false,
                anagrafica: false,
                timesheet: false,
                opt: "",
                selectValues: [],
                asigned: false,
            });
        }
    }, [data]);

    const saveNewFieldParams = {
        nome: fieldState?.name,
        descrizione: fieldState?.description,
        pratica: fieldState?.pratica,
        anagrafica: fieldState?.anagrafica,
        ...(fieldState?.type === "select" && {
            options: fieldState?.selectValues,
        }),
        ...(fieldState?.type === "regex" && {
            regex: fieldState?.opt,
        }),
        timesheet: fieldState?.timesheet,
        tipo:
            fieldState?.type === "regex"
                ? 0
                : fieldState?.type === "checkbox"
                ? 1
                : fieldState?.type === "data"
                ? 2
                : fieldState?.type === "select"
                ? 3
                : fieldState?.type === "memo"
                ? 4
                : "",
        id: fieldState?.id,
    };

    const fieldParams = new FormData();

    fieldState?.name && fieldParams.append("nome", fieldState?.name);
    fieldState?.type && fieldState.id === ""
        ? fieldParams.append(
              "tipo",
              fieldState?.type === "regex"
                  ? "0"
                  : fieldState?.type === "checkbox"
                  ? "1"
                  : fieldState?.type === "data"
                  ? "2"
                  : fieldState?.type === "select"
                  ? "3"
                  : fieldState?.type === "memo"
                  ? "4"
                  : ""
          )
        : fieldState && fieldParams.append("tipo", fieldState?.type);

    if (fieldState?.selectValues && fieldState.type === "select") {
        for (let [_index, value] of fieldState?.selectValues.entries()) {
            fieldParams.append("options[]", value);
        }
    }

    fieldState?.description &&
        fieldParams.append("descrizione", fieldState.description);
    fieldState?.id
        ? fieldParams.append("id", fieldState.id)
        : fieldParams.append("id", "");
    fieldState &&
        fieldParams.append("pratica", fieldState.pratica ? "true" : "false");
    fieldState &&
        fieldParams.append(
            "anagrafica",
            fieldState.anagrafica ? "true" : "false"
        );
    fieldState &&
        fieldParams.append(
            "timesheet",
            fieldState.timesheet ? "true" : "false"
        );
    fieldState?.type === "regex" && fieldParams.append("regex", fieldState.opt);

    const [deleteField, setDeleteField] = useState(false);
    const [confirmDeletion, setConfirmDeletion] = useState(false);
    const [saveFieldValues, setSaveFieldValues] = useState(false);

    const validName = saveNewFieldParams.nome;
    const validType = saveNewFieldParams.tipo !== "";

    const validFieldParams = [validName, validType].every((value) => value);

    const deleteResponse = useDeleteDynamicField(fieldState?.id, deleteField);

    const { modifyResponse, saveResponse } = useSaveDynamicValues(
        fieldParams,
        saveFieldValues,
        validFieldParams
    );

    useEffect(() => {
        if (deleteResponse.hasLoaded) {
            navigate("/campidinamici/campidinamici");
        }
        if (saveResponse.hasLoaded) {
            navigate("/campidinamici/campidinamici");
        }
        if (modifyResponse.hasLoaded) {
            navigate("/campidinamici/campidinamici");
        }
    }, [
        deleteResponse.hasLoaded,
        saveResponse.hasLoaded,
        modifyResponse.hasLoaded,
    ]);

    const handleTimesheetChange = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const checked = event.target.checked;
        setFieldState((prevState: any) => ({
            ...prevState,
            timesheet: checked,
        }));
    };

    const handleAnagraficaChange = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const checked = event.target.checked;
        setFieldState((prevState: any) => ({
            ...prevState,
            anagrafica: checked,
        }));
    };

    const handlePraticaChange = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const checked = event.target.checked;
        setFieldState((prevState: any) => ({
            ...prevState,
            pratica: checked,
        }));
    };

    const handleUpdateOpt = (value: string) => {
        setFieldState((prevState: any) => ({
            ...prevState,
            opt: value,
        }));
    };

    const handleUpdateSelectValues = (value: string) => {
        setFieldState((prevState: any) => ({
            ...prevState,
            selectValues: [...prevState.selectValues, value],
        }));
    };

    const handleUpdateType = (event: any) => {
        const selectedType = event.target.value;
        setFieldState((prevState: any) => ({
            ...prevState,
            type: selectedType,
        }));
    };

    const handleNameUpdate = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const value = event.target.value;
        setFieldState((prevState: any) => ({
            ...prevState,
            name: value,
        }));
    };

    const handleDescriptionUpdate = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const value = event.target.value;
        setFieldState((prevState: any) => ({
            ...prevState,
            description: value,
        }));
    };

    return (
        <VaporPage
            contentToolbar={
                <VaporToolbar
                    variant="regular"
                    contentRight={
                        <Stack gap={1} direction="row">
                            <Button
                                variant="outlined"
                                color="secondary"
                                onClick={() =>
                                    navigate("/campidinamici/campidinamici")
                                }
                            >
                                {t("Annulla")}
                            </Button>
                            {fieldState?.id !== "" && !fieldState?.asigned && (
                                <Button
                                    variant="outlined"
                                    color="error"
                                    onClick={() => setConfirmDeletion(true)}
                                    disabled={deleteResponse.loading}
                                >
                                    {t("Elimina")}
                                </Button>
                            )}
                            <Button
                                variant="outlined"
                                onClick={() => setSaveFieldValues(true)}
                                disabled={!validFieldParams}
                            >
                                {fieldState?.id ? t("Modifica") : t("Salva")}
                            </Button>
                        </Stack>
                    }
                ></VaporToolbar>
            }
        >
            <PageTitle
                pathToPrevPage="/campidinamici/campidinamici"
                title={t("Campi Dinamici")}
                description={
                    fieldId
                        ? t("Scheda Modifica Campo Dinamico")
                        : t("Scheda Aggiunta Campo Dinamico")
                }
            ></PageTitle>
            <VaporPage.Section>
                <Grid
                    container
                    columnGap={3}
                    direction="column"
                    justifyContent="flex-start"
                    alignItems="flex-start"
                    sx={{
                        display: "grid",
                        gridTemplateColumns: "1fr 1fr 1fr 1fr",
                    }}
                >
                    <ConfirmModal
                        open={confirmDeletion}
                        handleDecline={() => setConfirmDeletion(false)}
                        handleAgree={() => setDeleteField(true)}
                        confirmText="Il campo dinamico verrà eliminato."
                        title={t("Eliminare campo dinamico?")}
                        agree={t("Si")}
                        decline={t("No")}
                    ></ConfirmModal>
                    <Grid item xs={6}>
                        <FormControl sx={{ gap: 4, minWidth: 350 }}>
                            <TextField
                                label={t("Nome")}
                                value={fieldState && fieldState.name}
                                onChange={handleNameUpdate}
                            ></TextField>
                            <TextField
                                label={t("Descrizione")}
                                value={fieldState && fieldState.description}
                                onChange={handleDescriptionUpdate}
                            ></TextField>

                            <FormGroup>
                                <FormLabel>{t("Utilizzabile in")}</FormLabel>
                                <Stack direction="column">
                                    {fieldState && (
                                        <FormControlLabel
                                            value={fieldState.pratica}
                                            control={
                                                <Checkbox
                                                    checked={fieldState.pratica}
                                                    onChange={
                                                        handlePraticaChange
                                                    }
                                                    name="Pratica"
                                                />
                                            }
                                            label={t("Pratica")}
                                        />
                                    )}
                                    {fieldState && (
                                        <FormControlLabel
                                            value={fieldState.anagrafica}
                                            control={
                                                <Checkbox
                                                    checked={
                                                        fieldState.anagrafica
                                                    }
                                                    onChange={
                                                        handleAnagraficaChange
                                                    }
                                                    name="Anagrafica"
                                                />
                                            }
                                            label={t("Anagrafica")}
                                        />
                                    )}
                                    {fieldState && (
                                        <FormControlLabel
                                            value={fieldState.timesheet}
                                            control={
                                                <Checkbox
                                                    checked={
                                                        fieldState.timesheet
                                                    }
                                                    onChange={
                                                        handleTimesheetChange
                                                    }
                                                    name="Timesheet"
                                                />
                                            }
                                            label={"Timesheet"}
                                        />
                                    )}
                                </Stack>
                            </FormGroup>
                        </FormControl>
                    </Grid>
                    <Grid item xs={3}>
                        <Stack gap={4}>
                            {data && data.tipo ? (
                                <TextField
                                    label={t("Tipo")}
                                    value={data.tipo}
                                    disabled
                                ></TextField>
                            ) : (
                                <FormControl sx={{ minWidth: 350 }}>
                                    <InputLabel>{t("Tipo")}</InputLabel>
                                    <Select
                                        label={t("Tipo")}
                                        value={fieldState?.type}
                                        onChange={handleUpdateType}
                                    >
                                        <MenuItem value={"regex"}>
                                            {t("Testo")}
                                        </MenuItem>
                                        <MenuItem value={"checkbox"}>
                                            {t("Check box")}
                                        </MenuItem>
                                        <MenuItem value={"data"}>
                                            {t("Data")}
                                        </MenuItem>
                                        <MenuItem value={"select"}>
                                            {t("Select")}
                                        </MenuItem>
                                        <MenuItem value={"memo"}>
                                            {t("Area di testo")}
                                        </MenuItem>
                                    </Select>
                                </FormControl>
                            )}
                            {fieldState && fieldState.type === "regex" && (
                                <CommonExpressions
                                    handleUpdateOpt={handleUpdateOpt}
                                    fieldExpression={fieldState.opt}
                                ></CommonExpressions>
                            )}
                            {fieldState?.type === "select" && (
                                <Stack>
                                    {fieldState?.selectValues?.map(
                                        (value: string) => (
                                            <ListItem>
                                                <Typography variant="bodySmall">
                                                    {value}
                                                </Typography>
                                            </ListItem>
                                        )
                                    )}
                                    <InputLabel>{t("Opzioni")}</InputLabel>
                                    <NewSelectValue
                                        handleUpdateSelectValues={
                                            handleUpdateSelectValues
                                        }
                                    ></NewSelectValue>
                                </Stack>
                            )}
                        </Stack>
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};
