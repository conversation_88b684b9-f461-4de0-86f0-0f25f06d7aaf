import {
    <PERSON>Control,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";

const options = [
    { name: "Solo numeri", value: "^[0-9]*$" },
    { name: "Caratteri alfanumerici e spazi", value: "^[0-9a-zA-Z ]*$" },
    {
        name: "<PERSON><PERSON>",
        value: "^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$",
    },
    { name: "Valuta", value: "^(([1-9]\\d{0,})|0)?((\\.|,)\\d{1,2})?$" },
    { name: "Targa auto", value: "^[A-Z]{2}[0-9]{3}[A-Z]{2}$" },
    {
        name: "Valuta Euro",
        value: "^(([0-9]){1,3}(\\.){1}){0,3}([0-9]{1,3})(,)([0-9]{2})$",
    },
];

export const CommonExpressions = ({
    fieldExpression,
    handleUpdateOpt,
}: {
    fieldExpression: string;
    handleUpdateOpt: (value: string) => void;
}) => {
    const { t } = useTranslation();
    return (
        <Stack direction="column" gap={4}>
            <FormControl sx={{ minWidth: 350 }}>
                <TextField
                    placeholder={t("Nessuna espressione di controllo")}
                    label={t("Espressione di controllo")}
                    value={fieldExpression}
                    onChange={(e: { target: { value: string; }; }) =>  handleUpdateOpt(e.target.value)}
                ></TextField>
            </FormControl>
            <Stack direction="column">
            <Typography>{t("Espressioni comuni")}</Typography>
                {options.map((option) => (
                    <Stack direction="row" alignItems="center">
                        <Button value={option.value} variant="text" size="small" onClick={() => handleUpdateOpt(option.value)}>
                        <Typography>{option.name}</Typography>
                        </Button>
                    </Stack>
                ))}
            </Stack>
        </Stack>
    );
};
