import { <PERSON><PERSON>, Text<PERSON><PERSON>, But<PERSON> } from "@vapor/react-material";
import { useState } from "react";
import { useTranslation } from "@1f/react-sdk";

export const NewSelectValue = ({
    handleUpdateSelectValues,
}: {
    handleUpdateSelectValues: (value: string) => void;
}) => {
    const { t } = useTranslation();
    const [newSelectValue, setNewSelectValue] = useState("");

    const handleClick = () => {
        newSelectValue.length > 0 && handleUpdateSelectValues(newSelectValue);
        setNewSelectValue("");
    };

    return (
        <Stack direction="row">
            <TextField
                placeholder={t("Aggiungi una opzione alla select")}
                value={newSelectValue}
                onChange={(e: any) => setNewSelectValue(e.target.value)}
            ></TextField>
            <Button disabled={!newSelectValue.length} onClick={handleClick}>
                {t("Conferma")}
            </Button>
        </Stack>
    );
};
