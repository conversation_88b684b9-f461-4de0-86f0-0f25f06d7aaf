import { DynamicFields } from "./DynamicFields";
import { NewDynamicField } from "./NewDynamiField";

export const campidinamici = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/campidinamici/campidinamici",
      element: <DynamicFields />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/campidinamici/campidinamici/update/:id?",
      element: <NewDynamicField/>,
    },
  }
];
