import { useEffect } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";

interface FieldValueData {
    anagrafica: string;
    assigned: boolean;
    contratti: string;
    deleted: string;
    descrizione: string;
    export: string;
    id: string;
    nome: string;
    non_assegnato: string;
    opt: string;
    pratica: string;
    timesheet: string;
    tipo: string;
}

export const useGetFieldValues = (id: string | undefined) => {
    const { data, loading, doFetch,hasLoaded } = useGetCustom(
        "campidinamici/getvalues?noTemplateVars=true",
        {
            id: id,
        }
    );

    useEffect(() => {
        if (id) {
            doFetch(true);
        }
    }, []);

    return { data: data as FieldValueData, loading,hasLoaded };
};
