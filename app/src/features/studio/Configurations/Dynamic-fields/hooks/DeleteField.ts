import { useEffect } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";

export const useDeleteDynamicField = (
    id: string | undefined,
    deleteField: boolean
) => {
    const { doFetch, loading, hasLoaded, data } = usePostCustom(
        "campidinamici/deletecampo"
    );

    useEffect(() => {
        if (deleteField && id) {
            doFetch(true, { id: id });
        }
    }, [id, deleteField]);

    return { loading, hasLoaded, data };
};
