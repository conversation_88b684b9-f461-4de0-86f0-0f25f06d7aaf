import { useEffect } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";

export const useSaveDynamicValues = (
    params: any,
    saveFieldValues: boolean,
    validFieldParams: boolean
) => {
    const modifyResponse = usePostCustom("campidinamici/modificacampo");
    const saveResponse = usePostCustom("campidinamici/salvacampo");

    useEffect(() => {
        if (saveFieldValues && validFieldParams) {
            if (params.get("id") !== "") {
                modifyResponse.doFetch(true, params);
            } else {
                saveResponse.doFetch(true, params);
            }
        }
    }, [saveFieldValues]);

    return { modifyResponse, saveResponse };
};
