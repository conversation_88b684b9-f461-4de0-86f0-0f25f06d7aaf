import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useEffect } from "react";

const validationSchema = yup.object().shape({
    nome: yup.string().required("Il nome deve contenere almeno un carattere."),
});

export default function useYupValidation(initialValues: any) {
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({
        resolver: yupResolver(validationSchema),
        defaultValues: initialValues,
    });


    useEffect(() => {
        setValue("nome", initialValues.nome);
    }, [initialValues.nome]);

    return {
        register,
        handleSubmit,
        errors,
    };
}
