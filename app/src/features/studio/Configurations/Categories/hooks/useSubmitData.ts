import usePostCustom from "../../../../../hooks/usePostCustom";
import { useState } from "react";
import { DEFAULT_DELETE_MODAL, DEFAULT_INFO_ERROR_MODAL } from "../data/defaultData";
import { IDefaultInfoErrorModal, IDefaultDeleteModal } from "../interfaces/categories.interface";

export default function useSubmitData() {
    const saveCategoryRequest = usePostCustom("/categorie/salvacategoria?noTemplateVars=true");
    const updateCategoryRequest = usePostCustom("/categorie/modificacategoria?noTemplateVars=true");
    const deleteCategoryRequest = usePostCustom("/categorie/eliminacategoria?noTemplateVars=true");
    const [openDeleteModal, setOpenDeleteModal] = useState<IDefaultDeleteModal>(DEFAULT_DELETE_MODAL);
    const [openErrorNameModal, setOpenErrorNameModal] = useState<IDefaultInfoErrorModal>(DEFAULT_INFO_ERROR_MODAL);

    const deleteData = async (data: any): Promise<boolean> => {
        const response: any = await deleteCategoryRequest.doFetch(true, data);
        return response.data === 1; //response 1 -> success
    };

    const flattenObject = (obj: any, parentKey = '', res: any = {}) => {
        for (let key in obj) {
            let propName = parentKey ? `${parentKey}[${key}]` : key;
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                flattenObject(obj[key], propName, res);
            } else {
                res[propName] = obj[key];
            }
        }
        return res;
    };

    const submitData = async (data: any): Promise<boolean> => {
        const formData = new FormData();

        for (let key in data) {
            if (key === "campi") {
                data[key].forEach((campi: any) => {
                    formData.append("campi[]", campi.id);
                });
            } else if (key === "oggetti") {
                data[key].forEach((oggeti: any) => {
                    formData.append("oggetti[]", oggeti.id);
                });
            }  else if (key==="model") {
                data[key].forEach((modelItem: any, index: number) => {
                    const flattenedModelItem = flattenObject(modelItem, `model[${index}]`);
                    for (let flattenedKey in flattenedModelItem) {
                        formData.append(flattenedKey, flattenedModelItem[flattenedKey]);
                    }
                });
            }
            else {
                formData.append(key, data[key]);
            }
        }

        const response: any = data.id ? await updateCategoryRequest.doFetch(true, formData) : await saveCategoryRequest.doFetch(true, formData);
        if(response.data === 3) {
            setOpenErrorNameModal({...openErrorNameModal, open: true})
        }
        return response.data === 1;
    };

    return {
        submitData,
        deleteData,
        openDeleteModal,
        setOpenDeleteModal,
        openErrorNameModal,
        setOpenErrorNameModal,
    };
}
