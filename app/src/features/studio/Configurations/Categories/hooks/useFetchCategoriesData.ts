import useGetCustom from "../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { IList } from "../../../../../interfaces/general.interfaces";
import { getCategoriesViewGrid } from "../../../../../utilities/certificate/gridColumn";
import { useTranslation } from "@1f/react-sdk";
import {
    DEFAULT_QUERY,
    DEFAULT_PARAMS,
    INITIAL_TREEFOLDER_DATA,
} from "../data/defaultData";

export default function useFetchCategoriesData(
    type: string | null,
    id?: string | undefined
) {
    const { t } = useTranslation();
    const categorieViewRequest = useGetCustom(
        `/categorie/categorie/view?tipo=${type}`
    );
    const categorieListRequest = useGetCustom(
        `/categorie/list?noTemplateVars=true`
    );
    const getValuesRequest = useGetCustom(
        "/categorie/getvalues?noTemplateVars=true"
    );
    const getOggetiRequest = useGetCustom(
        "/categorie/getoggetti?noTemplateVars=true"
    );
    const [query, setQuery] = useState<any>({ ...DEFAULT_QUERY, tipo: type });
    const [folderTreeData, setFolderTreeData] = useState<any>(
        INITIAL_TREEFOLDER_DATA
    );
    const [campiData, setCampiData] = useState([]);
    const [params, setParams] = useState<any>(DEFAULT_PARAMS);
    const [selectedCampi, setSelectedCampi] = useState<any>([]);
    const [objectData, setObjectData] = useState<any>([]);
    const [objectSelected, setObjectSelected] = useState<any>([]);
    const [isFetchListDataComplete, setIsFetchListDataComplete] =
        useState(false);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const fetchListData = async () => {
        setIsFetchListDataComplete(false);
        const [columns, response, listResponse, oggetiResponse]: any =
            await Promise.all([
                getCategoriesViewGrid(t),
                categorieViewRequest.doFetch(true),
                categorieListRequest.doFetch(true, query),
                getOggetiRequest.doFetch(true),
            ]);

        const { campi } = response.data;
        const { currentPage, totalRows } = listResponse.data;

        setCampiData(campi);
        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: query?.page,
            pageSize: query?.pageSize,
        });
        setObjectData(oggetiResponse.data || []);
        setIsFetchListDataComplete(true);
    };

    const fetchValues = async () => {
        const { data }: any = await getValuesRequest.doFetch(true, { id });

        const parsedCampiIds = JSON.parse(data.campi);
        const parsedOggettiIds = JSON.parse(data.oggetti);
        const parsedFolderModels = JSON.parse(data.model_folder);

        const findSelectedCampi = parsedCampiIds.map((campiId: string) =>
            campiData.find((campi: any) => campi.id === campiId)
        );
        const findSelectedOggetti = parsedOggettiIds.map((oggettiId: string) =>
            objectData.find((oggetti: any) => oggetti.id === oggettiId)
        );

        setSelectedCampi(findSelectedCampi);
        setObjectSelected(findSelectedOggetti);
        setFolderTreeData(
            parsedFolderModels === null
                ? INITIAL_TREEFOLDER_DATA
                : parsedFolderModels
        );
        setParams({
            ...params,
            nome: data.nome,
            descrizione: data.descrizione,
            categoria_colonna: type,
        });
    };

    useEffect(() => {
        fetchListData();
    }, [query]);

    useEffect(() => {
        //if no id, it means its on creation mode, so dont fetch the getValues
        if (id !== undefined && isFetchListDataComplete) {
            fetchValues();
        }
    }, [id, isFetchListDataComplete]);

    return {
        DEFAULT_QUERY,
        query,
        setQuery,
        list,
        setList,
        loading: categorieViewRequest.loading || categorieListRequest.loading,
        campiData,
        params,
        setParams,
        selectedCampi,
        setSelectedCampi,
        objectData,
        objectSelected,
        setObjectSelected,
        folderTreeData,
        setFolderTreeData,
    };
}
