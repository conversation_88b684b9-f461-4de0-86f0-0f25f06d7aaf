import VaporPage from "@vapor/react-custom/VaporPage";
import { useState } from "react";
import PageTitle from "../../../../custom-components/PageTitle";
import ImportExportIcon from "@mui/icons-material/ImportExport";
import { useTranslation } from "@1f/react-sdk";
import { Box, TextField, FormControl, Chip, InputLabel, Typography } from "@vapor/react-material";
import { DndContext, DragOverlay } from "@dnd-kit/core";
import { arrayMove, SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import useFetchCategoriesData from "./hooks/useFetchCategoriesData";
import useSubmitData from "./hooks/useSubmitData";
import { useParams, useNavigate } from "react-router-dom";
import Spinner from "../../../../custom-components/Spinner";
import { SortableItem, DroppableContainer } from "./helpers/DragAndDrop";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import DocumentFolders from "./documentFoldersPractice";
import useYupValidation from "./hooks/useYupValidations";
import ToastNotification from "../../../../custom-components/ToastNotification";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

export default function SaveCategory() {
    const { t } = useTranslation();
    const { type, id }: any = useParams();
    const navigate = useNavigate();
    const [activeId, setActiveId] = useState<any>(null);
    const { campiData, loading, params, setParams, selectedCampi, setSelectedCampi, objectData, objectSelected, setObjectSelected, folderTreeData, setFolderTreeData } = useFetchCategoriesData(type, id);

    const { errors, register, handleSubmit } = useYupValidation(params);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const { submitData, deleteData, openDeleteModal, setOpenDeleteModal, openErrorNameModal, setOpenErrorNameModal } = useSubmitData();

    const [confirmContrattiModal, setConfirmContrattiModal] = useState({
        open: false,
        title: t("Conferma"),
        confirmText: t("Se si rimuovono i campi dinamici appartenenti a una Categoria si potrebbero perdere i rispettivi valori salvati. Proseguire?"),
        decline: t("Annulla"),
        agree: t("Conferma"),
        colorConfirmButton: "primary" as const
    });

    const handleChipClick = (campi: any) => {
        if (!selectedCampi.some((c: any) => c.id === campi.id)) {
            setSelectedCampi([...selectedCampi, campi]);
        } else {
            setSelectedCampi(selectedCampi.filter((c: any) => c.id !== campi.id));
        }
    };

    const handleDragEnd = ({ active, over }: any) => {
        if (!active || !over) return;

        const activeIndex = selectedCampi.findIndex((c: any) => c.id === active.id);
        const overIndex = selectedCampi.findIndex((c: any) => c.id === over.id);

        if (activeIndex !== overIndex) {
            setSelectedCampi((items: any) => arrayMove(items, activeIndex, overIndex));
        }
    };

    const getCampiNameById = (id: string) => {
        return selectedCampi.find((campi: any) => campi.id === id);
    };

    const onChangeInput = (event: any) => {
        const { name, value } = event.target;
        setParams({ ...params, [name]: value });
    };

    const handleAutocompleteChange = (_: any, value: any, reason: string) => {
        if (reason === "selectOption") {
            const newSelection = [...objectSelected, value[value.length - 1]];
            setObjectSelected(newSelection);
        } else if (reason === "removeOption") {
            setObjectSelected(value);
        }
    };

    const onSubmitData = async () => {
        const categoryParams = {
            ...(id !== undefined && { id: id }),
            nome: params.nome,
            descrizione: params.descrizione,
            campi: selectedCampi,
            categoria_colonna: type,
            ...(type === "pratica" && { model: folderTreeData }),
            ...(type === "pratica" && { oggetti: objectSelected })
        };

        const success: boolean = await submitData(categoryParams);
        if (success) return navigate(`/categorie/categorie/view?tipo=${type}`);
    };

    const onDeleteData = async () => {
        const data = {
            id,
            paramUrl: type
        };

        const success: boolean = await deleteData(data);
        if (type === "contratti") {
            setShowErrorMessage(true);
        }
        if (success) {
            setTimeout(() => {
                return navigate(`/categorie/categorie/view?tipo=${type}`);
            }, 1000);
        }
    };

    const handleSubmitByType = () => {
        if (type === "contratti" && id) {
            setConfirmContrattiModal({
                ...confirmContrattiModal,
                open: true
            });
            setTimeout(() => {
                setShowSuccessMessage(true);
            }, 1000);
            return;
        }
        return handleSubmit(onSubmitData)();
    };

    return (
        <>
            {loading ? (
                <Spinner />
            ) : (
                <VaporPage>
                    <ToastNotification showNotification={showErrorMessage} setShowNotification={setShowErrorMessage} severity="error" text={t("Categoria eliminata")} />
                    <ToastNotification showNotification={showSuccessMessage} setShowNotification={setShowSuccessMessage} severity="success" text={t(`${id ? "Modificata" : "Aggiunta"} categoria`)} />
                    <PageTitle
                        title={id ? t("Modifica categoria") : t("Creazione categoria")}
                        showBackButton={true}
                        pathToPrevPage={`/categorie/categorie/view?tipo=${type}`}
                        actionButtons={[
                            {
                                label: t("Elimina"),
                                onclick: () => {
                                    if (type === "contratti" && id) {
                                        return onDeleteData();
                                    }
                                    setOpenDeleteModal({
                                        ...openDeleteModal,
                                        open: true
                                    });
                                },
                                color: "error",
                                variant: "outlined",
                                sx: {
                                    visibility: id === undefined ? "hidden" : "" //if its on create(id is undefined), hide the delete button
                                }
                            },
                            {
                                label: t("Conferma"),
                                type: "submit",
                                onclick: handleSubmitByType,
                                variant: "contained"
                            }
                        ]}
                    />

                    <VaporPage.Section>
                        <Box
                            autoComplete="off"
                            component="form"
                            sx={{
                                display: "flex",
                                alignItems: "center", // Center vertically
                                flexDirection: "column",
                                "& .MuiTextField-root": {
                                    m: 2,
                                    width: 500
                                },
                                "& .MuiFormControl-root": {
                                    m: 2,
                                    width: 500
                                }
                            }}
                        >
                            <TextField
                                {...register("nome")}
                                error={!!errors.nome} // Show error if Yup validation error exists
                                name="nome"
                                label={t("Nome *")}
                                value={params.nome}
                                onChange={onChangeInput}
                            />
                            <TextField name="descrizione" label={t("Descrizione")} value={params.descrizione} onChange={onChangeInput} />
                            <FormControl fullWidth margin="normal">
                                <Box display="flex">
                                    <InputLabel htmlFor="campi">{t("Campi")}</InputLabel>
                                    {campiData.length === 0 && (
                                        <Typography variant="body2" sx={{ marginLeft: "30px" }} color="textSecondary">
                                            {t("Non è presente alcun campo dinamico")}
                                        </Typography>
                                    )}
                                </Box>
                                {campiData.length > 0 && (
                                    <Box
                                        component={"section"}
                                        sx={{
                                            mt: 1,
                                            display: "flex",
                                            flexWrap: "wrap",
                                            gap: 1
                                        }}
                                    >
                                        {campiData.map((campi: any) => (
                                            <span key={campi.id} onClick={() => handleChipClick(campi)}>
                                                <Chip label={campi.nome} variant={selectedCampi.some((c: any) => c.id === campi.id) ? "outlined" : "filled"} />
                                            </span>
                                        ))}
                                    </Box>
                                )}
                            </FormControl>
                            {type === "pratica" && (
                                <CustomAutocomplete
                                    disableCloseOnSelect
                                    multiple
                                    value={objectSelected}
                                    options={objectData}
                                    onChange={handleAutocompleteChange}
                                    getOptionLabel={(option: any) => option.nome}
                                    renderInput={(params: any) => <TextField {...params} label={t("Oggetto")} />}
                                    renderOption={(props: any, option: any) => (
                                        <li {...props}>
                                            <span
                                                dangerouslySetInnerHTML={{
                                                    __html: option.nome
                                                }}
                                            />
                                        </li>
                                    )}
                                    filterSelectedOptions={false} // Allow selecting the same option multiple times
                                    sx={{ width: 500, mr: 3 }}
                                />
                            )}
                            <FormControl fullWidth margin="normal">
                                <Box display="flex">
                                    <InputLabel htmlFor="campi">
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                ml: campiData.length === 0 ? "0" : "5px"
                                            }}
                                        >
                                            {t("Ordine campi")} <ImportExportIcon sx={{ ml: 1 }} />
                                            {campiData.length === 0 && (
                                                <Typography
                                                    variant="body2"
                                                    sx={{
                                                        marginLeft: "30px"
                                                    }}
                                                    color="textSecondary"
                                                >
                                                    {t("Non è presente alcun campo dinamico")}
                                                </Typography>
                                            )}
                                        </Box>
                                    </InputLabel>
                                </Box>
                                <Box
                                    sx={{
                                        mt: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                        gap: 1
                                    }}
                                >
                                    <DndContext onDragEnd={handleDragEnd} onDragStart={({ active }) => setActiveId(active.id)}>
                                        <SortableContext items={selectedCampi.map((c: any) => c.id)} strategy={verticalListSortingStrategy}>
                                            <DroppableContainer id="droppable">
                                                {selectedCampi.map((campi: any) => (
                                                    <SortableItem key={campi.id} id={campi.id} nome={campi.nome} />
                                                ))}
                                            </DroppableContainer>
                                        </SortableContext>
                                        <DragOverlay>
                                            {activeId ? (
                                                <Box
                                                    sx={{
                                                        padding: "8px 16px",
                                                        backgroundColor: "#D6F1FF",
                                                        border: "1px solid #008FD6",
                                                        borderRadius: 16,
                                                        width: 500,
                                                        height: "auto",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        cursor: "grabbing"
                                                    }}
                                                >
                                                    {getCampiNameById(activeId)?.nome}
                                                </Box>
                                            ) : null}
                                        </DragOverlay>
                                    </DndContext>
                                </Box>
                            </FormControl>
                        </Box>
                        {type === "pratica" && <DocumentFolders t={t} folderTreeData={folderTreeData} setFolderTreeData={setFolderTreeData} />}
                    </VaporPage.Section>
                </VaporPage>
            )}
            <ConfirmModal
                open={openDeleteModal.open}
                title={t(openDeleteModal.title)}
                confirmText={t(openDeleteModal.confirmText)}
                decline={t(openDeleteModal.decline)}
                agree={t(openDeleteModal.agree)}
                colorConfirmButton={
                    (openDeleteModal.colorConfirmButton && ["primary", "secondary", "error"].includes(openDeleteModal.colorConfirmButton)
                        ? openDeleteModal.colorConfirmButton
                        : "primary") as "primary" | "secondary" | "error"
                }
                handleDecline={() => setOpenDeleteModal({ ...openDeleteModal, open: false })}
                handleAgree={onDeleteData}
            />
            <ConfirmModal
                open={confirmContrattiModal.open}
                title={t(confirmContrattiModal.title)}
                confirmText={t(confirmContrattiModal.confirmText)}
                decline={t(confirmContrattiModal.decline)}
                agree={t(confirmContrattiModal.agree)}
                colorConfirmButton={
                    (["primary", "secondary", "error"].includes(confirmContrattiModal.colorConfirmButton)
                        ? confirmContrattiModal.colorConfirmButton
                        : "primary") as "primary" | "secondary" | "error"
                }
                handleDecline={() =>
                    setConfirmContrattiModal({
                        ...confirmContrattiModal,
                        open: false
                    })
                }
                handleAgree={() => {
                    setConfirmContrattiModal({
                        ...confirmContrattiModal,
                        open: false
                    });
                    handleSubmit(onSubmitData)();
                }}
            />
            <ToastNotification
                showNotification={openErrorNameModal.open}
                setShowNotification={() =>
                    setOpenErrorNameModal({
                        ...openErrorNameModal,
                        open: false
                    })
                }
                severity="error"
                text={t(openErrorNameModal.confirmText)}
            />
        </>
    );
}
