import VaporPage from "@vapor/react-custom/VaporPage";
import Spinner from "../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import PageTitle from "../../../../custom-components/PageTitle";
import { useLocation, useNavigate } from "react-router-dom";
import useFetchCategoriesData from "./hooks/useFetchCategoriesData";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel, GridCallbackDetails } from "@mui/x-data-grid-pro";


export default function CategoriesIndex() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    const type = new URLSearchParams(location.search).get("tipo");
    const { list, loading, query, setQuery } = useFetchCategoriesData(type);

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setQuery({ ...query, page: model.page, pageSize: model.pageSize });
    };

    const onClickCallback = (uniqueid: any) => {
        navigate(`/category/view/${type}/${uniqueid}`);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="categories"
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                selectableRows={false}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={onClickCallback}
                onClickKey="id"
                onClickCheckboxKey="id"
                query={query}
                setQuery={setQuery}
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle
                    title={t(`Gestione categoria ${type}`)}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: t("Nuova Categoria"),
                            onclick: () =>
                                navigate(`/add/category/view/${type}`),
                            variant: "contained",
                            startIcon: <AddCircleOutlineIcon />,
                        },
                    ]}
                />
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
