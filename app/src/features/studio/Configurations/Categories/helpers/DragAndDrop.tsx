import { useSortable } from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { Box } from "@vapor/react-material";

export const SortableItem = ({ id, nome }: { id: string; nome: string }) => {
    const { attributes, listeners, setNodeRef, transform, transition } =
        useSortable({ id });

    const style = {
        transform: CSS.Translate.toString(transform),
        transition,
        width: 500,
        padding: "8px 16px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#D6F1FF",
        border: "1px solid #008FD6",
        borderRadius: 16,
        marginBottom: 8,
        cursor: "grab", // Pointer cursor for drag indication
    };

    return (
        <Box ref={setNodeRef} {...listeners} {...attributes} style={style}>
            {nome}
        </Box>
    );
};

export const DroppableContainer = ({ children, id }: any) => {
    const { setNodeRef } = useDroppable({
        id,
    });

    return (
        <div
            ref={setNodeRef}
            style={{ display: "flex", flexDirection: "column", gap: 8 }}
        >
            {children}
        </div>
    );
};
