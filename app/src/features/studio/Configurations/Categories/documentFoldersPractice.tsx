import { useState } from 'react';
import { <PERSON>View, TreeItem } from '@vapor/x-treeview';
import { Button, Menu, MenuItem, TextField, Dialog, DialogActions, DialogContent, DialogTitle, Box, Typography, Divider  } from '@vapor/react-material';
import { ExpandMore, ChevronRight } from '@mui/icons-material';
import FolderOpenOutlinedIcon from '@mui/icons-material/FolderOpenOutlined';
import { IDocumentFolders, IContextMenu } from './interfaces/categories.interface';

export default function DocumentFolders(props: IDocumentFolders){
  const { t, folderTreeData, setFolderTreeData } = props;
  const [contextMenu, setContextMenu] = useState<IContextMenu | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogType, setDialogType] = useState<string>('');
  const [dialogValue, setDialogValue] = useState<string>('');

  const handleRightClick = (event: any) => {
    //retriving the nodeid from event
    const treeItem = (event.target as HTMLElement).closest('.MuiTreeItem-root') as HTMLElement;
    const nodeId = treeItem?.getAttribute('data-nodeid');
    event.preventDefault();
    setContextMenu({
      mouseX: event.clientX + 2,
      mouseY: event.clientY - 6,
    });
    setSelectedNode(nodeId);
  };

  const handleMenuClose = () => {
    setContextMenu(null);
    setSelectedNode(null); // Clear selected node after menu close to avoid conflicts
  };

  const handleDialogOpen = (action: any) => {
    setDialogType(action);
    if (action === 'rename') {
      const node = findNodeById(folderTreeData, selectedNode);
      setDialogValue(node ? node.text : '');
    }
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setDialogValue('');
    setDialogType('');
  };

  const handleDialogSave = () => {
    const newId = `node_${Date.now()}`;
    if (dialogType === 'new') {
      const newNode = {
        id: newId,
        text: dialogValue,
        parent: selectedNode,
        icon: true,
        li_attr: { id: newId },
        a_attr: { href: '#', id: `${newId}_anchor` },
        state: { loaded: true, opened: false, selected: false, disabled: false },
        type: 'default'
      };
      setFolderTreeData([...folderTreeData, newNode]);
    } else if (dialogType === 'rename') {
      const updatedData = folderTreeData.map((node: any) =>
        node.id === selectedNode ? { ...node, text: dialogValue } : node
      );
      setFolderTreeData(updatedData);
    }
    handleDialogClose();
    handleMenuClose();
  };

  const handleDelete = () => {
    const updatedData = folderTreeData.filter((node: any) => node.id !== selectedNode);
    setFolderTreeData(updatedData);
    handleMenuClose();
  };

  const findNodeById = (nodes: any, id: any) => {
    for (const node of nodes) {
      if (node.id === id) return node;
    }
    return null;
  };

  const renderTree = (nodes: any, parentId = '#') => {
    return nodes
      .filter((node: any) => node.parent === parentId)
      .map((node: any) => {
        const treeItemProps = {
          key: node.id,
          nodeId: node.id,
          itemId: node.id,
          "data-nodeid": node.id, // Add data attribute to store node id
          onContextMenu: (event: React.MouseEvent) => handleRightClick(event),
          label: (
            <div style={{ display: 'flex', alignItems: 'center'}}>
              <FolderOpenOutlinedIcon style={{ marginRight: 8 }}/>
              {node.text}
            </div>
          ),
          children: renderTree(nodes, node.id)
        };

        return <TreeItem {...treeItemProps} />;
      });
  };


  return (
    <>
    <Divider />
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 4
      }}
    >
      <Box sx={{ width: '100%', maxWidth: '800px' , textAlign: 'center'}}>
        <Typography sx={{ mt: 2 }}>
          {t("Con questo strumento è possibile creare il modello delle cartelle dei documenti. Fai click con il tasto destro sulle cartelle per visualizzare le opzioni disponibili.")}
        </Typography>
        {(() => {
          const treeViewProps = {
            defaultCollapseIcon: <ExpandMore />,
            defaultExpandIcon: <ChevronRight />,
            sx: { height: 240, flexGrow: 1, maxWidth: 400, overflowY: 'auto', mt: 5, ml: 2 }
          };
          return (
            <TreeView {...treeViewProps}>
              {renderTree(folderTreeData)}
            </TreeView>
          );
        })()}
        <Menu
          open={contextMenu !== null}
          onClose={handleMenuClose}
          anchorReference="anchorPosition"
          anchorPosition={
            contextMenu !== null
              ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
              : undefined
          }
        >
          <MenuItem onClick={() => handleDialogOpen('new')}>{t("Nuova cartella")}</MenuItem>
          <MenuItem onClick={() => handleDialogOpen('rename')}>{t("Rinomina")}</MenuItem>
          <MenuItem onClick={handleDelete}>{t("Cancella")}</MenuItem>
        </Menu>
        <Dialog open={dialogOpen} onClose={handleDialogClose}>
          <DialogTitle>{dialogType === 'new' ? t('Nuova cartella') : t('Rinomina cartella')}</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="Nome"
              fullWidth
              value={dialogValue}
              onChange={(e: any) => setDialogValue(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDialogClose}>{t("Annulla")}</Button>
            <Button onClick={handleDialogSave} color="primary">{t("Salva")}</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
    </>
  );
};