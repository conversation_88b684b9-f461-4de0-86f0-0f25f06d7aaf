import { VaporPage } from "@vapor/react-custom";
import { useEffect, useState } from "react";
import { Select, Stack } from "@vapor/react-material";
import { MenuItem } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel, GridCallbackDetails } from "@mui/x-data-grid-pro";
import PageTitle from "../../../../custom-components/PageTitle";
import { useSaveDelegationPower } from "./hooks/SaveDelegationsPowerChange";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useListUser } from "./hooks/GetUsers";
import useFilter from "./hooks/GetDelegations";

export const Delegations = () => {
    const { t } = useTranslation();

    const { query, setQuery, list, loading, selectedCheckbox } = useFilter();

    const onUserIdChange = (event: any, _child: any) => {
        const id = event.target.value;
        setQuery({
            ...query,
            user_id: id,
        });
    };

    const onSelectedPowerChange = (event: any, _child: any) => {
        const power = event.target.value;
        setQuery({
            ...query,
            power: power,
            page: 0,
        });
    };

    const saveDelegationResponse = useSaveDelegationPower(selectedCheckbox);

    const userlist = useListUser({
        nomeSearch: "",
        noTemplateVars: true,
        page: 0,
        pageSize: 30,
        referenteSearch: "",
        ruoloSearch: "",
        uid: "0",
        sortColumn: "id",
        sortOrder: "asc",
    });

    const [users, setUsers] = useState<any>([]);

    useEffect(() => {
        if (userlist.hasLoaded) {
            setUsers([
                { id: "0", nomeutente: "Tutti gli utenti" },
                ...userlist.data.currentPage,
            ]);
        }
    }, [userlist.hasLoaded]);

    const [showNotification, setShowNotification] = useState(false);

    useEffect(() => {
        if (
            saveDelegationResponse.hasLoaded === true &&
            saveDelegationResponse.loading === false
        ) {
            setShowNotification(true);
        }
    }, [saveDelegationResponse.hasLoaded, saveDelegationResponse.loading]);

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _details: GridCallbackDetails<any>
    ) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    return (
        <VaporPage>
            <ToastNotification
                setShowNotification={setShowNotification}
                severity="success"
                showNotification={showNotification}
                text={t("Salvataggio effettuato!")}
                variant="outlined"
            ></ToastNotification>
            <PageTitle
                title={t("CONFIGURAZIONE DELEGHE")}
                showBackButton={false}
            />
            <VaporPage.Section>
                <Stack direction="row" gap={4}>
                    <Select
                        sx={{ minWidth: "200px" }}
                        value={query.user_id}
                        onChange={onUserIdChange}
                    >
                        {users.map((user: any) => (
                            <MenuItem key={user.id} value={user.id}>
                                {user.nomeutente}
                            </MenuItem>
                        ))}
                    </Select>
                    <Select
                        sx={{ minWidth: "200px" }}
                        value={query.power}
                        onChange={onSelectedPowerChange}
                    >
                        <MenuItem value="0">{t("Tutti i poteri")}</MenuItem>
                        <MenuItem value="visura">{t("Visura")}</MenuItem>
                        <MenuItem value="conservazione">
                            {t("Conservazione")}
                        </MenuItem>
                        <MenuItem value="fatturazione">
                            {t("Fatturazione")}
                        </MenuItem>
                    </Select>
                </Stack>
            </VaporPage.Section>
            {!loading && (
                <VaporPage.Section>
                    <CustomDataGrid
                        name="delegations"
                        setQuery={setQuery}
                        columns={list.columns}
                        data={list.rows}
                        page={list.page}
                        totalRows={list.totalRows}
                        pageSize={list.pageSize}
                        loading={loading}
                        query={query}
                        onPageChangeCallback={onPageChangeCallback}
                        onClickKey="slaveId"
                        disableColumnResize={true}
                        disableColumnReorder={true}
                    />
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
