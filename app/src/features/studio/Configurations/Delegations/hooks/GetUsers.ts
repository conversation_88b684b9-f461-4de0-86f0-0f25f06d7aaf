import { useEffect } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";

export type useListUserProps = {
    noTemplateVars: true;
    uid: string;
    page: number;
    pageSize: number;
    sortColumn: "nomeutente" | "qualificautente" | "referente" | "id";
    sortOrder: "asc" | "desc";
    nomeSearch: string;
    ruoloSearch: string;
    referenteSearch: string;
};

export const useListUser = (params: useListUserProps) => {
    const { loading, data, error, doFetch, hasLoaded } = useGetCustom(
        "/groups/list-user",
        params
    );

    useEffect(() => {
        doFetch(true);
    }, [
        params.uid,
        params.page,
        params.pageSize,
        params.sortColumn,
        params.sortOrder,
        params.nomeSearch,
        params.ruoloSearch,
        params.referenteSearch,
    ]);
    return { loading, hasLoaded, error, data };
};
