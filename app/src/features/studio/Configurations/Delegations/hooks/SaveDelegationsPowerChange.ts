import { useEffect } from "react";
import usePostCustom from "../../../../../hooks/usePostCustom";

export interface SaveDelegationPowerParams {
    type: string;
    masterId: string;
    slaveId: string;
    checked: boolean;
}

export const useSaveDelegationPower = (
    params: SaveDelegationPowerParams | undefined
) => {
    const { data, loading, doFetch, hasLoaded } =
        usePostCustom("delegations/save");

    useEffect(() => {
        if (params) {
            doFetch(true, params);
        }
    }, [params?.type, params?.masterId, params?.checked, params?.slaveId]);
    return { data, loading, hasLoaded };
};
