import useGetCustom from "../../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback, useRef } from "react";
import { IDefaultQuery } from "../interfaces/simpleList.interface";
import { IList } from "../../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import { getDelegationGrid } from "../../../../../utilities/delegation/gridColumn";
import { SaveDelegationPowerParams } from "./SaveDelegationsPowerChange";

export default function useFilter() {
    const { t } = useTranslation();
    const filterRequest = useGetCustom("delegations/list?noTemplateVars=true");
    const [selectedCheckbox, setSelectedCheckbox] = useState<
        SaveDelegationPowerParams | undefined
    >();
    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "nomeutente",
        sortOrder: "desc",
        user_id: "0",
        power: "0",
    };

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterData = async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getDelegationGrid(t, setSelectedCheckbox),
            filterRequest.doFetch(true, cQuery),
        ]);
        if (response.data._redirectInfo === undefined) {
            const { currentPage, totalRows } = response.data;
            setList({
                ...list,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        } else {
            setList({
                ...list,
                rows: [],
                columns,
                totalRows: 0,
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        }
    };
    const isMountingRef = useRef(false);

    useEffect(() => {
        isMountingRef.current = true;
        filterData(query);
    }, []);
    const getSearchResults = useCallback(
        debounce((value: any) => {
            filterData(value);
        }, 500),
        []
    );

    useEffect(() => {
        if (!isMountingRef.current) {
            getSearchResults(query);
        } else {
            isMountingRef.current = false;
        }
    }, [query, selectedCheckbox]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: filterRequest.loading,
        selectedCheckbox,
        setSelectedCheckbox,
    };
}
