import {
    Box,
    Button,
    TextField,
    Select,
    MenuItem,
    InputLabel,
    FormControl,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";
interface selectProps {
    name: string;
    value: string;
}
export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData } = props;
    const { t } = useTranslation();
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        searchWithDebounce(e);
    };

    const onChangeInputs = (e: any) => {
        const { name, value } = e.target;
        setQuery({ ...query, [name]: value });
    };
    const macroTypes: selectProps[] = [
        { name: t("Tutte le caratterizzazioni"), value: "-1" },
        { name: t("Solo positive"), value: "1" },
        { name: t("Solo negative"), value: "2" },
        { name: t("Solo miste"), value: "3" },
    ];

    const visibilityTypes: selectProps[] = [
        { name: t("Tutte"), value: "-1" },
        { name: t("Nascoste"), value: "0" },
        { name: t("Visibili"), value: "1" },
    ];

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={"Nome"}
                variant="outlined"
                name={"searchField"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={query["searchField"]}
            />

            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"macroType-label"}></InputLabel>
                <Select
                    id={"macroType"}
                    name={"macroType"}
                    labelId={"macroType-label"}
                    onChange={onChangeInputs}
                    value={query["macroType"]}
                >
                    {(macroTypes || []).map((row: any, i: any) => {
                        return (
                            <MenuItem
                                value={row.value}
                                key={row.value + "-" + i}
                            >
                                {row.name}
                            </MenuItem>
                        );
                    })}
                </Select>
            </FormControl>
            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"visibility-label"}></InputLabel>
                <Select
                    id={"visibility"}
                    name={"visibility"}
                    labelId={"visibility-label"}
                    onChange={onChangeInputs}
                    value={query["visibility"]}
                >
                    {(visibilityTypes || []).map((row: any, i: any) => {
                        return (
                            <MenuItem
                                value={row.value}
                                key={row.value + "-" + i}
                            >
                                {row.name}
                            </MenuItem>
                        );
                    })}
                </Select>
            </FormControl>
            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
