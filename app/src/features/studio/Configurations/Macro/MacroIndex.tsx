import VaporPage from "@vapor/react-custom/VaporPage";
import Vapor<PERSON>eaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import Spinner from "../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useLocation, useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import ArrowLeft from "@vapor/react-icons/ArrowLeft";

export default function MacroIndex() {
    const navigate = useNavigate();

    const { t } = useTranslation();

    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter();

    const { mountedData } = useMountedData();

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const handleClickCallback = (uniqueid: string) => {
        navigate("/macro/update/" + uniqueid);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="macro"
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                query={query}
                setQuery={setQuery}
            />
        );
    };
    const redirectAction = () => {
        navigate("/macro/update");
    };
    const location = useLocation();
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    leftItems={[
                        location.state !== null &&
                            location.state.redirectBack === "true" && (
                                <Button
                                    onClick={() => navigate("/macro/utility")}
                                >
                                    <ArrowLeft />
                                </Button>
                            ),
                    ]}
                    rightItems={[
                        <Button variant="contained" onClick={redirectAction}>
                            {t("Nuova macro")}
                        </Button>,
                    ]}
                    title={t("GESTIONE MACRO")}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
