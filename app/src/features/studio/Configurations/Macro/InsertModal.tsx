import { useTranslation } from "@1f/react-sdk";
import { Box, InputAdornment, Stack } from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
interface InsertModalProps {
    macroLength: number;
    insertMacroData: any;
    setInsertMacroData: React.Dispatch<React.SetStateAction<any>>;
    macroUid: string;
    control: any;
}

export default function InsertModal(props: InsertModalProps) {
    const {
        macroLength,
        insertMacroData,
        setInsertMacroData,
        macroUid,
        control,
    } = props;
    const { t } = useTranslation();

    const handleInputs = (name: any, value: any) => {
        setInsertMacroData({ ...insertMacroData, [name]: value, macroUid });
    };
    //
    const handleCheckboxesChange = (name: any, checked: any) => {
        setInsertMacroData({ ...insertMacroData, [name]: checked, macroUid });
    };

    return (
        <Box>
            <Stack direction="column" gap={2}>
                <FormInput
                    style={{ width: 400 }}
                    name="number"
                    label="Numero"
                    type="number"
                    variant="outlined"
                    formData={insertMacroData}
                    handleOnChange={handleInputs}
                    control={control}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="end">
                                {t("tra") + " 1 " + t("e") + " " + macroLength}
                            </InputAdornment>
                        ),
                    }}
                />
                <FormInput
                    style={{ width: 400 }}
                    name="description"
                    label={"Descrizione"}
                    type="text"
                    variant="outlined"
                    control={control}
                    formData={insertMacroData}
                    handleOnChange={handleInputs}
                />
                <FormInput
                    style={{ width: 400, marginLeft: "0.5em" }}
                    name="updRifs"
                    label=""
                    type="checkbox"
                    variant="outlined"
                    formData={insertMacroData}
                    control={control}
                    options={[
                        {
                            label: t("Riallinea riferimenti"),
                            value: true,
                        },
                    ]}
                    handleOnChange={handleCheckboxesChange}
                />
            </Stack>
        </Box>
    );
}
