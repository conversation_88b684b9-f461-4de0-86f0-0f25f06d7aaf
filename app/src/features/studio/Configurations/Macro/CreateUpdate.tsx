import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack, Grid } from "@vapor/react-material";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../../custom-components/FormInput";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import PageTitle from "../../../../custom-components/PageTitle";
import DeadlineTable from "./DeadlineTable";
import BaseModal from "../../../../custom-components/BaseModal";
import AssociateMacrosModal from "./AssociateMacrosModal";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { Link } from "react-router-dom";
import { getScreenPixel } from "../../../../utilities/ScreenUtils";
const unitOfTime = [
    { value: "0", label: "Giorni liberi" },
    { value: "1", label: "Giorni" },
    { value: "2", label: "Mesi" },
    { value: "3", label: "Anni" },
];

const relationTypes = [
    { value: "1", label: "Relazione" },
    { value: "2", label: "Utenti Singoli" },
    { value: "3", label: "Gruppo Utenti" },
];

const initialFormData = {
    macro_id: "",
    uniqueid: "",
    type: "0",
    name: "",
    categoria: "-1",
};

const MacroCreateUpdate = () => {
    const { uniqueId } = useParams<any>();
    const navigate = useNavigate();

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? "macro/update?uid=" + uniqueId + "&type=0"
            : "macro/update"
    );

    const [deleteOpen, setDeleteOpen] = useState(false);
    const [duplicateOpen, setDuplicateOpen] = useState(false);
    const { t } = useTranslation();
    const [formData, setFormData] = useState<any>(initialFormData);
    const [updateData, setUpdateData] = useState<any>();
    const [openAssociateMacrosModal, setOpenAssociateMacrosModal] = useState<{
        state: boolean;
        index: number;
    }>({
        state: false,
        index: 0,
    });
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const noMacroWarningModalText = t(
        "E' necessario inserire almeno un termine per la macro"
    );

    const [addedAssociations, setAddAssociations] = useState<any[]>([]);
    const [rows, setRows] = useState<any>([]);

    const [selectedRelationType, setSelectedRelationType] = useState<any>("1"); // Initialize with a default value
    const [apiData, setApiData] = useState<any>({
        deadlineStatuses: [],
        deadlineCategories: [],
        categories: [],
        userRelationships: [],
        commitmentType: [],
        group: [],
        typeRel: [],
        unitOfTime: [],
        relationTypes: [],
        macroUsers: [],
        referenceOptions: [],
        loading: true,
    });

    /*
     * form validation schema
     */
    const rowValidation = () =>
        yup.array().of(
            yup.object().shape({
                description: yup.string().required("Descrizione obbligatoria"),
            })
        );

    const schema = yup.object().shape({
        name: yup.string().required(
            t("{{field}} è obbligatorio", {
                field: t("Name"),
            })
        ),
        rows: rowValidation(),
    });

    const methods = useForm({
        resolver: yupResolver(schema),
    });
    const {
        handleSubmit,
        control,
        register,
        setValue,
        getValues,
        formState: { errors },
    } = methods;

    const getDeadlineStatusesRequest = useGetCustom(
        "deadlinestandard/get-deadline-statuses?noTemplateVars=true"
    );
    const getDeadlineCategoriesRequest = useGetCustom(
        "deadlinestandard/get-deadline-categories?noTemplateVars=true"
    );
    const getCategorieRequest = useGetCustom(
        "macro/getcategorie?noTemplateVars=true"
    );
    const getUserReltaionshipsRequest = useGetCustom(
        "macro/getrelazioniutenti?noTemplateVars=true"
    );
    const getCommitmentTypesRequest = useGetCustom(
        "macro/gettipiimpegno?noTemplateVars=true"
    );
    const getGroupRequest = useGetCustom("macro/getgruppi?noTemplateVars=true");
    const getTypeRelRequest = useGetCustom(
        "macro/gettyperel?noTemplateVars=true"
    );
    const getMacroUsersRequest = useGetCustom(
        "macro/getusers?noTemplateVars=true"
    );

    const formatSelectOption = (
        dropdownOptions: any,
        value: string = "id",
        label: string = "name",
        dropdownName: string
    ) => {
        let optionFormatted: any =
            dropdownName == "deadlineStatuses"
                ? [
                      {
                          value: "",
                          label: "-",
                          disabled: true,
                      },
                  ]
                : dropdownName == "deadlineCategories"
                ? [
                      {
                          value: "",
                          label: "-",
                          disabled: true,
                      },
                  ]
                : dropdownName == "commitmentType"
                ? [
                      {
                          value: "-1",
                          label: "Nessuno",
                          disabled: true,
                      },
                  ]
                : dropdownName == "userRelationships"
                ? [
                      {
                          value: "-1",
                          label: "Seleziona relazione",
                          disabled: true,
                      },
                      {
                          value: "0",
                          label: "Intestatario",
                      },
                  ]
                : [];
        (dropdownOptions || []).map((ref: any) => {
            return optionFormatted.push({
                value: ref[value],
                label: ref[label],
            });
        });
        return optionFormatted;
    };

    const fetchRowsData = useCallback(async () => {
        if (uniqueId !== undefined) {
            const { data }: any = await updateMountedRequest.doFetch();
            let result = data.result;
            setUpdateData(data.result);

            let returnOption: any = [
                {
                    value: "",
                    label: "-",
                },
            ];
            const referenceOptions = Array.from(
                { length: result.macroDetails.length },
                (_: any, i: number) => i + 1
            );
            returnOption = (referenceOptions || []).map((refIndex: number) => {
                return {
                    value: refIndex,
                    label: refIndex.toString(),
                };
            });

            setApiData((listItem: any) => ({
                ...listItem,
                referenceOptions: returnOption,
            }));

            setFormData(() => {
                const updatedMacro = { ...result.macro };
                setValue("name", updatedMacro.nome);

                updatedMacro.macro_id = result.macro.id;
                updatedMacro.name = updatedMacro.nome;
                updatedMacro.hearingOnly =
                    updatedMacro.data_udienza == "1" ? true : "";
                updatedMacro.dynamic = updatedMacro.dinamica;
                updatedMacro.visibleMain =
                    updatedMacro.visible == "1" ? "on" : "";
                delete updatedMacro.id;
                delete updatedMacro.nome;
                delete updatedMacro.data_udienza;
                delete updatedMacro.dinamica;
                return updatedMacro;
            });
        }
    }, [uniqueId]);

    useEffect(() => {
        processMacros(
            updateData,
            apiData,
            decodeHTMLEntities,
            setAddAssociations,
            setRows,
            setValue
        );
    }, [updateData, apiData.userRelationships]);
    const processMacros = async (
        result: any,
        apiData: any,
        decodeHTMLEntities: any,
        setAddAssociations: any,
        setRows: any,
        setValue: any
    ) => {
        if (
            result !== "" &&
            result !== undefined &&
            result.macroDetails != undefined
        ) {
            const updatedRows: any = [];
            const macroPromises = result.macroDetails.map(
                async (macro: any, index: any) => {
                    let decodedMacroAssociate = decodeHTMLEntities(
                        macro.macro_associate
                    );
                    let utentisingoli = macro.utentisingoli.replace(
                        /&quot;/g,
                        '"'
                    );

                    const parsedUtentisingoli =
                        utentisingoli !== "" ? JSON.parse(utentisingoli) : [];

                    let userSelectionData = apiData.macroUsers.filter(
                        (thing: any) =>
                            parsedUtentisingoli.includes(thing.value)
                    );

                    let relationSelectionData =
                        apiData.userRelationships.reduce(
                            (ids: any, thing: any) => {
                                if (macro.relation.includes(thing.value)) {
                                    ids.push(thing);
                                }
                                return ids;
                            },
                            []
                        );

                    setAddAssociations((prevAssociations: any) => [
                        ...prevAssociations,
                        JSON.parse(decodedMacroAssociate || "[]"),
                    ]);

                    await updatedRows.push({
                        ...macro,
                        id: index + 1,
                        visible: macro.visible == "1" ? "on" : "",
                        fatturabile: macro.fatturabile == "1" ? "on" : "",
                        importante: macro.importante == "1" ? "on" : "",
                        tiporelazione: macro.tiporelazione,
                        days: parseInt(macro.days),
                        warning: parseInt(macro.warningDays),
                        relationSelection: relationSelectionData,
                        groupSelection:
                            macro.tiporelazione == "3"
                                ? JSON.parse(macro.relation)[0] || ""
                                : "",
                        userSelection: userSelectionData,
                        macro_associate: JSON.parse(
                            decodedMacroAssociate || "[]"
                        ),
                        macros: JSON.parse(decodedMacroAssociate || "[]")
                            .map((m: any) => m.id)
                            .join(","),
                        [`rows.${index}.description`]: macro.description,
                    });

                    setValue(`rows.${index}.description`, macro.description);
                }
            );

            await Promise.all(macroPromises);

            setRows(updatedRows);
        }
    };

    useEffect(() => {
        fetchData();
    }, [uniqueId]);

    const fetchData = async () => {
        const deadlineStatuses: any = await getDeadlineStatusesRequest.doFetch(
            true
        );
        const deadlineCategories: any =
            await getDeadlineCategoriesRequest.doFetch(true);
        const categories: any = await getCategorieRequest.doFetch(true);
        const userRelationships: any =
            await getUserReltaionshipsRequest.doFetch(true);
        const commitmentType: any = await getCommitmentTypesRequest.doFetch(
            true
        );
        const group: any = await getGroupRequest.doFetch(true);
        const typeRel: any = await getTypeRelRequest.doFetch(true);
        const macroUsers: any = await getMacroUsersRequest.doFetch(true);

        setApiData((listItem: any) => ({
            ...listItem,
            deadlineStatuses: formatSelectOption(
                deadlineStatuses.data,
                "value",
                "name",
                "deadlineStatuses"
            ),
            deadlineCategories: formatSelectOption(
                deadlineCategories.data,
                "value",
                "name",
                "deadlineCategories"
            ),
            categories: formatSelectOption(
                categories.data,
                "id",
                "nome",
                "categories"
            ),
            userRelationships: formatSelectOption(
                userRelationships.data,
                "id",
                "nome",
                "userRelationships"
            ),
            commitmentType: formatSelectOption(
                commitmentType.data,
                "id",
                "nome",
                "commitmentType"
            ),
            group: formatSelectOption(group.data, "id", "name", "group"),
            typeRel: formatSelectOption(typeRel.data, "id", "name", "typeRel"),
            unitOfTime: unitOfTime,
            relationTypes: relationTypes,
            macroUsers: formatSelectOption(
                macroUsers.data,
                "id",
                "nomeutente",
                "macroUsers"
            ),

            loading: false,
        }));

        if (uniqueId !== undefined) {
            await fetchRowsData();
        } else {
            let returnOption: any = [
                {
                    value: "",
                    label: "-",
                },
            ];
            const referenceOptions = Array.from(
                { length: rows.length },
                (_: any, i: number) => i + 1
            );
            returnOption = (referenceOptions || []).map((refIndex: number) => {
                return {
                    value: refIndex,
                    label: refIndex.toString(),
                };
            });
            setApiData((listItem: any) => ({
                ...listItem,
                referenceOptions: returnOption,
            }));
        }
    };

    const handleInputChange = (name: any, value: any) => {
        setFormData({ ...formData, [name]: value });
    };

    const handleCheckboxesChange = (name: any, checked: any) => {
        if (name === "hearingOnly") {
            if (checked) {
                setFormData({ ...formData, [name]: checked });
            } else {
                const { [name]: removedField, ...restFormData } = formData;
                setFormData(restFormData);
            }
        }

        if (name === "dynamic") {
            if (checked) {
                setFormData({ ...formData, [name]: "1" });
            } else {
                const { [name]: removedField, ...restFormData } = formData;
                setFormData(restFormData);
            }
        }

        if (name === "visibleMain") {
            if (checked) {
                setFormData({ ...formData, [name]: "on" });
            } else {
                const { [name]: removedField, ...restFormData } = formData;
                setFormData(restFormData);
            }
        }
    };

    const saveRequest = usePostCustom("macro/save");

    const handleMacroSave = async () => {
        if (rows.length > 0) {
            const transformedData: any[] = [];
            rows.forEach((detail: any, index: number) => {
                const indexCount = index + 1;
                const data: any = {
                    [`details[${indexCount}][categoria]`]:
                        detail.categoria ?? "",
                    [`details[${indexCount}][impegno]`]: detail.impegno ?? "",
                    [`details[${indexCount}][macros]`]: detail.macros ?? "",
                    [`details[${indexCount}][relation]`]:
                        detail.relationSelection ?? "",

                    [`details[${indexCount}][stato]`]: detail.stato ?? "",
                    [`details[${indexCount}][tempo]`]: detail.tempo ?? "",
                    [`details[${indexCount}][tiporelazione]`]:
                        detail.tiporelazione ?? "",
                    [`details[${indexCount}][reference]`]:
                        detail.reference ?? "",
                    [`details[${indexCount}][days]`]: detail.days || "0",
                    [`details[${indexCount}][warning]`]: detail.warning || "0",
                    [`details[${indexCount}][description]`]: getValues(
                        `rows.${index}.description`
                    ),
                };
                if (detail.tiporelazione === "2") {
                    data[`selectUser${indexCount}`] = detail[`userSelection`];
                } else if (detail.tiporelazione === "3") {
                    data[`selectGroup${indexCount}`] =
                        detail.groupSelection ?? "";
                }

                if (detail.fatturabile === "on") {
                    data[`details[${indexCount}][fatturabile]`] =
                        detail.fatturabile;
                }
                if (detail.importante === "on") {
                    data[`details[${indexCount}][importante]`] =
                        detail.importante;
                }
                if (detail.visible !== undefined && detail.visible !== "off") {
                    data[`details[${indexCount}][visible]`] = detail.visible;
                }

                transformedData.push(data);
            });

            let result: any = { ...formData };

            transformedData.forEach((data) => {
                result = { ...result, ...data };
            });
            const submitData = new FormData();
            Object.keys(result).forEach((key: string) => {
                if (key.includes("selectUser")) {
                    if (typeof result[key] === "object")
                        Object.keys(result[key]).forEach((key2: string) => {
                            submitData.append(
                                key + "[]",
                                result[key][key2].value
                            );
                        });
                } else if (key.includes("relation")) {
                    submitData.append(
                        key,
                        JSON.stringify(
                            (result[key] || []).map((item: any) => item.value)
                        )
                    );
                } else submitData.append(key, result[key]);
            });

            // If necessary, handle specific fields differently
            submitData.set("visible", submitData.get("visibleMain") || "");
            submitData.delete("visibleMain");

            // Uncomment this line if you want to actually perform the save action
            await saveRequest.doFetch(true, submitData);
            navigate("/macro/macro");
        } else {
            setShowErrorMessage(true);
        }
    };

    const redirectBack = () => {
        navigate("/macro/macro");
    };

    function decodeHTMLEntities(str: any) {
        const decoded = document.createElement("textarea");
        decoded.innerHTML = str;
        return decoded.value;
    }

    const handleDecline = () => {
        setOpenAssociateMacrosModal({
            state: false,
            index: openAssociateMacrosModal.index,
        });
    };
    const handleRemoveAssociation = (index: number, itemIndex: number) => {
        const updatedItems = [...addedAssociations];
        updatedItems[index].splice(itemIndex, 1);
        setAddAssociations(updatedItems);

        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            const macrosArray = updatedRows[index]?.macros
                .split(",")
                .map((id: string) => id.trim());
            macrosArray.splice(itemIndex, 1);
            updatedRows[index] = {
                ...updatedRows[index],
                macros: macrosArray.join(","),
            };
            return updatedRows;
        });
    };
    const deleteRequest = usePostCustom("macro/delete?noTemplateVars=true");
    const duplicatemacro = usePostCustom(
        "macro/duplicatemacro?noTemplateVars=true"
    );
    const deleteAction = () => {
        setDeleteOpen(true);
    };
    const handleDeleteDecline = () => {
        setDeleteOpen(false);
    };

    const handleAgree = async () => {
        setDeleteOpen(false);
        await deleteRequest.doFetch(true, {
            uniqueid: uniqueId,
        });
        navigate("/macro/macro");
    };

    const duplicateAction = () => {
        setDuplicateOpen(true);
    };
    const handleDuplicateDecline = () => {
        setDuplicateOpen(false);
    };

    const handleDuplicateAgree = async () => {
        setDuplicateOpen(false);
        await duplicatemacro.doFetch(true, {
            uniqueid: uniqueId,
        });
        navigate("/macro/macro");
    };

    const screenSize = getScreenPixel();

    const location = useLocation();
    return (
        <VaporPage>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={noMacroWarningModalText}
            />
            <ConfirmModal
                open={deleteOpen}
                handleDecline={handleDeleteDecline}
                handleAgree={handleAgree}
                decline={"No"}
                agree={"Yes, Delete"}
                confirmText={"Are you sure want to Delete"}
                title={"Delete Action"}
            />
            <ConfirmModal
                open={duplicateOpen}
                handleDecline={handleDuplicateDecline}
                handleAgree={handleDuplicateAgree}
                decline={"No"}
                agree={"Yes, Duplicate"}
                confirmText={"Are you sure want to Duplicate"}
                title={"Duplicate Action"}
            />
            {openAssociateMacrosModal && (
                <BaseModal
                    open={openAssociateMacrosModal.state}
                    handleDecline={handleDecline}
                    decline={"Chiudi"}
                    content={
                        <AssociateMacrosModal
                            rowIndex={openAssociateMacrosModal.index}
                            addedAssociations={addedAssociations}
                            setAddAssociations={setAddAssociations}
                            rows={rows}
                            setRows={setRows}
                        />
                    }
                    title={"Associate Macro"}
                    isFooter={false}
                />
            )}
            <PageTitle
                title={
                    (uniqueId !== undefined ? "Modifica " : `Nuovo `) + "macro"
                }
                pathToPrevPage={"/macro/macro"}
            />

            <VaporPage.Section>
                <form onSubmit={handleSubmit(handleMacroSave)}>
                    <Box
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="name"
                                    label="Nome"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    handleOnChange={handleInputChange}
                                />
                                <Grid container spacing={2}>
                                    <Grid item xs={6}>
                                        <FormInput
                                            style={{ width: 400 }}
                                            control={control}
                                            name="categoria"
                                            label="Categoria"
                                            type="select"
                                            variant="outlined"
                                            setValue={setValue}
                                            formData={formData}
                                            options={apiData.categories}
                                            handleOnChange={handleInputChange}
                                        />
                                        <Link
                                            to="/macrocategories"
                                            state={{ from: location.pathname }}
                                            style={{ marginLeft: "0.5em" }}
                                        >
                                            {t("Gestisci categorie macro")}
                                        </Link>
                                    </Grid>
                                </Grid>
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="hearingOnly"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        {
                                            label: t("Solo per udienza"),
                                            value: true,
                                        },
                                    ]}
                                    handleOnChange={handleCheckboxesChange}
                                />
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="dynamic"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        {
                                            label: t("Macro Dinamica"),
                                            value: "1",
                                        },
                                    ]}
                                    handleOnChange={handleCheckboxesChange}
                                />
                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="visibleMain"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    formData={formData}
                                    options={[
                                        { label: t("Visibile"), value: "on" },
                                    ]}
                                    handleOnChange={handleCheckboxesChange}
                                />
                            </Stack>
                        </div>
                    </Box>
                    <Box
                        sx={{
                            width:
                                (screenSize !== null
                                    ? screenSize - 120
                                    : "1000") + "px",
                        }}
                    >
                        <DeadlineTable
                            uniqueId={uniqueId}
                            setOpenAssociateMacrosModal={
                                setOpenAssociateMacrosModal
                            }
                            addedAssociations={addedAssociations}
                            setAddAssociations={setAddAssociations}
                            handleRemoveAssociation={handleRemoveAssociation}
                            rows={rows}
                            setRows={setRows}
                            apiData={apiData}
                            setApiData={setApiData}
                            selectedRelationType={selectedRelationType}
                            setSelectedRelationType={setSelectedRelationType}
                            formData={formData}
                            setFormData={setFormData}
                            errors={errors}
                            control={control}
                            register={register}
                            setValue={setValue}
                            fetchRowsData={fetchRowsData}
                        />
                    </Box>
                    <div>
                        <b>{t("N.B.")}</b>
                        <ul>
                            <li>
                                {t(
                                    "Per aggiungere i termini della macro utilizzare il pulsante"
                                )}
                                <b>{t("Aggiungi")}.</b>
                            </li>
                            <li>
                                {t(
                                    "Il compositore permette di creare le seguenti caratterizzazioni di macro"
                                )}
                                <ul>
                                    <li>
                                        <b>{t("macro positive")}</b>,{" "}
                                        {t(
                                            "con i 'Giorni/Mesi/Anni' di ciascun termine positivi;"
                                        )}
                                    </li>
                                    <li>
                                        <b>{t("macro negative")}</b>,{" "}
                                        {t(
                                            "con i 'Giorni/Mesi/Anni' di ciascun termine negativi;"
                                        )}
                                    </li>
                                    <li>
                                        <b>{t("macro miste")}</b>,{" "}
                                        {t(
                                            "con i 'Giorni/Mesi/Anni' di ciascun termine sia positivi che negativi, in questo caso non sar&agrave; possibile applicare la sospensione feriale. Legge 742/1969."
                                        )}
                                    </li>
                                </ul>
                            </li>
                            <li>
                                {t(
                                    "Il sistema identificata in modo automatico la caratterizzazione della macro."
                                )}
                            </li>
                            <li>
                                {t(
                                    "Il numero massimo ovvero minimo di 'Giorni', per ciascun termine, non deve superare i '300' per le macro;"
                                )}{" "}
                                <b>{t("positive")}</b>{" "}
                                {t("e i '-300' per quelle")}
                                <b>{t("negative")}</b>.{" "}
                                {t(
                                    "Il limite massimo ovvero minimo &egrave; imposto tenendo conto degli eventuali riferimenti per ciascun termine."
                                )}
                            </li>
                            <li>
                                {t("Una volta creata una")}{" "}
                                <b>{t("Macro Dinamica")}</b>,{" "}
                                {t(
                                    "non sarà possibile trasformarla in una macro statica e viceversa."
                                )}
                            </li>
                            <li>
                                {t("Con")} <b>{t("Giorni liberi")}</b>{" "}
                                {t("si intendono i giorni indicati")} <b>+ 1</b>{" "}
                                {t(
                                    "(quindi se si inserisce un numero, verrà salvato il valore incrementato di 1)"
                                )}
                            </li>
                        </ul>
                    </div>
                    <br />
                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={redirectBack}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId !== undefined && (
                            <>
                                <Button
                                    color="error"
                                    variant="outlined"
                                    type="button"
                                    onClick={deleteAction}
                                >
                                    {t("Elimina")}
                                </Button>
                                <Button
                                    color="success"
                                    variant="outlined"
                                    type="button"
                                    onClick={duplicateAction}
                                >
                                    {t("Duplica")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default MacroCreateUpdate;
