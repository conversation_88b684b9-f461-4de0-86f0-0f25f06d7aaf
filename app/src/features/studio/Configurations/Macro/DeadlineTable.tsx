import { List, Button, Grid, ListItemButton, TableBody, TextField, Checkbox, FormControl, MenuItem } from "@vapor/react-material";
import { useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { Close } from "@mui/icons-material";
import FormInput from "../../../../custom-components/FormInput";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import Table from "@mui/material/Table";
import TableCell from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import IconButton from "@mui/material/IconButton";
import DeleteIcon from "@mui/icons-material/Delete";
import BaseModal from "../../../../custom-components/BaseModal";
import InsertModal from "./InsertModal";
import usePostCustom from "../../../../hooks/usePostCustom";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

interface DeadlineTableProps {
    setOpenAssociateMacrosModal: React.Dispatch<React.SetStateAction<any>>;
    addedAssociations: any;
    setAddAssociations: React.Dispatch<React.SetStateAction<any[]>>;
    rows: any;
    setRows: React.Dispatch<React.SetStateAction<any[]>>;
    apiData: any;
    setApiData: React.Dispatch<React.SetStateAction<any[]>>;
    selectedRelationType: any;
    setSelectedRelationType: React.Dispatch<React.SetStateAction<any>>;
    formData: any;
    setFormData: React.Dispatch<React.SetStateAction<any>>;
    errors: any;
    control: any;
    register: any;
    setValue: any;
    handleRemoveAssociation: any;
    uniqueId: any;
    fetchRowsData: any;
}

const DeadlineTable = (props: DeadlineTableProps) => {
    const { setOpenAssociateMacrosModal, addedAssociations, setAddAssociations, rows, setRows, apiData, setApiData, formData, control, setValue, handleRemoveAssociation, fetchRowsData } = props;
    const { t } = useTranslation();

    const insertModal = usePostCustom("macro/insert-macro");

    const [multiselect, setMultiSelect] = useState<any>([]);

    const [openInsertModal, setOpenInsertModal] = useState(false);

    const [insertMacroData, setInsertMacroData] = useState<any>({
        number: "",
        description: "",
        updRifs: "",
        macroUid: ""
    });

    const [showSelectedCommitmentName, setShowSelectedCommitmentName] = useState<any>(
        Array.from({ length: rows.length }, () => ({
            value: "",
            disabled: false
        }))
    );

    const addRow = () => {
        setRows([
            ...rows,
            {
                id: rows.length + 1,
                tempo: "0",
                days: 0,
                warning: 0,
                stato: "0",
                categoria: "",
                tiporelazione: "1",
                relation: [],
                impegno: "-1",
                macros: "",
                visible: "",
                fatturabile: "",
                importante: "",
                description: "",
                reference: "-1"
            }
        ]);
        setShowSelectedCommitmentName([...showSelectedCommitmentName, { value: "", disabled: false }]);
        setAddAssociations((prevAssociations: any) => [...prevAssociations, []]);
        const referenceOptionsData = Array.from({ length: rows.length }, (_: any, i: number) => i + 1);
        let returnOption: any = [
            {
                value: "",
                label: "-"
            }
        ];
        returnOption = (referenceOptionsData || []).map((refIndex: number) => {
            return {
                value: refIndex,
                label: refIndex.toString()
            };
        });
        setApiData({ ...apiData, referenceOptions: returnOption });
    };

    const removeRow = (id: any) => {
        const updatedRows = rows.filter((row: any) => row.id !== id);
        const updatedAssociations: any = addedAssociations.filter((_: any, index: number) => index !== id - 1);
        setRows(updatedRows);
        setAddAssociations(updatedAssociations);
    };

    const handleMultiSelectRelations = (name: any, value: any, index: number) => {
        const updatedValues = [...multiselect];
        updatedValues[index] = value;
        setMultiSelect(updatedValues);

        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            updatedRows[index] = {
                ...updatedRows[index],
                [name]: updatedValues[index].map((item: any) => item)
            };
            return updatedRows;
        });
    };

    const handleInputs = (name: any, value: any, index: number) => {
        const inputValue = value;
        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            updatedRows[index] = {
                ...updatedRows[index],
                [name]: inputValue
            };
            return updatedRows;
        });
        if (name == "description") {
            setValue(`rows.${index}.description`, value);
        }
    };

    const handleCheckbox = (name: any, checked: any, index: number) => {
        const inputValue = checked ? "on" : "off";
        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            updatedRows[index] = {
                ...updatedRows[index],
                [name]: inputValue
            };
            return updatedRows;
        });
    };
    const handleDecline = () => {
        setOpenInsertModal(false);
    };

    const handleInsertMacro = async () => {
        setInsertMacroData({ ...insertMacroData, macroUid: formData.uniqueid });
        await insertModal.doFetch(true, insertMacroData);
        // refresh
        setOpenInsertModal(false);
        fetchRowsData();
    };

    const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
    const checkedIcon = <CheckBoxIcon fontSize="small" />;

    return (
        <>
            {openInsertModal && <BaseModal open={openInsertModal} handleDecline={handleDecline} decline={"Chiudi"} agree={"Inserisci"} handleAgree={handleInsertMacro} content={<InsertModal control={control} macroLength={rows.length} insertMacroData={insertMacroData} setInsertMacroData={setInsertMacroData} macroUid={formData.uniqueid} />} title={"Inserisci termine"} isFooter={true} />}
            <Grid container>
                {formData.uniqueid !== "" ? (
                    <Button
                        className="btn btn-info"
                        variant="contained"
                        onClick={() => {
                            setOpenInsertModal(true);
                        }}
                    >
                        {t("Inserisci termine")}
                    </Button>
                ) : (
                    ""
                )}
            </Grid>
            <div
                style={{
                    overflow: "auto"
                }}
            >
                <Table size="small">
                    <TableHead>
                        <TableRow>
                            <TableCell>#</TableCell>
                            <TableCell>{t("Visible")}</TableCell>
                            <TableCell>{t("Descrizione")}</TableCell>
                            <TableCell>{t("Rif.")}</TableCell>
                            <TableCell>{t("Unità di tempo")}</TableCell>
                            <TableCell>{"Giorni"}</TableCell>
                            <TableCell>{t("Giorni avviso")}</TableCell>
                            <TableCell>{t("Fatturabile")}</TableCell>
                            <TableCell>{t("Importante")}</TableCell>
                            <TableCell>{t("Stato")}</TableCell>
                            <TableCell>{t("Categoria")}</TableCell>
                            <TableCell>{t("Relazione")}</TableCell>
                            <TableCell></TableCell>
                            <TableCell>{t("Tipo di impegno")}</TableCell>
                            <TableCell>{t("Associazioni")}</TableCell>
                            <TableCell>
                                <Button className="btn btn-info btn-xs" variant="contained" onClick={addRow}>
                                    {t("Aggiungi")}
                                </Button>
                            </TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {rows.length > 0 ? (
                            (rows || []).map((row: any, index: number) => {
                                return (
                                    row !== undefined && (
                                        <TableRow key={row.detailOrder + "rows"}>
                                            <TableCell>{row.detailOrder ?? index + 1}</TableCell>
                                            <TableCell>
                                                <FormInput
                                                    style={{
                                                        width: 50
                                                    }}
                                                    control={control}
                                                    name={`visible`}
                                                    label={""}
                                                    type="checkbox"
                                                    variant="outlined"
                                                    setValue={setValue}
                                                    formData={row}
                                                    options={[
                                                        {
                                                            label: "",
                                                            value: "on"
                                                        }
                                                    ]}
                                                    handleOnChange={handleCheckbox}
                                                    customIndex={index}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput
                                                    label=""
                                                    style={{
                                                        width: 200
                                                    }}
                                                    control={control}
                                                    type="text"
                                                    handleOnChange={handleInputs}
                                                    customIndex={index}
                                                    name={`rows.${index}.description`}
                                                    setValue={setValue}
                                                    formData={row}
                                                    className="form-control-sm"
                                                />
                                            </TableCell>
                                            {row.id !== 1 ? (
                                                <TableCell width="10%">
                                                    <FormInput
                                                        control={control}
                                                        type="select"
                                                        style={{
                                                            width: "200px"
                                                        }}
                                                        handleOnChange={handleInputs}
                                                        customIndex={index}
                                                        label={""}
                                                        name={`reference`}
                                                        options={apiData.referenceOptions}
                                                        setValue={setValue}
                                                        formData={row}
                                                    />
                                                </TableCell>
                                            ) : (
                                                <TableCell></TableCell>
                                            )}
                                            <TableCell>
                                                <FormInput control={control} type="select" style={{ width: "200px" }} handleOnChange={handleInputs} customIndex={index} label="" name={`tempo`} options={apiData.unitOfTime} setValue={setValue} formData={row} />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput
                                                    control={control}
                                                    type="number"
                                                    style={{
                                                        width: 100
                                                    }}
                                                    handleOnChange={handleInputs}
                                                    customIndex={index}
                                                    label=""
                                                    name={`days`}
                                                    setValue={setValue}
                                                    formData={row}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput
                                                    control={control}
                                                    type="number"
                                                    style={{
                                                        width: 100
                                                    }}
                                                    handleOnChange={handleInputs}
                                                    customIndex={index}
                                                    label=""
                                                    name={`warning`}
                                                    setValue={setValue}
                                                    formData={row}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput
                                                    style={{
                                                        width: 50
                                                    }}
                                                    control={control}
                                                    name={`fatturabile`}
                                                    label=""
                                                    type="checkbox"
                                                    handleOnChange={handleCheckbox}
                                                    customIndex={index}
                                                    variant="outlined"
                                                    setValue={setValue}
                                                    formData={row}
                                                    options={[
                                                        {
                                                            label: "",
                                                            value: "on"
                                                        }
                                                    ]}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput
                                                    style={{
                                                        width: 50
                                                    }}
                                                    control={control}
                                                    name={`importante`}
                                                    label={""}
                                                    type="checkbox"
                                                    handleOnChange={handleCheckbox}
                                                    customIndex={index}
                                                    variant="outlined"
                                                    setValue={setValue}
                                                    formData={row}
                                                    options={[
                                                        {
                                                            label: "",
                                                            value: "on"
                                                        }
                                                    ]}
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput control={control} type="select" style={{ width: "200px" }} handleOnChange={handleInputs} customIndex={index} label={""} name={`stato`} options={apiData.deadlineStatuses} setValue={setValue} formData={row} />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput control={control} type="select" style={{ width: "200px" }} handleOnChange={handleInputs} customIndex={index} label={""} name={`categoria`} options={apiData.deadlineCategories} setValue={setValue} formData={row} />
                                            </TableCell>
                                            <TableCell>
                                                <FormInput control={control} type="select" style={{ width: "200px" }} handleOnChange={handleInputs} customIndex={index} label={""} name={`tiporelazione`} options={apiData.relationTypes} setValue={setValue} formData={row} />
                                            </TableCell>
                                            <TableCell>
                                                {row[`tiporelazione`] === "1" || row[`tiporelazione`] === "" ? (
                                                    <FormControl
                                                        sx={{
                                                            width: 250
                                                        }}
                                                    >
                                                        <CustomAutocomplete
                                                            multiple
                                                            disableCloseOnSelect
                                                            options={apiData.userRelationships}
                                                            isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                                                            renderOption={(props: any, option: any, { selected }: any) => {
                                                                const { key, ...optionProps } = props;
                                                                return (
                                                                    <MenuItem {...optionProps} key={option.value} disabled={option.disabled}>
                                                                        <Checkbox
                                                                            icon={icon}
                                                                            checkedIcon={checkedIcon}
                                                                            style={{
                                                                                marginRight: 8
                                                                            }}
                                                                            checked={selected}
                                                                        />
                                                                        {option.label}
                                                                    </MenuItem>
                                                                );
                                                            }}
                                                            renderInput={(params: any) => <TextField {...params} placeholder={t("")} />}
                                                            onChange={(_event: any, newValue: any[]) => handleMultiSelectRelations("relationSelection", newValue, index)}
                                                            defaultValue={() => row["relationSelection"] || []}
                                                            value={row["relationSelection"]}
                                                            sx={{
                                                                width: "100%"
                                                            }}
                                                        />
                                                    </FormControl>
                                                ) : (
                                                    ""
                                                )}

                                                {row[`tiporelazione`] === "2" && (
                                                    <FormControl
                                                        sx={{
                                                            width: 250
                                                        }}
                                                    >
                                                        <CustomAutocomplete
                                                            sx={{
                                                                width: "100%"
                                                            }}
                                                            multiple
                                                            disableCloseOnSelect
                                                            options={apiData.macroUsers}
                                                            isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                                                            renderOption={(props: any, option: any, { selected }: any) => {
                                                                const { key, ...optionProps } = props;
                                                                return (
                                                                    <li key={option.value} {...optionProps}>
                                                                        <Checkbox
                                                                            icon={icon}
                                                                            checkedIcon={checkedIcon}
                                                                            style={{
                                                                                marginRight: 8
                                                                            }}
                                                                            checked={selected}
                                                                        />
                                                                        {option.label}
                                                                    </li>
                                                                );
                                                            }}
                                                            renderInput={(params: any) => <TextField {...params} placeholder={t("")} />}
                                                            onChange={(_event: any, newValue: any[]) => handleMultiSelectRelations("userSelection", newValue, index)}
                                                            defaultValue={() => row["userSelection"] || []}
                                                            value={row["userSelection"]}
                                                        />
                                                    </FormControl>
                                                )}
                                                {row[`tiporelazione`] === "3" && (
                                                    <FormControl
                                                        sx={{
                                                            width: 250
                                                        }}
                                                    >
                                                        <FormInput
                                                            control={control}
                                                            type="select"
                                                            style={{
                                                                width: "200px"
                                                            }}
                                                            handleOnChange={handleInputs}
                                                            customIndex={index}
                                                            label={""}
                                                            name={`groupSelection`}
                                                            options={apiData.group}
                                                            setValue={setValue}
                                                            formData={row}
                                                        />
                                                    </FormControl>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <FormInput control={control} type="select" style={{ width: "200px" }} handleOnChange={handleInputs} customIndex={index} label={""} name={`impegno`} options={apiData.commitmentType} setValue={setValue} formData={row} />
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="contained"
                                                    type="button"
                                                    onClick={() =>
                                                        setOpenAssociateMacrosModal({
                                                            state: true,
                                                            index: index
                                                        })
                                                    }
                                                >
                                                    {t("Aggiungi")}
                                                </Button>
                                                {addedAssociations[index] !== undefined && Object.keys(addedAssociations[index]).length > 0 && (
                                                    <List
                                                        sx={{
                                                            width: "100%",
                                                            fontSize: "12px"
                                                        }}
                                                    >
                                                        {addedAssociations[index] !== undefined &&
                                                            Object.keys(addedAssociations[index])?.map((item: any) => {
                                                                const labelId = `checkbox-list-label-${item}`;
                                                                let data = addedAssociations[index][item];
                                                                if (data.nome !== undefined)
                                                                    return (
                                                                        <ListItem
                                                                            key={data.nome}
                                                                            secondaryAction={
                                                                                <IconButton edge="end" aria-label="delete" onClick={() => handleRemoveAssociation(index, item)}>
                                                                                    <DeleteIcon />
                                                                                </IconButton>
                                                                            }
                                                                            disablePadding
                                                                            divider
                                                                        >
                                                                            <ListItemButton>
                                                                                <ListItemText id={labelId} primary={data.nome} />
                                                                            </ListItemButton>
                                                                        </ListItem>
                                                                    );
                                                                else
                                                                    return (
                                                                        <ListItem
                                                                            key={data.title}
                                                                            secondaryAction={
                                                                                <IconButton edge="end" aria-label="delete" onClick={() => handleRemoveAssociation(index, item)}>
                                                                                    <DeleteIcon />
                                                                                </IconButton>
                                                                            }
                                                                            disablePadding
                                                                            divider
                                                                        >
                                                                            <ListItemButton>
                                                                                <ListItemText id={labelId} primary={data.title} />
                                                                            </ListItemButton>
                                                                        </ListItem>
                                                                    );
                                                            })}
                                                    </List>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <Button onClick={() => removeRow(row.id)} variant="outlined" color="error" type="button">
                                                    <Close />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    )
                                );
                            })
                        ) : (
                            <TableRow>
                                <TableCell colSpan={16} align="center">
                                    No records
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </>
    );
};
export default DeadlineTable;
