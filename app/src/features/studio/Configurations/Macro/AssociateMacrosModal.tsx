import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { Tabs, Tab } from "@vapor/react-extended";
import { CustomTabPanel } from "../../../../helpers/customTabPanel";
import { Box, Typography, TextField } from "@vapor/react-material";
import { CustomInfoBox } from "../../../../custom-components/CustomInfoBox";
import useGetCustom from "../../../../hooks/useGetCustom";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";
interface AssociateMacrosModalProps {
    rowIndex: number;
    addedAssociations: any;
    setAddAssociations: React.Dispatch<React.SetStateAction<any[]>>;
    rows: any;
    setRows: React.Dispatch<React.SetStateAction<any[]>>;
}

export default function AssociateMacrosModal(props: AssociateMacrosModalProps) {
    const { rowIndex, setAddAssociations, setRows } = props;
    const { t } = useTranslation();
    const [searchResult, setSearchResult] = useState<any>([]);
    const [searchDocumentsResult, setSearchDocumentsResult] = useState<any>([]);
    //search macros

    const getMacroRequest = useGetCustom("default/macro/get-macros?noTemplateVars=true");
    const getModelRequest = useGetCustom("default/macro/get-models?noTemplateVars=true");
    const debounceSearchMacros = debounce(async (value: string) => {
        const { data }: any = await getMacroRequest.doFetch(true, {
            q: value,
            increment: 0,
            uniqueid: ""
        });
        setSearchResult(data);
    }, 500);

    const memoizedSearchMacros = useCallback(debounceSearchMacros, [debounceSearchMacros]);

    const handleMacrosSearch = (e: any) => {
        memoizedSearchMacros(e.target.value);
    };

    const handleSelectedMacroSearch = (_: any, value: any) => {
        setAddAssociations((prevAssociations: any) => {
            return prevAssociations.map((associations: any, index: number) => {
                if (index === rowIndex) {
                    return [...associations, value]; // Add the new value to the specific row's associations
                }
                return associations; // Keep other rows' associations as they are
            });
        });
        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            updatedRows[rowIndex] = {
                ...updatedRows[rowIndex],
                macros: updatedRows[rowIndex].macros ? `${updatedRows[rowIndex].macros},${value.id}` : value.id
            };
            return updatedRows;
        });
    };

    //search documnts
    const debounceSearchDocuments = debounce(async (value: string) => {
        const { data }: any = await getModelRequest.doFetch(true, {
            q: value,
            increment: 0,
            uniqueid: ""
        });
        setSearchDocumentsResult(data);
    }, 500);

    const memoizedSearchDocuments = useCallback(debounceSearchDocuments, [debounceSearchDocuments]);

    const handleDocumentsSearch = (e: any) => {
        memoizedSearchDocuments(e.target.value);
    };

    useEffect(() => {
        memoizedSearchDocuments("");
        memoizedSearchMacros("");
    }, []);
    const handleSelectedDocumentsSearch = (_: any, value: any) => {
        setAddAssociations((prevAssociations: any) => {
            return prevAssociations.map((associations: any, index: number) => {
                if (index === rowIndex) {
                    return [...associations, value]; // Add the new value to the specific row's associations
                }
                return associations; // Keep other rows' associations as they are
            });
        });
        setRows((prevRows: any) => {
            const updatedRows = [...prevRows];
            updatedRows[rowIndex] = {
                ...updatedRows[rowIndex],
                macros: updatedRows[rowIndex].macros ? `${updatedRows[rowIndex].macros},${value.id}` : value.id
            };
            return updatedRows;
        });
    };

    const a11yProps = (index: number) => {
        return {
            id: `simple-tab-${index}`,
            "aria-controls": `simple-tabpanel-${index}`
        };
    };
    const [value, setValue] = useState(0);
    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        console.log(event);
        setValue(newValue);
    };
    return (
        <Box>
            <Tabs value={value} onChange={handleChange} size="extraSmall" variant="standard">
                <Tab label="Macro" {...a11yProps(0)} />
                <Tab label={t("Modelli di documento")} {...a11yProps(1)} />
            </Tabs>
            <CustomTabPanel value={value} index={0}>
                <CustomInfoBox backgroundColor={"#ADD8E6"}>
                    <Typography variant="body" gutterBottom component="div">
                        {t("Selezionare una modello dall'elenco per associarlo all'impegno.")}
                    </Typography>
                    <Typography variant="body" gutterBottom component="div">
                        {t("L'operazione può essere ripetuta per associare più modelli.")}
                    </Typography>
                </CustomInfoBox>
                <CustomAutocomplete
                    disableClearable={true}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 400
                        }
                    }}
                    options={searchResult}
                    onChange={(event: any, value: any) => handleSelectedMacroSearch(event, value)}
                    getOptionLabel={(option: any) => {
                        return option.nome;
                    }}
                    renderInput={(params: any) => <TextField {...params} onChange={handleMacrosSearch} placeholder="Macro" />}
                />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <CustomInfoBox backgroundColor={"#ADD8E6"}>
                    <Typography variant="body" gutterBottom component="div">
                        {t("Selezionare una modello dall'elenco per associarla all'impegno.")}
                    </Typography>
                    <Typography variant="body" gutterBottom component="div">
                        {t("L'operazione può essere ripetuta per associare più modelli.")}
                    </Typography>
                </CustomInfoBox>
                <CustomAutocomplete
                    disableClearable={true}
                    sx={{
                        "& .MuiTextField-root": {
                            m: 1,
                            width: 400
                        }
                    }}
                    options={searchDocumentsResult}
                    onChange={(event: any, value: any) => handleSelectedDocumentsSearch(event, value)}
                    getOptionLabel={(option: any) => {
                        return option.title;
                    }}
                    renderInput={(params: any) => <TextField {...params} onChange={handleDocumentsSearch} placeholder="Model" />}
                />
            </CustomTabPanel>
        </Box>
    );
}
