import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { getCertificateGrid } from "../../../../utilities/certificate/gridColumn";
import { useTranslation } from "@1f/react-sdk";

const defaultQuery: any = {
    page: 0,
    pageSize: 10,
    sortColumn: "date",
    sortOrder: "asc",
};

export const useCertificateData = () => {
    const [data, setData] = useState<any>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });
    const [id, setId] = useState<string>("");
    const [open, setOpen] = useState<boolean>(false);
    const [type, setType] = useState<string>("");
    const [query, setQuery] = useState<any>({
        ...defaultQuery,
    });

    const { t } = useTranslation();

    const getCertificateTemplate = useGetCustom(
        "certificates/list?noTemplateVars=true&"
    );

    const redirectPin = async (uniqueid: string, type: string) => {
        setId(uniqueid);
        setType(type);
        setOpen(true);
    };

    const fetchData = async () => {
        const [response, columns]: any = await Promise.all([
            getCertificateTemplate.doFetch(true, query),
            getCertificateGrid(t, redirectPin),
        ]);

        const { currentPage } = response.data;

        setData({
            ...data,
            rows: currentPage,
            totalRows: parseInt(response?.data.totalRows),
            columns,
            page: parseInt(query.page),
            pageSize: parseInt(query.pageSize),
        });
    };
    useEffect(() => {
        fetchData();
    }, [query]);

    return {
        data,
        open,
        setOpen,
        id,
        type,
        loading: getCertificateTemplate.loading,
        fetchData,
        query,
        setQuery,
    };
};

export const useGetOneCeftificate = (id: string) => {
    const [data, setData] = useState<any>({});

    const getOneCertificateTemplate = useGetCustom(
        "certificates/getrowdata?noTemplateVars=true&",
        { id: id }
    );

    const fetchData = async () => {
        const response: any = await getOneCertificateTemplate.doFetch(true);
        if (response.data) {
            setData(response?.data?.form);
        }
    };

    useEffect(() => {
        if (id) {
            fetchData();
        }
    }, [id]);

    return { data, loading: getOneCertificateTemplate.loading, fetchData };
};
