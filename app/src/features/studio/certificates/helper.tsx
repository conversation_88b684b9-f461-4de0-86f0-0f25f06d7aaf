import { StatusBadge } from "@vapor/react-custom";
import { Button, Grid } from "@mui/material";

//CERTIFICATE
export const statusCertificate = (row: any) => {
    if (row["status"] === "0") {
        return <StatusBadge bgColor="gray" label="Inserito" />;
    } else if (row["status"] === "1") {
        return <StatusBadge bgColor="blue" label="Da attivare" />;
    } else if (row["status"] === "2") {
        return <StatusBadge bgColor="green" label="Attivo" />;
    } else if (row["status"] === "3" || row["status"] === "5") {
        return (
            <StatusBadge
                bgColor="red"
                label={
                    row["status_description"] === null
                        ? "Null"
                        : row["status_description"]
                }
            />
        );
    } else if (row["status"] === "4") {
        return <StatusBadge bgColor="gray" label="Revocato" />;
    } else if (row["status"] === "6") {
        return <StatusBadge bgColor="yellow" label="Sospeso" />;
    }
};

export const changePinCertificate = (row: any, t: any, redirectPin?: any) => {
    return row["status"] === "2" && typeof redirectPin === "function" ? (
        <Grid container spacing={1} className="m-1">
            <Grid item xs={12} md={6}>
                <Button
                    variant="contained"
                    color="info"
                    size="small"
                    onClick={() => redirectPin(row["uniqueid"], "change")}
                >
                    {t("Cambio PIN")}
                </Button>
            </Grid>
            <Grid item xs={12} md={6}>
                <Button
                    variant="contained"
                    color="secondary"
                    size="small"
                    onClick={() => redirectPin(row["uniqueid"], "reset")}
                >
                    {t("Reset PIN")}
                </Button>
            </Grid>
        </Grid>
    ) : (
        ""
    );
};
