import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    Link,
    Typography,
} from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import {
    useCertificateData,
    useGetOneCeftificate,
} from "./hooks/useCertificatesRequest";
import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import AddIcon from "@mui/icons-material/Add";
import ChangeResetPin from "./components/ChangePinDialog";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";

const CertificatePage = () => {
    const { data, open, setOpen, type, id, loading, query, setQuery }: any =
        useCertificateData();
    const { data: oneRecord } = useGetOneCeftificate(id);

    const { t } = useTranslation();
    const navigate = useNavigate();

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };
    
    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="certificates"
                setQuery={setQuery}
                columns={data?.columns}
                data={data?.rows}
                page={data?.page}
                totalRows={data?.totalRows}
                pageSize={data?.pageSize}
                loading={loading}
                onClickKey="id"
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <Box>
            <ChangeResetPin
                open={open}
                setOpen={setOpen}
                oneRecord={oneRecord}
                type={type}
            />
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button
                            variant="contained"
                            onClick={() => navigate("/account/account")}
                            sx={{ mr: 3 }}
                        >
                            <AddIcon /> {t("Acquista certificato")}
                        </Button>,
                    ]}
                    title={t("ELENCO CERTIFICATI")}
                />
                <VaporPage.Section>
                    <Card>
                        <CardContent>
                            <Typography variant="h5" gutterBottom>
                                {t("Video guide")}
                            </Typography>
                            <ul>
                                <li>
                                    <Typography variant="body1" gutterBottom>
                                        <Link
                                            href="https://youtu.be/n6l5GlSlHGY"
                                            target="_blank"
                                            style={{ textDecoration: "none" }}
                                        >
                                            {t("Effettuare")}
                                        </Link>
                                        &nbsp;
                                        {t(
                                            "la richiesta per il rilascio di un nuovo certificato di firma remota."
                                        )}
                                    </Typography>
                                </li>
                                <li>
                                    <Typography variant="body1" gutterBottom>
                                        <Link
                                            href="https://youtu.be/jDmcPdFjb_k"
                                            target="_blank"
                                            style={{ textDecoration: "none" }}
                                        >
                                            {t("Attivare")}
                                        </Link>
                                        &nbsp;
                                        {t(
                                            "il certificato di firma remota (N.B. Durante la procedura di attivazione assicurarsi di scaricare la busta segreta contenente il PUK del certificato)."
                                        )}
                                    </Typography>
                                </li>
                                <li>
                                    <Typography variant="body1" gutterBottom>
                                        <Link
                                            href="https://youtu.be/t1Z473XmN7A"
                                            target="_blank"
                                            style={{ textDecoration: "none" }}
                                        >
                                            {t("Configurare")}
                                        </Link>
                                        &nbsp;{t("la firma remota in Netlex.")}
                                    </Typography>
                                </li>
                            </ul>
                        </CardContent>
                    </Card>
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </Box>
    );
};
export default CertificatePage;
