import { useState, useEffect } from 'react';
import { <PERSON>ton, TextField, Dialog, DialogActions, DialogContent, Typography, DialogTitle } from '@vapor/react-material';
import { useTranslation } from "@1f/react-sdk";
import * as Yup from 'yup';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

import usePostCustom from '../../../../hooks/usePostCustom';
import SpinnerButton from '../../../../custom-components/SpinnerButton';
import ToastNotification from '../../../../custom-components/ToastNotification';

export default function ChangeResetPin({ open, setOpen, oneRecord, type }: any) {
    const [loading, setLoading] = useState<boolean>(false);
    const [toastNotification, setToastNotification] = useState<boolean>(false);
    const [responseMessage, setResponseMessage] = useState<any>([]);

    const { t } = useTranslation();
    const changePinRequest = usePostCustom('certificates/change-pin?noTemplateVars=true&');

    const validationSchema = Yup.object().shape({
        pin: Yup.string()
            .required(`${type === "change" ? "PIN" : "PUK"} obbligatorio`),
        passwd: Yup.string()
            .matches(/^([a-zA-Z0-9]{8})$/, t('Per PIN solo numeri e lettere'))
            .required('PIN obbligatorio'),
        passwdConfirm: Yup.string()
            .oneOf([Yup.ref('passwd')], t('Il PIN e la conferma devono corrispondere'))
            .required(t('Conferma del PIN obbligatoria')),
    });

    const { control, handleSubmit, formState: { errors }, reset } = useForm({
        resolver: yupResolver(validationSchema),
    });

    const onSubmit = async (data: any) => {
        setLoading(true);
        const response: any = await changePinRequest.doFetch(true, { ...data, cUid: oneRecord?.uniqueid, changePIN: type === "change" ? true : false });
        setResponseMessage(response?.data)
        setLoading(false);
        setToastNotification(true);
        handleClose();
    };

    const handleClose = () => {
        setOpen(false);
        reset();
    };

    useEffect(() => {
        if (!open) {
            reset();
        }
    }, [open, reset]);

    return (
        <>
            <ToastNotification
                showNotification={toastNotification}
                setShowNotification={setToastNotification}
                severity={responseMessage?.error ? "error" : "success"}
                text={t(`${responseMessage?.error ? responseMessage.description : 'Il PIN è stato cambiato'}`)}
            />
            <Dialog
                open={open}
                onClose={handleClose}
                PaperProps={{
                    component: 'form',
                    onSubmit: handleSubmit(onSubmit),
                }}
            >
                <DialogTitle>{t("Gestione PIN")}</DialogTitle>
                <DialogContent>
                    <Typography variant="body1" sx={{ pb: 1 }} gutterBottom> {t("Codice fiscale")} : <span style={{ fontWeight: "bold" }}> {oneRecord && oneRecord?.fiscal_code}</span> </Typography>
                    <Typography variant="body1" sx={{ pb: 2 }} gutterBottom> {t("Numero cellulare")} : <span style={{ fontWeight: "bold" }}>{oneRecord && oneRecord?.mobile}</span> </Typography>
                    <Controller
                        name="pin"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <TextField
                                {...field}
                                autoFocus
                                id="pin"
                                type="password"
                                label={type === "change" ? "PIN" : "PUK"}
                                sx={{ pb: 1 }}
                                error={!!errors.pin}
                                helperText={errors.pin ? errors.pin.message : ''}
                            />
                        )}
                    />
                    <Controller
                        name="passwd"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <TextField
                                {...field}
                                id="passwd"
                                type="password"
                                label={t("Nuovo PIN")}
                                sx={{ pb: 1 }}
                                error={!!errors.passwd}
                                helperText={errors.passwd ? errors.passwd.message : ''}
                            />
                        )}
                    />
                    <Controller
                        name="passwdConfirm"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <TextField
                                {...field}
                                id="passwdConfirm"
                                type="password"
                                label={t("Conferma nuovo PIN")}
                                sx={{ pb: 1 }}
                                error={!!errors.passwdConfirm}
                                helperText={errors.passwdConfirm ? errors.passwdConfirm.message : ''}
                            />
                        )}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>{t("Annulla")}</Button>
                    <SpinnerButton type="submit" variant="contained" label={t("Conferma")} isLoading={loading} />
                </DialogActions>
            </Dialog>
        </>
    );
}