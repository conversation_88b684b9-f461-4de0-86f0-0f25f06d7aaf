import { useEffect, useState } from "react";
import { StatusBadge } from "@vapor/react-custom";
import {
    Grid,
    Typography,
    Divider,
    Skeleton,
    LinearProgress,
    Box,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";

import { useConfigs } from "../../../../store/ConfigStore";
import useGetCustom from "../../../../hooks/useGetCustom";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";

const Index = () => {
    const [accountData, setAccountData] = useState<any>(null);
    const [configsData, setConfigsData] = useState<any>(null);
    const [loading, setLoading] = useState<boolean>(true);

    const { t } = useTranslation();
    const { configs }: any = useConfigs();

    const getAccount = useGetCustom("account/account");

    useEffect(() => {
        if (configs) setConfigsData(configs);
    }, [configs]);

    useEffect(() => {
        const fetchAccountData = async () => {
            try {
                setLoading(true);
                const response: any = await getAccount.doFetch(true);
                setAccountData((response && response?.data) || null);
            } catch (error) {
                console.error("Error fetching account data:", error);
            } finally {
                setLoading(false);
            }
        };
        fetchAccountData();
    }, []);

    const DetailsGridRows = ({
        label,
        value,
    }: {
        t: any;
        label: string;
        value: string;
    }) => (
        <Grid container item spacing={1}>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <Typography
                    sx={{ fontWeight: "bold", textAlign: "right", m: 0.5 }}
                >
                    {label} :
                </Typography>
            </Grid>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <Typography sx={{ m: 0.5 }}>
                    {value ? (
                        value
                    ) : loading ? (
                        <>
                            <Skeleton
                                variant="text"
                                sx={{ m: 0.5, width: 100 }}
                            />
                        </>
                    ) : (
                        "-"
                    )}
                </Typography>
            </Grid>
        </Grid>
    );

    const SpazioCloudData = ({
        label,
        value,
    }: {
        t: any;
        label: string;
        value: string;
    }) => (
        <Grid container item spacing={1}>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <Typography
                    sx={{ fontWeight: "bold", textAlign: "right", m: 0.5 }}
                >
                    {label} :
                </Typography>
            </Grid>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <Box sx={{ display: "flex", alignItems: "center", mt: 0.9 }}>
                    <Box sx={{ width: "100%", mr: 1 }}>
                        <LinearProgress
                            variant="determinate"
                            value={parseInt(value)}
                        />
                    </Box>
                    <Box sx={{ minWidth: 35 }}>
                        <Typography
                            variant="body2"
                            color="text.secondary"
                        >{`${Math.round(parseInt(value))}%`}</Typography>
                    </Box>
                </Box>
            </Grid>
        </Grid>
    );

    const DetailCustom = ({
        label,
        value,
        color,
    }: {
        t: any;
        label: string;
        value: string;
        color: string;
    }) => (
        <Grid container item spacing={1}>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <Typography
                    sx={{ fontWeight: "bold", textAlign: "right", m: 0.8 }}
                >
                    {label} :
                </Typography>
            </Grid>
            <Grid item xs={6} sm={6} md={6} lg={6} xl={6}>
                <StatusBadge
                    sx={{ m: 0.8 }}
                    bgColor={color}
                    label={
                        value ? (
                            value
                        ) : loading ? (
                            <>
                                <Skeleton
                                    variant="text"
                                    sx={{ m: 0.5, width: 100 }}
                                />
                            </>
                        ) : (
                            "-"
                        )
                    }
                />
            </Grid>
        </Grid>
    );

    return (
        <VaporPage>
            <PageTitle
                title={t("GESTIONE ABBONAMENTO")}
                pathToPrevPage="/certificates/certificates"
            />
            <VaporPage.Section>
                <Grid container spacing={3}>
                    {accountData?.retailer.private_negotiation == 1 ||
                    accountData?.customerContractStatus == 3 ? (
                        <>
                            <Grid item md={12} sm={12} xs={12}>
                                <Grid
                                    container
                                    spacing={2}
                                    sx={{ bgcolor: "#b4e0f0", p: 1 }}
                                >
                                    <Grid item md={12} sm={12} xs={12}>
                                        <Typography
                                            variant="h6"
                                            sx={{
                                                fontWeight: "bold",
                                                color: "#2f75a9",
                                            }}
                                        >
                                            {t(
                                                "Per modificare il suo abbonamento Netlex o aggiungere nuove funzionalità è necessario contattare il rivenditore a uno dei seguenti recapiti"
                                            )}
                                        </Typography>
                                        <Divider
                                            className="MuiDivider-VaporLight"
                                            sx={{ width: "85%", mt: 0.8 }}
                                        />
                                    </Grid>
                                    <Grid item md={4} sm={6} xs={12}>
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Ragione sociale")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_company
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Partita iva")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_vat_number
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Email")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_email
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Indirizzo web")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_website
                                            }
                                        />
                                    </Grid>
                                    <Grid item md={4} sm={6} xs={12}>
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Telefono")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_phone
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Indirizzo")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_address
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Cap")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_post_code
                                            }
                                        />
                                        <DetailsGridRows
                                            t={t}
                                            label={t("Città")}
                                            value={
                                                accountData?.retailer
                                                    ?.retailer_city
                                            }
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </>
                    ) : (
                        ""
                    )}
                    {(parseInt(accountData?.customerContractStatus) > 0 &&
                        parseInt(accountData?.customerContractStatus) < 3) ||
                    accountData?.pendingContracts?.length > 0 ? (
                        <>
                            <Grid item md={12} sm={12} xs={12}>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: "bold",
                                        color: "#2f75a9",
                                    }}
                                >
                                    {t("Nuovo contratto")}
                                </Typography>
                                <Divider
                                    className="MuiDivider-VaporLight"
                                    sx={{ width: "85%", mt: 0.8 }}
                                />
                            </Grid>
                            <Grid item md={12} sm={12} xs={12}>
                                <Grid
                                    container
                                    spacing={2}
                                    sx={{ bgcolor: "#b4e0f0", p: 1 }}
                                >
                                    <Grid item md={12} sm={12} xs={12}>
                                        <Typography>
                                            {t(
                                                `Nel 2021 la società Netlex srl è stata acquisita definitivamente da Teamsystem spa, pertanto sarà necessario, per tutti gli ordini effettuati `
                                            )}
                                            <b>
                                                {t(
                                                    " Fino alla vostra prossima estensione"
                                                )}
                                            </b>
                                            {t(
                                                ` Della distribuzione, firmare ed inviare il contratto generato sull'ordine stesso .`
                                            )}
                                            <br />
                                            <br />
                                            {t(
                                                `Una volta che i contratti saranno verificati ed accettati dal reparto amministrativo, gli ordini in sospeso saranno accettati dal sistema`
                                            )}
                                        </Typography>
                                        <br />
                                        {accountData?.ownerUserData &&
                                        accountData?.ownerUserData
                                            ?.codicefiscale ==
                                            accountData?.loggedLawyerCF ? (
                                            <Typography>
                                                <b>
                                                    {" "}
                                                    {t(
                                                        `L'intestatario di questa distribuzione`
                                                    )}{" "}
                                                    {accountData?.nomeutente} -{" "}
                                                    {accountData?.codicefiscale}{" "}
                                                    {t(
                                                        "Dovrà accedere a questa pagina e, tramite alcuni semplici passaggi, fornire i nuovi contratti firmati"
                                                    )}
                                                </b>
                                            </Typography>
                                        ) : !accountData?.ownerUserData &&
                                          accountData?.retailer
                                              ?.retailer_code &&
                                          accountData?.retailer
                                              ?.retailer_code !=
                                              "NET2012LEX" ? (
                                            <Typography>
                                                <b>
                                                    {t(
                                                        "Contatta il tuo rivenditore per ricevere supporto ed essere guidato nella procedura"
                                                    )}
                                                </b>
                                            </Typography>
                                        ) : (
                                            <Typography>
                                                <b>
                                                    {t(
                                                        "Contatta l'assistenza per ricevere supporto ed essere guidato nella procedura"
                                                    )}
                                                </b>
                                            </Typography>
                                        )}
                                    </Grid>
                                </Grid>
                            </Grid>
                        </>
                    ) : (
                        ""
                    )}
                    <Grid item md={12} sm={12} xs={12} lg={12} sx={{ mt: 2 }}>
                        <Grid container spacing={2}>
                            <Grid item md={4} sm={5} xs={12}>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: "bold",
                                        color: "#2f75a9",
                                    }}
                                >
                                    {t("Dati generali")}
                                </Typography>
                                <Divider
                                    className="MuiDivider-VaporLight"
                                    sx={{ width: "90%", mt: 0.8, mb: 2 }}
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Tipo")}
                                    value={
                                        accountData?.account.is_demo
                                            ? "Prova gratuita"
                                            : configs
                                            ? configs?.data?.app?.name_string
                                            : ""
                                    }
                                />
                                {accountData?.provisioningRow
                                    .promoExpirationDate && (
                                    <DetailsGridRows
                                        t={t}
                                        label={t("Scadenza Promozione")}
                                        value={
                                            accountData?.provisioningRow
                                                .promoExpirationDate
                                        }
                                    />
                                )}
                                {!configsData?.data?.app
                                    ?.lawyers_to_users_count_bool && (
                                    <DetailsGridRows
                                        t={t}
                                        label={t("Avvocati")}
                                        value={
                                            accountData?.provisioningRow
                                                ?.number_of_lawyers
                                        }
                                    />
                                )}
                                <DetailsGridRows
                                    t={t}
                                    label={t("Utenti")}
                                    value={
                                        accountData?.provisioningRow
                                            ?.number_of_users
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Firme remote aggiun.")}
                                    value={
                                        accountData?.provisioningRow
                                            ?.certificates
                                    }
                                />
                                <SpazioCloudData
                                    t={t}
                                    label={t("Spazio Cloud")}
                                    value={
                                        accountData?.provisioningRow?.formatted
                                            ?.disk_space_usage
                                    }
                                />
                            </Grid>
                            <Grid item md={4} sm={5} xs={12}>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: "bold",
                                        color: "#2f75a9",
                                    }}
                                >
                                    {t("Moduli")}
                                </Typography>
                                <Divider
                                    className="MuiDivider-VaporLight"
                                    sx={{ width: "90%", mt: 0.8, mb: 0.8 }}
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Antiriciclaggio")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.antiriciclaggio_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.antiriciclaggio_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Campi dinamici")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.classificatore_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.classificatore_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Cassazione")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.cassazione_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.cassazione_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Penale")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.dep_penale_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.dep_penale_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Giudice di Pace")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.gdp_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.gdp_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("UNEP")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.unep_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.unep_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Imp. multi-intestatario")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.impegni_multiutente_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.impegni_multiutente_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Microsoft One Drive")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.one_drive_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.one_drive_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("NIR cartacea")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.nota_iscrizione_ruolo_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.nota_iscrizione_ruolo_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Recupero Crediti")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.recupero_crediti_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.recupero_crediti_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Studi di settore")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.studi_settore_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.studi_settore_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Smart Mailer")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.customer_smart_mailer_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app
                                            ?.customer_smart_mailer_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Log modifiche")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.full_audit_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.full_audit_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                                <DetailCustom
                                    t={t}
                                    label={t("Workflow")}
                                    value={
                                        configsData &&
                                        configsData?.data?.app?.workflow_bool
                                            ? t("Attivato")
                                            : t("Non attivato")
                                    }
                                    color={
                                        configsData &&
                                        configsData?.data?.app?.workflow_bool
                                            ? "green"
                                            : "red"
                                    }
                                />
                            </Grid>
                            <Grid item md={4} sm={5} xs={12}>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontWeight: "bold",
                                        color: "#2f75a9",
                                    }}
                                >
                                    {t("Dati cliente")}
                                </Typography>
                                <Divider
                                    className="MuiDivider-VaporLight"
                                    sx={{ width: "90%", mt: 0.8, mb: 0.8 }}
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Ragione sociale")}
                                    value={
                                        accountData?.provisioningRow
                                            ?.customer_name
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Partita IVA")}
                                    value={
                                        accountData?.provisioningRow?.vatnumber
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Email")}
                                    value={
                                        accountData?.provisioningRow
                                            ?.customer_email
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Telefono")}
                                    value={
                                        accountData?.provisioningRow
                                            ?.customer_phone
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Indirizzo")}
                                    value={
                                        accountData?.provisioningRow?.address
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Cap")}
                                    value={
                                        accountData?.provisioningRow?.zipcode
                                    }
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Città")}
                                    value={accountData?.provisioningRow?.city}
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Provincia")}
                                    value={accountData?.provisioningRow?.state}
                                />
                                <DetailsGridRows
                                    t={t}
                                    label={t("Data registrazione")}
                                    value={
                                        accountData?.provisioningRow?.formatted
                                            .creation_date
                                    }
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default Index;
