import { VaporPage } from "@vapor/react-custom";
import { Tabs, Tab } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";
import { SyntheticEvent, useEffect, useState } from "react";
import { useGetUserData } from "./hooks/GetUserData";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import PageTitle from "../../../custom-components/PageTitle";
import { ExternalUserForm } from "./ExternalUserForm";
import { Permissions } from "./Permissions";
import { parse, format } from "date-fns";
import { useSaveExternalUser } from "./hooks/SaveExternalUser";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useDeleteUser } from "./hooks/DeleteUser";
import ConfirmModal from "../../../custom-components/ConfirmModal";

export const UtentiEsterniUpdate = () => {
    const { t } = useTranslation();
    const [selectedTab, setSelectedTab] = useState(0);
    const [submit, setSubmit] = useState(false);
    const [deleteUser, setDeleteUser] = useState(false);

    const handleTabChange = (
        _event: SyntheticEvent<Element, Event>,
        value: number
    ) => {
        setSelectedTab(value);
    };

    const navigate = useNavigate();
    const { id } = useParams();

    const isCreate = !id;

    const {state} = useLocation();

    const userDataResponse = useGetUserData({ externalMode: 1, uniqueId: id });

    const data = state?.createFromAnagrafica && state?.id ? userDataResponse.data : userDataResponse?.data?.form;

    
    interface UserData {
        tipologia: null;
        tipologiaFE: string;
        uniqueid: string;
        accronym: null;
        birthday: Date | "";
        lawyerCode: string;
        userQualification: string;
        birthPlace: string;
        external_can_upload: boolean;
        external_upload_notification: boolean;
        externalUserName: string;
        externalcode: null;
        active: string;
        default_calendar_person: number;
        companyFields: null;
        archive_timesheet_bool: boolean;
        archive_impegni_bool: boolean;
        archive_udienze_bool: boolean;
        archive_general_data_bool: boolean;
        password: string;
        resetPassword: string;
        sendCredentials: boolean;
        codiceavvocato?: string;
    }

    const [externalUserData, setExternalUserData] = useState<UserData>();

    useEffect(() => {
        if (userDataResponse.data && id) {
            setExternalUserData({
                tipologia: data.tipologia,
                tipologiaFE: data.tipologiaFE,
                uniqueid: data.uniqueid,
                accronym: data.sigla,
                birthday:
                    data.natoil && parse(data.natoil, "dd/MM/yyyy", new Date()),
                lawyerCode: data.codiceavvocato,
                userQualification: data.qualificautente,
                birthPlace: data.natoa,
                external_can_upload: data.external_can_upload === "1",
                external_upload_notification:
                    data.external_upload_notification === "1",
                externalUserName: data.tipoutentenome,
                externalcode: data.externalcode,
                active: data.attivo,
                default_calendar_person: data.default_calendar_person,
                companyFields: data.campi_agenda,
                archive_timesheet_bool: data.archive_timesheet_bool === 1,
                archive_impegni_bool: data.archive_impegni_bool === 1,
                archive_udienze_bool: data.archive_udienze_bool === 1,
                archive_general_data_bool: data.archive_general_data_bool === 1,
                password: "",
                resetPassword: "",
                sendCredentials: true,
            });
        } else {
            setExternalUserData({
                tipologia: null,
                tipologiaFE: "",
                uniqueid: "",
                accronym: null,
                birthday: "",
                lawyerCode: "",
                userQualification: "2",
                birthPlace: "",
                external_can_upload: false,
                external_upload_notification: false,
                externalUserName: "",
                externalcode: null,
                active: "1",
                default_calendar_person: 0,
                companyFields: null,
                archive_timesheet_bool: false,
                archive_impegni_bool: false,
                archive_udienze_bool: false,
                archive_general_data_bool: false,
                password: "",
                resetPassword: "",
                sendCredentials: true,
            });
        }
    }, [userDataResponse.hasLoaded, id]);

    const passwordSchema = yup
        .string()
        .required(t("Per favore inserisci LA TUA password"))
        .min(5, t("La tua password deve essere più lunga di 5 caratteri."))
        .max(25)
        .matches(/^(?=.{6,})/, t("Deve contenere 6 caratteri"))
        .matches(
            /^(?=.*[a-z])(?=.*[A-Z])/,
            t("Deve contenere una lettera maiuscola e una minuscola")
        )
        .matches(
            /^(?=.*[!@#\$%\^&\*])/,
            t("Deve contenere un carattere maiuscolo/minuscolo speciale")
        );

    const externalUserSchema = (isCreate: boolean) =>
        yup.object().shape({
            username: yup.string().required("Nome completo obbligatorio"),
            password: isCreate
                ? passwordSchema
                : yup.string().test("password-length", "", function (value) {
                      if (!value) {
                          return true;
                      }
                      try {
                          passwordSchema.validateSync(value);
                          return true;
                      } catch (e: any) {
                          return this.createError({ message: e.errors[0] });
                      }
                  }),
            passwordConfirm: isCreate
                ? yup
                      .string()
                      .required(
                          t("Si prega di digitare nuovamente la password.")
                      )
                      .oneOf(
                          [yup.ref("password")],
                          t("Le tue password non corrispondono.")
                      )
                : yup.string().notRequired(),
            personalName: yup.string().required("Nome obbligatorio"),
            personalSurname: yup.string().required("Cognome obbligatorio"),
            Email: yup.string().email().required("Email obbligatorio"),
            name: yup.string().required("Nome utente obbligatorio"),
        });

    const {
        register,
        formState: { errors, isValid },
        setValue,
        trigger,
        getValues,
    } = useForm({
        mode: "all",
        resolver: yupResolver(externalUserSchema(isCreate)),
    });

    useEffect(() => {
        if (data && id) {
            setValue("username", data.nomeutente || "");
            setValue("personalName", data.nomepersonale || "");
            setValue("personalSurname", data.cognomepersonale || "");
            setValue("Email", data.Email || "");
            setValue("password", "");
            setValue("passwordConfirm", "");
            setValue("name", data.nome || "");
        } else if (state?.createFromAnagrafica && state?.id) {
            setValue("username", data?.nomeutente || "");
            setValue("personalName", data?.nome || "");
            setValue("personalSurname", data?.cognome || "");
            setValue("Email", data?.contatti || "");
            setValue("password", "");
            setValue("passwordConfirm", "");
            setValue("name", data?.denominazione || "");
        }
    }, [userDataResponse.hasLoaded]);

    let extpermission = [];

    if (externalUserData?.archive_general_data_bool) {
        extpermission.push({
            name: "archive_general_data_bool",
            value: "on",
        });
    }
    if (externalUserData?.archive_timesheet_bool) {
        extpermission.push({
            name: "archive_timesheet_bool",
            value: "on",
        });
    }
    if (externalUserData?.archive_impegni_bool) {
        extpermission.push({
            name: "archive_impegni_bool",
            value: "on",
        });
    }
    if (externalUserData?.archive_udienze_bool) {
        extpermission.push({
            name: "archive_udienze_bool",
            value: "on",
        });
    }

    const formData = new FormData();

    formData.append("nomeutente", getValues().username);
    formData.append("nomepersonale", getValues().personalName);
    formData.append("cognomepersonale", getValues().personalSurname);
    formData.append("Email", getValues().Email);
    formData.append("nome", getValues().name);
    formData.append("password", getValues().password || "");
    formData.append("passwordConfirm", getValues().passwordConfirm || "");
    externalUserData && formData.append("uniqueid", externalUserData.uniqueid);
    externalUserData &&
        formData.append("sigla", externalUserData.accronym || "");
    externalUserData &&
        formData.append("qualificautente", externalUserData.userQualification);
    externalUserData && formData.append("ordine", "");
    externalUserData &&
        formData.append("externalcode", externalUserData.externalcode || "");
    externalUserData &&
        formData.append("codiceavvocato", externalUserData.lawyerCode);
    externalUserData &&
        formData.append("attivo", externalUserData.active ? "1" : "0");
    externalUserData && formData.append("external_upload_notification", "on");
    formData.append("studioUniqueid", "");
    formData.append("userImport", "-1");
    formData.append("studioNome", "");
    formData.append("studioCodicefiscale", "");
    formData.append("studioPartitaiva", "");
    formData.append("studioIndirizzo", "");
    formData.append("studioCap", "");
    formData.append("studioCitta", "");
    formData.append("studioProvincia", "");
    formData.append("studioTelefono", "");
    formData.append("studioFax", "");
    formData.append("studioMobile", "");
    formData.append("studioEmail", "");
    formData.append("studioWeb", "");
    formData.append("studioPolizza", "");
    formData.append("pec_address_for_pa_invoice", "");
    formData.append("pa_tipo_ritenuta", "0");
    formData.append("pa_id_codice", "");
    formData.append("pa_id_codice_da", "");
    formData.append("pa_codice_fiscale_da", "");
    externalUserData?.sendCredentials &&
        formData.append("sendCredential", "on");

    externalUserData?.external_can_upload &&
        formData.append("external_can_upload", "on");
    externalUserData?.external_upload_notification &&
        formData.append("external_upload_notification", "on");

    externalUserData?.birthPlace &&
        formData.append("natoa", externalUserData?.birthPlace);

    externalUserData?.birthday &&
        formData.append(
            "natoil",
            externalUserData.birthday
                ? format(externalUserData.birthday, "dd/MM/yyyy")
                : externalUserData.birthday
        );

    for (let [index, permission] of extpermission.entries()) {
        formData.append(`extpermission[${index}][name]`, permission.name);
        formData.append(`extpermission[${index}][value]`, permission.value);
    }

    const saveUserResponse = useSaveExternalUser(formData, submit);

    const handleDeleteUserDecline = () => {
        setConfirmDeleteUser(false);
    };

    const handleDeleteUserAgree = () => {
        setDeleteUser(true);
    };

    const [confirmDeleteUser, setConfirmDeleteUser] = useState(false);

    const deleteUserResponse = useDeleteUser(
        externalUserData?.uniqueid,
        deleteUser
    );

    useEffect(() => {
        if (deleteUserResponse.hasLoaded || saveUserResponse.hasLoaded) {
            navigate("/externalusers/external");
        }
    }, [deleteUserResponse.hasLoaded, saveUserResponse.hasLoaded]);

    const actionButtons: any = [
        {
            label: "Annulla",
            onclick: () => navigate("/externalusers/external"),
        },
        ...(externalUserData?.uniqueid
            ? [
                  {
                      label: "Elimina",
                      onclick: () => setConfirmDeleteUser(true),
                      color: "error",
                  },
              ]
            : []),
        {
            label: "Conferma",
            onclick: () => {
                if (isValid) {
                    setSubmit(true);
                } else {
                    trigger();
                }
            },
        },
    ];
    return (
        <VaporPage>
            {data && (
                <ConfirmModal
                    open={confirmDeleteUser}
                    handleDecline={handleDeleteUserDecline}
                    handleAgree={handleDeleteUserAgree}
                    decline={t("No")}
                    agree={t("Si, Elimina")}
                    confirmText={t(
                        `Eliminare definitivamente l'utente "${data.nomeutente}"?`
                    )}
                    title={t("Sei sicuro?")}
                />
            )}
            <PageTitle
                showBackButton={true}
                pathToPrevPage="/externalusers/external"
                title={t("GESTIONE UTENTI")}
                actionButtons={actionButtons}
            />
            <VaporPage.Section>
                <Tabs
                    size="extraSmall"
                    variant="standard"
                    value={selectedTab}
                    onChange={handleTabChange}
                >
                    <Tab value={0} label={t("Scheda utente Esterno")} />
                    <Tab value={1} label={t("Permessi")} />
                </Tabs>
                {selectedTab === 0 && externalUserData && (
                    <ExternalUserForm
                        data={externalUserData}
                        setData={setExternalUserData}
                        register={register}
                        errors={errors}
                        isCreate={isCreate}
                    />
                )}
                {selectedTab === 1 && externalUserData && (
                    <Permissions
                        data={externalUserData}
                        setData={setExternalUserData}
                    />
                )}
            </VaporPage.Section>
        </VaporPage>
    );
};
