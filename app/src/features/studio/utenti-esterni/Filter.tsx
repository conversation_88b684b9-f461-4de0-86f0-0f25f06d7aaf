import {
    Box,
    Button,
    FormControl,
    MenuItem,
    Select,
    TextField,
} from "@vapor/react-material";

// Import components
import Legenda from "./components/Legenda";

interface FilterProps {
    defaultParams: any;
    onChangeInput: (e: any) => void;
    onClickReset: () => void;
    onSubmit: (e: any) => void;
    users: any[];
    userType: any;
}

const Filter = ({
    defaultParams,
    onChangeInput,
    onClickReset,
    onSubmit,
    users,
    userType,
}: FilterProps) => {
    return (
        <div style={{ display: "flex" }}>
            <Box
                component="form"
                display="flex"
                alignItems="end"
                gap={2}
                onSubmit={onSubmit}
            >
                <TextField
                    label="Nome"
                    variant="outlined"
                    value={defaultParams.code}
                    placeholder="Nome"
                    name="searchName"
                    sx={{ width: 1 / 5 }}
                    onChange={onChangeInput}
                />
                <TextField
                    label="Email"
                    variant="outlined"
                    value={defaultParams.code}
                    placeholder="Email"
                    name="searchEmail"
                    sx={{ width: 1 / 5 }}
                    onChange={onChangeInput}
                />

                {userType === "internal" ? (
                    <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
                        <Select
                            labelId="select-label"
                            value={defaultParams.searchLawyer}
                            label="Seleziona Categoria"
                            onChange={onChangeInput}
                            name="searchQualification"
                        >
                            <MenuItem value={-1}>
                                {"Tutte le qualifiche"}
                            </MenuItem>
                            {users.map((user) => (
                                <MenuItem key={user.id} value={user.id}>
                                    {user.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                ) : (
                    <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
                        <Select
                            labelId="select-label"
                            value={defaultParams.searchLawyer}
                            label="Seleziona Categoria"
                            onChange={onChangeInput}
                            name="searchLawyer"
                        >
                            <MenuItem value={-1}>
                                {"Tutti i referenti"}
                            </MenuItem>
                            {users.map((user) => (
                                <MenuItem key={user.id} value={user.id}>
                                    {user.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                )}

                <Button variant="contained" color="primary" type="submit">
                    Cerca
                </Button>

                <Button
                    variant="contained"
                    color="primary"
                    onClick={onClickReset}
                >
                    Mostra tutti
                </Button>
            </Box>
            <Box sx={{ marginLeft: "auto", alignContent: "end" }}>
                <Legenda />
            </Box>
        </div>
    );
};
export default Filter;
