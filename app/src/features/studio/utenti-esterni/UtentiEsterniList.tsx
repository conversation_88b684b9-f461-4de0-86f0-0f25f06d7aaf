import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useUser } from "../../../store/UserStore";

import Filter from "./Filter";
import PageTitle from "../../../custom-components/PageTitle";
import { getUserGrid } from "../../../utilities/user/gridColumn";

const UteniEsterniList = () => {
    const DEFAULT_LIST_PARAMS = {
        noTemplateVars: true,
        page: 0,
        pageSize: 10,
        sortColumn: "nomeutente",
        sortOrder: "asc",
        searchName: "",
        searchEmail: "",
        externalMode: "",
        searchLawyer: "-1",
        searchQualification: "-1",
    };
    const { t } = useTranslation();
    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);
    const [columns, setColumns] = useState<any>([]);
    const [totalRows, setTotalRows] = useState(0);
    const [data, setData] = useState([]);
    const [users, setUsers] = useState<any[]>([]);

    const navigate = useNavigate();
    const { userType } = useParams();
    const { modules } = useUser() as any;

    const externalUsersListRequest = useGetCustom(
        userType === "external" ? "externalusers/list" : "users/list",
        // "externalusers/list",
        defaultParams
    );
    const defaultExternalUsersIndexRequest = useGetCustom(
        userType === "external" ? "externalusers/list" : "users/list",
        // "externalusers/list",
        DEFAULT_LIST_PARAMS
    );

    const externalUsersIndexRequest = useGetCustom(
        userType === "external" ? "externalusers/external" : "users/internal"
        // "externalusers/external"
    );

    const fetchUsers = async () => {
        const response: any = await externalUsersIndexRequest.doFetch(true);
        if (userType === "internal") {
            setUsers(response.searchQualifications);
        } else {
            setUsers(response.data?.searchLawyers);
            defaultParams.externalMode = "1";
        }
    };

    useEffect(() => {
        fetchUsers();
    }, []);

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
    ]);
    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultExternalUsersIndexRequest.doFetch(true)
            : externalUsersListRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;

        const finalColumns = await getUserGrid(
            t,
            modules && modules.canAccessUserQualifica()
        );
        setColumns(finalColumns);

        setData(currentPage);
        setTotalRows(Number(totalRows));
    };

    const onSubmit = (e: any) => {
        e.preventDefault();
        startSearchList();
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickReset = async () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
    };

    const onClickCallback = (id: any) => {
        navigate(`/externalusers/update/${id}`);
    };

    const gotoNewUserPage = () => {
        navigate(`/externalusers/create`);
    };

    const exportCsvResponse = useGetCustom("externalusers/exportcsv", {
        searchName: defaultParams.searchName,
        searchEmail: defaultParams.searchEmail,
        searchLawyer: defaultParams.searchLawyer,
        externalMode: userType === "external" ? "1" : "0",
    });

    const exportDocs = async () => {
        const response: any = await exportCsvResponse.doFetch(true);
        const blob = new Blob([response.data], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute(
            "download",
            userType === "external" ? "Utenti esterni.csv" : "Utenti.csv"
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="externalUsers"
                setQuery={setDefaultParams}
                onClickCallback={onClickCallback}
                columns={columns}
                data={data}
                totalRows={totalRows}
                page={defaultParams.page}
                pageSize={defaultParams.pageSize}
                onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickKey="uniqueid"
            />
        );
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    userType === "external"
                        ? t("Utenti esterni")
                        : t("Utenti interni")
                }
                actionButtons={[
                    { label: "Esporta in csv", onclick: () => exportDocs() },
                    { label: "Nuovo utente", onclick: () => gotoNewUserPage() },
                ]}
            ></PageTitle>
            <VaporPage.Section>
                <Filter
                    defaultParams={defaultParams}
                    onChangeInput={onChangeInput}
                    onClickReset={onClickReset}
                    onSubmit={onSubmit}
                    users={users}
                    userType={userType}
                />
            </VaporPage.Section>
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
};
export default UteniEsterniList;
