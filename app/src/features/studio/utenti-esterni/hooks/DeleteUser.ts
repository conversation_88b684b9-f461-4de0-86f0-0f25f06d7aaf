import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDeleteUser = (
    uniqueid: string | undefined,
    deleteUser: boolean
) => {
    const { hasLoaded, doFetch, loading } = usePostCustom(
        "externalusers/delete"
    );

    useEffect(() => {
        if (deleteUser) {
            doFetch(true, { uniqueid: uniqueid });
        }
    }, [uniqueid, deleteUser]);

    return { loading, hasLoaded };
};
