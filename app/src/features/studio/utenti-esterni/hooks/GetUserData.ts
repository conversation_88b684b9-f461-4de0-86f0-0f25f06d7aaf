import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useLocation } from "react-router-dom";

interface GetUserDataProps {
    uniqueId: string | undefined;
    externalMode: number;
    state?: any;
}

export const useGetUserData = (params: GetUserDataProps) => {
    const userDataResponse = useGetCustom(
        "/externalusers/getrowdata?noTemplateVars=true",
        params
    );

    const { state } = useLocation();

     const postCustomResponse = usePostCustom(
        "archiveanagrafiche/fillexternalfromanagrafica?noTemplateVars=true",
    );

    const isFromAnagrafica = state?.createFromAnagrafica && state?.id;

  

    useEffect(() => {

        if(isFromAnagrafica){
            const formData = new FormData();
            formData.append("id", state.id);
            postCustomResponse.doFetch(true, formData);
        }

        if (params.uniqueId) {
            userDataResponse.doFetch(true);
        }
    }, [params.uniqueId]);

    return isFromAnagrafica ? postCustomResponse : userDataResponse;


};
