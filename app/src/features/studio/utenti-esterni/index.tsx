import UtentiEsterniList from "./UtentiEsterniList";
import { UtentiEsterniUpdate } from "./UtentiEsterniUpdate";

export const utentiEsterni = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/users/:userType",
            element: <UtentiEsterniList />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/externalusers/:userType",
            element: <UtentiEsterniList />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/externalusers/update/:id",
            element: <UtentiEsterniUpdate />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/externalusers/create",
            element: <UtentiEsterniUpdate />,
        },
    },
];
