import {
    FormControl,
    FormControlLabel,
    Checkbox,
    Stack,
    FormGroup,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";
import { SyntheticEvent } from "react";

export const Permissions = ({ data, setData }: { data: any; setData: any }) => {
    const { t } = useTranslation();

    const handleArchiveDataChange = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            archive_general_data_bool: checked,
        });
    };

    const handleTimesheetChange = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            archive_timesheet_bool: checked,
        });
    };

    const handleArchiveImpegniChange = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            archive_impegni_bool: checked,
        });
    };

    const handleArchiveUdienzeChange = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            archive_udienze_bool: checked,
        });
    };

    return (
        <FormControl>
            <FormGroup sx={{ gap: 2, pt: 2 }}>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Dati generali")}</Typography>
                    <FormControlLabel
                        onChange={handleArchiveDataChange}
                        value={data.archive_general_data_bool}
                        checked={data.archive_general_data_bool}
                        control={<Checkbox />}
                        label={t(
                            "Se spuntato vedo solo la sezione Dati generali"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Timesheet")}</Typography>
                    <FormControlLabel
                        onChange={handleTimesheetChange}
                        value={data.archive_timesheet_bool}
                        checked={data.archive_timesheet_bool}
                        control={<Checkbox />}
                        label={t(
                            "Può visualizzare la sezione timesheet della pratica"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Impegni")}</Typography>
                    <FormControlLabel
                        onChange={handleArchiveImpegniChange}
                        value={data.archive_impegni_bool}
                        checked={data.archive_impegni_bool}
                        control={<Checkbox />}
                        label={t(
                            "Può visualizzare la sezione impegni della pratica"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Udienze")}</Typography>
                    <FormControlLabel
                        onChange={handleArchiveUdienzeChange}
                        value={data.archive_udienze_bool}
                        checked={data.archive_udienze_bool}
                        control={<Checkbox />}
                        label={t(
                            "Può visualizzare la sezione udienze della pratica"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
            </FormGroup>
        </FormControl>
    );
};
