import { Vapor<PERSON><PERSON>, Toolbar } from "@vapor/react-custom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";
import { Button, Grid, TextField, Stack, Box } from "@vapor/react-material";
import { useNavigate, useParams } from "react-router-dom";
import { Typography } from "@vapor/react-extended";
import { useGetUserUpdate } from "./hooks/useGroupsUpdate";
import UserList from "./UserList";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useUpdateGroupPermissions } from "./hooks/useUpdateGroupPermissions";
import { useGetUserList } from "./hooks/useUserList";
import { useDeleteGroup } from "./hooks/useDeleteGroup";
import { Permissions } from "./Permissions";
import { updatePermissionValue } from "./UpdatePermissions";

interface GroupNameInputProps {
    groupName: string;
    setGroupName: Dispatch<SetStateAction<string>>;
}

const GroupNameInput = ({ groupName, setGroupName }: GroupNameInputProps) => {
    const [value, setValue] = useState(groupName);

    useEffect(() => {
        setValue(groupName);
    }, [groupName]);

    return (
        <Box width={400}>
            <Stack direction="row" alignItems="flex-end">
                <TextField
                    variant="outlined"
                    label="Nome"
                    onChange={(e: any) => setValue(e.target.value)}
                    onBlur={() => setGroupName(value)}
                    value={value}
                ></TextField>
            </Stack>
        </Box>
    );
};

export const Update = () => {
    const userParams = useParams();
    const userId = userParams?.uid;

    const navigate = useNavigate();

    const { data } = useGetUserUpdate(userId);

    interface GroupPermissions {
        name: string;
        r: string;
        c: string;
        u: string;
        d: string;
        access: string[];
        id: string;
        category: string;
        group: string;
        master: string;
    }

    const permissionStatus: GroupPermissions[] = data?.result.map(
        (row: any) => ({
            name: row.name,
            r: row.r,
            c: row.c,
            u: row.u,
            d: row.d,
            access: row.access ? row.access : [],
            id: row.id,
            category: row.category,
            group: row.name.split(" - ")[0],
            master: row.master,
        })
    );

    const [permissions, setPermissions] = useState<any[]>([]);

    useEffect(() => {
        if (permissionStatus && permissionStatus.length) {
            setPermissions(permissionStatus);
        }
    }, [permissionStatus && permissionStatus.length]);

    const activateAllPermissions = (permissions: any[]) => {
        return permissions.map((permission) => ({
            ...permission,
            access: ["c", "r", "u", "d"],
        }));
    };

    const { data: users } = useGetUserList();

    const userName =
        userId !== undefined
            ? users &&
              JSON.parse(users?.list)?.currentPage?.filter(
                  (group: any) => group.uniqueid === userId
              )[0].name
            : "";

    const [groupName, setGroupName] = useState("");
    const [selectedUserIds, setSelectedUserIds] = useState("");

    useEffect(() => {
        if (userName) {
            setGroupName(userName);
        }
    }, [userName]);

    const [deleteGroup, setDeleteGroup] = useState(false);

    const { loading: idDeleting, hasLoaded: isDeleted } = useDeleteGroup(
        userId ? userId : "",
        deleteGroup
    );

    const [sendFormData, setSendFormData] = useState(false);

    const formData = new FormData();

    formData.append("uniqueid", userId ?? "");
    formData.append("name", groupName);
    formData.append("nomeSearch", "");
    formData.append("ruoloSearch", "-1");
    formData.append("referenteSearch", "-1");
    formData.append("users", selectedUserIds);

    permissions.forEach((permission) => {
        permission.access.forEach((access: any) => {
            formData.append(`section_${permission.id}[${access}]`, "on");
        });
    });

    // Ora puoi passare `formData` alla tua richiesta HTTP
    const { loading, hasLoaded } = useUpdateGroupPermissions(
        formData,
        sendFormData
    );

    useEffect(() => {
        if (isDeleted || hasLoaded) {
            navigate("/groups/groups");
        }
    }, [isDeleted, navigate, hasLoaded]);

    useEffect(() => {
        setSendFormData(false);
    }, [sendFormData]);

    return (
        <VaporPage
            title="Riepilogo"
            headerLeft={
                <Button size="large" onClick={() => navigate("/groups/groups")}>
                    <FontAwesomeIcon icon={faArrowLeft}></FontAwesomeIcon>
                </Button>
            }
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack direction="row" gap={1} paddingLeft={2}>
                            <Button
                                variant="outlined"
                                color="secondary"
                                onClick={() => navigate("/groups/groups")}
                            >
                                Annulla
                            </Button>
                            {userId && (
                                <Button
                                    disabled={idDeleting}
                                    variant="outlined"
                                    color="error"
                                    onClick={() => setDeleteGroup(true)}
                                >
                                    Elimina
                                </Button>
                            )}
                            <Button
                                disabled={loading || groupName === ""}
                                variant="contained"
                                color="primary"
                                onClick={() => setSendFormData(true)}
                            >
                                Conferma
                            </Button>
                        </Stack>
                    }
                />
            }
        >
            <VaporPage.Section>
                <GroupNameInput
                    groupName={groupName}
                    setGroupName={setGroupName}
                />
                <Grid container gridTemplateColumns={2} spacing={8} pt={4}>
                    <Grid item xs={6}>
                        <Stack
                            direction="row"
                            justifyContent="space-between"
                            alignItems="center"
                            paddingBottom={2}
                        >
                            <Typography>SEZIONI</Typography>
                            {!userId && (
                                <Button
                                    variant="contained"
                                    size="small"
                                    onClick={() =>
                                        setPermissions((prevPermissions) =>
                                            activateAllPermissions(
                                                prevPermissions
                                            )
                                        )
                                    }
                                >
                                    Abilita tutto
                                </Button>
                            )}
                        </Stack>
                        <Permissions
                            permissions={permissions}
                            setPermissions={setPermissions}
                            updatePermissionValue={updatePermissionValue}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <UserList setSelectedUserIds={setSelectedUserIds} />
                    </Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};
