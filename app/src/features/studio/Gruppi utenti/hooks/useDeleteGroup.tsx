import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useDeleteGroup = (userId: string, remove: boolean) => {
  const { loading, data, error, doFetch, hasLoaded } = usePostCustom("groups/delete");

  useEffect(() => {
    if (remove) {
      doFetch(true, { uniqueid: userId });
    }
  }, [userId, remove]);

  return { loading, hasLoaded, error, data };
};
