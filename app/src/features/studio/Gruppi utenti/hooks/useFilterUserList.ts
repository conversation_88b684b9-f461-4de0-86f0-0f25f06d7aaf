import { useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export type filterUserGroupProps = {
  noTemplateVars: true;
  page: number;
  pageSize: number;
  sortColumn: "nome";
  sortOrder: "asc";
  searchUser: string;
};

export const useFilterUserList = (params: filterUserGroupProps) => {
  const { loading, data, error, doFetch, hasLoaded } = useGetCustom(
    "/groups/list",
    params
  );

  useEffect(() => {
    doFetch(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    params.searchUser,
    params.page,
    params.pageSize,
    params.sortColumn,
    params.sortOrder,
  ]);
  return { loading, hasLoaded, error, data };
};
