import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useUpdateGroupPermissions = (
  permissionsFormData: any,
  sendFormData: boolean
) => {
  const { loading, data, error, doFetch, hasLoaded } = usePostCustom("groups/save");

  useEffect(() => {
    if (sendFormData) {
      doFetch(true, permissionsFormData);
    }
  }, [sendFormData]);

  return { loading, hasLoaded, error, data };
};
