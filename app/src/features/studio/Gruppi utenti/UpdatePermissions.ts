export const updatePermissionValue = (
  name: string,
  permission: string,
  permissionStatus: any[]
) => {
  const newPermissions = [...permissionStatus];
  for (let i in newPermissions) {
    const group = newPermissions[i].group;
    if (
      newPermissions[i].name === name &&
      newPermissions[i][permission] === "1"
    ) {
      if (newPermissions[i].access.includes(permission)) {
        if (permission === "r") {
          newPermissions[i].access = [];
          if (newPermissions[i].name === newPermissions[i].group) {
            for (let j in newPermissions) {
              if (newPermissions[j].group === group) {
                newPermissions[j].access = [];
              }
            }
          }
        } else {
          if (
            newPermissions[i].access.includes("u") &&
            newPermissions[i].master === "1"
          ) {
            newPermissions[i].access = newPermissions[i].access.filter(
              (v: string) => v !== permission
            );
            for (let j in newPermissions) {
              if (
                newPermissions[j].group === group &&
                newPermissions[j].master === "0"
              ) {
                newPermissions[j].access = newPermissions[j].access.filter(
                  (v: string) => v === "r"
                );
              }
            }
          } else {
            newPermissions[i].access = newPermissions[i].access.filter(
              (v: string) => v !== permission
            );
          }
        }
      } else {
        if (permission === "r") {
          if (newPermissions[i].group === newPermissions[i].name) {
            if (newPermissions[i].u === "1") {
              newPermissions[i].access.push("u");
            }
            for (let j in newPermissions) {
              if (newPermissions[j].category === newPermissions[i].category) {
                newPermissions[j].access.push("r");
              }
            }
          } else {
            newPermissions[i].access.push("r");
            for (let j in newPermissions) {
              if (newPermissions[j].name === group) {
                if (
                  newPermissions[j].r === "1" &&
                  !newPermissions[j].access.includes("r")
                ) {
                  newPermissions[j].access.push("r");
                }
              }
            }
          }
        } else {
          if (
            newPermissions[i][permission] === "1" &&
            !newPermissions[i].access.includes(permission)
          ) {
            newPermissions[i].access.push(permission);
          }
          if (!newPermissions[i].access.includes("r")) {
            if (
              newPermissions[i].r === "1" &&
              !newPermissions[i].access.includes("r")
            ) {
              newPermissions[i].access.push("r");
            }
            for (let j in newPermissions) {
              if (newPermissions[j].name === group) {
                if (
                  newPermissions[j].r === "1" &&
                  !newPermissions[j].access.includes("r")
                ) {
                  newPermissions[j].access.push("r");
                }
                if (permission !== "r") {
                  if (
                    newPermissions[j][permission] === "1" &&
                    !newPermissions[j].access.includes(permission)
                  ) {
                    newPermissions[j].access.push(permission);
                  }
                }
              }
            }
          } else {
            for (let j in newPermissions) {
              if (newPermissions[j].name === group) {
                if (
                  newPermissions[j][permission] === "1" &&
                  !newPermissions[j].access.includes(permission)
                ) {
                  newPermissions[j].access.push(permission);
                }
              }
            }
          }
        }
      }
    }
  }
  return newPermissions;
};
