import { VaporPage } from "@vapor/react-custom";
import { Box, Button, Stack, Table, TableBody, TableCell, TableContainer, TableHead, TablePagination, TableRow, TextField } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCirclePlus, faSearch, faCircleCheck, faCircleMinus } from "@fortawesome/free-solid-svg-icons";
import { useGetUserList } from "./hooks/useUserList";
import { useEffect, useState } from "react";
import { filterUserGroupProps, useFilterUserList } from "./hooks/useFilterUserList";
import { useNavigate } from "react-router-dom";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

const AccesStatus = ({ value }: { value: number | undefined }) => {
    return <TableCell align="center">{value === 0 ? <FontAwesomeIcon icon={faCircleMinus} color="#D8D8D9" size="lg" /> : value === 1 ? <FontAwesomeIcon icon={faCircleMinus} color="rgb(253, 185, 39)" size="lg" /> : value === 2 ? <FontAwesomeIcon icon={faCircleCheck} color="rgb(9, 130, 42)" size="lg" /> : <></>}</TableCell>;
};

interface UserTableRowProps {
    values: any;
    index: number;
}

const UserTableRow = ({ values, index }: UserTableRowProps) => {
    const [hovered, setHovered] = useState<null | number>(null);
    const navigate = useNavigate();
    return (
        <TableRow
            key={values.uniqueid}
            onMouseEnter={() => setHovered(index)}
            onMouseLeave={() => setHovered(null)}
            hover={index === hovered}
            style={{
                cursor: "pointer"
            }}
            onClick={() => navigate(`/groups/update/${values.uniqueid}`)}
        >
            <TableCell>
                <Typography>{values.name}</Typography>
            </TableCell>
            <AccesStatus value={values.Dashboard} />
            <AccesStatus value={values.Pratiche} />
            <AccesStatus value={values.Agenda} />
            <AccesStatus value={values.Fatturazione} />
            <AccesStatus value={values.MailBox} />
            <AccesStatus value={values.PDA} />
            <AccesStatus value={values.Polisweb} />
            <AccesStatus value={values.Pct} />
            <AccesStatus value={values.Studio} />
            <AccesStatus value={values.Utilita} />
            <AccesStatus value={values.Shop} />
            <AccesStatus value={values.News} />
        </TableRow>
    );
};

export const Groups = () => {
    const { data } = useGetUserList();

    const [searchParams, setSearchParams] = useState<filterUserGroupProps>({
        page: 0,
        pageSize: 10,
        searchUser: "-1",
        sortColumn: "nome",
        sortOrder: "asc",
        noTemplateVars: true
    });

    const { data: userGroupData } = useFilterUserList(searchParams);

    const totalRows = userGroupData?.totalRows;

    const gridsSettings = data?.gridsSettings;
    const columnNames = gridsSettings?.column_names && JSON.parse(gridsSettings.column_names);

    const searchUsers = data?.searchUsers;

    const values = [
        { label: "Tutti gli utenti", value: "-1" },
        ...(searchUsers?.map((user: any) => ({
            label: user.nomeutente,
            value: user.id
        })) || [])
    ];

    const [page, setPage] = useState(() => userGroupData?.currentPage || []);

    useEffect(() => {
        setPage(userGroupData?.currentPage);
    }, [searchParams.searchUser, data, userGroupData?.currentPage]);

    const navigate = useNavigate();
    return (
        <VaporPage
            headerLeft={
                <Box paddingLeft={2}>
                    <Typography variant="titleMedium">GRUPPI UTENTI</Typography>
                </Box>
            }
            headerRight={
                <Box paddingRight={2}>
                    <Button onClick={() => navigate("/groups/update")} variant="contained" startIcon={<FontAwesomeIcon icon={faCirclePlus} />}>
                        Nuovo gruppo
                    </Button>
                </Box>
            }
        >
            <VaporPage.Section>
                <Stack paddingBottom={2} direction="row" maxWidth={300}>
                    <CustomAutocomplete
                        disableClearable
                        freeSolo
                        size="small"
                        options={values}
                        onChange={(_event: any, value: any) =>
                            setSearchParams({
                                ...searchParams,
                                searchUser: value.value
                            })
                        }
                        renderInput={(params: any) => <TextField {...params} placeholder="Tutti gli utenti" />}
                    />
                    <Button variant="contained" size="medium">
                        <FontAwesomeIcon icon={faSearch}></FontAwesomeIcon>
                    </Button>
                </Stack>
                <TableContainer>
                    <Table>
                        <TableHead shadow>
                            <TableRow>
                                {columnNames?.map((value: any) => (
                                    <TableCell align={value === "Nome" ? "left" : "center"} key={value}>
                                        <Typography>{value === "Utilit&agrave;" ? "Utilità" : value}</Typography>
                                    </TableCell>
                                ))}
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {page?.map((values: any, index: number) => (
                                <UserTableRow values={values} index={index} key={index}></UserTableRow>
                            ))}
                        </TableBody>
                        <TablePagination
                            labelRowsPerPage=""
                            rowsPerPageOptions={[]}
                            count={totalRows ? parseInt(totalRows) : 0}
                            rowsPerPage={searchParams.pageSize}
                            page={searchParams.page}
                            onPageChange={(_event: any, newPage: any) =>
                                setSearchParams({
                                    ...searchParams,
                                    page: newPage
                                })
                            }
                        />
                    </Table>
                </TableContainer>
            </VaporPage.Section>
        </VaporPage>
    );
};
