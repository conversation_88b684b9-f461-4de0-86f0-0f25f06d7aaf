import {
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@vapor/react-material";

interface PermissionsProps {
  permissions: any;
  setPermissions: any;
  updatePermissionValue: any;
}

export const Permissions = ({
  permissions,
  setPermissions,
  updatePermissionValue,
}: PermissionsProps) => {
  return (
    <TableContainer>
      <Table>
        <TableHead shadow>
          <TableRow>
            <TableCell> </TableCell>
            <TableCell>Lettura</TableCell>
            <TableCell>Inserimento</TableCell>
            <TableCell>Modifica</TableCell>
            <TableCell>Eliminazione</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {permissions?.map((value: any) => (
            <TableRow key={value.name}>
              <TableCell>{value.name}</TableCell>
              {["r", "c", "u", "d"].map((permission) => (
                <TableCell key={permission}>
                  {value[permission] === "1" && (
                    <Switch
                      checked={value.access.includes(permission)}
                      onChange={() =>
                        setPermissions((prevPermissions: any) =>
                          updatePermissionValue(
                            value.name,
                            permission,
                            prevPermissions
                          )
                        )
                      }
                    />
                  )}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
