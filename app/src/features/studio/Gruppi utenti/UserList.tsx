import { Stack, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, Select, MenuItem, FormControl, InputLabel, TablePagination, IconButton, Checkbox } from "@vapor/react-material";
import { faSearch, faArrowUp, faArrowDown } from "@fortawesome/free-solid-svg-icons";

import { Typography } from "@vapor/react-extended";
import { useListUser, type useListUserProps } from "./hooks/useListUser";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Dispatch, memo, SetStateAction, useEffect, useState } from "react";
import { useGetUserUpdate } from "./hooks/useGroupsUpdate";
import { useParams } from "react-router-dom";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

interface TableSorterProps {
    label: string;
    value: useListUserProps["sortColumn"];
    updateSelectedSortColumn: (column: useListUserProps["sortColumn"], order: useListUserProps["sortOrder"]) => void;
    sortColumn: useListUserProps["sortColumn"];
    sortOrder: useListUserProps["sortOrder"];
}

const TableSorter = ({ label, value, updateSelectedSortColumn, sortColumn, sortOrder }: TableSorterProps) => {
    return (
        <TableCell>
            <Stack direction="row" alignItems="center">
                <Typography>{label}</Typography>
                <IconButton size="small" onClick={() => updateSelectedSortColumn(value, sortColumn !== value ? "asc" : sortColumn === value ? "desc" : "asc")}>
                    <FontAwesomeIcon color={sortColumn === value ? "black" : "lightgrey"} icon={sortColumn !== value ? faArrowUp : sortOrder === "asc" ? faArrowUp : faArrowDown}></FontAwesomeIcon>
                </IconButton>
            </Stack>
        </TableCell>
    );
};

interface UserTableRowProps {
    index: number;
    roles: any[];
    user: any;
    selectedUsers: any[];
    setSelectedUsers: Dispatch<SetStateAction<any[]>>;
}

const UserTableRow = ({ index, roles, user, selectedUsers, setSelectedUsers }: UserTableRowProps) => {
    const [hovered, setHovered] = useState<null | number>(null);

    const isUserSelected = selectedUsers.some((value) => value.id === user.id);

    const updateSelectedUsers = () => {
        if (isUserSelected) {
            setSelectedUsers((prevUsers) => [...prevUsers.filter((value) => value.id !== user.id)]);
        } else {
            setSelectedUsers((prevUsers) => [...prevUsers, user]);
        }
    };

    return (
        <TableRow
            onClick={() => updateSelectedUsers()}
            onMouseEnter={() => setHovered(index)}
            onMouseLeave={() => setHovered(null)}
            hover={index === hovered}
            style={{
                cursor: "pointer"
            }}
        >
            <TableCell>
                <Checkbox checked={isUserSelected}></Checkbox>
            </TableCell>
            <TableCell>{user.nomeutente}</TableCell>
            <TableCell>{roles.filter((role: any) => role.value === user.qualificautente)[0].label}</TableCell>
            <TableCell>{user.referente}</TableCell>
        </TableRow>
    );
};

type searchParams = {
    updateName: (name: string) => void;
};

const Search = ({ updateName }: searchParams) => {
    const [inputValue, setInputValue] = useState("");
    return (
        <>
            <Stack paddingBottom={2} direction="row" maxWidth={300} alignItems="flex-end">
                <CustomAutocomplete
                    sx={{ width: 200 }}
                    disableClearable
                    freeSolo
                    size="small"
                    options={[]}
                    inputValue={inputValue}
                    onInputChange={(_event: any, newInputValue: SetStateAction<string>) => {
                        setInputValue(newInputValue);
                    }}
                    renderInput={(params: any) => <TextField {...params} label="Nome" />}
                />
                <Button variant="contained" size="medium" onClick={() => updateName(inputValue)}>
                    <FontAwesomeIcon icon={faSearch}></FontAwesomeIcon>
                </Button>
            </Stack>
        </>
    );
};

const UserList = ({ setSelectedUserIds }: { setSelectedUserIds: Dispatch<SetStateAction<string>> }) => {
    const [listUserParams, setListUserParams] = useState<useListUserProps>({
        noTemplateVars: true,
        uid: "",
        page: 0,
        pageSize: 10,
        sortColumn: "id",
        sortOrder: "asc",
        nomeSearch: "",
        ruoloSearch: "-1",
        referenteSearch: "-1"
    });

    const updateSelectedSortColumn = (column: useListUserProps["sortColumn"], order: useListUserProps["sortOrder"]) => {
        setListUserParams((prevParams) => ({
            ...prevParams,
            sortColumn: column,
            sortOrder: order
        }));
    };

    const updatePage = (newPage: number) => {
        setListUserParams((prevParams) => ({ ...prevParams, page: newPage }));
    };

    const updateName = (name: string) => {
        setListUserParams((prevParams) => ({
            ...prevParams,
            nomeSearch: name
        }));
    };

    const updateRole = (role: string) => {
        setListUserParams((prevParams) => ({
            ...prevParams,
            ruoloSearch: role
        }));
    };

    const updateReferrer = (referrer: string) => {
        setListUserParams((prevParams) => ({
            ...prevParams,
            referenteSearch: referrer
        }));
    };

    const userId = useParams().uid;

    const { data } = useListUser(listUserParams);
    const { data: userData } = useGetUserUpdate(userId);

    const totalRows = data?.totalRows;
    const allUsers = data?.currentPage;

    const roles: any = [
        { value: "-1", label: "Tutti" },
        { value: "1", label: "Avvocato" },
        { value: "3", label: "Praticante" },
        { value: "4", label: "Segretaria" },
        { value: "5", label: "Altro" },
        { value: "6", label: "Basic" }
    ];

    const [selectedRole, setSelectedRole] = useState({
        value: "-1",
        label: "Tutti"
    });

    const [selectedReferrer, setSelectedReferrer] = useState({
        value: "-1",
        label: "Tutti"
    });

    let referer_users = userData?.referer_users.map((obj: any) => ({
        value: obj.avvocato,
        label: obj.nome
    }));

    referer_users = referer_users && [{ value: "-1", label: "Tutti" }, ...referer_users];

    const users = userData?.users;

    const [selectedUsers, setSelectedUsers] = useState<any[]>([]);

    useEffect(() => {
        if (users) {
            setSelectedUsers(users);
        }
    }, [users]);

    setSelectedUserIds(selectedUsers.map((user) => user.id).join("-"));

    useEffect(() => {
        updateRole(selectedRole.value);
        updateReferrer(selectedReferrer.value);
        updatePage(0);
    }, [selectedRole, selectedReferrer]);

    return (
        <Stack direction="column" gap={3}>
            <Stack direction="column">
                <Typography>UTENTI</Typography>
                <Typography variant="body">Un utente non incluso in un gruppo vedrà tutte le sezioni abilitate.</Typography>
                <Typography variant="body">Un utente presente in più gruppi vedrà le sezioni abilitate di ciascun gruppo.</Typography>
                <Typography variant="body">Cliccare sulla riga per aggiungere/rimuovere l'utente dal gruppo.</Typography>
            </Stack>
            <Stack direction="row" spacing={4}>
                <Search updateName={updateName}></Search>
                <FormControl>
                    <InputLabel>Ruolo</InputLabel>
                    <Select
                        sx={{ width: 200 }}
                        value={selectedRole}
                        onChange={(e: any) => {
                            setSelectedRole(e.target.value);
                        }}
                        renderValue={() => <div>{selectedRole.label}</div>}
                    >
                        {roles?.map((role: any) => (
                            <MenuItem key={role.value} value={role}>
                                {role.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>Referente</InputLabel>
                    <Select sx={{ width: 200 }} value={selectedReferrer} onChange={(e: any) => setSelectedReferrer(e.target.value)} renderValue={() => <div>{selectedReferrer.label}</div>}>
                        {referer_users?.map((role: any) => (
                            <MenuItem key={role.value} value={role}>
                                {role.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Stack>
            <TableContainer>
                <Table>
                    <TableHead shadow>
                        <TableRow>
                            <TableCell></TableCell>
                            <TableSorter label="Nome" sortColumn={listUserParams.sortColumn} sortOrder={listUserParams.sortOrder} updateSelectedSortColumn={updateSelectedSortColumn} value="nomeutente"></TableSorter>
                            <TableSorter label="Ruolo" sortColumn={listUserParams.sortColumn} sortOrder={listUserParams.sortOrder} updateSelectedSortColumn={updateSelectedSortColumn} value="qualificautente"></TableSorter>
                            <TableSorter label="Referente" sortColumn={listUserParams.sortColumn} sortOrder={listUserParams.sortOrder} updateSelectedSortColumn={updateSelectedSortColumn} value="referente"></TableSorter>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {allUsers?.map((user: any, index: number) => (
                            <UserTableRow key={index} index={index} user={user} roles={roles} selectedUsers={selectedUsers} setSelectedUsers={setSelectedUsers}></UserTableRow>
                        ))}
                        {allUsers?.length < 10 &&
                            [...Array(allUsers.length + 2).keys()].map(() => (
                                <TableRow>
                                    <TableCell height={74}></TableCell>
                                    <TableCell height={74}></TableCell>
                                    <TableCell height={74}></TableCell>
                                    <TableCell height={74}></TableCell>
                                </TableRow>
                            ))}
                        <TablePagination labelRowsPerPage="" rowsPerPageOptions={[]} count={totalRows ? parseInt(totalRows) : 0} rowsPerPage={listUserParams.pageSize} page={listUserParams.page} onPageChange={(_event: any, newPage: number) => updatePage(newPage)} />
                    </TableBody>
                </Table>
            </TableContainer>
        </Stack>
    );
};

export default memo(UserList);
