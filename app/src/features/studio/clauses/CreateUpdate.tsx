import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack } from "@vapor/react-material";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../custom-components/FormInput";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import PageTitle from "../../../custom-components/PageTitle";

const ClauseCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [categoryList, setCategoryList] = useState<any>([]);

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? "clauses/update?uniqueid=" + uniqueId
            : "clauses/update"
    );
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        title: yup.string().required(),
        content: yup.string(),
        default: yup.string(),
        category: yup.string().required(),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
    } = useForm({
        resolver: yupResolver(schema),
    });
    const [open, setOpen] = useState(false);

    //api list

    const saveRequest = usePostCustom("clauses/save");

    useEffect(() => {
        async function initData() {
            try {
                const response: any = await updateMountedRequest.doFetch();
                setCategoryList(
                    response.data.archiveCategoryList.map((row: any) => {
                        return {
                            label: row.nome,
                            value: row.id,
                        };
                    })
                );
                if (response.data.archiveCategoryList.length > 0)
                    setValue(
                        "category",
                        response.data.archiveCategoryList[0].id
                    );
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setValue("title", response.data.result.nome);
                    setValue("content", response.data.result.content);
                    setValue("category", response.data.result.category_id);
                    setValue(
                        "default",
                        response.data.result.default === "1" ? "on" : ""
                    );
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let data: any = {
            uniqueid: getValues("uniqueid") ?? "",
            title: getValues("title"),
            content: getValues("content") ?? "",
            category: getValues("category"),
        };
        if (getValues("default") == "on") {
            data.default = getValues("default");
        }
        await saveRequest.doFetch(true, data);
        navigate("/clauses");
    };

    const deleteRequest = usePostCustom(
        uniqueId !== undefined
            ? "clauses/delete?uniqueid=" + uniqueId
            : "clauses/delete"
    );

    const redirectBack = () => {
        navigate("/clauses");
    };

    const handleDelete = () => {
        setOpen(!open);
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/clauses");
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    (uniqueId !== undefined ? "Modifica " : `Nuovo `) +
                    "Clausola"
                }
                pathToPrevPage={"/clauses"}
            />
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={handleAgree}
                decline={"No"}
                agree={"Yes, Delete"}
                confirmText={"Are you sure want to Delete"}
                title={"Delete Action"}
            />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="title"
                                    label="Titolo"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["title"] &&
                                        errors["title"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["title"] &&
                                        errors["title"]["message"]
                                    }
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="category"
                                    label="Categoria"
                                    type="select"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["category"] &&
                                        errors["category"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["category"] &&
                                        errors["category"]["message"]
                                    }
                                    options={categoryList}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="content"
                                    label="Descrizione"
                                    type="textarea"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["content"] &&
                                        errors["content"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["content"] &&
                                        errors["content"]["message"]
                                    }
                                />

                                <FormInput
                                    style={{ width: 400, marginLeft: "0.5em" }}
                                    control={control}
                                    name="default"
                                    label=""
                                    type="checkbox"
                                    variant="outlined"
                                    setValue={setValue}
                                    error={
                                        errors &&
                                        errors["default"] &&
                                        errors["default"]["message"] !== ""
                                            ? true
                                            : false
                                    }
                                    helperText={
                                        errors &&
                                        errors["default"] &&
                                        errors["default"]["message"]
                                    }
                                    options={[
                                        { label: "Preferenziale", value: "on" },
                                    ]}
                                />
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={redirectBack}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId !== undefined && (
                            <>
                                <Button
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    type="button"
                                >
                                    {t("Delete")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default ClauseCreateUpdate;
