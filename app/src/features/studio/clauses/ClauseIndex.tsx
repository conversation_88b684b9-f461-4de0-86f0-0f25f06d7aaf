import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Spinner from "../../../custom-components/Spinner";
import Filters from "./filter";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import useMountedData from "./hooks/useMountedData";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

export default function ClauseIndex() {
    const navigate = useNavigate();

    const { defaultQuery, query, setQuery, list, filterData, loading } =
        useFilter();

    const { mountedData } = useMountedData();

    const handleClickCallback = (uniqueid: string) => {
        navigate("/clauses/update/" + uniqueid);
    };
    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };
    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="clauses"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };
    const redirectAction = () => {
        navigate("/clauses/update");
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button variant="contained" onClick={redirectAction}>
                            Nuova Clausola
                        </Button>,
                    ]}
                    title={"Clausole Modelli"}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        filterData={filterData}
                        mountedData={mountedData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
