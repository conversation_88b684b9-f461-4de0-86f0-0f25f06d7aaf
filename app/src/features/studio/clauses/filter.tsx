import {
    Box,
    Button,
    TextField,
    Select,
    MenuItem,
    InputLabel,
    FormControl,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData, mountedData } = props;
    const { t } = useTranslation();
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: any) => {
        searchWithDebounce(e);
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <TextField
                label={"Nome"}
                variant="outlined"
                name={"nome"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                onKeyPress={handleKeywordKeyPress}
                value={query["nome"]}
            />

            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id={"category-label"}>Categoria</InputLabel>
                <Select
                    id={"category"}
                    name={"category"}
                    labelId={"category-label"}
                    onChange={(e) => onChangeFilterInputs(e)}
                    value={query["category"]}
                >
                    <MenuItem value="-1">{t("Tutte le categorie")}</MenuItem>
                    {(mountedData.archiveCategoryList || []).map(
                        (row: any, i: any) => {
                            return (
                                <MenuItem value={row.id} key={row.id + "-" + i}>
                                    {row.nome}
                                </MenuItem>
                            );
                        }
                    )}
                </Select>
            </FormControl>
            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
