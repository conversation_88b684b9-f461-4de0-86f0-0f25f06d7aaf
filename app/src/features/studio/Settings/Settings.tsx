import { VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../custom-components/PageTitle";
import { Tab, Tabs } from "@vapor/react-extended";
import { useEffect, useState } from "react";
import { useGetUserSettings } from "./hooks/GetUserSettings";
import { General } from "./Pages/General";
import { <PERSON><PERSON><PERSON> } from "./Pages/Pratiche";
import { Agenda } from "./Pages/Agenda";
import { Account } from "./Pages/Account";
import { useUser } from "../../../store/UserStore";
import { useSaveSettings } from "./hooks/SaveUserSettings";
import { parse } from "date-fns";

export interface SettingsProps {
    jws: string;
    alphanumericProgressive: any;
    checkNumProforma: any;
    check_progressive: any;
    onorari: any;
    forfettarie: any;
    imponibili: any;
    non_imponibili: any;
    updateFilesStatus: any;
    updateFilesSituation: any;
    codicearchivio: any;
    archive_code_format: any;
    fileId: any;
    all_files_reserved: any;
    split_siecic: any;
    archive_lawyer: any;
    archive_manual_search: any;
    archive_keep_codicearchivio: any;
    archive_codice_sequenziale_alert: any;
    subjectId: any;
    defaultSubjectName: any;
    defaultSubjectRelation: any;
    headerArchive: string;
    select: string[];
    deadlinesWarningDays: any;
    hearingsWarningDays: any;
    polisweb_data_owner: any;
    move_calendar_referent: any;
    startDateSearch: Date;
    voispeedDomain: any;
    voispeedToken: any;
    voispeedUrl: any;
    sharepoint_base_path: any;
    sharepoint_site: any;
    sharepoint_nested_folder: any;
    sharepoint_user: any;
    sharepoint_password: any;
    invoiceRowDate: any;
    feSuffix: any;
    visibleFromExt: any;
    old_deadlines_referent: any;
    show_deadlines_conflicts: any;
    deadlinesToOwners: any;
    hearingsToOwners: any;
    deadlinesHearingsDone: any;
    oneDriveSmartMailer: any;
    oneDrivePolisweb: any;
    one_drive_access_token: any;
    one_drive_master_name: any;
    one_drive_master_id: any;
    sortAttachmentsInFolder: any;
    seen_email_messages: any;
    hideSharedTemplates: any;
    enableDefaultSubject: any;
    identificatori_pratica: any;
    last_sharepoint_iteration: any;
}

export const Settings = () => {
    const [selectedTab, setSelectedTab] = useState("Generali");
    const [reload, setReload] = useState(false);

    const updateSelectedTab = (
        _event: React.SyntheticEvent,
        newTab: string
    ) => {
        setSelectedTab(newTab);
    };

    const [previousValues, setPreviousValues] = useState<SettingsProps>();

    const { data, hasLoaded } = useGetUserSettings(reload, setReload);

    const responseData = {
        jws: data?.settings.jws,
        alphanumericProgressive: data?.settings.alphanumeric_progressive,
        checkNumProforma: data?.settings.check_num_proforma,
        check_progressive: data?.settings.check_progressive,

        onorari: data && JSON.parse(data?.settings.voci_xml).onorari,
        forfettarie: data && JSON.parse(data?.settings.voci_xml).forfettarie,
        imponibili: data && JSON.parse(data?.settings.voci_xml).imponibili,
        non_imponibili:
            data && JSON.parse(data?.settings.voci_xml).non_imponibili,

        updateFilesStatus: data?.settings.update_files_status,
        updateFilesSituation: data?.settings.update_files_situation,
        codicearchivio: data?.settings.codicearchivio,
        archive_code_format: data?.settings.archive_code_format,
        fileId: data?.settings.file_id,
        all_files_reserved: data?.settings.all_files_reserved,
        split_siecic: data?.settings.split_siecic,
        archive_lawyer: data?.settings.archive_lawyer,
        archive_manual_search: data?.settings.archive_manual_search,
        archive_keep_codicearchivio: data?.settings.archive_keep_codicearchivio,
        archive_codice_sequenziale_alert:
            data?.settings.archive_keep_codicearchivio,
        subjectId: data?.settings.subjectId,
        defaultSubjectName: data?.settings.defaultSubjectName,
        defaultSubjectRelation: data?.settings.defaultSubjectRelation,
        headerArchive: data?.settings.header_archive,
        select: [],

        deadlinesWarningDays: data?.settings.deadlinesWarningDays,
        hearingsWarningDays: data?.settings.hearingsWarningDays,
        polisweb_data_owner: data?.settings.polisweb_data_owner,
        move_calendar_referent: data?.settings.move_calendar_referent,
        startDateSearch: parse(
            data?.settings.startDateSearch,
            "dd/MM/yyyy",
            new Date()
        ),
        voispeedDomain: data?.settings.voispeed_domain,
        voispeedToken: data?.settings.voispeed_token,
        voispeedUrl: data?.settings.voispeed_url,
        sharepoint_base_path: data?.settings.sharepoint_base_path,
        sharepoint_site: data?.settings.sharepoint_site,
        sharepoint_nested_folder: data?.settings.sharepoint_nested_folder,
        sharepoint_user: data?.settings.sharepoint_user,
        sharepoint_password: data?.settings.sharepoint_password,
        invoiceRowDate: data?.settings.invoice_row_date,
        feSuffix: data?.settings.fe_suffix,
        visibleFromExt: data?.settings.visible_from_ext,
        old_deadlines_referent: data?.settings.old_deadlines_referent,
        show_deadlines_conflicts: data?.settings.show_deadlines_conflicts,
        deadlinesToOwners: data?.settings.deadlines_to_owners,
        hearingsToOwners: data?.settings.hearings_to_owners,
        deadlinesHearingsDone: data?.settings.deadlines_hearings_done,
        oneDriveSmartMailer: data?.settings.one_drive_smart_mailer,
        oneDrivePolisweb: data?.settings.oneDrivePolisweb,
        one_drive_access_token: data?.settings.one_drive_access_token,
        one_drive_master_name: data?.settings.one_drive_master_name,
        one_drive_master_id: data?.settings.one_drive_master_id,
        sortAttachmentsInFolder: data?.settings.sort_attachments_in_folder,
        seen_email_messages: data?.settings.seen_email_messages,
        hideSharedTemplates: data?.settings.hide_shared_templates,
        enableDefaultSubject: data?.settings.enableDefaultSubject,
        identificatori_pratica: data?.settings.identificatori_pratica,
        last_sharepoint_iteration: data?.settings.last_sharepoint_iteration,
    };

    useEffect(() => {
        if (data && data.settings) {
            setPreviousValues(responseData);
        }
    }, [data, hasLoaded]);

    const { modules } = useUser() as any;
    const userHasAccess =
        !modules.isNetlexpdaUser &&
        !modules.isNetlexpdabasicUser &&
        !modules.isNetlexpdafreeUser;

    const {} = useSaveSettings(previousValues);

    const handleSettingsChange = (
        fieldName: keyof SettingsProps,
        value: any
    ) => {
        if (previousValues) {
            setPreviousValues(() => ({
                ...previousValues,
                [fieldName]: value,
            }));
        }
    };

    return (
        <VaporPage>
            <PageTitle title="Settings" showBackButton={false}></PageTitle>
            <VaporPage.Section>
                <Tabs
                    value={selectedTab}
                    onChange={updateSelectedTab}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label="Generali" value="Generali" />
                    <Tab label="Pratiche" value="Pratiche" />
                    <Tab label="Agenda" value="Agenda" />
                    <Tab label="Account esterni" value="Account esterni" />
                </Tabs>
            </VaporPage.Section>
            {previousValues && (
                <VaporPage.Section>
                    {selectedTab === "Generali" && (
                        <General
                            previousValues={previousValues}
                            userHasAccess={userHasAccess}
                            handleSettingsChange={handleSettingsChange}
                        />
                    )}
                    {selectedTab === "Pratiche" && (
                        <Pratiche
                            setReload={setReload}
                            previousValues={previousValues}
                            userHasAccess={userHasAccess}
                            handleSettingsChange={handleSettingsChange}
                        />
                    )}
                    {selectedTab === "Agenda" && (
                        <Agenda
                            previousValues={previousValues}
                            userHasAccess={userHasAccess}
                            handleSettingsChange={handleSettingsChange}
                        />
                    )}
                    {selectedTab === "Account esterni" && (
                        <Account
                            previousValues={previousValues}
                            userHasAccess={userHasAccess}
                            handleSettingsChange={handleSettingsChange}
                        />
                    )}
                </VaporPage.Section>
            )}
        </VaporPage>
    );
};
