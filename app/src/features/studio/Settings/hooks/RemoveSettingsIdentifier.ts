import { Dispatch, SetStateAction, useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useRemoveIdentifier = (
    identifier: string,
    setReload: Dispatch<SetStateAction<boolean>>
) => {
    const { loading, data, error, doFetch } = usePostCustom(
        "netlexsettings/rimuoviidentificatore"
    );

    useEffect(() => {
        if (identifier !== "") {
            doFetch(true, {
                identificatore: identifier,
            });
            setReload(true);
        }
    }, [identifier]);

    return { loading, data, error };
};
