import { Dispatch, SetStateAction, useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useAddIdentifier = (
    identifier: string,
    addIdentifier: boolean,
    setReload: Dispatch<SetStateAction<boolean>>
) => {
    const { loading, data, error, doFetch, hasLoaded } = usePostCustom(
        "netlexsettings/salvaidentificatore"
    );

    useEffect(() => {
        if (addIdentifier) {
            doFetch(true, {
                identificatore: identifier,
            });
            setReload(true);
        }
    }, [addIdentifier]);

    return { loading, data, error, hasLoaded };
};
