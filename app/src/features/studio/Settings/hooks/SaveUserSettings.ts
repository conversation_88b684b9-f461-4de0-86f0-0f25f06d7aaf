import { useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import { format } from "date-fns";
import debounce from "lodash/debounce";
import { SettingsProps } from "../Settings";

export const useSaveSettings = (previousValues: SettingsProps | undefined) => {
    const settings = {
        jws: previousValues?.jws,
        alphanumericProgressive: previousValues?.alphanumericProgressive,
        checkNumProforma: previousValues?.checkNumProforma,
        onorari: previousValues?.onorari,
        forfettarie: previousValues?.forfettarie,
        imponibili: previousValues?.imponibili,
        non_imponibili: previousValues?.non_imponibili,
        ...(previousValues?.archive_code_format >= 4 &&
            previousValues?.archive_code_format < 99 && {
                archive_code_format: previousValues?.archive_code_format,
            }),

        fileId: previousValues?.fileId,
        ...(previousValues?.defaultSubjectName && {
            defaultSubjectName: previousValues?.defaultSubjectName,
        }),
        ...(previousValues?.deadlinesWarningDays &&
            parseInt(previousValues?.deadlinesWarningDays) > 0 && {
                deadlinesWarningDays: parseInt(
                    previousValues?.deadlinesWarningDays
                ),
            }),
        ...(previousValues?.hearingsWarningDays &&
            parseInt(previousValues?.hearingsWarningDays) > 0 && {
                hearingsWarningDays: parseInt(
                    previousValues?.hearingsWarningDays
                ),
            }),
        ...(previousValues?.startDateSearch && {
            startDateSearch: format(
                previousValues?.startDateSearch,
                "dd/MM/yyyy"
            ),
        }),

        ...(previousValues?.seen_email_messages === "1" && {
            seenEmailMessages: "on",
        }),
        ...(previousValues?.updateFilesStatus === "1" && {
            updateFilesStatus: "on",
        }),
        ...(previousValues?.updateFilesSituation === "1" && {
            updateFilesSituation: "on",
        }),
        ...(previousValues?.invoiceRowDate === "1" && { invoiceRowDate: "on" }),
        ...(previousValues?.hideSharedTemplates === "1" && {
            hideSharedTemplates: "on",
        }),
        ...(previousValues?.all_files_reserved === "1" && {
            all_files_reserved: "on",
        }),
        ...(previousValues?.split_siecic === "1" && { split_siecic: "on" }),
        ...(previousValues?.archive_lawyer === "1" && { archive_lawyer: "on" }),
        ...(previousValues?.archive_manual_search === "1" && {
            archive_manual_search: "on",
        }),
        ...(previousValues?.archive_keep_codicearchivio === "1" && {
            archive_keep_codicearchivio: "on",
        }),
        ...(previousValues?.archive_codice_sequenziale_alert === "1" && {
            archive_codice_sequenziale_alert: "on",
        }),
        ...(previousValues?.enableDefaultSubject === "1" && {
            enableDefaultSubject: "on",
        }),
        ...(previousValues?.deadlinesToOwners === "1" && {
            deadlinesToOwners: "on",
        }),
        ...(previousValues?.hearingsToOwners === "1" && {
            hearingsToOwners: "on",
        }),
        ...(previousValues?.deadlinesHearingsDone === "1" && {
            deadlinesHearingsDone: "on",
        }),
        ...(previousValues?.oneDriveSmartMailer === "1" && {
            oneDriveSmartMailer: "on",
        }),
        ...(previousValues?.oneDrivePolisweb === "1" && {
            oneDrivePolisweb: "on",
        }),
        ...(previousValues?.visibleFromExt === "1" && { visibleFromExt: "on" }),
        ...(previousValues?.old_deadlines_referent === "1" && {
            old_deadlines_referent: "on",
        }),
        ...(previousValues?.visibleFromExt === "1" && { visibleFromExt: "on" }),
        ...(previousValues?.show_deadlines_conflicts === "1" && {
            show_deadlines_conflicts: "on",
        }),
        ...(previousValues?.feSuffix === "1" && { feSuffix: "on" }),
        ...(previousValues?.sortAttachmentsInFolder === "1" && {
            sortAttachmentsInFolder: "on",
        }),

        defaultSubjectRelation: previousValues?.defaultSubjectRelation,
        defaultSubjectName: previousValues?.defaultSubjectName,
        select: previousValues?.select,
        headerArchive: previousValues?.headerArchive,
        move_calendar_referent: previousValues?.move_calendar_referent,
        polisweb_data_owner: previousValues?.polisweb_data_owner,
        voispeedDomain: previousValues?.voispeedDomain,
        voispeedToken: previousValues?.voispeedToken,
        voispeedUrl: previousValues?.voispeedUrl,

        sharepoint_base_path: previousValues?.sharepoint_base_path,
        sharepoint_site: previousValues?.sharepoint_site,
        sharepoint_nested_folder: previousValues?.sharepoint_nested_folder,
        sharepoint_user: previousValues?.sharepoint_user,
        sharepoint_password: previousValues?.sharepoint_password,
    };

    const { loading, data, error, doFetch } = usePostCustom(
        "netlexsettings/save"
    );

    const debouncedSaveSettings = debounce(() => {
        doFetch(true, settings);
    }, 1000);

    useEffect(() => {
        debouncedSaveSettings();

        return () => {
            debouncedSaveSettings.cancel();
        };
    }, [previousValues]);

    return { loading, data, error };
};
