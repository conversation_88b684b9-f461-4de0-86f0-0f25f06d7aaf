import { Dispatch, SetStateAction, useEffect } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";

export const useChangeIdentifierMode = (
    identifier: {
        name: string;
        mode: string;
    },
    setReload: Dispatch<SetStateAction<boolean>>
) => {
    const { loading, data, error, doFetch } = usePostCustom(
        "netlexsettings/change-identificatore-mode"
    );

    useEffect(() => {
        if (identifier.name !== "") {
            doFetch(true, {
                identificatoreName: identifier.name,
                mode: identifier.mode === "prefix" ? "suffix" : "prefix",
            });
            setReload(true);
        }
    }, [identifier.name]);

    return { loading, data, error };
};

// change-identificatore-mode
