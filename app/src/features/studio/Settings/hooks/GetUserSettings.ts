import { Dispatch, SetStateAction, useEffect } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useGetUserSettings = (
    reload: boolean,
    setReload: Dispatch<SetStateAction<boolean>>
) => {
    const { data, doFetch, error, loading, hasLoaded } = useGetCustom(
        "netlexsettings/settings"
    );

    console.log("reload", reload);

    useEffect(() => {
        doFetch();
    }, []);

    useEffect(() => {
        if (reload) {
            doFetch();
            setReload(false);
        }
    }, [reload]);

    return { data, loading, error, hasLoaded };
};
