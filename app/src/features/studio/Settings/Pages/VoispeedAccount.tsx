import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

export const VoispeedAccount = ({
    previousValues,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    handleSettingsChange: any;
}) => {
    const { t } = useTranslation();
    return (
        <>
            <Card sx={{ maxWidth: "500px" }}>
                <CardHeader title={t("Account Voispeed")}></CardHeader>
                <CardContent>
                    <Stack gap={2}>
                        <Stack direction="row" gap={1}>
                            <TextField
                                value={previousValues.voispeedDomain}
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "voispeed_domain",
                                        e.target.value
                                    )
                                }
                                placeholder="Es. studiosrl.ucloud"
                                size="small"
                                label={t("Dominio")}
                            />
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                value={previousValues.voispeedToken}
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "voispeed_token",
                                        e.target.value
                                    )
                                }
                                placeholder="Es. 0ad99b81194b6766hf7e2i31c8970f5c"
                                size="small"
                                label={t("Token")}
                            />
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                value={previousValues.voispeedUrl}
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "voispeed_url",
                                        e.target.value
                                    )
                                }
                                placeholder="Es. sheratan.cluana.com:3542/PBX/seri_endpoint.php"
                                size="small"
                                label={t("Url integrazione")}
                            />
                        </Stack>
                    </Stack>
                </CardContent>
            </Card>
        </>
    );
};
