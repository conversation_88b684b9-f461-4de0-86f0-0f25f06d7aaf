import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON><PERSON>er,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useLoginMicrosoft } from "../hooks/OnedriveLogin";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

export const OneDriveAccount = ({
    previousValues,
}: {
    previousValues: SettingsProps;
}) => {
    const { data } = useLoginMicrosoft();

    const loginMicrosoft = () => {
        window.location.href = data._redirectInfo.url;
    };

    const logoutMicrosoft = () => {
        window.location.href = data._redirectInfo.url;
    };

    const { t } = useTranslation();
    return (
        <>
            {!previousValues.one_drive_access_token ? (
                <Card sx={{ maxWidth: "500px", minHeight: "200px" }}>
                    <Button onClick={() => loginMicrosoft()}>
                        {t("Collega Account Microsoft")}
                    </Button>
                </Card>
            ) : (
                <Card sx={{ maxWidth: "500px" }}>
                    <CardHeader title="Account OneDrive collegato"></CardHeader>
                    <CardContent>
                        <Stack direction="row" gap={1}>
                            <Typography>{t("Nome account")}</Typography>
                            <Typography>
                                {previousValues.one_drive_master_name}
                            </Typography>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <Typography>{t("Id account")}</Typography>
                            <Typography>
                                {previousValues.one_drive_master_id}
                            </Typography>
                        </Stack>
                    </CardContent>
                    <CardActions>
                        <Button color="error" onClick={() => logoutMicrosoft()}>
                            {t("Scollega")}
                        </Button>
                    </CardActions>
                </Card>
            )}
        </>
    );
};
