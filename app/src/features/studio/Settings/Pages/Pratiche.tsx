import {
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Stack,
    Radio,
    RadioGroup,
    FormControl,
    Box,
    TextField,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";
import { DefaultPracticeSubject } from "./DefaultPracticeSubject";
import { TagOptions } from "./TagInput";
import { ArchiveIdentifiers } from "./ArchiveIdentifierCodes";
import { SettingsProps } from "../Settings";

export const Pratiche = ({
    setReload,
    previousValues,
    userHasAccess,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    userHasAccess: boolean;
    handleSettingsChange: (fieldName: keyof SettingsProps, value: any) => void;
    setReload: any;
}) => {
    const { t } = useTranslation();

    return (
        <Stack direction="column" gap={5}>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Aggiornamento automatico degli stati delle pratiche")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.updateFilesStatus === "1"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "updateFilesStatus",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                                name="update_files_status"
                                value="1"
                            />
                        }
                        label={t(
                            "Aggiornare automaticamente gli stati delle pratiche con quelli Polisweb "
                        )}
                    />
                </FormGroup>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.updateFilesSituation === "1"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "updateFilesSituation",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                                name="update_files_situation"
                                value="1"
                            />
                        }
                        label={t(
                            "Aggiorna automaticamente la situazione pratica con lo stato Polisweb "
                        )}
                    />
                </FormGroup>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={previousValues.visibleFromExt === "1"}
                                name="visible_from_ext"
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "visibleFromExt",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                                value="1"
                            />
                        }
                        label={t(
                            "Rendere visibili agli utenti esterni gli impegni e i documenti Polisweb"
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Codice archivio automatico in creazione")}
                </FormLabel>
                <RadioGroup
                    name="codicearchivio"
                    value={previousValues.codicearchivio}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("codicearchivio", e.target.value)
                    }
                >
                    <FormControlLabel
                        value="1"
                        control={<Radio />}
                        label={t(
                            "Non assegnato automaticamente se vuoto (Verrà proposto un codice archivio autoincrementale, è possibile modificarlo o lasciarlo vuoto, in tal caso non verrà assegnato un codice in automatico)"
                        )}
                    />
                    <FormControlLabel
                        value="0"
                        control={<Radio />}
                        label={t(
                            "Assegnato automaticamente se vuoto (Non verrà proposto un codice archivio, verrà assegnato al salvataggio) "
                        )}
                    />
                </RadioGroup>
            </FormControl>

            <FormControl>
                <FormLabel component={Typography}>
                    {t(
                        "Formato codice archivio (Se lasciato vuoto verra' visualizzato solo il numero progressivo)"
                    )}
                </FormLabel>
                <Box style={{ maxWidth: "200px" }}>
                    <Stack direction="row">
                        <TextField
                            name="archive_code_format"
                            value={previousValues.archive_code_format}
                            onChange={(e: { target: { value: any } }) =>
                                handleSettingsChange(
                                    "archive_code_format",
                                    e.target.value
                                )
                            }
                            inputProps={{
                                min: 4,
                                max: 99,
                                style: { textAlign: "left" },
                            }}
                            type="number"
                            error={
                                Number(previousValues.archive_code_format) <
                                    4 ||
                                Number(previousValues.archive_code_format) > 99
                            }
                        />
                    </Stack>
                </Box>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    Modalità d'uso del codice pratica
                </FormLabel>
                <RadioGroup name="fileId" value={previousValues.fileId}>
                    <FormControlLabel
                        value="fileCode"
                        control={<Radio />}
                        label={t('Visualizzare solo "Codice pratica"')}
                    />
                    <FormControlLabel
                        value="fileArchiveCode"
                        control={<Radio />}
                        label={t('Visualizzare solo Codice archivio" ')}
                    />
                    <FormControlLabel
                        value="none"
                        control={<Radio />}
                        label={t("Non Visualizzare nessun codice ")}
                    />
                </RadioGroup>
            </FormControl>
            {userHasAccess && (
                <FormControl>
                    <FormLabel component={Typography}>
                        {t("Riserva automatica delle nuove pratiche")}
                    </FormLabel>
                    <FormGroup>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={
                                        previousValues.all_files_reserved ===
                                        "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) => {
                                        handleSettingsChange(
                                            "all_files_reserved",
                                            e.target.checked ? "1" : "0"
                                        );
                                    }}
                                    name="all_files_reserved"
                                    value="1"
                                />
                            }
                            label={t(
                                "Riservare automaticamente le nuove pratiche all'avvocato intestatario (Anche per sincronizzazione con Polisweb)"
                            )}
                        />
                    </FormGroup>
                </FormControl>
            )}

            <FormControl>
                <FormLabel component={Typography}>
                    {t("Esecuzioni mobiliari/immobiliari")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={previousValues.split_siecic === "1"}
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "split_siecic",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="split_siecic"
                                value="1"
                            />
                        }
                        label={t(
                            "Sincronizza i procedimenti di una stessa esecuzione in fascicoli separati"
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Responsabile pratica")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={previousValues.archive_lawyer === "1"}
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "archive_lawyer",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="archive_lawyer"
                                value="1"
                            />
                        }
                        label={t(
                            "L'avvocato è anche responsabile (anche per sincronizzazione con Polisweb)"
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Ricerca manuale")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.archive_manual_search === "1"
                                }
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "archive_manual_search",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="archive_manual_search"
                                value="1"
                            />
                        }
                        label={t(
                            "Disabilita la ricerca automatica pratiche all'apertura della pagina (Per vedere i risultati occorre cliccare su cerca o mostra tutti)"
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Duplicazione pratica")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.archive_keep_codicearchivio ===
                                    "1"
                                }
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "archive_keep_codicearchivio",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="archive_keep_codicearchivio"
                                value="1"
                            />
                        }
                        label={t("Mantieni il codice archivio ")}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Sequenzialità codice archivio")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.archive_codice_sequenziale_alert ===
                                    "1"
                                }
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "archive_codice_sequenziale_alert",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="archive_codice_sequenziale_alert"
                                value="1"
                            />
                        }
                        label={t("Obbligo codice archivio sequenziale")}
                    />
                </FormGroup>
            </FormControl>
            <DefaultPracticeSubject
                previousValues={previousValues}
            ></DefaultPracticeSubject>
            <FormControl>
                <FormLabel>
                    {t("Personalizzazione dell'intestazione della pratica")}
                </FormLabel>
                <Typography variant="bodySmall500">
                    {t(
                        "E' possibile utilizzare tag predefiniti e combinarli con del testo libero."
                    )}
                </Typography>
                {userHasAccess && (
                    <TagOptions
                        handleSettingsChange={handleSettingsChange}
                        previousValues={previousValues}
                    ></TagOptions>
                )}
            </FormControl>

            <ArchiveIdentifiers
                handleSettingsChange={handleSettingsChange}
                setReload={setReload}
                previousValues={previousValues}
            />
        </Stack>
    );
};
