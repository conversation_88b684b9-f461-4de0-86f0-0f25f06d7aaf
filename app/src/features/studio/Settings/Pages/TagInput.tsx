import { <PERSON><PERSON>, <PERSON>, TextField } from "@vapor/react-material";
import { SetStateAction, useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

export const TagOptions = ({
    handleSettingsChange,
    previousValues,
}: {
    handleSettingsChange: any;
    previousValues: SettingsProps;
}) => {
    const [headerArchive, setHeaderArchive] = useState("");
    const { t } = useTranslation();

    useEffect(() => {
        handleSettingsChange("headerArchive", headerArchive);
    }, [headerArchive]);

    useEffect(() => {
        if (previousValues.headerArchive) {
            setHeaderArchive(previousValues.headerArchive);
        }
    }, [previousValues]);

    return (
        <Stack direction="column" width={500} gap={3}>
            <Stack direction="row" gap={1}>
                {[
                    t("Lista clienti"),
                    t("Lista controparti"),
                    t("Altri soggetti"),
                    t("Codice archivio"),
                    t("Codice pratica"),
                    t("Sede"),
                    t("Oggetto"),
                    t("Stato"),
                    t("Tipologia"),
                    t("Descrizione"),
                    t("Nome Pratica"),
                    t("RG"),
                    t("Autorità"),
                ].map((s: string) => (
                    <Chip
                        onClick={() =>
                            setHeaderArchive(`${headerArchive} [${s}]`)
                        }
                        style={{ cursor: "pointer" }}
                        size="small"
                        label={s}
                    ></Chip>
                ))}
            </Stack>
            <TextField
                value={headerArchive}
                onChange={(e: { target: { value: SetStateAction<string> } }) =>
                    setHeaderArchive(e.target.value)
                }
            ></TextField>
        </Stack>
    );
};
