import { FormLabel, FormGroup, FormControlLabel, Checkbox, FormControl, TextField, Select, MenuItem, Stack } from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { SetStateAction, useState } from "react";
import { useGetAnagrafiche } from "../hooks/SearchAnagrafiche";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

export const DefaultPracticeSubject = ({ previousValues }: { previousValues: SettingsProps }) => {
    const { t } = useTranslation();
    const [searchParam, setSearchParam] = useState("");
    const [defaultRelation, setDefaultRelation] = useState(previousValues.defaultSubjectRelation || "4");
    const { data } = useGetAnagrafiche(searchParam);
    const suggestionValues = Array.isArray(data) ? data.map((value: any) => value.denominazione) : [];

    return (
        <FormControl sx={{ maxWidth: "500px" }}>
            <FormLabel component={Typography}>{t("Soggetto di default pratica")}</FormLabel>
            <FormGroup>
                <FormControlLabel value="0" control={<Checkbox defaultChecked={previousValues.archive_codice_sequenziale_alert === "1"} />} label={t("Usa soggetto di default ")} />
            </FormGroup>
            <Stack direction="row" gap={2}>
                <CustomAutocomplete
                    value={previousValues.defaultSubjectName || ""}
                    onBlur={() => {}}
                    onChange={() => {}}
                    noOptionsText={""}
                    onInputChange={(_event: any, newInputValue: SetStateAction<string>) => {
                        setSearchParam(newInputValue);
                    }}
                    inputValue={searchParam}
                    options={suggestionValues}
                    renderInput={(params: any) => <TextField {...params} placeholder={t("Cerca soggeto")} />}
                ></CustomAutocomplete>
                <Select sx={{ minWidth: "200px" }} size="small" value={defaultRelation} onChange={(e: any) => setDefaultRelation(e.target.value)}>
                    <MenuItem value="4">{t("Altro")}</MenuItem>
                    <MenuItem value="5">{t("Dominus")}</MenuItem>
                    <MenuItem value="6">{t("Procuratore")}</MenuItem>
                    <MenuItem value="7">{t("Esterno")}</MenuItem>
                    <MenuItem value="20">{t("Cointestatario")}</MenuItem>
                    <MenuItem value="21">{t("Domiciliatario")}</MenuItem>
                    <MenuItem value="22">{t("Corrispondente")}</MenuItem>
                    <MenuItem value="23">{t("Cliente Fatturazione")}</MenuItem>
                </Select>
            </Stack>
        </FormControl>
    );
};
