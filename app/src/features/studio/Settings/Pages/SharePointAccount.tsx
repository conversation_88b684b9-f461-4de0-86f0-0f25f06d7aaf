import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>Header,
    TextField,
    Button,
    CardActions,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

export const SharePointAccount = ({
    previousValues,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    handleSettingsChange: any;
}) => {
    const { t } = useTranslation();

    return (
        <>
            <Card sx={{ maxWidth: "500px" }}>
                <CardHeader title="Account SharePoint"></CardHeader>
                <CardContent>
                    <Stack gap={2}>
                        <Stack direction="row" gap={1}>
                            <TextField
                                defaultValue={
                                    previousValues.sharepoint_base_path
                                }
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "sharepoint_base_path",
                                        e.target.value
                                    )
                                }
                                placeholder="Es.https://teamsystem.sharepoint.com"
                                size="small"
                                label={t("Dominio SharePoint")}
                            ></TextField>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                defaultValue={previousValues.sharepoint_site}
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "sharepoint_site",
                                        e.target.value
                                    )
                                }
                                placeholder="Es. Gruppo-Avvocati"
                                size="small"
                                label={t("Sito")}
                            ></TextField>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                defaultValue={
                                    previousValues.sharepoint_nested_folder
                                }
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "sharepoint_nested_folder",
                                        e.target.value
                                    )
                                }
                                placeholder="Es. Documenti Condivisi/testcartella"
                                size="small"
                                label={t("Cartella")}
                            ></TextField>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                defaultValue={previousValues.sharepoint_user}
                                placeholder="Es. <EMAIL>"
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "sharepoint_user",
                                        e.target.value
                                    )
                                }
                                size="small"
                                label={t("Utente")}
                            ></TextField>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <TextField
                                placeholder="*******"
                                onChange={(e: { target: { value: any } }) =>
                                    handleSettingsChange(
                                        "sharepoint_password",
                                        e.target.value
                                    )
                                }
                                size="small"
                                label="Password"
                                type={t("password")}
                            ></TextField>
                        </Stack>
                        <CardActions>
                            <Button>{t("Conferma")}</Button>
                        </CardActions>
                    </Stack>
                </CardContent>
            </Card>
        </>
    );
};
