import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Di<PERSON><PERSON>,
    <PERSON>,
    <PERSON>Item,
    <PERSON>conButton,
    ListItemText,
    TextField,
} from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faTrash,
    faCircleCheck,
    faCircleMinus,
} from "@fortawesome/free-solid-svg-icons";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useRemoveIdentifier } from "../hooks/RemoveSettingsIdentifier";
import { useAddIdentifier } from "../hooks/AddSettingsIdentifier";
import { useChangeIdentifierMode } from "../hooks/ChangeIdentifierMode";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

const NewIdentifier = ({
    setReload,
}: {
    setReload: Dispatch<SetStateAction<boolean>>;
}) => {
    const { t } = useTranslation();
    const [addIdentifier, setAddIdentifier] = useState(false);
    const [newIdentifier, setNewIdentifier] = useState("");

    const {} = useAddIdentifier(newIdentifier, addIdentifier, setReload);

    useEffect(() => {
        if (addIdentifier) {
            setReload(true);
        }
    }, [addIdentifier]);

    useEffect(() => {
        if (addIdentifier === true) {
            setAddIdentifier(false);
        }
    }, [addIdentifier]);

    return (
        <Stack direction="row" justifyContent="space-between">
            <TextField
                value={newIdentifier}
                onChange={(e: any) => setNewIdentifier(e.target.value)}
                placeholder={t("Aggiungi sezionale codice archivio")}
            />
            <Button
                onClick={() => {
                    setAddIdentifier(true);
                    setNewIdentifier("");
                }}
                disabled={newIdentifier === ""}
            >
                {t("Conferma")}
            </Button>
        </Stack>
    );
};

export const ArchiveIdentifiers = ({
    previousValues,
    handleSettingsChange,
    setReload,
}: {
    previousValues: SettingsProps;
    handleSettingsChange: any;
    setReload: Dispatch<SetStateAction<boolean>>;
}) => {
    const [identifierRemove, setIdentifierRemove] = useState("");
    const [identifierMode, setIdentifierMode] = useState({
        name: "",
        mode: "",
    });

    const identifiers = JSON.parse(previousValues.identificatori_pratica);

    useRemoveIdentifier(identifierRemove, setReload);
    useChangeIdentifierMode(identifierMode, setReload);

    const archiveIdentifiersNames = previousValues
        ? Object.values(identifiers).map((e: any) => e.name)
        : [];

    useEffect(() => {
        handleSettingsChange("select", archiveIdentifiersNames);
    }, [archiveIdentifiersNames?.length]);

    return (
        <Stack direction="column" maxWidth={500}>
            {Object.values(identifiers).map((value: any, index: number) => (
                <List key={index}>
                    <ListItem
                        style={{
                            border: "1px solid white",
                        }}
                        dense
                        sx={{ justifyContent: "space-between" }}
                        secondaryAction={
                            <div>
                                <Button
                                    variant="text"
                                    color="warning"
                                    onClick={() => setIdentifierMode(value)}
                                >
                                    Usa prefisso
                                </Button>
                                <IconButton
                                    color="error"
                                    onClick={() =>
                                        setIdentifierRemove(value.name)
                                    }
                                >
                                    <FontAwesomeIcon icon={faTrash} />
                                </IconButton>
                            </div>
                        }
                    >
                        {value.mode === "prefix" ? (
                            <FontAwesomeIcon
                                icon={faCircleCheck}
                                color="green"
                            ></FontAwesomeIcon>
                        ) : (
                            <FontAwesomeIcon
                                icon={faCircleMinus}
                                color="grey"
                            ></FontAwesomeIcon>
                        )}
                        <ListItemText sx={{ paddingLeft: "16px" }}>
                            {value.name}
                        </ListItemText>
                    </ListItem>
                    <Divider variant="middle"></Divider>
                </List>
            ))}
            <NewIdentifier setReload={setReload}></NewIdentifier>
        </Stack>
    );
};
