import {
    FormLabel,
    FormControlLabel,
    Checkbox,
    Stack,
    FormControl,
    Button,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";
import { OneDriveAccount } from "./OneDriveAccount";
import { VoispeedAccount } from "./VoispeedAccount";
import { SharePointAccount } from "./SharePointAccount";
import { SettingsProps } from "../Settings";

export const Account = ({
    previousValues,
    userHasAccess,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    userHasAccess: boolean;
    handleSettingsChange: (fieldName: keyof SettingsProps, value: any) => void;
}) => {
    const { t } = useTranslation();

    return (
        <Stack direction="column" gap={5}>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Impostazioni One Drive")}
                </FormLabel>

                <FormControlLabel
                    control={
                        <Checkbox
                            checked={previousValues.oneDriveSmartMailer === "1"}
                            onChange={(e: { target: { checked: any } }) =>
                                handleSettingsChange(
                                    "oneDriveSmartMailer",
                                    e.target.checked ? "1" : "0"
                                )
                            }
                        />
                    }
                    label={t(
                        "Carica gli allegati delle email smistate con lo Smart Mailer direttamente su OneDrive"
                    )}
                />

                <FormControlLabel
                    control={
                        <Checkbox
                            checked={previousValues.oneDrivePolisweb === "1"}
                            onChange={(e: { target: { checked: any } }) =>
                                handleSettingsChange(
                                    "oneDrivePolisweb",
                                    e.target.checked ? "1" : "0"
                                )
                            }
                        />
                    }
                    label={t(
                        "Scarica i documenti polisweb direttamente su One Drive "
                    )}
                />
            </FormControl>
            {userHasAccess && (
                <>
                    <OneDriveAccount
                        previousValues={previousValues}
                    ></OneDriveAccount>
                    <VoispeedAccount
                        handleSettingsChange={handleSettingsChange}
                        previousValues={previousValues}
                    ></VoispeedAccount>
                    <SharePointAccount
                        handleSettingsChange={handleSettingsChange}
                        previousValues={previousValues}
                    ></SharePointAccount>
                    <FormControl>
                        <FormLabel>Ultimo Aggiornamento Sharepoint:</FormLabel>
                        <>
                            {previousValues &&
                            previousValues.last_sharepoint_iteration
                                ? previousValues.last_sharepoint_iteration
                                : t("Non ancora eseguito")}
                            {previousValues &&
                                previousValues.last_sharepoint_iteration && (
                                    <Button>
                                        {t("Richiedi Aggiornamento")}
                                    </Button>
                                )}
                        </>
                    </FormControl>
                </>
            )}
        </Stack>
    );
};
