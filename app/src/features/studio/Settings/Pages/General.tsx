import {
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Stack,
    Radio,
    TextField,
    Tooltip,
    FormControl,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { RadioGroup } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleInfo } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { SettingsProps } from "../Settings";

export const General = ({
    previousValues,
    userHasAccess,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    userHasAccess: boolean;
    handleSettingsChange: (fieldName: keyof SettingsProps, value: any) => void;
}) => {
    const { t } = useTranslation();

    return (
        <Stack direction="column" gap={5}>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Modalità d'uso di Java")}
                </FormLabel>
                <RadioGroup
                    value={previousValues.jws}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("jws", e.target.value)
                    }
                    name="jws"
                >
                    <FormControlLabel
                        value="0"
                        control={<Radio />}
                        label="Applet"
                    />
                    <FormControlLabel
                        value="1"
                        control={<Radio />}
                        label="Web Start"
                    />
                </RadioGroup>
            </FormControl>

            {userHasAccess && (
                <FormControl>
                    <FormLabel component={Typography}>
                        {t("Numerazione preavvisi di parcella")}
                    </FormLabel>
                    <RadioGroup
                        value={previousValues.alphanumericProgressive}
                        onChange={(e: { target: { value: any } }) =>
                            handleSettingsChange(
                                "alphanumericProgressive",
                                e.target.value
                            )
                        }
                        name="alphanumericProgressive"
                    >
                        <FormControlLabel
                            value="0"
                            control={<Radio />}
                            label={t("Progressivo numerico")}
                        />
                        <FormControlLabel
                            value="1"
                            control={<Radio />}
                            label={t("Progressivo alfanumerico")}
                        />
                    </RadioGroup>
                </FormControl>
            )}

            <FormControl>
                <Stack direction="row" gap={1} alignItems="center">
                    <FormLabel component={Typography}>
                        {t(
                            "Controllo di integrità sui progressivi nei preavvisi di parcella"
                        )}
                    </FormLabel>
                    <Tooltip
                        title={t(
                            "Aiutano a mantenere tale il progressivo, mostrando indicazioni sulla data in base agli inserimenti precedenti"
                        )}
                        placement="top"
                        arrow
                    >
                        <FontAwesomeIcon
                            icon={faCircleInfo}
                            color="#0090D1"
                        ></FontAwesomeIcon>
                    </Tooltip>
                </Stack>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.check_progressive === "1"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "check_progressive",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                                name="checkProgressive"
                            />
                        }
                        label={t("Si")}
                    />
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.check_progressive === "0"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "check_progressive",
                                        e.target.checked ? "0" : "1"
                                    )
                                }
                                name="checkProgressive"
                            />
                        }
                        label={t("No")}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <Stack direction="row" gap={1} alignItems="center">
                    <FormLabel component={Typography}>
                        {t("Disabilita numerazione Fatture Proforma")}
                    </FormLabel>
                    <Tooltip
                        title={t(
                            "Disabilita o abilita la possibilità di numerare le fatture proforma"
                        )}
                        placement="top"
                        arrow
                    >
                        <FontAwesomeIcon
                            icon={faCircleInfo}
                            color="#0090D1"
                        ></FontAwesomeIcon>
                    </Tooltip>
                </Stack>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.checkNumProforma === "1"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "checkNumProforma",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                                name="checkNumProforma"
                            />
                        }
                        label={t("Si")}
                    />
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.checkNumProforma === "0"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "checkNumProforma",
                                        e.target.checked ? "0" : "1"
                                    )
                                }
                                name="checkNumProforma"
                            />
                        }
                        label={t("No")}
                    />
                </FormGroup>
            </FormControl>

            <FormControl style={{ maxWidth: "500px" }}>
                <FormLabel>{t("Raggruppa voci XML")}</FormLabel>
                <TextField
                    label={t("Descrizione onorari")}
                    variant="outlined"
                    margin="normal"
                    size="small"
                    name="onorari"
                    value={previousValues.onorari}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("onorari", e.target.value)
                    }
                />

                <TextField
                    label={t("Descrizione Spese forfettarie")}
                    variant="outlined"
                    margin="normal"
                    size="small"
                    name="forfettarie"
                    value={previousValues.forfettarie}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("forfettarie", e.target.value)
                    }
                />

                <TextField
                    label={t("Descrizione Spese imponibili")}
                    variant="outlined"
                    margin="normal"
                    size="small"
                    name="imponibili"
                    value={previousValues.imponibili}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("imponibili", e.target.value)
                    }
                />
                <TextField
                    label={t("Descrizione Spese non imponibili")}
                    variant="outlined"
                    margin="normal"
                    size="small"
                    name="non_imponibili"
                    value={previousValues.non_imponibili}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange("non_imponibili", e.target.value)
                    }
                />
            </FormControl>

            <FormControl>
                <FormLabel component={Typography}>
                    {t("Data in dettaglio linee - fatturazione")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={previousValues.invoiceRowDate === "1"}
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "invoiceRowDate",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="invoiceRowDate"
                            />
                        }
                        label={t(
                            "Mostra la data di Timesheet e Movimenti nel dettaglio quando vengono importati in fattura "
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Suffisso /FE - Fatturazione")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={previousValues.feSuffix === "1"}
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "feSuffix",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="feSuffix"
                            />
                        }
                        label={t(
                            "Includi il suffisso /FE nei documenti fiscali elettronici "
                        )}
                    />
                </FormGroup>
            </FormControl>
            {userHasAccess && (
                <FormControl>
                    <FormLabel component={Typography}>
                        {t("Ordinamento cartella allegati deposito")}
                    </FormLabel>
                    <FormGroup>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={
                                        previousValues.sortAttachmentsInFolder ===
                                        "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) => {
                                        handleSettingsChange(
                                            "sortAttachmentsInFolder",
                                            e.target.checked ? "1" : "0"
                                        );
                                    }}
                                    name="sort_attachments_in_folder"
                                />
                            }
                            label={t(
                                "Ordina gli allegati del deposito per data decrescente nella cartella di appartenenza "
                            )}
                        />
                    </FormGroup>
                </FormControl>
            )}
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Impostazione per i messaggi email della Mailbox")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.seen_email_messages === "1"
                                }
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "seen_email_messages",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="seenEmailMessages"
                            />
                        }
                        label={t(
                            "Lascia il messaggio come non letto sul server "
                        )}
                    />
                </FormGroup>
            </FormControl>
            <FormControl>
                <FormLabel component={Typography}>
                    {t("Impostazione per nascondere i modelli condivisi")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={
                                    previousValues.hideSharedTemplates === "1"
                                }
                                onChange={(e: { target: { checked: any } }) => {
                                    handleSettingsChange(
                                        "hideSharedTemplates",
                                        e.target.checked ? "1" : "0"
                                    );
                                }}
                                name="hideSharedTemplates"
                            />
                        }
                        label={t("Nascondi")}
                    />
                </FormGroup>
            </FormControl>
        </Stack>
    );
};
