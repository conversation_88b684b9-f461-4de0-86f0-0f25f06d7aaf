import { useEffect, useState } from "react";
import {
    Box,
    Button,
    TextField,
    Checkbox,
    FormControlLabel,
    FormGroup,
    MenuItem,
    Select,
    InputLabel,
    FormControl,
} from "@vapor/react-material";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useForm } from "react-hook-form";
import usePostCustom from "../../../hooks/usePostCustom";
import useGetCustom from "../../../hooks/useGetCustom";
import { useNavigate, useParams } from "react-router-dom";
import Image from "../../../custom-components/Image";
import Delete from "../../../custom-components/DeleteModal";
import PageTitle from "../../../custom-components/PageTitle";
import SpinnerButton from "../../../custom-components/SpinnerButton";

const CreateUpdate = () => {
    const [align, setAlign] = useState<string>("left");
    const [data, setData] = useState<any>({});
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [defaultValue, setDefaultValue] = useState<any>({
        full_width: false,
        use_as_logo: false,
        replace_studio_data: false,
    });

    const navigate = useNavigate();
    const { id }: any = useParams<any>();

    const defaultLetterHeadsIndexRequest = useGetCustom(
        `letterheads/update?uniqueid=${id}`
    );

    const letterHeadsSaveRequest = usePostCustom(`letterheads/update`);

    const letterHeadsDeleteRequest = useGetCustom(
        `letterheads/delete?uniqueid=${id}`
    );

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({});

    const validateFileType = (fileList: any) => {
        const file = fileList[0];
        const validTypes = ["image/jpeg", "image/png", "image/gif"];
        return file && validTypes.includes(file.type);
    };

    const getOne = async () => {
        try {
            const response: any = await defaultLetterHeadsIndexRequest.doFetch(
                true
            );
            setData(response?.data && response?.data?.result);
        } catch (error) {
            console.log(error);
        }
    };

    useEffect(() => {
        if (id) {
            getOne();
        }
    }, []);

    useEffect(() => {
        if (data && id) {
            setValue("description", data?.description);
            setAlign(data?.align);
            setDefaultValue({
                full_width: data?.full_width == "0" ? false : true,
                use_as_logo: data?.use_as_logo == "0" ? false : true,
                replace_studio_data:
                    data?.replace_studio_data == "0" ? false : true,
            });
        }
    }, [data]);

    const handleSelectChange = (event: any) => {
        setAlign(event.target.value);
    };

    const handleCheckboxChange = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const { name, checked } = event.target;
        setDefaultValue({ ...defaultValue, [name]: checked });
    };

    const handleDelete = async () => {
        await letterHeadsDeleteRequest.doFetch(true);
        navigate("/letterheads");
    };

    const onSubmit = async (data: any) => {
        const formData = new FormData();

        formData.append("description", data.description);
        formData.append("align", align);

        if (defaultValue?.full_width) {
            formData.append("full_width", "on");
        }

        if (defaultValue?.replace_studio_data) {
            formData.append("replace_studio_data", "on");
        }

        if (defaultValue?.use_as_logo) {
            formData.append("use_as_logo", "on");
        }

        if (id) {
            formData.append("uniqueid", id);
        }

        if (data.filename) {
            formData.append("filename", data.filename[0]);
        }
        setIsLoading(true);
        await letterHeadsSaveRequest.doFetch(true, formData);
        setIsLoading(false);

        navigate("/letterheads");
    };

    return (
        <VaporPage>
            <PageTitle
                title={id ? "Modifica logo " : `Nuovo logo`}
                pathToPrevPage="/letterheads"
            />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        <TextField
                            label="Descrizione"
                            type="text"
                            variant="outlined"
                            {...register("description", {
                                required: "Descrizione obbligatoria",
                            })}
                            error={Boolean(errors.description)}
                            helperText={
                                errors.description &&
                                typeof errors.description.message === "string"
                                    ? errors.description.message
                                    : ""
                            }
                        />
                        <FormGroup>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="full_width"
                                        checked={defaultValue.full_width}
                                        onChange={handleCheckboxChange}
                                    />
                                }
                                label="Espandi in larghezza"
                            />
                        </FormGroup>
                        <FormControl
                            fullWidth
                            disabled={defaultValue?.full_width}
                        >
                            <InputLabel id="demo-simple-select-label">
                                Allineamento
                            </InputLabel>
                            <Select
                                labelId="demo-simple-select-label"
                                id="demo-simple-select"
                                value={align}
                                label="Allineamento"
                                onChange={handleSelectChange}
                            >
                                <MenuItem value={"left"}>Sinistra</MenuItem>
                                <MenuItem value={"center"}>Centro</MenuItem>
                                <MenuItem value={"right"}>Destra</MenuItem>
                            </Select>
                        </FormControl>
                        {id ? (
                            <Box
                                style={{
                                    minHeight: "20px",
                                    height: 350,
                                    width: 350,
                                    padding: "19px",
                                    marginBottom: "20px",
                                    backgroundColor: "#f5f5f5",
                                    border: "1px solid #e3e3e3",
                                    borderRadius: "4px",
                                    boxShadow:
                                        "inset 0 1px 1px rgb(0 0 0 / 5%)",
                                }}
                            >
                                <Image
                                    path={`/letterheads/preview?uniqueid=${id}`}
                                    style={{ width: 300 }}
                                />
                            </Box>
                        ) : (
                            <TextField
                                label="Immagine"
                                type="file"
                                variant="outlined"
                                inputProps={{
                                    ...register("filename", {
                                        required:
                                            "L'immagine selezionata deve essere in formato JPEG, PNG o GIF.",
                                        validate: validateFileType,
                                    }),
                                }}
                                error={Boolean(errors.filename)}
                                helperText={
                                    errors.filename &&
                                    typeof errors.filename.message === "string"
                                        ? errors.filename.message
                                        : ""
                                }
                            />
                        )}
                        <FormGroup>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="use_as_logo"
                                        checked={defaultValue.use_as_logo}
                                        onChange={handleCheckboxChange}
                                    />
                                }
                                label="Utilizza come logo di studio"
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        name="replace_studio_data"
                                        checked={
                                            defaultValue.replace_studio_data
                                        }
                                        onChange={handleCheckboxChange}
                                    />
                                }
                                label="Nascondi dati studio"
                            />
                        </FormGroup>
                    </Box>
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            onClick={() => {
                                navigate("/letterheads");
                            }}
                        >
                            Annulla
                        </Button>
                        <SpinnerButton
                            label="Conferma"
                            variant="contained"
                            isLoading={isLoading}
                            type="submit"
                        />
                        {id && <Delete handleDelete={handleDelete} />}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default CreateUpdate;
