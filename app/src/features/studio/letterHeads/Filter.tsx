import { Box, Button, TextField } from "@vapor/react-material";

// Import components
import Legenda from "./components/Legenda";

interface FilterProps {
    defaultParams: any;
    onChangeInput: (e: any) => void;
    onClickReset: () => void;
    onSubmit: (e: any) => void;
}

const Filter = ({ defaultParams, onChangeInput, onClickReset, onSubmit }: FilterProps) => {
    return (
        <div style={{ display: "flex" }}>
            <Box component="form" display="flex" alignItems="end" gap={2} onSubmit={onSubmit}>
                <TextField
                    label="Descrizione"
                    variant="outlined"
                    value={defaultParams.code}
                    placeholder="Descrizione"
                    name="description"
                    sx={{ width: 1 / 2 }}
                    onChange={onChangeInput}
                />
                <Button
                    variant="contained"
                    color="primary"
                    type='submit'
                >
                    Cerca
                </Button>

                <Button
                    variant="contained"
                    color="primary"
                    onClick={onClickReset}
                >
                    Mostra tutti
                </Button>
            </Box>
            <Box sx={{ marginLeft: "auto", alignContent: "end" }}>
                <Legenda />
            </Box>
        </div>
    )
}
export default Filter;
