import * as React from 'react';
import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import { Button } from '@vapor/react-material';

import MoreVertIcon from '@mui/icons-material/MoreVert';
import ImageIcon from '@mui/icons-material/Image';

import { Box, List, ListItemText } from '@vapor/react-material';
import { Divider } from '@vapor/react-material';

export default function MouseOverPopover() {
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);

    const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handlePopoverClose = () => {
        setAnchorEl(null);
    };

    const open = Boolean(anchorEl);

    return (
        <div>
            <Button
                aria-owns={open ? 'mouse-over-popover' : undefined}
                aria-haspopup="true"
                onMouseEnter={handlePopoverOpen}
                onMouseLeave={handlePopoverClose}
                variant="contained"
            >
                Legenda <MoreVertIcon />
            </Button>
            <Popover
                id="mouse-over-popover"
                sx={{
                    pointerEvents: 'none',
                }}
                open={open}
                anchorEl={anchorEl}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                onClose={handlePopoverClose}
                disableRestoreFocus
            >
                <Box sx={{ borderRadius: 4 }}>
                    <Typography sx={{ p: 1 }}>Legenda calendario</Typography>
                    <Divider />
                    <List disablePadding sx={{
                        width: 260
                    }}>
                        <Box sx={{ display: "flex" }}>
                            <ImageIcon sx={{ mt: 1.1, mr: 0.5, ml: 1 }} />
                            <ListItemText sx={{ p: 1 }} primary="Utilizzato come logo di studio" />
                        </Box>
                    </List>
                </Box>
            </Popover>
        </div>
    );
}