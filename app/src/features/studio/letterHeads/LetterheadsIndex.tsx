import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

import Filter from "./Filter";
import { getLetterHeadsGrid } from "../../../utilities/letterHead/gridColumn";

const LoghiIndex = () => {
    const DEFAULT_LIST_PARAMS = {
        noTemplateVars: true,
        page: 0,
        pageSize: 10,
        sortColumn: "description",
        sortOrder: "asc",
        description: "",
    };
    const { t } = useTranslation();
    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);
    const [columns, setColumns] = useState<any>([]);
    const [totalRows, setTotalRows] = useState(0);
    const [data, setData] = useState([]);

    const navigate = useNavigate();

    const defaultLetterHeadsIndexRequest = useGetCustom(
        "letterheads/list",
        DEFAULT_LIST_PARAMS
    );
    const letterHeadsListRequest = useGetCustom(
        "letterheads/list",
        defaultParams
    );

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultLetterHeadsIndexRequest.doFetch(true)
            : letterHeadsListRequest.doFetch(true));

        const { currentPage, totalRows } = response.data;

        const finalColumns = await getLetterHeadsGrid(t);
        setColumns(finalColumns);

        setData(currentPage);
        setTotalRows(Number(totalRows));
    };

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
    ]);

    const onSubmit = (e: any) => {
        e.preventDefault();
        startSearchList();
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickReset = async () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
    };

    const onClickAction = async (onClickKey: string) => {
        navigate("/letterheads/update/" + onClickKey);
    };

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="letterheads"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                totalRows={totalRows}
                page={defaultParams.page}
                pageSize={defaultParams.pageSize}
                onPageChangeCallback={onPageChangeCallback}
                query={defaultParams}
                onClickCallback={onClickAction}
            />
        );
    };

    const redirectAction = () => {
        navigate("/letterheads/update");
    };

    return (
        <VaporPage>
            <VaporHeaderBar
                rightItems={[
                    <Button
                        variant="contained"
                        onClick={redirectAction}
                        sx={{ mr: 3 }}
                    >
                        Nuova logo
                    </Button>,
                ]}
                title="LOGHI"
            />
            <VaporPage.Section>
                <Filter
                    defaultParams={defaultParams}
                    onChangeInput={onChangeInput}
                    onClickReset={onClickReset}
                    onSubmit={onSubmit}
                />
            </VaporPage.Section>
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
};
export default LoghiIndex;
