import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { Button } from "@vapor/react-material";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import useFilter from "./hooks/useFilter";
import { useNavigate } from "react-router-dom";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

export default function ProfitDistributionIndex() {
    const navigate = useNavigate();

    const { t } = useTranslation();

    const { query, setQuery, list, loading } = useFilter();

    const handleClickCallback = (uniqueid: string) => {
        navigate("/profits-distribution/update/" + uniqueid);
    };
    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="profitsDistribution"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };
    const redirectAction = () => {
        navigate("/profits-distribution/update");
    };
    return (
        <>
            <VaporPage>
                <VaporHeaderBar
                    rightItems={[
                        <Button variant="contained" onClick={redirectAction}>
                            {t("Nuovo basket")}
                        </Button>,
                    ]}
                    title={t("Gestione Utili")}
                />
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
