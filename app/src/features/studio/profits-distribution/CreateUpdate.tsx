import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import { Box, Button, Stack, Typography } from "@vapor/react-material";
import useGetCustom from "../../../hooks/useGetCustom";
import usePostCustom from "../../../hooks/usePostCustom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useNavigate, useParams } from "react-router-dom";
import FormInput from "../../../custom-components/FormInput";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import PageTitle from "../../../custom-components/PageTitle";
import CustomError from "../../../custom-components/CustomError";
import { CustomInfoBox } from "../../../custom-components/CustomInfoBox";

const ProfitDistributionCreateUpdate = () => {
    const { uniqueId } = useParams<any>();

    const { t } = useTranslation();
    const navigate = useNavigate();
    const [userList, setUserList] = useState<[]>([]);
    const [allowDefault, setAllowDefault] = useState<boolean>(false);
    const [rimuovibile, setRimuovibile] = useState<string>("1");

    const updateMountedRequest = useGetCustom(
        uniqueId !== undefined
            ? "profits-distribution/update?uid=" + uniqueId
            : "profits-distribution/update"
    );
    let schema: any = yup.object().shape({
        uniqueid: yup.string(),
        nome: yup.string().required(),
        descrizione: yup.string(),
        default: yup.string(),
        colore: yup.string(),
        percentuale: yup.number().max(100),
        actualBasketPercentage: yup.number(),
        freePercentage: yup.number(),
        users: yup
            .array()
            .of(
                yup.object().shape({
                    percentage: yup.number(),
                })
            )
            .test(
                "sum-of-percentage",
                "La somma delle percentuali deve essere 100",
                (users) => {
                    const sum =
                        users?.reduce((acc, user: any) => {
                            if (user.percentage !== undefined)
                                return acc + user.percentage;
                            else return acc;
                        }, 0) || 0;
                    return sum <= 100;
                }
            ),
    });
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        getValues,
        watch,
    } = useForm({
        resolver: yupResolver(schema),
    });
    const [open, setOpen] = useState(false);
    const [modalType, setModalType] = useState<string>("delete");
    const [confirmText, setConfirmText] = useState<string>("");

    const watche_default = watch("default");
    //api list

    const saveRequest = usePostCustom("profits-distribution/save");

    useEffect(() => {
        async function initData() {
            try {
                const { data }: any = await updateMountedRequest.doFetch();
                setUserList(data.users);
                setAllowDefault(data.allowDefault);
                setValue("colore", "#000000");
                console.log(data);
                setValue("freePercentage", parseFloat(data.freePercentage));
                if (uniqueId !== undefined) {
                    setValue("uniqueid", uniqueId);
                    setRimuovibile(data.result.rimuovibile);
                    setValue("nome", data.result.nome);
                    setValue("descrizione", data.result.descrizione);
                    setValue("colore", data.result.colore);
                    setValue("percentuale", data.result.percentuale);
                    setValue("actualBasketPercentage", data.result.percentuale);
                    setValue(
                        "default",
                        data.result.default === "1" ||
                            data.result.default !== ""
                            ? "on"
                            : ""
                    );
                    let jsonData: string | null = htmlDecode(
                        data.result.default
                    );
                    if (data.result.default !== "1") {
                        let defaultJson = JSON.parse(jsonData ?? "");
                        Object.keys(data.users).map((row: any, key: any) => {
                            setValue(
                                "users[" + key + "][percentage]",
                                defaultJson[data.users[row]["id"]]
                            );
                        });
                    }
                }
            } catch (error) {
                console.log("request error", error);
                return;
            }
        }

        initData();
    }, []);

    const onSubmit = async () => {
        // call
        let data: any = {};
        data.uniqueid = getValues("uniqueid") ?? "";
        data.descrizione = getValues("descrizione") ?? "";
        data.nome = getValues("nome") ?? "";
        data.colore = getValues("colore") ?? "#000000";
        if (getValues("default") == "on") {
            data.default = getValues("default");
        }

        var percentage = getValues("percentuale");
        var actualBasketPercentage = getValues("actualBasketPercentage")
            ? getValues("actualBasketPercentage")
            : 0;
        var freePercentage = getValues("freePercentage")
            ? getValues("freePercentage")
            : 0;
        freePercentage =
            parseFloat(freePercentage) + parseFloat(actualBasketPercentage);
        freePercentage = freePercentage.toFixed(2);

        setValue("freePercentage", freePercentage);

        if (parseFloat(percentage) <= parseFloat(freePercentage)) {
            data.percentuale = getValues("percentuale");
        } else {
            setOpen(true);
            setModalType("percentage");
            setConfirmText(
                t(
                    "Attenzione, la percentuale indicata sugli utili è superiore al massimo disponibile"
                ) +
                    " (" +
                    freePercentage +
                    "%), " +
                    t("Confermando verrà usato il valore") +
                    " " +
                    freePercentage +
                    ". " +
                    t("Si vuole procedere?")
            );

            return false;
        }

        if (getValues("users") !== undefined)
            getValues("users").forEach((user: any, key: number) => {
                if (user.percentage !== undefined) {
                    data["user_" + userList[key]["id"]] = user.percentage;
                }
            });

        await saveRequest.doFetch(true, data);
        navigate("/profits-distribution");
    };

    const deleteRequest = usePostCustom(
        uniqueId !== undefined
            ? "profits-distribution/delete?uniqueid=" + uniqueId
            : "profits-distribution/delete"
    );

    const redirectBack = () => {
        navigate("/profits-distribution");
    };

    const handleDelete = () => {
        setOpen(!open);
        setModalType("delete");
    };

    const handleDecline = () => {
        setOpen(false);
    };

    const handleAgree = async () => {
        setOpen(false);
        await deleteRequest.doFetch(true);
        navigate("/profits-distribution");
    };

    const handlePercetageAgree = () => {
        setValue("percentuale", getValues("freePercentage"));
        setOpen(false);
        onSubmit();
    };

    return (
        <VaporPage>
            <PageTitle
                title={
                    (uniqueId !== undefined ? "Modifica " : `Nuovo `) +
                    t("basket")
                }
                pathToPrevPage={"/profits-distribution"}
            />
            <ConfirmModal
                open={open}
                handleDecline={handleDecline}
                handleAgree={
                    modalType === "delete" ? handleAgree : handlePercetageAgree
                }
                decline={"No"}
                agree={modalType === "delete" ? "Sì, elimina" : "Sì"}
                confirmText={
                    modalType === "delete"
                        ? "Sei sicuro di voler eliminare?"
                        : confirmText
                }
                title={modalType === "delete" ? "Elimina azione" : "Attenzione"}
            />
            <VaporPage.Section>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <Box
                        component="section"
                        sx={{
                            "& .MuiTextField-root": {
                                m: 1,
                                width: 250,
                            },
                        }}
                    >
                        {uniqueId && rimuovibile == "0" && (
                            <CustomInfoBox backgroundColor={"#ADD8E6"}>
                                <Typography
                                    variant="body"
                                    gutterBottom
                                    component="div"
                                >
                                    {t(
                                        "Attenzione, non è possibile rimuovere questo oggetto poiché risulta essere usato in almeno una pratica"
                                    )}
                                </Typography>
                            </CustomInfoBox>
                        )}
                        <div>
                            <Stack direction="column" gap={2}>
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="nome"
                                    label="Titolo"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="descrizione"
                                    label="Descrizione"
                                    type="text"
                                    variant="outlined"
                                    setValue={setValue}
                                />

                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="percentuale"
                                    label="Percentuale sugli utili"
                                    type="number"
                                    variant="outlined"
                                    setValue={setValue}
                                />
                                <FormInput
                                    style={{ width: 400 }}
                                    control={control}
                                    name="colore"
                                    label="Colore"
                                    type="color"
                                    variant="outlined"
                                    setValue={setValue}
                                />
                                {allowDefault && (
                                    <FormInput
                                        style={{
                                            width: 400,
                                            marginLeft: "0.5em",
                                        }}
                                        control={control}
                                        name="default"
                                        label=""
                                        type="checkbox"
                                        variant="outlined"
                                        setValue={setValue}
                                        options={[
                                            {
                                                label: "Basket default",
                                                value: "on",
                                            },
                                        ]}
                                    />
                                )}
                                {watche_default == "on" && userList.length > 0
                                    ? userList.map(
                                          (data: any, index: number) => {
                                              return (
                                                  <FormInput
                                                      key={"users" + index}
                                                      style={{ width: 400 }}
                                                      control={control}
                                                      name={
                                                          "users[" +
                                                          index +
                                                          "][percentage]"
                                                      }
                                                      label={data.nomeutente}
                                                      type="number"
                                                      variant="outlined"
                                                      setValue={setValue}
                                                  />
                                              );
                                          }
                                      )
                                    : watche_default == "on" && (
                                          <CustomInfoBox
                                              backgroundColor={"#ADD8E6"}
                                          >
                                              <Typography
                                                  variant="body"
                                                  gutterBottom
                                                  component="div"
                                              >
                                                  {t(
                                                      "Non sono presenti soci da configurare per il basket di defaults."
                                                  )}
                                              </Typography>
                                          </CustomInfoBox>
                                      )}
                                {errors && (
                                    <div className="css-12l4ysv MuiTextField-root">
                                        <CustomError error={errors.users} />
                                    </div>
                                )}
                            </Stack>
                        </div>
                    </Box>
                    <br />
                    <Box
                        component={"section"}
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 100,
                            },
                        }}
                    >
                        <Button
                            variant="outlined"
                            type="button"
                            onClick={redirectBack}
                        >
                            {t("Annulla")}
                        </Button>
                        <Button variant="contained" type="submit">
                            {t("Conferma")}
                        </Button>

                        {uniqueId && rimuovibile === "1" && (
                            <>
                                <Button
                                    color="error"
                                    variant="outlined"
                                    onClick={handleDelete}
                                    type="button"
                                >
                                    {t("Rimuovi")}
                                </Button>
                            </>
                        )}
                    </Box>
                </form>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default ProfitDistributionCreateUpdate;

const htmlDecode = (input: any) => {
    var e = document.createElement("textarea");
    e.innerHTML = input;
    // handle case of empty input
    if (e.childNodes.length === 0) {
        return "";
    } else {
        return e.childNodes[0].nodeValue;
    }
};
