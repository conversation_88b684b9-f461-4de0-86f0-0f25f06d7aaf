import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { IDefaultQuery } from "../interfaces/simpleList.interface";
import { IList } from "../../../../interfaces/general.interfaces";
import { getProfitDistribuitionGrid } from "../../../../utilities/profitDistribution/gridColumn";
import { useTranslation } from "@1f/react-sdk";

export default function useFilter() {
    const { t } = useTranslation();
    const filterRequest = useGetCustom(
        "profits-distribution/list?noTemplateVars=true"
    );

    const defaultQuery: IDefaultQuery = {
        page: 0,
        pageSize: 10,
        sortColumn: "nome",
        sortOrder: "desc",
    };

    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterData = async (query?: IDefaultQuery) => {
        let cQuery: any = query;
        const [columns, response]: any = await Promise.all([
            getProfitDistribuitionGrid(t),
            filterRequest.doFetch(true, cQuery),
        ]);
        const { currentPage, totalRows } = response.data;
        setList({
            ...list,
            rows: currentPage,
            columns,
            totalRows: parseInt(totalRows),
            page: cQuery?.page,
            pageSize: cQuery?.pageSize,
        });
    };
    useEffect(() => {
        filterData(query);
    }, [query]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterData,
        loading: filterRequest.loading,
    };
}
