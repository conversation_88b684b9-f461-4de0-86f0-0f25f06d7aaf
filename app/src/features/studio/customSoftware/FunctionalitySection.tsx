import { useState } from 'react';
import { Grid, Box, Typography, Divider, Button } from '@vapor/react-material';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';

import ConfirmModal from '../../../custom-components/ConfirmModal';
import usePostCustom from '../../../hooks/usePostCustom';
import ToastNotification from '../../../custom-components/ToastNotification';

const FunctionalitySection = ({ menuItems, setIsRender, goPrev, setGoPrev }: any) => {
    const [openMenuItemsHide8, setOpenMenuItemsHide8] = useState<boolean>(false);
    const [openMenuItemsDisappear9, setOpenMenuItemsDisappear9] = useState<boolean>(false);
    const [openSuccess, setOpenSuccess] = useState<boolean>(false);
    const [openReset, setOpenReset] = useState<boolean>(false);
    const [openResetAlert, setOpenResetAlert] = useState<boolean>(false);

    const updateMenuSection = usePostCustom('customsoftware/saveconfiguration');
    const resetMenuSection = usePostCustom('customsoftware/resetconfiguration');

    const onMenuUpdate = async () => {
        if (menuItems.hide.includes('8')) {
            setOpenMenuItemsHide8(true)
        } else if (menuItems.disappear.includes('8')) {
            setOpenMenuItemsHide8(true)
        } else if (menuItems.disappear.includes('9')) {
            setOpenMenuItemsDisappear9(true)
        } else {
            handleUpdateMenu();
        }
    }

    const onMenuReset = async () => {
        setOpenReset(false);
        setOpenResetAlert(true)
        setIsRender(true);
        await resetMenuSection.doFetch(true, {});
        setIsRender(false);
    }

    const handleUpdateMenu = async () => {
        const { show, disappear, hide } = menuItems;
        const payload = new FormData();

        show.forEach((item: any) => {
            payload.append('show[]', item);
        });

        hide.forEach((item: any) => {
            payload.append('hide[]', item);
        });

        disappear.forEach((item: any) => {
            payload.append('disappear[]', item);
        });

        await updateMenuSection.doFetch(true, payload);
        setOpenSuccess(true)
    }

    return (
        <Box>
            <ToastNotification showNotification={openSuccess} setShowNotification={setOpenSuccess} severity='success' text='Configurazione salvata con successo!' />
            <ToastNotification showNotification={openMenuItemsHide8} setShowNotification={setOpenMenuItemsHide8} severity='warning' text='Lo studio non può essere rimosso' />
            <ToastNotification showNotification={openMenuItemsDisappear9} setShowNotification={setOpenMenuItemsDisappear9} severity='warning' text='Utilità può essere nascosto solo dal menù laterale' />
            <ToastNotification showNotification={openResetAlert} setShowNotification={setOpenResetAlert} severity='success' text='Visualizzazione ripristinata con successo!' />
            <ConfirmModal
                open={openReset}
                handleDecline={() => {
                    setOpenReset(false)
                }}
                handleAgree={
                    onMenuReset
                }
                decline={"Cancel"}
                agree={"Ok"}
                confirmText={"Rispristinare la visualizzazione di default?"}
                title={"Confirm Action"}
            />
            <Typography sx={{ fontSize: 15, pt: 2 }}>Muovi i Box delle Macro-Sezioni da un lista all'altra per nasconderle o visualizzarle.</Typography>
            <Grid container sx={{ pt: 3, pl: 7 }} spacing={3}>
                <Grid item md={2} sm={2}>
                    <Typography sx={{ fontWeight: "bold" }}>Visualizza</Typography>
                </Grid>
                <Grid item md={3} sm={3}>
                    <ArrowRightAltIcon />
                </Grid>
                <Grid item md={7} sm={7}>
                    <Typography sx={{ fontSize: 15 }}>Tutte le sezioni che si desidera visualizzare, nell'ordine selezionato</Typography>
                </Grid>
                <Grid item md={2} sm={2}>
                    <Typography sx={{ fontWeight: "bold" }}>Nascondi</Typography>
                </Grid>
                <Grid item md={3} sm={3}>
                    <ArrowRightAltIcon />
                </Grid>
                <Grid item md={7} sm={7}>
                    <Typography sx={{ fontSize: 15 }}>Nascondendo una sezione, si ridurrà la sua presenza anche nelle sottosezioni</Typography>
                </Grid>
                <Grid item md={2} sm={2}>
                    <Typography sx={{ fontWeight: "bold" }}>Soft Delete</Typography>
                </Grid>
                <Grid item md={3} sm={3}>
                    <ArrowRightAltIcon />
                </Grid>
                <Grid item md={7} sm={7}>
                    <Typography sx={{ fontSize: 15 }}>Le sezioni verranno nascoste soltanto dal menù laterale</Typography>
                </Grid>
            </Grid>
            <Divider sx={{ mt: 6 }} />
            <Box sx={{
                display: "flex",
                justifyContent: "right",
            }}>
                <Button variant="outlined" sx={{ mt: 3, ml: 1 }} onClick={() => setGoPrev(!goPrev)}>Annulla</Button>
                <Button variant="contained" disabled={menuItems && menuItems.show && menuItems.show.length === 13 && menuItems.hide && menuItems.hide.length === 0 && menuItems.disappear && menuItems.disappear.length === 0} onClick={() => setOpenReset(true)
                } sx={{ mt: 3, ml: 1 }}>Ripristina</Button>
                <Button variant="contained" onClick={
                    onMenuUpdate
                } sx={{ mt: 3, ml: 1 }}>Conferma</Button>
            </Box>
        </Box >
    );
}
export default FunctionalitySection;