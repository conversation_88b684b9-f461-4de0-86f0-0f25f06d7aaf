import { useEffect, useState } from "react";
import {
    Dnd<PERSON>ontext,
    DragOverlay,
    closestCorners,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
    DragStartEvent,
} from "@dnd-kit/core";
import { arrayMove, sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import useGetCustom from "../../../../hooks/useGetCustom";

import Container from "./Container";
import { Item } from "./SortableItem";
import { Grid } from "@vapor/react-material";

const DragAndDrop = ({ setMenuItems, isRender, goPrev }: any) => {
    const [items, setItems] = useState<any>({
        show: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"],
        hide: [],
        disappear: [],
    });
    const [activeId, setActiveId] = useState<string | null>(null);

    const getMenuSection = useGetCustom("customsoftware/update", {});

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragStart = (event: DragStartEvent) => {
        const { active } = event;
        const { id } = active;
        setActiveId(id as any);
    };

    const handleDragOver = (event: any) => {
        const { active, over } = event;
        const { id } = active!;
        const overId = over?.id;

        let draggingRect = over;

        if (!draggingRect) return;

        if (!over || !over.rect) return;

        const findContainer = (itemId: string) => {
            if (itemId in items) {
                return itemId;
            }

            return Object.keys(items).find((key) =>
                items[key].includes(itemId)
            );
        };

        if (!overId) return;

        const activeContainer = findContainer(id as any);
        const overContainer = findContainer(overId as any);

        if (
            !activeContainer ||
            !overContainer ||
            activeContainer === overContainer
        ) {
            return;
        }

        setItems((prev: any) => {
            const activeItems = prev[activeContainer!];
            const overItems = prev[overContainer!];

            const activeIndex = activeItems.indexOf(id as any);
            const overIndex = overItems.indexOf(overId as any);

            let newIndex;
            if (overId in prev) {
                newIndex = overItems.length + 1;
            } else {
                const isBelowLastItem =
                    overIndex === overItems.length - 1 &&
                    over.rect.offsetTop >
                        over.rect.offsetTop + over.rect.height;

                const modifier = isBelowLastItem ? 1 : 0;

                newIndex =
                    overIndex >= 0
                        ? overIndex + modifier
                        : overItems.length + 1;
            }

            return {
                ...prev,
                [activeContainer!]: [
                    ...prev[activeContainer!].filter(
                        (item: any) => item !== id
                    ),
                ],
                [overContainer!]: [
                    ...prev[overContainer!].slice(0, newIndex),
                    items[activeContainer!][activeIndex],
                    ...prev[overContainer!].slice(
                        newIndex,
                        prev[overContainer!].length
                    ),
                ],
            };
        });
    };

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        const { id } = active!;
        const overId = over?.id;

        const findContainer = (itemId: string) => {
            if (itemId in items) {
                return itemId;
            }

            return Object.keys(items).find((key) =>
                items[key].includes(itemId)
            );
        };

        const activeContainer = findContainer(id as any);
        const overContainer = findContainer(overId! as any);

        if (
            !activeContainer ||
            !overContainer ||
            activeContainer !== overContainer
        ) {
            return;
        }

        const activeIndex = items[activeContainer!].indexOf(id as any);
        const overIndex = items[overContainer!].indexOf(overId! as any);

        if (activeIndex !== overIndex) {
            setItems((prevItems: any) => ({
                ...prevItems,
                [overContainer!]: arrayMove(
                    prevItems[overContainer!],
                    activeIndex,
                    overIndex
                ),
            }));
        }
        setActiveId(null);
    };

    const fetchData = async () => {
        const { data }: any = await getMenuSection.doFetch();
        setItems({
            show: data.show || [],
            hide: data.hide || [],
            disappear: data.disappear || [],
        });
    };

    useEffect(() => {
        fetchData();
    }, [goPrev]);

    useEffect(() => {
        setMenuItems(items);
    }, [items]);

    useEffect(() => {
        setItems({
            show: [
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "10",
                "11",
                "12",
            ],
            hide: [],
            disappear: [],
        });
    }, [isRender]);

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "row",
            }}
        >
            <DndContext
                // announcements={defaultAnnouncements}
                sensors={sensors}
                collisionDetection={closestCorners}
                onDragStart={handleDragStart}
                onDragOver={handleDragOver}
                onDragEnd={handleDragEnd}
            >
                <Grid container>
                    <Grid item md={4}>
                        <Container
                            id="show"
                            title="Visualizza"
                            color="#478847"
                            items={items.show}
                        />
                    </Grid>
                    <Grid item md={4}>
                        <Container
                            id="hide"
                            title="Nascondi"
                            color="#B94A48"
                            items={items.hide}
                        />
                    </Grid>
                    <Grid item md={4}>
                        <Container
                            id="disappear"
                            title="Soft Delete"
                            color="#F89407"
                            items={items.disappear}
                        />
                    </Grid>
                </Grid>
                {/* <Container id="container3" items={items.container3} /> */}
                <DragOverlay>
                    {activeId ? <Item id={activeId} /> : null}
                </DragOverlay>
            </DndContext>
        </div>
    );
};
export default DragAndDrop;
