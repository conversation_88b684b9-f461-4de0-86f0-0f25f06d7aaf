import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent, Box } from "@vapor/react-material";
import { Typography } from "@vapor/react-material";

import {
    FactCheck,
    Home,
    Groups,
    Article,
    Euro,
    Email,
    Newspaper,
    AccountBalance,
    LibraryBooks,
    AddShoppingCart,
    PeopleAlt,
    Build,
} from "@mui/icons-material";

export function Item(props: any) {
    const { id } = props;

    let value;
    let icon;

    switch (id) {
        case "1":
            value = "Dashboard";
            icon = <Home />;
            break;
        case "2":
            value = "Anagrafiche";
            icon = <Groups />;
            break;
        case "3":
            value = "Pratiche";
            icon = <LibraryBooks />;
            break;
        case "4":
            value = "Agenda";
            icon = <FactCheck />;
            break;
        case "5":
            value = "Fatturazione";
            icon = <Euro />;
            break;
        case "6":
            value = "MailBox";
            icon = <Email />;
            break;
        case "7":
            value = "PCT";
            icon = <AccountBalance />;
            break;
        case "8":
            value = "Studio";
            icon = <PeopleAlt />;
            break;
        case "9":
            value = "Utilità";
            icon = <Build />;
            break;
        case "10":
            value = "Shop";
            icon = <AddShoppingCart />;
            break;
        case "11":
            value = "News";
            icon = <Newspaper />;
            break;
        case "12":
            value = "Documenti";
            icon = <Article />;
            break;
        case "13":
            value = "Contratti";
            icon = <Home />;
            break;
        default:
            value = "";
            break;
    }

    return (
        <>
            <Card
                sx={{
                    // maxWidth: 300,
                    margin: 1,
                    width: "95%",
                    height: 70,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <CardContent>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            mt: 1.5,
                        }}
                    >
                        {icon}
                    </Box>
                    <Typography sx={{ color: "#04ABDD", pt: 0.7 }}>
                        {" "}
                        {value}
                    </Typography>
                </CardContent>
            </Card>
        </>
    );
}

export default function SortableItem(props: any) {
    const { attributes, listeners, setNodeRef, transform, transition } =
        useSortable({ id: props.id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <>
            <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
                <Item id={props.id} />
            </div>
        </>
    );
}
