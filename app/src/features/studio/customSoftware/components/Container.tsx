import { useDroppable } from "@dnd-kit/core";
import {
    SortableContext,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Box } from "@vapor/react-material";

import SortableItem from "./SortableItem";

const containerStyle = {
    background: "#f0eded",
    padding: 10,
    margin: 6,
    flex: 1,
};

export default function Container(props: any) {
    const { id, items, title, color } = props;

    const { setNodeRef } = useDroppable({
        id,
    });

    return (
        <SortableContext
            id={id}
            items={items}
            strategy={verticalListSortingStrategy}
        >
            <Box ref={setNodeRef} style={containerStyle}>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        border: 1,
                        p: 3,
                        backgroundColor: `${color}`,
                        borderRadius: "5px",
                        color: "white",
                        fontSize: 22,
                    }}
                >
                    {title}
                </Box>
                {items.map((id: any) => (
                    <SortableItem key={id} id={id} />
                ))}
            </Box>
        </SortableContext>
    );
}
