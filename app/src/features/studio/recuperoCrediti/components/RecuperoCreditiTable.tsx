import {
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  InputAdornment,
} from "@vapor/react-material";
import { useTranslation } from "react-i18next";
import FormInput from "../../../../custom-components/FormInput";

export const RecuperoCreditiTable = (props: any) => {
  const { name, data, control } = props;
  const { t } = useTranslation();

  return (
    <TableContainer component={Paper} sx={{ mt: 2, width: 1 / 3 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell style={{ textAlign: "center" }}>{t("Da")}</TableCell>
            <TableCell style={{ textAlign: "center" }}>{t("A")}</TableCell>
            <TableCell style={{ textAlign: "center" }}>
              {t("Importo")}
            </TableCell>
            <TableCell style={{ textAlign: "center" }}>{t("Azioni")}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.fields.map((field: any, index: number) => {
            return (
              <TableRow key={field.id}>
                <TableCell>
                  <FormInput
                    control={control}
                    name={`${name}.${index}.from`}
                    sx={{
                      m: 1,
                      width: "12ch",
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">%</InputAdornment>
                      ),
                    }}
                  />
                </TableCell>
                <TableCell>
                  <FormInput
                    control={control}
                    name={`${name}.${index}.to`}
                    sx={{
                      m: 1,
                      width: "12ch",
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">%</InputAdornment>
                      ),
                    }}
                  />
                </TableCell>
                <TableCell>
                  <FormInput
                    control={control}
                    name={`${name}.${index}.amount`}
                    sx={{
                      m: 1,
                      width: "12ch",
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">%</InputAdornment>
                      ),
                    }}
                  />
                </TableCell>
                <TableCell>
                  {index === 0 ? (
                    <Button
                      variant="outlined"
                      onClick={() => {
                        data.update(0, {
                          from: "",
                          to: "",
                          amount: "",
                        });
                      }}
                    >
                      {t("Reset")}
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={() => {
                        data.remove(index);
                      }}
                    >
                      {t("Rimuovi")}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
