import VaporPage from "@vapor/react-custom/VaporPage";
import ModelPredefiniti from "./sections/ModelPredefiniti";
import CostoAggiuntivi from "./sections/CostiAggiuntivi";
import useGetCustom from "../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import PageTitle from "../../../custom-components/PageTitle";
import React from "react";
import { useTranslation } from "react-i18next";
import { Box, Typography } from "@vapor/react-material";
import { Tabs, Tab } from "@vapor/react-extended";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

const CustomTabPanel = (props: TabPanelProps) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    <Typography>{children}</Typography>
                </Box>
            )}
        </div>
    );
};

const a11yProps = (index: number) => {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
};
const RecuperoCreditiIndex = () => {
    const [value, setValue] = React.useState(0);
    const [warningsModels, setWarningModels] = useState<any[]>([]);
    const [creditRecoverySettings, setCreditRecoverySettings] = useState<any[]>(
        []
    );
    const { t } = useTranslation();
    const handleChange = (_event: any, newValue: number) => {
        setValue(newValue);
    };

    const archivecreditrecoveryRequest = useGetCustom(
        "archivecreditrecovery/settings"
    );

    useEffect(() => {
        async function initRecuperoCrediti() {
            try {
                const response: any =
                    await archivecreditrecoveryRequest.doFetch();
                setWarningModels(response.data.warningsModels);
                setCreditRecoverySettings(response.data.creditrecoverySettings);
            } catch (error) {
                console.log("RecuperoCrediti Request error", error);
                return;
            }
        }

        initRecuperoCrediti();
    }, []);

    return (
        <VaporPage>
            <PageTitle
                title={t("IMPOSTAZIONI RECUPERO CREDITI")}
                showBackButton={false}
            />

            <VaporPage.Section>
                <Box sx={{ width: "100%", typography: "body1" }}>
                    <Tabs
                        value={value}
                        onChange={handleChange}
                        size="extraSmall"
                        variant="standard"
                    >
                        <Tab
                            label={t("Modelli predefiniti")}
                            {...a11yProps(0)}
                        />
                        <Tab label={t("Costi Aggiuntivi")} {...a11yProps(1)} />
                    </Tabs>
                    {value === 0 && (
                        <CustomTabPanel value={value} index={0}>
                            <ModelPredefiniti
                                warningsModels={warningsModels}
                                creditRecoverySettings={creditRecoverySettings}
                            />
                        </CustomTabPanel>
                    )}
                    {value === 1 && (
                        <CustomTabPanel value={value} index={1}>
                            <CostoAggiuntivi
                                creditRecoverySettings={creditRecoverySettings}
                            />
                        </CustomTabPanel>
                    )}
                </Box>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default RecuperoCreditiIndex;
