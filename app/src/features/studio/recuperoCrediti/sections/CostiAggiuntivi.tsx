import React from "react";
import { <PERSON>, <PERSON>po<PERSON>, Button, InputAdornment } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import FormInput from "../../../../custom-components/FormInput";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useForm, useFieldArray } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { RecuperoCreditiTable } from "../components/RecuperoCreditiTable";
import CustomError from "../../../../custom-components/CustomError";
import { arrayValidation, numberValidation } from "../utils/validation";

const FormFields = [
  "uniqueid",
  "warnings_charges",
  "warnings_forfait",
  "warnings_interests",
  "warnings_range",
  "beware_range",
  "beware_charges",
  "beware_forfait",
  "beware_interests",
];

type FormKeys =
  | "uniqueid"
  | "warnings_charges"
  | "warnings_forfait"
  | "warnings_interests"
  | "warnings_range"
  | "beware_range"
  | "beware_charges"
  | "beware_forfait"
  | "beware_interests";

const warningInputFields = [
  { name: "warnings_charges", label: "Costi Fissi", adorment: "€" },
  { name: "warnings_forfait", label: "Penale", adorment: "%" },
  { name: "warnings_interests", label: "Interessi", adorment: "%" },
];

const bewareInputFields = [
  { name: "beware_charges", label: "Costi Fissi", adorment: "€" },
  { name: "beware_forfait", label: "Penale", adorment: "%" },
  { name: "beware_interests", label: "Interessi", adorment: "%" },
];

const CostoAggiuntivi = (props: any) => {
  const { creditRecoverySettings } = props;
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const schema = yup.object().shape({
    uniqueid: yup.string(),
    warnings_charges: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1.00)")
    ),
    warnings_forfait: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1)")
    ),
    warnings_interests: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1)")
    ),
    warnings_range: arrayValidation(t),
    beware_range: arrayValidation(t),
    beware_charges: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1.00)")
    ),
    beware_forfait: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1)")
    ),
    beware_interests: numberValidation(
      t("Inserire un importo nel formato corretto (es. 1)")
    ),
  });

  const usePostRequest = usePostCustom(
    `archivecreditrecovery/save-warnings-data`
  );

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      uniqueid: "",
      warnings_charges: undefined,
      warnings_forfait: "",
      warnings_interests: "",
      warnings_range: [],
      beware_range: [],
      beware_charges: "",
      beware_forfait: "",
      beware_interests: "",
    },
  });

  const warnings_range = useFieldArray({
    control,
    name: "warnings_range",
  });

  const beware_range = useFieldArray({
    control,
    name: "beware_range",
  });

  React.useEffect(() => {
    Object.keys(creditRecoverySettings).map((key: string) => {
      if (FormFields.includes(key)) {
        if (["warnings_range", "beware_range"].includes(key)) {
          setValue(key as FormKeys, JSON.parse(creditRecoverySettings[key]));
        } else {
          setValue(key as FormKeys, creditRecoverySettings[key]);
        }
      }
    });
  }, [creditRecoverySettings]);

  const onSubmit = async (values: any) => {
    const { beware_range, warnings_range, ...rest } = values;

    const formData = new FormData();
    Object.keys(rest).forEach((key) => {
      formData.append(key, rest[key]);
    });

    warnings_range.forEach((range: any) => {
      formData.append("warningsFrom[]", range.from);
      formData.append("warningsTo[]", range.to);
      formData.append("warningsAmount[]", range.amount);
    });

    beware_range.forEach((range: any) => {
      formData.append("bewareFrom[]", range.from);
      formData.append("bewareTo[]", range.to);
      formData.append("bewareAmount[]", range.amount);
    });
    const response: any = await usePostRequest.doFetch(true, formData);
    if (response.status === 200) {
      setShow(true);
    }
  };

  return (
    <Box>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Typography variant="h6">
          {t("GESTIONE INTERESSE E COSTI AGGIUNTIVI")}
        </Typography>
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1">
            {t("Messa in mora e Solleciti")}
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              mt: 2,
            }}
          >
            {warningInputFields.map(({ name, label, adorment }: any) => {
              return (
                <FormInput
                  key={name}
                  label={label}
                  control={control}
                  name={name}
                  sx={{
                    m: 1,
                    width: "18ch",
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {adorment}
                      </InputAdornment>
                    ),
                  }}
                />
              );
            })}
          </Box>
          <Box
            display="flex"
            alignItems="end"
            gap={1}
            sx={{ paddingBottom: "1rem" }}
          >
            <Typography variant="subtitle1" sx={{ mt: 3 }}>
              {t("Costi aggiuntivi per soglia")}
            </Typography>
            <Button
              variant="contained"
              size="small"
              sx={{ mt: 2 }}
              onClick={() => {
                warnings_range.append(warnings_range.fields[0]);
              }}
            >
              {t("Aggiungi Soglia")}
            </Button>
          </Box>

          {!!errors.warnings_range && (
            <CustomError error={errors.warnings_range.root} />
          )}
          <RecuperoCreditiTable
            name="warnings_range"
            data={warnings_range}
            control={control}
          />
        </Box>
        <Box sx={{ mt: 5 }}>
          <Typography variant="subtitle1">{t("Diffida")}</Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              mt: 2,
            }}
          >
            {bewareInputFields.map(({ name, label, adorment }: any) => {
              return (
                <FormInput
                  key={name}
                  label={label}
                  control={control}
                  name={name}
                  sx={{
                    m: 1,
                    width: "18ch",
                  }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        {adorment}
                      </InputAdornment>
                    ),
                  }}
                />
              );
            })}
          </Box>
          <Box
            display="flex"
            alignItems="end"
            gap={1}
            sx={{ paddingBottom: "1rem" }}
          >
            <Typography variant="subtitle1" sx={{ mt: 3 }}>
              {t("Costi aggiuntivi per soglia")}
            </Typography>
            <Button
              variant="contained"
              size="small"
              sx={{ mt: 3 }}
              onClick={() => {
                beware_range.append(beware_range.fields[0]);
              }}
            >
              {t("Aggiungi Soglia")}
            </Button>
          </Box>

          {!!errors.beware_range && (
            <CustomError error={errors.beware_range.root} />
          )}
          <RecuperoCreditiTable
            name="beware_range"
            data={beware_range}
            control={control}
          />
        </Box>
        <Button type="submit" variant="contained" sx={{ mt: 3 }}>
          {t("Salva")}
        </Button>
      </form>
      <ToastNotification
        showNotification={show}
        setShowNotification={setShow}
        severity="success"
        text={t("Recupero Crediti state salvate correttamente!")}
      />
    </Box>
  );
};

export default CostoAggiuntivi;
