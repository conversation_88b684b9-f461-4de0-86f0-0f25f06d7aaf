import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    Button,
    FormGroup,
    InputAdornment,
} from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useTranslation } from "react-i18next";
import usePostCustom from "../../../../hooks/usePostCustom";

const FormFields = [
    "uniqueid",
    "id_mora_model",
    "id_second_model",
    "id_third_model",
    "id_beware_model",
    "mora_expire",
    "second_expire",
    "third_expire",
    "beware_expire",
];

type FormKeys =
    | "uniqueid"
    | "id_mora_model"
    | "id_second_model"
    | "id_third_model"
    | "id_beware_model"
    | "mora_expire"
    | "second_expire"
    | "third_expire"
    | "beware_expire";

const ModelPredefiniti = (props: any) => {
    const { warningsModels, creditRecoverySettings } = props;
    const [show, setShow] = useState(false);
    const { t } = useTranslation();
    const schema = yup.object().shape({
        uniqueid: yup.string(),
        id_mora_model: yup.string(),
        id_second_model: yup.string(),
        id_third_model: yup.string(),
        id_beware_model: yup.string(),
        mora_expire: yup.string(),
        second_expire: yup.string(),
        third_expire: yup.string(),
        beware_expire: yup.string(),
    });

    const usePostRequest = usePostCustom(
        `archivecreditrecovery/save-warnings-models?noTemplateVars=true`
    );

    const { control, handleSubmit, setValue } = useForm({
        resolver: yupResolver(schema),
        defaultValues: {
            uniqueid: "",
            id_mora_model: "",
            id_second_model: "",
            id_third_model: "",
            id_beware_model: "",
            mora_expire: "",
            second_expire: "",
            third_expire: "",
            beware_expire: "",
        },
    });

    React.useEffect(() => {
        Object.keys(creditRecoverySettings).map((key: string) => {
            if (FormFields.includes(key)) {
                setValue(key as FormKeys, creditRecoverySettings[key]);
            }
        });
    }, [creditRecoverySettings]);

    const onSubmit = async (values: any) => {
        const formData = new FormData();
        Object.keys(values).forEach((key) => {
            formData.append(key, values[key]);
        });
        const response: any = await usePostRequest.doFetch(true, formData);
        if (response.status === 200) {
            setShow(true);
        }
    };
    return (
        <Box
            component={"section"}
            sx={{
                "& > :not(style)": {
                    m: 1,
                    width: 800,
                },
            }}
        >
            <form onSubmit={handleSubmit(onSubmit)}>
                <Typography variant="h6">
                    {t(
                        "SELEZIONA UN MODELLO DI DEFAULT PER OGNI COMUNICAZIONE"
                    )}
                </Typography>
                <Box
                    sx={{ display: "flex", alignItems: "center", mt: 2 }}
                    gap={4}
                >
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Messa in mora")}
                            type="select"
                            name="id_mora_model"
                            options={warningsModels.map((item: any) => ({
                                value: item.id,
                                label: item.title,
                            }))}
                            sx={{
                                width: 1,
                                minWidth: "350px",
                                maxWidth: "350px",
                            }}
                        />
                    </FormGroup>

                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Scadenza")}
                            type="text"
                            name="mora_expire"
                            sx={{ width: 1 }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment
                                        position="start"
                                        variant="standard"
                                        sx={{ color: "red" }}
                                    >
                                        {t("GG")}
                                    </InputAdornment>
                                ),
                            }}
                        />
                    </FormGroup>
                </Box>
                <Box
                    sx={{ display: "flex", alignItems: "center", mt: 2 }}
                    gap={4}
                >
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Modello 2° Sollecito")}
                            type="select"
                            name="id_second_model"
                            options={warningsModels.map((item: any) => ({
                                value: item.id,
                                label: item.title,
                            }))}
                            sx={{
                                width: 1,
                                minWidth: "350px",
                                maxWidth: "350px",
                            }}
                        />
                    </FormGroup>
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Scadenza")}
                            type="text"
                            name="second_expire"
                            sx={{ width: 1 }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        {t("GG")}
                                    </InputAdornment>
                                ),
                            }}
                        />
                    </FormGroup>
                </Box>
                <Box
                    sx={{ display: "flex", alignItems: "center", mt: 2 }}
                    gap={4}
                >
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Modello 3° Sollecito")}
                            type="select"
                            name="id_third_model"
                            options={warningsModels.map((item: any) => ({
                                value: item.id,
                                label: item.title,
                            }))}
                            sx={{
                                width: 1,
                                minWidth: "350px",
                                maxWidth: "350px",
                            }}
                        />
                    </FormGroup>
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Scadenza")}
                            type="text"
                            name="third_expire"
                            sx={{ width: 1 }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        {t("GG")}
                                    </InputAdornment>
                                ),
                            }}
                        />
                    </FormGroup>
                </Box>
                <Box
                    sx={{ display: "flex", alignItems: "center", mt: 2 }}
                    gap={4}
                >
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Modello per Diffida")}
                            type="select"
                            name="id_beware_model"
                            options={warningsModels.map((item: any) => ({
                                value: item.id,
                                label: item.title,
                            }))}
                            sx={{
                                width: 1,
                                minWidth: "350px",
                                maxWidth: "350px",
                            }}
                        />
                    </FormGroup>
                    <FormGroup>
                        <FormInput
                            control={control}
                            label={t("Scadenza")}
                            type="text"
                            name="beware_expire"
                            sx={{ width: 1 }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        {t("GG")}
                                    </InputAdornment>
                                ),
                            }}
                        />
                    </FormGroup>
                </Box>
                <Button type="submit" variant="contained" sx={{ mt: 3 }}>
                    {t("Salva")}
                </Button>
            </form>
            <ToastNotification
                showNotification={show}
                setShowNotification={setShow}
                severity="success"
                text={t("Recupero Crediti state salvate correttamente! ")}
            />
        </Box>
    );
};

export default ModelPredefiniti;
