import * as yup from "yup";

export const numberValidation = (message: string) =>
  yup.mixed().test("is-number", message, function (value: any) {
    if (!value) {
      return true;
    }
    return !isNaN(value);
  });

export const arrayValidation = (t: any) =>
  yup
    .array()
    .of(
      yup.object().shape({
        from: numberValidation(
          t("Inserire un importo nel formato corretto (es. 1.00)")
        ),
        to: numberValidation(
          t("Inserire un importo nel formato corretto (es. 1.00)")
        ),
        amount: numberValidation(
          t("Inserire un importo nel formato corretto (es. 1.00)")
        ),
      })
    )
    .test("is-complete", t("Riempi tutti i campi"), function (value) {
      if (!value || value.length === 0) {
        return true;
      }
      return value.every(
        (obj) =>
          obj.from && obj.to && obj.amount && obj !== null && obj !== undefined
      );
    });
