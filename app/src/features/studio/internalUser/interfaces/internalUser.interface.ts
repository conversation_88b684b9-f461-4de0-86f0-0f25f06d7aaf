export interface IDefaultQuery {
  page: number;
  pageSize: number;
  sortColumn: string;
  sortOrder: string;
  searchName?: string;
  searchEmail?: string;
  searchQualification?: string;
  searchGroup: string;
  searchLawyer: string;
  searchActive?: string;
}

export interface IFilterProps {
  defaultQuery: IDefaultQuery;
  query: IDefaultQuery;
  internalUserData: any;
  setQuery: React.Dispatch<React.SetStateAction<IDefaultQuery>>;
  filterInternalUserData: (query: IDefaultQuery) => void;
}