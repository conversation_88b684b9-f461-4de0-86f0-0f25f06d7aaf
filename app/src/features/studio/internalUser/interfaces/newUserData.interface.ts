export interface INewUserData {
    uniqueid: string;
    nomeutente: string;
    nomepersonale: string;
    cognomepersonale: string;
    sigla: string;
    natoil: string;
    natoa: string;
    Email: string;
    nome: string;
    password: string;
    passwordConfirm: string;
    qualificautente: string;
    ordine: string;
    codiceavvocato: string;
    attivo: string;
    consultant: string;
    studioUniqueid: string;
    userImport: string;
    studioNome: string;
    studioCodicefiscale: string;
    studioPartitaiva: string;
    studioIndirizzo: string;
    studioCap: string;
    studioCitta: string;
    studioProvincia: string;
    studioTelefono: string;
    studioFax: string;
    studioMobile: string;
    studioEmail: string;
    studioWeb: string;
    studioPolizza: string;
    identificatore: string;
    pec_address_for_pa_invoice: string;
    pa_tipo_ritenuta: string;
    pa_id_codice: string;
    pa_id_codice_da: string;
    pa_codice_fiscale_da: string;
    nascondi_dati_pagamento_xml: string;
    externalcode: string;
    external_can_upload: string;
    studioNascondiCFfatturazione: string;
    sendCredentials: boolean;
    costo_risorsa: string;
    max_hourly_rate: string;
    min_hourly_rate: string;
    dailyWorkload: string;
    enable_timesheet: string;
    default_page_size: string;
    voispeed_ext: string;
    default_calendar: string;
    extended_days_before: string;
    extended_days_before_commitments: string;
    is_assignee_pa: string;
    vede_soci: boolean;
    socio: boolean;
    notify_new_proceed: string;
    gestisce_riserva: boolean;
    previewDeadlineValues: [];
    previewDeadlineArchiveValues: [];
    previewHearingsValues: [];
    previewPoliswebValues: [];
    previewnomeutenteValues: [];
    semplice_intestatari?: string;
    semplice_annotazioni?: string;
    semplice_oggetto?: string;
    semplice_durata?: string;
    pratica_rg?: string;
    pratica_rgnr?: string;
    pratica_codice?: string;
    pratica_stato?: string;
    pratica_clienti?: string;
    pratica_controparti?: string;
    pratica_avvocato?: string;
    pratica_referente?: string;
    pratica_ultimaUdienza?: string;
    pratica_sezione?: string;
    pratica_autorita?: string;
    pratica_citta?: string;
    pratica_pm?: string;
    pratica_istruttore?: string;
    pratica_intestatari?: string;
    pratica_oggetto?: string;
    pratica_durata?: string;
    pratica_descrizione?: string;
    udienza_rg?: string;
    udienza_rgnr?: string;
    udienza_codice?: string;
    udienza_descrizione?: string;
    udienza_stato?: string;
    udienza_clienti?: string;
    udienza_controparti?: string;
    udienza_avvocato?: string;
    udienza_referente?: string;
    udienza_sezione?: string;
    udienza_autorita?: string;
    udienza_citta?: string;
    udienza_pm?: string;
    udienza_istruttore?: string;
    udienza_oggetto?: string;
    udienza_durata?: string;
    polisweb_rg?: string;
    polisweb_rgnr?: string;
    polisweb_codice?: string;
    polisweb_clienti?: string;
    polisweb_controparti?: string;
    polisweb_avvocato?: string;
    polisweb_referente?: string;
    polisweb_sezione?: string;
    polisweb_autorita?: string;
    polisweb_citta?: string;
    polisweb_pubblicoministero?: string;
    polisweb_giudice?: string;
    polisweb_oggetto?: string;
    polisweb_descrizione?: string;
    altroDefaultStudio: string;
    altroIban: string;
    altroDesc: string;
    codice_destinatario_af: string;
    altroAbi: string;
    altroCab: string;
    altroBic: string;
    altroRegimeFiscale: string;
    altroTipoCassa: string;
    bollo: string;
    altroPercCassa: string;
    addCassa: boolean;
    altroTipoCassa2: string;
    altroPercCassa2: string;
    tipologia: string;
    tipologiaFE: string;
    usernamecct: string;
    passwordcct: string;
    one_drive_email: string;
    role: string;
    letterhead_id: string;
    seniority: string;
    api_personal_token: string;
    bankAccountDetails?: any[];
    campi_agenda: any;
    studioRiservaFatturazione?: boolean;
}

interface BaseInternalUserData {
    t: any;
    newUserParamsData: INewUserData;
    setNewUserParamsData: React.Dispatch<React.SetStateAction<INewUserData>>;
}

export interface IBillingProps extends BaseInternalUserData {
    newUserData: any;
    register?: any;
    errors?: any;
}

export interface IAgendaProps extends BaseInternalUserData {
    newUserData: any;
    register?: any;
    errors?: any;
}

export interface IStudioData extends BaseInternalUserData {
    newUserData: any;
    register: any;
    errors: any;
    errorMessage?: any;
}

export interface IOtherProps extends BaseInternalUserData {}
export interface ICCTProps extends BaseInternalUserData {}

export interface IReservesProps {
    list: any;
    loading: boolean;
    queryParams: any;
    setQueryParams: React.Dispatch<React.SetStateAction<any>>;
}
