import {
  Box,
  Button,
  TextField,
  Checkbox,
  FormControlLabel,
  FormControl,
  Select,
  MenuItem,
} from "@vapor/react-material";
import { IFilterProps } from "./interfaces/internalUser.interface";


export default function Filters(props: IFilterProps) {
  const {
    defaultQuery,
    query,
    setQuery,
    internalUserData,
    filterInternalUserData,
  } = props;
  const { searchQualifications, searchLawyers, groups } = internalUserData;

  function onChangeFilterInputs(event: any) {
    const { name, value } = event.target;
    setQuery({ ...query, [name]: value });
  }

  function handleSearchActiveChange(event: any) {
    const { checked } = event.target;
    setQuery((prevQuery) => {
      const { searchActive, ...rest } = prevQuery;
      return checked ? { ...rest, searchActive: "on" } : rest;
    });
  }

  return (
    <Box component="form" display="flex" alignItems="end" gap={2}>
      <TextField
        sx={{ width: 250 }}
        id="searchName"
        name="searchName"
        label="Nome"
        value={query.searchName}
        onChange={onChangeFilterInputs}
      />
      <TextField
        sx={{ width: 250 }}
        id="searchEmail"
        name="searchEmail"
        label="Email"
        value={query.searchEmail}
        onChange={onChangeFilterInputs}
      />
      <FormControl
        sx={{
          width: 250,
        }}
      >
        <Select
          id="searchQualification"
          name="searchQualification"
          value={query.searchQualification}
          onChange={onChangeFilterInputs}
        >
          <MenuItem key="-1" value="-1">
            Tutte le qualifiche
          </MenuItem>
          {(searchQualifications || []).map(
            (qualification: any, index: number) => {
              return (
                <MenuItem key={index} value={qualification.id}>
                  {qualification.nome}
                </MenuItem>
              );
            }
          )}
        </Select>
      </FormControl>
      <FormControl
        sx={{
          width: 250,
        }}
      >
        <Select
          id="searchGroup"
          name="searchGroup"
          value={query.searchGroup}
          onChange={onChangeFilterInputs}
        >
          <MenuItem key="-1" value="-1">
            Tutti i gruppi
          </MenuItem>
          {(groups || []).map((group: any, index: number) => {
            return (
              <MenuItem key={index} value={group.id}>
                {group.name}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
      <FormControl
        sx={{
          width: 250,
        }}
      >
        <Select
          id="searchLawyer"
          name="searchLawyer"
          value={query.searchLawyer}
          onChange={onChangeFilterInputs}
        >
          <MenuItem key="-1" value="-1">
            Tutti i referenti
          </MenuItem>
          {(searchLawyers || []).map((lawyer: any, index: number) => {
            return (
              <MenuItem key={index} value={lawyer.id}>
                {lawyer.nome}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
      <FormControlLabel
        value="right"
        control={
          <Checkbox
            checked={query?.searchActive === "on"}
            onChange={handleSearchActiveChange}
          />
        }
        label="Solo utenti attivi"
        labelPlacement="end"
      />

      <Button
        onClick={() => filterInternalUserData(query)}
        variant="contained"
        color="primary"
        type="button"
      >
        Cerca
      </Button>

      <Button
        variant="contained"
        color="primary"
        onClick={() => setQuery(defaultQuery)}
      >
        Mostra tutti
      </Button>
    </Box>
  );
}
