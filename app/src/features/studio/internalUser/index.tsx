import VaporPage from "@vapor/react-custom/VaporPage";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import Filters from "./filters";
import useInternaUserData from "./hooks/useInternaUserData";
import useFilterInteranlUser from "./hooks/useFilterInternalUser";
import PageTitle from "../../../custom-components/PageTitle";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { useNavigate } from "react-router-dom";

export default function InternalUsersIndex() {
    const { internalUserData } = useInternaUserData();
    const navigate = useNavigate();
    const {
        defaultQuery,
        query,
        setQuery,
        list,
        filterInternalUserData,
        loading,
    } = useFilterInteranlUser();
    const { t } = useTranslation();

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="internalUsers"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows || []}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={query}
                onPageChangeCallback={onPageChange}
                onClickCallback={(uniqueId: string) =>
                    navigate(`/users/newUser/${uniqueId}`)
                }
            />
        );
    };

    return (
        <>
            <VaporPage>
                <PageTitle
                    title={t("Gestione Utenti Interni")}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: "Nuovo utente",
                            onclick: () => navigate("/users/newUser"),
                            variant: "contained",
                            startIcon: <AddCircleOutlineIcon />,
                        },
                    ]}
                />
                <VaporPage.Section>
                    <Filters
                        defaultQuery={defaultQuery}
                        query={query}
                        setQuery={setQuery}
                        internalUserData={internalUserData}
                        filterInternalUserData={filterInternalUserData}
                    />
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
