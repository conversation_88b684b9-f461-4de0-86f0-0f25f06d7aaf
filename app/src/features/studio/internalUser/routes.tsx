import InternalUsersIndex from ".";
import NewUserIndex from "./newUser";
import EditUserIndex from "./newUser/editIndex";

export const internalUsers = () => [
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/users/internal",
      element: <InternalUsersIndex />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/users/newUser",
      element: <NewUserIndex />,
    },
  },
  {
    target: "$ONE_LAYOUT_ROUTE",
    handler: {
      exact: true,
      path: "/users/newUser/:uniqueId",
      element: <EditUserIndex />,
    },
  },
];
