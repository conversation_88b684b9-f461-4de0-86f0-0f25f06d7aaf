import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState, useCallback } from "react";
import { IDefaultQuery } from "../interfaces/internalUser.interface";
import { IList } from "../../../../interfaces/general.interfaces";
import { useTranslation } from "@1f/react-sdk";
import { getInternalUsersGrid } from "../../../../utilities/internalUser/gridColumn";

const defaultQuery: IDefaultQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "nomeutente",
    sortOrder: "asc",
    searchName: "",
    searchEmail: "",
    searchQualification: "-1",
    searchGroup: "-1",
    searchLawyer: "-1",
};

export default function useFilterInteranlUser() {
    const { t } = useTranslation();
    const internalUsersFilterRequest = useGetCustom(
        "users/list?noTemplateVars=true"
    );
    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const filterInternalUserData = useCallback(
        async (query?: IDefaultQuery) => {
            let cQuery: any = query;
            const [columns, response]: any = await Promise.all([
                getInternalUsersGrid(t),
                internalUsersFilterRequest.doFetch(true, query),
            ]);
            const { currentPage, totalRows } = response.data;
            setList({
                ...list,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: cQuery?.page,
                pageSize: cQuery?.pageSize,
            });
        },
        []
    );

    useEffect(() => {
        filterInternalUserData(query);
    }, [query, filterInternalUserData]);

    return {
        defaultQuery,
        query,
        setQuery,
        list,
        setList,
        filterInternalUserData,
        loading: internalUsersFilterRequest.loading,
    };
}
