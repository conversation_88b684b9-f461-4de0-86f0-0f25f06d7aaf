import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";


export default function useInternalUserData() {
  const internalUsersRequest = useGetCustom("users/internal");
  const [internalUserData, setInternelUserData] = useState<any>([]);

  useEffect(() => {
    async function fetchData() {
      const { data }: any = await internalUsersRequest.doFetch(true);
      setInternelUserData(data);
    }

    fetchData();
  }, []);

  return { internalUserData };
}
