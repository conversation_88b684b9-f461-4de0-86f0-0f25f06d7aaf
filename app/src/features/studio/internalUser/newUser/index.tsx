import VaporPage from "@vapor/react-custom/VaporPage";
import { useEffect, useState } from "react";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { Tabs, Tab } from "@vapor/react-extended";
import { Box } from "@vapor/react-material";
import { CustomTabPanel, a11yProps } from "../../../../helpers/customTabPanel";
import InternalUserCard from "./internalUserCard";
import CCT from "./cct";
import Billing from "./billing";
import Agenda from "./agenda";
import FetchNewUserData from "./hooks/useFetchNewUserData";
import Spinner from "../../../../custom-components/Spinner";
import useYupValidation from "./hooks/useYupValidation";
import { DEFAULT_NEWUSER_DATA } from "./data/initialData";
import useSubmitData from "./hooks/useSubmitData";
import StudioData from "./studioData";
import Other from "./other";
import { useNavigate } from "react-router-dom";
import TabShowErrorHandler from "./helpers/tabShowErrorHandler";
import ErrorIcon from "@mui/icons-material/Error";
import ToastNotification from "../../../../custom-components/ToastNotification";
import NotAllowedToCreateUser from "./helpers/notAllowedToCreateUser";

export default function NewUserIndex() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { approveNewUser, loading, getRowData, newUserData } =
        FetchNewUserData();
    const {
        saveUser,
        goToSecondStep,
        isFirstStepCompleted,
        setIsFirstStepCompleted,
    } = useSubmitData();
    const { register, handleSubmit, errors } =
        useYupValidation(isFirstStepCompleted);
    const {
        HasTabErrors,
        showTabErrorNotification,
        setShowTabErrorNotificaion,
        tabTextError,
    } = TabShowErrorHandler(errors, t);
    const [value, setValue] = useState<number>(0);
    const [newUserParamsData, setNewUserParamsData] =
        useState(DEFAULT_NEWUSER_DATA);

    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    useEffect(() => {
        setNewUserParamsData({
            ...newUserParamsData,
            min_hourly_rate: getRowData?.form?.min_hourly_rate,
            max_hourly_rate: getRowData?.form?.max_hourly_rate,
            costo_risorsa: getRowData?.form?.costo_risorsa,
            qualificautente: getRowData?.form?.qualificautente.toString(),
            sendCredentials:
                getRowData?.form?.sendCredentials === 1 ? true : false,
            default_page_size: getRowData?.form?.default_page_size,
            altroPercCassa: newUserData?.officeSettings?.perc_cassa,
        });
    }, [getRowData.form]);

    const submitToSecondStep = async () => {
        await goToSecondStep(
            {
                active: newUserParamsData.attivo,
                userType: newUserParamsData.qualificautente,
                uniqueid: newUserParamsData.uniqueid,
            },
            {
                Email: newUserParamsData.Email,
                nome: newUserParamsData.nome,
                sigla: newUserParamsData.sigla,
                uniqueid: newUserParamsData.uniqueid,
                studioPartitaiva: newUserParamsData.studioPartitaiva,
                qualificautente: newUserParamsData.qualificautente,
                studioUniqueid: newUserParamsData.studioUniqueid,
            }
        );
    };

    const onSubmit = async () => {
        const response: any = await saveUser(newUserParamsData);
        if (response.status === 200) {
            navigate("/users/internal");
        }
    };
    const CustomTabLabel = ({
        label,
        hasError,
    }: {
        label: string;
        hasError: boolean;
    }) => (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
            }}
        >
            <span>{label}</span>
            {hasError && <ErrorIcon color="error" style={{ marginLeft: 8 }} />}
        </div>
    );

    return (
        <>
            {" "}
            {loading ? (
                <Spinner />
            ) : (
                <>
                    {approveNewUser ? (
                        <VaporPage>
                            <form
                                onSubmit={handleSubmit(
                                    isFirstStepCompleted === true
                                        ? onSubmit
                                        : submitToSecondStep
                                )}
                            >
                                <PageTitle
                                    title={t("Nuovo Utente")}
                                    pathToPrevPage="/users/internal"
                                    actionButtons={[
                                        {
                                            label: t("Precedente"),
                                            onclick: () =>
                                                setIsFirstStepCompleted(null),
                                            sx: {
                                                visibility:
                                                    isFirstStepCompleted ===
                                                    true
                                                        ? ""
                                                        : "hidden",
                                            },
                                        },
                                        {
                                            label:
                                                isFirstStepCompleted === true
                                                    ? t("Conferma")
                                                    : t("Successiva"),
                                            onclick: () => "",
                                            type: "submit",
                                            variant: "contained",
                                        },
                                    ]}
                                />
                                <VaporPage.Section>
                                    <Box>
                                        <Tabs
                                            onChange={handleChange}
                                            value={value}
                                            size="extraSmall"
                                            variant="standard"
                                        >
                                            <Tab
                                                label={
                                                    <CustomTabLabel
                                                        label={
                                                            isFirstStepCompleted ===
                                                            true
                                                                ? t(
                                                                      "Dati Studio"
                                                                  )
                                                                : t(
                                                                      "Scheda utente Interno"
                                                                  )
                                                        }
                                                        hasError={HasTabErrors(
                                                            isFirstStepCompleted ===
                                                                true
                                                                ? 1
                                                                : 0
                                                        )}
                                                    />
                                                }
                                                {...a11yProps(0)}
                                            />
                                            <Tab
                                                label={
                                                    <CustomTabLabel
                                                        label={t(
                                                            "Fatturazione"
                                                        )}
                                                        hasError={HasTabErrors(
                                                            2
                                                        )}
                                                    />
                                                }
                                                {...a11yProps(1)}
                                            />
                                            <Tab
                                                label={t("CCT")}
                                                {...a11yProps(2)}
                                            />
                                            <Tab
                                                label={t("Agenda")}
                                                {...a11yProps(3)}
                                            />
                                            {isFirstStepCompleted === true ? (
                                                <Tab
                                                    label={t("Altro")}
                                                    {...a11yProps(4)}
                                                />
                                            ) : null}
                                        </Tabs>
                                    </Box>
                                    <CustomTabPanel value={value} index={0}>
                                        {isFirstStepCompleted === true ? (
                                            <StudioData
                                                t={t}
                                                newUserData={newUserData}
                                                register={register}
                                                errors={errors}
                                                newUserParamsData={
                                                    newUserParamsData
                                                }
                                                setNewUserParamsData={
                                                    setNewUserParamsData
                                                }
                                            />
                                        ) : (
                                            <InternalUserCard
                                                t={t}
                                                newUserData={newUserData}
                                                register={register}
                                                errors={errors}
                                                newUserParamsData={
                                                    newUserParamsData
                                                }
                                                setNewUserParamsData={
                                                    setNewUserParamsData
                                                }
                                                errorMessage={
                                                    isFirstStepCompleted
                                                } //passing the data if its error on 'email', 'nome', 'sigla'
                                            />
                                        )}
                                    </CustomTabPanel>
                                    <CustomTabPanel value={value} index={1}>
                                        <Billing
                                            t={t}
                                            newUserData={newUserData}
                                            register={register}
                                            errors={errors}
                                            newUserParamsData={
                                                newUserParamsData
                                            }
                                            setNewUserParamsData={
                                                setNewUserParamsData
                                            }
                                        />
                                    </CustomTabPanel>
                                    <CustomTabPanel value={value} index={2}>
                                        <CCT
                                            t={t}
                                            newUserParamsData={
                                                newUserParamsData
                                            }
                                            setNewUserParamsData={
                                                setNewUserParamsData
                                            }
                                        />
                                    </CustomTabPanel>
                                    <CustomTabPanel value={value} index={3}>
                                        <Agenda
                                            t={t}
                                            newUserData={newUserData}
                                            newUserParamsData={
                                                newUserParamsData
                                            }
                                            setNewUserParamsData={
                                                setNewUserParamsData
                                            }
                                        />
                                    </CustomTabPanel>
                                    <CustomTabPanel value={value} index={4}>
                                        <Other
                                            t={t}
                                            newUserParamsData={
                                                newUserParamsData
                                            }
                                            setNewUserParamsData={
                                                setNewUserParamsData
                                            }
                                        />
                                    </CustomTabPanel>
                                </VaporPage.Section>
                            </form>
                        </VaporPage>
                    ) : (
                        <NotAllowedToCreateUser />
                    )}
                </>
            )}
            <ToastNotification
                showNotification={showTabErrorNotification}
                setShowNotification={setShowTabErrorNotificaion}
                severity="error"
                text={tabTextError}
            />
        </>
    );
}
