import {
    Box,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Divider,
    Button,
    Stack,
    Tooltip,
    InputAdornment,
} from "@vapor/react-material";
import InfoIcon from "@mui/icons-material/Info";
import { tipologiaData, tipologiaFEData } from "./data/selectInputData";
import { useState } from "react";
import { typeOnlyNumbers } from "./helpers/checkHourlyRate";
import { IBillingProps } from "../interfaces/newUserData.interface";

export default function Billing(props: IBillingProps) {
    const {
        t,
        newUserData,
        register,
        errors,
        newUserParamsData,
        setNewUserParamsData,
    } = props;

    const nameMappings1: any = {
        abi: "altroAbi",
        bic: "altroBic",
        cab: "altroCab",
        desc: "altroDesc",
        iban: "altroIban",
    };

    const initialBankDetails = (
        newUserParamsData?.bankAccountDetails || []
    )?.map((bank: any) => {
        const updatedBank: any = {};
        Object.entries(bank).forEach(([key, value]) => {
            updatedBank[nameMappings1[key]] = value;
        });
        return updatedBank;
    });

    // If initialBankDetails exist, select the first bank (index 0), else set to -1 (no selection).
    const [selectedBankIndex, setSelectedBankIndex] = useState<number>(
        initialBankDetails.length > 0 ? 0 : -1
    );

    const [banks, setBanks] = useState<any>(initialBankDetails || []);

    const addBankDetails = () => {
        const newBank = {
            altroDesc: t(`Descrizione banca n° ${banks.length + 1}`),
            altroIban: "",
            altroAbi: "",
            altroCab: "",
            altroBic: "",
        };

        const updatedBanks = [...banks, newBank];
        setBanks(updatedBanks);

        const updatedBankAccountDetails = updatedBanks.map((bank) => ({
            iban: bank.altroIban,
            abi: bank.altroAbi,
            cab: bank.altroCab,
            bic: bank.altroBic,
            desc: bank.altroDesc,
        }));

        setNewUserParamsData({
            ...newUserParamsData,
            bankAccountDetails: updatedBankAccountDetails,
            altroDesc: "", // Clearing the temporary data fields
            altroIban: "",
            altroAbi: "",
            altroCab: "",
            altroBic: "",
        });
        setSelectedBankIndex(updatedBanks.length - 1); // Select the newly added bank
    };

    const removeBank = (index: number) => {
        if (index === -1) return;
        const updatedBanks = [...banks];
        updatedBanks.splice(index, 1);
        setBanks(updatedBanks);

        const updatedBankAccountDetails = updatedBanks.map((bank) => ({
            iban: bank.altroIban,
            abi: bank.altroAbi,
            cab: bank.altroCab,
            bic: bank.altroBic,
            desc: bank.altroDesc,
        }));

        const previousBankIndex: number =
            index > 0 ? index - 1 : updatedBanks.length - 1;

        setNewUserParamsData({
            ...newUserParamsData,
            bankAccountDetails: updatedBankAccountDetails,
            // when removing a bank object, setting the current bank details to the details of the previous bank.
            altroDesc: updatedBankAccountDetails[previousBankIndex]?.desc || "",
            altroIban: updatedBankAccountDetails[previousBankIndex]?.iban || "",
            altroAbi: updatedBankAccountDetails[previousBankIndex]?.abi || "",
            altroCab: updatedBankAccountDetails[previousBankIndex]?.cab || "",
            altroBic: updatedBankAccountDetails[previousBankIndex]?.bic || "",
        });
        setSelectedBankIndex(updatedBanks.length > 0 ? 0 : -1);
    };

    const nameMappings: any = {
        altroDesc: "desc",
        altroIban: "iban",
        altroAbi: "abi",
        altroCab: "cab",
        altroBic: "bic",
    };

    const handleInputChangesBankDetils = (index: number, event: any) => {
        const { name, value } = event.target;
        const fieldName = nameMappings[name] || name; // Map field name

        setNewUserParamsData((prevData: any) => {
            // Ensure that bankAccountDetails is treated as an array
            const updatedBankAccountDetails = Array.isArray(
                prevData.bankAccountDetails
            )
                ? [...prevData.bankAccountDetails]
                : [];

            // Update the specific index of the array if it's not -1
            if (index !== -1) {
                updatedBankAccountDetails[index] = {
                    ...updatedBankAccountDetails[index],
                    [fieldName]: value,
                };
            }

            return {
                ...prevData,
                bankAccountDetails: updatedBankAccountDetails, // Ensure this is the array
                [name]: value,
            };
        });

        if (index === -1) return; //if no banks added prevent updating banks array
        const updatedBanks = [...banks];
        updatedBanks[index][name] = value;
        setBanks(updatedBanks);
    };

    const handleBankSelectChange = (event: any) => {
        const index = event.target.value;
        setSelectedBankIndex(index);
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: value });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: checked });
    };

    return (
        <Box
            autoComplete="off"
            component="form"
            sx={{
                display: "flex",
                alignItems: "center", // Center vertically
                flexDirection: "column",
                "& .MuiTextField-root": {
                    m: 1,
                    width: 500,
                },
                "& .MuiFormControl-root": {
                    m: 1,
                    width: 500,
                },
            }}
        >
            <FormControl>
                <InputLabel>{t("Default Studio Associato")}</InputLabel>
                <Select
                    name="altroDefaultStudio"
                    value={newUserParamsData.altroDefaultStudio}
                    onChange={handleInputChanges}
                >
                    {newUserData?.offices?.map((office: any, index: number) => (
                        <MenuItem key={index} value={office.id}>
                            {office.nome}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Divider
                className="MuiDivider-VaporLight"
                sx={{ width: "100%", m: 2 }}
            />
            <FormControl>
                <InputLabel>{t("Opzioni bancarie")}</InputLabel>
                <Select
                    value={selectedBankIndex}
                    onChange={handleBankSelectChange}
                >
                    {banks.map((bank: any, index: number) => (
                        <MenuItem key={index} value={index}>
                            {bank?.altroDesc}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <TextField
                name="altroDesc"
                value={
                    banks[selectedBankIndex]?.altroDesc === undefined
                        ? newUserParamsData.altroDesc
                        : banks[selectedBankIndex]?.altroDesc
                }
                onChange={(event: any) =>
                    handleInputChangesBankDetils(selectedBankIndex, event)
                }
                label="Banca"
            />
            <TextField
                {...register("altroIban")}
                error={errors.altroIban ? true : false}
                helperText={errors.altroIban?.message}
                name="altroIban"
                value={
                    banks[selectedBankIndex]?.altroIban === undefined
                        ? newUserParamsData.altroIban
                        : banks[selectedBankIndex]?.altroIban
                }
                onChange={(event: any) =>
                    handleInputChangesBankDetils(selectedBankIndex, event)
                }
                inputProps={{ maxLength: 34 }}
                label={t("IBAN *")}
            />{" "}
            {/* need check iban function */}
            <div style={{ display: "flex", flexDirection: "row" }}>
                <TextField
                    name="altroAbi"
                    onKeyDown={(event: any) => typeOnlyNumbers(event)}
                    value={banks[selectedBankIndex]?.altroAbi}
                    inputProps={{ maxLength: 5 }}
                    onChange={(event: any) =>
                        handleInputChangesBankDetils(selectedBankIndex, event)
                    }
                    label="ABI"
                    sx={{
                        "& .MuiInputBase-root": {
                            width: 240,
                        },
                        "& .MuiFormControl-root": {
                            width: 240,
                        },
                        width: "auto !important", // Override the width set by the Box component
                    }}
                />
                <span style={{ marginTop: "45px" }}>/</span>

                <TextField
                    name="altroCab"
                    value={
                        banks[selectedBankIndex]?.altroCab === undefined
                            ? newUserParamsData.altroCab
                            : banks[selectedBankIndex]?.altroCab
                    }
                    inputProps={{ maxLength: 5 }}
                    onChange={(event: any) =>
                        handleInputChangesBankDetils(selectedBankIndex, event)
                    }
                    onKeyDown={(event: any) => typeOnlyNumbers(event)}
                    label="CAB"
                    sx={{
                        "& .MuiInputBase-root": {
                            width: 240,
                        },
                        "& .MuiFormControl-root": {
                            width: 240,
                        },
                        width: "auto !important", // Override the width set by the Box component
                    }}
                />
            </div>
            <TextField
                {...register("altroBic")}
                error={errors.altroBic ? true : false}
                helperText={errors.altroBic?.message}
                name="altroBic"
                label="BIC"
                inputProps={{ maxLength: 11 }}
                value={
                    banks[selectedBankIndex]?.altroBic === undefined
                        ? newUserParamsData.altroBic
                        : banks[selectedBankIndex]?.altroBic
                }
                onChange={(event: any) =>
                    handleInputChangesBankDetils(selectedBankIndex, event)
                }
            />
            <div style={{ display: "flex" }}>
                <Button
                    color="error"
                    variant="outlined"
                    onClick={() => removeBank(selectedBankIndex)}
                    sx={{ mb: 2, mr: 2 }}
                >
                    {t("Elimina")}
                </Button>
                <Button
                    variant="outlined"
                    onClick={() => addBankDetails()}
                    sx={{ mb: 2 }}
                >
                    {t("Aggiungi banca")}
                </Button>
            </div>
            <Divider
                className="MuiDivider-VaporLight"
                sx={{ width: "100%", m: 2 }}
            />
            <Stack
                alignItems="end"
                autoComplete="off"
                component="form"
                direction="row"
                sx={{
                    ml: 3,
                }}
            >
                <TextField
                    name="codice_destinatario_af"
                    label={t("Codice destinatario")}
                    onChange={handleInputChanges}
                />
                <Tooltip
                    arrow
                    title={t(
                        "Codice destinatario utilizzato per l'autofattura"
                    )}
                >
                    <InfoIcon sx={{ mb: "15px" }} />
                </Tooltip>
            </Stack>
            <FormControl>
                <InputLabel>{t("Regime Fiscale")}</InputLabel>
                <Select name="altroRegimeFiscale" onChange={handleInputChanges}>
                    {newUserData?.regimeFiscale?.map(
                        (fiscale: any, index: number) => (
                            <MenuItem key={index} value={fiscale.id}>
                                {fiscale.nome}
                            </MenuItem>
                        )
                    )}
                </Select>
            </FormControl>
            <FormControl>
                <InputLabel>{t("Tipo cassa 1")}</InputLabel>
                <Select
                    name="altroTipoCassa"
                    value={newUserParamsData.altroTipoCassa}
                    onChange={handleInputChanges}
                >
                    {newUserData?.tipoCassa?.map((tipo: any, index: number) => (
                        <MenuItem key={index} value={tipo.id}>
                            {tipo.descrizione}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <TextField
                name="altroPercCassa"
                label={t("Percentuale cassa 1")}
                value={newUserParamsData.altroPercCassa}
                onChange={handleInputChanges}
                onKeyDown={(event: any) => typeOnlyNumbers(event)}
                InputProps={{
                    endAdornment: (
                        <InputAdornment position="end">%</InputAdornment>
                    ),
                }}
            />
            <FormControl>
                <FormGroup
                    sx={{
                        "& .MuiFormControlLabel-root": {
                            justifyContent: "start",
                            ml: "10px",
                        },
                    }}
                >
                    <FormControlLabel
                        value="left"
                        control={
                            <Checkbox
                                name="addCassa"
                                checked={newUserParamsData.addCassa}
                                onChange={handleCheckboxChanges}
                            />
                        }
                        label="Cassa 2"
                        labelPlacement="start"
                    />
                </FormGroup>
            </FormControl>
            {newUserParamsData.addCassa && (
                <>
                    <FormControl>
                        <InputLabel>{t("Tipo cassa 2")}</InputLabel>
                        <Select
                            name="altroTipoCassa2"
                            value={newUserParamsData.altroTipoCassa2}
                            onChange={handleInputChanges}
                        >
                            {newUserData?.tipoCassa?.map(
                                (tipo: any, index: number) => (
                                    <MenuItem key={index} value={tipo.id}>
                                        {tipo.descrizione}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>
                    <TextField
                        name="altroPercCassa2"
                        label={t("Percentuale cassa 2")}
                        onKeyDown={(event: any) => typeOnlyNumbers(event)}
                        value={newUserParamsData.altroPercCassa2}
                        onChange={handleInputChanges}
                    />
                </>
            )}
            <Stack
                alignItems="end"
                autoComplete="off"
                component="form"
                direction="row"
                sx={{
                    ml: 3,
                }}
            >
                <FormControl>
                    <InputLabel>{t("Tipologia")}</InputLabel>
                    <Select
                        name="tipologia"
                        value={newUserParamsData.tipologia}
                        onChange={handleInputChanges}
                    >
                        {tipologiaData.map((tipo: any, index: number) => (
                            <MenuItem key={index} value={tipo.value}>
                                {tipo.name}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <Tooltip
                    arrow
                    title={t(
                        "Personalizza la tipologia predifinita alla creazione di un nuovo documento"
                    )}
                >
                    <InfoIcon sx={{ mb: "15px" }} />
                </Tooltip>
            </Stack>
            <Stack
                alignItems="end"
                autoComplete="off"
                component="form"
                direction="row"
                sx={{
                    ml: 3,
                }}
            >
                <FormControl>
                    <InputLabel>{t("Tipo documento FE")}</InputLabel>
                    <Select
                        name="tipologiaFE"
                        onChange={handleInputChanges}
                        value={newUserParamsData.tipologiaFE}
                    >
                        {tipologiaFEData.map((tipo: any, index: number) => (
                            <MenuItem key={index} value={tipo.value}>
                                {tipo.name}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <Tooltip
                    arrow
                    title={t(
                        "Personalizza la tipologia predifinita alla creazione di un nuovo documento "
                    )}
                >
                    <InfoIcon sx={{ mb: "15px" }} />
                </Tooltip>
            </Stack>
        </Box>
    );
}
