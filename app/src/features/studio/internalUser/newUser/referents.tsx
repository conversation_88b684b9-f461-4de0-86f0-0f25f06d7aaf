import VaporPage from "@vapor/react-custom/VaporPage";
import Spinner from "../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import {
    Box,
    FormControl,
    Select,
    MenuItem,
    Button,
} from "@vapor/react-material";
import AddCircleIcon from "@mui/icons-material/AddCircle";
import { useState } from "react";

interface IReservesProps {
    t: any;
    delegates: any;
    list: any;
    loading: boolean;
    queryParams: any;
    setQueryParams: React.Dispatch<React.SetStateAction<any>>;
    addDelegates: (id: string) => void;
}

export default function Referents(props: IReservesProps) {
    const {
        t,
        delegates = [],
        list,
        loading,
        addDelegates,
        queryParams,
        setQueryParams,
    } = props;
    const [selectedDelegateId, setSelectedDelegateId] = useState<string>("");

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQueryParams({
            ...queryParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="referents"
                setQuery={setQueryParams}
                columns={list.columns}
                data={list.rows || []}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={queryParams}
                onPageChangeCallback={onPageChangeCallback}
            />
        );
    };
    return (
        <>
            <VaporPage.Section>
                <Box display="flex" alignItems="end" gap={2}>
                    <FormControl
                        sx={{
                            width: 250,
                        }}
                    >
                        <Select
                            name="delegato"
                            onChange={(event: any) =>
                                setSelectedDelegateId(event.target.value)
                            }
                        >
                            {(delegates || []).map(
                                (delegate: any, index: number) => {
                                    return (
                                        <MenuItem
                                            key={index}
                                            value={delegate.id}
                                        >
                                            {delegate.nome}
                                        </MenuItem>
                                    );
                                }
                            )}
                        </Select>
                    </FormControl>
                    <Button
                        type="button"
                        variant="outlined"
                        disabled={selectedDelegateId === ""}
                        onClick={() => addDelegates(selectedDelegateId)}
                        endIcon={<AddCircleIcon />}
                    >
                        {t("Aggiungi delegato")}
                    </Button>
                </Box>
            </VaporPage.Section>
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </>
    );
}
