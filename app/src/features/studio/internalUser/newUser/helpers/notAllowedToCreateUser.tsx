import VaporPage from "@vapor/react-custom/VaporPage";
import { Typography, Stack, Button } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";

export default function NotAllowedToCreateUser() {
    const navigate = useNavigate();
    const { t } = useTranslation();

    return (
        <VaporPage>
            <VaporPage.Section>
                {/* Centering using Stack with direction: 'column' */}
                <Stack direction="column" spacing={4} alignItems="center">
                    <Typography variant="h2">{t("Attenzione!")}</Typography>
                    <Typography>
                        {t(`È stato raggiunto il limite massimo di utenti. Per
                        aggiungere di nuovi è necessario corrispondere la quota
                        annuale per ogni nuovo utente.`)}
                    </Typography>
                    <Button
                        variant="contained"
                        onClick={() => navigate("/users/internal")}
                    >
                        {t("Annulla")}
                    </Button>
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
}
