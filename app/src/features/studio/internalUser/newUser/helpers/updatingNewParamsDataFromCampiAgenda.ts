// When render the agenda tab, we take the data from campi_agenda object and populate the newUserParamsData
export const updatingNewParamsDataFromCampiAgenda = (
    newUserParamsData: any
) => {
    const campi_agenda = newUserParamsData.campi_agenda || {};

    // Create a new object to store the transformed data
    const transformedData = { ...newUserParamsData };

    // Extract and set preview values
    transformedData.previewDeadlineValues =
        campi_agenda.semplice?.preview || [];
    transformedData.previewDeadlineArchiveValues =
        campi_agenda.pratica?.preview || [];
    transformedData.previewHearingsValues = campi_agenda.udienza?.preview || [];
    transformedData.previewPoliswebValues =
        campi_agenda.polisweb?.preview || [];

    // Loop through each key in campi_agenda and transform the values
    for (const section in campi_agenda) {
        for (const key in campi_agenda[section]) {
            if (key !== "preview") {
                const newKey = `${section}_${key}`;
                if (campi_agenda[section][key]) {
                    transformedData[newKey] = "on";
                }
            }
        }
    }
    return transformedData;
};
