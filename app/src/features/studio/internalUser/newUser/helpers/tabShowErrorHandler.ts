import { useCallback, useState } from "react";

export default function TabShowErrorHandler(errors: any = {}, t: any) {
    const [showTabErrorNotification, setShowTabErrorNotificaion] =
        useState<boolean>(false);
    const [tabTextError, setTabTextError] = useState<string>("");
    const [shownErrorTabs, setShownErrorTabs] = useState<Set<number>>(
        new Set()
    );

    const tabErrorMapping: any = [
        {
            tabName: t("Scheda utente Interno"),
            fields: [
                "nomeutente",
                "nomepersonale",
                "cognomepersonale",
                "Email",
                "nome",
                "password",
                "passwordConfirm",
                "min_hourly_rate",
                "max_hourly_rate",
            ],
        },
        {
            tabName: t("Dati studio"),
            fields: [
                "studioNome",
                "studioCodicefiscale",
                "studioPartitaiva",
                "studioIndirizzo",
                "studioCap",
                "studioCitta",
                "studioProvincia",
                "studioTelefono",
                "studioEmail",
            ],
        },
        { tabName: t("Fatturazione"), fields: ["altroIban", "altroBic"] },
    ];

    const HasTabErrors = (tabIndex: number): boolean => {
        const { tabName, fields } = tabErrorMapping[tabIndex];
        const fieldsHaveErrors = fields.some((field: any) => errors[field]);
        if (fieldsHaveErrors && !shownErrorTabs.has(tabIndex)) {
            setTabErrorCallback(tabIndex, tabName);
        }
        return fieldsHaveErrors;
    };

    const setTabErrorCallback = useCallback(
        (tabIndex: number, tabName: string) => {
            setShowTabErrorNotificaion(true);
            setTabTextError(
                t(`Correggere gli errori presenti nella tab ${tabName}`)
            );
            setShownErrorTabs((prev) => new Set(prev).add(tabIndex));
        },
        []
    );

    return {
        HasTabErrors,
        showTabErrorNotification,
        setShowTabErrorNotificaion,
        tabTextError,
    };
}
