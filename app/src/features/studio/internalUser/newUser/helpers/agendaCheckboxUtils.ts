const previewMappings: any = {
  semplice: 'previewDeadlineValues',
  pratica: 'previewDeadlineArchiveValues',
  polisweb: 'previewPoliswebValues',
  udienza: 'previewHearingsValues'
};

const addItemToPreview = (paramsDataUpdated: any, category: string, valueKeyName: string) => {
  if (!paramsDataUpdated.campi_agenda[category]) {
      paramsDataUpdated.campi_agenda[category] = {};
  }
  if (!paramsDataUpdated.campi_agenda[category].preview) {
      paramsDataUpdated.campi_agenda[category].preview = [];
  }
  if (!paramsDataUpdated.campi_agenda[category].preview.includes(valueKeyName)) {
      paramsDataUpdated.campi_agenda[category].preview.push(valueKeyName);
  }
  if (!paramsDataUpdated[previewMappings[category]].includes(valueKeyName)) {
      paramsDataUpdated[previewMappings[category]].push(valueKeyName);
  }
};

const removeItemFromPreview = (paramsDataUpdated: any, category: string, valueKeyName: string) => {
  paramsDataUpdated.campi_agenda[category].preview = paramsDataUpdated.campi_agenda[category].preview.filter(
      (previewItem: any) => previewItem !== valueKeyName
  );
  paramsDataUpdated[previewMappings[category]] = paramsDataUpdated[previewMappings[category]].filter(
      (item: any) => item !== valueKeyName
  );
};

export const handleItemAddition = (paramsDataUpdated: any, item: any) => {
  const { name } = item;
  const [category, key, subKey] = name.split("_");
  if (category === "anteprima") {
      addItemToPreview(paramsDataUpdated, key, subKey);
  } else {
      if (!paramsDataUpdated.campi_agenda[category]) {
          paramsDataUpdated.campi_agenda[category] = {};
      }
      paramsDataUpdated.campi_agenda[category][key] = 1;
      paramsDataUpdated[name] = "on";
  }
};

export const handleItemRemoval = (paramsDataUpdated: any, item: any) => {
  const { name } = item;
  const [category, key, subKey] = name.split("_");
  if (category === "anteprima") {
      removeItemFromPreview(paramsDataUpdated, key, subKey);
  } else {
      if (paramsDataUpdated.campi_agenda[category]) {
          delete paramsDataUpdated.campi_agenda[category][key];
      }
      delete paramsDataUpdated[name];
  }
};