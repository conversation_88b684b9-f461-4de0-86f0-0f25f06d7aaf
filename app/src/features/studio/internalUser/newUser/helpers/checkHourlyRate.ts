interface ICheckHourlyRate {
  newUserParamsData: any;
  setNewUserParamsData: React.Dispatch<React.SetStateAction<any>>;
}

export function checkHourlyRate(props: ICheckHourlyRate): void {
  const { newUserParamsData, setNewUserParamsData } = props;
  const { min_hourly_rate, max_hourly_rate } = newUserParamsData;
  if (parseFloat(min_hourly_rate) > parseFloat(max_hourly_rate)) {
    setNewUserParamsData({
      ...newUserParamsData,
      max_hourly_rate: min_hourly_rate,
    });
  } else {
    console.log("error false");
  }
}

export function typeOnlyNumbers(event: KeyboardEvent): void {
  const isNumberKey = /^\d$/.test(event.key);
  const isTabKey = event.key === "Tab";
  const isBackspaceKey = event.key === "Backspace";
  const isPeriod = event.key === ".";

  if (!isNumberKey && !isTabKey && !isBackspaceKey && !isPeriod) {
    event.preventDefault();
  }
}
