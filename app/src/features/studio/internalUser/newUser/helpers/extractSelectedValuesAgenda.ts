import { generatingPreviewSelectData } from "./generatingPreviewSelectData";

export const extractSelectedValues = (campi_agenda: any, options: any) => {
  const selectedValues: any = {};

  if (!campi_agenda) return selectedValues;

  // Iterate over each category in campi_agenda
  Object.entries(campi_agenda).forEach(([category, data]) => {
      // Initialize the array for the current category
      if (!selectedValues[category]) {
          selectedValues[category] = [];
      }

      // Iterate over each key in the category data
      Object.entries(data as any).forEach(([key, value]: any) => {
          // If the value is 1, it means it was selected
          if (value === 1) {
              // Find the corresponding option object
              const option = options.find(
                  (option: any) => option.name === `${category}_${key}`
              );
              if (option) {
                  // Push the option to the array
                  selectedValues[category].push(option);
              }
          } else if (key === "preview" && Array.isArray(value)) {
              // Handle preview options
              if (!selectedValues.preview) {
                  selectedValues.preview = {};
              }

              if (!selectedValues.preview[category]) {
                  selectedValues.preview[category] = [];
              }

              const addingAntiprima = value.map(
                  (item: any) => `anteprima_${category}_${item}`
              );
              const addingAntiprimaOnOptions =
                  generatingPreviewSelectData(options);

              addingAntiprima?.forEach((previewKey: string) => {
                  const option = addingAntiprimaOnOptions.find(
                      (option1: any) => option1.name === previewKey
                  );
                  if (option) {
                      // Push the option to the preview array under the correct category
                      selectedValues.preview[category].push(option);
                  }
              });
          }
      });
  });

  return selectedValues;
};