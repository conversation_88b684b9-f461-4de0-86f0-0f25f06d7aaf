import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
} from "@vapor/react-material";
import { useState } from "react";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { ICCTProps } from "../interfaces/newUserData.interface";

export default function CCT(props: ICCTProps) {
  const { t, newUserParamsData, setNewUserParamsData } = props;

  const [values, setValues] = useState({
    password: false,
    showPassword: false,
  });

  const handleClickShowPassword = () => {
    setValues({
      ...values,
      showPassword: !values.showPassword,
    });
  };
  const handleMouseDownPassword = (event: any) => {
    event.preventDefault();
  };

  const handleInputChanges = (event: any) => {
    const { name, value } = event.target;
    setNewUserParamsData({ ...newUserParamsData, [name]: value });
  };

  return (
    <Box
      autoComplete="off"
      component="form"
      sx={{
        display: "flex",
        alignItems: "center", // Center vertically
        flexDirection: "column",
        "& .MuiTextField-root": {
          m: 1,
          width: 500,
        },
        "& .MuiFormControl-root": {
          // Set width for FormControl components
          m: 1,
          width: 500,
        },
      }}
    >
      <TextField
        name="usernamecct"
        label={t("Username CCT")}
        onChange={handleInputChanges}
      />
      <TextField
        name="passwordcct"
        type={values.showPassword ? "text" : "password"}
        onChange={handleInputChanges}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={handleClickShowPassword}
                onMouseDown={handleMouseDownPassword}
                edge="end"
              >
                {values.showPassword ? (
                  <VisibilityIcon />
                ) : (
                  <VisibilityOffIcon />
                )}
              </IconButton>
            </InputAdornment>
          ),
        }}
        label={t("Password CCT")}
      />{" "}
    </Box>
  );
}
