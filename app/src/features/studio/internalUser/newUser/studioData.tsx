import {
    Box,
    TextField,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormControlLabel,
    Checkbox,
    Stack,
    Tooltip,
} from "@vapor/react-material";
import InfoIcon from "@mui/icons-material/Info";
import { typeOnlyNumbers } from "./helpers/checkHourlyRate";
import { IStudioData } from "../interfaces/newUserData.interface";

export default function StudioData(props: IStudioData) {
    const {
        newUserData,
        register,
        errors,
        newUserParamsData,
        setNewUserParamsData,
        t,
    } = props;

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: value });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: checked });
    };

    return (
        <div style={{ display: "flex" }}>
            {/* Left Box */}
            <Box
                component={"section"}
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                    "& .MuiFormControl-root": {
                        // Set width for FormControl components
                        m: 1,
                        width: 500,
                    },
                    "& .MuiFormHelperText-root": {
                        whiteSpace: "nowrap",
                    },
                }}
            >
                <TextField
                    name="nomeutente"
                    label={t("Avvocato")}
                    value={newUserParamsData?.nomeutente}
                    readOnly
                />
                <FormControl>
                    <InputLabel>{t("Importa da")}</InputLabel>
                    <Select
                        name="userImport"
                        value={newUserParamsData?.userImport}
                        onChange={handleInputChanges}
                    >
                        <MenuItem key={-1} value="-1">
                            -
                        </MenuItem>
                        {newUserData?.users.map((user: any, index: number) => (
                            <MenuItem key={index} value={user.id}>
                                {user.nomeutente}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <TextField
                    name="studioNome"
                    label={t("Nome studio *")}
                    value={newUserParamsData?.studioNome}
                    {...register("studioNome")}
                    error={errors.studioNome ? true : false}
                    helperText={errors.studioNome?.message}
                    onChange={handleInputChanges}
                />
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                    }}
                >
                    <TextField
                        name="studioCodicefiscale"
                        value={newUserParamsData?.studioCodicefiscale}
                        label={t("Codice fiscale *")}
                        sx={{ width: "300px !important" }}
                        {...register("studioCodicefiscale")}
                        error={errors.studioCodicefiscale ? true : false}
                        helperText={errors.studioCodicefiscale?.message}
                        onChange={handleInputChanges}
                    />

                    <FormControlLabel
                        value="left"
                        control={
                            <Checkbox
                                name="studioNascondiCFfatturazione"
                                value={
                                    newUserParamsData?.studioNascondiCFfatturazione
                                }
                                onChange={handleCheckboxChanges}
                            />
                        }
                        label={t("Nascondi in fatturazione")}
                        labelPlacement="start"
                        sx={{ mt: 3 }}
                    />
                </div>
                <TextField
                    name="studioPartitaiva"
                    label={t("Partita IVA *")}
                    value={newUserParamsData?.studioPartitaiva}
                    {...register("studioPartitaiva")}
                    error={errors.studioPartitaiva ? true : false}
                    helperText={errors.studioPartitaiva?.message}
                    onChange={handleInputChanges}
                />
                <div
                    style={{
                        display: "flex",
                        alignItems: "baseline",
                    }}
                >
                    <TextField
                        name="studioIndirizzo"
                        label={t("Indirizzo e CAP *")}
                        value={newUserParamsData?.studioIndirizzo}
                        sx={{ width: "385px !important" }}
                        {...register("studioIndirizzo")}
                        error={errors.studioIndirizzo ? true : false}
                        helperText={errors.studioIndirizzo?.message}
                        onChange={handleInputChanges}
                    />
                    <TextField
                        name="studioCap"
                        label={t("No label")}
                        value={newUserParamsData?.studioCap}
                        inputProps={{ maxLength: 5 }}
                        InputLabelProps={{
                            shrink: true,
                            style: { visibility: "hidden" },
                        }}
                        sx={{
                            width: "100px !important",
                        }}
                        {...register("studioCap")}
                        error={errors.studioCap ? true : false}
                        helperText={errors.studioCap?.message}
                        onChange={handleInputChanges}
                    />
                </div>
                <div
                    style={{
                        display: "flex",
                        alignItems: "baseline",
                    }}
                >
                    <TextField
                        name="studioCitta"
                        label={t("Città e Provincia *")}
                        value={newUserParamsData?.studioCitta}
                        sx={{ width: "385px !important" }}
                        {...register("studioCitta")}
                        error={errors.studioCitta ? true : false}
                        helperText={errors.studioCitta?.message}
                        onChange={handleInputChanges}
                    />
                    <TextField
                        name="studioProvincia"
                        label="No label"
                        value={newUserParamsData?.studioProvincia}
                        inputProps={{ maxLength: 2 }}
                        InputLabelProps={{
                            shrink: true,
                            style: { visibility: "hidden" },
                        }}
                        sx={{
                            width: "100px !important",
                        }}
                        {...register("studioProvincia")}
                        error={errors.studioProvincia ? true : false}
                        helperText={errors.studioProvincia?.message}
                        onChange={handleInputChanges}
                    />
                </div>

                <div
                    style={{
                        display: "flex",
                        alignItems: "baseline",
                    }}
                >
                    <TextField
                        label={t("Telefono *")}
                        name="studioTelefono"
                        value={newUserParamsData?.studioTelefono}
                        {...register("studioTelefono")}
                        error={errors.studioTelefono ? true : false}
                        helperText={errors.studioTelefono?.message}
                        onChange={handleInputChanges}
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                    />
                    <TextField
                        label={t("Fax")}
                        name="studioFax"
                        value={newUserParamsData?.studioFax}
                        onChange={handleInputChanges}
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                    />
                    <TextField
                        label={t("Mobile")}
                        name="studioMobile"
                        value={newUserParamsData?.studioMobile}
                        onChange={handleInputChanges}
                        onKeyDown={(event: any) => typeOnlyNumbers(event)}
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                    />
                </div>
            </Box>

            {/* Middle Box */}
            <Box
                autoComplete="off"
                component="form"
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                    "& .MuiFormControl-root": {
                        // Set width for FormControl components
                        m: 1,
                        width: 500,
                    },
                    ml: 6, // Add margin to the left side
                }}
            >
                <TextField
                    name="studioEmail"
                    label={t("Email Studio")}
                    value={newUserParamsData?.studioEmail}
                    {...register("studioEmail")}
                    error={errors.studioEmail ? true : false}
                    helperText={errors.studioEmail?.message}
                    onChange={handleInputChanges}
                />

                <TextField
                    name="studioWeb"
                    label="Web"
                    onChange={handleInputChanges}
                />

                <TextField
                    name="studioPolizza"
                    label={t("Polizza")}
                    value={newUserParamsData?.studioPolizza}
                    onChange={handleInputChanges}
                />

                <FormControl>
                    <InputLabel>{t("Sezionale pratiche")}</InputLabel>
                    <Select
                        name="identificatore"
                        value={newUserParamsData?.identificatore}
                        onChange={handleInputChanges}
                    >
                        <MenuItem value="-1">-</MenuItem>
                        {newUserData?.identificatori.map(
                            (identificatori: any, index: number) => (
                                <MenuItem
                                    key={index}
                                    value={identificatori.name}
                                >
                                    {identificatori.name}
                                </MenuItem>
                            )
                        )}
                    </Select>
                </FormControl>

                <FormControl>
                    <InputLabel>{t("Privacy Fatturazione")}</InputLabel>
                    <FormControlLabel
                        value="left"
                        control={
                            <Checkbox
                                name="studioRiservaFatturazione"
                                checked={
                                    newUserParamsData.studioRiservaFatturazione !==
                                    undefined
                                        ? newUserParamsData.studioRiservaFatturazione
                                        : false
                                }
                                onChange={handleCheckboxChanges}
                            />
                        }
                        label={t("Riserva i dati della fatturazione")}
                        labelPlacement="end"
                    />
                </FormControl>

                <div
                    style={{
                        marginTop: "30px",
                        marginBottom: "5px",
                        marginLeft: "10px",
                    }}
                >
                    <Typography>
                        {t("Impostazioni per la fatturazione elettronica")}
                    </Typography>
                </div>

                <Stack
                    alignItems="end"
                    autoComplete="off"
                    component="form"
                    direction="row"
                >
                    <TextField
                        name="pec_address_for_pa_invoice"
                        label={t("PEC di destinazione")}
                        value={newUserParamsData?.pec_address_for_pa_invoice}
                        onChange={handleInputChanges}
                    />
                    <Tooltip
                        arrow
                        title={t(
                            "Inserire l'indirizzo che si è ricevuto dal sistema di interscambio successivamente al primo invio"
                        )}
                    >
                        <InfoIcon sx={{ mb: "15px" }} />
                    </Tooltip>
                </Stack>

                <FormControl>
                    <InputLabel>{t("Tipo ritenuta")}</InputLabel>
                    <Select
                        name="pa_tipo_ritenuta"
                        value={newUserParamsData?.pa_tipo_ritenuta}
                        onChange={handleInputChanges}
                    >
                        <MenuItem key={0} value="0">
                            {t("Non impostata")}
                        </MenuItem>
                        <MenuItem key={1} value="1">
                            {t(
                                "Persona fisica - Utilizza nome e cognome personali"
                            )}{" "}
                        </MenuItem>
                        <MenuItem key={2} value="2">
                            {t(
                                "Persona giuridica - Utilizza i dati dello studio"
                            )}{" "}
                        </MenuItem>
                    </Select>
                </FormControl>
            </Box>

            {/* Right Box */}
            <Box
                autoComplete="off"
                component="form"
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        // width: 500,
                    },
                    "& .MuiFormControl-root": {
                        // Set width for FormControl components
                        m: 1,
                        width: 500,
                    },
                    ml: 6, // Add margin to the left side
                }}
            >
                <Stack
                    alignItems="end"
                    autoComplete="off"
                    component="form"
                    direction="row"
                >
                    <TextField
                        name="pa_id_codice"
                        label={t("IdCodice del blocco IdTrasmittente")}
                        value={newUserParamsData?.pa_id_codice}
                        onChange={handleInputChanges}
                    />
                    <Tooltip
                        arrow
                        title={t(
                            "Identificativo fiscale del soggetto trasmittente, se non valorizzato viene automaticamente utilizzato il Codice fiscale"
                        )}
                    >
                        <InfoIcon sx={{ mb: "15px" }} />
                    </Tooltip>
                </Stack>

                <Stack
                    alignItems="end"
                    autoComplete="off"
                    component="form"
                    direction="row"
                >
                    <TextField
                        name="pa_id_codice_da"
                        label={t("IdCodice del blocco IdFiscaleIVA")}
                        value={newUserParamsData?.pa_id_codice_da}
                        onChange={handleInputChanges}
                    />
                    <Tooltip
                        arrow
                        title={t(
                            "Numero di identificazione fiscale del cedente/prestatore, se non valorizzato viene automaticamente utilizzata la Partita IVA"
                        )}
                    >
                        <InfoIcon sx={{ mb: "15px" }} />
                    </Tooltip>
                </Stack>

                <Stack
                    alignItems="end"
                    autoComplete="off"
                    component="form"
                    direction="row"
                >
                    <TextField
                        name="pa_codice_fiscale_da"
                        label={t("CodiceFiscale del blocco DatiAnagrafici")}
                        value={newUserParamsData?.pa_codice_fiscale_da}
                        onChange={handleInputChanges}
                    />
                    <Tooltip
                        arrow
                        title={t(
                            "Codice fiscale del cedente/prestatore, se non valorizzato viene automaticamente utilizzato il Codice fiscale nel caso di Persona fisica e la Partita IVA nel caso di Persona giuridica"
                        )}
                    >
                        <InfoIcon sx={{ mb: "15px" }} />
                    </Tooltip>
                </Stack>

                <FormControlLabel
                    value="left"
                    control={
                        <Checkbox
                            name="nascondi_dati_pagamento_xml"
                            value={
                                newUserParamsData?.nascondi_dati_pagamento_xml
                            }
                            onChange={handleCheckboxChanges}
                        />
                    }
                    label={t("Nascondi Dati Pagamento in XML")}
                    labelPlacement="end"
                    sx={{ ml: "1px" }}
                />
            </Box>
        </div>
    );
}
