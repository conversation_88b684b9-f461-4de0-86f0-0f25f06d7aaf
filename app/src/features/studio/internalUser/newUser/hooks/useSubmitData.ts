import usePostCustom from "../../../../../hooks/usePostCustom";
import { useState } from "react";

export default function useSubmitData() {
    const checkUserRequest = usePostCustom(
        "users/check-user?noTemplateVars=true"
    );
    const checkUnivocalFieldsRequest = usePostCustom(
        "users/checkunivocalfields?noTemplateVars=true"
    );
    const saveUserRequest = usePostCustom("users/save?noTemplateVars=true");
    const [isFirstStepCompleted, setIsFirstStepCompleted] = useState<any>(null);

    const goToSecondStep = async (
        paramsForCheckUser: any,
        paramsForCheckUnivocalFields: any
    ): Promise<void> => {
        const response: any = await checkUserRequest.doFetch(
            true,
            paramsForCheckUser
        );

        if (response) {
            const { data }: any = await checkUnivocalFieldsRequest.doFetch(
                true,
                paramsForCheckUnivocalFields
            );

            setIsFirstStepCompleted(
                typeof data === "boolean" ? data : data.message
            );
        }
    };

    
    const convertToFormData = (data: any) => {
        const formData = new FormData();

        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                if (key === "bankAccountDetails" && Array.isArray(data[key])) {
                    // Handle bankAccountDetails array
                    data[key].forEach((bankDetail: any, index: any) => {
                        for (const field in bankDetail) {
                            if (bankDetail.hasOwnProperty(field)) {
                                formData.append(
                                    `bankAccountDetails[${index}][${field}]`,
                                    bankDetail[field]
                                );
                            }
                        }
                    });
                } else if (
                    [
                        "previewDeadlineValues",
                        "previewDeadlineArchiveValues",
                        "previewHearingsValues",
                        "previewPoliswebValues",
                    ].includes(key) &&
                    Array.isArray(data[key])
                ) {
                    // Handle specific arrays as JSON strings
                    formData.append(key, JSON.stringify(data[key]));
                } else {
                    // Append other fields directly
                    formData.append(key, data[key]);
                }
            }
        }

        return formData;
    };

    const saveUser = async (params: any) => {
        const paramsCopy = { ...params }; //copying the object to not effect the main object to other components

        const formData = convertToFormData(paramsCopy);

        const response = await saveUserRequest.doFetch(true, formData);
        return response;
    };

    return {
        saveUser,
        goToSecondStep,
        isFirstStepCompleted,
        setIsFirstStepCompleted,
    };
}
