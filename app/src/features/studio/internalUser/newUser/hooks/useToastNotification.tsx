import { useState } from "react";
import ToastNotification from "../../../../../custom-components/ToastNotification"; //its a component

const useToastNotification = () => {
    const [notification, setNotification] = useState<{
        showNotification: boolean;
        text: string;
    }>({
        showNotification: false,
        text: "",
    });

    const showNotification = (text: string): boolean => {
        setNotification({
            showNotification: true,
            text,
        });

        return true;
    };

    const hideNotification = () => {
        setNotification((prev: any) => ({ ...prev, showNotification: false }));
    };

    const ShowNotificationUI = () => (
        <ToastNotification
            showNotification={notification.showNotification}
            setShowNotification={hideNotification}
            text={notification.text}
            severity="info"
        />
    );

    return { showNotification, ShowNotificationUI };
};

export default useToastNotification;
