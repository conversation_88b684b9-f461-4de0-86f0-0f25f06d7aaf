import { useCallback, useEffect, useState } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import usePostCustom from "../../../../../hooks/usePostCustom";
import { IList } from "../../../../../interfaces/general.interfaces";
import useConfirmModal from "./useConfimModal";
import { getUserReferentsGrid } from "../../../../../utilities/userReferent/gridColumn";

const defaultQueryParmas = {
    uniqueid: "",
    qualifica: "1",
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "asc",
};

export default function useFetchReferents(uniqueid: string = "", t: any) {
    const getReferentsDataRequest = useGetCustom(
        "usersreferents/list?noTemplateVars=true"
    );
    const getDelegatesDataRequest = useGetCustom(
        `users/get-delegates-data?noTemplateVars=true&uniqueid=${uniqueid}`
    );
    const addDelegatesRequest = usePostCustom("usersreferents/adddelegate");
    const deleteDelegatesRequest = usePostCustom(
        "usersreferents/deletedelegate"
    );
    const { ConfirmModalComponent } = useConfirmModal();

    const [queryReferentsParams, setQueryReferentsParams] = useState({
        ...defaultQueryParmas,
        uniqueid,
    });
    const [delegates, setDelegates] = useState([]);
    const [openConfirmModalReferents, setOpenConfimModalReferents] = useState<{
        state: boolean;
        id: string;
    }>({ state: false, id: "" });
    const [listReferents, setListReferents] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const addDelegates = async (id: string) => {
        const params = {
            slaveId: id,
            uniqueid,
        };

        const response: any = await addDelegatesRequest.doFetch(true, params);
        if (response) {
            fetchingUserReferantsData(queryReferentsParams);
        }
    };

    const deleteDelegates = async (id: string) => {
        const params = {
            relationId: id,
            uniqueid,
        };

        const response: any = await deleteDelegatesRequest.doFetch(
            true,
            params
        );
        if (response) {
            fetchingUserReferantsData(queryReferentsParams);
            setOpenConfimModalReferents({ state: false, id: "" });
        }
    };

    const fetchingUserReferantsData = useCallback(
        async (queryReferentsParams?: any) => {
            const [columns, response, delegates]: any = await Promise.all([
                getUserReferentsGrid(setOpenConfimModalReferents, t),
                getReferentsDataRequest.doFetch(true, queryReferentsParams),
                getDelegatesDataRequest.doFetch(true),
            ]);
            const { data } = delegates;
            const { currentPage, totalRows } = response.data;

            setDelegates(data);
            setListReferents({
                ...listReferents,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: queryReferentsParams?.page,
                pageSize: queryReferentsParams?.pageSize,
            });
        },
        [queryReferentsParams, t]
    );

    useEffect(() => {
        fetchingUserReferantsData(queryReferentsParams);
    }, [queryReferentsParams, fetchingUserReferantsData]);

    return {
        queryReferentsParams,
        setQueryReferentsParams,
        listReferents,
        setListReferents,
        loadingReferents: getReferentsDataRequest.loading,
        delegates,
        addDelegates,
        openConfirmModalReferents,
        setOpenConfimModalReferents,
        deleteDelegates,
        ConfirmModalComponent,
    };
}
