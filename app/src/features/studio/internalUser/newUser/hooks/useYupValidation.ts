import { useForm } from "react-hook-form";
import { useEffect } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useTranslation } from "@1f/react-sdk";


declare module "yup" {
    interface StringSchema {
        fiscalCode(messageOrTranslator: string | Function): StringSchema<string>;
        vat(messageOrTranslator: string | Function): StringSchema<string>;
    }
}

function luhn(text: any) {
    var length = text.length;
    var sum = 0;
    var weight = 2;

    for (var i = length - 2; i >= 0; i--) {
        var digit = weight * text[i];
        sum += Math.floor(digit / 10) + (digit % 10);
        weight = (weight % 2) + 1;
    }

    return (10 - (sum % 10)) % 10 == text[length - 1];
}

// Define custom validators
yup.addMethod(yup.string, "fiscalCode", function (messageOrTranslator: any) {
    const isTranslator = typeof messageOrTranslator === 'function';
    const message = isTranslator
        ? messageOrTranslator("Codice fiscale in formato non corretto")
        : messageOrTranslator;

    return this.test("fiscalCode", message, function (code: any) {
        if (!code) return true;

        code = code.toUpperCase();
        if (/^[A-Z0-9]{16}$/.test(code)) {
            var set1 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ",
                set2 = "ABCDEFGHIJABCDEFGHIJKLMNOPQRSTUVWXYZ",
                setpari = "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
                setdisp = "BAKPLCQDREVOSFTGUHMINJWZYX",
                s = setdisp.indexOf(set2.charAt(set1.indexOf(code.charAt(14))));
            for (var i = 0; i <= 13; i += 2) {
                s +=
                    setpari.indexOf(
                        set2.charAt(set1.indexOf(code.charAt(i + 1)))
                    ) +
                    setdisp.indexOf(set2.charAt(set1.indexOf(code.charAt(i))));
            }
            return s % 26 === code.charCodeAt(15) - "A".charCodeAt(0);
        }
        return false;
    });
});

yup.addMethod(yup.string, "vat", function (messageOrTranslator: any) {
    const isTranslator = typeof messageOrTranslator === 'function';
    const message = isTranslator
        ? messageOrTranslator("Partita IVA in formato non corretto")
        : messageOrTranslator;

    return this.test("vat", message, function (val: any) {
        if (!val) return true;

        var result = false;
        var countryCode = val.substr(0, 2).toUpperCase();
        var vat;

        if (/[A-Z]/.test(countryCode)) {
            vat = val.substr(2);
        } else {
            countryCode = "IT";
            vat = val;
        }

        switch (countryCode) {
            case "DE":
                result = /^\d{9}$/.test(vat);
                break;
            case "IT":
                result = /^\d{11}$/.test(vat) && luhn(vat);
                break;
            case "FR":
                var sirenCode = vat.substr(2);
                var checkValidationKey =
                    (12 + 3 * (sirenCode % 97)) % 97 === vat.substr(0, 2);
                result =
                    /^\d{11}$/.test(vat) &&
                    luhn(sirenCode) &&
                    checkValidationKey;
                break;
            case "SM":
                result = /^\d{5}$/.test(vat);
                break;
        }

        return result;
    });
});

//yup schema
const schemaFirstStep = (isEdit: boolean = false, t: any) =>
    yup.object().shape({
        nomeutente: yup.string().required("Nome completo obbligatorio"),
        nomepersonale: yup.string().required("Nome obbligatorio"),
        Email: yup
            .string()
            .email(t("Email NON conforme"))
            .required(t("Email obbligatoria")),
        cognomepersonale: yup.string().required(t("Cognome obbligatorio")),
        nome: yup
            .string()
            .required(t("Nome utente obbligatorio"))
            .min(5, "Lunghezza minima 5 Caratteri"),
        password: isEdit
            ? yup
                  .string()
                  .notRequired()
                  .test(
                      "is-strong-password",
                      t(`Requisiti:
                      - almeno 8 caratteri
                      - almeno un carattere minuscolo
                      - almeno un carattere maiuscolo
                      - almeno un numero
                      - almeno un carattere speciale (!@#$%^&*)`),
                      function (value) {
                          if (!value) {
                              return true; // No value, no validation
                          }
                          return /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/.test(
                              value
                          );
                      }
                  )
            : yup
                  .string()
                  .required(t("Password obbligitaro"))
                  .matches(
                      /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/,
                      t(`Requisiti:
                      - almeno 8 caratteri
                      - almeno un carattere minuscolo
                      - almeno un carattere maiuscolo
                      - almeno un numero
                      - almeno un carattere speciale (!@#$%^&*)`)
                  ), // When creating
        passwordConfirm: isEdit
            ? yup.string().notRequired() // When editing
            : yup
                  .string()
                  .required(t("Conferma NON corrispondente"))
                  .oneOf([yup.ref("password")], t("Conferma NON corrispondente")), // When creating
        altroIban: yup
            .string()
            .required("IBAN obbligitaro")
            .matches(
                /^[a-zA-Z]{2}[0-9]{2}[a-zA-Z0-9]{23,30}$/,
                t("Inserire un IBAN valido")
            ),
        altroBic: yup
            .string()
            .matches(
                /^[A-Z]{6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3}){0,1}$/,
                t("Inserire un BIC valido")
            ),
    });

const schemaSecondStep = (t: any) => yup.object().shape({
    studioNome: yup.string().required(t("Nome studio obbligatorio")),
    studioCodicefiscale: yup.string().fiscalCode(t).required(t("Codice fiscale obbligatorio")),
    studioPartitaiva: yup.string().vat(t).required(t("Partita IVA obbligatoria")),
    studioIndirizzo: yup.string().required(t("Indirizzo obbligatorio")),
    studioCitta: yup.string().required(t("Città obbligatoria")),
    studioTelefono: yup.string().required(t("Telefono obbligatorio")),
    studioCap: yup
        .string()
        .matches(/^\d{5}$/, t("CAP non valido"))
        .required(t("CAP obbligatorio")),
    studioEmail: yup.string().email(t("Email NON conforme")),
    studioProvincia: yup
        .string()
        .matches(
            /^[A-Z]{2}$/,
            t("Provincia non valida")
        )
        .required(t("Provincia non valida")),
});

function mergeYupSchemas(...schemas: any) {
    const [first, ...rest] = schemas;
    const merged = rest.reduce(
      (mergedSchemas: any, schema: any) => mergedSchemas.concat(schema),
      first
    );
    return merged;
}

const useYupValidation = (
    isFirstStepCompleted?: any,
    initialValues?: any,
    isEdit: boolean = false
) => {
    const { t } = useTranslation();

    const schema = isEdit
    ? mergeYupSchemas(schemaFirstStep(isEdit, t), schemaSecondStep(t))
    : isFirstStepCompleted
    ? schemaSecondStep(t)
    : schemaFirstStep(isEdit, t);
    // const schema = isFirstStepCompleted
    //     ? schemaSecondStep
    //     : schemaFirstStep(isEdit);

    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        reset,
    } = useForm({
        resolver: yupResolver(schema as yup.ObjectSchema<any>),
        defaultValues: initialValues,
    });

    useEffect(() => {
        reset(initialValues);
    }, [initialValues, reset]);

    return {
        register,
        handleSubmit,
        errors,
        setValue,
    };
};

export default useYupValidation;
