import { useState } from "react";
import ConfirmModal from "../../../../../custom-components/ConfirmModal";

const useConfirmModal = () => {
    const [modalData, setModalData] = useState<any>({
        open: false,
        confirmText: "",
        title: "",
        declineText: "",
        agreeText: "",
        handleAgree: () => {},
        handleDecline: () => {},
    });

    const openModal = (
        title?: string,
        confirmText?: string,
        declineText?: string,
        agreeText?: string,
        onAgree?: () => void,
        onDecline?: () => void
    ) => {
        setModalData({
            open: true,
            confirmText,
            title,
            declineText,
            agreeText,
            handleAgree: onAgree,
            handleDecline: onDecline,
        });
    };

    const closeModal = () => {
        setModalData({
            open: false,
            confirmText: "",
            declineText: "",
            agreeText: "",
            handleAgree: () => {},
            handleDecline: () => {},
        });
    };

    const ConfirmModalComponent = () => (
        <ConfirmModal
            open={modalData.open}
            title={modalData.title}
            confirmText={modalData.confirmText}
            decline={modalData.declineText}
            agree={modalData.agreeText}
            handleDecline={() => closeModal()}
            handleAgree={() => {
                modalData.handleAgree();
                closeModal();
            }}
        />
    );

    return { openModal, ConfirmModalComponent };
};

export default useConfirmModal;
