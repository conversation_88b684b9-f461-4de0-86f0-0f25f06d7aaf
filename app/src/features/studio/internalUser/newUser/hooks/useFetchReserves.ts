import { useCallback, useEffect, useState } from "react";
import useGetCustom from "../../../../../hooks/useGetCustom";
import usePostCustom from "../../../../../hooks/usePostCustom";
import { IList } from "../../../../../interfaces/general.interfaces";
import { getUserReservesGrid } from "../../../../../utilities/userReserve/gridColumn";

const defaultQueryParmas = {
    uniqueid: "",
    page: 0,
    pageSize: 10,
    sortColumn: "codicearchivio",
    sortOrder: "asc",
};

export default function useFetchReserves(uniqueid: string = "", t: any) {
    const getReservesDataRequest = useGetCustom(
        "usersreserved/list?noTemplateVars=true"
    );
    const deleteUserReservesRequest = usePostCustom(
        "usersreserved/deletereservation"
    );
    const [queryReservesParams, setQueryReservesParams] = useState({
        ...defaultQueryParmas,
        uniqueid,
    });

    const [openConfirmModalReserves, setOpenConfimModalReserves] = useState<{
        state: boolean;
        id: string;
    }>({ state: false, id: "" });

    const [listReserves, setListReserves] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });

    const deleteReserves = async (id: string) => {
        const params = {
            uniqueid,
            fileId: id,
        };

        const response = await deleteUserReservesRequest.doFetch(true, params);
        if (response) {
            fetchingUserReserveData(queryReservesParams);
            setOpenConfimModalReserves({ state: false, id: "" });
        }
    };

    const fetchingUserReserveData = useCallback(
        async (queryParams?: any) => {
            const [columns, response]: any = await Promise.all([
                getUserReservesGrid(setOpenConfimModalReserves, t),
                getReservesDataRequest.doFetch(true, queryParams),
            ]);

            const { currentPage, totalRows } = response.data;

            setListReserves({
                ...listReserves,
                rows: currentPage,
                columns,
                totalRows: parseInt(totalRows),
                page: queryParams?.page,
                pageSize: queryParams?.pageSize,
            });
        },
        [t]
    );

    useEffect(() => {
        fetchingUserReserveData(queryReservesParams);
    }, [queryReservesParams, fetchingUserReserveData]);

    return {
        queryReservesParams,
        setQueryReservesParams,
        listReserves,
        setListReserves,
        loadingReserves: getReservesDataRequest.loading,
        openConfirmModalReserves: openConfirmModalReserves,
        setOpenConfimModalReserves: setOpenConfimModalReserves,
        deleteReserves: deleteReserves,
    };
}
