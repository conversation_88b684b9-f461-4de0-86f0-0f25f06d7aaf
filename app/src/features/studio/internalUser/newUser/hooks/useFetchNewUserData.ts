import usePostCustom from "../../../../../hooks/usePostCustom";
import useGetCustom from "../../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

export default function useFetchNewUserData(uniqueId: string = "") {
  const getNewUserAdditionalData = useGetCustom("users/internal");
  const internalApproveNewUserRequest = usePostCustom(
    "users/approvenewuser?noTemplateVars=true"
  );
  const getRowDataRequest = usePostCustom(
    "users/getrowdata?noTemplateVars=true"
  );
  const [approveNewUser, setApproveNewUser] = useState<null | boolean>(null);
  const [getRowData, setGetRowData] = useState<any>([]);
  const [newUserData, setNewUserData] = useState<any>([]);

  useEffect(() => {
    async function fetchData() {
      const { data }: any = await internalApproveNewUserRequest.doFetch(true);
      setApproveNewUser(data);
        const newUserData: any = await getNewUserAdditionalData.doFetch(true);
        setNewUserData(newUserData.data);
        const response: any = await getRowDataRequest.doFetch(true, {
          uniqueId,
          externalMode: "0",
        });
        setGetRowData(response.data);
    }

    fetchData();
  }, [uniqueId]);

  return {
    approveNewUser,
    loading:
      internalApproveNewUserRequest.loading ||
      getNewUserAdditionalData.loading ||
      getRowDataRequest.loading,
    getRowData,
    newUserData,
  };
}
