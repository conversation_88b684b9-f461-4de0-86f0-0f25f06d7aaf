import usePostCustom from "../../../../../hooks/usePostCustom";
import useToastNotification from "./useToastNotification";
import useConfirmModal from "./useConfimModal";
import { useNavigate } from "react-router-dom";

export default function useDeleteUser(userName: string, t: any) {
    const navigate = useNavigate();
    const checkFoldersRequest = usePostCustom(
        "users/checkFolders?noTemplateVars=true"
    );
    const checkReferentiRequest = usePostCustom(
        "users/checkReferenti?noTemplateVars=true"
    );
    const deleteUserRequest = usePostCustom("users/delete?noTemplateVars=true");
    const { showNotification, ShowNotificationUI } = useToastNotification();
    const { openModal, ConfirmModalComponent } = useConfirmModal();

    const checkReferenti = async (uniqueid: string): Promise<boolean> => {
        const { data }: any = await checkReferentiRequest.doFetch(true, {
            uniqueid: uniqueid,
        });
        return data.response;
    };

    const checkFolders = async (uniqueid: string): Promise<number> => {
        const { data }: any = await checkFoldersRequest.doFetch(true, {
            uniqueid: uniqueid,
        });
        return data;
    };

    const checkUserIfCanBeDeleted = async (uniqueid: any) => {
        const referent = await checkReferenti(uniqueid);
        if (!referent) {
            showNotification(
                t(`Attenzione, l'utente ${userName} E' indicato come unico referente di almeno un altro utente.`)
            );
        } else {
            const folders = await checkFolders(uniqueid);
            if (folders === 0) {
                showNotification(
                    t(`Attenzione, l'utente "${userName}" è indicato come unico possessore dei permessi di visualizzazione di cartelle di documenti.\nCancellare questo utente renderà le suddette cartelle visibili a tutti gli utenti della pratica.`)
                );

                openModal(
                    t("Elimina utente"),
                    t(`Eliminare definitivamente ${userName}`),
                    t("Annulla"),
                    t("Eliminare"),
                    () => deleteUser(uniqueid),
                    () => {}
                );
            } else {
                showNotification(
                    t(`Non è possibile procedere con l'eliminazione dell'utente selezionato. Si prega di verificare la presenza di documenti o impegni associati all'utente indicato.`)
                );
            }
        }
    };

    const deleteUser = async (uniqueid: any) => {
        const data: any = await deleteUserRequest.doFetch(true, {
            uniqueid: uniqueid,
        });

        if (data.activepage) {
            navigate("/users/internal");
        } else {
            showNotification(
                t(`Non è possibile procedere con l'eliminazione dell'utente selezionato. Si prega di verificare la presenza di documenti o impegni associati all'utente indicato.`)
            );
        }
    };

    return {
        checkUserIfCanBeDeleted,
        deleteUser,
        showNotification,
        ShowNotificationUI,
        ConfirmModalComponent,
    };
}
