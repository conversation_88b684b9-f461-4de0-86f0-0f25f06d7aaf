import { INewUserData } from "../../interfaces/newUserData.interface";

export const DEFAULT_NEWUSER_DATA: INewUserData = {
    uniqueid: "",
    nomeutente: "",
    nomepersonale: "",
    cognomepersonale: "",
    sigla: "",
    natoil: "",
    one_drive_email: "",
    role: "",
    natoa: "",
    Email: "",
    nome: "",
    password: "",
    passwordConfirm: "",
    qualificautente: "",
    ordine: "",
    letterhead_id: "",
    codiceavvocato: "",
    attivo: "1",
    consultant: "",
    studioUniqueid: "",
    userImport: "-1",
    studioNome: "",
    studioCodicefiscale: "",
    studioPartitaiva: "",
    studioIndirizzo: "",
    studioCap: "",
    studioCitta: "",
    studioProvincia: "",
    studioTelefono: "",
    studioFax: "",
    studioMobile: "",
    studioEmail: "",
    studioWeb: "",
    studioPolizza: "",
    pec_address_for_pa_invoice: "",
    pa_tipo_ritenuta: "",
    pa_id_codice: "",
    pa_id_codice_da: "",
    pa_codice_fiscale_da: "",
    externalcode: "",
    external_can_upload: "",
    sendCredentials: false,
    costo_risorsa: "",
    max_hourly_rate: "",
    min_hourly_rate: "",
    enable_timesheet: "",
    dailyWorkload: "",
    seniority: "",
    default_page_size: "",
    voispeed_ext: "",
    default_calendar: "0",
    extended_days_before: "",
    studioNascondiCFfatturazione: "",
    extended_days_before_commitments: "",
    is_assignee_pa: "",
    identificatore: "-1",
    nascondi_dati_pagamento_xml: "",
    socio: false,
    vede_soci: false,
    notify_new_proceed: "",
    gestisce_riserva: false,
    api_personal_token: "",
    previewDeadlineValues: [],
    previewDeadlineArchiveValues: [],
    previewHearingsValues: [],
    previewPoliswebValues: [],
    previewnomeutenteValues: [],
    altroDefaultStudio: "1",
    altroIban: "",
    altroDesc: "",
    codice_destinatario_af: "",
    altroAbi: "",
    altroCab: "",
    altroBic: "",
    altroRegimeFiscale: "",
    altroTipoCassa: "",
    bollo: "",
    altroPercCassa: "",
    addCassa: false,
    altroTipoCassa2: "",
    altroPercCassa2: "",
    tipologia: "",
    tipologiaFE: "",
    passwordcct: "",
    usernamecct: "",
    campi_agenda: {},
    studioRiservaFatturazione: false,
};
