export const simpleCommitmentValues = [
  { value: "Intestatari", name: "semplice_intestatari" },
  { value: "Annotazioni", name: "semplice_annotazioni" },
  { value: "<PERSON><PERSON><PERSON>", name: "semplice_oggetto" },
  { value: "Durata", name: "semplice_durata" },
];

export const commitmentLinkedToPractice = [
  { value: "R.G", name: "pratica_rg" },
  { value: "Stato Pratica", name: "pratica_stato" },
  { value: "Avvocato", name: "pratica_avvocato" },
  { value: "Se<PERSON>", name: "pratica_sezione" },
  { value: "PM", name: "pratica_pm" },
  { value: "R.G.N.R.", name: "pratica_rgnr" },
  { value: "Clienti", name: "pratica_clienti" },
  { value: "Referente", name: "pratica_referente" },
  { value: "<PERSON>rità", name: "pratica_autorita" },
  { value: "<PERSON><PERSON><PERSON>", name: "pratica_istruttore" },
  { value: "Durata", name: "pratica_durata" },
  { value: "Codice", name: "pratica_codice" },
  { value: "Controparti", name: "pratica_controparti" },
  { value: "Ultima udienza", name: "pratica_ultimaUdienza" },
  { value: "Città", name: "pratica_citta" },
  { value: "Intestatari", name: "pratica_intestatari" },
  { value: "Descrizione ", name: "pratica_descrizione" },
  { value: "Oggetto ", name: "pratica_oggetto" },
];

export const poliswebEvent = [
  { value: "R.G", name: "polisweb_rg" },
  { value: "R.G.N.R.", name: "polisweb_rgnr" },
  { value: "Codice", name: "polisweb_codice" },
  { value: "Clienti", name: "polisweb_clienti" },
  { value: "Controparti", name: "polisweb_controparti" },
  { value: "Avvocato", name: "polisweb_avvocato" },
  { value: "Referente", name: "polisweb_referente" },
  { value: "Sezione", name: "polisweb_sezione" },
  { value: "Autorità", name: "polisweb_autorita" },
  { value: "Città", name: "polisweb_citta" },
  { value: "PM", name: "polisweb_pubblicoministero" },
  { value: "Giudice", name: "polisweb_giudice" },
  { value: "Oggetto", name: "polisweb_oggetto" },
  { value: "Descrizione", name: "polisweb_descrizione" },
];

export const courtHearing = [
  { value: "R.G", name: "udienza_rg" },
  { value: "R.G.N.R.", name: "udienza_rgnr" },
  { value: "Codice", name: "udienza_codice" },
  { value: "Descrizione", name: "udienza_descrizione" },
  { value: "Stato Pratica", name: "udienza_stato" },
  { value: "Clienti", name: "udienza_clienti" },
  { value: "Controparti", name: "udienza_controparti" },
  { value: "Avvocato", name: "udienza_avvocato" },
  { value: "Referente", name: "udienza_referente" },
  { value: "Sezione", name: "udienza_sezione" },
  { value: "Autorità", name: "udienza_autorita" },
  { value: "Città", name: "udienza_citta" },
  { value: "PM", name: "udienza_pm" },
  { value: "Giudice", name: "udienza_istruttore" },
  { value: "Oggetto", name: "udienza_oggetto" },
  { value: "Durata", name: "udienza_durata" },
];


export const tipologiaData = [
  { name: "Preavviso di Parcella", value: "2" },
  { name: "Fattura", value: "0" },
  { name: "Fattura Elettronica", value: "6" },
  { name: "Nota di Credito", value: "1" },
  { name: "Nota di Debito Elettronica", value: "10" },
  { name: "Nota di Credito Elettronica", value: "7" },
  { name: "Fattura d'Acquisto", value: "5" },
];

export const tipologiaFEData = [
  { name: "Fattura TD01", value: "1" },
  { name: "Parcella TD06", value: "6" },
];