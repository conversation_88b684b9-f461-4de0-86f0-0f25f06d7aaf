import VaporPage from "@vapor/react-custom/VaporPage";
import Spinner from "../../../../custom-components/Spinner";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";

import { IReservesProps } from "../interfaces/newUserData.interface";

export default function Reserves(props: IReservesProps) {
    const { list, loading, queryParams, setQueryParams } = props;

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="reserves"
                setQuery={setQueryParams}
                columns={list.columns}
                data={list.rows || []}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                query={queryParams}
                onPageChangeCallback={() => ""}
            />
        );
    };
    return <VaporPage.Section>{renderDataTable()}</VaporPage.Section>;
}
