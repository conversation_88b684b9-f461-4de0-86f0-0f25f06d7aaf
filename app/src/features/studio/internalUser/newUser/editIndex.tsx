import VaporPage from "@vapor/react-custom/VaporPage";
import { useEffect, useState } from "react";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { Tabs, Tab } from "@vapor/react-extended";
import { Box } from "@vapor/react-material";
import { CustomTabPanel, a11yProps } from "../../../../helpers/customTabPanel";
import InternalUserCard from "./internalUserCard";
import CCT from "./cct";
import Billing from "./billing";
import Agenda from "./agenda";
import useFetchNewUserData from "./hooks/useFetchNewUserData";
import Spinner from "../../../../custom-components/Spinner";
import useYupValidation from "./hooks/useYupValidation";
import { DEFAULT_NEWUSER_DATA } from "./data/initialData";
import useSubmitData from "./hooks/useSubmitData";
import StudioData from "./studioData";
import Other from "./other";
import { useParams } from "react-router-dom";
import useFetchReserves from "./hooks/useFetchReserves";
import useFetchReferents from "./hooks/useFetchReferents";
import Reserves from "./reserves";
import Referents from "./referents";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useNavigate } from "react-router-dom";
import useDeleteUser from "./hooks/useDeleteUser";
import ErrorIcon from "@mui/icons-material/Error";
import ToastNotification from "../../../../custom-components/ToastNotification";
import TabShowErrorHandler from "./helpers/tabShowErrorHandler";

export default function EditUserIndex() {
    const { uniqueId } = useParams();
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [value, setValue] = useState<number>(0);
    const [newUserParamsData, setNewUserParamsData] =
        useState(DEFAULT_NEWUSER_DATA);
    const { loading, getRowData, newUserData } = useFetchNewUserData(uniqueId);
    const { saveUser, isFirstStepCompleted } = useSubmitData();
    const {
        checkUserIfCanBeDeleted,
        ConfirmModalComponent,
        ShowNotificationUI,
    } = useDeleteUser(newUserParamsData.nomeutente, t);
    const { register, handleSubmit, errors } = useYupValidation(
        isFirstStepCompleted,
        newUserParamsData,
        uniqueId !== undefined ? true : false //difference between create and edit
    );
    const {
        HasTabErrors,
        showTabErrorNotification,
        setShowTabErrorNotificaion,
        tabTextError,
    } = TabShowErrorHandler(errors, t);
    const {
        listReserves,
        loadingReserves,
        queryReservesParams,
        setQueryReservesParams,
        openConfirmModalReserves,
        setOpenConfimModalReserves,
        deleteReserves,
    } = useFetchReserves(uniqueId, t);
    const {
        delegates,
        listReferents,
        loadingReferents,
        queryReferentsParams,
        setQueryReferentsParams,
        addDelegates,
        deleteDelegates,
        openConfirmModalReferents,
        setOpenConfimModalReferents,
    } = useFetchReferents(uniqueId, t);

    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    useEffect(() => {
        const { form, paymentsData } = getRowData;
        setNewUserParamsData({
            ...newUserParamsData,
            ...form,
            bankAccountDetails: paymentsData,
        });
    }, [getRowData.form]);

    const onSubmit = async () => {
        const response: any = await saveUser(newUserParamsData);
        if (response.status === 200) {
            navigate("/users/internal");
        }
    };

    const CustomTabLabel = ({
        label,
        hasError,
    }: {
        label: string;
        hasError: boolean;
    }) => (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
            }}
        >
            <span>{label}</span>
            {hasError && <ErrorIcon color="error" style={{ marginLeft: 8 }} />}
        </div>
    );

    return (
        <>
            {" "}
            {loading ? (
                <Spinner />
            ) : (
                <VaporPage>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <PageTitle
                            title={t("Nuovo Utente")}
                            pathToPrevPage="/users/internal"
                            actionButtons={[
                                {
                                    color: "error",
                                    label: t("Elemina"),
                                    onclick: () =>
                                        checkUserIfCanBeDeleted(uniqueId),
                                },
                                {
                                    label: t("Conferma"),
                                    onclick: () => "",
                                    type: "submit",
                                    variant: "contained",
                                },
                            ]}
                        />
                        <VaporPage.Section>
                            <Box>
                                <Tabs
                                    onChange={handleChange}
                                    value={value}
                                    size="extraSmall"
                                    variant="standard"
                                >
                                    <Tab
                                        label={
                                            <CustomTabLabel
                                                label="Scheda utente Interno"
                                                hasError={HasTabErrors(0)}
                                            />
                                        }
                                        {...a11yProps(0)}
                                    />
                                    <Tab
                                        label={
                                            <CustomTabLabel
                                                label={t("Dati studio")}
                                                hasError={HasTabErrors(1)}
                                            />
                                        }
                                        {...a11yProps(1)}
                                    />
                                    <Tab
                                        label={
                                            <CustomTabLabel
                                                label={t("Fatturazione")}
                                                hasError={HasTabErrors(2)}
                                            />
                                        }
                                        {...a11yProps(2)}
                                    />
                                    <Tab label={t("Altro")} {...a11yProps(3)} />
                                    <Tab
                                        label={t("Riserve")}
                                        {...a11yProps(4)}
                                    />
                                    <Tab label={t("CCT")} {...a11yProps(5)} />
                                    <Tab
                                        label={t("Agenda")}
                                        {...a11yProps(6)}
                                    />
                                    <Tab
                                        label={t("Delegati fattura")}
                                        {...a11yProps(7)}
                                    />
                                </Tabs>
                            </Box>
                            <CustomTabPanel value={value} index={0}>
                                <InternalUserCard
                                    t={t}
                                    newUserData={newUserData}
                                    register={register}
                                    errors={errors}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                    errorMessage={isFirstStepCompleted} //passing the data if its error on 'email', 'nome', 'sigla'
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={1}>
                                <StudioData
                                    t={t}
                                    newUserData={newUserData}
                                    register={register}
                                    errors={errors}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={2}>
                                <Billing
                                    t={t}
                                    newUserData={newUserData}
                                    register={register}
                                    errors={errors}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={3}>
                                <Other
                                    t={t}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                />{" "}
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={4}>
                                <Reserves
                                    list={listReserves}
                                    loading={loadingReserves}
                                    queryParams={queryReservesParams}
                                    setQueryParams={setQueryReservesParams}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={5}>
                                <CCT
                                    t={t}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={6}>
                                <Agenda
                                    t={t}
                                    newUserData={newUserData}
                                    newUserParamsData={newUserParamsData}
                                    setNewUserParamsData={setNewUserParamsData}
                                />
                            </CustomTabPanel>
                            <CustomTabPanel value={value} index={7}>
                                <Referents
                                    t={t}
                                    delegates={delegates}
                                    list={listReferents}
                                    loading={loadingReferents}
                                    queryParams={queryReferentsParams}
                                    setQueryParams={setQueryReferentsParams}
                                    addDelegates={addDelegates}
                                />
                            </CustomTabPanel>
                        </VaporPage.Section>
                    </form>
                </VaporPage>
            )}
            <ConfirmModal
                open={openConfirmModalReserves.state}
                title={t("Attenzione!")}
                confirmText={t(
                    `Eliminare l'accesso riservato alla pratica per l'utente ${newUserParamsData.nomeutente}?`
                )}
                decline={t("Annulla")}
                agree={t("Conferma")}
                handleAgree={() => deleteReserves(openConfirmModalReserves.id)}
                handleDecline={() =>
                    setOpenConfimModalReserves({
                        state: false,
                        id: "",
                    })
                }
            />
            <ConfirmModal
                open={openConfirmModalReferents.state}
                title={t("Attenzione!")}
                confirmText={t(
                    `Rimuovere il delegato dall' utente ${newUserParamsData.nomeutente}?`
                )}
                decline={t("Annulla")}
                agree={t("Conferma")}
                handleAgree={() =>
                    deleteDelegates(openConfirmModalReferents.id)
                }
                handleDecline={() =>
                    setOpenConfimModalReferents({
                        state: false,
                        id: "",
                    })
                }
            />
            <ShowNotificationUI />
            <ConfirmModalComponent />
            <ToastNotification
                showNotification={showTabErrorNotification}
                setShowNotification={setShowTabErrorNotificaion}
                severity="error"
                text={tabTextError}
            />
        </>
    );
}
