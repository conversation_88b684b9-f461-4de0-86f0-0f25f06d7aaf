import { Box, FormControlLabel, Checkbox } from "@vapor/react-material";
import { IOtherProps } from "../interfaces/newUserData.interface";

export default function Other(props: IOtherProps) {
    const { t, newUserParamsData, setNewUserParamsData } = props;

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: checked });
    };

    return (
        <Box
            autoComplete="off"
            component="form"
            sx={{
                display: "flex",
                alignItems: "center", // Center vertically
                flexDirection: "column",
                "& .MuiTextField-root": {
                    m: 1,
                    width: 500,
                },
                "& .MuiFormControl-root": {
                    // Set width for FormControl components
                    m: 1,
                    width: 500,
                },
            }}
        >
            <FormControlLabel
                value="left"
                control={
                    <Checkbox
                        name="socio"
                        checked={newUserParamsData?.socio}
                        onChange={handleCheckboxChanges}
                    />
                }
                label={t("Socio")}
                labelPlacement="start"
            />
            <FormControlLabel
                value="left"
                control={
                    <Checkbox
                        name="vede_soci"
                        checked={newUserParamsData?.vede_soci}
                        onChange={handleCheckboxChanges}
                    />
                }
                label={t("Vede soci")}
                labelPlacement="start"
            />
        </Box>
    );
}
