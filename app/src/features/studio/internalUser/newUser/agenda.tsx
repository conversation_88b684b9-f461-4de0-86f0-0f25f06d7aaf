import { Box, FormControl, InputLabel, Select, MenuItem, TextField, Checkbox } from "@vapor/react-material";
import { Tabs, Tab, Avatar } from "@vapor/react-extended";

import { useState, useEffect } from "react";
import CircleIcon from "@mui/icons-material/Circle";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { CustomTabPanel, a11yProps } from "../../../../helpers/customTabPanel";
import { simpleCommitmentValues, commitmentLinkedToPractice, poliswebEvent, courtHearing } from "./data/selectInputData";
import { extractSelectedValues } from "./helpers/extractSelectedValuesAgenda";
import { generatingPreviewSelectData } from "./helpers/generatingPreviewSelectData";
import { updatingNewParamsDataFromCampiAgenda } from "./helpers/updatingNewParamsDataFromCampiAgenda";
import { handleItemAddition, handleItemRemoval } from "./helpers/agendaCheckboxUtils";
import { IAgendaProps } from "../interfaces/newUserData.interface";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

export default function Agenda(props: IAgendaProps) {
    const { t, newUserData, newUserParamsData, setNewUserParamsData } = props;
    const [value, setValue] = useState<number>(0);

    useEffect(() => {
        const transformedData = updatingNewParamsDataFromCampiAgenda(newUserParamsData);
        setNewUserParamsData((prevData: any) => ({
            ...prevData,
            ...transformedData
        }));
    }, []);

    const handleAutocompleteChange = (_: React.ChangeEvent<{}>, value: any, reason: any, detail: any) => {
        const paramsDataUpdated = { ...newUserParamsData };
        value.forEach((item: any) => handleItemAddition(paramsDataUpdated, item));
        if (reason === "removeOption") {
            handleItemRemoval(paramsDataUpdated, detail.option);
        }
        setNewUserParamsData(paramsDataUpdated);
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: value });
    };

    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const selectedValues = extractSelectedValues(newUserParamsData.campi_agenda, simpleCommitmentValues.concat(commitmentLinkedToPractice, poliswebEvent, courtHearing));

    const renderAutocomplete = (label: string, options: any, valueKey: string) => (
        <CustomAutocomplete
            disableCloseOnSelect
            getOptionLabel={(option: any) => option.value}
            multiple
            value={selectedValues[valueKey] || []}
            options={options}
            onChange={handleAutocompleteChange}
            renderInput={(params: any) => <TextField {...params} label={label} />}
            renderOption={(props: any, option: any, { selected }: any) => (
                <div {...props}>
                    <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                    <span dangerouslySetInnerHTML={{ __html: option.value }} />
                </div>
            )}
            sx={{ width: 500, mr: 2 }}
        />
    );

    // const getLabel = (option: any) => {
    //     return (
    //         <>
    //             <div style={{ display: "flex", alignItems: "center" }}>
    //                 <span>{option.value}</span>
    //                 <CircleIcon color="error" fontSize="small" />
    //             </div>
    //         </>
    //     );
    // };
    const renderPreviewAutocomplete = (label: string, options: any, valueKey: string, bgColor: string) => (
        <div style={{ display: "flex", alignItems: "center" }}>
            <CustomAutocomplete
                getOptionLabel={(option: any) => option.value}
                multiple
                value={selectedValues?.preview?.[valueKey] || []}
                isOptionEqualToValue={(option: any, value: any) => option.name === value.name}
                options={generatingPreviewSelectData(options)}
                onChange={handleAutocompleteChange}
                renderInput={(params: any) => <TextField {...params} label={label} />}
                renderOption={(props: any, option: any, { selected }: any) => (
                    <li {...props}>
                        <div style={{ display: "flex", alignItems: "center" }}>
                            <Checkbox icon={icon} checkedIcon={checkedIcon} style={{ marginRight: 8 }} checked={selected} />
                            <span>{option.value}</span>
                            <CircleIcon color="error" fontSize="small" />
                        </div>
                    </li>
                )}
                sx={{
                    width: 500,
                    ml: 5,
                    "& .MuiChip-root.MuiAutocomplete-tag": {
                        backgroundColor: bgColor
                    }
                }}
            />
            <Avatar
                variant="rounded"
                bgColor={bgColor}
                color="white"
                sx={{
                    mt: 3,
                    ml: 2,
                    "& .MuiSvgIcon-root": {
                        visibility: "hidden"
                    }
                }}
            />
        </div>
    );

    return (
        <Box
            autoComplete="off"
            component="form"
            sx={{
                display: "flex",
                alignItems: "center",
                flexDirection: "column",
                "& .MuiTextField-root, & .MuiFormControl-root": {
                    m: 1,
                    width: 500
                }
            }}
        >
            <Tabs onChange={handleChange} value={value} size="extraSmall" variant="standard">
                <Tab label="Tooltip" {...a11yProps(0)} />
                <Tab label="Anteprima" {...a11yProps(1)} />
            </Tabs>

            <FormControl sx={{ mb: "20px !important" }}>
                <InputLabel>{t("Apri Agenda di")}</InputLabel>
                <Select name="default_calendar" onChange={handleInputChanges} value={newUserParamsData?.default_calendar}>
                    <MenuItem value="0">-</MenuItem>
                    <MenuItem value="-1">{t("Tutti gli intestatari")}</MenuItem>
                    {newUserData?.people?.map((person: any, index: number) => (
                        <MenuItem key={index} value={person.id}>
                            {person.nome}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            <CustomTabPanel value={value} index={0}>
                {renderAutocomplete(t("Impegno semplice"), simpleCommitmentValues, "semplice")}
                {renderAutocomplete(t("Impegno legato a una pratica"), commitmentLinkedToPractice, "pratica")}
                {renderAutocomplete(t("Eventi Polisweb"), poliswebEvent, "polisweb")}
                {renderAutocomplete(t("Udienza"), courtHearing, "udienza")}
            </CustomTabPanel>

            <CustomTabPanel value={value} index={1}>
                {renderPreviewAutocomplete(t("Impegno semplice"), simpleCommitmentValues, "semplice", "#ff00ff")}
                {renderPreviewAutocomplete(t("Impegno legato a una pratica"), commitmentLinkedToPractice, "pratica", "#DFF0D8")}
                {renderPreviewAutocomplete(t("Eventi polisweb"), poliswebEvent, "polisweb", "#d5e439")}
                {renderPreviewAutocomplete(t("Udienza"), courtHearing, "udienza", "#D9EDF7")}
            </CustomTabPanel>
        </Box>
    );
}
