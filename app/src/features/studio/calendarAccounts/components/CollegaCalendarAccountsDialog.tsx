import { useState, Fragment, useEffect } from "react";
import {
    <PERSON><PERSON>,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    MenuItem,
    Select,
    InputLabel,
    FormControl,
    Grid,
    Typography,
    Tooltip,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";

import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";

import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";

const CalendarAccountsDialog = ({ label, calendarNetlex, setLoading }: any) => {
    const [open, setOpen] = useState(false);
    const [calendarOptions, setCalendarOptions] = useState<any[]>([]);

    const { t } = useTranslation();
    const location = useLocation();
    const navigate = useNavigate();

    const query = new URLSearchParams(location.search);

    const [selectValue, setSelectValue] = useState({
        tipo_account: query.get("clientType") || "1",
        filtroCalendario: query.get("calendarFilter") || "1",
        calendarId: null,
        calendarName: "",
    });

    const generateUrlRequest = usePostCustom(
        "calendaraccounts/generate-url?noTemplateVars=true"
    );
    const saveRequest = usePostCustom("calendaraccounts/save");
    const calendarOptionsRequest = useGetCustom(
        `calendaraccounts/index${location?.search}`
    );

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
        navigate("/calendaraccounts");
    };

    const handleSelectChange = (e: any) => {
        const { value, name } = e.target;
        setSelectValue((prev: any) => ({ ...prev, [name]: value }));
    };

    const handleSelectChangeCalendarIdAndName = (e: any) => {
        const { value } = e.target;

        const calendarSelectedItem = calendarOptions.find(
            (option: any) => option.id === value
        );
        const calendarName = calendarSelectedItem?.name || "";

        setSelectValue((prev: any) => ({
            ...prev,
            calendarId: value,
            calendarName,
        }));
    };

    const getCalendarOptions = async () => {
        try {
            const response: any = await calendarOptionsRequest.doFetch(true);
            setCalendarOptions(response.data.calendarsList);
        } catch (error) {
            console.log("error", error);
        }
    };

    useEffect(() => {
        if (location.search && calendarNetlex == query.get("netlexCalendar")) {
            handleClickOpen();
            getCalendarOptions();
        }
    }, [location?.search]);

    const handleGenerateUrl = async () => {
        const params = {
            calendario_netlex: calendarNetlex,
            tipo_account: selectValue.tipo_account,
            filtroCalendario: selectValue.filtroCalendario,
            calendarName: selectValue.calendarName
                ? selectValue.calendarName
                : null,
        };

        try {
            const { data }: any = await generateUrlRequest.doFetch(
                true,
                params
            );
            setLoading(true);
            if (data._redirectInfo?.url) {
                window.open(data._redirectInfo.url, "_blank");
            }
        } catch (error) {
            console.log("error", error);
        }
    };

    const handleSaveAccount = async () => {
        const params = {
            calendario_netlex: calendarNetlex,
            tipo_account: selectValue.tipo_account,
            filtroCalendario: selectValue.filtroCalendario,
            calendarId: selectValue.calendarId ? selectValue.calendarId : null,
            calendarName: selectValue.calendarName
                ? selectValue.calendarName
                : null,
        };

        try {
            await saveRequest.doFetch(true, params);
            setLoading(true);
            setOpen(false);
            navigate("/calendaraccounts");
        } catch (error) {
            console.log("error", error);
        }
    };

    return (
        <Fragment>
            <Button variant="contained" onClick={handleClickOpen}>
                {label}
            </Button>
            <Dialog
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                open={open}
                fullWidth
            >
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                    {t("Collega calendario")}
                    <IconButton
                        aria-label="close"
                        onClick={handleClose}
                        sx={{
                            position: "absolute",
                            right: 8,
                            top: 8,
                            color: (theme: any) => theme.palette.grey[500],
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={12} md={12}>
                            <FormControl fullWidth>
                                <Grid container spacing={2}>
                                    <Grid item md={3.5}>
                                        <InputLabel
                                            sx={{ pt: 1 }}
                                            id="demo-simple-select-label"
                                        >
                                            {t("Tipo Calendario")}
                                        </InputLabel>
                                    </Grid>
                                    <Grid item xs={6} sm={6} md={6}>
                                        <Select
                                            labelId="demo-simple-select-label"
                                            id="tipo_account"
                                            value={selectValue.tipo_account}
                                            name="tipo_account"
                                            onChange={handleSelectChange}
                                            disabled={
                                                location?.search ? true : false
                                            }
                                            fullWidth
                                        >
                                            <MenuItem value="1">
                                                Google Calendar
                                            </MenuItem>
                                            <MenuItem value="2">
                                                Microsoft Outlook
                                            </MenuItem>
                                        </Select>
                                    </Grid>
                                </Grid>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={12} md={12}>
                            <FormControl fullWidth>
                                <Grid container alignItems="center" spacing={2}>
                                    <Grid item md={3.5}>
                                        <InputLabel
                                            sx={{ pt: 1 }}
                                            id="demo-simple-select-label"
                                        >
                                            {t("Filtro Calendario")}
                                        </InputLabel>
                                    </Grid>
                                    <Grid item xs={6} sm={6} md={6}>
                                        <Select
                                            labelId="demo-simple-select-label"
                                            id="filtroCalendario"
                                            value={selectValue.filtroCalendario}
                                            name="filtroCalendario"
                                            onChange={handleSelectChange}
                                            disabled={
                                                location?.search ? true : false
                                            }
                                            fullWidth
                                        >
                                            <MenuItem value="1">
                                                {t("Personale")}
                                            </MenuItem>
                                            <MenuItem value="2">
                                                {t("Generale")}
                                            </MenuItem>
                                        </Select>
                                    </Grid>
                                    <Grid item md={2}>
                                        <Tooltip
                                            title={t(
                                                "Tipologia di calendario da collegare Personale: Prenderà soltanto Impegni/Udienze intestati al proprio utente. Generale: Prenderà tutti gli impegni visibili in agenda. "
                                            )}
                                        >
                                            <IconButton>
                                                <InfoIcon
                                                    sx={{
                                                        width: 20,
                                                        height: 20,
                                                    }}
                                                />
                                            </IconButton>
                                        </Tooltip>
                                    </Grid>
                                </Grid>
                            </FormControl>
                        </Grid>
                        {location?.search && (
                            <Grid item xs={12} sm={12} md={12}>
                                <FormControl fullWidth>
                                    <Grid container spacing={2}>
                                        <Grid item md={3.5}>
                                            <InputLabel
                                                sx={{ pt: 1 }}
                                                id="demo-simple-select-label"
                                            >
                                                {t("Calendario")}
                                            </InputLabel>
                                        </Grid>
                                        <Grid item xs={6} sm={6} md={6}>
                                            <Select
                                                labelId="demo-simple-select-label"
                                                id="calendarName"
                                                value={selectValue.calendarId}
                                                name="calendarName"
                                                onChange={
                                                    handleSelectChangeCalendarIdAndName
                                                }
                                                fullWidth
                                            >
                                                {calendarOptions?.map(
                                                    (option: any) => (
                                                        <MenuItem
                                                            key={option.id}
                                                            value={option.id}
                                                        >
                                                            {option.name}
                                                        </MenuItem>
                                                    )
                                                )}
                                            </Select>
                                        </Grid>
                                    </Grid>
                                </FormControl>
                            </Grid>
                        )}
                        <Grid item md={12}>
                            <Typography
                                sx={{ fontSize: 14, fontStyle: "italic" }}
                            >
                                <span style={{ fontWeight: "bold" }}>
                                    N.B.{" "}
                                </span>{" "}
                                {t(
                                    "Se possibile, si consiglia di collegare un calendario esterno creato ex novo, indipendente dal vostro calendario principale."
                                )}
                            </Typography>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button variant="outlined" onClick={handleClose}>
                        {t("Annulla")}
                    </Button>
                    <Button
                        variant="contained"
                        onClick={
                            location?.search
                                ? handleSaveAccount
                                : handleGenerateUrl
                        }
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
        </Fragment>
    );
};

export default CalendarAccountsDialog;
