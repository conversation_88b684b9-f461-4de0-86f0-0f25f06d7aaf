import { useState, Fragment } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Grid,
    Typography,
    Tooltip,
} from "@mui/material";
import { useTranslation } from "react-i18next";

import usePostCustom from "../../../../hooks/usePostCustom";

import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";

const ScollegaCalendarAccountsDialog = ({
    label,
    id,
    accountType,
    setLoading,
}: any) => {
    const [open, setOpen] = useState(false);
    const [checkBox, setCheckBox] = useState({
        checkBox1: false,
        checkBox2: false,
        checkboxCheckedOutlook: false,
    });

    const { t } = useTranslation();
    const usePostCustomRequest = usePostCustom("/calendaraccounts/delete");

    const handleClickOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const handleSelectChange = (e: any) => {
        const { checked, name } = e.target;
        setCheckBox((prev: any) => ({ ...prev, [name]: checked }));
    };

    const handleDelete = async () => {
        const params = {
            uniqueid: id && id,
            checkboxCheckedOutlook: checkBox.checkboxCheckedOutlook
                ? true
                : null,
        };

        if (typeof id !== "undefined") {
            if (checkBox?.checkBox1 && checkBox?.checkBox2) {
                await usePostCustomRequest.doFetch(true, params);
                setLoading(true);
                setCheckBox({
                    checkBox1: false,
                    checkBox2: false,
                    checkboxCheckedOutlook: false,
                });
            } else {
                if (!checkBox?.checkBox1) {
                    if (checkBox?.checkBox2) {
                        alert(
                            t(
                                "Si è verificato un errore durante l'operazione, se il problema persiste contattare l'assistenza tecnica."
                            )
                        );
                    } else {
                        alert(
                            t(
                                "Si devono prima confermare le opzioni obbligatorie."
                            )
                        );
                    }
                } else if (!checkBox?.checkBox2) {
                    if (checkBox?.checkBox1) {
                        alert(
                            t(
                                "Si deve prima confermare di voler procedere all'eliminazione degli impegni dal gestionale."
                            )
                        );
                    }
                }
            }
        } else {
            alert(
                t(
                    "Si è verificato un errore durante l'operazione, se il problema persiste contattare l'assistenza tecnica."
                )
            );
        }
        setOpen(false);
    };

    return (
        <Fragment>
            <Button variant="contained" color="error" onClick={handleClickOpen}>
                {label}
            </Button>
            <Dialog
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                open={open}
                fullWidth
            >
                <DialogTitle sx={{ m: 0, p: 2 }} id="customized-dialog-title">
                    {t("Conferma eliminazione account")}
                    <IconButton
                        aria-label="close"
                        onClick={handleClose}
                        sx={{
                            position: "absolute",
                            right: 8,
                            top: 8,
                            color: (theme: any) => theme.palette.grey[500],
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent dividers>
                    <Grid container spacing={2}>
                        <Grid item md={12}>
                            <Typography
                                sx={{ fontWeight: "bold", fontSize: 14 }}
                            >
                                {t(
                                    "Attenzione, l'operazione rimuoverà dal gestionale tutti gli impegni derivanti dal calendario esterno."
                                )}
                            </Typography>
                            <Typography
                                sx={{ fontWeight: "bold", fontSize: 14 }}
                            >
                                {t(
                                    "Ti avvisiamo inoltre che se in futuro sarà ricollegato lo stesso calendario, questo potrebbe causare la duplicazioni di impegni/eventi. Si è sicuri di voler procedere?"
                                )}
                            </Typography>
                        </Grid>
                        <Grid item md={12}>
                            <FormGroup>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="checkBox1"
                                            checked={checkBox?.checkBox1}
                                            onChange={handleSelectChange}
                                        />
                                    }
                                    label={t(
                                        "Confermo di voler procedere nonostante i rischi *"
                                    )}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="checkBox2"
                                            checked={checkBox?.checkBox2}
                                            onChange={handleSelectChange}
                                        />
                                    }
                                    label={t(
                                        "Confermo di voler eliminare dal gestionale gli impegni derivanti dal calendario esterno *"
                                    )}
                                />
                                {accountType == 2 && (
                                    <Grid container spacing={2}>
                                        <Grid item md={10}>
                                            <FormControlLabel
                                                control={
                                                    <Checkbox
                                                        name="checkboxCheckedOutlook"
                                                        checked={
                                                            checkBox?.checkboxCheckedOutlook
                                                        }
                                                        onChange={
                                                            handleSelectChange
                                                        }
                                                        required
                                                    />
                                                }
                                                label={t(
                                                    "Confermo di voler eliminare dal calendario Outlook gli impegni importati dal gestionale"
                                                )}
                                            />
                                        </Grid>
                                        <Grid item md={2}>
                                            <Tooltip
                                                title={t(
                                                    "Gli impegni che il gestionale ha creato sul calendario di Outlook verranno cancellati"
                                                )}
                                            >
                                                <IconButton>
                                                    <InfoIcon
                                                        sx={{
                                                            width: 20,
                                                            height: 20,
                                                        }}
                                                    />
                                                </IconButton>
                                            </Tooltip>
                                        </Grid>
                                    </Grid>
                                )}
                            </FormGroup>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button variant="outlined" onClick={handleClose}>
                        {t("Annulla")}
                    </Button>
                    <Button
                        variant="contained"
                        color="error"
                        onClick={handleDelete}
                    >
                        {t("Scollega")}
                    </Button>
                </DialogActions>
            </Dialog>
        </Fragment>
    );
};

export default ScollegaCalendarAccountsDialog;
