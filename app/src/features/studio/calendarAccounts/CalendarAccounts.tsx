import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";
import VaporPage from "@vapor/react-custom/VaporPage";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";

import { useGetCalendarAccount } from "./hooks/useCalendarAccount";

import CollegaCalendarAccountsDialog from "./components/CollegaCalendarAccountsDialog";
import ScollegaCalendarAccountsDialog from "./components/ScollegaCalendarAccountsDialog";

import GoogleImage from "../../../assets/google.jpg";
import MicrosoftImage from "../../../assets/microsoft.png";
import { getCalendarAccountsGrid } from "../../../utilities/calendarAccount/gridColumn";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";

const CalendarAccount = () => {
    const [list, setList] = useState<{ columns: any[]; data: any[] }>({
        columns: [],
        data: [],
    });
    const [loading, setLoading] = useState<boolean>(false);

    const { data, fetchData } = useGetCalendarAccount();
    const { t } = useTranslation();

    useEffect(() => {
        fetchData();
        setLoading(false);
    }, [loading]);

    const getColumns = async () => {
        const columns = await getCalendarAccountsGrid(
            t,
            ScollegaCalendarAccountsDialog,
            CollegaCalendarAccountsDialog,
            MicrosoftImage,
            GoogleImage,
            setLoading
        );
        setList({ columns, data });
    };

    useEffect(() => {
        getColumns();
    }, [data]);

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="calendarAccounts"
                columns={list?.columns}
                data={list?.data}
                totalRows={list?.data.length}
                page={0}
                pageSize={100}
                onClickKey="custom"
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <VaporPage>
            <VaporHeaderBar title={t("CONFIGURAZIONE CALENDARI")} />
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
};

export default CalendarAccount;
