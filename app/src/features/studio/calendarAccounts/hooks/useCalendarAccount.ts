import { useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

export const useGetCalendarAccount = () => {
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const defaultLetterHeadsIndexRequest = useGetCustom("calendaraccounts");

    const fetchData = async () => {
        setLoading(true);
        try {
            const response: any = await defaultLetterHeadsIndexRequest.doFetch(
                true
            );
            const parsedData = response?.data && JSON.parse(response.data.list);

            setData(parsedData.currentPage);
        } catch (error) {
            console.error("Error fetching data", error);
        } finally {
            setLoading(false);
        }
    };

    return { data, loading, fetchData };
};
