import usePostCustom from "../../../hooks/usePostCustom";
import { useState } from "react";
import { CURRENT_DATE_FORMATTED } from "../../agenda/generalCalendar/addCalendar/impegno/constants/constant";

export interface IUpdateDeadlineParams {
    deadline_uid: string | string[];
    oggetto: string;
    data: string;
    ora: string;
    minuti: string;
}

const DEFAULT_PAYLOAD: IUpdateDeadlineParams = {
    deadline_uid: "",
    oggetto: "",
    data: CURRENT_DATE_FORMATTED(),
    ora: "09",
    minuti: "00",
};

export default function useUpdateDeadlineObj() {
    const [updateDeadlineParams, setUpdateDeadlineParams] =
        useState<IUpdateDeadlineParams>(DEFAULT_PAYLOAD);
    const [disableModifyButton, setDisableModifyButton] =
        useState<boolean>(true);

    const UPDATE_DEADLINE_OBJECT_REQUEST = usePostCustom(
        "deadlines/updatedeadlineobj?noTemplateVars=true"
    );

    const handleUpdateDeadlineObject = async (params: IUpdateDeadlineParams) => {
        const response: any = await UPDATE_DEADLINE_OBJECT_REQUEST.doFetch(
            true,
            params
        );
        if (response.data) {
            setDisableModifyButton(true);
            setUpdateDeadlineParams(DEFAULT_PAYLOAD);
        }
        return response;
    };

    return {
        updateDeadlineParams,
        setUpdateDeadlineParams,
        handleUpdateDeadlineObject,
        disableModifyButton,
        setDisableModifyButton,
        loading: UPDATE_DEADLINE_OBJECT_REQUEST.loading,
    };
}
