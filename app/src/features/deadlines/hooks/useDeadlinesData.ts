import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { IDeadlinesData } from "../interfaces/interfaces";

const useDeadlinesData = () => {
    const getDeadlinesRequest = useGetCustom("deadlines/deadlines");
    const [deadlinesData, setDeadlinesData] = useState<IDeadlinesData | null>(null);

    const fetchData = async () => {
        try {
            const response: any = await getDeadlinesRequest.doFetch(true);
            setDeadlinesData(response.data);
        } catch (error) {
            console.error("Error fetching deadlines data:", error);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    return { 
        deadlinesData, 
        loading: getDeadlinesRequest.loading,
        refetch: fetchData
    };
};

export default useDeadlinesData;
