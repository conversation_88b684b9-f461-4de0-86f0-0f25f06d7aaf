import { useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { saveFile } from "../../../utilities/utils";
import { IDeadlinesQuery } from "../interfaces/interfaces";

interface ExportResponse {
    data?: any;
}

export const useExportPrint = () => {
    const [loading, setLoading] = useState(false);
    
    const exportCSVRequest = useGetCustom(
        "deadlines/exportcsv?noTemplateVars=true",
        {},
        null,
        true
    );
    
    const printPDFRequest = useGetCustom(
        "deadlines/printbysearch",
        {},
        null,
        true
    );

    const cleanQuery = (query: IDeadlinesQuery) => {
        const defaultsToRemove: any = {
            page: 0,
            pageSize: 10,
            sortColumn: "data",
            sortOrder: "desc",
        };

        const entries = Object.entries(query).map(([key, value]) => {
            if (typeof value === 'boolean' && (key === 'poliswebFilter' || key === 'importantOnly')) {
                return value ? [key, "on"] : null;
            }
            return [key, value];
        }).filter(entry => {
            if (!entry) return false; 
            const [key, value] = entry;
            // Handle boolean values
            if (typeof value === 'boolean') {
                return value !== false;
            }
            // Handle string values
            if (typeof value === 'string') {
                return value !== "" && defaultsToRemove[key] !== value;
            }
            return value !== null && value !== undefined;
        });

        return Object.fromEntries(entries as [string, any][]);
    };

    const exportToCSV = async (query: IDeadlinesQuery) => {
        try {
            setLoading(true);
            const cleanedQuery = cleanQuery(query);

            const response = await exportCSVRequest.doFetch(true, cleanedQuery) as ExportResponse;

            if (response?.data) {
                saveFile({
                    data: response.data,
                    fileName: "Lista_impegni",
                    type: "csv"
                });
            }
        } catch (error) {
            console.error("Error exporting CSV:", error);
        } finally {
            setLoading(false);
        }
    };

    const printToPDF = async (query: IDeadlinesQuery) => {
        try {
            setLoading(true);
            const cleanedQuery = cleanQuery(query);

            const response = await printPDFRequest.doFetch(true, cleanedQuery) as ExportResponse;

            if (response?.data) {
                saveFile({
                    data: response.data,
                    fileName: "Lista_impegni",
                    type: "pdf"
                });
            }
        } catch (error) {
            console.error("Error printing PDF:", error);
        } finally {
            setLoading(false);
        }
    };

    return {
        exportToCSV,
        printToPDF,
        loading: loading || exportCSVRequest.loading || printPDFRequest.loading,
    };
};
