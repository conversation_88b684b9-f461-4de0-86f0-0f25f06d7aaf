import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import { mapOtherList } from "../../../utilities/common";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExclamationCircle } from "@fortawesome/free-solid-svg-icons";

const cellTypographyStyle = {
    fontFamily: 'Roboto',
    fontWeight: 400,
    fontSize: '14px',
    lineHeight: '100%',
    letterSpacing: '0%',
    verticalAlign: 'middle',
};

const displayingStatusDeadlines = (params: any, t?: any) => {
    if (!params || !params.row) {
        return (
            <span style={cellTypographyStyle}>
                {t("No")}
            </span>
        );
    }

    const status = params.row.status || params.row.evaso;
    if (status === "1" || status === "true" || status === true) {
        return (
            <span style={cellTypographyStyle}>
                {t("Sì")}
            </span>
        );
    }
    return (
        <span style={cellTypographyStyle}>
            {t("No")}
        </span>
    );
};

export const mapDeadlinesColumnNames = (
    response: any,
    t?: any
): IGridColumn[] => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    const columns = column_keys
        .map((key, idx) => {
            const width = column_widths?.[idx];
            if (!key || !width || width === "0%") return undefined;

            const col: any = {
                field: key,
                headerName: column_names[idx],
                flex: parseInt(width, 10) / 100,
                sortable: sortable[idx],
                hideable: true,
            };

            if (key === "testo") {
                col.valueGetter = (_: any, row: any) => {
                    if (!row) return "";
                    // For new engagements, use titolo; for legacy, use oggetto or testo
                    return row.titolo || row.oggetto || row.testo || "";
                };
                col.renderCell = (params: any) => {
                    if (!params || !params.row) {
                        return <span style={cellTypographyStyle}>{params?.value || ""}</span>;
                    }
                    return (
                        <div style={{ display: "flex", alignItems: "center" }}>
                            {params.row.important === "1" && (
                                <FontAwesomeIcon
                                    icon={faExclamationCircle}
                                    style={{ color: "#d32f2f", marginRight: 8 }}
                                />
                            )}
                            <span style={cellTypographyStyle}>{params.value}</span>
                        </div>
                    );
                };
            } else if (key === "evaso") {
                col.renderCell = (params: any) => displayingStatusDeadlines(params, t);
                col.valueGetter = (_: any, row: any) => {
                    if (!row) return "";
                    return row.evaso || row.status || "";
                };
            } else if (key === "data") {
                col.valueGetter = (_: any, row: any) => {
                    if (!row) return "";
                    const date = row.data;
                    if (date) {
                        try {
                            // Parse DD/MM/YYYY HH:MM format
                            const dateTimeMatch = date.match(/^(\d{2})\/(\d{2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/);
                            if (dateTimeMatch) {
                                const [, day, month, year, hour, minute] = dateTimeMatch;
                                const parsedDate = new Date(
                                    parseInt(year, 10),
                                    parseInt(month, 10) - 1, // Month is 0-indexed
                                    parseInt(day, 10),
                                    parseInt(hour, 10),
                                    parseInt(minute, 10)
                                );
                                return parsedDate.toLocaleDateString("it-IT", {
                                    day: "2-digit",
                                    month: "2-digit",
                                    year: "numeric",
                                    hour: "2-digit",
                                    minute: "2-digit"
                                });
                            }
                            // Fallback to original date string if parsing fails
                            return date;
                        } catch {
                            return date;
                        }
                    }
                    return "";
                };
            }

            if (!col.valueGetter) {
                col.valueGetter = (_: any, row: any) => {
                    if (!row) return "";
                    let fieldValue = row[key];
                    
                    if (key === "codicearchivio" && !fieldValue) {
                        fieldValue = row.codiceArchivio;
                    } else if (key === "descrizione" && !fieldValue) {
                        fieldValue = row.descrizionePratica;
                    } else if (key === "ruologenerale" && !fieldValue) {
                        fieldValue = row.rg;
                    }
                    
                    return fieldValue || "";
                };
            }

            if (!col.renderCell) {
                col.renderCell = (params: any) => (
                    <span style={cellTypographyStyle}>
                        {params.value || ""}
                    </span>
                );
            }

            return col;
        })
        .filter((c): c is IGridColumn => !!c);

    return columns;
};
