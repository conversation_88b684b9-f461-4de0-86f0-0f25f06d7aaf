import { IDeadlinesQuery } from '../interfaces/interfaces';

export const VALID_SORT_COLUMNS = ['data', 'titolo', 'categoria', 'status'] as const;
export const VALID_SORT_ORDERS = ['asc', 'desc'] as const;

/**
 * Validates and parses URL parameters for deadlines query
 */
export const parseDeadlinesUrlParams = (searchParams: URLSearchParams): Partial<IDeadlinesQuery> => {
    const params: Partial<IDeadlinesQuery> = {};

    try {
        // Parse and validate numeric fields
        const page = searchParams.get('page');
        const pageSize = searchParams.get('pageSize');
        
        if (page) {
            const parsedPage = parseInt(page, 10);
            if (!isNaN(parsedPage) && parsedPage >= 0) {
                params.page = parsedPage;
            }
        }
        
        if (pageSize) {
            const parsedPageSize = parseInt(pageSize, 10);
            if (!isNaN(parsedPageSize) && parsedPageSize > 0 && parsedPageSize <= 100) {
                params.pageSize = parsedPageSize;
            }
        }

        // Parse string fields with validation
        const sortColumn = searchParams.get('sortColumn');
        const sortOrder = searchParams.get('sortOrder');
        
        if (sortColumn && VALID_SORT_COLUMNS.includes(sortColumn as any)) {
            params.sortColumn = sortColumn;
        }
        
        if (sortOrder && VALID_SORT_ORDERS.includes(sortOrder as any)) {
            params.sortOrder = sortOrder;
        }

        // Parse other string fields
        const stringFields = [
            'oldDeadlines', 'startDate', 'endDate', 'person', 'deadlinesTypeSearch',
            'deadlinesCategorySearch', 'object', 'UdienzeRitardo', 'processed',
            'notProcessed', 'controparti', 'practiceSearch', 'titleObjectSearch',
            'calendarGroup', 'calendarReferent', 'authoritySearchId'
        ] as const;

        stringFields.forEach(field => {
            const value = searchParams.get(field);
            if (value !== null) {
                (params as any)[field] = value;
            }
        });

        // Map intestatario to person if person not already set
        const intestatario = searchParams.get('intestatario');
        if (intestatario !== null && !params.person) {
            params.person = intestatario;
        }

        // Parse boolean fields
        const booleanFields = ['close', 'isArchived', 'poliswebFilter', 'importantOnly', 'onlyGroupEvents'] as const;
        booleanFields.forEach(field => {
            const value = searchParams.get(field);
            if (value !== null) {
                (params as any)[field] = value === 'true';
            }
        });

    } catch (error) {
        console.warn('Error parsing URL parameters:', error);
    }

    return params;
};

/**
 * Converts query object to URL search params, only including non-default values
 */
export const queryToUrlParams = (
    query: IDeadlinesQuery, 
    defaults: IDeadlinesQuery,
    requiredParams: Partial<IDeadlinesQuery>
): URLSearchParams => {
    const searchParams = new URLSearchParams();

    // Always set required params
    Object.entries(requiredParams).forEach(([key, value]) => {
        searchParams.set(key, String(value));
    });

    // Only set non-default values to keep URL clean
    Object.entries(query).forEach(([key, value]) => {
        const defaultValue = (defaults as any)[key];
        const requiredValue = (requiredParams as any)[key];
        
        // Skip if it's a required param (already set) or matches default
        if (requiredValue !== undefined || value === defaultValue) {
            return;
        }

        if (value !== null && value !== undefined && value !== '') {
            searchParams.set(key, String(value));
        }
    });

    return searchParams;
};

/**
 * Validates date format (DD/MM/YYYY)
 */
export const isValidDateFormat = (dateString: string): boolean => {
    const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
    if (!dateRegex.test(dateString)) return false;
    
    const [day, month, year] = dateString.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    
    return date.getFullYear() === year && 
           date.getMonth() === month - 1 && 
           date.getDate() === day;
};
