import BaseGridList from "../../../models/BaseGridList";
import { IGridColumn } from "../../../interfaces/general.interfaces";
import { mapDeadlinesColumnNames } from "./mapColumns";

export const getDeadlinesGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Clienti"),
                t("Controparti"),
                t("Codice archivio"),
                t("Descrizione"),
                t("R.G."),
                t("Oggetto/Titolo"),
                t("Data e ora"),
                t("Categoria"),
                t("Autorità"),
                t("Intestatari"),
                t("Evaso"),
            ],
            column_keys: [
                "listaclienti",
                "listacontroparti",
                "codicearchivio",
                "descrizione",
                "ruologenerale",
                "testo",
                "data",
                "categoria",
                "autorita",
                "users",
                "evaso",
            ],
            column_widths: [
                "10%",
                "12%",
                "8%",
                "15%",
                "6%",
                "20%",
                "10%",
                "8%",
                "8%",
                "8%",
                "5%",
            ],
            sortable: [true, true, true, true, true, true, true, true, true, true, true],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDeadlinesColumnNames(response, t);
};
