import React, { useState, useEffect, useCallback, useRef } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { VaporToolbar } from "@vapor/react-custom";
import { useTranslation } from "@1f/react-sdk";
import { useNavigate, useLocation } from "react-router-dom";
import { debounce } from "lodash";
import { Button, Stack, Box, EmptyState } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCalendarDays, faPlus, faExclamationCircle, faInbox } from "@fortawesome/free-solid-svg-icons";
import { CustomDataGrid } from "../../custom-components/CustomDataGrid";
import { GridPaginationModel, GridRowSelectionModel } from "@mui/x-data-grid-pro";
import Spinner from "../../custom-components/Spinner";
import PageTitle from "../../custom-components/PageTitle";
import DeadlinesFilters from "./components/DeadlinesFilters";
import MoreOptionsMenu from "./components/MoreOptionsMenu";
import useDeadlinesData from "./hooks/useDeadlinesData";
import useDeadlinesList from "./hooks/useDeadlinesList";
import { useExportPrint } from "./hooks/useExportPrint";
import { IDeadlinesQuery } from "./interfaces/interfaces";
import { parseDeadlinesUrlParams, queryToUrlParams } from "./utils/urlParams";

import ModifyDeadlineModal from "./components/ModifyDeadlineModal";
import useUpdateDeadlineObj from "./hooks/useUpdateDeadlineObj";
import ToastNotification from "../../custom-components/ToastNotification";

export const DEFAULT_LIST_PARAMS: IDeadlinesQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "data",
    sortOrder: "desc",
    oldDeadlines: "true",
    startDate: "02/01/2010",
    endDate: "10/7/2025",
    person: "-1", // Default to "all users" instead of hardcoded user ID
    deadlinesTypeSearch: "-1",
    deadlinesCategorySearch: "-1",
    object: "",
    UdienzeRitardo: "1",
    processed: "-1",
    notProcessed: "0",
    close: true,
    isArchived: false,
    poliswebFilter: false,
    importantOnly: false,
    controparti: "",
    practiceSearch: "",
    titleObjectSearch: "",
    calendarGroup: "-1",
    calendarReferent: "-1",
    authoritySearchId: "",
    onlyGroupEvents: false,
};

// Empty state for when no URL parameters exist
const getEmptyListParams = (): IDeadlinesQuery => {
    const today = new Date();
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(today.getFullYear() - 10);

    // Format dates as DD/MM/YYYY
    const formatDate = (date: Date) => {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    return {
        page: 0,
        pageSize: 10,
        sortColumn: "data",
        sortOrder: "desc",
        oldDeadlines: "false",
        startDate: formatDate(tenYearsAgo),
        endDate: formatDate(today),
        person: "-1",
        deadlinesTypeSearch: "-1",
        deadlinesCategorySearch: "-1",
        object: "",
        UdienzeRitardo: "0",
        processed: "-1",
        notProcessed: "0",
        close: false,
        isArchived: false,
        poliswebFilter: false,
        importantOnly: false,
        controparti: "",
        practiceSearch: "",
        titleObjectSearch: "",
        calendarGroup: "-1",
        calendarReferent: "-1",
        authoritySearchId: "",
        onlyGroupEvents: false,
    };
};



export const REQUIRED_URL_PARAMS = {
    oldDeadlines: "true",
    startDate: "02/01/2010",
    endDate: "10/7/2025",
};

const DeadlinesList: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();



    const updateUrlWithParams = useCallback(
        debounce((currentQuery: IDeadlinesQuery) => {
            const searchParams = queryToUrlParams(currentQuery, getEmptyListParams(), {});
            navigate({ search: searchParams.toString() }, { replace: true });
        }, 300),
        [navigate]
    );

    const parseUrlParams = (): Partial<IDeadlinesQuery> => {
        const searchParams = new URLSearchParams(location.search);
        return parseDeadlinesUrlParams(searchParams);
    };



    const initialLoadComplete = useRef(false);

    const [query, setQueryState] = useState<IDeadlinesQuery>(() => {
        const urlParams = parseUrlParams();
        if (Object.keys(urlParams).length > 0) {
            // Returning to page with URL params - preserve them, prioritizing URL params over defaults
            return { ...DEFAULT_LIST_PARAMS, ...urlParams };
        }
        // No URL params - use empty filters
        return { ...getEmptyListParams() };
    });
    const [selectedRows, setSelectedRows] = useState<string[]>([]);

    const queryRef = useRef<IDeadlinesQuery>(query);

    useEffect(() => {
        queryRef.current = query;
    }, [query]);

    // Don't automatically set user ID - let user choose filters

    const setQuery = useCallback((updater: React.SetStateAction<IDeadlinesQuery>) => {
        setQueryState(prevQuery => {
            const newQuery = typeof updater === 'function' ? updater(prevQuery) : updater;
            queryRef.current = newQuery;
            updateUrlWithParams(newQuery);
            return newQuery;
        });
    }, [updateUrlWithParams]);

    const handleClearFilters = useCallback(() => {
        const clearedQuery = { ...getEmptyListParams() };
        setQuery(clearedQuery);
    }, [setQuery]);
    const [openModal, setOpenModal] = useState(false);
    const [updateDeadlineParams, setUpdateDeadlineParams] = useState<any>({
        deadline_uid: [],
        oggetto: "",
        data: "",
        ora: "09",
        minuti: "00"
    });
    const [showToast, setShowToast] = useState<boolean>(false);
    const [lastUpdateParams, setLastUpdateParams] = useState<any>(null);

    const { deadlinesData, loading: dataLoading } = useDeadlinesData();
    const { list, loading: listLoading, error: listError, fetchData } = useDeadlinesList(query);
    const { exportToCSV, printToPDF, loading: exportLoading } = useExportPrint();
    const { handleUpdateDeadlineObject } = useUpdateDeadlineObj();

    const loading = dataLoading || listLoading || exportLoading;

    useEffect(() => {
        if (!initialLoadComplete.current) {
            initialLoadComplete.current = true;

            // On initial load, parse URL params or use empty state
            const urlParams = parseUrlParams();
            if (Object.keys(urlParams).length > 0) {
                setQueryState(prev => ({ ...prev, ...urlParams }));
            }
            // If no URL params, keep empty state (don't set any filters)
        }
    }, []); // Only run on mount

    // Separate effect for handling URL changes from browser navigation
    useEffect(() => {
        if (initialLoadComplete.current) {
            const urlParams = parseUrlParams();
            if (Object.keys(urlParams).length > 0) {
                setQueryState(prev => ({ ...prev, ...urlParams }));
            }
        }
    }, [location.search]);

    useEffect(() => {
        setSelectedRows([]);
    }, [
        query.importantOnly,
        query.poliswebFilter,
        query.processed,
        query.person,
        query.deadlinesTypeSearch,
        query.deadlinesCategorySearch,
        query.practiceSearch,
        query.titleObjectSearch,
        query.startDate,
        query.endDate
    ]);

    const handlePageChange = useCallback((model: GridPaginationModel) => {
        setQuery(prev => ({
            ...prev,
            page: model.page,
            pageSize: model.pageSize,
        }));
    }, []);

    const handleRowSelection = useCallback((rowSelectionModel: GridRowSelectionModel) => {
        setSelectedRows(rowSelectionModel as string[]);
    }, []);

    const handleRowClick = useCallback((uniqueid: string) => {
        navigate(`/impegno/update/${uniqueid}`, {
            state: {
                isFromDeadlinesList: true,
                returnUrl: location.pathname + location.search
            },
        });
    }, [navigate, location]);

    const handleCalendarView = useCallback(() => {
        navigate("/calendar/calendar");
    }, [navigate]);

    const handleAddCommitment = useCallback(() => {
        navigate("/impegno/update", {
            state: {
                isFromDeadlinesList: true,
                returnUrl: location.pathname + location.search
            }
        });
    }, [navigate, location]);

    const handleModify = useCallback(() => {
        if (selectedRows.length > 0) {
            // Find the first selected item to get current values
            const firstSelectedItem = list.rows?.find((row: any) => row.uniqueid === selectedRows[0]);

            let currentTitle = "";
            let currentDate = "";
            let currentHour = "09";
            let currentMinutes = "00";

            if (firstSelectedItem) {
                // Extract current title from 'testo' field
                currentTitle = firstSelectedItem.testo || "";

                // Extract current date and time from 'data' field
                if (firstSelectedItem.data) {
                    // Parse the date string (assuming format like "DD/MM/YYYY HH:MM" or similar)
                    const dateTimeMatch = firstSelectedItem.data.match(/(\d{2}\/\d{2}\/\d{4})\s*(\d{1,2}):(\d{2})/);
                    if (dateTimeMatch) {
                        currentDate = dateTimeMatch[1]; // DD/MM/YYYY
                        currentHour = dateTimeMatch[2].padStart(2, '0'); // HH
                        currentMinutes = dateTimeMatch[3]; // MM
                    } else {
                        // If no time found, try to extract just the date
                        const dateMatch = firstSelectedItem.data.match(/(\d{2}\/\d{2}\/\d{4})/);
                        if (dateMatch) {
                            currentDate = dateMatch[1];
                        }
                    }
                }
            }

            setUpdateDeadlineParams({
                deadline_uid: selectedRows,
                oggetto: currentTitle,
                data: currentDate,
                ora: currentHour,
                minuti: currentMinutes
            });
            setOpenModal(true);
        }
    }, [selectedRows, list.rows]);

    const handleDialog = useCallback(() => {
        setOpenModal(!openModal);
    }, [openModal]);

    const handleExportCSV = useCallback(() => {
        exportToCSV(query);
    }, [exportToCSV, query]);

    const handlePrintPDF = useCallback(() => {
        printToPDF(query);
    }, [printToPDF, query]);

    const handleUpdateDeadlineObjectWithToast = useCallback(async (params: any) => {
        try {
            const response = await handleUpdateDeadlineObject(params);
            if (response?.data) {
                // Store the update parameters for potential undo
                setLastUpdateParams(params);
                // Show success toast
                setShowToast(true);
                // Close modal and refresh data
                setOpenModal(false);
                fetchData();
            }
        } catch (error) {
            console.error("Error updating deadline:", error);
        }
    }, [handleUpdateDeadlineObject, fetchData]);

    const handleUndoUpdate = useCallback(() => {
        if (lastUpdateParams) {
            // Reopen the modal with the last update parameters
            setUpdateDeadlineParams(lastUpdateParams);
            setOpenModal(true);
            setShowToast(false);
        }
    }, [lastUpdateParams]);

    const renderDataTable = () => {
        if (listError) {
            return (
                <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 2
                }}>
                    <FontAwesomeIcon
                        icon={faExclamationCircle}
                        size="2x"
                        style={{ color: '#d32f2f' }}
                    />
                    <span style={{ color: '#d32f2f', textAlign: 'center' }}>
                        {listError}
                    </span>
                    <Button
                        variant="outlined"
                        onClick={() => fetchData()}
                        sx={{ textTransform: "none" }}
                    >
                        {t("Riprova")}
                    </Button>
                </Box>
            );
        }

        if (!list.columns?.length) {
            return <Spinner fullPage={false} />;
        }

        if (!loading && list.rows?.length === 0) {
            return (
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                    <EmptyState
                        title={t("Nessun impegno trovato")}
                        description={t("Prova a modificare i filtri di ricerca o aggiungi un nuovo impegno")}
                        icon={faInbox}
                    />
                    <Button
                        variant="contained"
                        startIcon={<FontAwesomeIcon icon={faPlus} />}
                        onClick={handleAddCommitment}
                        sx={{ textTransform: "none" }}
                    >
                        {t("Aggiungi impegno")}
                    </Button>
                </Box>
            );
        }

        return (
            <CustomDataGrid
                name="deadlines-list-v2"
                columns={list.columns}
                data={list.rows}
                page={list.page || 0}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={loading}
                selectableRows={true}
                onPageChangeCallback={handlePageChange}
                onClickCallback={handleRowClick}
                onRowSelectionModelChange={handleRowSelection}
                query={query}
                setQuery={setQuery}
                onClickKey="uniqueid"
                onClickCheckboxKey="uniqueid"
                hasAdditionaStyles={true}
            />
        );
    };

    return (
        <VaporPage>
            <PageTitle
                title={t("Lista impegni")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: t("Modifica"),
                        onclick: handleModify,
                        variant: "text",
                        disabled: selectedRows.length === 0,
                        sx: {
                            textDecoration: "underline",
                            textTransform: "none",
                            color: selectedRows.length === 0 ? "text.disabled" : ""
                        }
                    },
                    <MoreOptionsMenu
                        key="more-options"
                        onExportCSV={handleExportCSV}
                        onPrintPDF={handlePrintPDF}
                        onAddEngagement={handleAddCommitment}
                    />
                ]}
            />

            <VaporPage.Section>
                <DeadlinesFilters
                    deadlinePeople={deadlinesData?.people}
                    deadlineTypes={deadlinesData?.deadlineTypes}
                    deadlineCategories={deadlinesData?.deadlineCategories}
                    query={query}
                    setQuery={setQuery}
                    initialQuery={getEmptyListParams()}
                    onClearFilters={handleClearFilters}
                />
            </VaporPage.Section>

            <VaporPage.Section>
                {renderDataTable()}
            </VaporPage.Section>

            <Box sx={{
                position: 'fixed',
                bottom: 0,
                left: 0,
                right: 0,
                zIndex: 1000,
                maxWidth: '100%',
                overflow: 'hidden',
                paddingLeft: "calc(48px + 16px)", 
                paddingRight: "16px",
                "@media screen and (max-width: 1536px)": {
                    paddingLeft: "48px", 
                    paddingRight: "0px"
                },
                "@media screen and (max-width: 1200px)": {
                    paddingLeft: "0px", 
                    paddingRight: "0px"
                },
                '& > *': {
                    position: 'static !important',
                    width: '100% !important',
                    maxWidth: '100% !important',
                    left: 'auto !important',
                    right: 'auto !important',
                    bottom: 'auto !important'
                },
                '& > * > *': {
                    position: 'static !important',
                    width: '100% !important',
                    maxWidth: '100% !important'
                }
            }}>
                <VaporToolbar
                    variant="regular"
                    withoutAppBar={true}
                    contentRight={
                        <Stack direction="row" gap={2}>
                            <Button
                                variant="outlined"
                                startIcon={<FontAwesomeIcon icon={faCalendarDays} />}
                                onClick={handleCalendarView}
                                sx={{ textTransform: "none" }}
                            >
                                {t("Vista calendario")}
                            </Button>
                            <Button
                                variant="contained"
                                startIcon={<FontAwesomeIcon icon={faPlus} />}
                                onClick={handleAddCommitment}
                                sx={{ textTransform: "none" }}
                            >
                                {t("Aggiungi impegno")}
                            </Button>
                        </Stack>
                    }
                />
            </Box>

            <Box>
                <ToastNotification
                    showNotification={showToast}
                    setShowNotification={setShowToast}
                    severity="warning"
                    text={t(`Hai modificato l'oggetto, data e ora di ${Array.isArray(lastUpdateParams?.deadline_uid) ? lastUpdateParams.deadline_uid.length : 1} impegni.`)}
                    customButtons={[
                        <Button
                            key="annulla"
                            variant="contained"
                            size="small"
                            sx={{ mr: 1 }}
                            onClick={handleUndoUpdate}
                        >
                            {t("Annulla")}
                        </Button>,
                    ]}
                />
            </Box>

            <ModifyDeadlineModal
                open={openModal}
                handleDialog={handleDialog}
                updateDeadlineParams={updateDeadlineParams}
                setUpdateDeadlineParams={setUpdateDeadlineParams}
                handleUpdateDeadlineObject={handleUpdateDeadlineObjectWithToast}
                fetchData={fetchData}
                selectedItems={Array.isArray(updateDeadlineParams.deadline_uid) ? updateDeadlineParams.deadline_uid : []}
                mode={Array.isArray(updateDeadlineParams.deadline_uid) && updateDeadlineParams.deadline_uid.length > 1 ? 'multiple' : 'single'}
            />


        </VaporPage>
    );
};

export default DeadlinesList;
