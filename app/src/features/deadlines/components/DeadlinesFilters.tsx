import React, { useCallback, useEffect, useState } from "react";
import {
    Box,
    Button,
    Checkbox,
    FormControlLabel,
    Grid,
    SearchBar,
} from "@vapor/v3-components";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { CustomSelect } from "../../../custom-components/CustomSelect";
import { SelectMultiple } from "../../../custom-components/SelectMultiple";
import { debounce } from "lodash";

import { useTranslation } from "@1f/react-sdk";
import { parseDate } from "../../../helpers/parseDataFormat";
import { IDeadlinesQuery, IDeadlinesData } from "../interfaces/interfaces";

const PROCESSED_DATA = [
    { id: "1", nome: "Evaso" },
    { id: "0", nome: "Non evaso" },
];

interface DeadlinesFiltersProps {
    deadlinePeople?: IDeadlinesData["people"];
    deadlineTypes?: IDeadlinesData["deadlineTypes"];
    deadlineCategories?: IDeadlinesData["deadlineCategories"];
    query: IDeadlinesQuery;
    setQuery: React.Dispatch<React.SetStateAction<IDeadlinesQuery>>;
    initialQuery: IDeadlinesQuery;
    onClearFilters?: () => void;
}

const DeadlinesFilters: React.FC<DeadlinesFiltersProps> = ({
    deadlinePeople,
    deadlineTypes,
    deadlineCategories,
    query,
    setQuery,
    initialQuery,
    onClearFilters,
}) => {
    const { t } = useTranslation();

    const [practiceSearchValue, setPracticeSearchValue] = useState(query.practiceSearch);
    const [titleObjectSearchValue, setTitleObjectSearchValue] = useState(query.object);

    // Create stable debounced functions using useCallback
    const debouncedPracticeSearch = useCallback(
        debounce((value: string, setQueryFn: React.Dispatch<React.SetStateAction<IDeadlinesQuery>>) => {
            setQueryFn(prev => ({ ...prev, practiceSearch: value }));
        }, 300),
        []
    );

    const debouncedTitleObjectSearch = useCallback(
        debounce((value: string, setQueryFn: React.Dispatch<React.SetStateAction<IDeadlinesQuery>>) => {
            console.log('[DeadlinesFilters] Title/Object search debounced:', {
                value,
                timestamp: new Date().toISOString(),
                action: 'updating query with debounced value'
            });
            setQueryFn(prev => ({ ...prev, object: value }));
        }, 300),
        []
    );

    const handlePracticeSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setPracticeSearchValue(value);
        debouncedPracticeSearch(value, setQuery);
    };

    const handlePracticeSearchKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            setQuery(prev => ({ ...prev, practiceSearch: practiceSearchValue }));
        }
    };

    const handleTitleObjectSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        console.log('[DeadlinesFilters] Title/Object search input changed:', {
            newValue: value,
            previousValue: titleObjectSearchValue,
            timestamp: new Date().toISOString(),
            eventType: event.type
        });
        setTitleObjectSearchValue(value);
        debouncedTitleObjectSearch(value, setQuery);
    };

    const handleTitleObjectSearchKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === 'Enter') {
            event.preventDefault();
            console.log('[DeadlinesFilters] Title/Object search Enter key pressed:', {
                value: titleObjectSearchValue,
                timestamp: new Date().toISOString(),
                action: 'immediate query update'
            });
            setQuery(prev => ({ ...prev, object: titleObjectSearchValue }));
        }
    };

    useEffect(() => {
        setPracticeSearchValue(query.practiceSearch);
        setTitleObjectSearchValue(query.object);
    }, [query.practiceSearch, query.object]);

    useEffect(() => {
        return () => {
            debouncedPracticeSearch.cancel();
            debouncedTitleObjectSearch.cancel();
        };
    }, [debouncedPracticeSearch, debouncedTitleObjectSearch]);

    const onChangeSelect = (event: any) => {
        const { name, value } = event.target;
        const backendValue = (value === "" || value === null || value === undefined) ? "-1" : value;
        setQuery(prev => ({ ...prev, [name]: backendValue }));
    };



    const handleChangeCheckboxes = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = event.target;
        setQuery(prev => ({ ...prev, [name]: checked }));
    };

    const onChangeMultiSelect = (name: string, values: any[]) => {
        const selectedIds = values.map(item => item.id).join(',');
        setQuery(prev => ({ ...prev, [name]: selectedIds || "-1" }));
    };

    const onDateChange = (name: string, value: Date | null) => {
        if (value) {
            const date = new Date(value);
            const formattedDate = date.toLocaleDateString("en-GB");
            setQuery(prev => ({
                ...prev,
                [name]: formattedDate,
            }));
        }
    };

    const handleClearFilters = () => {
        setQuery({ ...initialQuery });
        // Reset local search values
        setPracticeSearchValue(initialQuery.practiceSearch);
        setTitleObjectSearchValue(initialQuery.titleObjectSearch);
        // Call the parent's clear function to also clear session storage
        if (onClearFilters) {
            onClearFilters();
        }
    };

    return (
        <Box>
            {/* First row: Date pickers (15% each), then 3 dropdowns (20% each) */}
            <Grid container spacing={2} sx={{ mb: 2, alignItems: 'end' }}>
                <Grid item xs={12} sm={6} md={1.8}>
                    <DatePicker
                        label={t("Dal")}
                        value={parseDate(query.startDate)}
                        onChange={(date: Date | null) => onDateChange("startDate", date)}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={1.8}>
                    <DatePicker
                        label={t("Al")}
                        value={parseDate(query.endDate)}
                        onChange={(date: Date | null) => onDateChange("endDate", date)}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.8}>
                    <CustomSelect
                        name="person"
                        label={t("Intestatario")}
                        placeholder={t("Tutti gli intestatari")}
                        value={query.person}
                        onChange={onChangeSelect}
                        options={[
                            { label: t("Tutti gli intestatari"), value: "-1" },
                            ...(deadlinePeople || []).map((people) => ({
                                label: people.nome,
                                value: people.id,
                            }))
                        ]}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.8}>
                    <CustomSelect
                        name="deadlinesTypeSearch"
                        label={t("Tipologia")}
                        placeholder={t("Tutte le tipologie")}
                        value={query.deadlinesTypeSearch}
                        onChange={onChangeSelect}
                        options={[
                            { label: t("Tutte le tipologie"), value: "-1" },
                            ...(deadlineTypes || []).map((type) => ({
                                label: type.nome,
                                value: type.id,
                            }))
                        ]}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.8}>
                    <CustomSelect
                        name="deadlinesCategorySearch"
                        label={t("Categoria")}
                        placeholder={t("Tutte le categorie")}
                        value={query.deadlinesCategorySearch}
                        onChange={onChangeSelect}
                        options={[
                            { label: t("Tutte le categorie"), value: "-1" },
                            ...(deadlineCategories || []).map((category) => ({
                                label: category.nome,
                                value: category.id,
                            }))
                        ]}
                    />
                </Grid>
            </Grid>

            {/* Second row: Search fields matching first row widths */}
            <Grid container spacing={2} sx={{ mb: 2, alignItems: 'end' }}>
                <Grid item xs={12} sm={6} md={1.8}>
                    <SearchBar
                        placeholder={t("Cerca per pratica")}
                        value={practiceSearchValue}
                        onChange={handlePracticeSearchChange}
                        onKeyDown={handlePracticeSearchKeyDown}
                        fullWidth
                        buttonVariant="secondary"
                        aria-label={t("Cerca per pratica: nome pratica, codice archivio, codice pratica, oggetto, RG, cliente, controparte")}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={1.8}>
                    <SearchBar
                        placeholder={t("Cerca per titolo/oggetto")}
                        value={titleObjectSearchValue}
                        onChange={handleTitleObjectSearchChange}
                        onKeyDown={handleTitleObjectSearchKeyDown}
                        fullWidth
                        buttonVariant="secondary"
                        aria-label={t("Cerca per titolo/oggetto impegno")}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.8}>
                    <SelectMultiple
                        name="processed"
                        label={t("Stato")}
                        placeholder={t("Seleziona stato")}
                        width="100%"
                        options={PROCESSED_DATA}
                        selectedValues={
                            query.processed && query.processed !== "-1"
                                ? query.processed.split(',').map(id =>
                                    PROCESSED_DATA.find(item => item.id === id)
                                  ).filter(Boolean)
                                : []
                        }
                        onChange={onChangeMultiSelect}
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={2.8}>
                    <Box sx={{
                        display: 'flex',
                        pl:1,
                        flexDirection: { xs: 'column', sm: 'row' },
                        gap: { xs: 1, sm: 2 },
                        flexWrap: 'wrap',
                        alignItems: 'flex-start'
                    }}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="importantOnly"
                                    checked={query.importantOnly}
                                    onChange={handleChangeCheckboxes}
                                />
                            }
                            label={t("Importanti")}
                            labelPlacement="end"
                        />
                        <FormControlLabel
                            control={
                                <Checkbox
                                    name="poliswebFilter"
                                    checked={query.poliswebFilter}
                                    onChange={handleChangeCheckboxes}
                                />
                            }
                            label={t("Polisweb")}
                            labelPlacement="end"
                        />
                    </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={2.8} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                        variant="text"
                        onClick={handleClearFilters}
                        sx={{ textTransform: "none" }}
                    >
                        {t("Annulla ricerca")}
                    </Button>
                </Grid>
            </Grid>

        </Box>
    );
};

export default DeadlinesFilters;
