import React from "react";
import {
    <PERSON><PERSON>,
    DialogT<PERSON>le,
    IconButton,
    Divider,
    DialogContent,
    TextField,
    Button,
    Grid,
    TimePicker,
    LocalizationProvider,
    AdapterDateFns,
    Stack,
    InputLabel,
} from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { parseDate } from "../../../helpers/parseDataFormat";
import { IUpdateDeadlineParams } from "../hooks/useUpdateDeadlineObj";

interface IModifyDeadlineModalProps {
    open: boolean;
    handleDialog: () => void;
    updateDeadlineParams: IUpdateDeadlineParams;
    setUpdateDeadlineParams: React.Dispatch<React.SetStateAction<IUpdateDeadlineParams>>;
    handleUpdateDeadlineObject: (params: IUpdateDeadlineParams) => void;
    fetchData: () => void;
    selectedItems?: string[];
    mode?: 'single' | 'multiple';
}

export default function ModifyDeadlineModal(props: IModifyDeadlineModalProps) {
    const {
        open,
        handleDialog,
        updateDeadlineParams,
        setUpdateDeadlineParams,
        handleUpdateDeadlineObject,
    } = props;
    const { t } = useTranslation();

    const onDateChange = (value: Date | null) => {
        if (value) {
            const date = new Date(value);
            const formattedDate = date.toLocaleDateString("en-GB");
            setUpdateDeadlineParams((prevValue) => ({
                ...prevValue,
                data: formattedDate,
            }));
        }
    };

    const onTimeChange = (value: any) => {
        if (value) {
            // Handle both Date and Dayjs objects
            const dateValue = value instanceof Date ? value : value.toDate();
            const hours = dateValue.getHours().toString().padStart(2, '0');
            const minutes = dateValue.getMinutes().toString().padStart(2, '0');
            setUpdateDeadlineParams((prevValue) => ({
                ...prevValue,
                ora: hours,
                minuti: minutes,
            }));
        }
    };

    const handleInputChanges = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;
        setUpdateDeadlineParams({ ...updateDeadlineParams, [name]: value });
    };

    const handleUpdateDeadlineConfirm = () => {
        handleUpdateDeadlineObject(updateDeadlineParams);
    };

    // Create time value for TimePicker
    const timeValue = React.useMemo(() => {
        const today = new Date();
        today.setHours(parseInt(updateDeadlineParams.ora) || 9);
        today.setMinutes(parseInt(updateDeadlineParams.minuti) || 0);
        today.setSeconds(0);
        return today;
    }, [updateDeadlineParams.ora, updateDeadlineParams.minuti]);

    return (
        <Dialog
            open={open}
            onClose={handleDialog}
            aria-labelledby="modify-deadline-dialog-title"
            maxWidth="xs"
            fullWidth
        >
            <DialogTitle
                id="modify-deadline-dialog-title"
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    pb: 1,
                }}
            >
                {t("Modifica")}
                <IconButton onClick={handleDialog} size="small">
                    <FontAwesomeIcon icon={faTimes} />
                </IconButton>
            </DialogTitle>
            <Divider />
            <DialogContent sx={{ pt: 3, pb: 0 }}>
                <Grid container spacing={2}>
                    <Grid item xs={12}>
                        <InputLabel>{t("Nouvo oggetto")}</InputLabel>
                        <TextField
                            name="oggetto"
                            value={updateDeadlineParams.oggetto}
                            onChange={handleInputChanges}
                            variant="outlined"
                            placeholder={t("Inserisci oggetto")}
                            fullWidth
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <InputLabel>{t("Data")}</InputLabel>
                        <DatePicker
                            value={parseDate(updateDeadlineParams.data)}
                            onChange={onDateChange}
                            sx={{ width: '100%' }}
                        />
                    </Grid>
                    <Grid item xs={6}>
                        <InputLabel>{t("Ora")}</InputLabel>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                value={timeValue}
                                onChange={onTimeChange}
                                ampm={false}
                                slotProps={{
                                    textField: {
                                        sx: { width: '100%' }
                                    }
                                }}
                            />
                        </LocalizationProvider>
                    </Grid>
                </Grid>
                <Stack direction="row" spacing={2} justifyContent="flex-end" pt={4} mb={2}>
                <Button variant="outlined" onClick={handleDialog}>
                    {t("Annulla")}
                </Button>
                <Button
                    variant="contained"
                    onClick={handleUpdateDeadlineConfirm}
                >
                    {t("Salva")}
                </Button>
                </Stack>
            </DialogContent>
        </Dialog>
    );
}
