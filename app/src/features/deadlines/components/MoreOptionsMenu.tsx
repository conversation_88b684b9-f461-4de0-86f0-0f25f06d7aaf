import React, { useState } from "react";
import {
    Menu,
    <PERSON>uItem,
    ListItemText,
    IconButton,
} from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEllipsisVertical } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";

interface MoreOptionsMenuProps {
    onExportCSV: () => void;
    onPrintPDF: () => void;
    onAddEngagement: () => void;
}

const MoreOptionsMenu: React.FC<MoreOptionsMenuProps> = ({
    onExportCSV,
    onPrintPDF,
    onAddEngagement,
}) => {
    const { t } = useTranslation();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleExportCSV = () => {
        onExportCSV();
        handleClose();
    };

    const handlePrintPDF = () => {
        onPrintPDF();
        handleClose();
    };

    const handleAddEngagement = () => {
        onAddEngagement();
        handleClose();
    };

    return (
        <>
            <IconButton
                onClick={handleClick}
                size="small"
                sx={{ ml: 1 }}
                aria-controls={open ? 'more-options-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
            >
                <FontAwesomeIcon icon={faEllipsisVertical} />
            </IconButton>
            <Menu
                id="more-options-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    'aria-labelledby': 'more-options-button',
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                <MenuItem onClick={handleExportCSV}>
                    <ListItemText>{t("Esporta in CSV")}</ListItemText>
                </MenuItem>
                <MenuItem onClick={handlePrintPDF}>
                    <ListItemText>{t("Stampa")}</ListItemText>
                </MenuItem>
                <MenuItem onClick={handleAddEngagement}>
                    <ListItemText>{t("Aggiungi impegno")}</ListItemText>
                </MenuItem>
            </Menu>
        </>
    );
};

export default MoreOptionsMenu;
