export interface IDeadlinesQuery {
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: string;
    oldDeadlines: string;
    startDate: string;
    endDate: string;
    person: string;
    deadlinesTypeSearch: string;
    deadlinesCategorySearch: string;
    object: string;
    UdienzeRitardo: string;
    processed: string;
    notProcessed: string;
    close: boolean;
    isArchived: boolean;
    poliswebFilter: boolean;
    importantOnly: boolean;
    controparti: string;
    practiceSearch: string;
    titleObjectSearch: string;
    // New fields to align with calendar parameters
    calendarGroup: string;
    calendarReferent: string;
    authoritySearchId: string;
    onlyGroupEvents: boolean;
}

export interface IDeadlinesData {
    people: Array<{
        id: string;
        nome: string;
    }>;
    deadlineTypes: Array<{
        id: string;
        nome: string;
    }>;
    deadlineCategories: Array<{
        id: string;
        nome: string;
    }>;
}

export interface IDeadlineItem {
    uniqueid: string;
    listaclienti: string;
    listacontroparti: string;
    testo: string;
    titolo?: string;
    oggetto?: string;
    data: string;
    categoria: string;
    autorita: string;
    users: string;
    status: string;
    important?: string;
    codicearchivio?: string;
    descrizione?: string;
    ruologenerale?: string;
    evaso?: string;
    annotazioni?: string;
    archiveid?: string;
    full_day?: string;
    private?: string;
    tiposcadenzaColor?: string;
    tiposcadenzaNome?: string;
}
