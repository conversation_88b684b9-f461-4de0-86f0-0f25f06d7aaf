import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { getFatturazioneGrids } from "../../../../utilities/fatture/gridColumn";

export const useFattureHooks = () => {
    const [lawyers, setLawyers] = useState<any[]>([]);
    const [categorie, setCategorie] = useState<any[]>([]);
    const [sedi, setSedi] = useState<any[]>([]);
    const [office, setOffices] = useState<any[]>([]);
    const [columns, setColumns] = useState<any[]>([]);
    const [sezionali, setSezionali] = useState<any[]>([]);
    const [timeSearch, setTimeSearch] = useState<string>("");
    const [referenceType, setReferenceType] = useState<string>("");

    const fattureRequest = useGetCustom("fatture/");

    useEffect(() => {
        async function initFatture() {
            try {
                const response: any = await fattureRequest.doFetch();
                setLawyers(response.data.lawyers);
                setCategorie(response.data.categories);
                setSedi(response.data.sedi);
                setOffices(response.data.offices);
                const finalColumns: any = await getFatturazioneGrids(
                    response.data
                );
                console.log("finalColumns", finalColumns);
                setColumns(finalColumns);
                setTimeSearch(response.data.timeSearch);
                setSezionali(response.data.sezionali);
                setReferenceType(response.data.referenceType);
            } catch (error) {
                console.log("fatture error", error);
                return;
            }
        }

        if (lawyers.length === 0) {
            initFatture();
        }
    }, []);

    return {
        lawyers,
        columns,
        categorie,
        sedi,
        office,
        timeSearch,
        sezionali,
        referenceType,
    };
};
