import FattureList from "./FattureList";

export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    page: 0,
    pageSize: 10,
    sortColumn: "data",
    sortOrder: "desc",
    startDateSearch: "",
    endDateSearch: "",
    timeSearch: "month-next",
    motivoSearch: "",
    intestatarioSearchType: "",
    intestatarioSearch: "",
    rgnSearch: "",
    rgaSearch: "",
    sede: -1,
    emittenteSearch: -10,
    statusSearch: -1,
    sentSearch: -1,
    statoTrasformataSearch: -1,
    statoPreavvisoSearch: -1,
    progressivoSearch: "",
    sezionaleSearch: -1,
    avvocatoSearch: "",
    idCategoriaSearch: "",
    dateTypeSearch: 0,
    genericDateSearchStart: "",
    genericDateSearchEnd: "",
};

export const fatture = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/fatture",
            element: <FattureList />,
        },
    },
];
