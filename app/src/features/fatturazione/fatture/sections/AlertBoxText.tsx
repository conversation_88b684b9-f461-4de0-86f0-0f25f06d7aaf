import { useTranslation } from "@1f/react-sdk";
import { Link } from "react-router-dom";
import { useUser } from "../../../../store/UserStore";
import { NotificationInline, Stack } from "@vapor/react-material";
import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";

const referentConditions = {
    needsToPurchaseInvoicePackage: (referent: any, office_enabled: number) =>
        referent.agyo_out_enabled === -2 && office_enabled !== 1,

    needsToConfigureAccount: (referent: any) =>
        referent.agyo_out_enabled === -1,

    lowPersonalInvoices: (referent: any) =>
        referent.agyo_out_enabled === 1 &&
        referent.agyo_in - referent.agyo_out < 30,

    outOfPersonalInvoices: (referent: any) => referent.agyo_out_enabled === 0,
};

const officeConditions = {
    needsToPurchaseOfficeInvoicePackage: (
        office_enabled: number,
        agyo_out_global_enabled: number
    ) => office_enabled === -2 && agyo_out_global_enabled !== 1,

    needsToConfigureOfficeAccount: (office_enabled: number) =>
        office_enabled === -1,

    lowOfficeInvoices: (office_enabled: number, office_agyo_info: any) =>
        office_enabled === 1 &&
        office_agyo_info.agyo_in - office_agyo_info.agyo_out < 30,

    outOfOfficeInvoices: (office_enabled: number) => office_enabled === 0,
};

const generateReferentMessages = (
    t: any,
    referent: any,
    office_enabled: number
) => {
    if (
        referentConditions.needsToPurchaseInvoicePackage(
            referent,
            office_enabled
        )
    ) {
        return (
            <>
                <b>{t("Attenzione!")}</b> {t("È necessario acquistare un")}{" "}
                <b>{t("Pacchetto di fatture")}</b> {t("per l'avvocato")}{" "}
                <b>{referent.nome_avvocato}</b> {t("per poter inviare")}{" "}
                <b>{t("Fatture Elettroniche")}</b>
                <Link
                    style={{ textDecoration: "underline" }}
                    to="/legacy/agyo">
                    {" "}
                    <b>{t("Qui ")}</b>
                </Link>
                {t("Il link per l'acquisto)")}.
            </>
        );
    }

    if (referentConditions.needsToConfigureAccount(referent)) {
        return (
            <>
                <b>{t("Attenzione!")}</b>{" "}
                {t("È necessario configurare l'account di")}{" "}
                <b>{referent.nome_avvocato}</b> {t("per poter inviare")}{" "}
                <b>{t("Fatture Elettroniche")}</b>.
                <br />
                <span>
                    <a
                        data-lawyer={referent.id}
                        href="#">
                        {t("Configura Account Personale")}
                    </a>
                </span>
            </>
        );
    }

    if (referentConditions.lowPersonalInvoices(referent)) {
        return (
            <>
                <b>{t("Attenzione!")}</b> {t("Hai ancora a disposizione")}{" "}
                <b>{referent.agyo_in - referent.agyo_out}</b>{" "}
                {t("Fatture personali.")}
            </>
        );
    }

    if (referentConditions.outOfPersonalInvoices(referent)) {
        return (
            <>
                <b>{t("Attenzione!")}</b>
                {t("Hai terminato le fatture personali a tua disposizione.")}
            </>
        );
    }

    return null;
};

const generateOfficeMessages = (
    t: any,
    user: any,
    office_enabled: number,
    agyo_out_global_enabled: number,
    office_agyo_info: any
) => {
    if (
        officeConditions.needsToPurchaseOfficeInvoicePackage(
            office_enabled,
            agyo_out_global_enabled
        )
    ) {
        return (
            <>
                <b>{t("Attenzione!")}</b> {t("È necessario aver acquistato un")}{" "}
                <b>{t("Pacchetto di fatture")}</b> {t("per poter inviare")}{" "}
                <b>{t("Fatture Elettroniche")}</b>{" "}
                {t("Come Studio Associato (")}
                <Link
                    style={{ textDecoration: "underline" }}
                    to="/legacy/agyo">
                    {" "}
                    <b>{t("Qui ")}</b>
                </Link>
                {t("Il link per l'acquisto).")}
            </>
        );
    }

    if (officeConditions.needsToConfigureOfficeAccount(office_enabled)) {
        return (
            <>
                <b>{t("Attenzione!")}</b>{" "}
                {t(
                    "È necessario configurare l'account dello studio per poter inviare Fatture Elettroniche come"
                )}{" "}
                <b>{t("Studio Associato")}</b>.
                <br />
                {user?.isSuperAdmin && (
                    <Link to="/legacy/officesettings">
                        <b> {t("Configura Account")}</b>
                    </Link>
                )}
            </>
        );
    }

    if (officeConditions.lowOfficeInvoices(office_enabled, office_agyo_info)) {
        return (
            <>
                <b>{t("Attenzione!")}</b> {t("Hai ancora a disposizione")}{" "}
                <b>{office_agyo_info.agyo_in - office_agyo_info.agyo_out}</b>{" "}
                {t("Fatture dello studio associato.")}
            </>
        );
    }

    if (officeConditions.outOfOfficeInvoices(office_enabled)) {
        return (
            <>
                <b>{t("Attenzione!")}</b>
                {t("Hai terminato le fatture dello studio a tua disposizione.")}
            </>
        );
    }

    return null;
};

export interface AgyoReferent {
    agyo: string;
    id: string;
    agyo_out: string;
    agyo_in: string;
    agyo_access_token: string;
    scadenza: string;
    nome_avvocato: string;
    agyo_id: string;
    agyo_out_enabled: number;
}

export interface OfficeAgyoInfo {
    agyo_out: number;
    agyo_in: number;
}

export interface AgyoConfig {
    show_load_xml: boolean;
    office_agyo_info: OfficeAgyoInfo;
    office_enabled: number;
    agyo_out_enabled: number;
    agyo_out_global_enabled: number;
    alert: number;
    agyoReferents: {
        [key: string]: AgyoReferent;
    };
    isSuperAdmin: boolean;
}

export const AlertBoxText = () => {
    const [data, setData] = useState<AgyoConfig | null>();

    const agyoRequest = useGetCustom("fatture/agyo-info?noTemplateVars=true");

    useEffect(() => {
        agyoRequest.doFetch(true);
    }, []);

    useEffect(() => {
        if (agyoRequest.hasLoaded) {
            setData(agyoRequest.data);
        }
    }, [agyoRequest.hasLoaded]);

    const { user }: any = useUser();
    const { t } = useTranslation();

    if (!data) return null;

    return (
        <Stack gap={1}>
            {Object.entries(data.agyoReferents).map(([key, referent]) => {
                const referentMessage = generateReferentMessages(
                    t,
                    referent,
                    data.office_enabled
                );
                return referentMessage ? (
                    <NotificationInline
                        key={key}
                        variant="outlined"
                        severity="info">
                        <div>{referentMessage}</div>
                    </NotificationInline>
                ) : null;
            })}

            {generateOfficeMessages(
                t,
                user,
                data.office_enabled,
                data.agyo_out_global_enabled,
                data.office_agyo_info
            ) && (
                <NotificationInline
                    variant="outlined"
                    severity="info">
                    <div>
                        {generateOfficeMessages(
                            t,
                            user,
                            data.office_enabled,
                            data.agyo_out_global_enabled,
                            data.office_agyo_info
                        )}
                    </div>
                </NotificationInline>
            )}
        </Stack>
    );
};
