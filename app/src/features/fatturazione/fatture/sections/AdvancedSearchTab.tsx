import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@vapor/react-material";

import { useTranslation } from "@1f/react-sdk";
import { TabPanel } from "../../components/TabPanel";

export const AdvancedSearchTab = (props: any) => {
  const {
    value,
    defaultParams,
    onChangeInput,
    onSubmit,
    onClickReset,
    filterData,
  } = props;

  const { categorie, lawyers } = filterData;

  const { t } = useTranslation();

  return (
    <TabPanel value={value} index={1}>
      <Box display="flex" alignItems="end" gap={2}>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <InputLabel id="select-label">Avvocato pratica:</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.avvocatoSearch}
            label="Tutti gli avvocati"
            onChange={onChangeInput}
            name="avvocatoSearch"
          >
            <MenuItem value="-1">{t("Tutti gli avvocati")}</MenuItem>
            {lawyers?.map((lawyer: any) => (
              <MenuItem key={lawyer.id} value={lawyer.id}>
                {lawyer.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <InputLabel id="select-label">Categoria pratica:</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.idCategoriaSearch}
            label="Tutte le categorie"
            onChange={onChangeInput}
            name="idCategoriaSearch"
          >
            <MenuItem value="-1">{t("Tutte le categorie")}</MenuItem>
            {categorie?.map((category: any) => (
              <MenuItem key={category.id} value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Button
          variant="contained"
          color="primary"
          type="submit"
          onClick={onSubmit}
        >
          Cerca
        </Button>

        <Button variant="contained" color="primary" onClick={onClickReset}>
          Mostra tutti
        </Button>
      </Box>
    </TabPanel>
  );
};
