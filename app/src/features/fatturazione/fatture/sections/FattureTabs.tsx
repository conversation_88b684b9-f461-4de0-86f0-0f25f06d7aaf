import { Box } from "@vapor/react-material";
import React from "react";
import { Tab, Tabs } from "@vapor/react-extended";
import { useFattureHooks } from "../hooks/useFattureHooks";
import { useTranslation } from "@1f/react-sdk";
import { a11yProps } from "../../components/TabPanel";
import { SearchTab } from "./SearchTab";
import { AdvancedSearchTab } from "./AdvancedSearchTab";
import { DateSearch } from "./DateSearch";

export const FattureTabs = (props: any) => {
    const [value, setValue] = React.useState(0);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const {
        params: defaultParams,
        onChangeFunctions,
        date,
        genericDate,
        multiSelectValues,
        selectedValues,
        typologyMultiselect,
        filterData,
    } = props;

    const { t } = useTranslation();

    const isFattura =
        typologyMultiselect.length === 1 &&
        typologyMultiselect.find((item: any) => item === "Fatture");

    const {
        onChangeCheckbox,
        onChangeInput,
        onClickReset,
        onSubmit,
        onDateChange,
        onTimeSearchChange,
        handleChangeMultiSelect,
    } = onChangeFunctions;

    const { referenceType } = useFattureHooks();

    const [anchorEl, setAnchorEl] = React.useState(null);
    const handleClick = (event: any) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const open = Boolean(anchorEl);
    const id = open ? "simple-popover" : undefined;

    return (
        <>
            <Box>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label={t("Ricerca")} {...a11yProps(0)} />
                    <Tab label={t("Ricerca avanzata(1)")} {...a11yProps(1)} />
                    <Tab label={t("Ricerca per data")} {...a11yProps(2)} />
                    {referenceType != "2" && referenceType != "0" && (
                        <div
                            style={{
                                marginTop: "10px",
                                marginLeft: "auto",
                                color: "#006dcc",
                            }}
                        >
                            <b>{t("Codice Destinatario: M5UXCR1")}</b>
                        </div>
                    )}
                </Tabs>
            </Box>
            <SearchTab
                value={value}
                date={date}
                onDateChange={onDateChange}
                onTimeSearchChange={onTimeSearchChange}
                defaultParams={defaultParams}
                handleClick={handleClick}
                anchorEl={anchorEl}
                handleClose={handleClose}
                onChangeInput={onChangeInput}
                isFattura={isFattura}
                onSubmit={onSubmit}
                onClickReset={onClickReset}
                selectedValues={selectedValues}
                multiSelectValues={multiSelectValues}
                handleChangeMultiSelect={handleChangeMultiSelect}
                onChangeCheckbox={onChangeCheckbox}
                open={open}
                id={id}
                filterData={filterData}
            />
            <AdvancedSearchTab
                value={value}
                defaultParams={defaultParams}
                onChangeInput={onChangeInput}
                onSubmit={onSubmit}
                onClickReset={onClickReset}
                filterData={filterData}
            />
            <DateSearch
                value={value}
                onDateChange={onDateChange}
                defaultParams={defaultParams}
                onChangeInput={onChangeInput}
                onSubmit={onSubmit}
                onClickReset={onClickReset}
                genericDate={genericDate}
            />
        </>
    );
};
