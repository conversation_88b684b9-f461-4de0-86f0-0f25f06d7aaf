import { Box } from "@vapor/react-material";

import { ReactNode, ReactElement } from "react";

type Props = {
  children: ReactNode;
};

export const InformationBox = ({ children }: Props): ReactElement => {
  return (
    <Box
      component="section"
      sx={{
        padding: "15px",
        textAlign: "left",
        color: "#3a87ad",
        backgroundColor: "#d9edf7",
        width: "auto",
        height: "fit-content",
        borderColor: "#bce8f1",
      }}
    >
      {children}
    </Box>
  );
};
