const now = new Date();

export const currentDateFilter = {
  startDateSearch: new Date(now.getFullYear(), 0, 1),
  endDateSearch: new Date(now.getFullYear(), 11, 31),
};

const getDays = (year: number, month: number) => {
  return new Date(year, month, 0).getDate();
};

export const formatDate = (date: Date) => {
  if (!date) {
    return null;
  }
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

export const updateParams = (
  params: any,
  date: any,
  genericDate: any,
  selectedValues: any[],
  multiSelectedValues: any[]
) => {
  const { startDateSearch, endDateSearch } = date;
  const { genericDateSearchStart, genericDateSearchEnd } = genericDate;
  if (startDateSearch && startDateSearch instanceof Date) {
    params.startDateSearch = formatDate(startDateSearch);
  }

  if (endDateSearch && endDateSearch instanceof Date) {
    params.endDateSearch = formatDate(endDateSearch);
  }

  if (genericDateSearchStart && genericDateSearchStart instanceof Date) {
    params.genericDateSearchStart = formatDate(genericDateSearchStart);
  }

  if (genericDateSearchEnd && genericDateSearchEnd instanceof Date) {
    params.genericDateSearchEnd = formatDate(genericDateSearchEnd);
  }
  if (params.advanceSearch) {
    params.advanceSearch = "on";
  }
  if (params.detraiNoteCredito) {
    params.detraiNoteCredito = "on";
  }

  if (selectedValues.length > 0) {
    params["tipoSearch[]"] = selectedValues.map((element: any) => {
      const selectedValue = multiSelectedValues.find(
        (el: any) => el.label === element
      );
      if (selectedValue) {
        return selectedValue.value;
      }
    });
  } else {
    delete params["tipoSearch[]"];
  }

  return params;
};

export const getDateFiltersByValue = (value: string) => {
  const currentYear = now.getFullYear();
  const currentMonth = now.getUTCMonth();
  let filterDates = currentDateFilter;
  if (value === "year-current") {
    filterDates = currentDateFilter;
  }

  if (value === "year-prev") {
    const prevYear = currentYear - 1;
    filterDates = {
      startDateSearch: new Date(prevYear, 0, 1),
      endDateSearch: new Date(prevYear, 11, 31),
    };
  }

  if (value === "year-next") {
    const nextYear = currentYear + 1;
    filterDates = {
      startDateSearch: new Date(nextYear, 0, 1),
      endDateSearch: new Date(nextYear, 11, 31),
    };
  }

  if (value === "month-current") {
    const lastDayMonth = getDays(currentYear, currentMonth + 1);
    filterDates = {
      startDateSearch: new Date(currentYear, currentMonth, 1),
      endDateSearch: new Date(currentYear, currentMonth, lastDayMonth),
    };
  }

  if (value === "month-prev") {
    const monthPrev = currentMonth - 1;
    const lastDayMonthPrev = getDays(currentYear, currentMonth);
    filterDates = {
      startDateSearch: new Date(currentYear, monthPrev, 1),
      endDateSearch: new Date(currentYear, monthPrev, lastDayMonthPrev),
    };
  }

  if (value === "month-next") {
    const monthNext = currentMonth + 1;
    const lastDayMonthNext = getDays(currentYear, monthNext + 1);

    filterDates = {
      startDateSearch: new Date(currentYear, monthNext, 1),
      endDateSearch: new Date(currentYear, monthNext, lastDayMonthNext),
    };
  }

  return filterDates;
};

export const mapDataWIthShortValues = (currentData: any[]) => {
  const dataToReturn = currentData?.map((item: any) => {
    const { intestatario, listaclienti, ...rest } = item;
    return {
      intestatario:
        intestatario && intestatario.length > 15
          ? intestatario?.substring(0, 15) + " ..."
          : intestatario || "",
      listaclienti:
        listaclienti && listaclienti.length > 15
          ? listaclienti?.substring(0, 15) + " ..."
          : listaclienti || "",
      ...rest,
    };
  });

  return dataToReturn;
};

/* This is an array with empty values that matched the columns length */
export const totalValuesArray = [
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
  "",
];

function formatNumber(numberString: string): string {
  const number = parseFloat(numberString.replace(",", "."));
  if (isNaN(number)) {
    return "";
  }

  const [integerPart, decimalPart] = number.toFixed(2).split(".");
  const formattedIntegerPart = integerPart.replace(
    /\B(?=(\d{3})+(?!\d))/g,
    "."
  );

  return `${formattedIntegerPart},${decimalPart}`;
}

export const mapTotalValueArray = (selectedValues: any[], totals: any) => {
  const newValues =
    selectedValues.length === 1
      ? [
          "Totali (€)",
          formatNumber(totals.totale_iva),
          formatNumber(totals.totale),
          formatNumber(totals.scoperto),
          formatNumber(totals.importofatturato),
        ]
      : ["Per visualizzare i Totali filtra per Tipologia", "", "", "", ""];

  const lastIndex = totalValuesArray.length - 1;

  const valuesWithTotalFields = [
    ...totalValuesArray.slice(0, lastIndex - newValues.length + 1),
    ...newValues,
  ];

  return valuesWithTotalFields;
};
