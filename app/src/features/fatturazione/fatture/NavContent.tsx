import { useTranslation } from "@1f/react-sdk";
import { Link, useLocation } from "react-router-dom";

import List from "@vapor/react-material/List";
import ListItemButton from "@vapor/react-material/ListItemButton";
import ListItemIcon from "@vapor/react-material/ListItemIcon";
import ListItemText from "@vapor/react-material/ListItemText";
import GroupIcon from "@mui/icons-material/Group";
import VisibleWhenLogged from "../../../hoc-components/VisibleWhenLogged";

export const NavContent = () => {
  const { t } = useTranslation();
  const selected = useLocation().pathname === "/fatture";

  return (
    <VisibleWhenLogged>
      <List>
        <ListItemButton selected={selected} component={Link} to={"/fatture/"}>
          <ListItemIcon>
            <GroupIcon />
          </ListItemIcon>
          <ListItemText primary={t("Fatture")} />
        </ListItemButton>
      </List>
    </VisibleWhenLogged>
  );
};
