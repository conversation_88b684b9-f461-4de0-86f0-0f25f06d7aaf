import VaporPage from "@vapor/react-custom/VaporPage";
import Spinner from "../../../custom-components/Spinner";
import { FattureTabs } from "./sections/FattureTabs";
import { useFattureHooks } from "./hooks/useFattureHooks";
import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { DEFAULT_LIST_PARAMS } from ".";
import { useTranslation } from "@1f/react-sdk";
import VaporHeaderBar from "@vapor/react-custom/VaporHeaderBar";
import { useNavigate } from "react-router-dom";
import { AlertBoxText } from "./sections/AlertBoxText";
import {
    currentDateFilter,
    getDateFiltersByValue,
    mapDataWIthShortValues,
    mapTotalValueArray,
    totalValuesArray,
    updateParams,
} from "./utils/index";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

const FattureList = () => {
    const navigate = useNavigate();
    const [data, setData] = useState<any[]>([]);

    const [totalRows, setTotalRows] = useState<number>(0);
    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);

    const { t } = useTranslation();

    const multiSelectValues = [
        { value: "0", label: t("Fatture") },
        { value: "1", label: t("Nota di credito") },
        { value: "2", label: t("Preavviso di parcella") },
        { value: "3", label: t("Fattura elettronica") },
        { value: "4", label: t("Nota di credito elettronica") },
        { value: "5", label: t("Fattura d'acquisto") },
        { value: "10", label: t("Nota di debito elettronica") },
    ];

    const [date, setDate] = useState<any>(currentDateFilter);
    const [genericDate, setGenericDate] = useState<any>({
        genericDateSearchStart: null,
        genericDateSearchEnd: null,
    });

    const [typologyMultiselect, setTypologyMultiselect] = useState<any>([]);
    const [selectedValues, setSelectedValues] = useState<string[]>([]);

    const handleChangeMultiSelect = (_event: any, values: any[]) => {
        setTypologyMultiselect(values.map((e) => e.label));
        setSelectedValues(values);
    };

    const { columns, timeSearch, lawyers, sedi, office, sezionali, categorie } =
        useFattureHooks();

    const [, setTotalValues] = useState<any[]>(totalValuesArray);

    useEffect(() => {
        if (timeSearch) {
            onTimeSearchChange(timeSearch);
        }
    }, [timeSearch]);

    const fattureListRequest = useGetCustom(
        "fatture/list",
        updateParams(
            defaultParams,
            date,
            genericDate,
            selectedValues,
            multiSelectValues
        )
    );

    const defaultFattureListRequest = useGetCustom(
        "fatture/list",
        updateParams(
            DEFAULT_LIST_PARAMS,
            date,
            genericDate,
            selectedValues,
            multiSelectValues
        )
    );

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        selectedValues.length,
    ]);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultFattureListRequest.doFetch(true)
            : fattureListRequest.doFetch(true));

        const { currentPage, totalRows, totals } = response.data;

        setTotalValues(mapTotalValueArray(selectedValues, totals));

        setData(mapDataWIthShortValues(currentPage));
        setTotalRows(totalRows);
    };

    const onSubmit = () => {
        startSearchList();
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const onDateChange = (name: string, value: Date) => {
        if (name.startsWith("generic")) {
            return setGenericDate((date: any) => ({ ...date, [name]: value }));
        }
        setDate((date: any) => ({ ...date, [name]: value }));
    };

    const onChangeCheckbox = (e: any) => {
        const { name, checked } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: checked || "",
        });
    };

    const onClickReset = () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);
        startSearchList(true);
        setDate(currentDateFilter);
        setGenericDate({
            genericDateSearchStart: null,
            genericDateSearchEnd: null,
        });
        setSelectedValues([]);
    };

    const handleClickCallback = (uniqueid: any) => {
        navigate(`/legacy/fatturecomplex/viewfe?uid=${uniqueid}`);
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="fatture"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                totalRows={totalRows}
                loading={
                    fattureListRequest.loading ||
                    defaultFattureListRequest.loading
                }
                query={defaultParams}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                page={defaultParams.page}
                pageSize={defaultParams.pageSize}
            />
        );
    };

    const onTimeSearchChange = (value: string) => {
        setDate(getDateFiltersByValue(value));
        setDefaultParams({
            ...defaultParams,
            timeSearch: value,
        });
    };

    const onChangeFunctions = {
        onChangeCheckbox,
        onChangeInput,
        onDateChange,
        onClickReset,
        onPageChangeCallback,
        onSubmit,
        onTimeSearchChange,
        handleChangeMultiSelect,
    };

    const filterData = { lawyers, sedi, office, sezionali, categorie };

    return (
        <VaporPage>
            <VaporPage.Section>
                <AlertBoxText />
            </VaporPage.Section>

            <VaporHeaderBar title={t("Gestione Fatture")} />
            <VaporPage.Section>
                <FattureTabs
                    params={defaultParams}
                    onChangeFunctions={onChangeFunctions}
                    date={date}
                    genericDate={genericDate}
                    multiSelectValues={multiSelectValues}
                    selectedValues={selectedValues}
                    typologyMultiselect={typologyMultiselect}
                    filterData={filterData}
                />
            </VaporPage.Section>
            <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
        </VaporPage>
    );
};

export default FattureList;
