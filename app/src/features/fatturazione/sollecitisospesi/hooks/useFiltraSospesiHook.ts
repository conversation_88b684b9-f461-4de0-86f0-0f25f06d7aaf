import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";

import { useTranslation } from "@1f/react-sdk";
import { RiepilogoCreateLetterActions } from "../../../../interfaces/fatturazione.interface";
import { getPaymentReminderGrid } from "../../../../utilities/paymentReminder/gridColumn";
import { getRiepilogoGrid } from "../../../../utilities/riepilogo/gridColumn";

export const useFiltraSospesiHook = (
    actions?: RiepilogoCreateLetterActions
) => {
    const { t } = useTranslation();
    const [columns, setColumns] = useState<any[]>([]);
    const [lawyers, setLawyers] = useState<any[]>([]);
    const [letterColumns, setLetterColumns] = useState<any[]>([]);
    const [lettersJSON, setLettersJson] = useState<any[]>([]);
    const [sendersData, setSendersData] = useState<any[]>([]);

    const sollecitiRequest = useGetCustom("paymentreminder");

    useEffect(() => {
        if (
            !columns.length ||
            !letterColumns.length ||
            !lawyers.length ||
            sendersData.length
        )
            initSollecitiSospesi();
    }, []);

    async function initSollecitiSospesi() {
        try {
            const [response, finalColumns]: any = await Promise.all([
                sollecitiRequest.doFetch(true),
                getPaymentReminderGrid(t),
            ]);
            const { lettersJSON, senders, lawyers } = response?.data;

            setLawyers(lawyers);
            setColumns(finalColumns);
            if (lettersJSON && actions) {
                setLettersJson(JSON.parse(lettersJSON));
                const lettersJSONColumns: any = await getRiepilogoGrid(
                    t,
                    actions
                );
                setSendersData(senders);
                setLetterColumns(lettersJSONColumns);
            }
        } catch (error) {
            console.log("Solleciti Sospesi error", error);
            // return;
        }
    }

    return {
        t,
        columns,
        lawyers,
        lettersJSON,
        letterColumns,
        senders: sendersData,
        fetchAgain: initSollecitiSospesi,
    };
};
