import {
    Box,
    Button,
    Dialog,
    DialogTitle,
    Divider,
    DialogActions,
    IconButton,
    DialogContent,
    Stack,
    FormControl,
    InputLabel,
} from "@vapor/react-material";
import FormInput from "../../../../custom-components/FormInput";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { useState } from "react";
import CloseIcon from "@mui/icons-material/Close";
import { Typography } from "@vapor/react-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useTranslation } from "react-i18next";

export const SendMessageModal = (props: {
    open: boolean;
    handleClose: () => void;
    senders: any[];
    row: any;
    fetchAgain: () => void;
}) => {
    const { open, handleClose, senders, row, fetchAgain } = props;
    const { t } = useTranslation();
    const [senderValues] = useState(
        senders.map((item: any) => ({
            label: item.email,
            value: item.id,
        }))
    );

    const senByEmailRequest = usePostCustom(
        "paymentreminder/send-by-email?noTemplateVars=true"
    );

    const schema = yup.object().shape({
        letterId: yup.string(),
        sender: yup.object(),
        recipient: yup
            .string()
            .email("Invalid email address")
            .required("Recipient is required"),
        subject: yup.string().required(),
        message: yup.string(),
    });

    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
    } = useForm({
        resolver: yupResolver(schema),
        defaultValues: {
            letterId: row.id,
            sender: senderValues[0],
            recipient: "",
        },
    });

    const onSubmit = async (values: any) => {
        values.sender = values.sender.value;
        const response: any = await senByEmailRequest.doFetch(true, values);

        if (!response?.data?.error) {
            handleClose();
            fetchAgain();
        }
    };

    return (
        <>
            <Dialog
                open={open}
                onClose={handleClose}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t("Lettera di sollecito - Invio per email")}
                    <IconButton color="primary" onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: 350,
                                },
                            }}
                        >
                            <div>
                                <Stack>
                                    <FormInput
                                        sx={{
                                            width: "95%",
                                            margin: "8px",
                                        }}
                                        control={control}
                                        name="sender"
                                        label={t("Mittente")}
                                        type="select"
                                        variant="outlined"
                                        onChange={(data: any) => {
                                            setValue("sender", data.target);
                                        }}
                                        options={senders.map((item: any) => ({
                                            label: item.email,
                                            value: item.id,
                                        }))}
                                    />
                                    <FormInput
                                        control={control}
                                        name="recipient"
                                        label={t("Destinatario*")}
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["recipient"] &&
                                            errors["recipient"]["message"] != ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["recipient"] &&
                                            errors["recipient"]["message"]
                                        }
                                    />

                                    <FormControl
                                        variant="outlined"
                                        sx={{
                                            width: "95%",
                                            paddingLeft: "7px",
                                        }}
                                    >
                                        <InputLabel id="select-label">
                                            {t("Allegato:")}
                                        </InputLabel>
                                        <Typography>
                                            <InsertDriveFileIcon />{" "}
                                            {row?.filename}
                                        </Typography>
                                    </FormControl>
                                    <FormInput
                                        control={control}
                                        name="subject"
                                        label={t("Oggeto*")}
                                        type="text"
                                        variant="outlined"
                                        setValue={setValue}
                                        error={
                                            errors &&
                                            errors["subject"] &&
                                            errors["subject"]["message"] != ""
                                                ? true
                                                : false
                                        }
                                        helperText={
                                            errors &&
                                            errors["subject"] &&
                                            errors["subject"]["message"]
                                        }
                                    />
                                    <FormInput
                                        control={control}
                                        name="message"
                                        label={t("Messaggio*")}
                                        type="textarea"
                                        variant="outlined"
                                        setValue={setValue}
                                        multiline
                                        rows={8}
                                    />
                                </Stack>
                            </div>
                        </Box>
                    </DialogContent>

                    <DialogActions>
                        <Button
                            type="submit"
                            variant="contained"
                            sx={{
                                mr: 1,
                            }}
                        >
                            {t("Conferma")}
                        </Button>
                    </DialogActions>
                </form>
            </Dialog>
        </>
    );
};
