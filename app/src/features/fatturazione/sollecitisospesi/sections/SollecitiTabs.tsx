import { Box } from "@vapor/react-material";
import React from "react";
import { Tabs, Tab } from "@vapor/react-extended";
import FormatListNumberedIcon from "@mui/icons-material/FormatListNumbered";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import EmailIcon from "@mui/icons-material/Email";
import { FiltraSospesi } from "./FiltraSospesi";
import { RiepilogoCreateLettera } from "./RiepilogoCreateLettera";
import { LetterLevel } from "./LetterLevel";
import { useTranslation } from "react-i18next";

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}
export const SollecitiTabs = () => {
    const [value, setValue] = React.useState(0);
    const [level, setLevel] = React.useState<string>("1");
    const [sollecitiContentEditor, setSollecitiContentEditor] =
        React.useState<any>({});
    const { t } = useTranslation();
    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const changeLevel = async (e: React.ChangeEvent<HTMLInputElement>) => {
        setLevel(e.target.value);
    };

    return (
        <>
            <Box>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                    aria-label="icon position tabs example"
                >
                    <Tab
                        icon={<FormatListNumberedIcon />}
                        iconPosition="start"
                        label={t("Scegli livello lettera di sollecito")}
                        {...a11yProps(0)}
                    />

                    <Tab
                        icon={<FilterAltIcon />}
                        iconPosition="start"
                        label={t(" Filtra i sospesi")}
                        {...a11yProps(1)}
                    />
                    <Tab
                        icon={<EmailIcon />}
                        iconPosition="start"
                        label={t("  Riepilogo lettere create")}
                        {...a11yProps(2)}
                    />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
                <Box sx={{ p: 1 }}>
                    <LetterLevel
                        level={level}
                        changeLevel={changeLevel}
                        sollecitiContentEditor={sollecitiContentEditor}
                        setSollecitiContentEditor={setSollecitiContentEditor}
                    />
                </Box>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Box sx={{ p: 1 }}>
                    <FiltraSospesi
                        level={level}
                        sollecitiContentEditor={sollecitiContentEditor}
                    />
                </Box>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={2}>
                <RiepilogoCreateLettera />
            </CustomTabPanel>
        </>
    );
};
