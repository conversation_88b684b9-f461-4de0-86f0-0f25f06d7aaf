import React from "react";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useTranslation } from "@1f/react-sdk";
import {
    FormControl,
    InputLabel,
    MenuItem,
    Select,
} from "@vapor/react-material";

export const LetterLevel = (props: {
    level: string;
    changeLevel: (e: any) => void;
    sollecitiContentEditor: any;
    setSollecitiContentEditor: React.Dispatch<React.SetStateAction<any>>;
}) => {
    const {
        level,
        changeLevel,
        sollecitiContentEditor,
        setSollecitiContentEditor,
    } = props;


    const headerRef = React.useRef(null);
    const { t } = useTranslation();
    const paymentRequest = useGetCustom("paymentreminder?noTemplateVars=true");

    React.useEffect(() => {
        getData(level);
    }, [level]);

    const getData = async (level: any) => {
        const response: any = await paymentRequest.doFetch(true, { level });
        if (response?.data?.letteraContent) {
            setSollecitiContentEditor(response?.data?.letteraContent);
        }
    };

    return (
        <>
            <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                <InputLabel id="select-label">{t("Livello")}</InputLabel>
                <Select
                    labelId="select-label"
                    value={level}
                    label="Livello"
                    onChange={changeLevel}
                    name="level"
                >
                    <MenuItem value="1">{t("1° Livello")}</MenuItem>
                    <MenuItem value="2">{t("2° Livello")}</MenuItem>
                    <MenuItem value="3">{t("3° Livello")}</MenuItem>
                </Select>
            </FormControl>
            <br />
            <br />
            <FormControl>
                <InputLabel>{t("Lettera di sollecito")}</InputLabel>
                <CKEditor
                    ref={headerRef}
                    config={{
                        toolbar: [
                            "heading",
                            "|",
                            "bold",
                            "italic",
                            "blockQuote",
                            "link",
                            "numberedList",
                            "bulletedList",
                            "uploadImage",
                            "insertTable",
                            "mediaEmbed",
                            "|",
                            "undo",
                            "redo",
                        ],
                    }}
                    editor={ClassicEditor as any}
                    data={sollecitiContentEditor}
                    onReady={(editor: any) => {
                        editor?.editing?.view.change((writer: any) => {
                            writer.setStyle(
                                "min-height",
                                "400px",
                                editor.editing.view.document.getRoot()
                            );

                            writer.setStyle(
                                "color",
                                "#343a40",
                                editor.editing.view.document.getRoot()
                            );
                        });
                    }}
                />
            </FormControl>
        </>
    );
};
