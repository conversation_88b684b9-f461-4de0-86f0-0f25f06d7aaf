import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import {
    GridPaginationModel,
    GridCallbackDetails,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import Spinner from "../../../../custom-components/Spinner";
import { FiltraSospesiFilters } from "./FiltraSospesiFilters";
import { useFiltraSospesiHook } from "../../sollecitisospesi/hooks/useFiltraSospesiHook";
import { useEffect, useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { DEFAULT_LIST_PARAMS } from "../../sollecitisospesi/index";
import { useLocation } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { Box, Button } from "@vapor/react-material";
import DownloadIcon from "@mui/icons-material/Download";
import CheckIcon from "@mui/icons-material/Check";
import {
    mapDataWIthShortValues,
    mapTotalValueArray,
    totalValuesArray,
    updateParams,
} from "./../utils/index";
import CreaLetteraModal from "./CreaLetteraModal";

export const FiltraSospesi = (props: {
    level: string | number;
    sollecitiContentEditor: any;
}) => {
    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [defaultParams, setDefaultParams] = useState(DEFAULT_LIST_PARAMS);
    const [selectedValues, setSelectedValues] = useState<string[]>([]);
    const { columns, lawyers, fetchAgain } = useFiltraSospesiHook();
    const [, setTotalValues] = useState<any[]>(totalValuesArray);
    const [selectedRows] = useState<any[]>([]);
    const { level, sollecitiContentEditor } = props;

    console.log("lawyers", lawyers);

    const [open, setOpen] = useState(false);
    const [show, setShow] = useState(false);
    const handleDialog = () => {
        if (selectedRows.length > 0) {
            setOpen(!open);
        } else {
            setShow(true);
        }
    };

    const [date, setDate] = useState({
        startDate: null,
        endDate: null,
    });

    const { t } = useTranslation();
    const location = useLocation();

    const paymentListRequest = useGetCustom(
        "paymentreminder/list",
        updateParams(defaultParams, date)
    );

    const downloadRequest = useGetCustom(
        "paymentreminder/export-documents",
        updateParams(defaultParams, date)
    );

    const defaultPaymentList = useGetCustom(
        "paymentreminder/list",
        updateParams(DEFAULT_LIST_PARAMS, date)
    );

    const createLetteraRequest = usePostCustom(
        "paymentreminder/generate-lettera-pdf?noTemplateVars=true",
        {}
    );

    useEffect(() => {
        if (location) {
            const searchParams = new URLSearchParams(location.search);
            const nome = searchParams.get("data");
            setDefaultParams((prevParams: any) => ({
                ...prevParams,
                searchField: nome,
            }));
        }
    }, []);

    useEffect(() => {
        startSearchList();
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        selectedValues,
    ]);

    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultPaymentList.doFetch(true)
            : paymentListRequest.doFetch(true));
        console.log("response.data", response.data);
        const { currentPage, totalRows, totals } = response.data;

        setTotalValues(mapTotalValueArray(selectedValues, totals));
        setData(mapDataWIthShortValues(currentPage));
        setTotalRows(totalRows);
    };

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickCallback = (uniqueid: any) => {
        console.log(uniqueid);
    };

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;

        if (name === "tipo") {
            setSelectedValues([value]);
        }
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };
    const onDateChange = (name: string, value: Date) => {
        setDate((prevValue: any) => ({ ...prevValue, [name]: value }));
    };

    const onSubmit = () => {
        startSearchList();
    };

    const onClickReset = async () => {
        setDefaultParams(DEFAULT_LIST_PARAMS);

        setDate({ startDate: null, endDate: null });
        setSelectedValues([]);
        startSearchList(true);
    };

    const handleRowSelection = (
        rowSelectionModel: GridRowSelectionModel,
        details: GridCallbackDetails<any>
    ) => {
        console.log("details", details);
        console.log("rowSelectionModel", rowSelectionModel);
    };
    const onChangeFunctions = {
        onChangeInput,
        onDateChange,
        onClickReset,
        onPageChange,
        onSubmit,
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="paymentreminder"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                totalRows={totalRows}
                loading={
                    paymentListRequest.loading || defaultPaymentList.loading
                }
                // onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickCallback={onClickCallback}
                selectableRows
                onRowSelectionModelChange={handleRowSelection}
                page={defaultParams.page}
                pageSize={defaultParams.pageSize}
                onClickKey="progressivo"
            />
        );
    };

    const exportDocs = async () => {
        const response: any = await downloadRequest.doFetch(true);
        const blob = new Blob([response.data], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "documenti_solleciti_sospesi.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const createLetter = async (values: any[]) => {
        const valuesToSend: any = {
            activeTab: "#documents-tab",
            level: defaultParams.level,
            startDate: defaultParams.startDate,
            endDate: defaultParams.endDate,
            searchField: defaultParams.searchField,
            emittente: defaultParams.emittente,
            avvocatoSearch: defaultParams.avvocatoSearch,
            tipo: defaultParams.tipo,
            stato: defaultParams.stato,
            searchDaysOld: defaultParams.searchDaysOld,
            rgn: defaultParams.rgn,
            rga: defaultParams.rga,
            livello: level,
            selectedCustomers: values
                .map((item: any) => item.nome_intestatario)
                .join(","),
            selectedDocuments: values.map((item: any) => item.id).join(","),
            lettera_content: sollecitiContentEditor,
        };
        const formData = new FormData();

        Object.keys(valuesToSend).forEach((key: string) => {
            formData.append(key, valuesToSend[key]);
        });

        await createLetteraRequest.doFetch(true, formData);
    };

    return (
        <>
            <Box
                component="section"
                display="flex"
                alignItems="end"
                justifyContent="flex-end"
                gap={2}
                sx={{ mb: "7px" }}
            >
                <Button
                    startIcon={<DownloadIcon />}
                    variant="outlined"
                    color="primary"
                    onClick={exportDocs}
                >
                    {t("Esporta in csv")}
                </Button>

                <Button
                    startIcon={<CheckIcon />}
                    variant="contained"
                    color="primary"
                    onClick={handleDialog}
                >
                    {t("Crea lettera")}
                </Button>
            </Box>
            <FiltraSospesiFilters
                params={defaultParams}
                onChangeFunctions={onChangeFunctions}
                lawyers={lawyers}
                date={date}
                selectedRows={selectedRows}
            />
            {selectedRows.length > 0 ? (
                <CreaLetteraModal
                    handleDialog={handleDialog}
                    open={open}
                    rows={data.filter((item) => selectedRows.includes(item.id))}
                    handleSubmit={createLetter}
                    fetchAgain={fetchAgain}
                />
            ) : (
                <ToastNotification
                    showNotification={show}
                    setShowNotification={setShow}
                    severity="error"
                    text={t(
                        "Impossibile procedere: non ci sono clienti selezionati!"
                    )}
                />
            )}

            {renderDataTable()}
        </>
    );
};
