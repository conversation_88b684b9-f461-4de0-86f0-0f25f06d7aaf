import {
    <PERSON>ton,
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Divider,
    IconButton,
    DialogContentText,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Paper,
} from "@vapor/react-material";
import { useState } from "react";
import { Close } from "@mui/icons-material";
import { useTranslation } from "react-i18next";
import ToastNotification from "../../../../custom-components/ToastNotification";

export default function CreaLetteraModal(props: {
    handleDialog: () => void;
    open: boolean;
    rows: any[];
    fetchAgain: () => void;
    handleSubmit: (values: any[]) => void;
}) {
    const { t } = useTranslation();
    const { handleDialog, open, rows, handleSubmit, fetchAgain } = props;
    const [show, setShow] = useState(false);
    const handleConfirmButton = () => {
        const response: any = handleSubmit(rows);
        if (response.status === 200) {
            handleDialog();
            fetchAgain();
            setShow(true);
        }
    };

    return (
        <>
            <Dialog
                open={open}
                scroll="paper"
                onClose={handleDialog}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {t(" Verranno create lettere per i seguenti clienti:")}
                    <IconButton color="primary" onClick={handleDialog}>
                        <Close />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />
                <DialogContent>
                    <DialogContentText>
                        <TableContainer component={Paper}>
                            <Table
                                sx={{ minWidth: 350 }}
                                aria-label="simple table"
                            >
                                <TableBody>
                                    {(rows || []).map((row: any) => {
                                        return (
                                            <TableRow
                                                key="name"
                                                sx={{
                                                    "&:last-child td, &:last-child th":
                                                        { border: 0 },
                                                }}
                                            >
                                                <TableCell
                                                    component="th"
                                                    scope="row"
                                                >
                                                    {row.nome_intestatario}
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button variant="outlined" onClick={handleDialog}>
                        {t("Annulla")}
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleConfirmButton}
                        sx={{
                            mr: 1,
                        }}
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
            <ToastNotification
                showNotification={show}
                setShowNotification={setShow}
                severity="success"
                text={t("Tutte le lettere sono state salvate correttamente!")}
            />
        </>
    );
}
