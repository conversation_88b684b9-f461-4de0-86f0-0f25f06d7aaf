import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";

import Spinner from "../../../../custom-components/Spinner";
import { useState } from "react";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useFiltraSospesiHook } from "../../sollecitisospesi/hooks/useFiltraSospesiHook";
import { useTranslation } from "@1f/react-sdk";
import { Box, Button } from "@vapor/react-material";
import DownloadIcon from "@mui/icons-material/Download";
import { RiepilogoCreateLetterActions } from "../../../../interfaces/fatturazione.interface";
import { SendMessageModal } from "./SendMessageModal";
import ConfirmModal from "../../../../custom-components/ConfirmModal";

export const RiepilogoCreateLettera = () => {
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    const [selectedId, setSelectedId] = useState("");
    const [openSendLetterModal, setOpenSendLetterModal] = useState(false);
    const [rowSelected, setRowSelected] = useState<any | undefined>();
    const downloadRequest = useGetCustom("paymentreminder/export-solleciti");
    const deleteLettera = useGetCustom(
        "paymentreminder/delete-letter?noTemplateVars=true"
    );

    const toggleDeleteModal = async (id: string) => {
        setSelectedId(id);
        setOpen(true);
    };

    const closeDeleteModal = async () => {
        setOpen(false);
    };

    const handleDeleteContent = async () => {
        const response: any = await deleteLettera.doFetch(true, {
            id: selectedId,
        });
        if (response.status === 200) {
            setOpen(false);
            fetchAgain();
        }
    };

    const handleDialog = () => {
        setOpenSendLetterModal(!openSendLetterModal);
    };

    const handleGetMessageData = (row: any) => {
        setRowSelected(row);
        handleDialog();
    };

    const actions: RiepilogoCreateLetterActions = {
        handleDelete: toggleDeleteModal,
        handleSend: handleGetMessageData,
    };
    const { lettersJSON, letterColumns, senders, fetchAgain } =
        useFiltraSospesiHook(actions);

    const exportDocs = async () => {
        const response: any = await downloadRequest.doFetch(true);
        const blob = new Blob([response.data], { type: "text/csv" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "lettere_sollecito.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const renderDataTable = () => {
        if (!lettersJSON?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="letters"
                page={0}
                pageSize={100}
                columns={letterColumns}
                data={lettersJSON}
                totalRows={lettersJSON?.length}
                onClickKey="id"
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <>
            <Box
                component={"section"}
                display="flex"
                alignItems="end"
                justifyContent="flex-end"
                gap={2}
                sx={{ mb: "7px" }}
            >
                <Button
                    startIcon={<DownloadIcon />}
                    variant="outlined"
                    color="primary"
                    onClick={exportDocs}
                >
                    {t("Esporta in csv")}
                </Button>
            </Box>

            {openSendLetterModal && (
                <SendMessageModal
                    open={openSendLetterModal}
                    senders={senders}
                    handleClose={handleDialog}
                    row={rowSelected}
                    fetchAgain={fetchAgain}
                />
            )}
            {open && (
                <ConfirmModal
                    open={open}
                    handleDecline={closeDeleteModal}
                    handleAgree={handleDeleteContent}
                    decline={t("No")}
                    agree={t("Si, Elimina")}
                    confirmText={t(
                        "Si è sicuri di voler eliminare questa lettera?"
                    )}
                    title={t("Sei sicuro?")}
                />
            )}
            {renderDataTable()}
        </>
    );
};
