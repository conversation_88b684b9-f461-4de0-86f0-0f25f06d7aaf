import VaporPage from "@vapor/react-custom/VaporPage";
import Badge from "@mui/material/Badge";
import { InformationBox } from "../../fatturazione/fatture/sections/InformationBox";
import { useTranslation } from "@1f/react-sdk";
import { SollecitiTabs } from "./sections/SollecitiTabs";
import { Box, Typography } from "@vapor/react-material";

const SollecitiIndex = () => {
  const { t } = useTranslation();

  return (
    <VaporPage title={t("Solleciti e Sospesi")}>
      <VaporPage.Section>
        <Box>
          <InformationBox>
            <div
              className="mb-3"
              style={{
                color: "#3a87ad",
                backgroundColor: "#d9edf7",
                borderColor: "#bce8f1",
                width: "auto",
                height: "fit-content",
                padding: "inherit",
              }}
            >
              <Typography
                variant="body"
                component="div"
                color="primary.interactiveDefault"
                gutterBottom
              >
                <Badge
                  badgeContent={1}
                  color="info"
                  style={{
                    marginRight: "12px",
                    marginBottom: "5px",
                  }}
                />
                {t("Nella sezione")}{" "}
                <b> {t("<PERSON>eg<PERSON> livello lettera di sollecito")} </b>
                {t(
                  "selezionare il livello del sollecito (1°, 2°, 3°) a cui corrisponde un template diverso per la lettera."
                )}
              </Typography>

              <Typography
                variant="body"
                component="div"
                color="primary.interactiveDefault"
                gutterBottom
              >
                <Badge
                  badgeContent={2}
                  color="info"
                  style={{ marginRight: "12px", marginBottom: "5px" }}
                />
                {t(
                  "Scegliere i documenti che si vogliono inserire nel template della lettera nella sezione"
                )}
                <b> {t("Filtra i sospesi.")}</b>
              </Typography>

              <Typography
                variant="body"
                component="div"
                color="primary.interactiveDefault"
                gutterBottom
              >
                <Badge
                  badgeContent={3}
                  color="info"
                  style={{ marginRight: "12px", marginBottom: "5px" }}
                />
                {t("Cliccare sul pulsante")}
                <b> {t("Crea lettera/e.")}</b>
              </Typography>

              <Typography
                variant="body"
                component="div"
                color="primary.interactiveDefault"
                gutterBottom
                style={{ marginTop: "50px", marginBottom: "30px" }}
              >
                {t(
                  "E' possibile visualizzare i pdf ed inviarli via email al cliente nella sezione"
                )}
                <b> {t("Riepilogo lettere create.")}</b>
              </Typography>
              <Typography
                variant="bodySmall700"
                color="primary.interactiveDefault"
                gutterBottom
                component="div"
              >
                {t("Ulteriori info:")}
              </Typography>

              <Typography
                variant="bodySmall500"
                color="primary.interactiveDefault"
                gutterBottom
                component="div"
              >
                {t(
                  "Le lettere verranno salvate anche nelle corrispondenti anagrafiche dei clienti."
                )}
              </Typography>

              <Typography
                variant="bodySmall500"
                color="primary.interactiveDefault"
                gutterBottom
                component="div"
              >
                {t(
                  "Le lettere non possono essere create per fatture e note di credito inviate a PA."
                )}
              </Typography>
            </div>
          </InformationBox>
        </Box>
      </VaporPage.Section>
      <VaporPage.Section>
        <SollecitiTabs />
      </VaporPage.Section>
    </VaporPage>
  );
};

export default SollecitiIndex;
