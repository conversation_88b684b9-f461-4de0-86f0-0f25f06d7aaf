export const formatDate = (date: Date) => {
  if (!date) {
    return null;
  }
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0"); // Months are zero-based
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
};

export const updateParams = (params: any, date: any) => {
  const { startDate, endDate } = date;

  if (startDate && startDate instanceof Date) {
    params.startDate = formatDate(startDate);
  } else {
    params.startDate = null;
  }

  if (endDate && endDate instanceof Date) {
    params.endDate = formatDate(endDate);
  } else {
    params.endDate = null;
  }

  return params;
};

export const updateParamsRiepilogo = (params: any) => {
  return params;
};

const formatNumber = (numberString: string): string => {
  const number = parseFloat(numberString);
  if (isNaN(number)) {
    throw new Error("Invalid number format");
  }

  const [integerPart, decimalPart] = number.toFixed(2).toString().split(".");

  const formattedIntegerPart = integerPart.replace(
    /\B(?=(\d{3})+(?!\d))/g,
    "."
  );

  return `${formattedIntegerPart},${decimalPart}`;
};

export const totalValuesArray = ["", "", "", "", "", "", "", "", "", ""];

export const mapTotalValueArray = (selectedValues: any[], totals: any) => {
  const newValues =
    selectedValues.length === 1 && selectedValues[0] !== -1
      ? [
          "Totali (€)",
          formatNumber(totals.totale_iva),
          formatNumber(totals.totale),
          formatNumber(totals.scoperto),
          formatNumber(totals.importofatturato),
        ]
      : ["Per visualizzare i Totali filtra per Tipologia", "", "", "", ""];

  const lastIndex = totalValuesArray.length - 1;

  const valuesWithTotalFields = [
    ...totalValuesArray.slice(0, lastIndex - newValues.length + 1),
    ...newValues,
  ];

  return valuesWithTotalFields;
};

export const mapDataWIthShortValues = (currentData: any[]) => {
  const dataToReturn = currentData?.map((item: any) => {
    const { intestatario, listaclienti, ...rest } = item;
    return {
      intestatario:
        intestatario && intestatario.length > 15
          ? intestatario?.substring(0, 15) + " ..."
          : intestatario || "",
      listaclienti:
        listaclienti && listaclienti.length > 15
          ? listaclienti?.substring(0, 15) + " ..."
          : listaclienti || "",
      ...rest,
    };
  });

  return dataToReturn;
};
