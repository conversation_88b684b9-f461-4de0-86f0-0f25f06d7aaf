import { Box, <PERSON>ton, <PERSON>alog, <PERSON>alogT<PERSON>le, <PERSON>vider, DialogActions, IconButton, DialogContent, TextField, CircularProgress } from "@vapor/react-material";
import { useNavigate } from "react-router-dom";
import useGetCustom from "../../../hooks/useGetCustom";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "@1f/react-sdk";
import React, { useState } from "react";
import { debounce } from "lodash";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

export const removeLinks = (input: string, replacement = "_") => input?.replace(/<a[^>]*>.*?<\/a>/g, replacement);

export const NewNotificaModal = (props: { newNotificaModal: boolean; handleClose: () => void }) => {
    const { newNotificaModal, handleClose } = props;
    const navigate = useNavigate();
    const [search, setSearch] = React.useState("");
    const [practicaArchive, setPracticaArchive] = React.useState([]);
    const [selectedPractica, setSelectedPractica] = React.useState({
        value: "",
        label: ""
    });
    const [isLoading, setIsLoading] = useState(false);
    const [value, setValue] = useState<string | null>("");
    const { t } = useTranslation();

    const getArchivePracticaDataReq = useGetCustom(search ? `default/archive/search?from=calendar&noTemplateVars=true&q=${search}` : "default/archive/search?from=calendar&noTemplateVars=true");

    const getPracticaArchiveData = React.useCallback(async () => {
        setIsLoading(true);
        try {
            const response: any = await getArchivePracticaDataReq.doFetch(true);

            setPracticaArchive(
                response.data.map((item: any) => ({
                    value: item.uniqueid,
                    label: item.codicearchivio ? `${item.codicearchivio}:${removeLinks(item.headerArchive)}` : removeLinks(item.headerArchive)
                }))
            );
        } finally {
            setIsLoading(false);
        }
    }, [search]);

    const debouncedGetPracticaArchiveData = React.useCallback(debounce(getPracticaArchiveData, 500), [getPracticaArchiveData]);

    React.useEffect(() => {
        debouncedGetPracticaArchiveData();

        return () => {
            debouncedGetPracticaArchiveData.cancel();
        };
    }, [search, debouncedGetPracticaArchiveData]);

    return (
        <>
            <Dialog open={newNotificaModal} onClose={handleClose} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
                <DialogTitle>
                    {t("Notifica in proprio")}
                    <IconButton color="primary" onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />

                <DialogContent>
                    <Box display="flex" gap={1} sx={{ m: 2 }}>
                        <CustomAutocomplete
                            size="small"
                            options={practicaArchive}
                            isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                            renderInput={(params: any) => (
                                <TextField
                                    {...params}
                                    placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG…")}
                                    InputProps={{
                                        ...params.InputProps,
                                        endAdornment: (
                                            <>
                                                {isLoading ? <CircularProgress size={20} /> : null}
                                                {params.InputProps.endAdornment}
                                            </>
                                        )
                                    }}
                                />
                            )}
                            onInputChange={(_event: any, value: any, reason: string) => {
                                if (reason === "input") {
                                    setSearch(value);
                                }
                            }}
                            value={selectedPractica}
                            onChange={(_event: any, item: any) => {
                                setValue(item.value);
                                setSelectedPractica(item);
                            }}
                            loadingText="Loading options..."
                        />
                        <Button
                            type="button"
                            variant="outlined"
                            size="small"
                            disabled={!value}
                            onClick={() => {
                                setValue("");
                                setSelectedPractica({ value: "", label: "" });
                            }}
                        >
                            {t("Cambia")}
                        </Button>
                    </Box>
                </DialogContent>

                <DialogActions>
                    <Button type="button" variant="outlined" size="small" onClick={handleClose} sx={{ mr: 1 }}>
                        {t("Annulla")}
                    </Button>

                    {value && (
                        <Button type="button" variant="outlined" size="small" onClick={() => navigate(`/legacy/archive/summary?uid=${selectedPractica.value}`)} sx={{ mr: 1 }}>
                            {t("Vai alla pratica")}
                        </Button>
                    )}
                    {value && (
                        <Button
                            variant="contained"
                            size="small"
                            sx={{
                                mr: 1
                            }}
                            onClick={() => navigate(`/legacy/archivenotifications/precheck?fileUniqueid=${selectedPractica.value}`)}
                        >
                            {t("Vai alla notifica in proprio")}
                        </Button>
                    )}
                </DialogActions>
            </Dialog>
        </>
    );
};
