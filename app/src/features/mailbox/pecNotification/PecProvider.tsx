import React, {
    createContext,
    useContext,
    useEffect,
    useState,
    useRef,
} from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useTranslation } from "@1f/react-sdk";

interface PecNotificationContextType {
    t: (key: string) => string;
    search: any;
    response: any[];
    isLoading: boolean;
    error: any;
    data: any[];
    date: any;
    defaultParams: any;
    totalRows: number;
    setData: any;
    setDate: any;
    setTotalRows: any;
    setDefaultParams: any;
    fetchData: (query: any, isReset?: boolean) => void;
}

const PecNotificationContext = createContext<
    PecNotificationContextType | undefined
>(undefined);

export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    page: 0,
    pageSize: 10,
    sortColumn: "created_at",
    sortOrder: "desc",
    start_date: "",
    end_date: "",
    status: -1,
};

const convertStringToDate = (date: any) => {
    if (!date) {
        return null;
    }
    const [day, month, year] = date.split("/");
    return new Date(year, month - 1, day);
};

// Provider component
export const PecNotificationProvider: React.FC<{
    children: React.ReactNode;
}> = ({ children }) => {
    const { t } = useTranslation();
    const [response, setResponse] = useState<any[]>([]);
    const [search, setSearch] = useState<any>();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<any>(null);
    const [defaultParams, setDefaultParams] =
        useState<any>(DEFAULT_LIST_PARAMS);
    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);
    const [date, setDate] = useState({
        start_date: null,
        end_date: null,
    });

    const initialized = useRef(false);

    const pecNotificationRequest = useGetCustom("pecnotifications/");

    const listPecNotificationsRequest = useGetCustom(
        "pecnotifications/list",
        defaultParams
    );

    useEffect(() => {
        const initPecNotifications = async () => {
            setIsLoading(true);
            try {
                const response: any = await pecNotificationRequest.doFetch();
                const { data } = response;
                setResponse(data);
                setSearch(data.search);

                setDefaultSearchParams(data.search);

                await startSearchList(data.search);
            } catch (error) {
                console.error("Pec Notifications error", error);
                setError(error);
            } finally {
                setIsLoading(false);
                initialized.current = true;
            }
        };

        initPecNotifications();
    }, []);

    useEffect(() => {
        if (initialized.current) {
            startSearchList(defaultParams);
            initialized.current = false;
        }
    }, [
        defaultParams.searchField,
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.start_date,
        defaultParams.end_date,
        defaultParams.status,
    ]);

    const startSearchList = async (query?: any, isReset = false) => {
        const { XDEBUG_SESSION_START, ...resetSearch } = query || {};

        if (isReset) {
            setDefaultSearchParams(search);
            initialized.current = false;
        }

        delete query.XDEBUG_SESSION_START;
        const response: any =
            Object.keys(query).length > 0
                ? await listPecNotificationsRequest.doFetch(true, resetSearch)
                : await listPecNotificationsRequest.doFetch(true);

        const { currentPage, totalRows } = response.data;
        setData(currentPage);
        setTotalRows(totalRows);
        initialized.current = true;
    };

    const setDefaultSearchParams = (query: any) => {
        if (query && (query.start_date || query.end_date)) {
            setDefaultParams({ ...query, noTemplateVars: true });
            const startDate = convertStringToDate(query.start_date);
            const endDate = convertStringToDate(query.end_date);

            setDate((prevDate: any) => ({
                ...prevDate,
                start_date: startDate,
                end_date: endDate,
            }));
        }
    };

    return (
        <PecNotificationContext.Provider
            value={{
                t,
                search,
                response,
                isLoading,
                error,
                data,
                totalRows,
                date,
                defaultParams,
                setDefaultParams,
                setData,
                setDate,
                setTotalRows,
                fetchData: startSearchList,
            }}
        >
            {children}
        </PecNotificationContext.Provider>
    );
};

export const usePecNotificationContext = (): PecNotificationContextType => {
    const context = useContext(PecNotificationContext);
    if (!context) {
        throw new Error(
            "usePecNotificationContext must be used within a PecNotificationProvider"
        );
    }
    return context;
};
