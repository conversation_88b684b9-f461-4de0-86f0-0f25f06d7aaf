import React, { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { useForm, Controller } from "react-hook-form";
import PersonIcon from "@mui/icons-material/Person";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import CheckIcon from "@mui/icons-material/Check";
import FormInput from "../../../custom-components/FormInput";
import { useLocation } from "react-router-dom";
import { Box, TextField, Button, MenuItem, Stack, FormControl, InputLabel, Select, IconButton, List, ListItem, ListItemText, Paper, Typography, Chip, FormHelperText } from "@mui/material";
import { useTranslation } from "@1f/react-sdk";
import CloseIcon from "@mui/icons-material/Close";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import usePostCustom from "../../../hooks/usePostCustom";
import ToastNotification from "../../../custom-components/ToastNotification";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

const schemaResolver = yupResolver(
    yup.object().shape({
        subject: yup.string().required("Please specify subject"),
        from: yup.string(),
        to: yup.string().required("È necessario inserire almeno un destinatario"),
        cc: yup.string(),
        bcc: yup.string(),
        content: yup.string(),
        emailFolder: yup.string()
    })
);

const EmailComposer = () => {
    const {
        control,
        handleSubmit,
        formState: { errors },
        watch,
        setValue
    } = useForm({
        resolver: schemaResolver,
        defaultValues: {
            from: "",
            to: "",
            cc: "",
            bcc: "",
            subject: "",
            content: "",
            emailFolder: "{imap.gmail.com/ssl/service=imap/novalidate-cert/norsh/notls}[Gmail]/Posta inviata"
        }
    });
    const values = watch();
    const [search, setSearch] = useState("");
    const [recipients, setRecipients] = useState<any[]>([]);
    const [cc, setCc] = useState<any[]>([]);
    const [bcc, setBcc] = useState<any[]>([]);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);
    const [emailSuggestions, setEmailSuggestions] = React.useState<any[]>([]);
    const { t } = useTranslation();
    const [ccActivated, setCcActivated] = useState(false);
    const [ccnActivated, setCcnActivated] = useState(false);
    const [emailContentEditor] = React.useState<any>({});
    const headerRef = React.useRef(null);
    const [files, setFiles] = useState<any[]>([]);
    const hiddenFileInput = React.useRef<HTMLInputElement>(null);

    const location = useLocation();

    const searchEmailReq = usePostCustom("mailbox/get-available-emails?noTemplateVars=true");

    const sendMailReq = usePostCustom(`mailbox/send-email?noTemplateVars=true`);

    React.useEffect(() => {
        searchExistingEmails();
    }, [search]);

    React.useEffect(() => {
        setValue("to", recipients.length === 0 ? "" : recipients[0].name, {
            shouldTouch: true,
            shouldValidate: true
        });
    }, [recipients.length]);

    const handleClick = () => {
        hiddenFileInput?.current?.click();
    };

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const selectedFiles = Array.from(event.target.files);

            setFiles((prevFiles) => {
                const newFiles = selectedFiles.filter((file) => !prevFiles.some((f) => f.name === file.name));
                return [...prevFiles, ...newFiles];
            });
        }
    };

    const searchExistingEmails = async () => {
        const formData = new FormData();
        if (location?.state?.uid) {
            formData.append("search", search);
        }
        const response: any = await searchEmailReq.doFetch(true, formData);
        setEmailSuggestions(
            response.data?.map((email: string) => ({
                name: email,
                email
            })) as any[]
        );
    };

    const handleRemoveFile = (fileToRemove: string) => {
        setFiles(files.filter((file) => file.name !== fileToRemove));
    };

    const ccActivatedButton = () => {
        setCcActivated(true);
    };

    const ccnActivatedButton = () => {
        setCcnActivated(true);
    };

    const onSubmit = async (values: any) => {
        const data = new FormData();
        const emailsToSend: string = recipients.length === 1 ? recipients[0].name : recipients.map((email: any) => email.name).join(",");
        const emailsCC: string = cc.length === 1 ? cc[0].name : cc.map((email: any) => email.name).join(",");
        const emailsCCN: string = bcc.length === 1 ? bcc[0].name : bcc.map((email: any) => email.name).join(",");
        data.append("uid", location.state.uid);
        data.append("toAddress", emailsToSend);
        data.append("ccAddress", emailsCC);
        data.append("ccnAddress", emailsCCN);
        data.append("emailSubject", values.subject);
        data.append("emailMessage", values.content);
        data.append("emailFolder", values.emailFolder);
        data.append("fileUid", "");
        files.forEach((item: any) => {
            data.append("attachments[]", item);
        });

        const response: any = await sendMailReq.doFetch(true, data);
        if (response?.data) {
            setShowSuccessMessage(true);
        } else {
            setShowErrorMessage(true);
        }
    };

    const isValidEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

    return (
        <VaporPage>
            <ToastNotification showNotification={showErrorMessage} setShowNotification={setShowErrorMessage} severity="error" text={t(`Siamo spiacenti, l'operazione non può essere completata. Se l'errore persiste la preghiamo di contattare l'assistenza tecnica. Grazie`)} />
            <ToastNotification showNotification={showSuccessMessage} setShowNotification={setShowSuccessMessage} severity="success" text={t("Email sent")} />
            <PageTitle title={t("Nuovo messaggio")} showBackButton={true} pathToPrevPage={`/default/mailbox/client/${location?.state?.uid}`}></PageTitle>

            <VaporPage.Section>
                <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                    <Stack
                        alignItems="end"
                        autoComplete="off"
                        component="form"
                        direction="row"
                        gap={1}
                        sx={{
                            width: 350
                        }}
                    >
                        <>
                            <Typography gutterBottom component="div">
                                {t("Da")}:{" "}
                            </Typography>
                            <Typography
                                variant="bodyLarge700"
                                sx={{
                                    fontStyle: "italic"
                                }}
                                gutterBottom
                                component="div"
                            >
                                {location?.state?.emailAccount?.email || ""}
                            </Typography>
                        </>
                    </Stack>
                    <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center" sx={{ width: "90%", pt: 2 }}>
                        <FormControl fullWidth error={Boolean(errors?.to?.message)} sx={{ flexGrow: 1 }}>
                            <Box>
                                <InputLabel>{t("A")}:</InputLabel>
                                <CustomAutocomplete
                                    multiple
                                    value={recipients}
                                    onChange={(_event: any, newValue: any, reason: any) => {
                                        if (reason === "createOption" && isValidEmail(search) && !recipients.some((item) => item.email === search)) {
                                            const newRecipient = {
                                                email: search,
                                                name: search
                                            };
                                            setRecipients([...recipients, newRecipient]);
                                            setEmailSuggestions([...emailSuggestions, newRecipient]);
                                        } else {
                                            setRecipients(newValue);
                                        }
                                    }}
                                    freeSolo
                                    options={emailSuggestions}
                                    getOptionLabel={(option: any) => option.email}
                                    renderTags={(value: any, getTagProps: any) => value.map((option: any, index: any) => <Chip label={`${option.email}`} size="small" {...getTagProps({ index })} />)}
                                    renderInput={(params: any) => {
                                        return <TextField {...params} placeholder="A:" size="small" margin="dense" onChange={(e) => setSearch(e.target.value)} />;
                                    }}
                                />
                                {errors?.to?.message && (
                                    <FormHelperText
                                        sx={{
                                            margin: 0,
                                            padding: 0,
                                            position: "absolute"
                                        }}
                                    >
                                        {errors.to.message}
                                    </FormHelperText>
                                )}
                            </Box>
                        </FormControl>{" "}
                        {!ccActivated && (
                            <Button startIcon={<PersonIcon />} size="small" variant="outlined" style={{ marginTop: "24px" }} onClick={ccActivatedButton}>
                                {t("CC")}
                            </Button>
                        )}
                        {!ccnActivated && (
                            <Button type="button" startIcon={<PersonIcon />} size="small" variant="outlined" style={{ marginTop: "24px" }} onClick={ccnActivatedButton}>
                                {t(" CCN")}
                            </Button>
                        )}
                    </Stack>
                    {ccActivated && (
                        <CustomAutocomplete
                            multiple
                            value={cc}
                            onChange={(_event: any, newValue: any, reason: any) => {
                                if (reason === "createOption" && isValidEmail(search) && !recipients.some((item: any) => item.email === search)) {
                                    const newRecipient = {
                                        email: search,
                                        name: search
                                    };
                                    setCc([...recipients, newRecipient]);
                                    setEmailSuggestions([...emailSuggestions, newRecipient]);
                                } else {
                                    setCc(newValue);
                                }
                            }}
                            freeSolo
                            options={emailSuggestions}
                            getOptionLabel={(option: any) => option.email}
                            renderTags={(value: any, getTagProps: any) => value.map((option: any, index: any) => <Chip label={`${option.email}`} size="small" {...getTagProps({ index })} />)}
                            renderInput={(params: any) => {
                                return <TextField {...params} placeholder="Cc:" size="small" margin="dense" onChange={(e) => setSearch(e.target.value)} />;
                            }}
                            sx={{ width: "90%" }}
                        />
                    )}
                    {ccnActivated && (
                        <CustomAutocomplete
                            multiple
                            value={bcc}
                            onChange={(_event: any, newValue: any, reason: any) => {
                                if (reason === "createOption" && isValidEmail(search) && !recipients.some((item) => item.email === search)) {
                                    const newRecipient = {
                                        email: search,
                                        name: search
                                    };
                                    setBcc([...recipients, newRecipient]);
                                    setEmailSuggestions([...emailSuggestions, newRecipient]);
                                } else {
                                    setBcc(newValue);
                                }
                            }}
                            freeSolo
                            options={emailSuggestions}
                            getOptionLabel={(option: any) => option.email}
                            renderTags={(value: any, getTagProps: any) => value.map((option: any, index: any) => <Chip label={`${option.email}`} size="small" {...getTagProps({ index })} />)}
                            renderInput={(params: any) => {
                                return <TextField {...params} placeholder="Ccn:" size="small" margin="dense" onChange={(e) => setSearch(e.target.value)} />;
                            }}
                            sx={{ width: "90%" }}
                        />
                    )}
                    <Stack direction="row" spacing={2} sx={{ width: "90%", pt: 3 }}>
                        <FormInput name="subject" label="Oggetto:" variant="outlined" control={control} type="text" />
                    </Stack>
                    <Box sx={{ width: "90%", pt: 2 }}>
                        <Controller
                            name="content"
                            control={control}
                            render={({ field }) => (
                                <CKEditor
                                    ref={headerRef}
                                    config={{
                                        toolbar: ["heading", "|", "bold", "italic", "blockQuote", "link", "numberedList", "bulletedList", "uploadImage", "insertTable", "mediaEmbed", "|", "undo", "redo"]
                                    }}
                                    editor={ClassicEditor as any}
                                    data={emailContentEditor}
                                    onChange={(_event, editor) => {
                                        field.onChange(editor.getData());
                                    }}
                                    onReady={(editor: any) => {
                                        editor.editing.view.change((writer: any) => {
                                            writer.setStyle("min-height", "500px", editor.editing.view.document.getRoot());

                                            writer.setStyle("color", "#343a40", editor.editing.view.document.getRoot());
                                        });
                                    }}
                                />
                            )}
                        />
                    </Box>
                    <Stack direction="row" spacing={2} sx={{ width: "90%", alignItems: "flex-start", pt: 2 }}>
                        <Paper
                            variant="outlined"
                            sx={{
                                flexGrow: 1,
                                minHeight: 100,
                                border: "1px solid #ccc",
                                padding: 1,
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "flex-start"
                            }}
                        >
                            {files.length > 0 ? (
                                <List sx={{ width: "100%" }}>
                                    {files.map((file, index) => (
                                        <ListItem
                                            key={index}
                                            sx={{
                                                display: "flex",
                                                justifyContent: "space-between",
                                                padding: 0
                                            }}
                                        >
                                            <ListItemText
                                                primary={file.name}
                                                primaryTypographyProps={{
                                                    sx: {
                                                        color: "blue",
                                                        textDecoration: "underline"
                                                    }
                                                }}
                                            />
                                            <IconButton edge="end" aria-label="delete" onClick={() => handleRemoveFile(file.name)} size="small">
                                                <CloseIcon />
                                            </IconButton>
                                        </ListItem>
                                    ))}
                                </List>
                            ) : (
                                <div style={{ color: "#999" }}>{t("No files uploaded")}</div>
                            )}
                        </Paper>

                        {/* Upload Button */}
                        <Button
                            variant="contained"
                            startIcon={<AttachFileIcon />}
                            onClick={handleClick}
                            sx={{
                                alignSelf: "flex-start",
                                whiteSpace: "nowrap"
                            }}
                        >
                            <i className="fe-paperclip">
                                <input type="file" name="file" accept=".csv,.xlsx,.pdf,.docs,.png,.jpg" multiple onChange={handleChange} ref={hiddenFileInput} style={{ display: "none" }} />
                            </i>
                            {t("Allegati")}
                        </Button>
                    </Stack>
                    <Stack direction="row" spacing={2} sx={{ width: 1 / 2, pt: 2 }}>
                        <FormControl variant="outlined" sx={{ width: 1 / 2 }}>
                            <InputLabel id="select-label">{t("Salva una copia in")}</InputLabel>
                            <Select
                                labelId="select-label"
                                value={values.emailFolder}
                                label={t("Salva una copia in")}
                                onChange={(event: any) => {
                                    setValue("emailFolder", event.target.value);
                                }}
                                name="emailFolder"
                            >
                                {location?.state?.folders?.map((folder: any, index: number) => {
                                    return (
                                        <MenuItem value={folder.name} key={index}>
                                            {folder.printName}
                                        </MenuItem>
                                    );
                                })}
                            </Select>
                        </FormControl>
                    </Stack>
                    <Stack direction="row" spacing={2} sx={{ width: 650, pt: 2 }}>
                        <Button type="submit" variant="contained" sx={{ mr: 1 }} startIcon={<CheckIcon />}>
                            {t("Invia")}
                        </Button>
                    </Stack>
                </Box>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default EmailComposer;
