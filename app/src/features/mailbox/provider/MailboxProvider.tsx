import React, {
    ReactNode,
    useState,
    useCallback,
    useContext,
    useReducer,
} from "react";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useApi } from "../../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../../utilities/constants";
import useGetCustom from "../../../hooks/useGetCustom";

import moment from "moment";

const INITIALIZE = "INITIALIZE";
const SET_MAILBOX = "SET_MAILBOX";
const SET_MESSAGES = "SET_MESSAGES";
const OPENED_MESSAGE = "OPENED_MESSAGE";

export type ActionMap<M extends { [index: string]: any }> = {
    [Key in keyof M]: M[Key] extends undefined
        ? { type: Key }
        : { type: Key; payload: M[Key] };
};

interface IMailBox {
    folders: any[];
    storage: any;
    messages: any[];
    error: any;
    emailAccount: any;
    maxMailsPerPage: number;
    countMessages: number;
}

type MailBoxActionTypes = {
    [INITIALIZE]: {
        isInitialized: boolean;
        mailbox: IMailBox;
        messages: any[];
        message: any;
        messageLoading: boolean;
    };
    [SET_MAILBOX]: {
        mailbox: IMailBox;
    };
    [SET_MESSAGES]: {
        messages: any[];
    };
    [OPENED_MESSAGE]: {
        message: any;
        messageLoading: boolean;
    };
};

export type MailboxState = {
    isInitialized: boolean;
    mailbox: IMailBox;
    messages: any[];
    message: any;
    messageLoading: boolean;
};

const initialState: MailboxState = {
    isInitialized: false,
    mailbox: {
        folders: [],
        storage: {},
        error: {},
        emailAccount: {},
        messages: [],
        maxMailsPerPage: 13,
        countMessages: 0,
    },
    messages: [],
    message: {},
    messageLoading: false,
};

const JWTReducer = (
    state: MailboxState,
    action: ActionMap<MailBoxActionTypes>[keyof ActionMap<MailBoxActionTypes>]
) => {
    switch (action.type) {
        case INITIALIZE:
            return {
                isInitialized: true,
                messages: [],
                mailbox: action.payload.mailbox,
                message: {},
                messageLoading: false,
            };
        case SET_MAILBOX:
            return {
                ...state,
                isInitialized: true,
                mailbox: action.payload.mailbox,
            };

        case SET_MESSAGES:
            return {
                ...state,
                messages: action.payload.messages,
            };

        case OPENED_MESSAGE:
            return {
                ...state,
                message: action.payload.message,
                messageLoading: action.payload.messageLoading,
            };
        default:
            return state;
    }
};

export interface MailboxContextType {
    isInitialized: boolean;
    mailbox: any;
    messages: any[];
    message: any;
    messageLoading: boolean;
    uid: string;
    fetchMailbox: () => void;
    getMessages: (query: any) => void;
    openMessage: (query: any) => void;
    clearMessage: () => void;
    downloadAttachment: (query: any) => void;
}

export const MailboxContext = React.createContext<MailboxContextType>({
    isInitialized: false,
    mailbox: {
        folders: [],
        storage: {},
        error: {},
        emailAccount: {},
        messages: [],
        maxMailsPerPage: 13,
        countMessages: 0,
    },
    messages: [],
    message: {},
    messageLoading: false,
    uid: "",
    fetchMailbox: () => {},
    getMessages: () => {},
    openMessage: () => {},
    clearMessage: () => {},
    downloadAttachment: () => {},
});

export function useMailboxProvider() {
    return useContext(MailboxContext);
}

export function MailboxProvider({ children }: { children: ReactNode }) {
    const [state, dispatch] = useReducer(JWTReducer, initialState);
    const [error, setError] = useState<any>(null);
    const params = useParams<{ uid: string }>();
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    const mailboxDefaultRequest = useGetCustom(
        `default/mailbox/client?uid=${params.uid}`
    );

    const getMessagesReq = useGetCustom(
        `mailbox/getmessages?uid=${params.uid}&noTemplateVars=true`
    );

    const openMessageReq = useGetCustom(
        `mailbox/openmessage?uid=${params.uid}&noTemplateVars=true`
    );

    useEffect(() => {
        fetchMailbox();
    }, []);

    const fetchMailbox = useCallback(async () => {
        return await mailboxDefaultRequest
            .doFetch()
            .then((response: any) => {
                const mailboxResponse = response.data;
                let folders = mailboxResponse.folders;

                if (!Array.isArray(folders)) {
                    mailboxResponse.folders = Object.values(folders);
                }
                dispatch({
                    type: SET_MAILBOX,
                    payload: {
                        mailbox: mailboxResponse,
                    },
                });

                dispatch({
                    type: SET_MESSAGES,
                    payload: {
                        messages: response.data.messages,
                    },
                });
            })
            .catch((error) => {
                setError(error);
            });
    }, [params.uid]);

    const getMessages = async (filterQuery: any) => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: true,
            },
        });

        if (filterQuery.date) {
            filterQuery.date = moment(filterQuery.date, "DD/MM/YYYY").format(
                "DD/MM/YYYY"
            );
        } else {
            filterQuery.date = "";
        }
        if (filterQuery.untilDate) {
            filterQuery.untilDate = moment(
                filterQuery.untilDate,
                "DD/MM/YYYY"
            ).format("DD/MM/YYYY");
        } else {
            filterQuery.untilDate = "";
        }
        const response: any = await getMessagesReq.doFetch(true, filterQuery);
        dispatch({
            type: SET_MESSAGES,
            payload: {
                messages: response.data.messages,
            },
        });

        dispatch({
            type: SET_MAILBOX,
            payload: {
                mailbox: {
                    ...state.mailbox,
                    countMessages: response.data.countMessages,
                },
            },
        });

        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: false,
            },
        });
    };

    const openMessage = async (query: any) => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: true,
            },
        });
        const response: any = await openMessageReq.doFetch(true, query);
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: { ...response.data, messageUid: query.messageUid },
                messageLoading: false,
            },
        });
    };

    const downloadAttachment = async (query: any) => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: state.message,
                messageLoading: true,
            },
        });

        const url = `https://${usedSubdomain}.netlex.cloud/mailbox/getattachment?uid=${params.uid}&noTemplateVars=true&num=${query.num}&messageUid=${query.messageUid}&originFolder=${query.folder}&showFullMsg=undefined`;
        window.open(url);

        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: state.message,
                messageLoading: false,
            },
        });
    };

    const clearMessage = () => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: false,
            },
        });
    };

    return (
        <MailboxContext.Provider
            value={{
                ...state,
                uid: params.uid as string,
                fetchMailbox,
                getMessages,
                openMessage,
                clearMessage,
                downloadAttachment,
            }}
        >
            {error ? <h1>Error</h1> : children}
        </MailboxContext.Provider>
    );
}
