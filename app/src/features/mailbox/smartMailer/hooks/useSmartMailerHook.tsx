import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";

export const useSmartMailerHook = () => {
    const { t } = useTranslation();

    const [messages, setMessages] = useState<any>();
    const [subdomain, setSubdomain] = useState<any>("test");
    const [smart_mailer_enable, setSmart_mailer_enable] = useState<any>();
    const smartMailerRequest = useGetCustom("sorter/client");

    useEffect(() => {
        initSmartMailer();
    }, []);

    async function initSmartMailer() {
        try {
            const response: any = await smartMailerRequest.doFetch();
            setSmart_mailer_enable(response.data.smart_mailer_enable);
            setSubdomain(response.data.subdomain);
            setMessages(response.data.messages);
        } catch (error) {
            console.log("Mailbox error", error);
            return;
        }
    }

    return {
        t,
        smart_mailer_enable,
        subdomain,
        messages,
        fetchAgain: initSmartMailer,
    };
};
