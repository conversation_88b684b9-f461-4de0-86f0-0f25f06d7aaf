import { Box, Button } from "@vapor/react-material";
import DeleteIcon from "@mui/icons-material/Delete";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { useTranslation } from "@1f/react-sdk";
import { useSmartMailerProvider } from "../../providers/SmartMailerProvider";

const Filters = (props: any) => {
    const { setOpenDeleteConfirmation, handleOpenFasciola } = props;
    const { t } = useTranslation();
    const { message, clearMessage } = useSmartMailerProvider();

    if (Object.keys(message).length === 0) {
        return null;
    }

    return (
        <Box display="flex" alignItems="end" gap={1} sx={{ pt: 1, pb: 1 }}>
            <Button
                variant="outlined"
                color="primary"
                size="small"
                onClick={clearMessage}
            >
                <ArrowBackIcon /> {t("Indietro")}
            </Button>

            <Button
                variant="outlined"
                color="error"
                onClick={() => {
                    setOpenDeleteConfirmation(true);
                }}
                size="small"
            >
                <DeleteIcon /> {t("Cestina")}
            </Button>

            <Button
                variant="outlined"
                color="secondary"
                size="small"
                onClick={() => handleOpenFasciola(true)}
            >
                <InsertDriveFileIcon /> {t("Fascicola")}
            </Button>
        </Box>
    );
};

export default Filters;
