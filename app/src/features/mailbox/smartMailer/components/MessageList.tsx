import React from "react";
import {
    Box,
    Typography,
    Checkbox,
    ListItem,
    ListItemText,
    Divider,
    List,
} from "@vapor/react-material";

import { useSmartMailerProvider } from "../../providers/SmartMailerProvider";
const MessageList = (props: any) => {
    const { messages, openMessage } = useSmartMailerProvider();

    const { selectedMails, setSelectedMails } = props;

    const openSelectedMessage = (messageId: string) => {
        openMessage({
            messageUid: messageId,
        });
    };

    return (
        <Box display="flex" alignItems="end" gap={1} sx={{ p: 1 }}>
            <List
                sx={{
                    width: "100%",
                    bgcolor: "background.paper",
                    mt: 3,
                }}
            >
                {messages.length > 0 ? (
                    messages.map((message: any) => (
                        <React.Fragment key={message.message_uid}>
                            <ListItem
                                alignItems="flex-start"
                                sx={{
                                    cursor: "pointer",
                                    position: "relative",
                                    "&:hover": {
                                        backgroundColor: "#f0f0f0",
                                        border: "1px solid #007bff", // Optional: Adds border on hover
                                    },
                                }}
                                disablePadding
                            >
                                <Box
                                    sx={{
                                        position: "absolute",
                                        left: 8,
                                        top: "50%",
                                        transform: "translateY(-50%)",
                                        zIndex: 1,
                                    }}
                                >
                                    <Checkbox
                                        edge="start"
                                        checked={
                                            !!selectedMails?.find(
                                                (item: any) =>
                                                    item.message_uid ===
                                                    message.message_uid
                                            )
                                        }
                                        tabIndex={-1}
                                        disableRipple
                                        onChange={() => {
                                            if (
                                                !!selectedMails?.find(
                                                    (item: any) =>
                                                        item.message_uid ===
                                                        message.message_uid
                                                )
                                            ) {
                                                setSelectedMails(
                                                    selectedMails?.filter(
                                                        (item: any) =>
                                                            item.message_uid !==
                                                            message.message_uid
                                                    )
                                                );
                                            } else {
                                                setSelectedMails([
                                                    ...selectedMails,
                                                    message,
                                                ]);
                                            }
                                        }}
                                    />
                                </Box>
                                <ListItemText
                                    primary={
                                        <Box
                                            sx={{
                                                display: "flex",
                                                justifyContent: "space-between",
                                                alignItems: "center",
                                                width: "100%",
                                                paddingLeft: "40px", // Ensure text doesn't overlap with checkbox
                                            }}
                                        >
                                            <Typography
                                                component="span"
                                                variant="body1"
                                                sx={{
                                                    fontWeight: "bold",
                                                    color: "black",
                                                    flex: 1,
                                                }}
                                            >
                                                {message.from
                                                    .split("<")[0]
                                                    .trim()}
                                            </Typography>
                                            <Typography
                                                component="span"
                                                variant="body2"
                                                sx={{
                                                    color: "text.primary",
                                                    fontWeight: "bold",
                                                    flex: 1,
                                                    textAlign: "center",
                                                }}
                                            >
                                                {message.subject}
                                            </Typography>
                                            <Typography
                                                component="span"
                                                variant="body2"
                                                sx={{
                                                    color: "text.secondary",
                                                    flex: 1,
                                                    textAlign: "right",
                                                }}
                                            >
                                                {message.date}
                                            </Typography>
                                        </Box>
                                    }
                                    onClick={() =>
                                        openSelectedMessage(message.uniqueid)
                                    }
                                />
                            </ListItem>
                            <Divider className="MuiDivider-VaporLight" />
                        </React.Fragment>
                    ))
                ) : (
                    <ListItem>
                        <ListItemText
                            primary="Nessun messaggio presente"
                            sx={{
                                textAlign: "center",
                                color: "text.secondary",
                            }}
                        />
                    </ListItem>
                )}
            </List>
        </Box>
    );
};

export default MessageList;
