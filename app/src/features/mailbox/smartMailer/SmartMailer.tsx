import React, { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { useUser } from "../../../store/UserStore";
import usePostCustom from "../../../hooks/usePostCustom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import {
    Box,
    Typography,
    Grid,
    Checkbox,
    Menu,
    Button,
    ButtonGroup,
    Modal,
    Divider,
} from "@vapor/react-material";

import FolderOpenIcon from "@mui/icons-material/FolderOpen";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { styled, alpha } from "@mui/material/styles";
import MenuItem from "@mui/material/MenuItem";
import { useTranslation } from "@1f/react-sdk";
import { AlertBox } from "../../../custom-components/AlertBox";
import FascicolaSmartMailer from "./sections/FascicolaSmartMailer";
import { useSmartMailerProvider } from "../providers/SmartMailerProvider";
import Spinner from "../../../custom-components/Spinner";
import EmailView from "./components/EmailView";
import Filters from "./components/Filters";
import MessageList from "./components/MessageList";

const StyledMenu = styled((props: any) => (
    <Menu
        elevation={0}
        anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
        }}
        transformOrigin={{
            vertical: "top",
            horizontal: "right",
        }}
        {...props}
    />
))(({ theme }) => ({
    "& .MuiPaper-root": {
        borderRadius: 6,
        marginTop: theme.spacing(1),
        minWidth: 180,
        color: "rgb(55, 65, 81)",
        boxShadow:
            "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
        "& .MuiMenu-list": {
            padding: "4px 0",
        },
        "& .MuiMenuItem-root": {
            "& .MuiSvgIcon-root": {
                fontSize: 18,
                color: theme.palette.text.secondary,
                marginRight: theme.spacing(1.5),
            },
            "&:active": {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    theme.palette.action.selectedOpacity
                ),
            },
        },
        ...theme.applyStyles("dark", {
            color: theme.palette.grey[300],
        }),
    },
}));

const SmartMailerIndex = () => {
    const { t } = useTranslation();
    const { user }: any = useUser();
    const [selectedMails, setSelectedMails] = useState<any>([]);
    const [openDeleteConfirmation, setOpenDeleteConfirmation] =
        useState<boolean>(false);

    const [checkedAll, setCheckedAll] = useState(false);
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const [openFasciola, setOpenFasciola] = React.useState(false);

    React.useEffect(() => {
        if (selectedMails.length === 0) {
            setCheckedAll(false);
        }
    }, [selectedMails.length]);

    const { mailbox, isInitialized, message, clearMessage, fetchSmartMail } =
        useSmartMailerProvider();

    const { messages, smart_mailer_enable } = mailbox;

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const closeDeleteConfirmation = async () => {
        setOpenDeleteConfirmation(false);
    };

    const handleClose = (event: any) => {
        if (typeof event === "string" && event === "tutti") {
            setSelectedMails(messages);
            setCheckedAll(true);
        }
        if (typeof event === "string" && event === "nessuno") {
            setSelectedMails([]);
            setCheckedAll(false);
        }

        //TODO to find how it save unread and read messages
        if (typeof event === "string" && event === "unreed") {
            const unreadMessages = messages.filter(
                (message: any) => message.seen === 0 || message.seen === false
            );

            setSelectedMails(unreadMessages);
            setCheckedAll(false);
        }
        if (typeof event === "string" && event === "read") {
            const unreadMessages = messages.filter(
                (message: any) => message.seen === 1 || message.seen === true
            );
            setSelectedMails(unreadMessages);
            setCheckedAll(false);
        }
        setAnchorEl(null);
    };

    const handleSelectAll = () => {
        setCheckedAll(!checkedAll);
        checkedAll ? setSelectedMails([]) : setSelectedMails(messages);
    };

    const [isOpenFromInsideMessage, setIsOpenFromInsideMessage] =
        React.useState(false);

    const handleOpenFasciola = (inside: boolean = false) => {
        setOpenFasciola(true);
        setIsOpenFromInsideMessage(inside);
    };

    const handleCloseFasciola = () => setOpenFasciola(false);

    const deleteMessageRequest = usePostCustom(
        "default/sorter/deletemessages?noTemplateVars=true"
    );

    const deleteMessage = async () => {
        const formData = new FormData();
        formData.append("messageUid", message.messageUid);
        const response: any = await deleteMessageRequest.doFetch(
            true,
            formData
        );
        if (response.data) {
            setOpenDeleteConfirmation(false);
            clearMessage();
            fetchSmartMail();
        }
    };

    if (!isInitialized) {
        return <Spinner />;
    }

    const isMessageOpen = Object.keys(message).length > 0;

    return (
        <VaporPage>
            <PageTitle
                title={t("Smart Mailer ")}
                showBackButton={true}
                pathToPrevPage="/default/mailbox"
            />

            <VaporPage.Section>
                {!smart_mailer_enable ? (
                    <AlertBox textAlign="left">
                        <b>{t("ATTENZIONE ")}</b>:
                        {t(" Il modulo non risulta attivo")}
                        <ul>
                            {user?.configs?.app
                                ?.can_active_smart_mailer_upgrade_bool ? (
                                <li>
                                    {t("Per avere lo")}{" "}
                                    <b>{t("Smart Mailer")}</b>{" "}
                                    {t(
                                        "Effettua un upgrade a Netlex in Cloud versione Plus e poi acquista il modulo"
                                    )}
                                </li>
                            ) : (
                                <li>
                                    {t("Per avere lo")}{" "}
                                    <b>{t("Smart Mailer")}</b>{" "}
                                    {t("E' necessario acquistare il modulo")}.
                                </li>
                            )}
                        </ul>
                    </AlertBox>
                ) : (
                    <>
                        <Box sx={{ mt: 3 }}>
                            <Grid container spacing={12}>
                                <Grid
                                    item
                                    md={1}
                                    xl={1}
                                    xs={1}
                                    sx={{
                                        display: "flex",
                                        justifyContent: "flex-end",
                                        paddingTop: "10px",
                                    }}
                                >
                                    <Typography
                                        variant="titleSmall"
                                        color="primary.main"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Smart Mailer")}
                                    </Typography>
                                </Grid>
                                <Grid item md={11} xl={11}>
                                    <AlertBox
                                        backgroundColor={"#f5f5f5"}
                                        color={"#000000c2"}
                                        textAlign={"left"}
                                    >
                                        {t(
                                            "Nel caso ci fosse un errore nel codice pratica o nell'indirizzo Smart Mailer indicato al momento dell'invio della mail e Smart Mailer non riesca ad associare l'email ad una pratica il messaggio verrà posizionato in questa sezione da dove potrà essere smistata manualmente."
                                        )}
                                    </AlertBox>
                                    <Box
                                        display="flex"
                                        alignItems="end"
                                        gap={1}
                                        sx={{ pt: 3 }}
                                    >
                                        {!isMessageOpen && (
                                            <ButtonGroup
                                                variant="outlined"
                                                aria-label="Basic button group"
                                            >
                                                <Button>
                                                    <Checkbox
                                                        onChange={
                                                            handleSelectAll
                                                        }
                                                        checked={checkedAll}
                                                        size="large"
                                                    />
                                                </Button>
                                                <Button
                                                    onClick={handleClick}
                                                    sx={{ mr: "1rem" }}
                                                    endIcon={
                                                        <ArrowDropDownIcon />
                                                    }
                                                >
                                                    {t("Seleziona")}
                                                </Button>
                                                <StyledMenu
                                                    id="demo-customized-menu"
                                                    MenuListProps={{
                                                        "aria-labelledby":
                                                            "demo-customized-button",
                                                    }}
                                                    anchorEl={anchorEl}
                                                    open={open}
                                                    onClose={handleClose}
                                                >
                                                    <MenuItem
                                                        onClick={() =>
                                                            handleClose("tutti")
                                                        }
                                                        disableRipple
                                                    >
                                                        {t("Tutti")}
                                                    </MenuItem>
                                                    <MenuItem
                                                        onClick={() =>
                                                            handleClose(
                                                                "nessuno"
                                                            )
                                                        }
                                                        disableRipple
                                                    >
                                                        {t("Nessuno")}
                                                    </MenuItem>
                                                    <Divider sx={{ my: 0.5 }} />
                                                    <MenuItem
                                                        onClick={() =>
                                                            handleClose(
                                                                "unreed"
                                                            )
                                                        }
                                                        disableRipple
                                                    >
                                                        {t("Da leggere")}
                                                    </MenuItem>
                                                    <MenuItem
                                                        onClick={() =>
                                                            handleClose("read")
                                                        }
                                                        disableRipple
                                                    >
                                                        {t("Letti")}
                                                    </MenuItem>
                                                </StyledMenu>
                                            </ButtonGroup>
                                        )}

                                        {!isMessageOpen &&
                                            selectedMails.length > 0 && (
                                                <Button
                                                    variant="outlined"
                                                    color="secondary"
                                                    onClick={() =>
                                                        handleOpenFasciola()
                                                    }
                                                >
                                                    <FolderOpenIcon />
                                                </Button>
                                            )}

                                        <Filters
                                            setOpenDeleteConfirmation={
                                                setOpenDeleteConfirmation
                                            }
                                            handleOpenFasciola={
                                                handleOpenFasciola
                                            }
                                        />
                                    </Box>
                                    {!isMessageOpen && (
                                        <MessageList
                                            selectedMails={selectedMails}
                                            setSelectedMails={setSelectedMails}
                                        />
                                    )}
                                    {isMessageOpen && (
                                        <Box gap={1} sx={{ paddingTop: 2 }}>
                                            <EmailView query={{}} />
                                        </Box>
                                    )}
                                </Grid>
                            </Grid>
                        </Box>
                    </>
                )}
            </VaporPage.Section>

            <Modal
                open={openFasciola}
                onClose={handleCloseFasciola}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <FascicolaSmartMailer
                    handleCloseFasciola={handleCloseFasciola}
                    isOpenFromInsideMessage={isOpenFromInsideMessage}
                    selectedMails={selectedMails}
                    fileName={selectedMails
                        ?.map((item: any) => item.subject)
                        .join(",")}
                    message={message}
                />
            </Modal>

            <ConfirmModal
                open={openDeleteConfirmation}
                handleDecline={closeDeleteConfirmation}
                handleAgree={deleteMessage}
                decline={t("Cancel")}
                agree={t("OK")}
                confirmText={`${t(
                    "Sei sucuro di voler eliminare il messaggio?"
                )}`}
                title={t("Conferma eliminazione")}
            />
        </VaporPage>
    );
};

export default SmartMailerIndex;
