import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Box, Button, Grid, TextField, Typography } from "@vapor/react-material";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import SpinnerButton from "../../../../custom-components/SpinnerButton";
import { useTranslation } from "@1f/react-sdk";
import { debounce } from "lodash";
import useGetCustom from "../../../../hooks/useGetCustom";
import { removeLinks } from "../../../../utilities/utils";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useSmartMailerProvider } from "../../providers/SmartMailerProvider";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

interface FormData {
    archiveSearchTarget: string;
    practiceSearch: any;
    title: string;
    fileName: string;
    attachmentOneDrive: string;
    saveFullMsg: string;
    createMacro: boolean;
    messageUid: number;
}

const defaultValues: FormData = {
    archiveSearchTarget: "codicearchivio",
    practiceSearch: {
        label: "",
        value: ""
    },
    title: "",
    fileName: "",
    attachmentOneDrive: "0",
    saveFullMsg: "0",
    createMacro: false,
    messageUid: 0
};

const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 600,
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
    p: 4
};

const FascicolaSmartMailer = (props: { handleCloseFasciola: any; isOpenFromInsideMessage: boolean; selectedMails: any[]; fileName: string; message: any }) => {
    const { handleCloseFasciola, selectedMails, fileName, message } = props;
    const { fetchSmartMail } = useSmartMailerProvider();

    const { control, handleSubmit, watch, setValue, reset } = useForm<FormData>({
        defaultValues
    });
    const [search, setSearch] = React.useState("");
    const [loader, setLoader] = useState(false);
    const [practicaDisabled, setPracticaDisabled] = useState(false);
    const [praticheSearchResults, setPracticaSearchResult] = React.useState([]);

    const practiceSearch = watch("practiceSearch");
    const archiveSearchTarget = watch("archiveSearchTarget");
    const { t } = useTranslation();

    const getTaskFileDataReq = useGetCustom(`default/archive/search?q=${search}&from=mailbox&target=${archiveSearchTarget}&noTemplateVars=true`);

    const saveFasciolaRequest = usePostCustom(`default/sorter/saveemail?noTemplateVars=true`);

    React.useEffect(() => {
        if (message && Object.keys(message).length > 0) {
            setValue("fileName", selectedMails.length > 1 ? fileName : message.subject);
            setValue("messageUid", message.uid);
        } else if (Object.keys(message).length === 0 && selectedMails.length === 1) {
            setValue("fileName", fileName);
        }
    }, [message]);

    React.useEffect(() => {
        const debouncedSearch = debounce(() => {
            getPratiche();
        }, 500);
        search && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [search]);

    const getPratiche = async () => {
        const response: any = await getTaskFileDataReq.doFetch(true);
        setPracticaSearchResult(
            response.data.map((item: any) => ({
                label: `${removeLinks(item.headerArchive)}`,
                value: item.uniqueid
            }))
        );
    };

    const handleSubmitData = async (data: any) => {
        const formData = new FormData();

        formData.append("uid", "");
        formData.append("messageUid", selectedMails.length === 0 ? message.uniqueid : selectedMails.map((item: any) => item.uniqueid).join(","));

        formData.append("originFolder", "");
        formData.append("fileUid", data.practiceSearch.value);
        formData.append("title", data.title);
        formData.append("fileName", data.fileName);

        const response: any = await saveFasciolaRequest.doFetch(true, formData);

        if (response.data) {
            handleCloseFasciola();
            fetchSmartMail();
        }
        setLoader(true);
    };

    const onSubmit = (data: FormData) => {
        handleSubmitData(data);
        reset();
    };

    return (
        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={style}>
            <Typography variant="h6" gutterBottom>
                {t("Fascicola email")}
            </Typography>

            <Grid container sx={{ display: "flex", pt: 1 }}>
                <Grid item md={10} lg={10}>
                    <CustomAutocomplete
                        options={praticheSearchResults}
                        sx={{ width: "90%" }}
                        isOptionEqualToValue={(option: any, value: any) => {
                            return option.value === value.value;
                        }}
                        renderInput={(params: any) => {
                            return <TextField {...params} placeholder={t("Cerca pratica ")} />;
                        }}
                        onInputChange={(_event: any, value: any, reason: string) => {
                            if (reason === "input") {
                                setSearch(value);
                            }
                        }}
                        disabled={practicaDisabled}
                        value={practiceSearch.label}
                        onChange={(_event: any, item: any) => {
                            setValue("practiceSearch", item);
                            setPracticaDisabled(true);
                        }}
                    />
                </Grid>
                <Grid item md={2} lg={2}>
                    <Button
                        variant="contained"
                        color="primary"
                        disabled={!practicaDisabled}
                        onClick={() => {
                            setValue("practiceSearch", {
                                label: "",
                                value: ""
                            });
                            setSearch("");
                            setPracticaDisabled(false);
                        }}
                    >
                        {t("Cambia")}
                    </Button>
                </Grid>
            </Grid>
            <Grid container style={{ display: "flex" }}>
                <Grid item md={12} lg={12}>
                    <Controller name="title" control={control} render={({ field }: any) => <TextField label={t(`La fascicolazione avverrà nella sezione "Email fascicolate" della pratica`)} fullWidth margin="normal" placeholder={t("Descrizione")} {...field} />} />
                </Grid>
            </Grid>
            <Grid container style={{ display: "flex" }}>
                <Grid item md={11} lg={11}>
                    <Controller name="fileName" control={control} render={({ field }: any) => <TextField label={t(`Se non specificata verrà utilizzata la descrizione "Email del gg-mm-aaaa"`)} fullWidth margin="normal" placeholder={t("Nome file senza estensione")} {...field} />} />
                </Grid>
                <Grid item md={1} lg={1}>
                    {" "}
                    <Typography variant="body700" gutterBottom component="div" sx={{ pt: 8 }}>
                        {t(".eml")}
                    </Typography>
                </Grid>
            </Grid>

            <Typography variant="bodySmall" gutterBottom component="div" sx={{ pt: 1 }}>
                {t(`Se non specificato verrà utilizzato il nome "Email_numeromessaggio"`)}
            </Typography>

            <Box mt={2} display="flex" alignItems="center" justifyContent="space-between">
                {loader && (
                    <Typography mt={1} variant="bodySmall500" component="div" color="primary.interactiveDefault" gutterBottom>
                        {t("Operazione in corso attendere prego...")}
                    </Typography>
                )}

                <Box display="flex" gap={2}>
                    {practicaDisabled && <SpinnerButton label={t("Fascicola")} variant="contained" isLoading={loader} type="submit" startIcon={<CheckIcon />} />}

                    <Button variant="outlined" color="secondary" onClick={handleCloseFasciola} startIcon={<CloseIcon />}>
                        {t("Annulla")}
                    </Button>
                </Box>
            </Box>
        </Box>
    );
};

export default FascicolaSmartMailer;
