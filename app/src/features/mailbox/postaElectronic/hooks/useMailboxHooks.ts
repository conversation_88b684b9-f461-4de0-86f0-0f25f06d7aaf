import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect, useState } from "react";
import { useTranslation } from "@1f/react-sdk";

export const useMailboxHooks = () => {
  const { t } = useTranslation();

  const [emailAccounts, setEmailAccounts] = useState<any[]>([]);
  const [subdomain, setSubdomain] = useState<any>();
  const mailboxDefaultRequest = useGetCustom("default/mailbox/");

  useEffect(() => {
    initMailbox();
  }, []);

  async function initMailbox() {
    try {
      const response: any = await mailboxDefaultRequest.doFetch();
      setEmailAccounts(response.data.emailAccounts);
      setSubdomain(response.data.subdomain);
    } catch (error) {
      console.log("Mailbox error", error);
      return;
    }
  }

  return {
    t,
    emailAccounts,
    subdomain,
    fetchAgain: initMailbox,
  };
};
