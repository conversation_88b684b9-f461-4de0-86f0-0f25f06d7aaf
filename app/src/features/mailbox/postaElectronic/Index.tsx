import EmailIndex from "./EmailIndex";
import EmailMailbox from "./sections/EmailMailbox";
import { MailboxProvider } from "../provider/MailboxProvider";
import ComposeEmail from "../newEmail/ComposeEmail";
export const mailbox = () => [
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "/mailbox",
            element: <EmailIndex />,
        },
    },
    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "default/mailbox/client/:uid",
            element: (
                <MailboxProvider>
                    <EmailMailbox />
                </MailboxProvider>
            ),
        },
    },

    {
        target: "$ONE_LAYOUT_ROUTE",
        handler: {
            exact: true,
            path: "mailbox/write-email",
            element: <ComposeEmail />,
        },
    },
];
