import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../custom-components/PageTitle";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import AddIcon from "@mui/icons-material/Add";
import { useMailboxHooks } from "./hooks/useMailboxHooks";
import SettingsIcon from "@mui/icons-material/Settings";
import CircleOutlinedIcon from "@mui/icons-material/CircleOutlined";
import {
    ListItem,
    List,
    ListItemText,
    IconButton,
    Grid,
    ListItemIcon,
} from "@vapor/react-material";

const PostaElectronica = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const { emailAccounts, subdomain } = useMailboxHooks();

    return (
        <VaporPage>
            <PageTitle
                title={t("INDIRIZZI EMAIL DA CONSULTARE ")}
                showBackButton={false}
                actionButtons={[
                    {
                        label: t(" Nuovo account email"),
                        onclick: () =>
                            navigate("/emailaccounts/create", {
                                state: {
                                    prevPath: "/mailbox",
                                },
                            }),
                        variant: "outlined",
                        startIcon: <AddIcon />,
                    },
                ]}
            ></PageTitle>
            <VaporPage.Section>
                <Grid container>
                    <Grid item xs={4}>
                        <List>
                            {emailAccounts?.map((email: any) => (
                                <ListItem
                                    key={email}
                                    secondaryAction={
                                        <IconButton
                                            onClick={() =>
                                                navigate(
                                                    `/emailaccounts/update/${email.uniqueid}`,
                                                    {
                                                        state: {
                                                            prevPath:
                                                                "/mailbox",
                                                        },
                                                    }
                                                )
                                            }
                                        >
                                            <SettingsIcon />
                                        </IconButton>
                                    }
                                >
                                    <ListItemIcon>
                                        <CircleOutlinedIcon />
                                    </ListItemIcon>

                                    <ListItemText
                                        primary={email.email}
                                        color="primary.interactiveDefault"
                                        onClick={() =>
                                            navigate(
                                                `/default/mailbox/client/${email.uniqueid}`
                                            )
                                        }
                                        style={{ cursor: "pointer" }}
                                    />
                                </ListItem>
                            ))}

                            <ListItem>
                                <ListItemIcon>
                                    <CircleOutlinedIcon />
                                </ListItemIcon>

                                <ListItemText
                                    primary={`${t(
                                        "Smart Mailer"
                                    )} (*@${subdomain}.netlex.cloud)`}
                                    color="primary.interactiveDefault"
                                    onClick={() =>
                                        navigate(
                                            `/legacy/default/sorter/client`
                                        )
                                    }
                                    style={{ cursor: "pointer" }}
                                />
                            </ListItem>
                        </List>
                    </Grid>
                    <Grid item xs={8}></Grid>
                </Grid>
            </VaporPage.Section>
        </VaporPage>
    );
};

export default PostaElectronica;
