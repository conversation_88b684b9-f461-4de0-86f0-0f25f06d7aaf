import { Box, Typography, Paper, Stack, Divider } from "@mui/material";
import { useMailboxProvider } from "../../provider/MailboxProvider";
import Spinner from "../../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import AttachmentList from "./Attachments";

const EmailView = (props: any) => {
    const { t } = useTranslation();
    const { message, messageLoading } = useMailboxProvider();

    const isBase64 = (str: string): boolean => {
        try {
            return btoa(atob(str)) === str;
        } catch {
            return false;
        }
    };

    const { query } = props;

    if (messageLoading) {
        return <Spinner />;
    }

    const decodeBase64ToHtml = (encodedString: string): string => {
        try {
            return atob(encodedString);
        } catch (error) {
            console.error("Error decoding base64 string:", error);
            return "";
        }
    };

    const decodedHtml = isBase64(message?.body)
        ? decodeBase64ToHtml(message?.body)
        : message?.body;

    return (
        <Paper
            elevation={1}
            sx={{ padding: 2, maxWidth: "100%", margin: "auto" }}
        >
            <Box mb={2}>
                <Typography variant="h6" fontWeight="bold">
                    {message?.subject}
                </Typography>
                <Typography variant="body2">
                    <strong>{t("Da")}:</strong> {message?.from}
                </Typography>
                <Typography variant="body2">
                    <strong>{t("A")}:</strong> {message?.to}
                </Typography>
                <Typography variant="body2" align="right" sx={{ mt: 1 }}>
                    {message?.date}
                </Typography>
            </Box>

            <Divider />

            <Stack alignItems="center" spacing={2} sx={{ my: 4 }}>
                <Box sx={{ maxWidth: "100%" }}>
                    <div dangerouslySetInnerHTML={{ __html: decodedHtml }} />
                </Box>
            </Stack>
            <Divider />
            <AttachmentList attachments={message.attachments} query={query} />
        </Paper>
    );
};

export default EmailView;
