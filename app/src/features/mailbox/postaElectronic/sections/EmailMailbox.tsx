import VaporPage from "@vapor/react-custom/VaporPage";
import PageTitle from "../../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { Link } from "react-router-dom";
import EmailMainPage from "./EmailMainPage";
import { List, Typography } from "@vapor/react-material";
import { AlertBox } from "../../../../custom-components/AlertBox";
import { useMailboxProvider } from "../../provider/MailboxProvider";
import Spinner from "../../../../custom-components/Spinner";

const EmailMailbox = () => {
    const { t } = useTranslation();
    const { mailbox, isInitialized } = useMailboxProvider();
    const { error, emailAccount } = mailbox;

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <VaporPage>
            <PageTitle
                title={emailAccount?.email}
                showBackButton={true}
                pathToPrevPage="/mailbox"
            ></PageTitle>

            {error && (
                <>
                    <VaporPage.Section>
                        {" "}
                        <AlertBox
                            backgroundColor={"#f2dede"}
                            color={"#b94a48"}
                            textAlign={"left"}
                        >
                            <b>{t("Si è verificato un errore!")}</b>
                            <br />
                            {error}.
                            <br />
                            {t("Si prega di ")}
                            <Link
                                to="/emailaccounts"
                                style={{
                                    color: "#b94a48",
                                    textDecoration: "underline",
                                    cursor: "pointer",
                                }}
                            >
                                {t("Verificare")}
                            </Link>{" "}
                            {t(
                                " Le credenziali dell'indirizzo email e di riprovare"
                            )}
                        </AlertBox>
                        {emailAccount?.email?.includes("@gmail") && (
                            <>
                                <div style={{ paddingTop: "5px" }}>
                                    <Typography
                                        variant="bodySmall700"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Solo per utenti Gmail")}
                                    </Typography>
                                    <Typography
                                        variant="bodySmall500"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t(
                                            "In alcuni casi è necessario abilitare Netlex alla lettura delle email, seguendo le istruzioni seguenti"
                                        )}
                                        :
                                    </Typography>
                                    <List>
                                        <ul>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t("Accedere all' ")}
                                                    <Link
                                                        to="//gmail.com"
                                                        style={{
                                                            textDecoration:
                                                                "underline",
                                                            cursor: "pointer",
                                                        }}
                                                    >
                                                        {t("Account Gmail")}
                                                    </Link>{" "}
                                                    {t(
                                                        "E aprire il pannello delle impostazioni"
                                                    )}
                                                </Typography>
                                            </li>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t(
                                                        `Selezionare la scheda "Account e importazione" E fare clic sul link "Altre impostazioni dell'account Google".`
                                                    )}
                                                </Typography>
                                            </li>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t(
                                                        `Nella sezione Sicurezza, scorrere fino alla voce "Accesso a Google" E configurare la "Verifica in due passaggi".`
                                                    )}
                                                    <br />
                                                    {t(
                                                        `Dopo aver attivato la verifica in due passaggi, tornare alla sezione Sicurezza.`
                                                    )}
                                                </Typography>
                                            </li>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t(
                                                        `In "Accesso a Google", E' ora disponibile la funzionalità "Password per le app".`
                                                    )}
                                                </Typography>
                                            </li>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t(
                                                        `Creare una password personalizzata utilizzando l'opzione "Altra (Nome personalizzato)" E assegnare un nome a piacere.`
                                                    )}
                                                </Typography>
                                            </li>
                                            <li>
                                                {" "}
                                                <Typography
                                                    variant="bodySmall"
                                                    gutterBottom
                                                    component="div"
                                                >
                                                    {t(
                                                        `Utilizzare la password generata per configurare l'email in Studio -> Configurazioni -> Email, Al posto della password standard.`
                                                    )}
                                                </Typography>
                                            </li>
                                        </ul>
                                    </List>
                                </div>
                            </>
                        )}
                    </VaporPage.Section>
                </>
            )}

            {!error && <EmailMainPage />}
        </VaporPage>
    );
};

export default EmailMailbox;
