import React, { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "@1f/react-sdk";
import MailIcon from "@mui/icons-material/Mail";
import CreateIcon from "@mui/icons-material/Create";
import {
    Button,
    Grid,
    Box,
    Typography,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Paper,
} from "@vapor/react-material";
import { AlertBox } from "../../../../custom-components/AlertBox";
import { EmailFilters } from "./EmailFilters";
import InboxIcon from "@mui/icons-material/Inbox";
import SendIcon from "@mui/icons-material/Send";
import DeleteIcon from "@mui/icons-material/Delete";
import SpamIcon from "@mui/icons-material/Report";
import StarIcon from "@mui/icons-material/Star";
import ArchiveIcon from "@mui/icons-material/Archive";
import AllInboxIcon from "@mui/icons-material/AllInbox";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import ScheduleSendIcon from "@mui/icons-material/ScheduleSend";
import DraftsIcon from "@mui/icons-material/Drafts";
import OutboxIcon from "@mui/icons-material/Outbox";
import { EmailMessages } from "./EmailMessages";
import { useMailboxProvider } from "../../provider/MailboxProvider";
import Spinner from "../../../../custom-components/Spinner";
import EmailView from "./EmailView";

const getIcon = (folder: any) => {
    if (folder.isInbox) return <InboxIcon />;
    if (folder.isSent) return <SendIcon />;
    if (folder.isAllMessages) return <AllInboxIcon />;
    if (folder.isTrash) return <DeleteIcon />;
    if (folder.isSpam) return <SpamIcon />;
    if (folder.isStarred) return <StarIcon />;
    if (folder.printName === "Bin") return <DeleteIcon />;
    if (folder.printName === "Archive") return <ArchiveIcon />;
    if (folder.printName === "Conversation History") return <WorkHistoryIcon />;
    if (folder.printName === "Bozze") return <DraftsIcon />;
    if (folder.printName === "Scheduled") return <ScheduleSendIcon />;
    if (folder.printName === "Sent Items") return <OutboxIcon />;
    else return <InboxIcon />;
};

const EmailMainPage = () => {
    const { t } = useTranslation();

    const {
        mailbox,
        messages,
        getMessages,
        isInitialized,
        message,
        messageLoading,
        uid,
    } = useMailboxProvider();

    const { storage, folders, maxMailsPerPage, emailAccount } = mailbox;

    const [selectedFolder, setSelectedFolder] = useState<any>(null);

    const [query, setQuery] = useState<any>({
        folder: "",
        maxMailsPerPage: maxMailsPerPage ?? 13,
        page: 0,
        date: null,
        untilDate: null,
        query: "",
    });

    const [selectedMails, setSelectedMails] = useState([]);
    const [openedMessage, setOpenedMessage] = useState([]);

    React.useEffect(() => {
        if (folders.length > 0) {
            const folderSelect = emailAccount?.email.endsWith("@gmail.com")
                ? folders[0]
                : folders.find(
                      (folder: any) => folder.printName === "Posta in arrivo"
                  );

            setSelectedFolder(folderSelect);
            setQuery((prevQuery: any) => ({
                ...prevQuery,
                folder: folderSelect?.id ? folderSelect.id : folderSelect.name,
            }));
        }
    }, [folders.length]);

    const handleFolderChange = (folder: any) => {
        const newQuery = { ...query };
        newQuery.folder = folder.id ? folder.id : folder.name;
        setQuery(newQuery);
        getMessages(newQuery);
        setSelectedFolder(folder);
    };

    const navigate = useNavigate();
    const handleRefreshButton = () => {
        query.folder = selectedFolder?.id
            ? selectedFolder.id
            : selectedFolder.name;

        getMessages(query);
    };

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <VaporPage.Section>
            {" "}
            <Box sx={{ mt: 3 }}>
                <Grid container spacing={12}>
                    <Grid item md={3} xl={3}>
                        {" "}
                        <Typography
                            variant="titleSmall"
                            color="primary.main"
                            gutterBottom
                            component="div"
                        >
                            {t("Posta")}
                        </Typography>
                        <Box>
                            {" "}
                            {emailAccount?.email.endsWith("@gmail.com") && (
                                <AlertBox
                                    backgroundColor={"#f5f5f5"}
                                    color={"#000000c2"}
                                    textAlign={"left"}
                                >
                                    <Typography
                                        variant="bodySmall500"
                                        gutterBottom
                                        component="div"
                                    >
                                        {t("Spazio di archiviazione mailbox")}{" "}
                                        <MailIcon
                                            sx={{
                                                marginLeft: "5px",
                                                display: "inline-block",
                                                fontSize: "1rem",
                                            }}
                                        />
                                        <br />
                                        {`${storage?.percentage}% ${t(
                                            "usato"
                                        )} - ${storage?.usage} ${t("GB di ")} ${
                                            storage?.limit
                                        } ${t("GB")}`}
                                    </Typography>
                                </AlertBox>
                            )}
                            <div style={{ paddingTop: "10px" }}>
                                {" "}
                                <Button
                                    variant="contained"
                                    startIcon={<CreateIcon />}
                                    fullWidth
                                    onClick={() =>
                                        navigate(`/mailbox/write-email`, {
                                            state: {
                                                uid,
                                                folders,
                                                emailAccount,
                                            },
                                        })
                                    }
                                >
                                    {t("Scrivi")}
                                </Button>
                            </div>
                            <div style={{ paddingTop: "10px" }}>
                                {" "}
                                <List disablePadding>
                                    {folders?.map((folder: any) => {
                                        return (
                                            <ListItemButton
                                                key={folder.name}
                                                selected={
                                                    selectedFolder?.name ===
                                                    folder?.name
                                                }
                                                onClick={() =>
                                                    handleFolderChange(folder)
                                                }
                                            >
                                                <ListItemIcon>
                                                    {getIcon(folder)}
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={folder.printName}
                                                />
                                            </ListItemButton>
                                        );
                                    })}
                                </List>
                            </div>
                        </Box>
                    </Grid>
                    <Grid item md={9} xl={9}>
                        <EmailFilters
                            selectedMails={selectedMails}
                            messagesIds={messages?.map(
                                (message: any) => message.uid
                            )}
                            setSelectedMails={setSelectedMails}
                            setQuery={setQuery}
                            query={query}
                            handleRefreshButton={handleRefreshButton}
                            openedMessage={openedMessage}
                            selectedFolder={selectedFolder}
                        />
                        {messageLoading && (
                            <Paper
                                elevation={1}
                                sx={{
                                    padding: 2,
                                    maxWidth: "100%",
                                    margin: "auto",
                                }}
                            >
                                <Spinner />
                            </Paper>
                        )}
                        {Object.keys(message).length > 0 && (
                            <EmailView query={query} />
                        )}
                        {Object.keys(message).length === 0 &&
                            !messageLoading && (
                                <EmailMessages
                                    selectedMails={selectedMails}
                                    setOpenedMessage={setOpenedMessage}
                                    setSelectedMails={setSelectedMails}
                                    query={query}
                                />
                            )}
                    </Grid>
                </Grid>
            </Box>
        </VaporPage.Section>
    );
};

export default EmailMainPage;
