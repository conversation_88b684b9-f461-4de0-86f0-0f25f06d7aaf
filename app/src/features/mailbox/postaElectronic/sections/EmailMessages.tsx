import * as React from "react";
import { ListItemIcon, Checkbox, Divider } from "@vapor/react-material";
import AttachmentIcon from "@mui/icons-material/Attachment";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemText from "@mui/material/ListItemText";
import Typography from "@mui/material/Typography";
import { useMailboxProvider } from "../../provider/MailboxProvider";

export const EmailMessages = (props: any) => {
    const { openMessage, messages } = useMailboxProvider();

    const { selectedMails, setOpenedMessage, query, setSelectedMails } = props;

    const openSelectedMessage = (messageId: string) => {
        openMessage({
            messageUid: messageId,
            originFolder: query.folder,
        });
        setOpenedMessage([messageId]);
    };

    return (
        <>
            <List
                sx={{
                    width: "100%",
                    bgcolor: "background.paper",
                }}
            >
                {messages && messages.length > 0 ? (
                    messages.map((message: any) => (
                        <React.Fragment key={message.uid}>
                            <ListItem
                                alignItems="flex-start"
                                sx={{
                                    cursor: "pointer",
                                }}
                                secondaryAction={
                                    <Typography
                                        variant="captionSmall"
                                        gutterBottom
                                        component="div"
                                    >
                                        {message.hasAttachments && (
                                            <AttachmentIcon
                                                sx={{
                                                    fontSize: 17,
                                                    marginTop: "15px",
                                                    marginRight: "10px",
                                                }}
                                            />
                                        )}
                                        {message.formatted.date}
                                    </Typography>
                                }
                                disablePadding
                            >
                                <ListItemIcon>
                                    <Checkbox
                                        edge="start"
                                        checked={selectedMails.includes(
                                            message.uid
                                        )}
                                        tabIndex={-1}
                                        disableRipple
                                        onChange={() => {
                                            if (
                                                !selectedMails.includes(
                                                    message.uid
                                                )
                                            ) {
                                                setSelectedMails([
                                                    ...selectedMails,
                                                    message.uid,
                                                ]);
                                            } else {
                                                setSelectedMails(
                                                    selectedMails.filter(
                                                        (item: number) =>
                                                            item !== message.uid
                                                    )
                                                );
                                            }
                                        }}
                                    />
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography
                                            component="span"
                                            variant="body1"
                                            sx={{
                                                fontWeight:
                                                    message.seen === 0 ||
                                                    !message.seen
                                                        ? "bold"
                                                        : "normal",
                                                color:
                                                    message.seen === 0 ||
                                                    !message.seen
                                                        ? "black"
                                                        : "inherit",
                                            }}
                                        >
                                            {message.subject}
                                        </Typography>
                                    }
                                    secondary={
                                        <React.Fragment>
                                            <Typography
                                                component="span"
                                                variant="body2"
                                                sx={{
                                                    color: "text.primary",
                                                    display: "inline",
                                                    fontWeight:
                                                        message.seen === 0
                                                            ? "bold"
                                                            : "normal",
                                                }}
                                            >
                                                {
                                                    message.formatted.from[0]
                                                        .personal
                                                }
                                            </Typography>
                                        </React.Fragment>
                                    }
                                    onClick={() =>
                                        openSelectedMessage(message.uid)
                                    }
                                />
                            </ListItem>
                            <Divider className="MuiDivider-VaporLight" />
                        </React.Fragment>
                    ))
                ) : (
                    <ListItem>
                        <ListItemText
                            primary="Nessun messaggio presente"
                            sx={{
                                textAlign: "center",
                                color: "text.secondary",
                            }}
                        />
                    </ListItem>
                )}
            </List>
        </>
    );
};
