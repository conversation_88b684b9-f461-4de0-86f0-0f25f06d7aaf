import React from "react";
import { Card, Grid, Typography, IconButton, Avatar } from "@mui/material";
import { Link } from "react-router-dom";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";
import { useMailboxProvider } from "../../provider/MailboxProvider";

interface Attachment {
    num: number;
    ext: string;
    filename: string;
    size: string;
}

interface Props {
    attachments: Attachment[];
    query: any;
}

const AttachmentList: React.FC<Props> = ({ attachments, query }) => {
    const { message, downloadAttachment } = useMailboxProvider();

    const downloadEmailFile = async (num: number, fileName: string) => {
        const queryToSend = {
            num,
            messageUid: message.messageUid,
            folderId: query.folder,
            filename: fileName,
        };

        downloadAttachment(queryToSend);
    };
    return (
        <Grid container spacing={2} sx={{ pt: 2 }}>
            {(attachments || []).map((item) => (
                <Grid item xl={3} key={item.num}>
                    <Card variant="outlined" sx={{ mb: 1, boxShadow: 1 }}>
                        <Grid
                            container
                            alignItems="center"
                            spacing={2}
                            sx={{ p: 2 }}
                        >
                            <Grid item>
                                <Avatar
                                    sx={{
                                        bgcolor: "primary.light",
                                        color: "primary.main",
                                        width: 40,
                                        height: 40,
                                    }}
                                >
                                    {item.ext.toUpperCase()}
                                </Avatar>
                            </Grid>

                            <Grid item xs>
                                <Link to="#" style={{ textDecoration: "none" }}>
                                    <Typography
                                        variant="body1"
                                        fontWeight="bold"
                                        color="text.primary"
                                    >
                                        {item.filename}
                                    </Typography>
                                </Link>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                >
                                    {item.size}
                                </Typography>
                            </Grid>

                            <Grid item>
                                <IconButton
                                    color="primary"
                                    onClick={() =>
                                        downloadEmailFile(
                                            item.num,
                                            item.filename
                                        )
                                    }
                                >
                                    <CloudDownloadIcon />
                                </IconButton>
                            </Grid>
                        </Grid>
                    </Card>
                </Grid>
            ))}
        </Grid>
    );
};

export default AttachmentList;
