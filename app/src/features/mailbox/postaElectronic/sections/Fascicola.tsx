import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Box, Button, Checkbox, FormControl, FormControlLabel, Grid, InputLabel, MenuItem, Radio, RadioGroup, Select, TextField, Typography } from "@vapor/react-material";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import SpinnerButton from "../../../../custom-components/SpinnerButton";
import { useTranslation } from "@1f/react-sdk";
import { useMailboxProvider } from "../../provider/MailboxProvider";
import { debounce } from "lodash";
import useGetCustom from "../../../../hooks/useGetCustom";
import { removeLinks } from "../../../../utilities/utils";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

interface FormData {
    archiveSearchTarget: string;
    practiceSearch: any;
    title: string;
    fileName: string;
    attachmentOneDrive: string;
    saveFullMsg: string;
    createMacro: boolean;
    messageUid: number;
}

const defaultValues: FormData = {
    archiveSearchTarget: "codicearchivio",
    practiceSearch: {
        label: "",
        value: ""
    },
    title: "",
    fileName: "",
    attachmentOneDrive: "0",
    saveFullMsg: "0",
    createMacro: false,
    messageUid: 0
};

const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: 600,
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
    p: 4
};

const Fascicola = (props: { handleCloseFascicola: any; handleSubmitData: any; isOpenFromInsideMessage: boolean; selectedMails: any[]; loader: boolean; fileName: string }) => {
    const { handleCloseFascicola, handleSubmitData, selectedMails, fileName, loader } = props;
    const { control, handleSubmit, watch, setValue, reset } = useForm<FormData>({
        defaultValues
    });
    const [search, setSearch] = React.useState("");
    const [practicaDisabled, setPracticaDisabled] = useState(false);
    const [praticheSearchResults, setPracticaSearchResult] = React.useState([]);

    const { message } = useMailboxProvider();

    const practiceSearch = watch("practiceSearch");
    const archiveSearchTarget = watch("archiveSearchTarget");
    const { t } = useTranslation();

    const getTaskFileDataReq = useGetCustom(`default/archive/search?q=${search}&from=mailbox&target=${archiveSearchTarget}&noTemplateVars=true`);

    React.useEffect(() => {
        if (message && Object.keys(message).length > 0) {
            setValue("fileName", selectedMails.length > 1 ? fileName : message.subject);
            setValue("messageUid", message.uid);
        } else if (Object.keys(message).length === 0 && selectedMails.length === 1) {
            setValue("fileName", fileName);
        }
    }, [message]);

    React.useEffect(() => {
        const debouncedSearch = debounce(() => {
            getPratiche();
        }, 500);
        search && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [search]);

    const getPratiche = async () => {
        const response: any = await getTaskFileDataReq.doFetch(true);
        setPracticaSearchResult(
            response.data.map((item: any) => ({
                label: `${removeLinks(item.headerArchive)}`,
                value: item.uniqueid
            }))
        );
    };

    const onSubmit = (data: FormData) => {
        handleSubmitData(data);
        reset();
    };

    return (
        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={style}>
            <Typography variant="h6" gutterBottom>
                {t("Fascicola email")}
            </Typography>
            <Grid container style={{ display: "flex" }}>
                <Grid item md={12} lg={12}>
                    {" "}
                    <FormControl fullWidth margin="normal">
                        <InputLabel id="search-type-label">{t("Ricerca pratica per")}</InputLabel>
                        <Controller
                            name="archiveSearchTarget"
                            control={control}
                            render={({ field }: any) => (
                                <Select labelId="search-type-label" label="Ricerca pratica per" {...field}>
                                    <MenuItem value="codicepratica">{t("Codice pratica")}</MenuItem>
                                    <MenuItem value="descrizionepratica">{t("Descrizione pratica")}</MenuItem>
                                    <MenuItem value="codicearchivio">{t("Codice archivio")}</MenuItem>
                                    <MenuItem value="oggettopratica">{t("Oggetto")}</MenuItem>
                                    <MenuItem value="controparte">{t("Controparte")}</MenuItem>
                                    <MenuItem value="cliente">{t("Cliente")}</MenuItem>
                                    <MenuItem value="RG">{t("RG")}</MenuItem>
                                </Select>
                            )}
                        />
                    </FormControl>
                </Grid>
            </Grid>

            <Grid container sx={{ display: "flex", pt: 1 }}>
                <Grid item md={10} lg={10}>
                    <CustomAutocomplete
                        options={praticheSearchResults}
                        sx={{ width: "90%" }}
                        isOptionEqualToValue={(option: any, value: any) => {
                            return option.value === value.value;
                        }}
                        renderInput={(params: any) => {
                            return <TextField {...params} placeholder={t("Cerca pratica ")} />;
                        }}
                        onInputChange={(_event: any, value: any, reason: string) => {
                            if (reason === "input") {
                                setSearch(value);
                            }
                        }}
                        disabled={practicaDisabled}
                        value={practiceSearch.label}
                        onChange={(_event: any, item: any) => {
                            setValue("practiceSearch", item);
                            setPracticaDisabled(true);
                        }}
                    />
                </Grid>
                <Grid item md={2} lg={2}>
                    <Button
                        variant="contained"
                        color="primary"
                        disabled={!practicaDisabled}
                        onClick={() => {
                            setValue("practiceSearch", {
                                label: "",
                                value: ""
                            });
                            setSearch("");
                            setPracticaDisabled(false);
                        }}
                    >
                        {t("Cambia")}
                    </Button>
                </Grid>
            </Grid>
            <Grid container style={{ display: "flex" }}>
                <Grid item md={12} lg={12}>
                    <Controller name="title" control={control} render={({ field }: any) => <TextField label={t(`La fascicolazione avverrà nella sezione "Email fascicolate" della pratica`)} fullWidth margin="normal" placeholder={t("Descrizione")} {...field} />} />
                </Grid>
            </Grid>
            {selectedMails.length <= 1 && (
                <Grid container style={{ display: "flex" }}>
                    <Grid item md={11} lg={11}>
                        <Controller name="fileName" control={control} render={({ field }: any) => <TextField label={t(`Se non specificata verrà utilizzata la descrizione "Email del gg-mm-aaaa"`)} fullWidth margin="normal" placeholder={t("Nome file senza estensione")} {...field} />} />
                    </Grid>
                    <Grid item md={1} lg={1}>
                        {" "}
                        <Typography variant="body700" gutterBottom component="div" sx={{ pt: 8 }}>
                            {t(".eml")}
                        </Typography>
                    </Grid>
                </Grid>
            )}

            <Typography variant="bodySmall" gutterBottom component="div" sx={{ pt: 1 }}>
                {t(`Se non specificato verrà utilizzato il nome "Email_numeromessaggio"`)}
            </Typography>

            {(message?.attachments?.length > 0 || selectedMails.length > 1) && (
                <FormControl component="fieldset" margin="normal">
                    <Typography variant="body1" gutterBottom>
                        {t("Salva documenti allegati")}:
                    </Typography>
                    <Controller
                        name="attachmentOneDrive"
                        control={control}
                        render={({ field }: any) => (
                            <RadioGroup row {...field}>
                                <FormControlLabel value="0" control={<Radio />} label={t("No")} />
                                <FormControlLabel value="1" control={<Radio />} label={t("One Drive")} />
                                <FormControlLabel value="2" control={<Radio />} label={t("In documenti")} />
                            </RadioGroup>
                        )}
                    />
                </FormControl>
            )}

            <Grid>
                <FormControl component="fieldset" margin="normal">
                    <Typography variant="body1" gutterBottom>
                        {t("Visualizzazione da salvare")}:
                    </Typography>
                    <Controller
                        name="saveFullMsg"
                        control={control}
                        render={({ field }: any) => (
                            <RadioGroup row {...field}>
                                <FormControlLabel value="0" control={<Radio />} label={t("Semplice")} />
                                <FormControlLabel value="1" control={<Radio />} label={t("Avanzata")} />
                            </RadioGroup>
                        )}
                    />
                </FormControl>
            </Grid>

            <Grid>
                <Controller name="createMacro" control={control} render={({ field }: any) => <FormControlLabel control={<Checkbox {...field} checked={field.value} />} label={t("Crea macro")} />} />
                <Typography variant="bodySmall" gutterBottom component="div">
                    {t(`Utilizzando la fascicolazione "Avanzata" Non sarà possibile inviare in conservazione digitale`)}
                </Typography>
            </Grid>

            <Box mt={2} display="flex" alignItems="center" justifyContent="space-between">
                {loader && (
                    <Typography mt={1} variant="bodySmall500" component="div" color="primary.interactiveDefault" gutterBottom>
                        {t("Operazione in corso attendere prego...")}
                    </Typography>
                )}

                <Box display="flex" gap={2}>
                    {practicaDisabled && <SpinnerButton label={t("Fascicola")} variant="contained" isLoading={loader} type="submit" startIcon={<CheckIcon />} />}

                    <Button variant="outlined" color="secondary" onClick={handleCloseFascicola} startIcon={<CloseIcon />}>
                        {t("Annulla")}
                    </Button>
                </Box>
            </Box>
        </Box>
    );
};

export default Fascicola;
