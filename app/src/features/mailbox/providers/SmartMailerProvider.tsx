import React, {
    ReactNode,
    useState,
    useCallback,
    useContext,
    useReducer,
} from "react";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useApi } from "../../../store/ApiStore";
import { DEFAULT_SUBDOMAIN } from "../../../utilities/constants";
import useGetCustom from "../../../hooks/useGetCustom";

const INITIALIZE = "INITIALIZE";
const SET_SMART_MAIL = "SET_SMART_MAIL";
const SET_MESSAGES = "SET_MESSAGES";
const OPENED_MESSAGE = "OPENED_MESSAGE";

export type ActionMap<M extends { [index: string]: any }> = {
    [Key in keyof M]: M[Key] extends undefined
        ? { type: Key }
        : { type: Key; payload: M[Key] };
};

interface IMailBox {
    folders: any[];
    storage: any;
    messages: any[];
    error: any;
    emailAccount: any;
    maxMailsPerPage: number;
    countMessages: number;
}

type MailBoxActionTypes = {
    [INITIALIZE]: {
        isInitialized: boolean;
        mailbox: IMailBox;
        messages: any[];
        message: any;
        messageLoading: boolean;
    };
    [SET_SMART_MAIL]: {
        mailbox: IMailBox;
    };
    [SET_MESSAGES]: {
        messages: any[];
    };
    [OPENED_MESSAGE]: {
        message: any;
        messageLoading: boolean;
    };
};

export type MailboxState = {
    isInitialized: boolean;
    mailbox: IMailBox;
    messages: any[];
    message: any;
    messageLoading: boolean;
};

const initialState: MailboxState = {
    isInitialized: false,
    mailbox: {
        folders: [],
        storage: {},
        error: {},
        emailAccount: {},
        messages: [],
        maxMailsPerPage: 13,
        countMessages: 0,
    },
    messages: [],
    message: {},
    messageLoading: false,
};

const JWTReducer = (
    state: MailboxState,
    action: ActionMap<MailBoxActionTypes>[keyof ActionMap<MailBoxActionTypes>]
) => {
    switch (action.type) {
        case INITIALIZE:
            return {
                isInitialized: true,
                messages: [],
                mailbox: action.payload.mailbox,
                message: {},
                messageLoading: false,
            };
        case SET_SMART_MAIL:
            return {
                ...state,
                isInitialized: true,
                mailbox: action.payload.mailbox,
            };

        case SET_MESSAGES:
            return {
                ...state,
                messages: action.payload.messages,
            };

        case OPENED_MESSAGE:
            return {
                ...state,
                message: action.payload.message,
                messageLoading: action.payload.messageLoading,
            };
        default:
            return state;
    }
};

export interface SmartMailContextType {
    isInitialized: boolean;
    mailbox: any;
    messages: any[];
    message: any;
    messageLoading: boolean;
    uid: string;
    fetchSmartMail: () => void;
    openMessage: (query: any) => void;
    clearMessage: () => void;
    downloadAttachment: (query: any) => void;
}

export const MailboxContext = React.createContext<SmartMailContextType>({
    isInitialized: false,
    mailbox: {
        folders: [],
        storage: {},
        error: {},
        emailAccount: {},
        messages: [],
        maxMailsPerPage: 13,
        countMessages: 0,
        subdomain: "test",
        smart_mailer_enable: false,
    },
    messages: [],
    message: {},
    messageLoading: false,
    uid: "",
    fetchSmartMail: () => {},
    openMessage: () => {},
    clearMessage: () => {},
    downloadAttachment: () => {},
});

export function useSmartMailerProvider() {
    return useContext(MailboxContext);
}

export function SmartMailerProvider({ children }: { children: ReactNode }) {
    const [state, dispatch] = useReducer(JWTReducer, initialState);
    const [error, setError] = useState<any>(null);
    const params = useParams<{ uid: string }>();
    const { api }: any = useApi();

    const usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;

    const smartMailerRequest = useGetCustom("sorter/client");

    const openMessageReq = useGetCustom(`sorter/openmessage`);

    useEffect(() => {
        fetchSmartMail();
    }, []);

    const fetchSmartMail = useCallback(async () => {
        return await smartMailerRequest
            .doFetch()
            .then((response: any) => {
                dispatch({
                    type: SET_SMART_MAIL,
                    payload: {
                        mailbox: response.data,
                    },
                });

                dispatch({
                    type: SET_MESSAGES,
                    payload: {
                        messages: response.data.messages,
                    },
                });
            })
            .catch((error) => {
                console.log({ error });
                setError(error);
            });
    }, [params.uid]);

    const openMessage = async (query: any) => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: true,
            },
        });
        query.uid = "";
        query.originFolder = "";
        query.noTemplateVars = true;
        const response: any = await openMessageReq.doFetch(true, query);

        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: { ...response.data, messageUid: query.messageUid },
                messageLoading: false,
            },
        });
    };

    const downloadAttachment = async (query: any) => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: state.message,
                messageLoading: true,
            },
        });
        const url = `https://${usedSubdomain}.netlex.cloud/sorter/get-attachment?uid=${params.uid}&noTemplateVars=true&num=${query.num}&messageUid=${query.messageUid}&originFolder=${query.folder}&name=${query.name}`;
        window.open(url);

        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: state.message,
                messageLoading: false,
            },
        });
    };

    const clearMessage = () => {
        dispatch({
            type: OPENED_MESSAGE,
            payload: {
                message: {},
                messageLoading: false,
            },
        });
    };

    return (
        <MailboxContext.Provider
            value={{
                ...state,
                uid: params.uid as string,
                fetchSmartMail,
                openMessage,
                clearMessage,
                downloadAttachment,
            }}
        >
            {error ? <h1>Error</h1> : children}
        </MailboxContext.Provider>
    );
}
