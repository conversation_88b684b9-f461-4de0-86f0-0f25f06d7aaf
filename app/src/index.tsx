/**
 * OneFront SDK
 * ============
 *
 * This package provides most of the Services that you may need
 * in a OnePlatform Application.
 *
 * You write simple React Components and interact with the SDK
 * using modern hooks API.
 *
 * The SDK guides you into dividing you App into isolated Features
 * that you will glue together using ForrestJS APIs.
 */
import { OneFront } from "@1f/react-sdk";
import { LicenseInfo } from "@mui/x-license-pro";

/**
 * Import the App's Features
 * =========================
 *
 * A Feature is an isolated portion of business logic that
 * interacts with the App's Services
 */
import { sidebarMenu } from "./layout/toolbar/index";
import { oneAuth } from "./features/one-auth";
import { root } from "./features/root";
import { archive } from "./features/archive";
import { anagrafiche } from "./features/anagrafiche";
import { fatture } from "./features/fatturazione/fatture";
import { infocamere } from "./features/utility/infocamere/routes";
import { tabelle } from "./features/studio/tabelle";
import { clauses } from "./features/studio/clauses";
import { emailmodels } from "./features/studio/emailmodels";
import { cerved } from "./features/utility/cerved";
import { utentiEsterni } from "./features/studio/utenti-esterni";
import { universalEmbedding } from "./features/universal-embedding";
import { internalUsers } from "./features/studio/internalUser/routes";
import { agenda } from "./features/agenda/generalCalendar/routes";
import { agendaLegale } from "./features/agenda/agenda/Index";
import { agendaDelayed } from "./features/agenda/agendaDelayed/Index";
import { customSoftware } from "./features/studio/customSoftware/Index";
import { LoghiIndex } from "./features/studio/letterHeads/Index";
import { groups } from "./features/studio/Gruppi utenti";
import { printsIndex } from "./features/studio/prints";
import { customArchiveMenu } from "./features/studio/customArchiveMenu";
import { PrintTemplateStudioAssociatedIndex } from "./features/studio/printTemplatesStudio";
import { CalendarAccountsIndex } from "./features/studio/calendarAccounts";
import { NetlexSettings } from "./features/studio/Settings";
import { campidinamici } from "./features/studio/Configurations/Dynamic-fields";
import { OfficeSettingsIndex } from "./features/studio/officeSettings";
import { sollecitisospesi } from "./features/fatturazione/sollecitisospesi";
import { smartMail } from "./features/studio/smart-mail";
import { Email } from "./features/studio/email";
import { delegations } from "./features/studio/Configurations/Delegations";
import { profititDistribution } from "./features/studio/profits-distribution";
import { RecuperoCreditiIndex } from "./features/studio/recuperoCrediti";
import { dashboardIndex } from "./features/dashboard/dashboardIndex";
import { certificateIndex } from "./features/studio/certificates";
import { studioCategories } from "./features/studio/Configurations/Categories";
import { remoteSignature } from "./features/studio/Configurations/RemoteSignature";
import { tariffs } from "./features/utility/custom-rates";
import { dashoardFatture } from "./features/dashboard/dashboardFatture";
import { dashboardPratiche } from "./features/dashboard/dashboardPratiche";
import { dashoardClienti } from "./features/dashboard/dashboardClienti";
import { timesheet } from "./features/agenda/timesheet/routes";
import { documentiinuscita } from "./features/documents/documentiinuscita";
import { biblioteca } from "./features/utility/Biblioteca";
import { impegni } from "./features/utility/Impegni";
import { documents } from "./features/documents/GestioneDocumenti";
import { monthlyReport } from "./features/agenda/monthlyReport/index";
import { dashboardTimesheet } from "./features/dashboard/dashboardTimesheet";
import { utilitylegal } from "./features/utility/utility-legal";
import { deadlines } from "./features/deadlines";
import { pecNotifications } from "./features/mailbox/pecNotification/index";
import { mailbox } from "./features/mailbox/postaElectronic/Index";
import { smartmailer } from "./features/mailbox/smartMailer";
import { other } from "./features/utility/Altro/workflow";
import { entries } from "./features/utility/Altro/Entries/Index";
import { log_pec } from "./features/utility/Altro/LogPec";
import { items } from "./features/utility/Altro/Items";
import { recoveryid } from "./features/utility/Altro/Recovery";
import { statistics } from "./features/utility/Altro/Statistics";
import { uploadbsttl } from "./features/utility/Altro/Entries/Uploadbsttl";
import { importModel } from "./features/utility/ImportModel";
import { macro } from "./features/studio/Configurations/Macro";
import { querybuilder } from "./features/utility/querybuilder";
const appId = import.meta.env.VITE_APP_ID || "demo";

const proxyBaseUrl = import.meta.env.VITE_APP_PROXY_BASE_URL || "/api";

const VITE_SECTIONS_TO_SHOW = import.meta.env.VITE_SECTIONS_TO_SHOW || "all";

const sectionMap: { [key: string]: any } = {
    anagrafiche: anagrafiche,
    archive: archive,
    infocamere: infocamere,
    tabelle: tabelle,
    clauses: clauses,
    cerved: cerved,
    utentiEsterni: utentiEsterni,
    fatture: fatture,
    sollecitisospesi: sollecitisospesi,
    internalUsers: internalUsers,
    customSoftware: customSoftware,
    groups: groups,
    agenda: agenda,
    printsIndex: printsIndex,
    smartMail: smartMail,
    customArchiveMenu: customArchiveMenu,
    LoghiIndex: LoghiIndex,
    PrintTemplateStudioAssociatedIndex: PrintTemplateStudioAssociatedIndex,
    CalendarAccountsIndex: CalendarAccountsIndex,
    campidinamici: campidinamici,
    NetlexSettings: NetlexSettings,
    Email: Email,
    delegations: delegations,
    RecuperoCreditiIndex: RecuperoCreditiIndex,
    OfficeSettingsIndex: OfficeSettingsIndex,
    profititDistribution: profititDistribution,
    emailmodels: emailmodels,
    dashboardIndex: dashboardIndex,
    studioCategories: studioCategories,
    remoteSignature: remoteSignature,
    certificateIndex: certificateIndex,
    tariffs: tariffs,
    dashoardFatture: dashoardFatture,
    dashboardPratiche: dashboardPratiche,
    timesheet: timesheet,
    documentiinuscita: documentiinuscita,
    agendaLegale: agendaLegale,
    agendaDelayed: agendaDelayed,
    dashoardClienti: dashoardClienti,
    impegni: impegni,
    bibloteca: biblioteca,
    documents: documents,
    macro: macro,
    monthlyReport: monthlyReport,
    dashboardTimesheet: dashboardTimesheet,
    utilitylegal: utilitylegal,
    deadlines: deadlines,
    pecnotifications: pecNotifications,
    mailbox: mailbox,
    smartmailer: smartmailer,
    other: other,
    entries: entries,
    log_pec: log_pec,
    items: items,
    recoveryid: recoveryid,
    statistics: statistics,
    uploadbsttl: uploadbsttl,
    importModel: importModel,
    querybuilder: querybuilder
};

let features = [sidebarMenu, oneAuth, root, universalEmbedding];

const allSectionsAreVisible = VITE_SECTIONS_TO_SHOW === "all";
if (allSectionsAreVisible) {
    features = [...features, ...Object.values(sectionMap)];
} else {
    const sectionsToShow = VITE_SECTIONS_TO_SHOW.split(",");

    sectionsToShow.forEach((section: string) => {
        if (sectionMap[section]) {
            features.push(sectionMap[section]);
        }
    });
}

LicenseInfo.setLicenseKey(import.meta.env.VITE_APP_MUI_TOKEN as string);
/**
 * Configure and Run the App
 * =========================
 *
 * The boot process returns a Promise that you can handle
 * to figure out whether all was good or not.
 */
OneFront.run({
    trace: "compact",
    settings: {
        one: {
            // Forces a JWT refresh on every FPR
            appId,
            axios: {
                proxy: {
                    baseUrl: proxyBaseUrl
                }
            },
            auth: {
                token: {
                    verify: false,
                    refresh: false
                }
            },
            // loading: {
            //   delay: 2000,
            // },
            layout: {
                appSwitch: false
            },
            // login: {
            //   target: {
            //     params: ["mode=redirect", "appId=app3000"],
            //   },
            // },
            i18n: {
                options: {
                    defaultNS: "app"
                }
            }
        }
    },
    features
}).catch(console.error);
