import { create } from 'zustand';
import Configs from '../models/permissions/Configs';

const useConfigs = create((set) => {
    return {
        configs: new Configs({}), // Instantiate an instance of Configs with initial empty data
        isLoading: false,
        error: null,
        reset: () => set({ configs: new Configs({}), isLoading: false, error: null }),
        getConfigs: async (configDataRequest: any) => {
            set({ isLoading: true });
            try {
                const response = await configDataRequest.doFetch(true, {}, true);
                const configs = new Configs(response?.data?.configs);
                set({ configs, isLoading: false }); // Update configs with new instance of Configs
                if (!response) {
                    throw new Error("Failed to fetch configuration data");
                }
                
                return configs;
            } catch (error) {
                console.error("Fetch configs error: ", error);
                set({ error, isLoading: false });
            }
        },
        setConfigs: (configs: Configs) => set({ configs }),
    };
});
export { useConfigs };
