import { create } from 'zustand';
import { useBrowserStorage } from '@1f/react-sdk';

const useApi = create((set, get: any) => {
  const apiStorage = useBrowserStorage("apiStorage");

  return {
    api: {},
    sessionToken: "",
    reset: () => {
      set({ api: {} });
      apiStorage.clean();
    },
    setApi: (api: any) => {
      set({ ...get().api, api });
      apiStorage.set({ ...apiStorage.get(), ...api });
    },
    setSessionToken: (sessionToken: string) => {
      set({ sessionToken });
    },
    hydrateApi: async () => {
      const api = await apiStorage.get();
      set({ api });
      return api;
    }
  };
});

export { useApi };
