import { create } from 'zustand';
import { useBrowserStorage } from '@1f/react-sdk';

const useLegacySwitch = create((set) => {
  const legacySwitchStorage = useBrowserStorage("legacySwitch");

  return {
    legacySwitch: false,
    reset: () => {
      set({ legacySwitch: false });
      legacySwitchStorage.clean();
    },
    setLegacySwitch: (value: boolean) => {
      set({ legacySwitch: value });
      legacySwitchStorage.set({ value });
    },
    hydrateLegacySwitch: async () => {
      const legacySwitchValue = await legacySwitchStorage.get();
      let legacySwitch = false;
      if (legacySwitchValue) {
        legacySwitch = legacySwitchValue.value;
      }
      set({ legacySwitch });
      return legacySwitch;
    }
  };
});

export { useLegacySwitch };
