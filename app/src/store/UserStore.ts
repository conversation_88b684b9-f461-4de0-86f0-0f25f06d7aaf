import { create } from "zustand";
import User from "../models/permissions/User";
import Modules from "../models/permissions/Modules";

const useUser = create((set) => {
    return {
        user: "",
        modules: "",
        menuConditions: null,
        isLoading: false,
        error: null,
        reset: () =>
            set({ user: "", modules: "", isLoading: false, error: null }),
        getUser: async (configDataRequest: any) => {
            set({ isLoading: true });
            try {
                const response = await configDataRequest.doFetch(
                    true,
                    {},
                    true
                );
                const user = User.fromJson({
                    ...response?.data?.loggedUser,
                    account: response?.data?.account,
                    isLawyerUser: response?.data?.isLawyerUser,
                    isPracticeSecretaryAndOther:
                        response?.data?.isPracticeSecretaryAndOther,
                    groups: response?.data?.groups,
                    people: response?.data?.people,
                    configs: response?.data?.configs,
                });

                const modules = Modules.from<PERSON>son({
                    ...response.data.loggedUser,
                    show: response.data.netlexSettings.show,
                    campi_agenda: response.data.loggedUser.campi_agenda,
                    isAdmin: response.data.isAdmin,
                    isDiskSpaceFull: response.data.isDiskSpaceFull,
                    isGiapppichelliInstance:
                        response.data.isGiapppichelliInstance,
                    provisioningRow: response.data.provisioningRow,
                    netlexSettings: response.data.netlexSettings,
                    gitVersion: response.data.gitVersion,
                    lastLoginDate: response.data.lastLoginDate,
                    timertaskdata: response?.data?.timertaskdata,
                    activetimesheet: response?.data?.activetimesheet,
                    configs: response?.data?.configs?.app?.item_bool,
                });

                const menuConditions = response?.data?.menuConditions || null;

                set({ user, modules, menuConditions, isLoading: false });
                if (!response) {
                    throw new Error("Failed to fetch configuration data");
                }

                return user;
            } catch (error) {
                set({ error, isLoading: false });
            }
        },
        setMenuConditions: (menuConditions: any) => set({ menuConditions }),
    };
});

export { useUser };
