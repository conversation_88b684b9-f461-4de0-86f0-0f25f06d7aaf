import { useState, useEffect } from "react"

const useStateStorage = (key: any, initialValue: any = {}) => {
  const [storedValue, setStoredValue] = useState<any>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn("Errore nel recuperare i dati dal localStorage:", error)
      return initialValue
    }
  })

  useEffect(() => {
    try {
      const valueToStore = JSON.stringify(storedValue)
      window.localStorage.setItem(key, valueToStore)
    } catch (error) {
      console.warn("Errore nel salvare i dati nel localStorage:", error)
    }
  }, [storedValue])

  const setValue = (value: any) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      window.localStorage.setItem(key, JSON.stringify(valueToStore))
    } catch (error) {
      console.warn("Errore nel salvare i dati nel localStorage:", error)
    }
  }

  return [storedValue, setValue]
}

export default useStateStorage
