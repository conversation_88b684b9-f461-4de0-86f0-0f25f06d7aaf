import { useNavigate } from 'react-router-dom';
import { useLegacySwitch } from '../store/LegacySwitchStore';

const useCustomNavigate = () => {
	const navigate = useNavigate();
	const { legacySwitch }: any = useLegacySwitch();

	const customNavigate = (path: string) => {

		let finalPath = path;

		if (legacySwitch && !path.includes('/legacy')) {
			finalPath = `/legacy${path}`;
		}

		navigate(finalPath);
	};

	return { customNavigate };
}

export default useCustomNavigate;