import { useRequest, useAuth } from "@1f/react-sdk";
import { appendXDebugParamToUrl } from "../utilities/utils";
import {
    BASE_URN,
    BASE_CUSTOM_HEADERS,
    DEFAULT_SUBDOMAIN,
} from "../utilities/constants";
import { useConfigs } from "../store/ConfigStore";
import { useUser } from "../store/UserStore";
import { useApi } from "../store/ApiStore";
import { useLegacySwitch } from "../store/LegacySwitchStore";

const usePostCustom = (path: any, customSubdomain: any = null) => {
    const { dropToken, isPublic } = useAuth();
    const { reset: resetConfig }: any = useConfigs();
    const { reset: resetUser }: any = useUser();
    const { api, reset: resetApi }: any = useApi();
    const { legacySwitch }: any = useLegacySwitch();

    const headers: any = {
        ...BASE_CUSTOM_HEADERS,
        "is-legacy": legacySwitch ? "true" : "false",
    };

    let usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;
    if (customSubdomain) {
        usedSubdomain = customSubdomain;
    }

    const url = `${BASE_URN}://${usedSubdomain}/${appendXDebugParamToUrl(
        path
    )}`;

    const { loading, data, error, fetch, hasLoaded } = useRequest({
        method: "post",
        lazy: true,
        url,
        headers,
        withCredentials: true,
    });

    const doFetch = (
        isAsyncReqType: boolean = false,
        data: any = {},
        method: string = "post",
        responseType: string = "json",
        isApplicationJson: boolean = false
    ) => {
        let fetchData = {
            data,
            method,
            responseType,
            headers,
        };

        if (isApplicationJson) {
            fetchData = {
                data: JSON.stringify(data),
                method,
                responseType,
                headers: {
                    ...headers,
                    "Content-Type": "application/json",
                },
            };
        }

        return new Promise((resolve, reject) => {
            fetch(fetchData)
                .then((response: any) => {
                    const { loggedUser } = response.data;

                    if (isPublic || (!loggedUser && !isAsyncReqType)) {
                        dropToken();
                        resetUser();
                        resetApi();
                        resetConfig();
                        reject("logged out");

                        return;
                    }

                    resolve(response);
                })
                .catch((e: any) => {
                    if (e.status === 401) {
                        dropToken();
                        resetUser();
                        resetApi();
                        resetConfig();

                        return;
                    }

                    console.log("POST ERROR", e);
                    reject(e);
                });
        });
    };

    return { loading, data, error, doFetch, hasLoaded };
};

export default usePostCustom;
