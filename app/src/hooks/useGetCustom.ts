import { useRequest, useAuth } from "@1f/react-sdk";
import { appendXDebugParamToUrl } from "../utilities/utils";
import {
    BASE_URN,
    BASE_CUSTOM_HEADERS,
    DEFAULT_SUBDOMAIN,
} from "../utilities/constants";
import { useConfigs } from "../store/ConfigStore";
import { useUser } from "../store/UserStore";
import { useApi } from "../store/ApiStore";
import { useLegacySwitch } from "../store/LegacySwitchStore";

const useGetCustom = (
    path: any,
    params: any = {},
    customSubdomain: any = null,
    isBlob: boolean = false
) => {
    const { dropToken, isPublic } = useAuth();
    const { reset: resetConfig }: any = useConfigs();
    const { reset: resetUser }: any = useUser();
    const { api, reset: resetApi }: any = useApi();
    const { legacySwitch }: any = useLegacySwitch();

    let usedSubdomain = (api && api.subdomain) || DEFAULT_SUBDOMAIN;
    if (customSubdomain) {
        usedSubdomain = customSubdomain;
    }

    const url = `${BASE_URN}://${usedSubdomain}/${appendXDebugParamToUrl(
        path
    )}`;

    const headers = {
        ...BASE_CUSTOM_HEADERS,
        "is-legacy": legacySwitch ? "true" : "false",
    };

    const { loading, data, error, fetch, hasLoaded } = useRequest({
        method: "get",
        lazy: true,
        url,
        headers,
        withCredentials: true,
        ...(isBlob ? { responseType: "blob" } : {}),
    });

    const doFetch = (
        isAsyncReqType: boolean = false,
        data: any = {},
        passToPublic: boolean = false // If true, the request will be made even if the user is not logged into onefront (isPublic means the request is public for onefront)
    ) => {
        return new Promise((resolve, reject) => {
            fetch({
                params: { ...params, ...data },
            })
                .then((response: any) => {
                    const { loggedUser } = response.data;

                    if (
                        (!passToPublic && isPublic) ||
                        (!loggedUser && !isAsyncReqType)
                    ) {
                        dropToken();
                        resetUser();
                        resetApi();
                        resetConfig();
                        reject("logged out");

                        return;
                    }

                    resolve(response);
                })
                .catch((e: any) => {
                    if (e.status === 401) {
                        dropToken();
                        resetUser();
                        resetApi();
                        resetConfig();

                        return;
                    }

                    console.log("GET ERROR", e);
                    reject(e);
                });
        });
    };

    return { loading, data, error, doFetch, hasLoaded };
};

export default useGetCustom;
