export default class User {
    private account: any;
    private Email: string;
    private agyo: string;
    private agyo_access_token: string;
    private agyo_id: string;
    private agyo_in: string;
    private agyo_last_sync: string;
    private agyo_office: string;
    private agyo_out: string;
    private attivo: string;
    private campi_agenda: string;
    private cct: string;
    private cctActive: string;
    private cct_password: string;
    private cct_username: string;
    private codiceavvocato: string;
    private default_calendar: string;
    private default_page_size: string;
    private emailNotifications: string;
    private enable_timesheet: string;
    private external_can_upload: string;
    private feNewYear: string;
    private hideChromeAlert: string;
    private id: string;
    private identificatore_codice: string;
    private isAccounting: string;
    private isConsultant: string;
    private isExternal: string;
    private isLawyer: string;
    private isLawyerUser: boolean;
    private isNetlexcassazioneUser: string;
    private isNetlexeasynotaUser: string;
    private isNetlexfeUser: string;
    private isNetlexpdaUser: string;
    private isNetlexpdabasicUser: string;
    private isNetlexpdafreeUser: string;
    private isNetlexpdaulofUser: string;
    private isSuperAdmin: boolean;
    private legacy_mode: string;
    private nocturnal_synch_status: string;
    private nome: string;
    private nome_avvocato: string;
    private nomeutente: string;
    private office: string;
    private one_drive_uniqueid: string;
    private pda_registration_status: string;
    private permissions: string;
    private qualificautente: string;
    private regime_fiscale_id: string;
    private ruolo_permessi: string;
    private terms_of_use_id: string;
    private theme: string;
    private tipo_cassa_id: string;
    private tipoutente: string;
    private tipoutentenome: string;
    private uniqueid: string;
    private gitVersion: string;
    private isPracticeSecretaryAndOther: boolean;
    private groups: any;
    private people: any;
    private configs: any;
    constructor(
        account: any,
        Email: string,
        agyo: string,
        agyo_access_token: string,
        agyo_id: string,
        agyo_in: string,
        agyo_last_sync: string,
        agyo_office: string,
        agyo_out: string,
        attivo: string,
        campi_agenda: string,
        cct: string,
        cctActive: string,
        cct_password: string,
        cct_username: string,
        codiceavvocato: string,
        default_calendar: string,
        default_page_size: string,
        emailNotifications: string,
        enable_timesheet: string,
        external_can_upload: string,
        feNewYear: string,
        hideChromeAlert: string,
        id: string,
        identificatore_codice: string,
        isAccounting: string,
        isConsultant: string,
        isExternal: string,
        isLawyer: string,
        isLawyerUser: boolean,
        isNetlexcassazioneUser: string,
        isNetlexeasynotaUser: string,
        isNetlexfeUser: string,
        isNetlexpdaUser: string,
        isNetlexpdabasicUser: string,
        isNetlexpdafreeUser: string,
        isNetlexpdaulofUser: string,
        isSuperAdmin: boolean,
        legacy_mode: string,
        nocturnal_synch_status: string,
        nome: string,
        nome_avvocato: string,
        nomeutente: string,
        office: string,
        one_drive_uniqueid: string,
        pda_registration_status: string,
        permissions: string,
        qualificautente: string,
        regime_fiscale_id: string,
        ruolo_permessi: string,
        terms_of_use_id: string,
        theme: string,
        tipo_cassa_id: string,
        tipoutente: string,
        tipoutentenome: string,
        uniqueid: string,
        gitVersion: string,
        isPracticeSecretaryAndOther: boolean,
        groups: any,
        people: any,
        configs: any
    ) {
        this.account = account;
        this.Email = Email;
        this.agyo = agyo;
        this.agyo_access_token = agyo_access_token;
        this.agyo_id = agyo_id;
        this.agyo_in = agyo_in;
        this.agyo_last_sync = agyo_last_sync;
        this.agyo_office = agyo_office;
        this.agyo_out = agyo_out;
        this.attivo = attivo;
        this.campi_agenda = campi_agenda;
        this.cct = cct;
        this.cctActive = cctActive;
        this.cct_password = cct_password;
        this.cct_username = cct_username;
        this.codiceavvocato = codiceavvocato;
        this.default_calendar = default_calendar;
        this.default_page_size = default_page_size;
        this.emailNotifications = emailNotifications;
        this.enable_timesheet = enable_timesheet;
        this.external_can_upload = external_can_upload;
        this.feNewYear = feNewYear;
        this.hideChromeAlert = hideChromeAlert;
        this.id = id;
        this.identificatore_codice = identificatore_codice;
        this.isAccounting = isAccounting;
        this.isConsultant = isConsultant;
        this.isExternal = isExternal;
        this.isLawyer = isLawyer;
        this.isLawyerUser = isLawyerUser;
        this.isNetlexcassazioneUser = isNetlexcassazioneUser;
        this.isNetlexeasynotaUser = isNetlexeasynotaUser;
        this.isNetlexfeUser = isNetlexfeUser;
        this.isNetlexpdaUser = isNetlexpdaUser;
        this.isNetlexpdabasicUser = isNetlexpdabasicUser;
        this.isNetlexpdafreeUser = isNetlexpdafreeUser;
        this.isNetlexpdaulofUser = isNetlexpdaulofUser;
        this.isSuperAdmin = isSuperAdmin;
        this.legacy_mode = legacy_mode;
        this.nocturnal_synch_status = nocturnal_synch_status;
        this.nome = nome;
        this.nome_avvocato = nome_avvocato;
        this.nomeutente = nomeutente;
        this.office = office;
        this.one_drive_uniqueid = one_drive_uniqueid;
        this.pda_registration_status = pda_registration_status;
        this.permissions = permissions;
        this.qualificautente = qualificautente;
        this.regime_fiscale_id = regime_fiscale_id;
        this.ruolo_permessi = ruolo_permessi;
        this.terms_of_use_id = terms_of_use_id;
        this.theme = theme;
        this.tipo_cassa_id = tipo_cassa_id;
        this.tipoutente = tipoutente;
        this.tipoutentenome = tipoutentenome;
        this.uniqueid = uniqueid;
        this.gitVersion = gitVersion;
        this.isPracticeSecretaryAndOther = isPracticeSecretaryAndOther;
        this.groups = groups;
        this.people = people;
        this.configs = configs;
    }
    public getAccount(): any {
        return this.account;
    }
    public setAccount(_account: any): any {
        return this.account;
    }

    public getEmail(): string {
        return this.Email;
    }

    public setEmail(_Email: string) {
        return this.Email;
    }

    public getAgyo(): string {
        return this.agyo;
    }

    public setAgyo(_agyo: string) {
        return this.agyo;
    }

    public getAgyoAccessToken(): string {
        return this.agyo_access_token;
    }

    public setAgyoAccessToken(_agyo_access_token: string) {
        return this.agyo_access_token;
    }

    public getAgyoId(): string {
        return this.agyo_id;
    }

    public setAgyoId(_agyo_id: string) {
        return this.agyo_id;
    }

    public getAgyoIn(): string {
        return this.agyo_in;
    }

    public setAgyoIn(_agyo_in: string) {
        return this.agyo_in;
    }

    public getAgyoLastSync(): string {
        return this.agyo_last_sync;
    }

    public setAgyoLastSync(_agyo_last_sync: string) {
        return this.agyo_last_sync;
    }

    public getAgyoOffice(): string {
        return this.agyo_office;
    }

    public setAgyoOffice(_agyo_office: string) {
        return this.agyo_office;
    }

    public getAgyoOut(): string {
        return this.agyo_out;
    }

    public setAgyoOut(_agyo_out: string) {
        return this.agyo_out;
    }

    public getAttivo(): string {
        return this.attivo;
    }

    public setAttivo(_tivoid: string) {
        return this.attivo;
    }

    public getCampiAgenda(): string {
        return this.campi_agenda;
    }

    public setCampiAgenda(_campi_agenda: string) {
        return this.campi_agenda;
    }

    public getCct(): string {
        return this.cct;
    }

    public setCct(_cct: string) {
        return this.cct;
    }

    public getCctActive(): string {
        return this.cctActive;
    }

    public setCctActive(_cctActive: string) {
        return this.cctActive;
    }

    public getCctPassword(): string {
        return this.cct_password;
    }

    public setCctPassword(_cct_password: string) {
        return this.cct_password;
    }

    public getCctUsername(): string {
        return this.cct_username;
    }

    public setCctUsername(_cct_username: string) {
        return this.cct_username;
    }

    public getCodiceavvocato(): string {
        return this.codiceavvocato;
    }

    public setCodiceavvocato(_codiceavvocato: string) {
        return this.codiceavvocato;
    }

    public getDefaultCalendar(): string {
        return this.default_calendar;
    }

    public setDefaultCalendar(_default_calendar: string) {
        return this.default_calendar;
    }

    public getDefaultPageSize(): string {
        return this.default_page_size;
    }

    public setDefaultPageSize(_default_page_size: string) {
        return this.default_page_size;
    }

    public getEmailNotifications(): string {
        return this.emailNotifications;
    }

    public setEmailNotifications(_emailNotifications: string) {
        return this.emailNotifications;
    }

    public getEnableTimesheet(): string {
        return this.enable_timesheet;
    }

    public setEnableTimesheet(_enable_timesheet: string) {
        return this.enable_timesheet;
    }

    public getExternalCanUpload(): string {
        return this.external_can_upload;
    }

    public setExternalCanUpload(_external_can_upload: string) {
        return this.external_can_upload;
    }

    public getFeNewYear(): string {
        return this.feNewYear;
    }

    public setFeNewYear(_feNewYear: string) {
        return this.feNewYear;
    }

    public getHideChromeAlert(): string {
        return this.hideChromeAlert;
    }

    public setHideChromeAlert(_hideChromeAlert: string) {
        return this.hideChromeAlert;
    }

    public getId(): string {
        return this.id;
    }

    public setId(_id: string) {
        return this.id;
    }

    public getIdentificatoreCodice(): string {
        return this.identificatore_codice;
    }

    public setIdentificatoreCodice(_identificatore_codice: string) {
        return this.identificatore_codice;
    }

    public getIsAccounting(): string {
        return this.isAccounting;
    }

    public setIsAccounting(_isAccounting: string) {
        return this.isAccounting;
    }

    public getIsConsultant(): string {
        return this.isConsultant;
    }

    public setIsConsultant(_isConsultant: string) {
        return this.isConsultant;
    }

    public getIsExternal(): string {
        return this.isExternal;
    }

    public setIsExternal(_isExternal: string) {
        return this.isExternal;
    }

    public getIsLawyer(): string {
        return this.isLawyer;
    }

    public setIsLawyer(_isLawyer: string) {
        return this.isLawyer;
    }

    public getIsNetlexcassazioneUser(): string {
        return this.isNetlexcassazioneUser;
    }

    public setIsNetlexcassazioneUser(_isNetlexcassazioneUser: string) {
        return this.isNetlexcassazioneUser;
    }

    public getIsNetlexeasynotaUser(): string {
        return this.isNetlexeasynotaUser;
    }

    public setIsNetlexeasynotaUser(_isNetlexeasynotaUser: string) {
        return this.isNetlexeasynotaUser;
    }

    public getIsNetlexfeUser(): string {
        return this.isNetlexfeUser;
    }

    public setIsNetlexfeUser(_isNetlexfeUser: string) {
        return this.isNetlexfeUser;
    }

    public getIsNetlexpdaUser(): string {
        return this.isNetlexpdaUser;
    }

    public setIsNetlexpdaUser(_isNetlexpdaUser: string) {
        return this.isNetlexpdaUser;
    }

    public getIsNetlexpdabasicUser(): string {
        return this.isNetlexpdabasicUser;
    }

    public setIsNetlexpdabasicUser(_isNetlexpdabasicUser: string) {
        return this.isNetlexpdabasicUser;
    }

    public getIsNetlexpdafreeUser(): string {
        return this.isNetlexpdafreeUser;
    }

    public setIsNetlexpdafreeUser(_isNetlexpdafreeUser: string) {
        return this.isNetlexpdafreeUser;
    }

    public getIsNetlexpdaulofUser(): string {
        return this.isNetlexpdaulofUser;
    }

    public setIsNetlexpdaulofUser(_isNetlexpdaulofUser: string) {
        return this.isNetlexpdaulofUser;
    }
    public getIsLawyerUser(): boolean {
        return this.isLawyerUser;
    }

    public setisLawyerUser(_isLawyerUser: boolean) {
        return this.isLawyerUser;
    }

    public getIsSuperAdmin(): boolean {
        return this.isSuperAdmin;
    }

    public setIsSuperAdmin(_isSuperAdmin: boolean) {
        return this.isSuperAdmin;
    }

    public getLegacyMode(): string {
        return this.legacy_mode;
    }

    public setLegacyMode(_legacy_mode: string) {
        return this.legacy_mode;
    }

    public getNocturnalSynchStatus(): string {
        return this.nocturnal_synch_status;
    }

    public setNocturnalSynchStatus(_nocturnal_synch_status: string) {
        return this.nocturnal_synch_status;
    }

    public getNome(): string {
        return this.nome;
    }

    public setNome(_nome: string) {
        return this.nome;
    }

    public getNomeAvvocato(): string {
        return this.nome_avvocato;
    }

    public setNomeAvvocato(_nome_avvocato: string) {
        return this.nome_avvocato;
    }

    public getNomeutente(): string {
        return this.nomeutente;
    }

    public setNomeutente(_nomeutente: string) {
        return this.nomeutente;
    }

    public getOffice(): string {
        return this.office;
    }

    public setOffice(_office: string) {
        return this.office;
    }

    public getOne_drive_uniqueid(): string {
        return this.one_drive_uniqueid;
    }

    public setOne_drive_uniqueid(one_drive_uniqueid: string) {
        this.one_drive_uniqueid = one_drive_uniqueid;
        return this.one_drive_uniqueid;
    }

    public getPdaRegistrationStatus(): string {
        return this.pda_registration_status;
    }

    public setPdaRegistrationStatus(_pda_registration_status: string) {
        return this.pda_registration_status;
    }

    public getPermissions(): string {
        return this.permissions;
    }

    public setPermissions(_permissions: string) {
        return this.permissions;
    }

    public getQualificautente(): string {
        return this.qualificautente;
    }

    public setQualificautente(_qualificautente: string) {
        return this.qualificautente;
    }

    public getRegimeFiscaleId(): string {
        return this.regime_fiscale_id;
    }

    public setRegimeFiscaleId(_regime_fiscale_id: string) {
        return this.regime_fiscale_id;
    }

    public getRuoloPermessi(): string {
        return this.ruolo_permessi;
    }

    public setRuoloPermessi(_ruolo_permessi: string) {
        return this.ruolo_permessi;
    }

    public getTermsOfUseId(): string {
        return this.terms_of_use_id;
    }

    public setTermsOfUseId(_terms_of_use_id: string) {
        return this.terms_of_use_id;
    }

    public getTheme(): string {
        return this.theme;
    }

    public setTheme(_theme: string) {
        return this.theme;
    }

    public getTipoCassaId(): string {
        return this.tipo_cassa_id;
    }

    public setTipoCassaId(_tipo_cassa_id: string) {
        return this.tipo_cassa_id;
    }

    public getTipoutente(): string {
        return this.tipoutente;
    }

    public setTipoutente(_tipoutente: string) {
        return this.tipoutente;
    }

    public getTipoutentenome(): string {
        return this.tipoutentenome;
    }

    public setTipoutentenome(_tipoutentenome: string) {
        return this.tipoutentenome;
    }

    public getUniqueid(): string {
        return this.uniqueid;
    }

    public setUniqueid(_uniqueid: string) {
        return this.uniqueid;
    }
    public getGitVersion(): string {
        return this.gitVersion;
    }

    public setGitVersion(_gitVersion: string) {
        return this.gitVersion;
    }
    public getIsPracticeSecretaryAndOther(): boolean {
        return this.isPracticeSecretaryAndOther;
    }

    public setIsPracticeSecretaryAndOther(_isPracticeSecretaryAndOther: boolean) {
        return this.isPracticeSecretaryAndOther;
    }

    public getGroups(): any {
        return this.groups;
    }
    public setGroups(_groups: any): any {
        return this.groups;
    }

    public getPeople(): any {
        return this.people;
    }
    public setPeople(_people: any): any {
        return this.people;
    }

    public getConfigs(): any {
        return this.configs;
    }
    public setConfigs(_configs: any): any {
        return this.configs;
    }
    public static fromJson(data: any): User {
        return new User(
            data?.account,
            data?.Email,
            data?.agyo,
            data?.agyo_access_token,
            data?.agyo_id,
            data?.agyo_in,
            data?.agyo_last_sync,
            data?.agyo_office,
            data?.agyo_out,
            data?.attivo,
            data?.campi_agenda,
            data?.cct,
            data?.cctActive,
            data?.cct_password,
            data?.cct_username,
            data?.codiceavvocato,
            data?.default_calendar,
            data?.default_page_size,
            data?.emailNotifications,
            data?.enable_timesheet,
            data?.external_can_upload,
            data?.feNewYear,
            data?.hideChromeAlert,
            data?.id,
            data?.identificatore_codice,
            data?.isAccounting,
            data?.isConsultant,
            data?.isExternal,
            data?.isLawyer,
            data?.isLawyerUser,
            data?.isNetlexcassazioneUser,
            data?.isNetlexeasynotaUser,
            data?.isNetlexfeUser,
            data?.isNetlexpdaUser,
            data?.isNetlexpdabasicUser,
            data?.isNetlexpdafreeUser,
            data?.isNetlexpdaulofUser,
            data?.isSuperAdmin,
            data?.legacy_mode,
            data?.nocturnal_synch_status,
            data?.nome,
            data?.nome_avvocato,
            data?.nomeutente,
            data?.office,
            data?.one_drive_uniqueid,
            data?.pda_registration_status,
            data?.permissions,
            data?.qualificautente,
            data?.regime_fiscale_id,
            data?.ruolo_permessi,
            data?.terms_of_use_id,
            data?.theme,
            data?.tipo_cassa_id,
            data?.tipoutente,
            data?.tipoutentenome,

            data?.uniqueid,
            data?.gitVersion,
            data?.isPracticeSecretaryAndOther,
            data?.groups,
            data?.people,
            data?.configs
        );
    }
}
