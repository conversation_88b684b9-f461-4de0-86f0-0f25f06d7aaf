export default class Configs {
    private data: {};
  
    constructor(data: {}) {
      this.data = data;
    }
  
    public config(key: string | null): boolean | number | string | {} | null {
      return this.getValue(this.data, key);
    }
  
    public getValue(data: any | null, key: string | null): boolean | number | string | {} | null {
      if (!data) {
        return null;
      }
  
      if (key === null) {
        return data;
      }
  
      if (data.hasOwnProperty(key)) {
        return data[key];
      }
  
      if (!key.includes('.')) {
        return data[key] ?? null;
      }
  
      key.split('.').every((item) => {
        if (!(data && data.hasOwnProperty(item))) {
          data = null;
          return false;
        }
        data = data[item];
        return true;
      });
  
      return data;
    }
  }