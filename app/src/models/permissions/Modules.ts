import User from "./User";
import { CrudItems, CrudOperations } from "../../enums/Permissions";
import { ArchiveItems } from "../../enums/ArchiveItems";

interface CheckCrudPermissionsItem {
    crudItem: CrudItems;
    crudOperations: Array<CrudOperations>;
}

export default class Modules {
    private isAccounting: boolean;
    private isConsultant: boolean;
    private isExternal: boolean;
    private isLawyer: boolean;
    private isNetlexcassazioneUser: boolean;
    private isNetlexCloudUser: boolean;
    private isNetlexCloudPdaUser: boolean;
    private isNetlexCloudPdaPromoUser: boolean;
    private isNetlexCloudPromoUser: boolean;
    private isDiskSpaceFull: boolean;
    private isGiapppichelliInstance: boolean;
    private isNetlexBasicUser: boolean;

    private isNetlexeasynotaUser: boolean;
    private isNetlexfeUser: boolean;
    private isNetlexpdaUser: boolean;
    private isNetlexpdabasicUser: boolean;
    private isNetlexpdafreeUser: boolean;
    private isNetlexpdaulofUser: boolean;
    private isSuperAdmin: boolean;
    private legacyMode: string;
    private permissions: any[] | null;
    private campi_agenda: string;
    private tipoUtente: string;
    private show: string[];
    private isAdmin: number;
    private provisioningRow: any | null;
    private showSections: any | null;
    private netlexSettings: any | null;
    private gitVersion: string | null;
    private lastLoginDate: string | null;
    private timertaskdata: any;
    private activetimesheet: string;

    constructor(isAccounting: boolean, isConsultant: boolean, isExternal: boolean, isLawyer: boolean, isNetlexBasicUser: boolean, isNetlexcassazioneUser: boolean, isNetlexCloudUser: boolean, isNetlexCloudPdaUser: boolean, isNetlexCloudPdaPromoUser: boolean, isNetlexCloudPromoUser: boolean, isNetlexeasynotaUser: boolean, isNetlexfeUser: boolean, isNetlexpdaUser: boolean, isNetlexpdabasicUser: boolean, isNetlexpdafreeUser: boolean, isNetlexpdaulofUser: boolean, isSuperAdmin: boolean, isDiskSpaceFull: boolean, isGiapppichelliInstance: boolean, legacyMode: string, permissions: any[] | null, campi_agenda: string, tipoUtente: string, show: string[], isAdmin: number, provisioningRow: any | null, showSections: any | null, netlexSettings: any | null, gitVersion: string | null, lastLoginDate: string | null, timertaskdata: any, activetimesheet: string) {
        this.isAdmin = isAdmin;
        this.isAccounting = isAccounting;
        this.isConsultant = isConsultant;
        this.isExternal = isExternal;
        this.isLawyer = isLawyer;
        this.isNetlexBasicUser = isNetlexBasicUser;
        this.isNetlexcassazioneUser = isNetlexcassazioneUser;
        this.isNetlexCloudUser = isNetlexCloudUser;
        this.isNetlexCloudPdaUser = isNetlexCloudPdaUser;
        this.isNetlexCloudPdaPromoUser = isNetlexCloudPdaPromoUser;
        this.isNetlexCloudPromoUser = isNetlexCloudPromoUser;

        this.isNetlexeasynotaUser = isNetlexeasynotaUser;
        this.isNetlexfeUser = isNetlexfeUser;
        this.isNetlexpdaUser = isNetlexpdaUser;
        this.isNetlexpdabasicUser = isNetlexpdabasicUser;
        this.isNetlexpdafreeUser = isNetlexpdafreeUser;
        this.isNetlexpdaulofUser = isNetlexpdaulofUser;
        this.isSuperAdmin = isSuperAdmin;
        this.isDiskSpaceFull = isDiskSpaceFull;
        this.isGiapppichelliInstance = isGiapppichelliInstance;
        this.legacyMode = legacyMode;
        this.permissions = permissions;
        this.campi_agenda = campi_agenda;
        this.tipoUtente = tipoUtente;
        this.show = show;
        this.provisioningRow = provisioningRow;
        this.showSections = showSections;
        this.netlexSettings = netlexSettings;
        this.gitVersion = gitVersion;
        this.lastLoginDate = lastLoginDate;
        this.timertaskdata = timertaskdata;
        this.activetimesheet = activetimesheet;
    }
    public getIsNetlexCloudUserGet(): boolean {
        return this.isNetlexCloudUser;
    }

    public getIsNetlexCloudPdaUserGet(): boolean {
        return this.isNetlexCloudPdaUser;
    }

    public getIsNetlexCloudPdaPromoUserGet(): boolean {
        return this.isNetlexCloudPdaPromoUser;
    }

    public getIsNetlexCloudPromoUserGet(): boolean {
        return this.isNetlexCloudPromoUser;
    }

    public getIsDiskSpaceFullGet(): boolean {
        return this.isDiskSpaceFull;
    }

    public getIsGiapppichelliInstanceGet(): boolean {
        return this.isGiapppichelliInstance;
    }

    public getIsNetlexBasicUserGet(): boolean {
        return this.isNetlexBasicUser;
    }

    public getMenuModules(): boolean {
        return !this.isNetlexpdaUser && !this.isNetlexfeUser && !this.isNetlexpdabasicUser && !this.isNetlexpdafreeUser && !this.isNetlexpdaulofUser && !this.isNetlexeasynotaUser && !this.isNetlexcassazioneUser;
    }

    public hasPermissions(): boolean {
        if (!this.permissions) {
            return false;
        }
        return true;
    }

    public canAccessAnagrafiche(): boolean {
        return (
            !this.getIsConsultant() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessPdaCloudPct(_user: User): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PDA,
                    crudOperations: [CrudOperations.READ]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.POLISWEB,
                    crudOperations: [CrudOperations.READ]
                },
                {
                    crudItem: CrudItems.PCT,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessAlphanumericNetlexSettings(user: User): boolean {
        return !user.getIsNetlexpdaUser() && !user.getIsNetlexpdabasicUser() && !user.getIsNetlexpdafreeUser() && !user.getIsNetlexeasynotaUser();
    }

    public canAccessSeenMailboxNetlexSettings(user: User): boolean {
        return !user.getIsNetlexpdaUser() && !user.getIsNetlexpdabasicUser() && !user.getIsNetlexpdafreeUser();
    }

    public canAccessHeaderArchive(user: User): boolean {
        return this.canAccessSeenMailboxNetlexSettings(user);
    }

    public canAccessOneDriveSettings(user: User): boolean {
        return this.canAccessSeenMailboxNetlexSettings(user);
    }

    public canAccessSmartMailerArchive(): boolean {
        return this.getFieldFromProvisioningRow("smart_mailer");
    }

    public canAccessAccountVoiSpeed(user: User): boolean {
        return this.canAccessSeenMailboxNetlexSettings(user);
    }

    public canAccessSharepointNetlexSettings(_user: User): boolean {
        return this.getIsSuperAdmin();
    }

    public canAccessTs(user: User): boolean {
        return !user.getIsConsultant() && !user.getIsExternal();
    }

    public getFeGeneralCondition(user: User): boolean {
        return (
            !user.getIsConsultant() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessSupportoTopbar(user: User): boolean {
        return (
            (!user.getIsExternal && !user.getIsConsultant && !this.hasPermissions()) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.SUPPORTO__APERTURA_TICKET,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }
    public canAccessGestioneAbbonamento(user: User): boolean {
        return (
            (!user.getIsExternal && !user.getIsConsultant && !this.hasPermissions()) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.GESTIONE_ABBONAMENTO,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public getPdaCloudDashboard(user: User): boolean {
        return (
            !user.getIsConsultant() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.DASHBOARD,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessPosteItaliane(user: User): boolean {
        return (
            !user.getIsConsultant() &&
            this.getFieldFromProvisioningRow("poste_italiane") === 1 &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessGroups(_user: User): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.STUDIO__GRUPPI_UTENTI,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public getPdaCloudAnagrafiche(): boolean {
        return (
            !this.getIsConsultant() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public getDatiLiquadazione = (): boolean => {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DATI_LIQUIDAZIONE,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    };
    public getOthersPratiche(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") !== "1";
    }

    public getLiquidationPa(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") === "1";
    }

    public checkArchiveItem(item: ArchiveItems): boolean {
        return this.getArchiveItemsShow().includes(item);
    }

    public getArchiveItemsShow(): any | null {
        return this.getNetlexSettingsByField("archive_items_show");
    }

    public canAccessOthersFeesItems(): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.FATTURAZIONE__VOCI_FATTURAZIONE,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccessOthersRecuperoCredit(): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.STUDIO__GRUPPI_UTENTI,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccessOtherMailbox(): boolean {
        return (
            this.getIsAdmin() !== 2 &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.MAILBOX,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public isNotExternal(): boolean {
        return !this.isExternal;
    }

    public canAccessCassazioneAnagrafiche(): boolean {
        return (
            !this.getNetlexSettingsByField("hide") ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public isDashboardPdaCloud(): boolean {
        return !this.isExternal && !this.isConsultant && (!this.permissions || (this.permissions && this.permissions[1]["r"]));
    }

    public canAccessLVentureAdmin0(): boolean {
        return this.getFieldFromProvisioningRow("payment_type") || this.getIsAdmin() === 0;
    }

    public canAccessPdaCloudPoliswebpoliswebrgexclusion(): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.POLISWEB__FILTRI_PER_LISTA_RG,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccessLVentureAdmin1(): boolean {
        return this.getFieldFromProvisioningRow("pda_number_of_users") > 0 && this.getIsAdmin() === 1;
    }

    public canAccessPdaCloudPoliswebrgFilters(): boolean {
        return this.canAccessPdaCloudPoliswebpoliswebrgexclusion();
    }

    public getIsSollectiSospesi() {
        return this.provisioningRow && this.provisioningRow["solleciti_sospesi"];
    }

    public getFieldFromProvisioningRow(field: string) {
        if (!this.provisioningRow) {
            return "";
        }
        return this.provisioningRow[field];
    }

    public canAccessFondoSoccombenza(): boolean {
        return (
            this.isSuperAdmin ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.DASHBOARD__FONDO_SOCCOMBENZA,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessFondoDashboard(user: User): boolean {
        return (
            user.getIsSuperAdmin() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.DASHBOARD__FONDO_SOCCOMBENZA,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessNetlexSettingsItem(): boolean {
        return this.getIsSuperAdmin();
    }

    public getOthersDocuments2(user: User): boolean {
        return (
            !user.getIsConsultant() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__POLISWEB,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public getMenuItemList(): number[] {
        return this.show?.map((item: string) => parseInt(item));
    }

    public getIsAccounting(): boolean {
        return this.isAccounting;
    }

    public getIsConsultant(): boolean {
        return this.isConsultant;
    }

    public getIsExternal(): boolean {
        return this.isExternal;
    }

    public getIsLawyer(): boolean {
        return this.isLawyer;
    }

    public getIsNetlexBasicUser(): boolean {
        return this.isNetlexcassazioneUser;
    }

    public getIsNetlexcassazioneUser(): boolean {
        return this.isNetlexcassazioneUser;
    }

    public getIsNetlexCloudUser(): boolean {
        return this.isNetlexeasynotaUser;
    }
    public getIsNetlexCloudPdaUser(): boolean {
        return this.isNetlexeasynotaUser;
    }
    public getIsNetlexCloudPdaPromoUser(): boolean {
        return this.isNetlexeasynotaUser;
    }
    public getIsNetlexCloudPromoUser(): boolean {
        return this.isNetlexeasynotaUser;
    }
    public getIsNetlexeasynotaUser(): boolean {
        return this.isNetlexeasynotaUser;
    }

    public getIsNetlexfeUser(): boolean {
        return this.isNetlexfeUser;
    }

    public getIsNetlexpdaUser(): boolean {
        return this.isNetlexpdaUser;
    }

    public getIsNetlexpdabasicUser(): boolean {
        return this.isNetlexpdabasicUser;
    }

    public getIsNetlexpdafreeUser(): boolean {
        return this.isNetlexpdafreeUser;
    }

    public getIsNetlexpdaulofUser(): boolean {
        return this.isNetlexpdaulofUser;
    }

    public getIsSuperAdmin(): boolean {
        return this.isSuperAdmin;
    }

    public getLegacyMode(): string {
        return this.legacyMode;
    }

    private getPermissions(index: number, key: string): boolean {
        if (!this.permissions) {
            return false;
        }

        if (this.permissions[index]) {
            return this.permissions[index][key];
        }

        return this.permissions[index];
    }

    public getLoggedUserCampiAgenda(): any | null {
        return this.campi_agenda;
    }

    public getLoggedUserCampiAgendaField(key: string): any | null {
        if (!this.campi_agenda) {
            return null;
        } else {
            let campiAgenda = JSON.parse(this.campi_agenda);
            return campiAgenda[key];
        }
    }

    public getTipoUtente(): string {
        return this.tipoUtente;
    }

    public getIsAdmin(): number {
        return Number(this.isAdmin);
    }

    public getShowSections(): any | null {
        return this.showSections;
    }

    public getNetlexSettings(): any | null {
        return this.netlexSettings;
    }

    public getNetlexSettingsByField(field: string): any | null {
        if (!this.netlexSettings) {
            return;
        }
        return this.netlexSettings[field];
    }
    public canAccessOfficeSettingsTypology(): boolean {
        return this.getIsNetlexfeUser() || this.getIsNetlexpdaulofUser();
    }

    /**
     * Checks whether or not the logged user has all the required CRUD permissions
     *  for all the required CRUD items.
     * If any of these checks fails, it will return `false`.
     *
     * @param items array of `CheckCrudPermissionsItem`
     * @returns boolean
     */
    public checkCrudPermissions = (items: Array<CheckCrudPermissionsItem>): boolean => {
        // Check if the user has permissions
        if (this.hasPermissions()) {
            for (const item of items) {
                for (const crudOp of item.crudOperations) {
                    if (!this.getPermissions(item.crudItem, crudOp)) {
                        return false;
                    }
                }
            }
        }
        return true;
    };

    public canAccessExcludedIssuesList(): boolean {
        return !this.checkCrudPermissions([
            {
                crudItem: CrudItems.USA_WHITELIST_PER_SINCRONIZZAZIONE,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccessRGlistFilter(): boolean {
        return !this.checkCrudPermissions([
            {
                crudItem: CrudItems.USA_WHITELIST_PER_SINCRONIZZAZIONE,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccessCheckPDA(): boolean {
        return this.checkCrudPermissions([{ crudItem: CrudItems.PDA, crudOperations: [CrudOperations.READ] }]);
    }

    public canAccessRepilogue(user: User): boolean {
        return user.getIsSuperAdmin();
    }

    public canAccessBilling(custom?: boolean): boolean {
        return this.getIsAdmin() !== 2 && custom !== undefined
            ? this.checkCrudPermissions([
                  {
                      crudItem: CrudItems.FATTURAZIONE,
                      crudOperations: [CrudOperations.READ]
                  }
              ])
            : true;
    }

    public canAccessPCT(user: User): boolean {
        return this.canAccessPdaCloudPct(user) && this.isNotExternal();
    }

    public canAccessStudio(): boolean {
        return (
            this.isNotExternal() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.STUDIO,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessUtility(): boolean {
        return (
            this.isNotExternal() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.UTILITA,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessShop(): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.SHOP,
                    crudOperations: [CrudOperations.READ]
                }
            ]) && this.isNotExternal()
        );
    }

    public canAccessNews(): boolean {
        return (
            this.isNotExternal() &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.NEWS,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessDelegation(): number {
        return this.getIsAdmin();
    }

    public canAccessRegistry(): boolean {
        return (
            !this.getNetlexSettingsByField("hide") &&
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessCalendarReferent(commitmentsSearch: string): boolean {
        return this.getFieldFromProvisioningRow("impegni_multiutente") === "1" && commitmentsSearch !== "deadlinesOnly";
    }

    public canAccessCertificates(): number {
        return this.getIsAdmin();
    }

    public canAccessOfficesettings(): boolean {
        return this.getIsSuperAdmin();
    }

    public canAccessStudioCustomization(): number {
        return this.getIsAdmin();
    }

    public canAccessStudioGroups(): number {
        return this.getIsAdmin();
    }

    public canAccessStudioGeneralSettings(): number {
        return this.getIsAdmin();
    }

    public canAccessAgenda(): boolean {
        return this.getIsAdmin() !== 2;
    }

    public canAccessDocuments(user: User): boolean {
        return this.isNotExternal() && this.getOthersDocuments2(user);
    }

    public canAccessCalendarGestione(user: User): boolean {
        return user.getTipoutentenome() === "superadmin";
    }

    public canAccessDelegations(): boolean {
        return this.getIsAdmin() === 1;
    }

    public canAccessRemoteSign(): boolean {
        return this.getIsAdmin() === 1;
    }

    public canAccessBillingSection(user: User): string {
        return user.getIsConsultant();
    }

    public canAccessAnagrafica(): boolean {
        return this.getPdaCloudAnagrafiche() && this.isNotExternal();
    }

    public canAccessAccounting(): boolean {
        return this.getIsAccounting();
    }

    public canAccessUserAccounting(user: User): boolean {
        return !user.getIsAccounting();
    }

    public canAccessPctSection(user: User): boolean {
        return this.canAccessPdaCloudPct(user) && this.isNotExternal();
    }

    public canAccessStudioTable(): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.TABELLE__INSERIMENTO_RAPIDO,
                crudOperations: [CrudOperations.CREATE]
            }
        ]);
    }

    public canAccessStudioLetterhead(): boolean {
        return this.getIsAdmin() === 1;
    }

    public canAccessCustomizationLogo(): boolean {
        return this.getIsAdmin() === 1;
    }

    public canAccessPecNotifications(): boolean {
        return this.getIsAdmin() === 2;
    }

    public canAccessAnagraficaExportConDatiLiquidazione(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") === 1;
    }

    public canAccessArchiveFileId(): string {
        return this.getNetlexSettingsByField("file_id");
    }

    public canAccessSmartMailerNeedUpgrade(): boolean {
        return this.isNetlexpdaulofUser;
    }

    public canAccessAddCertificate(): string {
        return this.getFieldFromProvisioningRow("certificates");
    }

    public canAccessTabelleDescriptions(): boolean {
        return !this.getIsNetlexpdaUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser() && !this.getIsNetlexCloudUser() && !this.getIsNetlexBasicUser();
    }

    public canAccessTabelleTemplate(): boolean {
        return !this.getIsNetlexpdaUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser() && !this.getIsNetlexpdaulofUser() && !this.getIsNetlexeasynotaUser() && !this.getIsNetlexcassazioneUser();
    }

    public canAccessTabelleAdditionalnotes(): boolean {
        return !this.getIsNetlexpdaUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser() && !this.getIsNetlexpdaulofUser() && !this.getIsNetlexCloudUser() && !this.getIsNetlexBasicUser();
    }

    public canAccessTabelleTimesheetStandard(): boolean {
        return this.isNetlexcassazioneUser;
    }

    public canAccessTabelleDeadlinestypes(): boolean {
        return this.isNetlexfeUser;
    }

    public canAccessTabelleAntirecregister(): boolean {
        return !this.getIsNetlexpdaUser() && !this.getIsNetlexfeUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser() && !this.getIsNetlexpdaulofUser() && !this.getIsNetlexCloudUser() && !this.getIsNetlexBasicUser();
    }

    public canAccessTabelleProposalcode(): boolean {
        return this.getFieldFromProvisioningRow("documentale_id") === "2";
    }

    public canAccessRecupeoCrediti(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") === 1;
    }

    public canAccessRecupeoDirtti(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") === 1;
    }

    public canAccessRecuperoCreditiArchive(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") != 1;
    }

    public canAccessUserAltroFormTipo(): boolean {
        return this.getIsNetlexfeUser() || this.getIsNetlexpdaulofUser();
    }

    public canAccessUserGroup(): boolean {
        return !this.getIsNetlexpdaUser() && !this.getIsNetlexfeUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser();
    }

    public canAccessUserExternalcode(): boolean {
        return this.getFieldFromProvisioningRow("whitelist") !== undefined && this.getFieldFromProvisioningRow("whitelist") !== null && this.getFieldFromProvisioningRow("whitelist").indexOf(11) !== -1;
    }

    public canAccessUserAttivo(): boolean {
        return !this.getIsNetlexpdaUser() || !this.getIsNetlexpdabasicUser() || !this.getIsNetlexpdafreeUser();
    }

    public canAccessUserEnableTimesheet(): boolean {
        return this.getFieldFromProvisioningRow("whitelist") !== undefined && this.getFieldFromProvisioningRow("whitelist") !== null && this.getFieldFromProvisioningRow("whitelist").indexOf(3) !== -1 && this.getIsAdmin() === 1;
    }

    public canAccessDatiLiquidazionePratica(): boolean {
        return this.getIsAdmin() != 2 && !this.getIsNetlexpdaUser() && !this.getIsNetlexpdabasicUser() && !this.getIsNetlexpdafreeUser() && !this.getIsNetlexcassazioneUser() && !this.getIsNetlexfeUser() && this.getDatiLiquadazione() && this.getNetlexSettingsByField("liquidation_pa") != "1";
    }

    public canAccessUserIsAssigneePa(): boolean {
        return this.getNetlexSettingsByField("liquidation_pa") === 1;
    }

    public canAccessUserNotifyNewProceed(): boolean {
        return this.getFieldFromProvisioningRow("whitelist") !== undefined && this.getFieldFromProvisioningRow("whitelist") !== null && this.getFieldFromProvisioningRow("whitelist").indexOf(9) !== -1;
    }

    public canAccessUserQualifica(): boolean {
        return !this.getIsNetlexfeUser();
    }

    public canAccessUserIdentificatore(): boolean {
        return !this.getIsNetlexpdaUser() || !this.getIsNetlexpdabasicUser() || !this.getIsNetlexpdafreeUser();
    }

    public canAccessSummarySoci(vede_soci: string): boolean {
        return vede_soci === "1";
    }

    public canAccessOpenText(): boolean {
        return this.getFieldFromProvisioningRow("opentext") && !this.isExternal;
    }

    public canAccesFileManagerDelete(): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DOCUMENTI,
                    crudOperations: [CrudOperations.DELETE]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.UTILITA__DOCUMENTI,
                    crudOperations: [CrudOperations.DELETE]
                }
            ])
        );
    }

    public canAccesPractice(): boolean {
        return this.checkCrudPermissions([
            {
                crudItem: CrudItems.PRATICHE__DOCUMENTI,
                crudOperations: [CrudOperations.READ]
            }
        ]);
    }

    public canAccesPracticeButtonTimesheet(): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccesFileManagerSignature(): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DOCUMENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.UTILITA__DOCUMENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ])
        );
    }

    public canAccesFileManagerReplace(): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DOCUMENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.UTILITA__DOCUMENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ])
        );
    }

    public canAccessOnedriveVersionPanel(): boolean {
        return (
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DOCUMENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.AGENDA_AGENDA,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ])
        );
    }

    public getGitVersion(): string | null {
        return this.gitVersion;
    }
    public getLastLoginDate(): string | null {
        return this.lastLoginDate;
    }

    public getTimertaskdata(): any {
        return this.timertaskdata;
    }

    public getActivetimesheet(): string {
        return this.activetimesheet;
    }

    public canAccessGestioneCreditoTopbar(user: User): boolean {
        return !user.getIsNetlexpdaUser() && (!Array.isArray(user.getAccount()) || !user.getAccount().is_demo) && !user.getIsNetlexfeUser() && !user.getIsNetlexpdabasicUser() && !user.getIsNetlexpdafreeUser() && !user.getIsNetlexpdaulofUser() && !user.getIsNetlexeasynotaUser();
    }

    public canModifyAnagrafiche(_user: User): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.CLIENTI,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ])
        );
    }

    public canAccessDeleteAgendaUdienza(): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.AGENDA_AGENDA,
                    crudOperations: [CrudOperations.DELETE]
                }
            ]) ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__AGENDA,
                    crudOperations: [CrudOperations.DELETE]
                }
            ])
        );
    }

    public canAccessGoToPractichaAgendaUdienza(): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }

    public canAccessUpdateAgendaUdienza(): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__AGENDA,
                    crudOperations: [CrudOperations.UPDATE]
                }
            ])
        );
    }

    public canAccessDivisioneDegliUtili(): boolean {
        return (
            !this.hasPermissions() ||
            this.checkCrudPermissions([
                {
                    crudItem: CrudItems.PRATICHE__DIVISIONE_DEGLI_UTILI,
                    crudOperations: [CrudOperations.READ]
                }
            ])
        );
    }
    public static fromJson(data: any): Modules {
        return new Modules(data?.isAccounting, data?.isConsultant, data?.isExternal, data?.isLawyer, data?.isNetlexBasicUser, data?.isNetlexcassazioneUser, data?.isNetlexCloudUser, data?.isNetlexCloudPdaUser, data?.isNetlexCloudPdaPromoUser, data?.isNetlexCloudPromoUser, data?.isNetlexeasynotaUser, data?.isNetlexfeUser, data?.isNetlexpdaUser, data?.isNetlexpdabasicUser, data?.isNetlexpdafreeUser, data?.isNetlexpdaulofUser, data?.isSuperAdmin, data?.isDiskSpaceFull, data?.isGiapppichelliInstance, data?.legacy_mode, data?.permissions, data?.campi_agenda, data?.tipoutente, data?.show, data?.isAdmin, data?.provisioningRow, data?.showSections, data?.netlexSettings, data?.gitVersion, data.lastLoginDate, data?.timertaskdata, data?.activetimesheet);
    }
}
