import Authorita from './Authorita';

export default class ListResult {
  private currentPage: Authorita[];
  private totalRows: number;

  constructor(currentPage: Authorita[] = [], totalRows: number = 0) {
    this.currentPage = currentPage;
    this.totalRows = totalRows;
  }

  public getCurrentPage(): Authorita[] {
    return this.currentPage;
  }

  public setCurrentPage(currentPage: Authorita[]): void {
    this.currentPage = currentPage;
  }

  public getTotalRows(): number {
    return this.totalRows;
  }

  public setTotalRows(totalRows: number): void {
    this.totalRows = totalRows;
  }

  public static fromJson(data: any): ListResult {
    return new ListResult(
      data.currentPage.map((authorita: any) => {
        return Authorita.fromJson(authorita);
      }),

      data.totalRows
    );
  }
}
