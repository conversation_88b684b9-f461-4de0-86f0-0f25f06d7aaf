export default class Authorita {
  private id?: string;
  private pwid?: string;
  private citta?: string;
  private uniqueid?: string;
  private preferita?: string;
  private nome?: string;

  constructor(
    id?: string,
    pwid?: string,
    citta?: string,
    uniqueid?: string,
    preferita?: string,
    nome?: string
  ) {
    this.id = id;
    this.pwid = pwid;
    this.citta = citta;
    this.uniqueid = uniqueid;
    this.preferita = preferita;
    this.nome = nome;
  }

  public getId(): string | undefined {
    return this.id;
  }

  public setId(id: string): void {
    this.id = id;
  }

  public getPwid(): string | undefined {
    return this.pwid;
  }

  public setPwid(pwid: string): void {
    this.pwid = pwid;
  }

  public getCitta(): string | undefined {
    return this.citta;
  }

  public setCitta(citta: string): void {
    this.citta = citta;
  }

  public getUniqueid(): string | undefined {
    return this.uniqueid;
  }

  public setUniqueid(uniqueid: string): void {
    this.uniqueid = uniqueid;
  }

  public getPreferita(): string | undefined {
    return this.preferita;
  }

  public setPreferita(preferita: string): void {
    this.preferita = preferita;
  }

  public getNome(): string | undefined {
    return this.nome;
  }

  public setNome(nome?: string): void {
    this.nome = nome;
  }

  public static fromJson(data: any): Authorita {
    return new Authorita(data.id, data.pwid, data.citta, data.uniqueid, data.preferita, data.nome);
  }
}
