export default class GridsSettings {
  private column_keys: string
  private column_names: string
  private cell_templates: string
  private column_widths: string
  private header_templates: string
  private sortable: string

  constructor(
    column_keys: string,
    column_names: string,
    column_widths: string,
    cell_templates: string,
    header_templates: string,
    sortable: string
  ) {
    this.column_keys = column_keys
    this.column_names = column_names
    this.column_widths = column_widths
    this.cell_templates = cell_templates
    this.header_templates = header_templates
    this.sortable = sortable
  }

  public getColumn_keys(): string {
    return this.column_keys
  }

  public setColumn_keys(column_keys: string): void {
    this.column_keys = column_keys
  }

  public getColumn_names(): string {
    return this.column_names
  }

  public setColumn_names(column_names: string): void {
    this.column_names = column_names
  }

  public getColumn_widths(): string {
    return this.column_widths
  }

  public setColumn_widths(column_widths: string): void {
    this.column_widths = column_widths
  }

  public getCell_templates(): string {
    return this.cell_templates
  }

  public setCell_templates(cell_templates: string): void {
    this.cell_templates = cell_templates
  }

  public getHeader_templates(): string {
    return this.header_templates
  }

  public setHeader_templates(header_templates: string): void {
    this.header_templates = header_templates
  }

  public getSortable(): string {
    return this.sortable
  }

  public setSortable(sortable: string): void {
    this.sortable = sortable
  }

  public static fromJson(data: any): GridsSettings {
    return new GridsSettings(
      data.column_keys,
      data.column_names,
      data.column_widths,
      data.cell_templates,
      data.header_templates,
      data.sortable
    )
  }
}
