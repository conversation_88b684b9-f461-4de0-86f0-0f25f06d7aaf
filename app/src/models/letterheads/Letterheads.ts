export default class Letterheads {
    private align: string;
    private description: string;
    private filename: File[] | string;
    private full_width: string | null;
    private replace_studio_data: string;
    private uniqueid: string;
    private use_as_logo: string;
    // private users: string | null;
  
    constructor(
      align: string,
      description: string,
      filename: File[] | string,
      full_width: string | null,
      replace_studio_data: string,
      use_as_logo: string,
      uniqueid: string,
    ) {
      this.align = align;
      this.description = description;
      this.filename = filename;
      this.full_width = full_width;
      this.replace_studio_data = replace_studio_data;
      this.use_as_logo = use_as_logo;
      this.uniqueid = uniqueid;
    }
  
    public getAlign(): string {
      return this.align;
    }
  
    public setAlign(align: string): void {
      this.align = align;
    }
  
    public getDescription(): string {
      return this.description;
    }
  
    public setDescription(description: string): void {
      this.description = description;
    }
  
    public getFilename(): File[] | string {
      return this.filename;
    }
  
    public setFilename(filename: File[] | string): void {
      this.filename = filename;
    }
  
    public getFullwidth(): string | null {
      return this.full_width;
    }
  
    public setFullwidth(full_width: string | null): void {
      this.full_width = full_width;
    }
  
    public getReplaceStudioData(): string {
      return this.replace_studio_data;
    }
  
    public setReplaceStudioData(replace_studio_data: string): void {
      this.replace_studio_data = replace_studio_data;
    }
  
    public getUniqueid(): string {
      return this.uniqueid;
    }
  
    public setUniqueid(uniqueid: string): void {
      this.uniqueid = uniqueid;
    }
  
    public getUseAsLogo() {
      return this.use_as_logo;
    }
  
    public setUseAsLogo(use_as_logo: string): void {
      this.use_as_logo = use_as_logo;
    }
  
    // public getUsers(): string | null {
    //   return this.users;
    // }
  
    // public setUsers(users: string | null): void {
    //   this.users = users;
    // }
  
    public static fromJson(data: any): Letterheads {
      return new Letterheads(
        data.align,
        data.description,
        data.filename,
        data.full_width,
        data.replace_studio_data,
        data.use_as_logo,
        data.uniqueid,
        // data.users
      );
    }
  }
  