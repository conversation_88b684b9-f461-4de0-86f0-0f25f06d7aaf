import GridsSettings from "./GridsSettings";

export default class BaseGridList {
  private gridsSettings: GridsSettings;

  constructor(gridsSettings: GridsSettings) {
    this.gridsSettings = gridsSettings;
  }

  public getGridsSettings(): GridsSettings {
    return this.gridsSettings;
  }

  public setGridsSettings(gridsSettings: GridsSettings): void {
    this.gridsSettings = gridsSettings;
  }

  public static fromJson(data: any): BaseGridList {
    return new BaseGridList(GridsSettings.fromJson(data.gridsSettings));
  }
}

