const toFixedFix = (n: any, precisione: any) => {
  var k = Math.pow(10, precisione);
  return '' + Math.round(n * k) / k;
};

const currencyFormat = (number: any, decimali: any, dec_separatore: any, mig_separatore: any) => {
  number = (number + '').replace(/[^0-9\.\-]?/gi, '');
  var n = 0;
  if (isFinite(+number)) {
    n = number;
  }
  var precisione = 0;
  if (isFinite(+decimali) && decimali > -1) {
    precisione = decimali;
  }
  var separatore = '.';
  if (typeof mig_separatore !== 'undefined') {
    separatore = mig_separatore;
  }
  var dec: any = ',';
  if (typeof dec_separatore !== 'undefined') {
    dec = dec_separatore;
  }
  var s: any = '';
  if (precisione !== 0) {
    s = toFixedFix(n, precisione);
  } else {
    s = '' + Math.round(n);
  }
  s = s.split('.');
  if (s[0].length > 3) {
    s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, separatore);
  }
  if ((s[1] || '').length < precisione) {
    s[1] = s[1] || '';
    s[1] += new Array(precisione - s[1].length + 1).join('0');
  }
  return s.join(dec);
};

export { toFixedFix, currencyFormat };
