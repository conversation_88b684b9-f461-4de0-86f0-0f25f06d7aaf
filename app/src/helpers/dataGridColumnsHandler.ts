import { IGridConfig } from "../services/DataGridConfigService";

export const sortColumnsByConfig = (columns: any[], configFields: any) => {
    const currentColumns = columns;
    const newColumnOrder = [...currentColumns].sort((a, b) => {
        const indexA = configFields.indexOf(a.field);
        const indexB = configFields.indexOf(b.field);

        if (indexA === -1) return 1;
        if (indexB === -1) return -1;

        return indexA - indexB;
    }).map(col => col.field);

    return newColumnOrder;
};

export const getColumnWidthsFromConfig = (config: IGridConfig, currentColumns: any[]) => {
    if (!config?.columns_config || !currentColumns?.length) {
        return [];
    }

    const columnsToUpdate: any[] = [];

    config.columns_config.forEach((configCol: any) => {
        const column = currentColumns.find((col: any) => col.field === configCol.field);
        if (column) {
            const updatedColumn = { ...column };
            let needsUpdate = false;

            if (configCol.flex !== undefined) {
                updatedColumn.flex = configCol.flex;
                needsUpdate = true;
            }

            if (configCol.width !== undefined) {
                if (configCol.field === "__check__") {
                    updatedColumn.width = 30;
                    // Ensure checkbox column is not sortable, hideable, or resizable
                    updatedColumn.sortable = false;
                    updatedColumn.hideable = false;
                    updatedColumn.resizable = false;
                } else if (configCol.field === "Azioni") {
                    updatedColumn.sortable = false;
                    updatedColumn.hideable = false;
                    updatedColumn.resizable = false;
                    updatedColumn.width = 120;
                } else {
                    updatedColumn.width = configCol.width < 100 ? 100 : configCol.width;
                }
                needsUpdate = true;
            }

            if (configCol.hide !== undefined) {
                updatedColumn.hide = configCol.hide;
                needsUpdate = true;
            }

            if (needsUpdate) {
                columnsToUpdate.push(updatedColumn);
            }
        }
    });

    return columnsToUpdate;
};

export const getVisibilityModelFromConfig = (config: IGridConfig) => {
    const visibilityModel = config.columns_config
        .reduce((acc: any, col: any) => {
            acc[col.field] = !col.hide;
            return acc;
        }, {});

    return visibilityModel;
};

