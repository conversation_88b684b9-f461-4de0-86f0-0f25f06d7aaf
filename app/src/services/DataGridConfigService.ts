import { useRef, useState } from 'react';
import useGetCustom from '../hooks/useGetCustom';
import usePostCustom from '../hooks/usePostCustom';

export interface IColumnConfig {
    flex?: number;
    hide: boolean;
    field: string;
    width: string;
    hideable: boolean;
    sortable: boolean;
    headerName: string;
}

export interface IGridConfig {
    columns_config: IColumnConfig[];
    name: string;
    id?: number | string;
}

interface ApiResponse<T> {
    data: T;
    status: number;
}

export const useDataGridConfig = () => {

    const [isCreateMode, setIsCreateMode] = useState(false);
    const saveConfigRequest = usePostCustom('datagridpro/save?noTemplateVars=true');
    const getCustomListRequest = useGetCustom('datagridpro/list?noTemplateVars=true');
    const getDefaultListRequest = useGetCustom('datagridpro/default-list?noTemplateVars=true');
    const updateRowId = useRef<number | string | undefined>(null) as { current: number | string | undefined };

    const saveGridConfig = async (config: IGridConfig): Promise<IGridConfig> => {
        try {

           
            const formData = new FormData();
            const id = updateRowId.current || config.id;

            if (id && !isCreateMode) {
                formData.append('id', id.toString());
            }
      
   
            formData.append('name', config.name);
            formData.append('columns_config', JSON.stringify(config.columns_config));
            
        
            const response = await saveConfigRequest.doFetch(true, formData) as ApiResponse<IGridConfig>;

            setIsCreateMode(false);
            if (response.data.id && !updateRowId.current) {
                updateRowId.current = response.data.id;
            }
            return response.data;
        } catch (error) {
            console.error('Error saving grid configuration:', error);
            throw error;
        }
    };

    const getCustomList = async (name: string): Promise<IGridConfig | null> => {
        try {
            const response = await getCustomListRequest.doFetch(true, { name }) as ApiResponse<IGridConfig>;
            return response.data;
        } catch (error: any) {
            if (error.status === 404) {
                return null;
            }
            console.error('Error getting custom list:', error);
            throw error;
        }
    };

    const getDefaultList = async (section: string): Promise<IGridConfig> => {
        try {
            const response = await getDefaultListRequest.doFetch(true, { section }) as ApiResponse<IGridConfig>;
            setIsCreateMode(true);
            return response.data;
        } catch (error) {
            console.error('Error getting default list:', error);
            throw error;
        }
    };

    const getGridConfig = async (name: string): Promise<IGridConfig> => {
        try {
            const customList = await getCustomList(name);
            if (customList && customList?.columns_config && customList?.name) {
                return customList;
            }

            return await getDefaultList(name);
        } catch (error) {
            console.error('Error getting grid configuration:', error);
            throw error;
        }
    };

    return {
        saveGridConfig,
        getCustomList,
        getDefaultList,
        getGridConfig
    };
}; 