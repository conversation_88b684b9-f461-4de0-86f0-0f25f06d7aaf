{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"includes": ["app/src/**/*.{js,jsx,ts,tsx}", "*.{js,jsx,ts,tsx}", "vite.config.ts"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "warn", "useExhaustiveDependencies": "off"}, "style": {"noNonNullAssertion": "off", "useImportType": "off", "noParameterAssign": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "complexity": {"noExcessiveCognitiveComplexity": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "attributePosition": "auto"}}, "json": {"formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 120}}}