# Troubles pulling Docker images from Nexus?
# https://development.teamsystem.com/docs/default/component/vapor-react/DOCKER_NEXUS_TOKEN/
#
# For more information about the environment variables, please refer to the documentation:
# https://development.teamsystem.com/docs/default/component/one-front/ENVIRONMENT-SETUP/

# Let the registry connect in read-only to TeamSystem's cluster
GITLAB_READ_API_TOKEN="xxx"

# Let <PERSON><PERSON> pull TeamSystem's custom NPM packages
NEXUS_NPM_TOKEN="NpmToken.xxx"

# Let <PERSON><PERSON> pull FontAwesome with TeamSystem's licence
FONTAWESOME_TOKEN="xxx"

# only for testing purposes
SKIP_SSL_CERTIFICATE_VALIDATION="true"

# Customize your OneFront/ReactStater-powered application
VITE_APP_PROXY_BASE_URL="/api"
VITE_APP_ID="demo"
# For local development, use "app3000" or "demo" if your using the port 3000. You could also you app3010, app3020, etc. if you are using different ports. For a complete list of the available app ids, please refer to the documentation: https://onef-registry-dev.agyo.io/v2/services

VITE_COOKIE_PRO_ID=f15d2fde-7131-4bb2-8411-bc8bde0fece1-test
VITE_APP_MUI_TOKEN="xxx"
VITE_SECTIONS_TO_SHOW="all"
VITE_TEST_TOKEN="xxx"

VITE_WORKER_AUTHMAKEUSER="dummytoken"
