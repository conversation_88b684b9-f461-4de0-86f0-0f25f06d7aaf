stages:
  - tsc
  - build
  - deploy

# Definizioni predefinite per tutti i job
default:
  image: node:23
  before_script:
    - echo "@1f:registry=https://nexus.ts-paas.com/repository/ts-one-front-npm-repo/" >> .npmrc
    - echo "//nexus.ts-paas.com/repository/ts-one-front-npm-repo/:_authToken=${NEXUS_NPM_TOKEN}" >> .npmrc
    - echo "@fortawesome:registry=https://npm.fontawesome.com/" >> .npmrc
    - echo "//npm.fontawesome.com/:_authToken=${FONTAWESOME_TOKEN}" >> .npmrc
  cache:
    paths:
      - node_modules/

# Job per la compilazione TypeScript
tsc:
  stage: tsc
  script:
    - echo "Compilazione TypeScript in corso..."
    - npm install
    - npm run tsc
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always

# Job per la build
build:
  stage: build
  script:
    - echo "VITE_COOKIE_PRO_ID=$VITE_COOKIE_PRO_ID" >> .env
    - echo "Esecuzione build senza TypeScript..."
    - echo "Pulizia cache npm e vite..."
    - npm cache clean --force
    - rm -rf node_modules .vite
    - npm install --force 
    - npm run build-no-tsc
  artifacts:
    paths:
      - dist/assets
      - dist/index.html
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        ENVIRONMENT: "staging"
      when: always
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        ENVIRONMENT: "dev"
      when: always
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      variables:
        ENVIRONMENT: "production"
      when: always
    - when: never
  environment:
    name: $ENVIRONMENT

# Job per il deploy
deploy:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  dependencies:
    - build
  script:
    - aws s3 cp dist/assets s3://$S3_BUCKET_NAME/assets --recursive
    - aws s3 cp dist/index.html s3://$S3_BUCKET_NAME/index.html
    - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        ENVIRONMENT: "staging"
      when: always
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        ENVIRONMENT: "dev"
      when: always
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      variables:
        ENVIRONMENT: "production"
      when: always
    - when: never
  environment:
    name: $ENVIRONMENT